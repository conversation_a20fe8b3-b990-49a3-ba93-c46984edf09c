const CAMERA_RETRY_DELAY_MS = 1000
const VIDEO_LOAD_TIMEOUT_MS = 5000
const FLASH_ANIMATION_DURATION_MS = 500
const POSE_BOUNCE_DURATION_MS = 3000

export default {
  data () {
    return {
      captured: false,
      capturedImage: null,
      cameraAllowed: 'pending',
      backgrounds: [
        { label: 'White', imgPath: require('@/assets/img/phone-upload/white-bg.webp') },
        { label: 'Window', imgPath: require('@/assets/img/phone-upload/window-bg.webp') },
        { label: 'Outside', imgPath: require('@/assets/img/phone-upload/outdoors.webp') }
      ],
      poses: ['Smile', 'Look professional', 'Turn head slightly, look at camera'],
      instructionsModal: false,
      stream: null,
      pauseFor: 0,
      freeCameraMode: false,
      bounceLocation: true,
      isUploading: false,
      isTransitioning: false,
      cameraRetryCount: 0,
      maxCameraRetries: 3
    }
  },
  computed: {
    allSteps () {
      // we have a minimum photos to cover
      const totalBackgrounds = this.backgrounds.length
      const totalPoses = this.poses.length
      const photosPerStep = Math.ceil(this.minimumPhotos / (totalBackgrounds * totalPoses))
      const steps = []
      for (const background of this.backgrounds) {
        for (const pose of this.poses) {
          for (let i = 0; i < photosPerStep; i++) {
            steps.push({ background, pose })
          }
        }
      }

      return steps
    },
    currentBackground () {
      if (this.goodPhotos.length >= this.minimumPhotos) {
        return null
      }

      return this.allSteps[this.goodPhotos.length].background
    },
    currentPose () {
      if (this.goodPhotos.length >= this.minimumPhotos) {
        return null
      }

      return this.allSteps[this.goodPhotos.length].pose
    }
  },
  watch: {
    activeStep (newVal) {
      if (newVal === 'Capture') {
        this.$nextTick(() => {
          this.setupCamera()
        })
      }
    },
    currentPose () {
      this.bounceLocation = true
      setTimeout(() => {
        this.bounceLocation = false
      }, POSE_BOUNCE_DURATION_MS)
    },
    currentBackground (newValue, oldValue) {
      if (newValue !== oldValue && newValue) {
        this.instructionsModal = true
      }
    }
  },
  methods: {
    async setupCamera (resetRetryCount = true) {
      try {
        if (this.stream) {
          this.stopCamera()
        }

        if (resetRetryCount) {
          this.cameraRetryCount = 0
        }
        await this.attemptCameraSetup()
      } catch (error) {
        console.error('Camera setup failed:', error)
        this.cameraAllowed = 'denied'

        if (this.cameraRetryCount < this.maxCameraRetries) {
          this.cameraRetryCount++
          this.$toast.info(`Retrying camera setup (${this.cameraRetryCount}/${this.maxCameraRetries})...`)
          setTimeout(() => {
            this.setupCamera(false)
          }, CAMERA_RETRY_DELAY_MS)
        } else {
          this.$toast.error('Camera access denied or unavailable. Please check your camera permissions and try refreshing the page.')
        }
      }
    },
    capture () {
      if (!this.captured) {
        if (!this.validateCameraState()) {
          this.$toast.error('Camera is not ready. Please wait and try again.')
          return
        }
        this.triggerFlash()
        this.capturedImage = this.createCanvasFromVideo(this.$refs.video)
        this.$refs.video.pause()
        this.captured = true
      } else {
        this.isUploading = true

        this.capturedImage.toBlob(async (blob) => {
          const file = new File([blob], 'photo.jpg', { type: 'image/jpeg' })
          try {
            await this.uploadFileToServer(file)
            await this.nextStep()
            if (this.$refs.video) {
              this.$refs.video.play()
            }
            this.captured = false
          } catch (error) {
            this.handleUploadError(error)
            this.retake()
          }
          this.isUploading = false
        }, 'image/jpeg')
      }
    },
    async nextStep () {
      if (this.goodPhotos.length >= this.minimumPhotos && !this.freeCameraMode) {
        this.isTransitioning = true
        try {
          this.stopCamera()
          await this.$nextTick()
        } catch (error) {
          console.error('Error during transition:', error)
          this.$toast.error('There was an issue completing the upload. Please refresh the page.')
        } finally {
          this.isTransitioning = false
        }
      } else if (this.goodPhotos.length < this.minimumPhotos) {
        await this.ensureCameraReady()
      }
    },
    retake () {
      this.captured = false
      if (this.$refs.video) {
        this.$refs.video.play()
      }
    },
    createCanvasFromVideo (video) {
      video.pause()
      const canvas = document.createElement('canvas')
      canvas.width = this.$refs.video.videoWidth
      canvas.height = this.$refs.video.videoHeight
      const ctx = canvas.getContext('2d')
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
      return canvas
    },
    uploadFromGallery () {
      this.$refs.fileInput.click()
    },
    handleFileSelect (event) {
      const files = event.target.files
      if (files.length) {
        this.pauseFor = files.length * 1000
        this.$toast.info(`Uploading ${files.length} files. Please wait...`)
        Array.from(files).forEach((file) => {
          this.uploadFileToServer(file)
        })
        this.$toast.success('All images processed!')
      }
    },
    triggerFlash () {
      const flashElement = this.$refs.flash
      if (this.$refs.shutterSound) {
        this.$refs.shutterSound.play()
      }
      flashElement.classList.add('flash-animation')
      setTimeout(() => flashElement.classList.remove('flash-animation'), FLASH_ANIMATION_DURATION_MS)
    },
    isMobile () {
      const userAgent = navigator.userAgent
      if ((/windows phone/i.test(userAgent) || /android/i.test(userAgent) || /iPad|iPhone|iPod/.test(userAgent)) && !window.MSStream) {
        return true
      }
      return false
    },
    stopCamera () {
      if (this.stream) {
        this.stream.getTracks().forEach((track) => {
          track.stop()
        })
        this.stream = null
      }
      if (this.$refs.video) {
        this.$refs.video.srcObject = null
      }
    },
    enableFreeCamera () {
      this.freeCameraMode = true
      this.instructionsModal = false
      this.activeStep = 'Capture'
      this.setupCamera()
    },
    disableFreeCamera () {
      this.freeCameraMode = false
      this.instructionsModal = true
      this.activeStep = 'Camera Selection'
      this.stopCamera()
      this.instructionsModal = false
    },
    validateCameraState () {
      return this.stream &&
             this.$refs.video &&
             this.$refs.video.readyState >= 2 &&
             !this.$refs.video.paused
    },
    async ensureCameraReady () {
      if (!this.validateCameraState()) {
        this.isTransitioning = true
        try {
          await this.setupCamera()
        } catch (error) {
          console.error('Failed to setup camera:', error)
          this.$toast.error('Camera setup failed. Please check your camera permissions.')
        } finally {
          this.isTransitioning = false
        }
      }
    },
    async attemptCameraSetup () {
      this.stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1920 },
          height: { ideal: 1080 }
        }
      })

      this.cameraAllowed = 'allowed'
      this.activeStep = 'Capture'
      this.instructionsModal = true

      const videoElement = this.$refs.video
      if (videoElement) {
        videoElement.srcObject = this.stream

        // Wait for video to be ready
        await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('Video loading timeout'))
          }, VIDEO_LOAD_TIMEOUT_MS)

          videoElement.addEventListener('loadedmetadata', () => {
            clearTimeout(timeout)
            resolve()
          }, { once: true })

          videoElement.addEventListener('error', () => {
            clearTimeout(timeout)
            reject(new Error('Video loading error'))
          }, { once: true })
        })
      }

      this.instructionsModal = !this.freeCameraMode
    },
    serializeError (error) {
      if (typeof error === 'string') {
        return error
      }

      if (error && typeof error === 'object') {
        if (error.message) {
          return error.message
        }
        if (error.name) {
          return error.name
        }
        try {
          return JSON.stringify(error)
        } catch {
          return '[Unserializable Error Object]'
        }
      }

      return String(error) || 'Unknown error occurred'
    },
    handleUploadError (error) {
      console.error('Upload error:', error)

      const errorMessage = this.serializeError(error) || 'Failed to upload image, please try again.'
      this.$toast.error(errorMessage)
    }
  }
}
