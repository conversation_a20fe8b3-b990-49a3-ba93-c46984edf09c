import axios from '../utils/axios'

export default function ({ $axios, $fire, store }) {
  $axios.interceptors.request.use(request => addState(axios, $fire, store, request))
  axios.interceptors.request.use(request => addState(axios, $fire, store, request))
}

async function addState (ax, $fire, store, request) {
  // Check for maintenance mode and block requests if on client side
  // if (process.client && process.env.MAINTENANCE_MODE === 'true') {
  //   throw new Error('Service is currently under maintenance')
  // }

  ax.defaults.headers.common.uid = store.state.user.uid
  if ($fire.auth && $fire.auth.currentUser) {
    const idToken = await $fire.auth.currentUser.getIdToken()
    ax.defaults.headers.common.idToken = idToken
  }
  return request
}
