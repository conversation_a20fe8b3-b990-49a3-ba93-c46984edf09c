import Vue from 'vue'
import J<PERSON><PERSON>ip from 'jszip'
import { saveAs } from 'file-saver'
import currencySymbol from '../assets/currency-symbol.json'
import PosthogMixin from '../mixins/PosthogMixin'
import ReferralMixin from '../mixins/ReferralTrackingMixin'

Vue.mixin({
  mixins: [PosthogMixin, ReferralMixin],
  data () {
    return {
      siteId: process.env.SITE_ID
    }
  },
  computed: {
    photoPerStyle () {
      const { photoPerStyle } = this.$store.state
      // if (this.checkIfUserIsInTestId('13-20-vs-40-photos-per-style')) {
      //   return photoPerStyle / 2
      // }
      return photoPerStyle
    },
    env () {
      return process.env.NODE_ENV
    },
    selectedCurrency () {
      let preferredCurrency = 'usd'
      if (process.client && localStorage.getItem('preferedCurrency')) {
        const storedPrefferedCurrency = localStorage.getItem('preferedCurrency')
        if (this.allowedCurrencyCodes.includes(storedPrefferedCurrency)) {
          preferredCurrency = storedPrefferedCurrency
          return preferredCurrency
        }
      }
      // Fallback logic might be needed depending on how default currency is handled
      const defaultCurrency = 'usd' // this.$store.state.user.countryCode
      // ? (this.$localization.countryCurrency[this.$store.state.user.countryCode] || 'usd')
      // : 'usd'
      return defaultCurrency
    },
    isMaintenanceMode () {
      return process.env.MAINTENANCE_MODE === 'true'
    },
    isHolidayMode () {
      return process.env.HOLIDAY_MODE === 'true'
    },
    holidayEndDate () {
      if (this.isHolidayMode) {
        return process.env.HOLIDAY_END_DATE
      }
      return null
    },
    shouldShowUpsell () {
      const baseCondition = this.isTeamMember || this.model || this.isHolidayMode
      if (baseCondition) {
        return false
      }

      return true
    },
    shouldShowReferralUpsell () {
      return this.shouldShowUpsell && !this.$store.state?.user?.referralCode
    },
    isNewReferredUser () {
      return this.userWasReferredViaInternalSystem && !this.$store.state.user.hasPaidAccount
    },
    userWasReferredViaInternalSystem () {
      return this.$store.state?.user?.referredBy?.length > 0
    },
    isTeamMember () {
      return this.$store.state?.user?.role === 'TeamMember' || this.$store.state?.user?.role === 'TeamLead'
    },
    isTeamLead () {
      return this.$store.state?.user?.role === 'TeamLead'
    },
    adminRole () {
      return this.$store.state?.user?.adminRole
    },
    isSafari () {
      if (process.client) {
        return /^((?!chrome|android).)*safari/i.test(navigator.userAgent)
      }
      return false
    },
    isIframe () {
      if (process.client) {
        return window.self !== window.top
      }

      return false
    },
    hasInvites () {
      return this.$store.state?.user?.invites || null
    },
    showInviteWarning () {
      return this.$store.state?.showInviteWarning || null
    },
    isLoggedIn () {
      if (this.$store.state.user.uid && this.$store.state.user.uid.length > 0) {
        return true
      }
      return false
    },
    hasActiveSubscription () {
      const { subscription } = this.$store.state.user
      if (!subscription || !subscription.status || subscription.status.length === 0) {
        return false
      }
      const { status } = subscription
      if (status === 'active' || status === 'trialing') {
        return true
      }
      return false
    },
    hasParityPricing () {
      return this.$cookies.get('hpai_parity') === true
    },
    parityMultiplier () {
      return this.$store.state.parityMultiplier
    },
    screenWidth () {
      // Get screen width
      if (process.client) {
        return window.innerWidth
      }
      return null
    },
    screenHeight () {
      if (process.client) {
        return window.innerHeight
      }
      return null
    },
    isDevelopment () {
      return process.env.NODE_ENV === 'development'
    },
    canShareHeadshots () {
      // return !this.isTeamMember
      return true
    },
    teamDiscountOptions () {
      return this.getTeamDiscountOptions(this.$store.state?.organization?.organization)
    },
    teamMaxSeats () {
      const bulkDiscounts = this.teamDiscountOptions
      const last = bulkDiscounts[bulkDiscounts.length - 1]
      // ceil to next 500
      return Math.ceil((last.from + 1) / 500) * 500
    },
    packages () {
      return this?.$store?.state?.packages || {}
    }
  },
  mounted () {
    if (typeof window !== 'undefined') {
      window.addEventListener('unsavedChanges', this.callUnsavedChanges)
    }
  },
  beforeDestroy () {
    if (typeof window !== 'undefined') {
      window.removeEventListener('unsavedChanges', this.callUnsavedChanges)
    }
  },
  methods: {
    getTeamDiscountOptions (organization) {
      const bulkDiscounts = this.$store.state.organizationBulkDiscounts
      if (!bulkDiscounts) {
        // Fallback for if this ever doesn't get loaded
        return [
          { label: '1+', value: '1-9', discount: 0, from: 1, to: 4 }, // 0% discount
          { label: '5+', value: '10-24', discount: 10, from: 5, to: 24 }, // 10% discount
          { label: '25+', value: '25-49', discount: 15, from: 25, to: 49 }, // 15% discount
          { label: '50+', value: '50-99', discount: 20, from: 50, to: 99 }, // 20% discount
          { label: '100+', value: '100-249', discount: 25, from: 100, to: 249 }, // 25% discount
          { label: '250+', value: '250-499', discount: 30, from: 250, to: 499 }, // 30% discount
          { label: '500+', value: '500-999', discount: 40, from: 500, to: 999 }, // 40% discount
          { label: '1000+', value: '1000+', discount: 50, from: 1000, to: 1000000 } // 50% discount
        ]
      }
      // Get the bulk discount version from the organization. Defaults to v1 if not set
      let organizationBulkDiscountVersion = 'v2'
      if (organization?.bulkDiscountVersion) {
        organizationBulkDiscountVersion = organization?.bulkDiscountVersion
      }

      return bulkDiscounts[organizationBulkDiscountVersion]
    },
    callUnsavedChanges () {
      // Only execute if saveChangesBeforeLeaving exists and hasUnsavedChanges is true
      if (this.saveChangesBeforeLeaving && this.$store.state.hasUnsavedChanges) {
        this.saveChangesBeforeLeaving()
      }
    },
    setupPosthogExperiments () {
      this.$posthog.onFeatureFlags(async () => {
        // feature flags should be available at this point
        const allFlags = await this.$posthog.featureFlags.getFlags()
        const flagsAndVersion = {}
        allFlags.forEach((flag) => {
          flagsAndVersion[flag] = this.$posthog.getFeatureFlag(flag)
        })

        this.$store.commit('SET_POSTHOG_FEATURE_FLAGS', flagsAndVersion)
      })
    },
    totalPhotosPerPackage (styles) {
      return (styles * this.photoPerStyle) + 10
    },
    async initPageLoad () {
      await this.getUserCountryCode()

      this.handlePartnerId()
      if (!localStorage.getItem('hsp_settings')) {
        const settings = {}
        localStorage.setItem('hsp_settings', JSON.stringify(settings))
      }
      this.trackUniqueVisitor()
      this.trackReferral()
      this.trackGclid()
      this.trackFb()
      this.removeRewardfulCommissionOnBannedReferral()

      this.setupPosthogExperiments()

      if (this.$route.query.preview) {
        this.$posthog.featureFlags.override({ [this.$route.query.preview.split(':'[0])]: this.$route.query.preview.split(':')[1] })
      }
    },
    removeRewardfulCommissionOnBannedReferral () {
      if (!process.client) { return }

      try {
        const referralUrl = new URL(window.location.href)
        const searchParams = referralUrl.searchParams

        // Comprehensive list of paid traffic parameters
        const paidTrafficParams = [
          'gclid', // Google Ads Click ID
          'gad_source', // Google Ads source
          'fbclid', // Facebook Click ID
          'msclkid', // Microsoft/Bing Ads Click ID
          'ttclid', // TikTok Click ID
          'twclid', // Twitter Ads Click ID
          'li_fat_id', // LinkedIn Click ID
          'reddit_source', // Reddit ads
          'utm_source', // Generic UTM source (check if it's paid)
          'utm_medium' // Generic UTM medium (check if it's paid)
        ]

        // Check for referral parameters
        const referralParams = ['via', 'ref', 'referral', 'partner']

        const hasPaidTraffic = paidTrafficParams.some(param => searchParams.has(param)) ||
          (searchParams.get('utm_source') === 'google' && searchParams.get('utm_medium') === 'cpc') ||
          (searchParams.get('utm_medium') && ['cpc', 'ppc', 'paid', 'ads'].includes(searchParams.get('utm_medium').toLowerCase()))

        const hasReferralParam = referralParams.some(param => searchParams.has(param))

        if (hasPaidTraffic && hasReferralParam) {
          // Remove immediately instead of waiting 2 seconds to avoid race conditions
          this.$cookies.remove('rewardful.referral', {
            path: '/',
            domain: window.location.hostname
          })

          // Also remove from subdomain if applicable
          const rootDomain = window.location.hostname.split('.').slice(-2).join('.')
          if (rootDomain !== window.location.hostname) {
            this.$cookies.remove('rewardful.referral', {
              path: '/',
              domain: `.${rootDomain}`
            })
          }

          console.log('Removed referral cookie - paid traffic with referral detected (Terms of Service compliance)')

          // Track this event for analytics
          if (this.$posthog) {
            this.$posthog.capture('referral_cookie_removed', {
              reason: 'paid_traffic_with_referral',
              trafficParams: paidTrafficParams.filter(param => searchParams.has(param)),
              referralParams: referralParams.filter(param => searchParams.has(param))
            })
          }
        }
      } catch (error) {
        console.warn('Error in removeRewardfulCommissionOnBannedReferral:', error)
      }
    },
    trackGclid () {
      if (!process.client) { return }
      const gclid = this.$route.query.gclid
      if (gclid) {
        this.$cookies.set('hsp_gclid', gclid, {
          path: '/',
          maxAge: 60 * 60 * 24 * 365
        })
      }
    },
    trackFb () {
      if (!process.client) { return }
      const fbclid = this.$route.query?.fbclid

      if (fbclid) {
        // Set _fbc cookie with format fb.1.{timestamp}.{fbclid}
        const value = `fb.1.${Date.now()}.${fbclid}`
        this.$cookies.set('_fbc', value)

        // Also store fbclid in our own cookie for tracking purposes
        this.$cookies.set('hsp_fbclid', value, {
          path: '/',
          maxAge: 60 * 60 * 24 * 365 // 1 year
        })
      }

      if (!this.$cookies.get('_fbp')) {
        const randomNumber = Math.floor(Math.random() * 900000000) + 100000000
        this.$cookies.set('_fbp', `fb.1.${Date.now()}.${randomNumber}`)
      }
    },
    trackReferral () {
      try {
        if (!process.client) { return }
        let trafficSource = 'direct'

        const previousUrl = document.referrer

        if (previousUrl.includes('headshotpro.com')) { return } // Don't track if the user is coming from headshotpro.com

        if (previousUrl.includes('google') || previousUrl.includes('bing') || previousUrl.includes('duckduckgo')) {
          if (this.$route.query?.utm_source !== 'adwords' || this.$route.query?.utm_source !== 'google') {
            trafficSource = 'seo'
          }
        }

        if (previousUrl.includes('chatgpt') || previousUrl.includes('openai')) {
          trafficSource = 'llm'
        }

        if (this.$route.query?.utm_source) {
          trafficSource = this.$route.query?.utm_source
        }

        if (this.$route.query?.ref) {
          trafficSource = 'affiliate'
        }

        // Create a traffic source cookie if not exists as empty array
        if (!localStorage.getItem('hsp_traffic_source')) {
          localStorage.setItem('hsp_traffic_source', JSON.stringify([]))
        }

        // Create a traffic source cookie if not exists as empty array
        if (!localStorage.getItem('hsp_traffic_source')) {
          localStorage.setItem('hsp_traffic_source', JSON.stringify([]))
        }

        // Append to the traffic source cookie
        const trafficSources = JSON.parse(localStorage.getItem('hsp_traffic_source'))
        const now = new Date().getTime()
        const thirtyMinutes = 1 * 60 * 1000 // 30 minutes in milliseconds

        // Check if the last traffic source was added more than 30 minutes ago
        if (!trafficSources.length || now - trafficSources[trafficSources.length - 1].timestamp > thirtyMinutes) {
          trafficSources.push({ source: trafficSource, timestamp: now })
          localStorage.setItem('hsp_traffic_source', JSON.stringify(trafficSources))
        }
      } catch (err) {
        console.log('Could not track referral')
      }
    },

    trackUniqueVisitor () {
      try {
        if (!localStorage.getItem('hsp_visitor')) {
          this.$posthog.capture('$funnel:new_visitor')
          this.$axios.$post('/analytics/track/view')
            .then((response) => {
              if (response && response.success) {
                localStorage.setItem('hsp_visitor', response?.uuid || '')
              }
            })
        }
      } catch (err) {
        console.log('Could not count visitor')
      }
    },
    updateLocalStorageSettings (key, value) {
      const settings = JSON.parse(localStorage.getItem('hsp_settings'))
      settings[key] = value
      localStorage.setItem('hsp_settings', JSON.stringify(settings))
    },
    getLocalStorageSettings (key) {
      const settings = JSON.parse(localStorage.getItem('hsp_settings'))
      if (!settings) {
        return null
      }
      if (settings[key] !== null && settings[key] !== undefined) {
        return settings[key]
      } else {
        return null
      }
    },
    handlePartnerId () {
      const partnerId = this.$route?.query?.partnerId
      if (partnerId) {
        this.$cookies.set('hp_partner_id', partnerId, {
          path: '/',
          maxAge: 60 * 60 * 24 * 365
        })
      }
    },
    async createDatabaseAccount (user) {
      const { uid, email, displayName, photoURL } = user
      const name = displayName

      if (!uid || uid.length === 0 || !email || email.length === 0) {
        this.hasError = true
        return false
      }

      const referUid = this.$cookies.get('hp_referred_by') || null
      const inviteUid = this.$cookies.get('hshq_invite') || null
      const partnerId = this.$cookies.get('hp_partner_id') || null
      const referralCode = this.$cookies.get('hsp:referral') || (process.client ? localStorage.getItem('hsp:referral') : null)

      const response = await this.$axios.$post('/user', {
        uid,
        email,
        name,
        ...this.data,
        referUid,
        inviteUid,
        partnerId,
        avatar: photoURL,
        userCountry: this.$store.state?.user?.countryCode || null,
        referralCode
      })
      if (response && response.success === true) {
        if (response && response.new) {
          this.$gtm.push({
            email: email || this.$store.state?.user?.email || null,
            event: 'registration',
            uid
          })
          this.$posthog.capture('$funnel:signup')
          if (referralCode) {
            this.$posthog.capture('$funnel:signup_with_referral', { referralCode })
          }
          this.$facebook.trackEvent('CompleteRegistration')
        } else {
          this.$posthog.capture('$funnel:login')
        }
        this.loadingMessage = 'Logging you in...'
        this.$emit('success')
        this.$cookies.remove('hshq_invite')
      } else {
        if (response.reason === 'rate-limited') {
          await this.$fire.auth.signOut()
          return this.$toast.error('You signed up too many times. Contact support if you think this error is wrong.')
        }
        this.$toast.error('Something went wrong. Please try again.')
        this.hasError = true
        this.$router.push('/')
      }
      return response
    },
    openLiveChat () {
      // this.$crisp(['do', 'chat:show'])
      // this.$crisp(['do', 'chat:open'])
      this.$store.commit('SET_MODAL', { name: 'support', value: true })
    },
    setModalStatus (name, value) {
      this.$store.commit('SET_MODAL', { name, value })
    },
    async setupUserDetails (user) {
      this.$axios.defaults.headers.common.idToken = await user.getIdToken(true)
      await this.setupUserFirebaseData()
      await this.getUserData()
      await this.getInvites()
      return true
    },
    destroyUserDetails () {
      this.$axios.defaults.headers.common.idToken = null
      this.$store.commit('user/SET_USER_TO_NULL', {})
    },
    async setupUserFirebaseData () {
      const idToken = await this.$fire.auth.currentUser.getIdToken(true)

      if (this.$fire.auth.currentUser.photoURL) {
        this.$store.commit('user/SET_AVATAR', this.$fire.auth.currentUser.photoURL)
      }
      const { uid, email, displayName } = this.$fire.auth.currentUser
      if (uid) {
        this.$store.commit('user/SET_UID', uid)
      }
      if (email) {
        this.$store.commit('user/SET_EMAIL', email)
      }

      let distincId = null

      try {
        const phProjectAPIKey = 'phc_Vuo62u3ZVz4EzAATlnWPMNDJQFbCnf4V7ARGYMvBt4y'
        const phCookieName = `ph_${phProjectAPIKey}_posthog`
        const phCookie = this.$cookies.get(phCookieName)

        if (phCookie?.distinct_id) {
          distincId = phCookie.distinct_id
        }
      } catch (err) {
        // Can't get cookie
      }

      if (email && uid) {
        this.$posthog?.identify(
          (distincId || uid),
          { email },
          {}
        )
      }
      if (displayName) {
        this.$store.commit('user/SET_DISPLAY_NAME', displayName)
      }
      this.$store.commit('user/SET_IDTOKEN', idToken)
      this.$axios.defaults.headers.common.idToken = idToken

      this.$gtm.push({
        email: email || this.$store.state?.user?.email || null,
        event: 'login',
        authenticationMethod: 'email',
        uid
      })
    },

    async getUserData () {
      try {
        const idToken = await this.$fire.auth.currentUser.getIdToken(true)
        const data = await this.$axios.$get('/user/details', { idToken })
        if (!data.user || !data.user.uid) {
          const currentUser = this.$fire.auth.currentUser
          if (currentUser) {
            await this.createDatabaseAccount(currentUser)
            await this.getUserData()
            return
          }
        }
        await this.$store.dispatch('user/setupUserData', data)
      } catch (err) {
        console.log(err)
      }
    },
    async getInvites () {
      try {
        const { success, data, errorMessage } = await this.$axios.$get('/invite')
        if (!success) {
          throw new Error(errorMessage)
        }
        if (!data) {
          return
        }
        this.$store.commit('user/SET_INVITES', data)
      } catch (err) {
        console.log(err)
      }
    },
    async logout () {
      await this.$fire.auth.signOut()
      await this.destroyUserDetails()
      window.location.href = '/auth/login/?redirect=' + this.$route.path
    },

    stripHtml (string) {
      return string.replace(/<\/?[^>]+(>|$)/g, '')
    },

    handleReferral () {
      const referralCode = this.$route.query.ref
      if (referralCode) {
        this.$cookies.set('hp_referred_by', referralCode, {
          path: '/',
          maxAge: 60 * 60 * 24 * 45
        })
      }
    },

    formatPrice (number, currency, minimumFractionDigits = 2, shorten = true) {
      const symbol = currencySymbol[currency?.toUpperCase()]?.symbol || '$'
      const formatter = new Intl.NumberFormat('en-US', { minimumFractionDigits, maximumFractionDigits: minimumFractionDigits })
      // const value = (number > 1000 && shorten) ? `${number / 1000}K` : formatter.format(number)
      const value = formatter.format(number)
      // return formatter.format(number)
      return `${symbol}${value}`
    },
    formatNumber (number) {
      const formatter = new Intl.NumberFormat('en-US', {
        maximumSignificantDigits: 3
      })
      return formatter.format(number)
    },
    timestampToDate (timestamp) {
      const a = new Date(timestamp * 1000)
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
      const year = a.getFullYear()
      const month = months[a.getMonth()]
      const date = a.getDate()
      const time = date + ' ' + month + ' ' + year
      return time
    },

    titleCase (str) {
      if (!str || str.length === 0) {
        return str
      }
      const splitStr = str.toLowerCase().split(' ')
      for (let i = 0; i < splitStr.length; i++) {
        // You do not need to check if i is larger than splitStr length, as your for does that for you
        // Assign it back to the array
        splitStr[i] = splitStr[i].charAt(0).toUpperCase() + splitStr[i].substring(1)
      }
      // Directly return the joined string
      return splitStr.join(' ')
    },

    formatDate (oldDate) {
      const date = new Date(oldDate)
      const options = { year: 'numeric', month: 'long', day: 'numeric' }
      return date.toLocaleDateString('en-us', options)
    },
    formatLongDate (oldDate) {
      const date = new Date(oldDate)
      const options = { year: 'numeric', month: 'long', day: 'numeric', minute: 'numeric', hour: 'numeric' }
      return date.toLocaleDateString('en-us', options)
    },
    formatDateAndTime (oldDate) {
      const date = new Date(oldDate)
      const options = { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' }
      return date.toLocaleDateString('en-us', options)
    },

    dnsImage (url) {
      return 'https://storage.googleapis.com/' + process.env.GOOGLE_STORAGE_BUCKET + '/' + url
    },

    getFinalPrice (price) {
      if (this.parityMultiplier) {
        return (price * this.parityMultiplier).toFixed(2)
      } else {
        return price
      }
    },

    copyString (item) {
      const el = document.createElement('textarea')
      el.value = item
      document.body.appendChild(el)
      el.select()
      document.execCommand('copy')
      document.body.removeChild(el)
      this.$toast.open({ type: 'success', message: 'Copied to clipboard' })
    },

    convertToSlug (string) {
      if (!string || string.length === 0) {
        return string
      }
      return string
        .toLowerCase()
        .replace(/ /g, '-')
        .replace(/[^\w-]+/g, '')
    },

    toSlug (str) {
      if (!str || str.length === 0) {
        return str
      }
      return str
        .normalize('NFD') // Normalize string to decompose special characters
        .replace(/[\u0300-\u036F]/g, '') // Remove diacritics
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/[^a-zA-Z0-9-]/g, '') // Remove any non-alphanumeric or non-hyphen characters
        .toLowerCase()
    },

    slugToTitle (slug, capitalizeFirstLetter = true) {
      try {
        const words = slug.split('-')

        for (let i = 0; i < words.length; i++) {
          const word = words[i]
          if (capitalizeFirstLetter) {
            words[i] = word.charAt(0).toUpperCase() + word.slice(1)
          } else if (i === 0) {
            words[i] = word.charAt(0).toUpperCase() + word.slice(1)
          }
        }

        return words.join(' ')
      } catch (err) {
        return slug
      }
    },

    slugToWord (slug) {
      try {
        const words = slug.split('-')
        return words.join(' ')
      } catch (err) {
        return slug
      }
    },

    stripLength (string, length) {
      try {
        if (length < string.length) {
          return string.substring(0, length).trim() + '...'
        }
        return string
      } catch (err) {
        return string
      }
    },

    stripString (string) {
      if (string && string.length > 15) {
        const firstPart = string.substring(0, 5).trim()
        const lastPart = string.slice(-8)
        return `${firstPart}...${lastPart}`
      }
      return string
    },

    capitalizeFirstLetter (string) {
      return string.charAt(0).toUpperCase() + string.slice(1)
    },

    refreshMasonry () {
      if (typeof this.$redrawVueMasonry === 'function') {
        this.$redrawVueMasonry()
      }
    },

    isNumber (str) {
      const pattern = /^\d+\.?\d*$/
      return pattern.test(str) // returns a boolean
    },
    inputNumbersOnly (evt) {
      evt = evt || window.event
      const charCode = evt.which ? evt.which : evt.keyCode
      if (charCode > 31 && (charCode < 48 || charCode > 57) && charCode !== 46) {
        evt.preventDefault()
      } else {
        return true
      }
    },
    isValidEmail (email) {
      const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      return re.test(String(email).toLowerCase())
    },
    handleError (err) {
      // Check if the error has a response object
      if (err.response) {
        // HTTP error (e.g., 404, 500)
        let message = err.response.data?.errorMessage || `Error ${err.response.status}: ${err.response.statusText}`

        // Handle "Unauthorized" responses with a more friendly message
        if (err.response.data === 'Unauthorized' || message.includes('Unauthorized')) {
          message = 'Please sign in to access this page'
        }

        this.$toast.open({ type: 'error', message })
      } else if (err.message || err.errorMessage) {
        // JavaScript error or custom error message
        this.$toast.open({ type: 'error', message: err.message || err.errorMessage })
      } else {
        // Fallback error message
        this.$toast.open({ type: 'error', message: 'Something went wrong' })
      }
      console.log(err) // Log the error for debugging purposes
    },
    async downloadFromUrl (urls) {
      try {
        if (!urls || urls.length === 0) {
          return
        }
        const promises = urls
          .map(async (img) => {
            try {
              const response = await this.$axios.$get(img, { responseType: 'blob', headers: { 'Access-Control-Allow-Origin': '*' } })
              return response
            } catch (err) {
              console.error('Failed to download image:', err)
              return null
            }
          })
          .filter(p => p !== null)

        const blobs = await Promise.all(promises)
        const validBlobs = blobs.filter(blob => blob !== null)

        if (validBlobs.length === 0) {
          this.$toast.error('Failed to download any images. Please try again.')
          return
        }

        const zip = new JSZip()
        validBlobs.forEach((blob, index) => {
          zip.file(`${index + 1}.png`, blob)
        })

        const content = await zip.generateAsync({
          type: 'blob',
          compression: 'DEFLATE',
          compressionOptions: { level: 6 }
        })

        saveAs(content, 'All headshots - HeadshotPro.zip')
        this.$toast.success('Downloaded selected images. If not, accept our download request.')
      } catch (err) {
        this.handleError(err)
      }
    },
    getFormattedPriceParts (priceString) {
      priceString = String(priceString || '')
      let smallTextPrefix = ''
      let largePart = priceString

      // If the entire string is just letters (and possibly spaces), it's all small prefix.
      if (/^[A-Za-z\s]+$/.test(priceString) && priceString.trim().length > 0) {
        smallTextPrefix = priceString.trim()
        largePart = ''
      } else {
        // Try to find a leading alphabetical prefix.
        const match = priceString.match(/^([A-Za-z]+)(.*)/)
        if (match) {
          smallTextPrefix = match[1]
          largePart = match[2]
        }
      }

      return {
        smallTextPrefix,
        largePart
      }
    }
  }
})
