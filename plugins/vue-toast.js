import Vue from 'vue'

// Create toast component using render function instead of template
const ToastComponent = Vue.extend({
  name: 'Toast',
  props: {
    message: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: 'default'
    },
    duration: {
      type: Number,
      default: 3000
    },
    dismissible: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      isVisible: false
    }
  },
  mounted () {
    this.show()
  },
  methods: {
    show () {
      this.isVisible = true
      if (this.duration > 0) {
        setTimeout(this.dismiss, this.duration)
      }
    },
    dismiss () {
      this.isVisible = false
      setTimeout(() => {
        this.$destroy()
        this.$el.remove()
      }, 300) // match transition duration
    }
  },
  render (h) {
    return h('transition', {
      props: {
        name: 'toast'
      }
    }, [
      this.isVisible && h('div', {
        class: ['toast-notification', this.type],
        on: {
          click: this.dismiss
        }
      }, [
        h('div', {
          class: ['toast-content']
        }, [
          h('span', {
            class: ['message']
          }, this.message),
          this.dismissible && h('button', {
            class: ['close-button'],
            on: {
              click: (e) => {
                e.stopPropagation()
                this.dismiss()
              }
            }
          }, '×')
        ])
      ])
    ])
  }
})

// Create toast plugin
const Toast = {
  install (Vue, options = {}) {
    // Default options
    const defaultOptions = {
      position: 'bottom-right',
      duration: 3000,
      dismissible: true
    }

    // Create toast container
    const createContainer = (position) => {
      const container = document.createElement('div')
      container.className = `toast-container ${position}`
      document.body.appendChild(container)
      return container
    }

    // Add $toast method to Vue prototype
    Vue.prototype.$toast = {
      show (message, options = {}) {
        const finalOptions = { ...defaultOptions, ...options }
        const container = document.querySelector(`.toast-container.${finalOptions.position}`) ||
          createContainer(finalOptions.position)

        const instance = new ToastComponent({
          propsData: {
            message,
            type: finalOptions.type || 'default',
            duration: finalOptions.duration,
            dismissible: finalOptions.dismissible
          }
        })

        instance.$mount()
        container.appendChild(instance.$el)
      },
      open (options) {
        const message = typeof options === 'string' ? options : options.message
        this.show(message, typeof options === 'string' ? {} : options)
      },
      success (message, options = {}) {
        this.show(message, { ...options, type: 'success' })
      },
      error (message, options = {}) {
        this.show(message, { ...options, type: 'error' })
      },
      warning (message, options = {}) {
        this.show(message, { ...options, type: 'warning' })
      },
      info (message, options = {}) {
        this.show(message, { ...options, type: 'info' })
      }
    }
  }
}

Vue.use(Toast)

// Export for Nuxt.js
export default ({ app }, inject) => {
  inject('toast', Vue.prototype.$toast)
}
