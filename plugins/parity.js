import Vue from 'vue'
import timezones from '~/assets/timezones.js'
// import countryCodes from '~/assets/countryCodes.js'
import countryIncomes from '~/assets/countryIncomes.js'

Vue.mixin({
  data () {
    return {
      dollarDiscountPerTier: {
        1: 0,
        0.9: 0,
        0.8: 500,
        0.7: 1000,
        0.6: 1200,
        0.5: 1400
      }
    }
  },
  computed: {
    parityDiscountDollarAmount () {
      // get () { return this.$store.state.parityDiscountDollarAmount },
      // set (value) { this.$store.commit('SET_PARITY_DISCOUNT_DOLLAR_AMOUNT', value) }
      return 0
    }
  },
  methods: {
    initParityPricing () {
      if (!this.checkIfUserIsInTestId('4-parity-pricing')) { return }
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
      for (const [key, value] of Object.entries(timezones)) {
        if (value.includes(timezone)) {
          // const countryCode = key
          // const countryName = countryCodes[key]
          const countryIncome = countryIncomes[key] // USD/year

          const percentageOfUsIncome = countryIncome / 40000

          let parityMultiplier = Math.ceil(percentageOfUsIncome * 10) / 10

          if (parityMultiplier > 1) { parityMultiplier = 1 }
          if (parityMultiplier < 0.5) { parityMultiplier = 0.5 }

          this.parityDiscountDollarAmount = this.dollarDiscountPerTier[parityMultiplier]
        }
      }
    }
  }
})
