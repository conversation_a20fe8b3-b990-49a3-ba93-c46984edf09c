export default ({ app, store }) => {
  app.router.beforeEach((to, from, next) => {
    if (to.path !== from.path) {
      if (store.state.hasUnsavedChanges && process.client) {
        const confirm = window.confirm('You have unsaved changes. Do you want to save the changes before you leave?')
        if (!confirm) {
          store.commit('SET_HAS_UNSAVED_CHANGES', false)
          next(false)
          return
        }

        window.dispatchEvent(new CustomEvent('unsavedChanges'))

        store.commit('SET_HAS_UNSAVED_CHANGES', false)
        next()
      }
    }
    next()
  })
}
