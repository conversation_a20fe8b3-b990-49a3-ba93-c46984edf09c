export default ({ app }, inject) => {
  window.$crisp = []
  window.CRISP_WEBSITE_ID = 'fd4779ea-13f4-448e-906d-ea3647f4d8c8';

  (function () {
    const d = document
    const s = d.createElement('script')

    s.src = 'https://client.crisp.chat/l.js'
    s.async = 1
    d.getElementsByTagName('head')[0].appendChild(s)
  })()
  // Inject $hello(msg) in Vue, context and store.
  inject('crisp', action => window.$crisp.push(action))
  window.$crisp.push(['safe', true])
}
