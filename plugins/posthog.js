/* eslint-disable */

import posthog from 'posthog-js'
import Vue from 'vue'

export default function ({ app: { router } }, inject) {
  // Init PostHog
  if (!window.posthog) {
    posthog.init('phc_Vuo62u3ZVz4EzAATlnWPMNDJQFbCnf4V7ARGYMvBt4y', {
      // api_host: 'https://app.posthog.com',
      api_host: 'https://ph-worker.headshotpror2.com',
      ui_host: 'https://us.posthog.com',
      capture_pageview: false,
      disable_session_recording: true,
      autocapture: {
        dom_event_allowlist: ['click'], // DOM events from this list ['click', 'change', 'submit']
        url_allowlist: ['headshotpro.com(?!/app/).*'], // strings or RegExps
        element_allowlist: ['button', 'a'], // DOM elements from this list ['a', 'button', 'form', 'input', 'select', 'textarea', 'label']
        css_selector_allowlist: ['[ph-autocapture]'], // List of CSS selectors
      },
      // loaded: () => {
      //   // posthog.identify('unique_id') // If you can already identify your user
       
      // }
    })
  }

  // Inject PostHog into the application and make it available via this.$posthog (or app.$posthog)
  inject('posthog', posthog)

  // Make sure that pageviews are captured with each route change
  router.afterEach((to) => {
    Vue.nextTick(() => {
      try{
        /* Note: this might also be a good place to call posthog.register(...) in order to update your properties
        on each page view
        */
        posthog.capture('$pageview', {
          $current_url: to.fullPath
        })
      } catch (e) {
        console.log('error capturing pageview', e)
      }
    })
  })
}
