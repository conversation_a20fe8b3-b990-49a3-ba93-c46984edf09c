import Vue from 'vue'
import { template2h } from '@/utils/template-to-render'

// Create loading component using render function
const LoadingComponent = Vue.extend({
  name: 'Loading',
  data () {
    return {
      isVisible: false,
      title: '',
      description: ''
    }
  },
  methods: {
    show (options = {}) {
      this.title = options.title || ''
      this.description = options.description || ''
      this.isVisible = true
      // Prevent scrolling on body
      document.body.style.overflow = 'hidden'
    },
    hide () {
      this.isVisible = false
      // Restore scrolling on body
      document.body.style.overflow = ''
      setTimeout(() => {
        this.$destroy()
        this.$el.remove()
      }, 300) // match transition duration
    }
  },
  render (h) {
    if (!this.isVisible) { return null }

    return template2h`
      <transition name="loading">
        <div class="loading-overlay vue-loading">
          <div class="loading-container">
            <div class="loading-content">
              <div class="loading-spinner"></div>
              ${this.title ? ` <div class="loading-title">${this.title}</div>` : ''}
            </div>
            ${this.description ? ` <div class="loading-description">${this.description}</div>` : ''}
          </div>
        </div>
      </transition>
    `(h)
  }
})

// Create loading plugin
const Loading = {
  install (Vue) {
    let instance = null

    // Create loading container
    const createContainer = () => {
      const container = document.createElement('div')
      container.className = 'loading-wrapper'
      document.body.appendChild(container)
      return container
    }

    // Add $loading method to Vue prototype
    Vue.prototype.$loading = {
      show (options = {}) {
        if (instance) {
          instance.show(options)
          return
        }

        const container = document.querySelector('.loading-wrapper') ||
          createContainer()

        instance = new LoadingComponent()
        instance.$mount()
        container.appendChild(instance.$el)
        instance.show(options)
      },
      hide () {
        if (instance) {
          instance.hide()
          instance = null
        }
      }
    }
  }
}

Vue.use(Loading)

// Export for Nuxt.js
export default ({ app }, inject) => {
  inject('loading', Vue.prototype.$loading)
}
