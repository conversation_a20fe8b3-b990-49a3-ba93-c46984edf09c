import { loadStripe } from '@stripe/stripe-js'

let stripe = null
let embeddedCheckout = null

const stripePlugin = {
  async initialize () {
    if (!stripe) {
      stripe = await loadStripe(process.env.STRIPE_PUBLISHABLE_KEY)
    }
    return stripe
  },

  async createEmbeddedCheckout (clientSecret, options = {}) {
    try {
      if (!stripe) {
        await this.initialize()
      }

      // Default options for embedded checkout
      const defaultOptions = {
        fetchClientSecret: () => {
          return Promise.resolve(clientSecret)
        },
        onComplete: () => {
          // Default completion handler
          console.log('Checkout completed successfully')
        },
        ...options
      }

      // Initialize embedded checkout
      embeddedCheckout = await stripe.initEmbeddedCheckout(defaultOptions)

      return embeddedCheckout
    } catch (error) {
      console.error('Error creating embedded checkout:', error)
      throw error
    }
  },

  async mountEmbeddedCheckout (elementOrSelector) {
    try {
      if (!embeddedCheckout) {
        throw new Error('Embedded checkout not initialized. Call createEmbeddedCheckout first.')
      }

      let element
      let mountTarget

      // Check if it's a DOM element or a selector string
      if (typeof elementOrSelector === 'string') {
        element = document.querySelector(elementOrSelector)
        mountTarget = elementOrSelector
      } else if (elementOrSelector && elementOrSelector.nodeType === Node.ELEMENT_NODE) {
        element = elementOrSelector
        mountTarget = element
      } else {
        throw new Error('Invalid element or selector provided')
      }

      if (!element) {
        throw new Error('Element not found')
      }

      // Mount the embedded checkout
      await embeddedCheckout.mount(mountTarget)

      return embeddedCheckout
    } catch (error) {
      console.error('Error mounting embedded checkout:', error)
      throw error
    }
  },

  destroyEmbeddedCheckout () {
    try {
      if (embeddedCheckout) {
        embeddedCheckout.destroy()
        embeddedCheckout = null
      }
    } catch (error) {
      console.error('Error destroying embedded checkout:', error)
    }
  },

  async redirectToCheckout (sessionId) {
    try {
      if (!stripe) {
        await this.initialize()
      }

      const { error } = await stripe.redirectToCheckout({
        sessionId
      })

      if (error) {
        throw error
      }
    } catch (error) {
      console.error('Error redirecting to checkout:', error)
      throw error
    }
  },

  async confirmPayment (clientSecret, paymentData = {}) {
    try {
      if (!stripe) {
        await this.initialize()
      }

      const { error, paymentIntent } = await stripe.confirmPayment({
        clientSecret,
        ...paymentData
      })

      if (error) {
        throw error
      }

      return paymentIntent
    } catch (error) {
      console.error('Error confirming payment:', error)
      throw error
    }
  },

  async retrievePaymentIntent (clientSecret) {
    try {
      if (!stripe) {
        await this.initialize()
      }

      const { error, paymentIntent } = await stripe.retrievePaymentIntent(clientSecret)

      if (error) {
        throw error
      }

      return paymentIntent
    } catch (error) {
      console.error('Error retrieving payment intent:', error)
      throw error
    }
  },

  // Utility method to extract client secret from URL
  getClientSecretFromUrl () {
    const urlParams = new URLSearchParams(window.location.search)
    return urlParams.get('payment_intent_client_secret') || urlParams.get('client_secret')
  },

  // Helper method to handle payment status from URL
  async handlePaymentStatus () {
    const clientSecret = this.getClientSecretFromUrl()

    if (clientSecret) {
      try {
        const paymentIntent = await this.retrievePaymentIntent(clientSecret)
        return {
          status: paymentIntent.status,
          paymentIntent
        }
      } catch (error) {
        console.error('Error handling payment status:', error)
        return {
          status: 'error',
          error
        }
      }
    }

    return null
  }
}

export default ({ app }, inject) => {
  // Initialize Stripe on plugin load
  stripePlugin.initialize()

  // Inject the stripe plugin into Vue instances
  inject('stripe', stripePlugin)

  // Make it available in the context
  app.$stripe = stripePlugin
}
