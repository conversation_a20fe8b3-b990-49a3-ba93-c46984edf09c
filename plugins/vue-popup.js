import Vue from 'vue'

// Create popup component using render function
const PopupComponent = Vue.extend({
  name: 'Popup',
  data () {
    return {
      isVisible: false,
      title: '',
      message: '',
      buttonText: '',
      onButtonClick: null,
      id: null
    }
  },
  methods: {
    show (options = {}) {
      this.id = options.id
      this.title = options.title || ''
      this.message = options.message || options.description || ''
      this.buttonText = options.buttonText || ''
      this.onButtonClick = options.onButtonClick || null
      this.isVisible = true
      // Prevent scrolling on body if this is the first popup
      if (document.querySelectorAll('.popup-overlay').length === 0) {
        document.body.style.overflow = 'hidden'
      }
    },
    hide () {
      this.isVisible = false
      // Only restore scrolling if this is the last popup
      if (document.querySelectorAll('.popup-overlay').length === 1) {
        document.body.style.overflow = ''
      }
      setTimeout(() => {
        this.$destroy()
        this.$el.remove()
        // Remove this instance from the instances array
        const index = instances.findIndex(instance => instance.id === this.id)
        if (index !== -1) {
          instances.splice(index, 1)
        }
      }, 300) // match transition duration
    },
    handleButtonClick () {
      if (this.onButtonClick) {
        this.onButtonClick()
      }
      this.hide()
    }
  },
  render (h) {
    if (!this.isVisible) { return null }

    return h('transition', {
      props: {
        name: 'popup'
      }
    }, [
      h('div', {
        class: ['popup-overlay', 'vue-popup'],
        style: {
          zIndex: 9999 + (this.id || 0) // Stack popups with increasing z-index
        }
      }, [
        h('div', {
          class: ['popup-container']
        }, [
          h('div', {
            class: ['popup-content']
          }, [
            this.title && h('div', {
              class: ['popup-title']
            }, this.title),
            this.message && h('div', {
              class: ['popup-message']
            }, this.message),
            this.buttonText && h('button', {
              class: ['popup-button'],
              on: {
                click: this.handleButtonClick
              }
            }, this.buttonText)
          ].filter(Boolean))
        ])
      ])
    ])
  }
})

// Keep track of all popup instances
const instances = []

// Create popup plugin
const Popup = {
  install (Vue) {
    // Create popup container
    const createContainer = () => {
      const container = document.createElement('div')
      container.className = 'popup-wrapper'
      document.body.appendChild(container)
      return container
    }

    // Add $popup method to Vue prototype
    Vue.prototype.$popup = {
      show (options = {}) {
        const container = document.querySelector('.popup-wrapper') ||
          createContainer()

        const instance = new PopupComponent()
        // Generate unique ID for this instance
        instance.id = Date.now()
        instance.$mount()
        container.appendChild(instance.$el)
        instances.push(instance)
        instance.show({ ...options, id: instance.id })

        return instance.id // Return ID for reference
      },
      hide (id) {
        if (id) {
          // Hide specific popup
          const instance = instances.find(inst => inst.id === id)
          if (instance) {
            instance.hide()
          }
        } else {
          // Hide all popups
          [...instances].forEach(instance => instance.hide())
        }
      }
    }
  }
}

Vue.use(Popup)

// Export for Nuxt.js
export default ({ app }, inject) => {
  inject('popup', Vue.prototype.$popup)
}
