import Vue from 'vue'
import Toast from '~/components/modal/Toast.vue'

Vue.prototype.$modal = {
  open (options) {
    const ComponentClass = Vue.extend(Toast)
    const instance = new ComponentClass({
      propsData: options
    })
    instance.$mount()
    document.body.appendChild(instance.$el)

    if (options?.time && typeof options.time === 'number' && options.time > 0) {
      setTimeout(() => {
        instance.$destroy()
        instance.$el.parentNode.removeChild(instance.$el)
      }, options.time)
    }

    instance.$on('close', () => {
      instance.$destroy()
      instance.$el.parentNode.removeChild(instance.$el)
    })
  }
}
