import Vue from 'vue'
import countryCurrency from '~/assets/country-currency.json'
import Posthog from '@/mixins/PosthogMixin'

Vue.mixin({
  mixins: [Posthog],
  computed: {
    userCountryCode () {
      return this.$store.state.user.countryCode
    },
    allowedCurrencyCodes () {
      return this.$store.state.allowedCurrencies.map(item => item.code)
    },
    userCurrency () {
      if (process.client && localStorage.getItem('preferedCurrency')) {
        if (this.allowedCurrencyCodes.includes(localStorage.getItem('preferedCurrency'))) {
          return localStorage.getItem('preferedCurrency')
        }
      }
      if (countryCurrency[this.userCountryCode]?.toLowerCase()) {
        if (this.allowedCurrencyCodes.includes(countryCurrency[this.userCountryCode]?.toLowerCase())) {
          return countryCurrency[this.userCountryCode]?.toLowerCase()
        }
      }
      return 'usd'
    },
    packages () {
      return this?.$store?.state?.packages || {}
    }

  },
  methods: {
    async getUserCountryCode () {
      try {
        const response = await this.$axios.$get('/user/location')
        if (response && response.countryCode) {
          this.$store.commit('user/SET_COUNTRY_CODE', response.countryCode)
          const currencyToStore = countryCurrency[this.userCountryCode]?.toLowerCase() || 'usd'
          if (process.client) {
            if (process.client && localStorage.getItem('preferedCurrency')) {
              // Already have a preference stored, no need to fetch
              return localStorage.getItem('preferedCurrency')
            } else if (this.allowedCurrencyCodes.includes(currencyToStore)) {
              localStorage.setItem('preferedCurrency', currencyToStore)
            } else {
              localStorage.removeItem('preferedCurrency')
            }
          }
        }
      } catch (error) {
        console.error(error)
      }
    },
    getLocalizedPrice (priceId, format = false, minimumFractionDigits = 2, shorten = true, discount = 0, quantity = 1, teams = false) {
      const packages = this.packages
      if (!packages) { return null }
      let selectedPackage = {}
      Object.keys(packages).forEach((key) => {
        if (packages[key].id === priceId) {
          // Create a deep copy of the package to avoid mutating store state
          selectedPackage = JSON.parse(JSON.stringify(packages[key]))
        }
      })

      const { userCurrency } = this

      const defaultValue = { price: selectedPackage?.price * (1 - discount / 100), currency: 'usd' }

      if (!userCurrency) {
        if (format) {
          return this.formatPrice(defaultValue.price / 100 * quantity, defaultValue.currency, minimumFractionDigits, shorten)
        }
        return defaultValue
      }

      if (!this.allowedCurrencyCodes.includes(userCurrency.toLowerCase())) {
        if (format) {
          return this.formatPrice(defaultValue.price / 100 * quantity, defaultValue.currency, minimumFractionDigits, shorten)
        }
        return defaultValue
      }

      if (!selectedPackage?.currency && !selectedPackage?.currency?.[userCurrency]) {
        if (format) {
          return this.formatPrice(defaultValue.price / 100 * quantity, defaultValue.currency, minimumFractionDigits, shorten)
        }
        return defaultValue
      }

      const selectedPrice = { price: selectedPackage?.currency[userCurrency] * (1 - discount / 100), currency: userCurrency }

      if (format) {
        return this.formatPrice(selectedPrice.price / 100 * quantity, selectedPrice.currency, minimumFractionDigits, shorten)
      }
      return selectedPrice
    }
  }
})
