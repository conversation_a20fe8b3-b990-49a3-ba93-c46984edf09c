// /* eslint-disable */
// import { PostHog } from 'posthog-node'
// import posthog from 'posthog-js'
// import { v4 as uuidv4 } from 'uuid'
// const cookie = require('cookie')

// const phProjectAPIKey = 'phc_Vuo62u3ZVz4EzAATlnWPMNDJQFbCnf4V7ARGYMvBt4y'
// const phCookieName = `ph_${phProjectAPIKey}_posthog`

// export default async function (req, res, next) {
//     // Don't do tests for Google bots etc
//     const userAgent = req.headers['user-agent'].toLowerCase();
//     const isScraper = /googlebot|bingbot|yandex|baiduspider|duckduckbot|slurp|exabot|facebot|ia_archiver/.test(userAgent);
//     if (isScraper) {
//         return; // Exit middleware early
//     }

//     const cookies = cookie.parse(req.headers.cookie || '')

//     let distinct_id = ''

//     if(cookies[phCookieName]){
//         try{
//             const obj = JSON.parse(cookies[phCookieName]);
//             distinct_id = obj.distinct_id
//         }catch(err){
//             console.log('Could not distruct object')
//         }
//     }

//     if(!distinct_id){
//         distinct_id = uuidv4()
//     }

//     const client = new PostHog( phProjectAPIKey, { host: "https://us.i.posthog.com" })
//     const flags = await client.getAllFlags(distinct_id)

//     const experimentName = '12-new-landing-page' //
//     if(flags && flags[experimentName]){
//         if(flags[experimentName] === 'test' && req.url === '/'){
//             res.setHeader('Set-Cookie', cookie.serialize(phCookieName, JSON.stringify({ distinct_id }), { path: '/', httpOnly: true, maxAge: 60 * 60 * 24 * 365 })); // 1 year for example
//             res.writeHead(301, { Location: '/lp/v2' })
//             res.end();
//             return
//         }else{
//             next();
//         }
//     }else{
//         next();
//     }
// }
