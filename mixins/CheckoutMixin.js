import PosthogMixin from '@/mixins/PosthogMixin'

export default {
  mixins: [PosthogMixin],
  data () {
    return {
      isLoading: true,
      isPaying: false,
      discountPercentage: 0,
      discountAmount: 0,
      // currency: 'usd',
      couponCode: null,
      showCheckout: false,
      selectedPaymentProvider: 'credit-card'
    }
  },
  computed: {
    isSafariBrowser () {
      if (!process.client) {
        return false
      }
      return /^((?!chrome|android).)*safari/i.test(navigator.userAgent)
    },
    isChromeOrAndroid () {
      if (!process.client) {
        return false
      }
      const ua = navigator.userAgent.toLowerCase()
      return ua.includes('chrome') || ua.includes('android')
    },
    paymentProviders () {
      return [
        { images: [require('@/assets/img/card.svg')], name: this.$t('credit card'), id: 'credit-card' }, // [require('@/assets/img/payment-methods/visa.svg'), require('@/assets/img/payment-methods/mastercard.svg'), require('@/assets/img/payment-methods/jcb.svg')]
        ...(this.$store.state.user.role !== 'TeamLead' ? [{ images: [require('@/assets/img/payment-methods/paypal.svg')], name: 'PayPal', id: 'paypal' }] : []),
        ...(this.isSafariBrowser ? [{ images: [require('@/assets/img/payment-methods/applepay.svg')], name: 'Apple Pay', id: 'apple-pay' }] : []),
        ...(this.isChromeOrAndroid ? [{ images: [require('@/assets/img/google-pay.png')], name: 'Google Pay', id: 'google-pay' }] : []),
        { images: [require('@/assets/img/logo-ideal.svg')], name: 'iDEAL', currencies: ['eur'], id: 'ideal' },
        { images: [require('@/assets/img/logo-bancontact.svg')], name: 'Bancontact', currencies: ['eur'], id: 'cancontact' },
        { images: [require('@/assets/img/logo-giropay.svg')], name: 'Giropay', currencies: ['eur'], id: 'giropay' },
        { images: [require('@/assets/img/logo-grab-pay.svg')], name: 'Grab Pay', currencies: ['sgd', 'myr'], id: 'grab-pay' },
        { images: [require('@/assets/img/logo-paynow.svg')], name: 'PayNow', currencies: ['sgd'], id: 'pay-now' },
        // { images: [require('@/assets/img/logo-sofort.svg')], name: 'Sofort', currencies: ['eur'], id: 'sofort' }
        { images: [require('@/assets/img/eps.svg')], name: 'EPS', currencies: ['eur'], id: 'eps' },
        { images: [require('@/assets/img/p24.png')], name: 'Przelewy24', currencies: ['pln'], id: 'p24' },
        { images: [require('@/assets/img/upi.png')], name: 'UPI', description: 'Settled in SGD', currencies: ['inr'], id: 'upi' },
        { images: [require('@/assets/img/qris.svg')], name: 'QRIS', currencies: ['idr'], id: 'qris' },
        { images: [require('@/assets/img/alipay.png')], name: 'Alipay', currencies: ['cny', 'sgd'], id: 'alipay' }
      ]
    },
    canPay () {
      return !!this.selectedPaymentProvider
    },
    selectedCurrency () {
      let preferredCurrency = 'usd'
      if (process.client && localStorage.getItem('preferedCurrency')) {
        const storedPrefferedCurrency = localStorage.getItem('preferedCurrency')
        if (this.allowedCurrencyCodes.includes(storedPrefferedCurrency)) {
          preferredCurrency = storedPrefferedCurrency
          return preferredCurrency
        }
      }
      // Fallback logic might be needed depending on how default currency is handled
      const defaultCurrency = 'usd' // this.$store.state.user.countryCode
      // ? (this.$localization.countryCurrency[this.$store.state.user.countryCode] || 'usd')
      // : 'usd'
      return defaultCurrency
    },
    currency () {
      return this.$store.state.upsell.currency
    },
    happyCustomers () {
      return Math.round(this.$store.state.stats.users.replace(/,/g, '') / 1000) + 'K'
    },
    trustPilotReviews () {
      return this.$store.state.trustpilotReviews
    }
  },
  methods: {
    async pay (productType = 'package', embedded = false) {
      if (this.selectedPaymentProvider === 'paypal') {
        await this.toPayPal(productType)
      } else if (this.selectedPaymentProvider === 'upi' || this.selectedPaymentProvider === 'qris') {
        await this.toHitPay(productType)
      } else {
        await this.toCheckout(productType, embedded)
      }
    },
    async toHitPay (productType = 'package') {
      try {
        this.isPaying = true
        this.trackCheckoutStartEvent()

        const response = await this.$axios.$post('/checkout/hitpay/create-checkout-session', {
          priceId: this.priceId,
          productId: this.productId,
          currency: this.selectedCurrency || 'sgd',
          version: this.$cookies.get('postcrafts_version') || null,
          rewardfulId: this.userWasReferredViaInternalSystem ? null : (window?.Rewardful?.referral || null),
          quantity: this.quantity,
          couponCode: this.couponCode,
          parityDiscountDollarAmount: this.parityDiscountDollarAmount,
          userCountry: this.$store?.state?.user?.countryCode || null,
          trafficAttribution: JSON.parse(localStorage.getItem('hsp_traffic_source') || '[]').map(item => item.source) || [],
          trackingData: this.getTrackingData(),
          locale: this.$i18n.locale,
          paymentIntentId: this.basket?._id,
          productType,
          tests: this.posthogFeatureFlags
        })

        if (response && response.success) {
          this.$facebook.trackEvent('InitiateCheckout')
          this.$posthog.capture('$funnel:checkout_started', {
            provider: 'hitpay',
            product: productType,
            team: false
          })
          window.location.href = response.url || response?.data?.url
          setTimeout(() => {
            this.isPaying = false
          }, 5000)
        } else {
          this.$toast.open({ type: 'error', message: this.$t('Something went wrong while setting up your payment. Please contact support.') })
        }
      } catch (err) {
        this.handleError(err)
      } finally {
        setTimeout(() => {
          this.isPaying = false
        }, 5000)
      }
    },
    async toPayPal (productType = 'package') {
      try {
        this.isPaying = true
        this.trackCheckoutStartEvent()
        // const url = (this.checkIfUserIsInTestId('17-lemonsqueezy-vs-paypal') ? '/checkout/lemonsqueezy/create-checkout-session' : '/checkout/paypal/create-payment-intent')
        const response = await this.$axios.$post('/checkout/paypal/create-payment-intent', {
          priceId: this.priceId,
          productId: this.productId,
          currency: this.selectedCurrency || 'usd',
          version: this.$cookies.get('postcrafts_version') || null,
          rewardfulId: this.userWasReferredViaInternalSystem ? null : (window?.Rewardful?.referral || null),
          quantity: this.quantity,
          couponCode: this.couponCode,
          parityDiscountDollarAmount: this.parityDiscountDollarAmount,
          userCountry: this.$store?.state?.user?.countryCode || null,
          trafficAttribution: JSON.parse(localStorage.getItem('hsp_traffic_source') || '[]').map(item => item.source) || [],
          trackingData: this.getTrackingData(),
          locale: this.$i18n.locale,
          paymentIntentId: this.basket?._id,
          productType,
          tests: this.posthogFeatureFlags
        })
        if (response && response.success) {
          this.$facebook.trackEvent('InitiateCheckout')
          this.$posthog.capture('$funnel:checkout_started', {
            provider: 'paypal',
            product: productType,
            team: false
          })
          window.location.href = response.url || response?.data?.url
          setTimeout(() => {
            this.isPaying = false
          }, 5000)
        } else {
          this.$toast.open({ type: 'error', message: this.$t('Something went wrong while setting up your payment. Please contact support.') })
        }
      } catch (err) {
        this.handleError(err)
      } finally {
        setTimeout(() => {
          this.isPaying = false
        }, 5000)
      }
    },
    getTrackingData () {
      const _fbc = this.$cookies.get('_fbc') || this.$cookies.get('hsp_fbclid') || null
      const _fbp = this.$cookies.get('_fbp') || this.$cookies.get('hsp_fbclid') || null
      const gclid = this.$cookies.get('hsp_gclid') || null

      return {
        ...(_fbc ? { _fbc } : {}),
        ...(_fbp ? { _fbp } : {}),
        ...(gclid ? { gclid } : {}),
        conversionUrl: window.location.href
      }
    },

    async toCheckout (productType = 'package', embedded = false) {
      try {
        this.$loading.show({
          title: 'Loading checkout...'
        })
        this.isPaying = true
        this.trackCheckoutStartEvent()
        const { success, data, errorMessage } = await this.$axios.$post('/checkout/stripe/create-checkout-session', {
          currency: this.selectedCurrency || 'usd', // currency: currency || 'usd',
          priceId: this.priceId,
          productId: this.productId,
          quantity: this.quantity,
          rewardfulId: this.userWasReferredViaInternalSystem ? null : (window?.Rewardful?.referral || null),
          partnerId: this.$cookies.get('hp_partner_id') || null,
          couponCode: this.couponCode,
          parityDiscountDollarAmount: this.parityDiscountDollarAmount,
          userCountry: this.$store?.state?.user?.countryCode || null,
          trafficAttribution: JSON.parse(localStorage.getItem('hsp_traffic_source') || '[]').map(item => item.source) || [],
          trackingData: this.getTrackingData(),
          locale: this.$i18n.locale,
          paymentIntentId: this.basket?._id,
          productType,
          tests: this.posthogFeatureFlags,
          embedded
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.$facebook.trackEvent('InitiateCheckout')
        this.$posthog.capture('$funnel:checkout_started', {
          provider: 'stripe',
          product: productType,
          team: false
        })
        if (embedded) {
          this.clientSecret = data.client_secret
          this.showCheckout = true

          // Wait for DOM to update before mounting
          await this.$nextTick()

          // Create and mount embedded checkout
          await this.$stripe.createEmbeddedCheckout(this.clientSecret, {
            onComplete: () => {
              // Handle successful payment
              this.$router.push('/checkout/success')
            }
          })

          // Use ref instead of selector
          if (this.$refs.checkoutContainer) {
            await this.$stripe.mountEmbeddedCheckout(this.$refs.checkoutContainer)
          }
          // return data.url
        } else {
          window.location.href = data.url
        }
      } catch (err) {
        this.handleError(err)
      } finally {
        this.$loading.hide()
        setTimeout(() => {
          this.isPaying = false
        }, 5000)
      }
    },
    async toTeamCheckout () {
      if (this.isPaying) {
        return
      }

      try {
        this.$loading.show({
          title: 'Redirecting to checkout'
        })

        this.trackCheckoutStartEvent()
        this.isPaying = true

        const { success, data, errorMessage } = await this.$axios.$post('/checkout/stripe/create-checkout-session-team', {
          priceId: this.priceId,
          quantity: this.quantity ?? 1,
          currency: this.selectedCurrency || 'usd',
          rewardfulId: this.userWasReferredViaInternalSystem ? null : (window?.Rewardful?.referral || null),
          partnerId: this.$cookies.get('hp_partner_id') || null
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.$facebook.trackEvent('InitiateCheckout')
        this.$posthog.capture('$funnel:checkout_started', {
          provider: 'stripe',
          product: 'package',
          team: true
        })
        window.location.href = data.url
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isPaying = false
        this.$loading.hide()
      }
    },
    trackCheckoutStartEvent () {
      this.$gtm.push({
        event: 'upsellCheckoutStarted',
        email: this.$store.state?.user?.email || null
      })
    },
    applyDiscount (event) {
      const { amount, code, type } = event
      if (type === 'percentage') {
        this.discountPercentage = amount
      }
      if (type === 'amount') {
        this.discountAmount = amount
      }

      if (amount === 0 || !code) {
        this.couponCode = null
        this.discountPercentage = 0
        this.discountAmount = 0
      }
      if (code) {
        this.couponCode = code
      }
    }
  }
}
