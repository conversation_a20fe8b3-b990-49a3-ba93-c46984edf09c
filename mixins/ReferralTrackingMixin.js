const mixin = {
  methods: {
    trackReferralProgram (code) {
      this.$cookies.set('hsp:referral', code, {
        maxAge: 60 * 60 * 24 * 7, // 7 days
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax'
      })

      localStorage.setItem('hsp:referral', code)
      localStorage.setItem('hsp:referral:timestamp', Date.now().toString())
    },
    checkOutdatedReferralProgram () {
      const referralTimestamp = localStorage.getItem('hsp:referral:timestamp')

      if (referralTimestamp) {
        const now = Date.now()
        const referralDate = new Date(referralTimestamp)
        const diffTime = Math.abs(now - referralDate)
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

        if (diffDays > 7) {
          this.removeReferralProgram()
        }
      }
    },
    removeReferralProgram () {
      this.$cookies.remove('hsp:referral')

      localStorage.removeItem('hsp:referral')
      localStorage.removeItem('hsp:referral:timestamp')
    }
  },
  mounted () {
    this.checkOutdatedReferralProgram()
  }
}

export default mixin
