const mixin = {
  computed: {
    posthogFeatureFlags () {
      return this.$store.state.posthogFeatureFlags
    }
  },
  methods: {
    experimentIsTest (testId) {
      if (this.featureFlagExists(testId)) {
        return this.getFeatureFlagVariant(testId) === 'test'
      }
      return false
    },
    getFeatureFlagVariant (testId) {
      try {
        // Overwrite if URL has testFlag and testVariant
        if (this.$route.query.testFlag === testId && this.$route.query.testVariant) {
          return this.$route.query.testVariant
        }
        return this.posthogFeatureFlags[testId]
      } catch (e) {
        console.log('error getting feature flag variant', e)
        return 'control'
      }
    },
    featureFlagExists (testId) {
      try {
        // Overwrite if URL has testFlag and testVariant even if the feature flag doesn't exist
        if (this.$route.query.testFlag === testId && this.$route.query.testVariant) {
          return true
        }
        return this.posthogFeatureFlags[testId] !== undefined
      } catch (e) {
        console.log('error checking if feature flag exists', e)
        return false
      }
    }
  }
}

export default mixin
