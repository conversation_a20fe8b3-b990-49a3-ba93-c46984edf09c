import debounce from '../../../utils/debounce'

const mixin = {
  props: {
    member: {
      type: Object,
      required: true
    },
    teamId: {
      type: String,
      required: true
    },
    selectedStyle: {
      type: String,
      required: false,
      default: null
    },
    favoritePictures: {
      type: Array,
      required: false,
      default: () => []
    }
  },
  data () {
    return {
      updatedMember: JSON.parse(JSON.stringify(this.member)),
      debouncedUpsertMember: debounce(() => this.upsertMember(), 2500, { leading: false })
    }
  },
  computed: {
    styleIsLoading () {
      return this.selectedStyle && this.member.styles?.[this.selectedStyle]?.status !== 'completed'
    },
    styleImage () {
      if (!this.selectedStyle) {
        return this.member.basePictureUrl
      }

      return this.styleIsLoading ? require('~/assets/img/loading.gif') : this.member.styles?.[this.selectedStyle]?.result
    },
    editable () {
      if (this.styleIsLoading) {
        return false
      }

      return true
    }
  },
  methods: {
    updatePicture (newUrl) {
      this.updatedMember.basePictureUrl = newUrl
      this.debouncedUpsertMember()
    },
    updateRole (newRole) {
      this.updatedMember.role = newRole
      this.debouncedUpsertMember()
    },
    updateName (newName) {
      this.updatedMember.name = newName
      this.debouncedUpsertMember()
    },
    upsertMember () {
      this.$axios.post(`/tools/team-page/page/${this.teamId}/upsert`, {
        ...this.updatedMember
      })
        .then((res) => {
          if (!this.updatedMember._id) {
            this.$emit('added-member', res.data.data.member)
          } else {
            this.$emit('updated-member')
          }

          this.updatedMember._id = res.data.data.member._id
          this.updatedMember.id = res.data.data.member.id
        })
        .catch(() => {
          this.$toast.error('Error updating member')
        })
    },
    deletedMember () {
      this.$emit('deleted-member', this.updatedMember.id)
    }
  }
}

export default mixin
