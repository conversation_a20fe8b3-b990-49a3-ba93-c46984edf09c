import ModelPersonalizationMixin from './ModelPersonalizationMixin'

export default {
  mixins: [ModelPersonalizationMixin],
  mounted () {
    //
  },
  computed: {
    styles () {
      const styles = this.$store.state.styles
      const userOrganization = this.$store.state?.organization?.organization?._id
      const filteredStyles = styles.filter((style) => {
        if (!style?.organizationIds) { return true }
        if (style?.organizationIds?.length === 0) { return true }
        if (style?.organizationIds?.includes(userOrganization)) { return true }
        return false
      })
      return filteredStyles
    },
    clothingItems () {
      return this.$store.state.clothing || []
    },
    selectedStyles () {
      return this.$store.state.upsell.selectedStyles
    },
    personalInfoSummary () {
      return {
        age: this.$store.state.upsell.selectedAge,
        ethnicity: this.$store.state.upsell.selectedEthnicity,
        bodyType: this.$store.state.upsell.selectedBodyType,
        height: this.$store.state.upsell.selectedHeight,
        glasses: this.$store.state.upsell.selectedGlasses
      }
    }
  }
}
