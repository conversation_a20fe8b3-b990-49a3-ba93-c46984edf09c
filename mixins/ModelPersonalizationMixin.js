import md5 from 'md5'

export default {
  data () {
    return {
      ageList: [
        { title: this.$t('12 - 18 years'), value: 'adolescence' },
        { title: this.$t('19 - 25 years'), value: 'young adult' },
        { title: this.$t('26 - 29 years'), value: 'late twenties' },
        { title: this.$t('30 - 35 years'), value: 'early thirties' },
        { title: this.$t('36 - 45 years'), value: 'mid-life' },
        { title: this.$t('46 - 55 years'), value: 'middle-aged' },
        { title: this.$t('56 - 65 years'), value: 'late middle age' },
        { title: this.$t('66 - 75 years'), value: 'senior' },
        { title: this.$t('76+ years'), value: 'elderly' }
      ],
      ethnicityList: [
        { title: this.$t('Select your option'), value: '' },
        { title: this.$t('African'), value: 'african' },
        { title: this.$t('Arabic'), value: 'arabic' },
        { title: this.$t('Asian'), value: 'asian' },
        { title: this.$t('Black or African American'), value: 'black-or-african-american' },
        { title: this.$t('Caribbean'), value: 'caribbean' },
        { title: this.$t('Indian'), value: 'indian' },
        { title: this.$t('Melanesian'), value: 'melanesian' },
        { title: this.$t('Polynesian'), value: 'polynesian' },
        { title: this.$t('European'), value: 'european' },
        { title: this.$t('Caucasian'), value: 'caucasian' },
        { title: this.$t('Latin American'), value: 'latin-american' },
        { title: this.$t('Hispanic'), value: 'hispanic' },
        { title: this.$t('Other'), value: 'other' }
      ],
      eyeColors: [
        { id: 'hazel', label: this.$t('Hazel'), color: '#A6B6B5' },
        { id: 'gray', label: this.$t('Gray'), color: '#C6CCD1' },
        { id: 'light-brown', label: this.$t('Light brown'), color: '#B79885' },
        { id: 'blue', label: this.$t('Blue'), color: '#8BA4C0' },
        { id: 'green', label: this.$t('Green'), color: '#C0D1BD' },
        { id: 'dark-brown', label: this.$t('Dark brown'), color: '#4D3028' }
      ],
      heightList: [
        { value: 'under_150cm', title: '< 150 cm (4\'11")' },
        { value: '150-160cm', title: '150 - 160 cm (4\'11" - 5\'3")' },
        { value: '161-170cm', title: '161 - 170 cm (5\'3" - 5\'7")' },
        { value: '171-180cm', title: '171 - 180 cm (5\'7" - 5\'11")' },
        { value: '181-190cm', title: '181 - 190 cm (5\'11" - 6\'3")' },
        { value: 'over_190cm', title: '>= 190 cm (6\'3"+)' }
      ],
      weightList: [
        { value: 'under_40kg', title: '< 40 kg (88 lbs)' },
        { value: '40-50kg', title: '40 - 50 kg (88 - 110 lbs)' },
        { value: '51-60kg', title: '51 - 60 kg (110 - 132 lbs)' },
        { value: '61-70kg', title: '61 - 70 kg (132 - 154 lbs)' },
        { value: '71-80kg', title: '71 - 80 kg (154 - 176 lbs)' },
        { value: '81-90kg', title: '81 - 90 kg (176 - 198 lbs)' },
        { value: '91-100kg', title: '91 - 100 kg (198 - 220 lbs)' },
        { value: 'over_100kg', title: '>= 100 kg (220 lbs)' }
      ],
      bodyTypeList: [
        { value: 'ectomorph', title: this.$t('Ectomorph (Slim)') },
        { value: 'mesomorph', title: this.$t('Mesomorph (Athletic)') },
        { value: 'endomorph', title: this.$t('Endomorph (Full)') }
      ],
      glassesList: [
        { value: 'none', title: this.$t('No glasses') },
        { value: 'half', title: this.$t('Half with glasses') },
        { value: 'always', title: this.$t('Always with glasses') }
      ],
      glassesEditList: [
        { value: 'no', title: this.$t('No') },
        { value: 'yes', title: this.$t('Yes') }
      ]
    }
  },
  computed: {
    clothingForGender () {
      if (!this.gender) {
        return this.clothingItems.filter(item => !!this.clotheImage(item))
      }

      return this.clothingItems
        .filter((item) => {
          if (!item?.gender) { return false }
          return item.gender.includes(this.gender)
        })
        .filter(item => !!this.clotheImage(item))
    }
  },
  methods: {
    clotheImage (clothe) {
      if (typeof clothe === 'string') {
        clothe = this.clothingItems.find(item => item._id === clothe)
      }

      if (this.gender === 'male' && clothe.images?.male) {
        return clothe.images.male
      }

      if (this.gender === 'female' && clothe.images?.female) {
        return clothe.images.female
      }

      return clothe.images?.male || clothe.images?.female || ''
    },
    styleImage (style) {
      if (typeof style === 'string') {
        style = this.styles.find(item => item._id === style)
      }

      if (this.gender === 'male' && style.image?.maleImage) {
        return style.image.maleImage
      }

      if (this.gender === 'female' && style.image?.femaleImage) {
        return style.image.femaleImage
      }

      return style.image?.maleImage || style.image?.femaleImage || ''
    },
    styleName (id) {
      const style = this.$store.state.styles.find(style => style._id === id)
      return style?.title || ''
    },
    clothingName (id) {
      const clothing = this.$store.state.clothing.find(clothing => clothing._id === id)
      return clothing?.type || ''
    },
    eyeColorFromId (id) {
      if (id?.includes(' eyes')) {
        id = id.replace(' eyes', '')
      }

      return this.eyeColors.find(color => color.id === id)
    },
    genderFromId (id) {
      return id === 'male' ? this.$t('Male') : this.$t('Female')
    },
    ageFromId (id) {
      const hashed = this.ageList.find(age => md5(age.value) === id)
      return hashed || this.ageList.find(age => age.value === id) || { title: id }
    },
    ethnicityFromId (id) {
      const hashed = this.ethnicityList.find(ethnicity => md5(ethnicity.value) === id)
      return hashed || this.ethnicityList.find(ethnicity => ethnicity.value === id)
    },
    bodyTypeFromId (id) {
      const hashed = this.bodyTypeList.find((bodyType) => {
        if (md5(bodyType.value) === id) {
          return true
        }

        const newValue = {
          mesomorph: 'athletic',
          ectomorph: 'slim',
          endomorph: 'curvy'
        }[bodyType.value] || bodyType.value

        return md5(newValue) === id || md5(newValue + ' body') === id
      })

      return hashed || this.bodyTypeList.find(bodyType => bodyType.value === id)
    },
    heightFromId (id) {
      const initialHeight = this.heightList.find(height => md5(height.value) === id)
      if (initialHeight) {
        return initialHeight
      }

      const hashed = this.heightList.find((height) => {
        const newValue = height.value.replace('_', ' ') + ' tall'
        return md5(newValue) === id
      })

      return hashed || this.heightList.find(height => height.value === id)
    },
    weightFromId (id) {
      const initialWeight = this.weightList.find(weight => md5(weight.value) === id)
      if (initialWeight) {
        return initialWeight
      }

      const hashed = this.weightList.find((weight) => {
        const newValue = weight.value.replace('_', ' ') + ' weight'
        return md5(newValue) === id
      })

      return hashed || this.weightList.find(weight => weight.value === id)
    },
    glassesFromId (id) {
      const hashed = this.glassesList.find(glasses => md5(glasses.value) === id)
      return hashed || this.glassesList.find(glasses => glasses.value === id)
    },
    hairstyleFromPrompt (prompt) {
      if (!prompt) { return null }
      return this.$store.state.hairstyles.find(hairstyle => hairstyle.prompt === prompt)
    },
    clothingFromPrompt (prompt) {
      if (!prompt) { return null }
      return this.$store.state.clothing.find(clothing => clothing.prompt === prompt)
    },
    styleFromPrompt (location) {
      if (!location) { return null }
      return this.$store.state.styles.find(style => style.location === location)
    },
    emotionFromPrompt (prompt) {
      if (!prompt) { return null }
      return this.$store.state.emotions.find(emotion => emotion.prompt === prompt)
    },
    metaVariable (key, defaultValue) {
      return this.photo.meta?.variables?.find(v => v.key === key)?.value || defaultValue
    }
  }
}
