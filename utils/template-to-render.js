/**
 * Converts a template string to a Vue render function
 * @param {TemplateStringsArray} strings - Template string parts
 * @param {...any} values - Template string interpolated values
 * @returns {Function} Vue render function that takes h as an argument
 */
export const template2h = (strings, ...values) => {
  // Convert template string to DOM nodes
  const template = document.createElement('template')
  template.innerHTML = strings.reduce((acc, str, i) =>
    acc + str + (values[i] || ''), '')

  // Convert DOM node to render function
  const toRenderFn = (node) => {
    if (node.nodeType === 3) { // Text node
      return node.textContent.trim() ? h => node.textContent : null
    }
    if (node.nodeType !== 1) { return null } // Skip non-element nodes

    return h => h(
      node.tagName.toLowerCase(),
      {
        class: [...node.classList],
        attrs: [...node.attributes]
          .reduce((acc, attr) => ({ ...acc, [attr.name]: attr.value }), {})
      },
      [...node.childNodes]
        .map(toRenderFn)
        .filter(Boolean)
        .map(fn => fn(h))
    )
  }

  return h => toRenderFn(template.content.firstElementChild)(h)
}
