const axios = require('./axios')

export function getImageSize (file) {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.src = window.URL.createObjectURL(file)
    img.onload = () => {
      resolve({
        width: img.width,
        height: img.height
      })
    }
    img.onerror = (error) => {
      reject(error)
    }
  })
};

export function resizeImage (blob, size, fileType = 'image/png', quality = 1) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(blob)
    reader.onload = (event) => {
      const img = new Image()
      img.src = event.target.result
      img.onload = () => {
        const elem = document.createElement('canvas')
        const scaleFactor = img.width / size
        elem.width = size
        elem.height = img.height / scaleFactor
        const ctx = elem.getContext('2d')
        ctx.drawImage(img, 0, 0, elem.width, elem.height)
        ctx.canvas.toBlob(
          (blob) => {
            resolve(blob)
          },
          fileType,
          quality
        )
      }
      img.onerror = (error) => {
        reject(error)
      }
    }
  })
};

export async function detectFaces (blob, shouldBypass) {
  const resizedBlob = await resizeImage(blob, 1024)
  const formData = new FormData()
  formData.append('files', resizedBlob)
  const size = await getImageSize(resizedBlob)
  formData.append('size', JSON.stringify(size))

  if (shouldBypass) {
    formData.append('bypass', '1')
  }

  const { success, data, errorMessage } = (await axios.post(
    '/image/face/detect',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }
  ))?.data
  if (success) {
    return { data }
  } else {
    return { errorMessage }
  }
};

export async function rankPhotos (paths, base) {
  // Check if both are array and length > 0
  if (!Array.isArray(paths) || paths.length === 0) { return [] }
  if (!Array.isArray(base) || base.length === 0) { return [] }

  const response = (await axios.post('/image/rank', { paths, base }))?.data

  if (response.success && response.id) {
    // Poll task on
    const pollTask = (id) => {
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Timeout after 30 seconds'))
        }, 30000) // 30 seconds
        const poll = () => {
          axios.get('/image/task/' + id)
            .then((response) => {
              if (response.data?.data?.status !== 'succeeded') {
                setTimeout(poll, 2000)
              } else {
                clearTimeout(timeout)
                resolve(response.data?.data?.output)
              }
            })
            .catch(reject)
        }
        poll()
      })
    }

    return pollTask(response.id)
  }
};

export function getLargestFace (faces) {
  const biggestFace = faces.reduce((acc, curr) => {
    // const { x: x1, y: y1 } = acc[0]
    // const { x: x2, y: y2 } = acc[3]
    // const { x: x3, y: y3 } = acc[2]
    // const { x: x4, y: y4 } = acc[1]

    // Corect for Google Vision
    const { x: x1, y: y1 } = acc[0]
    const { x: x2, y: y2 } = acc[1]
    const { x: x3, y: y3 } = acc[2]
    const { x: x4, y: y4 } = acc[3]
    const accArea = (x1 * y2 - y1 * x2 + (x2 * y3 - y2 * x3) + (x3 * y4 - y3 * x4) + (x4 * y1 - y4 * x1)) / 2

    // Corect for Google Vision
    const { x: x1c, y: y1c } = curr[0]
    const { x: x2c, y: y2c } = curr[1]
    const { x: x3c, y: y3c } = curr[2]
    const { x: x4c, y: y4c } = curr[3]
    // const { x: x1c, y: y1c } = curr[0]
    // const { x: x2c, y: y2c } = curr[3]
    // const { x: x3c, y: y3c } = curr[2]
    // const { x: x4c, y: y4c } = curr[1]

    const currArea = (x1c * y2c - y1c * x2c + (x2c * y3c - y2c * x3c) + (x3c * y4c - y3c * x4c) + (x4c * y1c - y4c * x1c)) / 2

    return currArea > accArea ? curr : acc
  }, faces[0])

  return biggestFace
};

export function getBoundingBox (face) {
  let minX = Number.MAX_SAFE_INTEGER
  let minY = Number.MAX_SAFE_INTEGER
  let maxX = Number.MIN_SAFE_INTEGER
  let maxY = Number.MIN_SAFE_INTEGER

  face.forEach((point) => {
    if (point.x < minX) {
      minX = point.x
    }
    if (point.x > maxX) {
      maxX = point.x
    }
    if (point.y < minY) {
      minY = point.y
    }
    if (point.y > maxY) {
      maxY = point.y
    }
  })

  return {
    x: minX,
    y: minY,
    width: maxX - minX,
    height: maxY - minY
  }
};

export async function cropHead (blob, boundingBox, sizeCheck = true, minFaceSize = 512, outputSize = 1024) {
  const img = new Image()
  img.src = window.URL.createObjectURL(blob)
  await img.decode()

  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')

  let { x, y, width, height } = boundingBox

  if (boundingBox.height > boundingBox.width) {
    width = boundingBox.height
    x = boundingBox.x - (width - boundingBox.width) / 2
  } else {
    height = boundingBox.width
    y = boundingBox.y - (height - boundingBox.height) / 2
  }

  x = Math.round(x)
  y = Math.round(y)
  width = Math.round(width)
  height = Math.round(height)

  const bufferWidth = width * 0.1
  const bufferHeight = height * 0.1

  x = Math.round(x - bufferWidth)
  y = Math.round(y - bufferHeight)
  width = Math.round(width + bufferWidth * 2)
  height = Math.round(height + bufferHeight * 2)

  if (x < 0) { x = 0 }
  if (y < 0) { y = 0 }
  if (x + width > img.width) { width = img.width - x }
  if (y + height > img.height) { height = img.height - y }

  // Calculate the aspect ratio of the cropped face
  const aspectRatio = width / height

  if (sizeCheck) {
    if (width < (minFaceSize * 0.8) || height < (minFaceSize * 0.8)) {
      return {
        success: false,
        blob,
        message: 'The face is too small. Please try again.'
      }
    }
  }
  // Determine the size of the image on the canvas
  let imageWidth, imageHeight
  if (aspectRatio > 1) {
    // If the face is wider than it is tall, set the image width to 1024 and scale the height proportionally
    imageWidth = outputSize
    imageHeight = outputSize / aspectRatio
  } else {
    // If the face is taller than it is wide, set the image height to 1024 and scale the width proportionally
    imageHeight = outputSize
    imageWidth = outputSize * aspectRatio
  }

  // Calculate the position to start drawing the image
  const dx = (outputSize - imageWidth) / 2
  const dy = (outputSize - imageHeight) / 2

  canvas.width = outputSize
  canvas.height = outputSize

  // Fill the canvas with white
  ctx.fillStyle = 'white'
  ctx.fillRect(0, 0, canvas.width, canvas.height)

  ctx.drawImage(img, x, y, width, height, dx, dy, imageWidth, imageHeight)

  const faceImage = canvas.toDataURL('image/jpeg', 1)

  const originalArea = img.width * img.height
  const faceArea = width * height
  const faceCoverage = faceArea / originalArea

  return {
    image: faceImage,
    faceCoverage,
    success: true
  }
};

export async function cropBody (blob, boundingBox) {
  const img = new Image()
  img.src = window.URL.createObjectURL(blob)
  await img.decode()

  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')

  const squareSize = Math.min(img.width, img.height)

  let extractY = boundingBox.y - 32
  if (extractY < 0) { extractY = 0 }
  if (squareSize + extractY > img.height) { extractY = img.height - squareSize }

  const extractHeight = squareSize

  let extractX = 0
  const extractWidth = squareSize
  if (squareSize === img.height) {
    const photoWidth = img.width
    const faceWidth = boundingBox.width

    const spaceBetweenSquareAndFace = (squareSize - faceWidth) / 2
    const spaceBetweenSquareAndPhoto = (photoWidth - squareSize) / 2

    extractX = spaceBetweenSquareAndFace - spaceBetweenSquareAndPhoto + (faceWidth / 2)
    if (extractX < 0) { extractX = 0 }
    if (extractX + squareSize > photoWidth) { extractX = photoWidth - squareSize }
  }

  canvas.width = extractWidth
  canvas.height = extractHeight

  ctx.drawImage(img, extractX, extractY, extractWidth, extractHeight, 0, 0, extractWidth, extractHeight)

  const faceImage = canvas.toDataURL('image/jpeg', 1)
  return { image: faceImage, success: true }
};

export default {
  methods: {
    getImageSize,
    resizeImage,
    detectFaces,
    rankPhotos,
    getLargestFace,
    getBoundingBox,
    cropHead,
    cropBody
  }
}
