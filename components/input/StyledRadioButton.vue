<template>
  <div class="flex items-center content-center justify-start p-2 space-x-2 rounded cursor-pointer" :class="{'border border-blue-300 bg-blue-100':model === value, ' bg-gray-100':model !== value}" @click="updateSelf(value)">
    <div class="flex items-center content-center justify-center w-4 h-4 rounded-full" :class="{'bg-white border-blue-300 border': model !== value, 'bg-white': model === value}">
      <div v-if="model === value" class="w-2 h-2 bg-blue-500 rounded-full" />
    </div>
    <span :for="value" class="text-sm" :class="{'font-medium text-blue-500': model === value}">
      {{ label }}
    </span>
  </div>
</template>

<script>
export default {
  props: {
    model: {
      type: String,
      required: true
    },
    label: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      default: ''
    }
  },
  methods: {
    updateSelf (name) {
      this.$emit('click', name)
    }
  }
}
</script>

<style>

</style>
