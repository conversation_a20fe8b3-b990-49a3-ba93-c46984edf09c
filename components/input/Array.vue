<template>
  <div class="array-input">
    <label
      v-if="label"
      :for="id"
      class="block text-sm font-medium leading-5"
    >
      {{ label }}
    </label>
    <ul class="mt-2">
      <li v-for="(item, index) in localItems" :key="index" class="flex items-center justify-start space-x-2">
        <input v-model="localItems[index]" class="bg-gray-50 text-xs shadow-inner rounded-md px-2 py-1.5 border border-gray-100" @input="updateModel">
        <ButtonWhite size="xs" @click="removeItem(index)">
          Remove
        </ButtonWhite>
      </li>
    </ul>
    <div class="flex items-center justify-start space-x-2">
      <input
        :id="id"
        v-model="newItem"
        class="block w-full bg-dark/10 border-black/20 text-black px-3 pt-2 pb-2 bg-white border rounded-lg border-gray-300 placeholder-gray-400 focus:outline-none focus:border-teal-500 focus:ring-teal-500 sm:text-sm focus:ring-1"
        placeholder="Enter new item"
        @keyup.enter="addItem"
      >
      <ButtonWhite class="flex-shrink-0" size="xs" :disabled="!newItem.trim()" @click="addItem">
        Add Item
      </ButtonWhite>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ArrayInput',
  props: {
    label: {
      type: String,
      default: ''
    },
    items: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      newItem: '',
      localItems: []
    }
  },
  watch: {
    items: {
      handler (newValue) {
        this.localItems = [...newValue]
      },
      deep: true
    }
  },
  created () {
    this.localItems = [...this.items]
  },
  methods: {
    addItem () {
      if (this.newItem.trim()) {
        this.localItems.push(this.newItem.trim())
        this.newItem = ''
        this.updateModel()
      }
    },
    removeItem (index) {
      this.localItems.splice(index, 1)
      this.updateModel()
    },
    updateModel () {
      this.$emit('update', this.localItems)
    }
  }
}
</script>

  <style scoped>
  .array-input ul {
    list-style-type: none;
    padding: 0;
  }

  .array-input li {
    margin-bottom: 10px;
  }

  .array-input input {
    margin-right: 10px;
  }
  </style>
