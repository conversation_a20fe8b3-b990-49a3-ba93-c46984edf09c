<template>
  <div class="">
    <div v-if="label || description" class="mb-1 flex flex-col items-start justify-between space-y-0.5">
      <div class="flex justify-between w-full items-center">
        <label
          v-if="label"
          for="email"
          class="block text-sm font-semibold leading-5"
          :class="{
            'text-primary-500': !disabled,
            'text-gray-400': disabled,
            'text-white/90': darkMode,
            'text-gray-700': !darkMode
          }"
        >
          {{ label }}
        </label>
        <slot name="action" />
      </div>

      <span v-if="description" class="text-xs text-gray-500 italic">
        {{ description }}
      </span>
    </div>
    <div class="relative rounded-md shadow-sm">
      <input
        :id="id"
        :type="type"
        :value="value"
        :max="['number', 'range'].includes(type) ? max : undefined"
        :min="['number', 'range'].includes(type) ? min : undefined"
        :placeholder="placeholder"
        class="block w-full px-3 pt-2 pb-2.5 bg-white border rounded-lg border-gray-300 placeholder-gray-400 focus:outline-none focus:border-teal-500 focus:ring-teal-500 sm:text-sm focus:ring-1"
        :list="list"
        :class="{
          'border-red-500 focus:border-red-500': error,
          'border-black/20': !error,
          'opacity-50': disabled,
          'opacity-100': !disabled,
          'border-white/20 bg-white/10 text-white': darkMode,
          'bg-dark/10 border-black/20 text-black': !darkMode,
          [inputClass]: !!inputClass
        }"
        :disabled="disabled"
        @input="$emit('input', $event.target.value)"
        @keypress="validateInput($event)"
        @keyup="type === 'number' ? validateInput($event) : null"
        @keyup.enter="$emit('handleEnter')"
      >
      <slot name="affix" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    min: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      default: 999999999
    },
    list: {
      type: String,
      default: null
    },
    darkMode: {
      type: Boolean,
      default: false
    },
    numbersOnly: {
      type: Boolean,
      default: false
    },
    label: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    },
    value: {
      type: [String, Number],
      default: ''
    },
    description: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'text'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    error: {
      type: Boolean,
      default: false
    },
    charRegex: {
      type: String,
      default: ''
    },
    inputClass: {
      type: String,
      default: ''
    }
  },
  methods: {
    validateInput (event) {
      if (this.numbersOnly) {
        this.inputNumbersOnly(event)
      } else if (this.charRegex) {
        this.charRegexOnly(event)
      }

      if (event.target.value) {
        if (this.type === 'number' || this.type === 'range') {
          if (event.target.value > this.max) {
            event.target.value = this.max
            this.$emit('input', this.max)
            event.preventDefault()
          } else if (event.target.value < this.min) {
            event.target.value = this.min
            this.$emit('input', this.min)
            event.preventDefault()
          }
        } else if (event.target.value.length > this.max) {
          event.preventDefault()
        }
      }
    },
    charRegexOnly (evt) {
      const charCode = evt.which ? evt.which : evt.keyCode
      const char = String.fromCharCode(charCode)
      const regex = new RegExp('([' + this.charRegex + '])')
      if (char.match(regex)) {
        return true
      } else {
        evt.preventDefault()
      }
    }
  }
}
</script>

<style></style>
