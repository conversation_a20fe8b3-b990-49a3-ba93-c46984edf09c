<template>
  <div :id="item.id" class="bg-white rounded-md border border-[#E4E4E7]">
    <div class="flex items-center justify-between cursor-pointer px-3 py-2" @click="toggleOpen">
      <p class="text-[13px] text-paragraph">
        <span class="font-bold opacity-[0.23] text-paragraph text-sm inline-block mr-2">?</span>
        <span>{{ item.question }}</span>
      </p>
      <div v-if="open" class="w-6 h-6 flex items-center justify-center">
        <div class="w-[10px] h-[1.5px] rounded-full bg-paragraph flex-shrink-0" />
      </div>
      <IconPlus v-else class="text-paragraph flex-shrink-0" />
    </div>
    <div v-if="open" class="px-4 pb-3">
      <div class="h-px bg-black/5" />
      <p class="mt-3 text-[13px] text-paragraph" v-html="item.answer" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      open: false
    }
  },
  methods: {
    toggleOpen () {
      if (this.item.onClick) {
        this.item.onClick()
        return
      }

      if (this.item.link) {
        this.$router.push(this.item.link)
        return
      }

      this.open = !this.open
    }
  }
}
</script>
