<template>
  <Popup size="xl" @closeModal="$emit('closeModal')">
    <h2 class="text-lg font-semibold text-primary-500">
      {{ title }}
    </h2>
    <p class="text-slate-500 font-medium">
      {{ subtitle }}
    </p>
    <div class="flex flex-col gap-2 mt-6">
      <FaqSmallCollapsibleItem v-for="faq in faqs" :key="faq.question" :item="faq" />
    </div>
    <div class="mt-6">
      <ButtonWhite size="sm" @click="$emit('closeModal')">
        Close popup
      </ButtonWhite>
    </div>
  </Popup>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: 'Frequently asked questions'
    },
    subtitle: {
      type: String,
      default: 'Answers to common questions'
    },
    faqs: {
      type: Array,
      default: () => []
    }
  }
}
</script>
