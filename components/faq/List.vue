<template>
  <div class="">
    <div class="text-center">
      <h2 class="text-3xl font-bold text-gray-900">
        Frequently Asked Questions
      </h2>
      <!-- <p class="mt-2 text-base text-gray-600 font-pj">
        Make sure to read this questions and answers before you make your purchase
      </p> -->
    </div>

    <div class="grid w-full grid-cols-1 mx-auto mt-10 sm:mt-10 md:grid-cols-2 gap-8">
      <FaqItem v-for="(item, index) in items" :key="'faq-item-' + index" :item="item" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    items: {
      type: Array,
      required: true
    }
  }

}
</script>

<style>

</style>
