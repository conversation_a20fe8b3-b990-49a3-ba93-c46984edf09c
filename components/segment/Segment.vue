<template>
  <div class="flex">
    <button
      v-for="(item, index) in items"
      :key="item.value"
      type="button"
      class="text-sm px-4 py-3 leading-none border border-[#D0D5DD]"
      :class="{
        'text-paragraph font-medium bg-white': activeItem !== item.value,
        'text-primary-500 font-bold bg-[#F9FAFB]': activeItem === item.value,
        'rounded-l-lg': index === 0,
        'rounded-r-lg': index === items.length - 1,
        'border-r-0': index < items.length - 1
      }"
      @click="handleClick(item.value)"
    >
      {{ item.label }}
    </button>
  </div>
</template>

<script>
export default {
  props: {
    items: {
      type: Array,
      required: true,
      validator: value => value.every(item => 'label' in item && 'value' in item)
    },
    initialActive: {
      type: String,
      default: null
    }
  },
  data () {
    return {
      activeItem: this.initialActive
    }
  },
  methods: {
    handleClick (value) {
      this.activeItem = value
      this.$emit('selected', value)
    }
  }
}
</script>
