<template>
  <MaintenanceWrapper>
    <section v-if="variant" class="bg-cover bg-no-repeat bg-right-top w-full overflow-hidden" :style="{ backgroundImage: 'url(' + require('@/assets/img/login-bg.png') + ')' }">
      <div class="bg-white bg-opacity-95 px-5 pb-5 pt-20 min-h-screen xl:max-w-none xl:h-screen xl:p-8">
        <div class="grid grid-cols-1 max-w-[500px] mx-auto xl:grid-cols-2 xl:max-w-none">
          <div class="flex items-center justify-center flex-1 xl:px-8">
            <div class="w-full">
              <nuxt-link to="/" title="" class="flex">
                <img class="h-7 w-auto" src="@/assets/img/logo.svg" alt="">
              </nuxt-link>
              <slot name="left" />
            </div>
          </div>
          <div class="w-full mx-auto mt-10 hidden xl:flex xl:mt-20 lg:max-w-[500px] xl:max-w-[645px]">
            <div class="w-full max-w-[400px] mx-auto relative xl:max-w-none xl:rounded-xl">
              <div class="grid grid-cols-2">
                <div class="relative">
                  <div class="login-card absolute -left-7 -top-4 animate-[firstCardBack_1s_ease-in-out] rotate-[-5.54deg]" />
                  <div class="login-card relative animate-[firstCardFront_1s_ease-in-out] rotate-[-7.29deg]" :style="{ backgroundImage: 'url(' + require('@/assets/img/testimonial/testimonial-4.jpeg') + ')' }" />
                </div>
                <div class="relative">
                  <div class="login-card absolute left-9 -top-6 animate-[secondCardBack_1s_ease-in-out] rotate-[9.22deg]" />
                  <div class="login-card relative animate-[secondCardFront_1s_ease-in-out] rotate-[3.87deg]" :style="{ backgroundImage: 'url(' + require('@/assets/img/testimonial/testimonial-1.jpeg') + ')' }" />
                </div>
              </div>
              <div class="bg-white shadow p-10 -mt-[200px] mx-auto max-w-[480px] relative rounded-[20px]">
                <slot name="right" />
              </div>
            </div>
          </div>
          <div class="hidden xl:block xl:col-span-2 xl:px-8 xl:mt-12">
            <div class="flex items-center justify-start">
              <span class="flex-shrink-0 mr-5 font-medium text-slate-500">
                <span class="text-[#21B8BA]">{{ happyCustomers }}</span> happy customers
              </span>
              <div class="h-px bg-slate-300 w-full" />
            </div>
            <div class="flex justify-between items-center text-slate-500 mt-6">
              <IconLogoBox />
              <IconLogoEbay />
              <IconLogoStackOverflow />
              <IconLogoRogers />
              <IconLogoHarsco />
              <IconLogoNcr />
              <IconLogoShopify />
              <IconLogoOkta />
            </div>
          </div>
        </div>
      </div>
    </section>
    <section v-else class="pt-20 px-5 pb-5 bg-white flex flex-col h-screen lg:p-4 lg:flex-row">
      <!-- <div class="w-full mx-auto flex order-last mt-10 lg:mt-0 lg:order-first lg:max-w-[500px] xl:max-w-[645px]">
      <div class="w-full max-w-[400px] mx-auto aspect-[2/3] relative overflow-hidden rounded-b-[50px] rounded-t-[20px] lg:max-w-none lg:rounded-xl">
        <MarketingTestimonialSlider />
      </div>
    </div> -->
      <div class="flex items-center justify-center flex-1 lg:px-8">
        <div class="w-full max-w-[400px] mx-auto">
          <nuxt-link to="/" title="" class="flex">
            <img class="h-7 w-auto" src="@/assets/img/logo.svg" alt="">
          </nuxt-link>
          <slot />
        </div>
      </div>
    </section>
  </MaintenanceWrapper>
</template>

<script>
export default {
  props: {
    variant: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    happyCustomers () {
      return Math.round(this.$store.state.stats.users.replace(/,/g, '') / 1000) + ',000+'
    }
  }
}
</script>

<style>
  .login-card {
    @apply shadow-md bg-cover bg-center bg-no-repeat origin-top-left;
    width: 293px;
    height: 383px;
    background-color: rgba(215, 215, 215, 0.2);
    border: solid white 7px;
    border-radius: 29px;
  }

  @keyframes firstCardBack {
    0% {
      transform: rotate(-7.54deg);
    }
    100% {
      transform: rotate(-5.54deg);
    }
  }
  @keyframes firstCardFront {
    0% {
      transform: rotate(-9.29deg);
    }
    100% {
      transform: rotate(-7.29deg);
    }
  }
  @keyframes secondCardBack {
    0% {
      transform: rotate(11.22deg);
    }
    100% {
      transform: rotate(9.22deg);
    }
  }
  @keyframes secondCardFront {
    0% {
      transform: rotate(5.87deg);
    }
    100% {
      transform: rotate(3.87deg);
    }
  }
</style>
