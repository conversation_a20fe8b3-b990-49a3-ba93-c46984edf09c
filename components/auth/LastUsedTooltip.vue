<template>
  <div
    v-if="provider === lastUsed"
    class="hidden absolute right-full mr-2 top-1/2 -translate-y-1/2 bg-black bg-opacity-50 text-white px-2 py-1 rounded-md text-xs whitespace-nowrap md:flex items-center justify-center"
  >
    <div
      class="absolute left-full top-2 w-0 h-0
      border-t-[4px] border-t-transparent
      border-l-[6px] border-l-black border-opacity-50
      border-b-[4px] border-b-transparent"
    />
    <IconKey class="h-3 w-3 mr-2 scale-125" />
    <span>Last used</span>
  </div>
</template>

<script>
export default {
  props: {
    provider: {
      type: String,
      required: true
    }
  },
  data () {
    if (typeof window === 'undefined') {
      return {
        lastUsed: null
      }
    }

    return {
      lastUsed: localStorage.getItem('authProvider')
    }
  }
}
</script>
