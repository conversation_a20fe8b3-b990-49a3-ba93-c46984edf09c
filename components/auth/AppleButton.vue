<template>
  <ButtonDark v-if="isSelectedDevice" class="bg-black hover:bg-gray-900 h-30" @click="appleSignin">
    <img src="@/assets/img/apple-logo.svg" class="w-3.5 mr-1.5">
    <span class="apple-font">Sign in with Apple</span>
  </ButtonDark>
</template>

<script>
import { OAuthProvider } from 'firebase/auth'
// const provider = new OAuthProvider('apple.com')

export default {
  computed: {
    isSelectedDevice () {
      // All Apple user agents
      const bannedAgents = ['TikTok', 'musical_ly', 'Bytedance', 'Instagram']
      const onlyOnDevices = ['iPhone', 'iPad', 'iPod', 'Mac', 'Macintosh']
      // return onlyOnDevices.some(device => navigator.userAgent.includes(device))
      return onlyOnDevices.some(device => navigator.userAgent.includes(device)) && !bannedAgents.some(agent => navigator.userAgent.includes(agent))
    }
  },
  methods: {
    appleSignin () {
      // const provider = new this.$fireModule.auth.GoogleAuthProvider()
      const provider = new OAuthProvider('apple.com')
      this.$fire.auth
        .signInWithPopup(provider)
        .then(async (result) => {
          await this.createDatabaseAccount(result.user)
        })
        .catch((e) => {
          console.log(e)
          this.$toast.error(e.message)
        })
    }
  }
}
</script>

<style scoped>
.apple-font {
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
}
</style>
