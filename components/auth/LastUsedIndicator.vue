<template>
  <div
    v-if="lastUsed"
    class="text-xs border bg-[#f2fdfd] border-[#e5fafa] text-[#0c4445] px-4 py-2 rounded-md text-center flex items-center justify-center md:hidden"
  >
    <IconKey class="h-3 w-3 mr-2 scale-125" />
    <span>You used <strong>{{ lastUsedName }}</strong> to log in last time</span>
  </div>
  <div v-else />
</template>

<script>
export default {
  data () {
    if (typeof window === 'undefined') {
      return {
        lastUsed: null
      }
    }

    return {
      lastUsed: localStorage.getItem('authProvider')
    }
  },
  computed: {
    lastUsedName () {
      switch (this.lastUsed) {
        case 'google':
          return 'Google'
        case 'linkedin':
          return 'LinkedIn'
        case 'email':
          return 'Email'
        default:
          return null
      }
    }
  }
}
</script>
