<template>
  <LoadingWrapper :is-loading="isSubmitting">
    <form class="space-y-6" @submit.prevent="handleLogin">
      <div class="space-y-5 text-left">
        <div>
          <label for="" class="text-base font-medium text-gray-900">Email address</label>
          <div class="relative mt-2.5 text-gray-600 focus-within:text-gray-400">
            <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
              </svg>
            </div>

            <input
              id="email"
              v-model="email"
              name="email"
              type="email"
              autocomplete="email"
              required=""
              placeholder="Enter email to get started"
              class="block w-full rounded-md border border-gray-200 bg-gray-50 py-4 pl-10 pr-4 text-black placeholder-gray-500 caret-blue-600 transition-all duration-200 focus:border-blue-600 focus:bg-white focus:outline-none"
            >
          </div>
        </div>

        <div>
          <div class="flex items-center justify-between">
            <label for="" class="text-base font-medium text-gray-900">Password</label>

            <nuxt-link to="/auth/reset-password" title="" class="text-sm font-medium text-blue-600 transition-all duration-200 hover:text-blue-700 hover:underline focus:text-blue-700">
              Forgot password?
            </nuxt-link>
          </div>
          <div class="relative mt-2.5 text-gray-600 focus-within:text-gray-400">
            <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 11c0 3.517-1.009 6.799-2.753 9.571m-3.44-2.04l.054-.09A13.916 13.916 0 008 11a4 4 0 118 0c0 1.017-.07 2.019-.203 3m-2.118 6.844A21.88 21.88 0 0015.171 17m3.839 1.132c.645-2.266.99-4.659.99-7.132A8 8 0 008 4.07M3 15.364c.64-1.319 1-2.8 1-4.364 0-1.457.39-2.823 1.07-4" />
              </svg>
            </div>

            <input
              id="password"
              v-model="password"
              name="password"
              type="password"
              autocomplete="current-password"
              required=""
              placeholder="Enter your password"
              class="block w-full rounded-md border border-gray-200 bg-gray-50 py-4 pl-10 pr-4 text-black placeholder-gray-500 caret-blue-600 transition-all duration-200 focus:border-blue-600 focus:bg-white focus:outline-none"
            >
          </div>
        </div>

        <div>
          <ButtonPrimary type="submit" class="w-full">
            Log in
          </ButtonPrimary>
          <!-- <button type="submit" class="inline-flex items-center justify-center w-full px-4 py-4 text-base font-medium text-black transition-all duration-200 border border-transparent rounded-md bg-brand-500 focus:outline-none hover:opacity-80 focus:opacity-80">
            Log in
          </button> -->
        </div>
      </div>
    </form>
  </LoadingWrapper>
</template>

<script>
export default {
  name: 'LoginForm',
  data () {
    return {
      email: '',
      password: '',
      isSubmitting: false,
      hasLoginError: false,
      errorMessage: '',
      isLoading: false
    }
  },
  methods: {
    handleLogin () {
      this.isSubmitting = true
      try {
        const email = this.email
        const password = this.password
        // console.log(this.values.email, this.values.password)
        this.$fire.auth
          .setPersistence('local')
          .then(async (user) => {
            await this.$fire.auth.signInWithEmailAndPassword(email, password)
            this.$emit('success')
            // this.trackEvent({ name: 'auth:login' })
          })
          .catch((e) => {
            this.$toast.open({ type: 'error', message: e.message })
            console.error('error', e.code, e.message)
            this.isSubmitting = false
            this.hasLoginError = true
          })
      } catch (e) {
        this.$toast.open({ type: 'error', message: e.message })
        console.error('error', e.code, e.message)
        this.isSubmitting = false
        this.hasLoginError = true
      }
    }
  }
}
</script>

<style></style>
