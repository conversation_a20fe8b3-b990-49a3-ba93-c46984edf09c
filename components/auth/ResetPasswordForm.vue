<template>
  <div>
    <form v-if="!resetSuccess" class="pt-3 md:pt-8" @submit.prevent="resetPassword">
      <div class="space-y-5">
        <div>
          <label for="" class="text-base font-medium text-gray-900">
            Email address
          </label>
          <div class="mt-2.5 relative text-gray-400 focus-within:text-gray-600">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
              </svg>
            </div>
            <input
              id="email"
              v-model="email"
              name="email"
              type="email"
              autocomplete="email"
              required=""
              placeholder="Enter email to get started"
              class="block w-full py-4 pl-10 pr-4 text-black placeholder-gray-500 transition-all duration-200 border border-gray-200 rounded-md bg-gray-50 focus:outline-none focus:border-blue-600 focus:bg-white caret-blue-600"
            >
          </div>
        </div>
        <div>
          <LoadingWrapper :is-loading="isSubmitting">
            <ButtonPrimary class="w-full" type="submit">
              Reset password
            </ButtonPrimary>
          </LoadingWrapper>
        </div>
        <p v-if="hasLoginError" class="mt-4 auth-error-message">
          Password reset failed. Please check your email address.
        </p>
      </div>
    </form>
    <div v-else class="flex content-center items-center justify-start space-x-2">
      <IconCheckCircle class="w-4 h-4 text-green-500 mr-2" />
      Password reset request send to your email.
    </div>
  </div>
</template>

<script>

export default {
  data () {
    return {
      email: null,
      isSubmitting: false,
      hasLoginError: false,
      resetSuccess: false
    }
  },
  methods: {
    async sendResetEmail (userEmail) {
      return await this.$axios.$post('/auth/send-password-reset-email', {
        userEmail
      })
    },
    async resetPassword () {
      this.isSubmitting = true
      try {
        await this.sendResetEmail(this.email)
        // await this.$fire.auth.sendPasswordResetEmail(this.email)
        this.resetSuccess = true
      } catch (e) {
        console.error('error', e.code, e.message)
        this.isSubmitting = false
        this.hasLoginError = true
      }
    }
  }
}
</script>

<style scoped>

</style>
