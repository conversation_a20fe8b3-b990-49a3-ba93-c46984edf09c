<template>
  <thead class="bg-[#FCFCFD]">
    <template v-for="(item, index) in head">
      <th :key="index" class="py-3 table-break-all text-left text-xs font-medium text-[#667085] last:relative px-6 last:text-right">
        {{ item }}
      </th>
    </template>
  </thead>
</template>

<script>
export default {
  props: {
    head: {
      type: Array,
      required: true
    }
  }
}
</script>

<style></style>
