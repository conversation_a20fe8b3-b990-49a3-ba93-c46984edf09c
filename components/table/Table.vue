<template>
  <div>
    <table class="min-w-full divide-y divide-gray-300">
      <TableHead v-if="head" :head="head" class="top-0" :class="{ 'lg:sticky lg:self-start': sticky }" />
      <tbody class="divide-y divide-[#EAECF0] bg-white">
        <!-- Odd row -->
        <slot />

        <!-- More rows... -->
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  props: {
    head: {
      type: Array,
      required: false
    },
    sticky: {
      type: Boolean,
      required: false,
      default: true
    }
  }
}
</script>
