<template>
  <div class="w-full h-full fixed top-0 left-0 flex items-center justify-center z-[9999]">
    <div class="transition-opacity fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm" aria-hidden="true" @click="$emit('close')" />
    <div class="overflow-hidden bg-white rounded-lg shadow card z-20 relative max-w-[400px] max-h-[50vh] overflow-y-scroll">
      <div class="p-6 space-y-2">
        <h1 class="font-bold text-lg">
          {{ title }}
        </h1>
        <p class="text-gray-700 text-sm" v-html="description" />
        <ButtonPrimary v-if="action" size="sm" class="w-full" @click="handleActionClick">
          {{ action }}
        </ButtonPrimary>
      </div>
      <div v-if="time && time > 0" class="countdown transition" :style="{ width: countdownWidth + '%' }" />
      <div class="w-4 h-4 bg-black rounded-full absolute top-2 right-2 flex items-center justify-center cursor-pointer" @click="$emit('close')">
        <IconCross class="w-3 h-3 text-white" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ['title', 'description', 'time', 'action', 'onAction'],
  data () {
    return {
      countdownWidth: 100
    }
  },
  mounted () {
    if (this.time) {
      const intervalTime = 10 // 10ms for smoother transition
      const decrementValue = 100 / (this.time / intervalTime)
      setInterval(() => {
        this.countdownWidth -= decrementValue
      }, intervalTime)
      if (this.time) {
        setTimeout(() => {
          this.$emit('close')
        }, this.time)
      }
    }
  },
  methods: {
    async handleActionClick () {
      if (typeof this.onAction === 'function') {
        await this.onAction() // Call the callback function
        this.$emit('close') // Close the modal
      }
    }
  }
}
</script>

  <style>
  .countdown {
    height: 5px;
    background-color: green;
  }
  </style>
