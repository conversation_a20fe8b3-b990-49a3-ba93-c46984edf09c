<template>
  <div class="flex flex-col md:flex-row justify-between items-center border-b border-gray-200 bg-white pb-4">
    <div>
      <h3 v-if="title" class="text-lg font-bold leading-6 text-gray-900">
        {{ title }}
      </h3>
      <p v-if="subTitle" class="mt-1 text-sm text-gray-700">
        {{ subTitle }}
      </p>
    </div>
    <slot />
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: null
    },
    subTitle: {
      type: String,
      default: null
    }
  }
}
</script>

<style></style>
