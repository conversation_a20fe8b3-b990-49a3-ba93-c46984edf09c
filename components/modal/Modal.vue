<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto p-2 md:p-0 h-screen w-full" role="dialog" aria-modal="true">
    <div class="transition-opacity fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm" aria-hidden="true" @click="$emit('close')" />
    <div :class="`transition-all max-h-[calc(90vh)] relative mx-auto inline-block transform rounded-lg bg-white overflow-y-scroll shadow ${maxWidth} border border-gray-200 overflow-hidden`">
      <slot />
      <div v-if="showCloseButton" class="w-4 h-4 bg-black rounded-full absolute top-2 right-2 flex items-center justify-center cursor-pointer" @click="$emit('close')">
        <IconCross class="w-3 h-3 text-white" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    showCloseButton: {
      type: Boolean,
      default: true
    },
    maxWidth: {
      type: String,
      default: 'sm:max-w-3xl'
    }
  }
}
</script>

<style></style>
