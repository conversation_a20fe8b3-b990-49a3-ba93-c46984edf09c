<template>
  <button
    type="button"
    class="flex items-center justify-center gap-1 flex-col p-2 rounded-md text-xs"
    :class="{
      'text-primary-500 bg-white/50': current === tab,
      'text-gray-500': current !== tab
    }"
    @click="$emit('selected', tab)"
  >
    <slot />
  </button>
</template>

<script>
export default {
  props: {
    current: {
      type: String,
      required: true
    },
    tab: {
      type: String,
      required: true
    }
  }
}
</script>
