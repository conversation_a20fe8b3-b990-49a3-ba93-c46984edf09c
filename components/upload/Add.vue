<template>
  <Card class="min-w-lg mt-4 w-full max-w-lg rounded-lg shadow">
    <LoadingSpinner
      v-if="isSaving"
      title="Uploading your photos. Could take up to 60 seconds. Don't close this page!"
    />
    <div v-if="!isSaving">
      <div class="space-y-2">
        <div>
          <h2 class="text-xl font-bold">
            Upload your photos
          </h2>
          <p class="text-sm text-gray-700">
            Upload your best photo of yourself. These will be used to generate
            your headshots.
          </p>

          <button
            class="w-full pb-2 text-left text-xs text-gray-600 underline"
            @click="showRequirementsModal = true"
          >
            Show photo requirements
          </button>
        </div>
        <div v-if="addingFiles" class="rounded-md bg-gray-100 px-4 py-2">
          <LoadingSpinner
            class="mx-auto"
            title="Checking photos. Duration depends on size and internet speed."
          />
        </div>
        <div class="">
          <template v-if="showPhoneUpload">
            <ButtonPrimary class="w-full" @click="startPhoneUploadFlow">
              <template v-if="isMobile()">
                <span>Start capturing your selfies</span>
                <span class="i-heroicons-chevron-right" />
              </template>
              <template v-else>
                <span class="i-heroicons-qr-code mr-2" />
                <span>Upload photos from your phone</span>
              </template>
            </ButtonPrimary>
            <p v-if="photos.approved.length === 0" class="text-xs text-gray-500 italic w-full text-center py-1">
              Recommended method for best results.
            </p>
            <div v-if="photos.approved.length === 0" class="flex items-center justify-center pt-2">
              <div class="border-t border-gray-300 flex-grow" />
              <span class="px-2 text-sm bg-white text-gray-500">OR</span>
              <div class="border-t border-gray-300 flex-grow" />
            </div>
          </template>
          <div class="mt-2">
            <!-- <label
              v-if="photos.approved.length > 0"
              class="mb-1 block text-sm font-medium leading-5"
            >
              Photos
            </label> -->
            <template
              v-if="
                !addingFiles ||
                  photos.approved.length > 0 ||
                  photos.declined.length > 0
              "
            >
              <div
                v-if="photos.approved.length === 0"
                class="mt-4 sm:col-span-2 sm:mt-0"
              >
                <div
                  class="relative flex max-w-lg items-center justify-center rounded-md border-2 border-dashed border-gray-300 hover:border-blue-300 bg-gray-50 hover:bg-blue-100  px-6 pb-6 pt-5"
                >
                  <div class="text-center">
                    <svg
                      v-if="photos.approved.length === 0"
                      class="mx-auto h-12 w-12 text-gray-700"
                      stroke="currentColor"
                      fill="none"
                      viewBox="0 0 48 48"
                    >
                      <path
                        d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                    <svg
                      v-else
                      class="mx-auto h-12 w-12 text-gray-700"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
                      />
                    </svg>
                    <template v-if="photos.approved.length === 0">
                      <p class="mt-1 text-sm text-gray-700">
                        <button
                          type="button"
                          class="font-medium text-blue-600 transition duration-150 ease-in-out hover:text-blue-500 focus:underline focus:outline-none"
                        >
                          Click to upload {{ minimumPhotos }} photos
                        </button>
                        or drag and drop
                      </p>
                      <p class="mt-1 text-xs text-gray-500">
                        JPG/PNG up to 5MB (min 512px)
                      </p>
                    </template>
                    <template v-else>
                      <p class="mt-1 text-sm text-gray-700">
                        {{ inputFiles.length }} photo(s) selected
                      </p>
                    </template>
                  </div>
                  <label for="file" class="file-label cursor-pointer" />
                  <input
                    id="file"
                    ref="file"
                    name="file"
                    type="file"
                    class="absolute left-0 top-0 z-20 h-full w-full opacity-0 cursor-pointer"
                    accept="image/png, image/jpeg, image/heic, image/heif, image/jpg"
                    multiple
                    @change="addFiles"
                  >
                </div>
              </div>
              <div v-if="photos.approved.length > 0" class="">
                <div class="flex items-center justify-start space-x-0.5">
                  <IconCheck class="h-4 w-4 text-green-500" />
                  <span class="text-sm font-medium">Passed check</span>
                </div>
                <p class="text-xs text-gray-600">
                  Make sure to check these photos yourself too.
                </p>

                <div
                  class="mt-1.5 grid grid-cols-5 gap-2 rounded-md border border-green-100 bg-green-50 p-2 md:grid-cols-6"
                >
                  <template v-for="(file, index) in photos.approved">
                    <div
                      :key="index"
                      class="group relative w-full overflow-hidden rounded-lg"
                    >
                      <img
                        :src="file"
                        class="relative z-10 w-full h-full object-cover"
                      >
                      <div
                        class="absolute left-0 top-0 z-20 hidden h-full min-h-[32px] w-full cursor-pointer items-center justify-center bg-black/80 group-hover:flex"
                        @click="removeFile(index)"
                      >
                        <div
                          class="flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-white"
                        >
                          <IconCross class="h-3 w-3" />
                        </div>
                      </div>
                    </div>
                  </template>
                  <!-- <div class="bg-gray-200 flex items-center justify-center w-10 h-10 rounded-lg cursor-pointer"> -->
                  <label
                    for="fileextra"
                    class="flex w-full cursor-pointer items-center justify-center rounded-lg bg-gray-200 hover:bg-gray-300"
                  >
                    <IconPlus
                      v-if="!addingFiles"
                      class="h-6 w-6 text-gray-700"
                    />
                    <LoadingSpinnerSingle v-else />
                  </label>
                  <input
                    id="fileextra"
                    ref="fileextra"
                    name="fileextra"
                    type="file"
                    class="hidden"
                    accept="image/png, image/jpeg, image/heic, image/heif, image/jpg"
                    multiple
                    @change="addFiles"
                  >
                </div>
              </div>
              <div v-if="photos.declined.length > 0" class="mt-2">
                <div class="flex items-center justify-start space-x-0.5">
                  <IconCross class="h-4 w-4 text-red-500" />
                  <span class="text-sm font-medium">Declined photos</span>
                </div>
                <p class="text-xs text-gray-600">
                  These photos were declined because they did not meet the
                  requirements.
                </p>

                <div
                  class="mt-1.5 grid aspect-w-1 aspect-h-1 overflow-hidden grid-cols-6 gap-2 rounded-md border border-red-100 bg-red-50 p-2 md:grid-cols-7"
                >
                  <template v-for="(img, index) in photos.declined">
                    <div
                      :key="index"
                      class="aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-lg bg-red-500"
                    >
                      <img :src="img" class="rounded-lg object-cover">
                    </div>
                  </template>
                </div>
              </div>
            </template>
          </div>
          <UploadRemoteSources @imageSelected="receiveImage($event, 'Optimising Image')" />
          <div class="grid grid-cols-3 gap-2 mt-4">
            <ButtonWhite class="col-span-1" @click="$emit('return')">
              <IconChevron class="w-4 h-4 text-gray-500 mr-1.5 transform rotate-180" />
              <span>Back</span>
            </ButtonWhite>
            <template v-if="addingFiles">
              <ButtonDisabled class="col-span-2">
                <LoadingSpinner title="Optimizing files..." />
              </ButtonDisabled>
            </template>
            <template v-else>
              <ButtonPrimary v-if="!isSaving && approvedPhotoPaths.length >= minimumPhotos" class="col-span-2" @click="$emit('next')">
                <span>Continue to summary</span>
                <IconChevron class="ml-2 h-4 w-4 text-white" />
              </ButtonPrimary>
              <ButtonDisabled v-else class="col-span-2">
                <span v-if="photos?.approved?.length < minimumPhotos">
                  Upload atleast {{ minimumPhotos }} photos
                </span>
              </ButtonDisabled>
            </template>
          </div>
        </div>
      </div>
    </div>
    <Popup
      v-if="showRequirementsModal"
      size="lg"
      @closeModal="showRequirementsModal = false"
    >
      <img src="@/assets/img/good-bad-example.png" class="w-full">
      <img src="@/assets/img/usage-tips.jpg">
    </Popup>
    <Popup v-if="showPhoneLink" size="sm" @closeModal="showPhoneLink = false">
      <div class="space-y-2">
        <h2 class="font-bold text-lg">
          Scan QR with phone camera
        </h2>
        <p class="text-sm text-gray-500">
          Scan this QR with your phone camera to start uploading your photos. It takes only a few minutes.
        </p>
        <div class="bg-gray-100 p-3 flex items-center justify-center rounded-md">
          <qrcode
            v-if="phoneRoomLinkQrCode"
            :value="phoneRoomLinkQrCode"
            class="rounded-md ring-1 ring-gray-200"
          />
        </div>
        <AlertComplete v-if="phoneConnected" title="Phone connected" description="You can start clicking pictures now" />
        <AlertWarning title="Keep this screen open" description="Do not close this screen until you have uploaded all your photos on your phone." />
        <p class="text-sm text-gray-500">
          Does your phone camera not accept QR codes? Open link on your phone:
        </p>
        <p class="mt-2 text-xs font-medium text-gray-600">
          {{ phoneRoomLinkQrCode }}
        </p>
        <p class="mt-1 text-xs text-gray-500">
          This link is valid for 30 mins and <span v-if="roomExpiresAt">
            expires at <span>{{ roomExpiresAt }}</span>
          </span>
        </p>
      </div>
    </Popup>
    <UploadPhoneModal v-if="webcamModal" @captureImage="receiveImage($event, 'Optimising Image')" @closeWebcamModal="closeWebcamModal" />
  </Card>
</template>

<script>
import SparkMD5 from 'spark-md5'
import io from 'socket.io-client'
import VueQrcode from '@chenfengyuan/vue-qrcode'
import { mapGetters } from 'vuex'
import imageMixin from '@/utils/image'

export default {
  components: {
    qrcode: VueQrcode
  },
  mixins: [imageMixin],
  data () {
    return {
      average: 0,
      uploadedFileHashes: new Set(),
      showRequirementsModal: false,
      source: null,
      file: null,
      name: 'male',
      tunes: [],
      isSaving: false,
      hasFullBodyShot: false,
      files: [],
      isSubmitted: false,
      addingFiles: false,
      inputFiles: [],
      showPhoneLink: false,
      phoneRoomLink: null,
      socket: null,
      roomExpiresAt: null,
      webcamModal: false,
      phonePopupTimer: null,
      phoneConnected: false,
      showPhoneUpload: true
    }
  },
  computed: {
    minimumPhotos () {
      const { env } = this
      if (env === 'development') {
        return 5
      }
      return this.$store.state.onboarding.minimumPhotos
    },
    photos () {
      return this.$store.state?.onboarding?.photos || {}
    },
    approvedPhotos: {
      get () {
        return this.$store.state?.onboarding?.photos.approved || []
      },
      set (value) {
        this.$store.commit('onboarding/SET_APPROVED_PHOTOS', value)
      }
    },
    declinedPhotos: {
      get () {
        return this.$store.state?.onboarding?.photos.declined || []
      },
      set (value) {
        this.$store.commit('onboarding/SET_DECLINED_PHOTOS', value)
      }
    },
    approvedPhotoPaths: {
      get () {
        return this.$store.state?.onboarding?.approvedPhotoPaths || []
      },
      set (value) {
        this.$store.commit('onboarding/SET_APPROVED_PHOTO_PATHS', value)
      }
    },
    trainingId: {
      get () {
        return this.$store.state?.onboarding?.trainingId || null
      },
      set (value) {
        this.$store.commit('onboarding/SET_TRAINING_ID', value)
      }
    },
    env () {
      return process.env.NODE_ENV
    },
    phoneRoomLinkQrCode () {
      if (this.photos.approved.length > 0) {
        return `${this.phoneRoomLink}?hasPicturesApproved=true`
      }
      return this.phoneRoomLink
    },
    isDesktop () {
      if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
        return false
      }
      return true
    },
    ...mapGetters('onboarding', [
      'selectedSex',
      'selectedEyeColor',
      'ethnicity',
      'title',
      'selectedAge'
    ])
  },
  watch: {
    showPhoneLink (value) {
      if (!value) {
        clearTimeout(this.phonePopupTimer)
      }
    }
  },
  async mounted () {
    this.trainingId = SparkMD5.hash(
      Math.random().toString() + new Date().getTime().toString()
    )

    // Load localStorage items
    const approvedPhotoPaths = localStorage.getItem('approvedPhotoPaths')

    if (approvedPhotoPaths) {
      this.approvedPhotoPaths = JSON.parse(approvedPhotoPaths)
      this.inputFiles = [JSON.parse(approvedPhotoPaths)]
      const response = await this.$axios.$post('/image/sign-paths', {
        paths: this.approvedPhotoPaths
      })
      if (response && response.data) {
        this.approvedPhotos = [...response.data]
      }
    }
  },
  beforeDestroy () {
    if (this.socket) {
      this.socket.disconnect()
    }
  },
  methods: {
    calculateMD5 (file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        const spark = new SparkMD5.ArrayBuffer()

        reader.onload = (event) => {
          spark.append(event.target.result)
          const hash = spark.end()
          resolve(hash)
        }

        reader.onerror = (error) => {
          reject(error)
        }

        reader.readAsArrayBuffer(file)
      })
    },
    createObjectUrl (file) {
      return URL.createObjectURL(file)
    },
    removeFile (index) {
      this.handleFileDeletion({
        trainingId: this.trainingId,
        image: this.approvedPhotos[index]
      })
      this.files.splice(index, 1)
      this.approvedPhotos = this.approvedPhotos.filter((item, i) => i !== index)
      this.approvedPhotoPaths = this.approvedPhotoPaths.filter(
        (item, i) => i !== index
      )
      this.inputFiles = [...this.approvedPhotoPaths]
      this.uploadedFileHashes.delete(this.uploadedFileHashes[index])
      localStorage.setItem(
        'approvedPhotoPaths',
        JSON.stringify(this.approvedPhotoPaths)
      )
    },
    handleFileDeletion (payload) {
      const { trainingId, image } = payload
      this.$axios
        .$delete('/image', {
          data: {
            trainingId,
            image
          }
        })
        .then((data) => {
          return true
        })
        .catch((err) => {
          console.log(err)
        })
    },
    isMobile () {
      const userAgent = navigator.userAgent
      if ((/windows phone/i.test(userAgent) || /android/i.test(userAgent) || /iPad|iPhone|iPod/.test(userAgent)) && !window.MSStream) {
        return true
      }
      return false
    },
    async uploadFile (file, source) {
      const declinePhoto = (fileData, reason) => {
        this.declinedPhotos = [
          ...this.declinedPhotos,
          this.createObjectUrl(fileData)
        ]
        throw new Error(reason || 'Photo does not meet the requirements')
      }

      try {
        this.addingFiles = true
        let fileData = file

        const fileName =
          fileData.name || 'trainingImage' + new Date().getTime().toString()
        const fileHash = await this.calculateMD5(fileData)

        if (this.uploadedFileHashes.has(fileHash)) {
          declinePhoto(fileData, 'You already uploaded this photo')
        }

        if (file.type === 'image/heic' || file.type === 'image/heif') {
          const heic2any = (await import('heic2any')).default
          fileData = await heic2any({ blob: file, toType: 'image/png' })
        }

        const size = await this.getImageSize(fileData)
        if (size.width < 512 || size.height < 512) {
          declinePhoto(fileData, 'Image is too small')
        } else if (size.width > 1024 || size.height > 1024) {
          fileData = await this.resizeImage(fileData, 1024)
        }

        const blob = new File([fileData], fileName, { type: fileData.type })
        const shouldBypassDetection = this.$route.query.bypass === 'true'
        const data = await this.detectFaces(blob, shouldBypassDetection)
        const { boundingBoxes: faces, faceCoveragePercentage } = data?.data || {} // faceCoveragePercentage

        if (!faces || faces.length === 0) {
          declinePhoto(fileData, faces?.errorMessage || 'We could not find any faces in the photo.')
        }

        const largestFace = this.getLargestFace(faces)

        const boundingBox = this.getBoundingBox(largestFace)

        let image = null
        if (faceCoveragePercentage < 20 && !this.hasFullBodyShot) {
          const { image: croppedBodyImage, success } = await this.cropBody(fileData, boundingBox)
          if (success) {
            image = croppedBodyImage
            this.hasFullBodyShot = true
          }
        } else {
          const { image: croppedImage, success, message } = await this.cropHead(fileData, boundingBox)
          if (success) {
            image = croppedImage
          } else {
            declinePhoto(fileData, message)
          }
        }
        // if (!this.hasFullBodyShot) {
        //   const { image: croppedImage } = await this.cropBody(
        //     fileData,
        //     boundingBox
        //   )
        //   image = croppedImage
        //   this.hasFullBodyShot = true
        // } else {
        //   const {
        //     image: croppedImage,
        //     success,
        //     message
        //   } = await this.cropHead(fileData, boundingBox)
        //   if (success) {
        //     image = croppedImage
        //   } else {
        //     declinePhoto(fileData, message)
        //   }
        // }

        // Base64 to file
        const dataURItoBlob = (dataURI) => {
          const byteString = window.atob(dataURI.split(',')[1])
          const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0]
          const ab = new ArrayBuffer(byteString.length)
          const ia = new Uint8Array(ab)
          for (let i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i)
          }
          return new Blob([ab], { type: mimeString })
        }
        const imageBlob = dataURItoBlob(image)
        const newFile = new File([imageBlob], fileName, {
          type: imageBlob.type
        })

        // Formdata
        const formData = new FormData()
        formData.append('files', newFile)

        const response = await this.$axios.$post('/image/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })

        if (response && response.success) {
          this.approvedPhotoPaths = [
            ...this.approvedPhotoPaths,
            response.data.path
          ]
          this.approvedPhotos = [...this.approvedPhotos, image]

          this.inputFiles = [...this.approvedPhotoPaths]
          this.uploadedFileHashes.add(fileHash)

          localStorage.setItem(
            'approvedPhotoPaths',
            JSON.stringify(this.approvedPhotoPaths)
          )
        } else {
          this.declinedPhotos = [
            ...this.declinedPhotos, this.createObjectUrl(fileData)
          ]
          this.$toast.warning('Could not approve this photo.')
        }
      } catch (err) {
        if (err?.type === 'error') {
          this.$toast.warning('Cannot upload this photo. Please try another photo.')
          return
        }
        if (err.message === 'Canceled') {
          return
        }
        this.$toast.open({
          message: err.message || err || 'Something went wrong. Please try another photo.',
          type: 'warning',
          duration: 5000
        })
      }
    },
    async addFiles (event) {
      try {
        this.addingFiles = true
        const files = event.target.files

        const source = this.$axios.CancelToken.source()
        this.source = source

        const filesArray = []
        for (let i = 0; i < files.length; i++) {
          filesArray.push(files[i])
        }

        // await Promise.all(
        //   filesArray.map(file => this.uploadFile(file, source))
        // )
        for (let i = 0; i < filesArray.length; i += 5) {
          const chunk = filesArray.slice(i, i + 5)
          await Promise.all(chunk.map(file => this.uploadFile(file, source)))
          console.log('Finoshed cunk' + i)
        }

        this.addingFiles = false
      } catch (err) {
        if (err.message === 'Canceled') {
          return
        }
        this.$toast.warning(
          err.message || 'Something went wrong. Please try another photo.'
        )
        this.addingFiles = false
      }
    },
    async createWebsocketRoom () {
      try {
        const storedLink = localStorage.getItem('phoneRoomLink')
        if (storedLink) {
          const parsedLink = JSON.parse(storedLink)
          if (parsedLink && parsedLink.link) {
            this.phoneRoomLink = parsedLink.link
            this.roomExpiresAt = parsedLink.expiresAt
          }
        }
        if (this.phoneRoomLink) {
          const id = this.phoneRoomLink.split('/').pop()
          const response = await this.$axios.$get(`/photo-upload/validate-link/${id}`)
          this.isValid = response.isValid
          if (this.isValid) {
            this.socket.emit('joinRoom', { room: id, isPhone: false })
          } else {
            await this.createAndStoreNewLink()
          }
        } else {
          await this.createAndStoreNewLink()
        }

        this.showPhoneLink = true
        this.phonePopupTimer = setTimeout(() => {
          this.showPhoneLink = false
        }, 1800000) // 30 mins
      } catch (error) {
        this.$toast.warning(
          error?.response?.data?.message || 'Something went wrong'
        )
      }
    },
    async receiveImage (arrayBuffer, message = 'Received image') {
      this.$toast.success(message)
      this.showPhoneLink = false
      const blob = new Blob([arrayBuffer], { type: 'image/png' })
      await this.uploadFile(blob, null)
      this.addingFiles = false
    },
    async createAndStoreNewLink () {
      const data = await this.$axios.$get('photo-upload/create-link')
      const { uuid, link } = data
      this.socket.emit('joinRoom', { room: uuid, isPhone: false })
      this.phoneRoomLink = link
      this.roomExpiresAt = this.get30MinTimestamp()
      localStorage.setItem('phoneRoomLink', JSON.stringify({
        link,
        expiresAt: this.roomExpiresAt
      }))
      return data
    },
    get30MinTimestamp () {
      const now = new Date()
      now.setMinutes(now.getMinutes() + 30)
      let hours = now.getHours()
      let minutes = now.getMinutes()
      const ampm = hours >= 12 ? 'PM' : 'AM'
      hours = hours % 12
      hours = hours || 12
      minutes = minutes < 10 ? '0' + minutes : minutes
      return hours + ':' + minutes + ' ' + ampm
    },
    initializeSocketConnection () {
      if (!this.socket) {
        this.socket = io(`${this.$config.SOCKET_URL}`, {
          reconnection: true,
          reconnectionDelay: 1000,
          reconnectionDelayMax: 5000,
          reconnectionAttempts: 5
        })
        this.socket.on('receiveImage', (arrayBuffer) => {
          this.receiveImage(arrayBuffer)
        })
        this.socket.on('uploadStart', () => {
          this.$toast.info('Uploading image from your phone')
        })
        this.socket.on('connect', () => {
          this.$toast.success('Connection established with server')
        })
        this.socket.on('disconnect', () => {
          this.$toast.warning('Connection lost with server, trying to reconnect')
        })
        this.socket.io.on('reconnect_attempt', (attemptNumber) => {
          this.$toast.info(`Reconnect attempt ${attemptNumber}`)
        })
        this.socket.io.on('reconnect_failed', () => {
          this.$toast.warning('Reconnection failed, please try uploading directly from your computer')
          this.disconnectSocketAndHideQRModal()
        })
        this.socket.on('error', (error) => {
          this.$toast.error('Connection encountered error:', error)
        })
        this.socket.on('phoneConnected', () => {
          this.$toast.success('Phone is connected')
          this.phoneConnected = true
        })
        this.socket.on('phoneDisconnected', () => {
          this.$toast.warning('Phone is disconnected')
          this.phoneConnected = false
        })
      }
    },
    disconnectSocketAndHideQRModal () {
      if (this.socket) {
        this.socket.disconnect()
      }
      localStorage.removeItem('phoneRoomLink')
      this.showPhoneLink = false
      this.showPhoneUpload = false
    },
    startPhoneUploadFlow () {
      if (this.isDesktop) {
        this.initializeSocketConnection()
        this.createWebsocketRoom()
      } else {
        document.body.style.overflow = 'hidden'
        this.webcamModal = true
      }
    },
    handleSocketDisconnect () {
      setTimeout(() => {
        this.initializeSocketConnection()
      }, 5000)
    },
    closeWebcamModal () {
      document.body.style.overflow = 'auto'
      this.webcamModal = false
    }
  }
}
</script>
<style scoped>
.file-input {
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  position: absolute;
  z-index: -1;
}

.file-label {
  width: 100%;
  height: 130px;
  cursor: pointer;
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  z-index: 10;

  @apply rounded-md;
}

.thumbnail img {
  display: block;
  position: relative;
}

.thumbnail img :before {
  content: '';
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: teal;
}
</style>
