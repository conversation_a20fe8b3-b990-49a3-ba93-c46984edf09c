<template>
  <footer class="relative flex h-32 items-center justify-center bg-black px-8 pb-4">
    <div
      v-if="freeCameraMode || uploadingFromPhone"
      class="absolute inset-y-0 left-4 flex flex-col items-center justify-center pl-4"
    >
      <button
        class="flex h-8 w-8 items-center justify-center rounded-full bg-white/20"
        @click="uploadingFromPhone ? $emit('exitPhoneModal') : $emit('disableFreeCamera')"
      >
        <span class="i-heroicons-x-mark h-5 w-5 text-white" />
      </button>
      <span class="text-xs text-yellow-500">Exit</span>
    </div>
    <button
      v-if="captured"
      type="button"
      class="inline-flex flex-col items-center gap-1.5 text-xs font-medium text-green-500"
      :disabled="disabled"
      :class="{ 'opacity-50 cursor-not-allowed': disabled }"
      @click="disabled ? null : $emit('capture')"
    >
      <div class="inline-flex items-center justify-center text-white bg-green-500 rounded-full size-16">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="2.5"
          stroke="currentColor"
          class="size-6"
        >
          <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3" />
        </svg>
      </div>
      Next
    </button>
    <div v-else class="text-center">
      <button
        class="flex h-16 w-16 items-center justify-center rounded-full"
        style="background: white;"
        :disabled="disabled"
        :class="{ 'opacity-50 cursor-not-allowed': disabled }"
        @click="disabled ? null : $emit('capture')"
      >
        <svg
          width="48"
          height="48"
          viewBox="0 0 48 48"
          fill="none"
        >
          <circle cx="24" cy="24" r="23" stroke="black" stroke-width="2" />
        </svg>
      </button>
      <span class="text-xs text-yellow-500">
        Capture
      </span>
    </div>
    <div
      class="absolute inset-y-0 right-4 flex flex-col items-center justify-center pl-4"
      :class="{ 'opacity-30': !captured }"
    >
      <button
        class="flex h-8 w-8 items-center justify-center rounded-full bg-white/20"
        @click="$emit('retake')"
      >
        <span class="i-heroicons-arrow-path h-5 w-5 text-white" />
      </button>
      <span class="text-xs text-yellow-500">Retake</span>
    </div>
  </footer>
</template>

<script>
export default {
  props: {
    captured: Boolean,
    freeCameraMode: Boolean,
    uploadingFromPhone: Boolean,
    disabled: {
      type: Boolean,
      default: false
    }
  }
}
</script>
