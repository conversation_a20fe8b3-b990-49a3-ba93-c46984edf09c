<template>
  <div class="flex h-full flex-1 flex-col bg-gray-50">
    <div class="flex-1 flex justify-center items-center overflow-auto pt-4">
      <PostcheckoutRequirements class="h-full" />
    </div>
    <div class="p-2">
      <ButtonPrimary
        v-if="canContinue"
        class="w-full"
        @click="$emit('next-step', 'Allow Camera')"
      >
        <span>I understand, next step</span>
        <IconChevron class="w-4 h-4 ml-1.5" />
      </ButtonPrimary>
      <ButtonDisabled v-else size="lg" class="w-full">
        <LoadingSpinnerGray class="mr-1.5" />
        <span>Please, read the message first</span>
      </ButtonDisabled>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      canContinue: false
    }
  },
  created () {
    setTimeout(() => {
      this.canContinue = true
    }, 5000)
  }
}
</script>
