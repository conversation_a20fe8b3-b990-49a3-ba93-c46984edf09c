<template>
  <div class="flex flex-col items-center justify-center p-4 gap-4 text-center">
    <img class="object-cover w-full rounded-lg" :src="currentBackground.imgPath" alt="Background">
    <p class="text-sm font-medium text-gray-500">
      <template v-if="currentBackground?.label === 'Window'">
        Please stand facing a <span class="font-medium text-primary-500">window</span> (in front of you) and
      </template>
      <template v-else>
        Please stand in front of a <span class="font-medium text-primary-500">{{ currentBackground.label.toLowerCase() }} background</span> and
      </template>
      <span class="font-medium text-primary-500">{{ currentPose.toLowerCase() }}</span>.
    </p>
    <ButtonPrimary class="w-full" @click="$emit('closeModal')">
      Continue
    </ButtonPrimary>
  </div>
</template>

<script>
export default {
  props: {
    currentBackground: {
      type: Object,
      default: () => ({ label: 'White', imgPath: require('@/assets/img/phone-upload/white-bg.webp') })
    },
    currentPose: {
      type: String,
      default: 'Smile'
    }
  }
}
</script>
