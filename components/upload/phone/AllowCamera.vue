<template>
  <section class="relative pt-8 pb-28 sm:pt-12 md:pb-12">
    <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8">
      <div class="mx-auto text-left md:max-w-xl md:text-center">
        <h1 class="text-xl font-bold tracking-tight sm:text-2xl lg:text-3xl text-primary-500">
          Allow camera access
        </h1>
        <p class="mt-2 text-base font-medium text-gray-500 md:max-w-md md:mx-auto">
          We need access to your camera to take pictures. Please click the button bellow and give us access.
        </p>
      </div>

      <div v-if="isPending" class="mt-8">
        <button
          type="button"
          class="text-base font-medium text-primary-500 rounded-lg shadow-sm bg-white border border-gray-200 transition-all duration-150 hover:bg-gray-50 pt-2 pb-2.5 px-3.5 inline-flex items-center w-full gap-1.5 justify-center"
          @click="$emit('allowCamera')"
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-5 -mb-0.5">
            <path
              fill-rule="evenodd"
              d="M10 1a4.5 4.5 0 0 0-4.5 4.5V9H5a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-6a2 2 0 0 0-2-2h-.5V5.5A4.5 4.5 0 0 0 10 1Zm3 8V5.5a3 3 0 1 0-6 0V9h6Z"
              clip-rule="evenodd"
            />
          </svg>
          Open camera and give access
        </button>
      </div>

      <div v-else-if="isDenied" class="mt-8">
        <div class="inline-flex items-center gap-1.5">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
            class="-mb-1 text-red-500 size-5"
          >
            <path
              fill-rule="evenodd"
              d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z"
              clip-rule="evenodd"
            />
          </svg>
          <p class="text-lg font-bold tracking-tight text-red-500">
            Uh oh!
          </p>
        </div>
        <p class="mt-0.5 text-base font-medium text-gray-500">
          It looks like you denied access to your camera. Please follow the steps below to reset your camera
          permission.
        </p>

        <img
          class="w-full mt-5"
          :src="isAndroid
            ? '/phone-upload/android-permissions.webp'
            : '/phone-upload/ios-permissions.webp'"
          alt="Instructions"
        >
      </div>
    </div>

    <div v-if="isPending" class="fixed inset-x-0 bottom-0 py-4 bg-white border-t border-gray-100 shadow-sm md:hidden">
      <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8">
        <button
          type="button"
          class="text-base inline-flex w-full font-medium text-white rounded-lg shadow-sm bg-primary-500 gap-1.5 pt-2 pb-2.5 px-3.5 border border-transparent transition-all duration-150 hover:bg-opacity-90 items-center justify-center disabled:bg-opacity-20"
          @click="$emit('allowCamera')"
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-5 -mb-0.5">
            <path
              fill-rule="evenodd"
              d="M10 1a4.5 4.5 0 0 0-4.5 4.5V9H5a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-6a2 2 0 0 0-2-2h-.5V5.5A4.5 4.5 0 0 0 10 1Zm3 8V5.5a3 3 0 1 0-6 0V9h6Z"
              clip-rule="evenodd"
            />
          </svg>
          Open camera and give access
        </button>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  props: {
    cameraAllowed: {
      type: String,
      default: 'pending'
    },
    showUploadFromGallery: {
      type: Boolean,
      default: false
    },
    pauseFor: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      loading: false
    }
  },
  computed: {
    isAndroid () {
      return navigator.userAgent.toLowerCase().includes('android')
    },
    isIOS () {
      return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream
    },
    isPending () {
      return this.cameraAllowed === 'pending' || this.cameraAllowed === 'prompt'
    },
    isDenied () {
      return this.cameraAllowed === 'denied'
    },
    isGranted () {
      return this.cameraAllowed === 'granted' || this.cameraAllowed === 'allowed'
    }
  },
  watch: {
    pauseFor (newVal) {
      if (newVal > 0) {
        this.loading = true
        setTimeout(() => {
          this.loading = false
          this.$emit('resetPause')
        }, newVal)
      }
    }
  },
  mounted () {
    if (this.isGranted) {
      this.$emit('next-step', 'Capture')
    }
  }
}
</script>
