<template>
  <div>
    <div class="flex h-[100dvh] flex-col bg-gray-50 fixed top-0 left-0 w-full z-50">
      <template v-if="!allPhotosCaptured">
        <UploadPhoneHeader :show-logo="false" @close="closeWebcamModal" />
        <UploadPhoneAllowCamera
          v-if="activeStep === 'Allow Camera'"
          :camera-allowed="cameraAllowed"
          :show-upload-from-gallery="hasSomePicturesApproved"
          @allowCamera="setupCamera"
          @next-step="activeStep = $event"
        />
        <UploadPhoneCameraSelection
          v-else-if="activeStep === 'Camera Selection'"
          :pause-for="pauseFor"
          @freeCamera="enableFreeCamera"
          @next-step="activeStep = $event"
          @uploadFromGallery="uploadFromGallery"
          @resetPause="pauseFor = 0"
        />
        <template v-if="activeStep === 'Capture'">
          <div class="relative flex flex-1 items-center justify-center">
            <div
              v-if="!freeCameraMode"
              class="absolute top-0 z-[50] w-full border-b bg-gray-100/80 p-3 text-center text-base text-gray-700"
              :class="{'bounce-bar' : bounceLocation}"
            >
              <template v-if="currentPose === 'Smile'">
                <template v-if="currentBackground.label === 'Window'">
                  Face a window and
                </template>
                <template v-else>
                  {{ currentBackground.label }} Background and
                </template>
              </template>
              <strong>{{ currentPose }}</strong>
            </div>
            <video
              ref="video"
              autoplay
              playsinline
              class="max-h-[calc(100dvh-9rem)] max-w-full -scale-x-100"
            />
            <div
              class="mask-overlay absolute left-0 top-0 h-full w-full bg-black/60 pointer-events-none z-20"
            />
            <div ref="flash" class="absolute left-0 top-0 w-full h-full bg-white pointer-events-none z-30 opacity-0" />
          </div>
          <UploadPhoneCameraActions
            :captured="captured"
            :free-camera-mode="freeCameraMode"
            :uploading-from-phone="true"
            @capture="capture"
            @retake="retake"
            @disableFreeCamera="disableFreeCamera"
            @exitPhoneModal="closeWebcamModal"
          />
        </template>
      </template>
      <UploadPhoneFinalStep v-else :show-close-button="true" @next-step="activeStep = $event; allPhotosCaptured = false;" @closeWebcamModal="closeWebcamModal" />
    </div>
    <Popup v-if="instructionsModal" size="sm" @closeModal="instructionsModal = false">
      <UploadPhonePoseModal
        :current-background="currentBackground"
        :current-pose="currentPose"
        @closeModal="instructionsModal = false"
      />
    </Popup>
    <audio ref="shutterSound" src="/phone-upload/camera_sound.mp3" preload="auto" />
    <input
      ref="fileInput"
      type="file"
      accept="image/*"
      multiple
      style="display: none"
      @change="handleFileSelect"
    >
  </div>
</template>

<script>
import phoneUploadMixin from '../../../plugins/phoneUploadMixin'
export default {
  mixins: [phoneUploadMixin],
  props: {
    hasSomePicturesApproved: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      activeStep: 'Allow Camera'
    }
  },
  head () {
    return {
      title: this.title,
      meta: [
        { hid: 'no-zoom', name: 'viewport', content: 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no' }
      ]
    }
  },
  async mounted () {
    if (navigator.permissions && navigator.permissions.query) {
      try {
        const result = await navigator.permissions.query({ name: 'camera' })
        if (result.state === 'granted') {
          this.activeStep = 'Capture'
        }
        if (result.state === 'denied') {
          this.cameraAllowed = 'denied'
        } else {
          this.cameraAllowed = 'pending'
        }
      } catch (error) {
        this.$toast.error('Error checking camera permission: ' + error)
      }
    }
  },
  methods: {
    uploadCanvasImage (canvas) {
      canvas.toBlob((blob) => {
        this.$emit('captureImage', blob)
      }, 'image/png')
    },
    closeWebcamModal () {
      this.stopCamera()
      this.$emit('closeWebcamModal')
    }
  }
}
</script>

<style scoped>
.mask-overlay {
  mask-image: radial-gradient(
    ellipse 100px 125px at 50% 50%,
    transparent 95%,
    black 95.01%
  );
}
@keyframes flash {
  0% { opacity: 0; }
  50% { opacity: 1; }
  100% { opacity: 0; }
}

.flash-animation {
  animation: flash 0.5s;
}
</style>

<style>
:root {
  touch-action: pan-x pan-y;
  height: 100%;
}
</style>
