<template>
  <section class="relative pt-8 pb-28 sm:pt-12 md:pb-12">
    <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8">
      <div class="mx-auto text-left md:max-w-xl md:text-center">
        <h1 class="text-xl font-bold tracking-tight sm:text-2xl lg:text-3xl text-primary-500">
          Capture your selfies
        </h1>
        <p class="mt-2 text-base font-medium text-gray-500 md:max-w-md md:mx-auto">
          You are about to start capturing {{ minimumPhotos }} photos of yourself with our easy onboarding. When you are ready, press
          the button below. You can always retake photos if you are not happy with the results.
        </p>
      </div>

      <div class="mt-8 space-y-3">
        <div class="flex items-center gap-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="shrink-0 text-sky-400 size-6"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg>
          <p class="text-base font-medium tracking-tight text-gray-500 sm:text-lg">
            Takes up to 5 minutes
          </p>
        </div>

        <div class="flex items-center gap-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="shrink-0 text-sky-400 size-6"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"
            />
          </svg>
          <p class="text-base font-medium tracking-tight text-gray-500 sm:text-lg">
            Be rested and dressed up
          </p>
        </div>

        <div class="flex items-center gap-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="shrink-0 text-sky-400 size-6"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"
            />
          </svg>
          <p class="text-base font-medium tracking-tight text-gray-500 sm:text-lg">
            Take photos in a well lit room
          </p>
        </div>
      </div>

      <p class="mt-5 text-sm italic font-medium tracking-tight text-gray-500">
        This link is valid for 30 mins so make sure you are ready.
      </p>
    </div>

    <div class="fixed inset-x-0 bottom-0 py-4 bg-white border-t border-gray-100 shadow-sm md:hidden">
      <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8">
        <ButtonPrimary class="font-medium w-full" @click="$emit('next-step', 'Terms')">
          Start taking photos
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-5 -mb-0.5">
            <path
              fill-rule="evenodd"
              d="M3 10a.75.75 0 0 1 .75-.75h10.638L10.23 5.29a.75.75 0 1 1 1.04-1.08l5.5 5.25a.75.75 0 0 1 0 1.08l-5.5 5.25a.75.75 0 1 1-1.04-1.08l4.158-3.96H3.75A.75.75 0 0 1 3 10Z"
              clip-rule="evenodd"
            />
          </svg>
        </ButtonPrimary>
      </div>
    </div>
  </section>
</template>

<script>
import PostcheckoutMixin from '../../../mixins/PostcheckoutMixin'
export default {
  mixins: [PostcheckoutMixin]
}
</script>
