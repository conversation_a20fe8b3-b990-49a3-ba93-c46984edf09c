<template>
  <Card class="min-w-lg mt-4 w-full max-w-4xl rounded-lg shadow">
    <div class="p-2">
      <div class="flex flex-col md:flex-row items-center justify-between mb-2 md:mb-0">
        <ButtonWhite size="sm" class="w-full md:w-auto md:mr-4" @click="$emit('return')">
          <IconChevron class="mr-1.5 h-4 w-4 text-black transform -rotate-180" />
          <span>Back</span>
        </ButtonWhite>
        <div class="text-center">
          <h2 class="text-xl font-bold">
            Select your headshot styles
          </h2>
          <p class="mb-4 text-sm text-gray-700">
            You may self-select <strong>{{ maxStyles }} styles.</strong> The rest of your styles are pre-selected by us.
            <!-- Choose the additional background and clothing combinations. You can select <strong>{{ maxStyles }} styles.</strong> -->
          </p>
        </div>
        <ButtonDisabled v-if="!selectedStyle || selectedStyle?.length < 1 || (maxStyles - selectedStyle?.length) > 0" class="w-full md:w-auto md:ml-4 cursor-not-allowed" @click="$toast.warning(`Please select ${maxStyles} more backgrounds.`)">
          Next
        </ButtonDisabled>
        <ButtonPrimary v-else class="w-full md:w-auto md:ml-4 px-4 animate-pulse" @click="next">
          <span>Next</span>
          <IconChevron class="ml-1.5 h-4 w-4 text-white" />
        </ButtonPrimary>
      </div>
      <hr class="py-2">
      <template v-if="selectedStyle && selectedStyle?.length > 0">
        <h2 class="text-base font-medium leading-6 text-gray-900">
          Selected Styles
        </h2>
        <p class="text-sm font-light text-gray-600">
          These are the styles and clothing items that you selected for your perfect headshot.
        </p>
        <div class="mt-4 mb-8 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 bg-yellow-100/50 p-2 rounded-lg border border-black/10 shadow-md">
          <template v-for="(item, index) in selectedStyle">
            <div :key="item.clothing + item.style + index" class="group relative flex items-center rounded-md border border-teal-200 bg-teal-50 px-4 py-2">
              <!-- <span class="mr-4 text-sm font-light text-green-500">{{ numberOfItems[index] }}x</span> -->
              <span class="mr-4 text-sm font-light text-brand-500">40x</span>
              <div class="flex flex-col space-y-0.5">
                <span class="text-sm font-medium tracking-tight text-black">
                  {{ styleName(item.style) }}
                </span>
                <span class="text-sm font-normal leading-none text-gray-500">{{ clothingName(item.clothing) }}</span>
              </div>

              <button class="absolute right-4 ml-auto rounded-lg border border-gray-200 bg-white p-2 text-gray-600 group-hover:block lg:hidden" @click="deleteStyle(item)">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="h-4 w-4 hover:text-red-500"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                </svg>
              </button>
            </div>
          </template>
          <template v-for="index in Math.max(0, maxStyles - selectedStyle.length)">
            <div :key="`notselected-${index}`" class="w-full bg-gray-100 rounded-md flex items-center justify-center text-center p-2">
              <span class="text-sm text-black/50">
                Background not yet selected
              </span>
            </div>
          </template>
        </div>
      </template>
      <template v-if="selectedStyleIndexes.length < maxStyles">
        <h2 class="text-base font-medium leading-6 text-gray-900">
          Portrait styles (for all genders)
        </h2>
        <p class="text-sm font-light text-gray-600">
          These are all available styles and clothing items for your perfect headshot.
        </p>
        <div v-if="styles && styles.length > 0" class="mt-4 grid grid-cols-1 gap-2 md:grid-cols-2 lg:grid-cols-3">
          <template v-for="(style, index) in styles">
            <StyleCard
              v-if="selectedStyleIndexes.indexOf(index) === -1 && style?._id"
              :key="style._id + index"
              :card="true"
              :index="index"
              :style-item="style"
              :clothing="clothingItems"
              :selected-styles="selectedStyle"
              :team-member="role === 'TeamMember' || role === 'TeamLead'"
              @select="selectStyle($event, style._id, index)"
            />
          </template>
        </div>
        <div v-else class="mt-4">
          <AlertWarningV2 text="Your organization has not selected any styles or clothing items for you to choose from.">
            <p class="text-sm text-black/70">
              Please contact the admin of your organization and ask them to select some styles and clothing items for you to choose from.
            </p>
          </AlertWarningV2>
        </div>
      </template>
    </div>
  </Card>
</template>

<script>
export default {
  data () {
    return {
      isLoading: true,
      maxStyles: 1,
      numberOfHeadshots: 10,
      selectedStyleIndexes: []
    }
  },
  computed: {
    role () {
      return this.$store.state.user.role
    },
    selectedStyleIds () {
      return this.selectedStyle.map(item => item.style)
    },
    selectedSex: {
      get () {
        return this.$store.state?.onboarding?.selectedSex || null
      },
      set (value) {
        this.$store.commit('onboarding/SET_SELECTED_SEX', value)
      }
    },
    styles () {
      const allStyles = this.$store.state.styles
      // What's happening? - We are filtering out the styles that are not allowed in the org for the team member as well as the team lead
      if ((this.role === 'TeamMember' || this.role === 'TeamLead') && this.$store.state.organization?.organization) {
        const allowedOptions = this.$store.state.organization.organization.allowedOptions
        if (allowedOptions && allowedOptions.styles) {
          return allowedOptions.styles.map((style) => {
            // Find style in allStyles and return it
            return allStyles.find(s => s._id === style)
          }).filter(style => style?._id)
          // return allStyles.filter(style => allowedOptions.styles.includes(style._id))
        } else {
          const styleIds = this.$store.state.organization.organization.style.map(style => style.style)
          return allStyles.filter((style) => {
            return styleIds.includes(style._id)
          }).filter(style => style?._id)
        }
      }
      return allStyles
    },
    clothingItems () {
      const allClothingItems = this.$store.state.clothing || []
      if ((this.role === 'TeamMember' || this.role === 'TeamLead') && this.$store.state.organization?.organization) {
        const allowedOptions = this.$store.state.organization.organization.allowedOptions
        const selectedSex = this.$store.state.onboarding.selectedSex
        if (allowedOptions && allowedOptions.clothing && allowedOptions.clothing[selectedSex]) {
          return allClothingItems.filter(clothing => allowedOptions.clothing[selectedSex].includes(clothing._id))
        }
      }
      return allClothingItems
    },
    selectedStyle: {
      get () {
        return this.$store.state.onboarding.selectedStyles || []
      },
      set (style) {
        this.$store.commit('onboarding/SET_SELECTED_STYLE', style)
      }
    },
    numberOfItems () {
      if (!this.selectedStyle) {
        return []
      } else {
        return this.selectedStyle.map((item) => {
          return this.numberOfHeadshots / this.selectedStyle.length
        })
      }
    }
  },
  mounted () {
    // this.fetch()
    this.getSelectedPackage()
    if (!this.styles || this.styles.length === 0) {
      this.$store.dispatch('getStyles')
    }
    if (this.$store.state?.clothing?.length === 0) {
      this.$store.dispatch('getClothing')
    }
    this.selectedStyleIndexes = this.selectedStyle.map(item => item.index)
  },
  methods: {
    getSelectedPackage () {
      if (this.role === 'TeamMember' || this.role === 'TeamLead') {
        // if (this.styles.length < 3) {
        //   this.maxStyles = this.styles.length
        // } else {
        this.maxStyles = this.styles.length
        return
        // }
      }
      const packageId = this.$store.state?.user?.packages[0]
      if (packageId) {
        const selectedPackage = this.$store.state.packages[packageId]
        this.maxStyles = selectedPackage.styles
        this.numberOfHeadshots = selectedPackage.photos
      }
    },
    styleName (id) {
      const style = this.styles.find(style => style._id === id)
      return style?.title || ''
    },
    clothingName (id) {
      const clothing = this.clothingItems.find(clothing => clothing._id === id)
      return clothing?.type || ''
    },
    // async fetch () {
    //   try {
    //     this.isLoading = true
    //     const { success, errorMessage, data } = await this.$axios.$get('/clothing')
    //     if (!success) {
    //       throw new Error(errorMessage)
    //     }
    //     this.$store.commit('SET_CLOTHING', data)
    //   } catch (err) {
    //     this.handleError(err)
    //   } finally {
    //     this.isLoading = false
    //   }
    // },
    selectStyle (clothing, style, index) {
      try {
        this.isSaving = true
        if (this.selectedStyle && this.selectedStyle.length >= this.maxStyles) {
          throw new Error(`You can only select ${this.maxStyles} styles`)
        }
        this.selectedStyle = {
          style,
          clothing,
          index
        }
        this.$toast.success('Background and clothing selected.')
        window?.scrollTo({
          top: 0,
          behavior: 'smooth'
        })
        this.selectedStyleIndexes.push(index)
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isSaving = false
      }
    },
    deleteStyle (item) {
      const index = item.index
      try {
        this.$store.commit('onboarding/REMOVE_STYLE', index)
        this.selectedStyleIndexes = this.selectedStyleIndexes.filter(i => i !== index)
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isSaving = false
      }
    },
    next () {
      if (!this.selectedStyle || this.selectedStyle.length < 1) {
        this.$toast.warning('Please select at least 1 style')
        return
      }
      this.$emit('next')
    }
  }
}
</script>

<style></style>
