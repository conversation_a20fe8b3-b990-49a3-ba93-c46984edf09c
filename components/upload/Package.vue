<template>
  <Card class="min-w-lg mt-4 w-full max-w-3xl rounded-lg shadow">
    <div class="flex">
      <div class="p-8 pr-4 w-full">
        <AlertWarning v-if="hasInvites && showInviteWarning" class="mb-2" title="Accept invite to continue for free." description="You have invites to join a team. Click on the 'Invites' button to accept them." />
        <h2 class="text-xl font-bold">
          Select a package
        </h2>
        <p class="mb-4 text-gray-700 text-sm">
          Pay once, no subscriptions or hidden fees. We offer no trial due to high costs, but we will refund you if you're unsatisfied.
        </p>
        <!-- <div class="bg-green-500/5 border border-green-500/10 inline-flex w-full content-start items-start justify-start p-2.5 text-left text-sm font-medium text-green-700 rounded-lg">
          <p>
            <strong>Black Friday Offer:</strong> Get up to 120 extra photos
            <span class="text-black/70 text-sm"><CountdownText date="2023-11-28T04:00:00" /></span>
          </p>
        </div> -->

        <!-- <div class="bg-green-500/5 border border-green-500/10 inline-flex w-full content-center items-center justify-center p-1.5 text-center text-sm font-medium text-green-700 rounded-lg">
        <svg class="mr-2 h-5 w-5 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
        </svg>
        14 Days Money Back Guarantee
      </div> -->
        <!-- <ExperimentWrapper id="7-profile-worthy-refund">
          <template #variant>
            <div class="bg-green-500/5 border border-green-500/10 inline-flex w-full content-start items-start justify-start p-2.5 text-left text-sm font-medium text-green-700 rounded-lg">
              <svg class="mr-2 h-5 w-5 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              <p class="mt-[-2px]">
                Try risk free with our ‘Profile-Worthy’
                Guarantee.
                <nuxt-link to="/refund" class="underline" target="_blank">
                  Learn more.
                </nuxt-link>
              </p>
            </div>
          </template>
        </ExperimentWrapper> -->
        <!-- <div class="flex items-center justify-center border-green-100 bg-green-50 text-green-900 border p-3 rounded-lg mt-2">
        <CountdownUntilDate start-date="2023-01-30T00:00:00.000Z" end-date="2023-02-06T00:00:00.000Z" />
      </div> -->

        <div class="mt-2 flex flex-col overflow-hidden rounded-xl border border-gray-200">
          <template v-for="price in prices">
            <div v-if="price?.meta?.visible" :key="price.packageId" class="grid md:flex w-full grid-cols-5 md:space-x-4 items-center border-b border-gray-200 p-4 gap-4">
              <div class="col-span-5 mb-2 flex flex-col md:col-span-4 md:mb-0 pr-2">
                <div class="flex items-center justify-start space-x-2">
                  <h2 class="text-base text-gray-800 flex items-center space-x-1">
                    <span class="font-bold text-black">{{ getLocalizedPrice(price.id, true, 0, false) }}</span>
                    <span class="text-gray-700">∙</span>
                    <span class="text-sm font-medium">{{ price.title }}</span>
                  </h2>
                  <div v-if="price.meta.tag === 'popular'" class="gradient-bg rounded-lg px-1.5 py-0.5 text-[9px] font-bold uppercase text-white">
                    78% PICK THIS PLAN
                  </div>
                  <!-- <div v-if="price.tag === 'value'" class="rounded-lg bg-green-500 px-1.5 py-0.5 text-[9px] font-bold uppercase text-white">
                    BEST VALUE
                  </div> -->
                </div>

                <p class="text-sm text-gray-600">
                  <span class="font-medium">
                    Get <strike v-if="price.meta.regularStyles" class="text-red-500"> {{ price.meta.regularStyles * photoPerStyle }}</strike> <strong>{{ (price.meta.styles + price.meta.additional) * photoPerStyle }}</strong>
                    headshots with <strike v-if="price.meta.regularStyles" class="text-red-500"> {{ price.meta.regularStyles }}</strike>
                    <strong>{{ (price.meta.styles + price.meta.additional) }}</strong> unique
                    <template v-if="price.meta.styles === 1">background and outfit</template>
                    <template v-else>backgrounds and outfits</template>.
                    <span>Done in <strong>{{ price?.meta?.turnAroundTime ?? '2 hours' }}</strong></span>
                    <!-- <template v-if="checkIfUserIsInTestId('10-shipping-speed-large-packages')">
                      <span v-if="price.title === 'Small'">Done in <strong class="text-orange-800">24 hours</strong>.</span>
                      <span v-else>Done in <strong>2</strong> hours.</span>
                    </template>
                    <template v-else> -->
                    <!-- <span>Done in <strong>2</strong> hours.</span> -->
                    <!-- </template> -->
                  </span>
                </p>
              </div>
              <div class="col-span-5 flex w-full justify-start md:col-span-1 md:w-auto md:justify-end">
                <nuxt-link :to="`/checkout/?priceId=${price.id}`" class="block w-full md:w-auto">
                  <ButtonDark v-if="price.meta.tag === 'popular'" size="sm" class="w-full md:w-auto">
                    <span>Select </span>
                    <IconSmallArrow class="w-4 h-4 text-white ml-0.5" />
                  </ButtonDark>
                  <ButtonWhite v-else size="sm" class="w-full md:w-auto">
                    <span>Select </span>
                    <IconSmallArrow class="w-4 h-4 text-black ml-0.5" />
                  </ButtonWhite>
                </nuxt-link>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div v-if="reviewsWithContent.length" class="hidden md:flex w-[250px] flex-shrink-0 border-l border-gray-100 py-4">
        <div class="p-4 divide-y-1 gap-4 flex flex-col divide-y-gray-100">
          <div id="review" class="transition duration-300 relative rounded-md overflow-hidden">
            <div class="bg-white/70 backdrop-blur-md text-brand-500 text-[9px] p-1 px-2 font-bold rounded-md absolute top-2 left-2">
              AI Headshot
            </div>
            <ImageDns :src="reviewsWithContent[selectedReview].image" @load="increaseOpacity()" />
            <div class="absolute bg-gradient-to-t h-1/2 flex justify-end flex-col from-black/80 to-transparent text-white p-3 bottom-0 left-0 w-full text-sm  space-y-1">
              <p class="font-bold">
                {{ reviewsWithContent[selectedReview].title }}
              </p>
              <p class="italic text-white/80 text-xs">
                {{ reviewsWithContent[selectedReview].review.quote }}
              </p>
            </div>
          </div>
          <div class="text-xs text-gray-600 flex flex-col items-start justify-start space-y-1.5">
            <span> Used by {{ $store.state.stats.users }} happy customers. <a href="https://www.headshotpro.com/reviews" target="_blank" class="underline">Check reviews</a></span>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="border-t text-xs text-gray-600 border-gray-200 bg-yellow-500/10 px-8 py-3 flex items-center justify-center space-x-1.5">
      <div class="flex items-center space-x-px">
        <IconSolidStar v-for="index of 5" :key="index" class="w-3 h-4 text-yellow-500" />
      </div>
      <span> Used by {{ $store.state.stats.users }} happy customers. <a href="https://www.headshotpro.com/reviews" target="_blank" class="underline">Check reviews</a></span>
    </div> -->
  </Card>
</template>

<script>
export default {
  data () {
    return {
      selectedReview: 0
    }
  },
  computed: {
    hasInvites () {
      return this.$store.state?.user?.invites || null
    },
    showInviteWarning () {
      return this.$store.state?.showInviteWarning || null
    },
    prices () {
      const packages = this.$store.state.packages
      // if (this.$store.state?.user?.countryCode === 'US') {
      //   delete packages.small
      //   return packages
      // } else {
      return packages
      // }
      // return this.$store.state.packages
    },
    env () {
      return process.env.NODE_ENV
    },
    reviewsWithContent () {
      // Create a copy of the reviews array to avoid side effects
      const reviewsCopy = [...this.$store.state.reviews]
      if (!reviewsCopy.length) {
        return []
      }
      // Sort review.review.quote to the front
      return reviewsCopy.sort((a, b) => {
        if (a.review.quote && !b.review.quote) { return -1 }
        if (!a.review.quote && b.review.quote) { return 1 }
        return 0
      })
    }
  },
  mounted () {
    // Create an interval of 5000 milliseconds a  nd increment selectedReview
    // On change, change the opacity from 1 to 0 and back to 1
    if (this.reviewsWithContent.length) {
      setInterval(() => {
        this.selectedReview = (this.selectedReview + 1) % this.reviewsWithContent.length
        const $review = document.getElementById('review')
        if ($review) {
          $review.style.opacity = 0.1
        }
      }, 5000)
    }

    this.$posthog.capture('$funnel:package_selection')
  },
  methods: {
    increaseOpacity () {
      document.getElementById('review').style.opacity = 1
    }
  }
}
</script>

<style></style>
