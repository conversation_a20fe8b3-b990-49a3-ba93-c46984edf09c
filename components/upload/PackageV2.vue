<template>
  <Card class="min-w-lg mt-4 w-full max-w-lg rounded-lg shadow">
    <div class="p-8">
      <h2 class="text-xl font-bold">
        Select a package
      </h2>
      <p class="mb-4 text-gray-700">
        One-time payment. No subscription. After payment you need to upload atleas 15 photos of yourself.
      </p>
      <div id="convert-3-v2" class="bg-green-500/5 border border-green-500/10 inline-flex w-full content-center items-center justify-center p-1.5 text-center text-sm font-medium text-green-700 rounded-lg">
        <svg class="mr-2 h-5 w-5 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
        </svg>
        14 Days Money Back Guarantee
      </div>
      <!-- <div class="flex items-center justify-center border-green-100 bg-green-50 text-green-900 border p-3 rounded-lg mt-2">
        <CountdownUntilDate start-date="2023-01-30T00:00:00.000Z" end-date="2023-02-06T00:00:00.000Z" />
      </div> -->

      <div class="mt-2 flex flex-col overflow-hidden rounded-xl border border-gray-200">
        <template v-for="price in prices">
          <div :key="price.packageId" class="grid w-full grid-cols-5 items-center border-b border-gray-200 p-4">
            <div class="col-span-5 mb-2 flex flex-col md:col-span-3 md:mb-0">
              <div class="flex items-center justify-start space-x-2">
                <h2 class="text-base font-bold">
                  {{ price.title }}
                </h2>
                <div v-if="price?.meta?.tag === 'popular'" class="gradient-bg rounded-lg px-1.5 py-0.5 text-[9px] font-bold uppercase text-white">
                  POPULAR
                </div>
                <div v-if="price?.meta?.tag === 'value'" class="rounded-lg bg-green-500 px-1.5 py-0.5 text-[9px] font-bold uppercase text-white">
                  BEST VALUE
                </div>
              </div>
              <p class="text-sm text-gray-700">
                <span class="font-medium">
                  Get {{ price?.meta?.styles * 40 }}
                  headshots with
                  {{ price?.meta?.styles }} different
                  <template v-if="price?.meta?.styles === 1">background</template>
                  <template v-else>backgrounds</template>.
                </span>
              </p>
            </div>
            <div class="col-span-5 flex w-full justify-start md:col-span-2 md:w-auto md:justify-end">
              <nuxt-link :to="`/checkout/?priceId=${(env === 'development') ? price.ids[0] : price.ids[1]}`" class="block w-full md:w-auto">
                <ButtonDark v-if="price?.meta?.tag === 'popular'" size="sm" class="w-full md:w-auto">
                  Select (${{ price.price }}
                  )
                </ButtonDark>
                <ButtonWhite v-else size="sm" class="w-full md:w-auto">
                  Select (${{ price.price }}
                  )
                </ButtonWhite>
              </nuxt-link>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="border-t text-xs text-gray-600 border-gray-200 bg-yellow-500/10 px-8 py-3 flex items-center justify-center space-x-1.5">
      <div class="flex items-center space-x-px">
        <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path
            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
          />
        </svg>
        <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path
            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
          />
        </svg>
        <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path
            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
          />
        </svg>
        <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path
            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
          />
        </svg>
        <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path
            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
          />
        </svg>
      </div>
      <span> Used by {{ $store.state.stats.users }} happy customers.</span>
    </div>
  </Card>
</template>

<script>
export default {
  computed: {
    prices () {
      return this.$store.state.packages
    },
    env () {
      return process.env.NODE_ENV
    }
  },
  // data () {
  //   return {
  //     prices: [
  //       {
  //         packageId: 'small',
  //         title: 'Small',
  //         styles: 1,
  //         price: 29, // 34,
  //         id: this.$config.NODE_ENV === 'development' ? 'price_1MngkZImxLpARiYCsRBPLJcy' : 'price_1Mng6mImxLpARiYCtWqeMWIe'
  //       },
  //       {
  //         packageId: 'medium',
  //         title: 'Normal',
  //         styles: 3,
  //         tag: 'popular',
  //         price: 39, // 34,
  //         id: this.$config.NODE_ENV === 'development' ? 'price_1MngA1ImxLpARiYC2ZWkoa8O' : 'price_1Mng78ImxLpARiYC8YRfVr2F'
  //       },
  //       {
  //         packageId: 'large',
  //         title: 'Premium',
  //         styles: 6,
  //         price: 69, // 34,
  //         id: this.$config.NODE_ENV === 'development' ? 'price_1MngAHImxLpARiYCAJdEuf7i' : 'price_1Mng8YImxLpARiYCoxDDXl4p'
  //       }
  //     ]
  //   }
  // },

  methods: {}
}
</script>

<style></style>
