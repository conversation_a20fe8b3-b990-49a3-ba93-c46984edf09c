<template>
  <Card class="min-w-lg mt-4 w-full max-w-lg rounded-lg shadow">
    <div class="p-2">
      <h2 class="text-xl font-bold">
        Create your headshots for {{ organization.name }}
      </h2>
      <p class="mb-4 text-gray-700">
        You've been invited to create professional headshots for {{ organization.name }}. You will need to upload some photos of yourself in the following steps. These photos will be used to create your headshots.
      </p>
      <div class="grid w-full grid-cols-2">
        <img src="~assets/img/headshot-example-male.png">
        <img src="~assets/img/headshot-example-female.png">
      </div>

      <ButtonPrimary v-if="canContinue" class="mt-4 w-full" @click="$emit('next')">
        <span class="hidden md:block">Ok, Let's go</span>
        <span class="block md:hidden">Ok, Let's go</span>
      </ButtonPrimary>
      <ButtonDisabled v-else size="lg" class="mt-4 w-full">
        <!-- <LoadingSpinnerGray class="mr-1.5" /> -->
        <span>Read the message above first</span>
      </ButtonDisabled>
    </div>
  </Card>
</template>

<script>
export default {
  data () {
    return {
      canContinue: false
    }
  },
  computed: {
    organization () {
      return this.$store.state.organization.organization
    }
  },
  created () {
    // Set this.canContinue to true after 3 seconds
    setTimeout(() => {
      this.canContinue = true
    }, 2000)
  }
}
</script>

<style></style>
