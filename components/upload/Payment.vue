<template>
  <Card class="min-w-lg mt-4 w-full max-w-lg rounded-lg shadow">
    <div class="p-8">
      <h2 class="text-xl font-bold">
        Payment
      </h2>
      <p class="mb-4 text-sm text-gray-700">
        One-time payment. No subscription. All packages come with a default amount of photos. Payment securely handled by Stripe.
      </p>
      <div class="py-5">
        <div class="space-y-2">
          <LoadingWrapper :is-loading="isLoading" :title="isBuying ? 'Redirecting you to safe checkout...' : 'Loading your items...'">
            <div v-if="product" class="w-full rounded-md">
              <div class="flex w-full justify-between space-x-4">
                <div class="flex items-start space-x-4">
                  <ImageDns v-if="product.image" :src="product.image" class="h-20 w-20 rounded-md" />
                  <div class="flex flex-col space-y-1">
                    <h2 v-if="product.name" class="font-medium">
                      {{ product.name }}
                    </h2>
                    <p v-if="product.description" class="-mt-1 text-gray-600">
                      {{ product.description }}
                    </p>
                    <p v-if="price" class="text-lg font-medium text-gray-800">
                      {{ formatPrice(price / 100) }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <hr class="my-6 w-full border-gray-200">
            <div class="flex items-center justify-between">
              <span class="text-lg text-gray-500">Subtotal</span>
              <span v-if="!discounted" class="text-lg font-bold text-teal-500">
                {{ formatPrice((price * quantity) / 100) }}
              </span>
              <span v-else class="text-lg font-bold text-teal-500">
                <span class="font-normal text-gray-500 line-through">$49</span>
                $39
              </span>
            </div>
          </LoadingWrapper>
        </div>
      </div>
      <ButtonDisabled v-if="isBuying" size="lg" class="w-full">
        <LoadingSpinnerGray class="mr-1.5" />
        <span>Redirecting you to safe checkout...</span>
      </ButtonDisabled>
      <template v-else>
        <ButtonPrimary v-if="true" class="mt-4 w-full" @click="toCheckout()">
          <span class="hidden md:block">Continue to payment</span>
          <span class="block md:hidden">To payment</span>
        </ButtonPrimary>
        <ButtonDisabled v-else size="lg" class="mt-4 w-full">
          <!-- <LoadingSpinnerGray class="mr-1.5" /> -->
          <span>Read the message above first</span>
        </ButtonDisabled>
      </template>
    </div>
  </Card>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: null
    },
    subTitle: {
      type: String,
      default: null
    }
  },
  data () {
    return {
      isLoading: true,
      isBuying: false,
      product: null,
      price: null,
      quantity: 1
    }
  },
  computed: {
    discounted () {
      const endTime = new Date('March 19, 2023 10:00:00').getTime()
      // if now is before endTime, return true
      return new Date().getTime() < endTime
    },
    priceId () {
      return this.$config.NODE_ENV === 'development' ? 'price_1MkmMjImxLpARiYC0tUmw9kQ' : 'price_1MmAqwImxLpARiYCA723tf0x'
    }
  },

  mounted () {
    this.getPrice()
  },

  methods: {
    async getPrice () {
      try {
        this.isLoading = true

        const { success, data, errorMessage } = await this.$axios.$get('/checkout/stripe/price/retrieve/' + this.priceId)
        if (!success) {
          throw new Error(errorMessage)
        }
        this.price = data.unit_amount
        const product = await this.$axios.$get('/checkout/stripe/product/retrieve/' + data.product)
        if (product) {
          const { description, images, name } = product
          this.product = { description, name, image: images[0] }
        }
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = false
      }
    },
    async toCheckout () {
      try {
        this.isBuying = true
        this.isSaving = true

        const { success, data, errorMessage } = await this.$axios.$post('/checkout/stripe/create-checkout-session', {
          priceId: this.priceId,
          quantity: this.quantity,
          rewardfulId: this.userWasReferredViaInternalSystem ? null : (window?.Rewardful?.referral || null)
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        window.location.href = data.url
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isBuying = false
        this.isSaving = false
      }
    }
  }
}
//
</script>
