<template>
  <Card class="min-w-lg mt-4 w-full max-w-lg rounded-lg shadow">
    <div class="p-2">
      <!-- <h2 class="text-xl font-bold">
        Good and bad examples
      </h2> -->

      <img src="@/assets/img/good-bad-example.png">
      <ul class="mb-2 space-y-2 rounded-md bg-orange-500/10 p-2 px-4 ">
        <li class="flex items-center justify-start">
          <IconExclamation class="mr-1.5 h-3 w-3 text-orange-500" />
          <span class="text-sm"> Use a variety of good quality photos.</span>
        </li>
      </ul>

      <!-- <div class="my-4 flex w-full items-center justify-center">
        <IconExclamation class="mr-1.5 h-4 w-4 text-orange-500" />
        <span class="text-sm font-medium">We do NOT refund anyone who breaks these terms.</span>
      </div> -->
      <ButtonPrimary v-if="canContinue" class="mt-4 w-full" @click="$emit('next')">
        <span class="hidden md:block">Ok, I understand the above</span>
        <span class="block md:hidden">Ok, I understand</span>
      </ButtonPrimary>
      <ButtonDisabled v-else size="lg" class="mt-4 w-full">
        <LoadingSpinnerGray class="mr-1.5" />
        <span>Read the message above first</span>
      </ButtonDisabled>
    </div>
  </Card>
</template>

<script>
export default {
  data () {
    return {
      canContinue: false
    }
  },
  created () {
    // Set this.canContinue to true after 3 seconds
    setTimeout(() => {
      this.canContinue = true
    }, (this.env === 'development') ? 10 : 5000)
  }
}
</script>

<style></style>
