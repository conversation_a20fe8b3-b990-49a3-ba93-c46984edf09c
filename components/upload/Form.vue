<template>
  <Card class="min-w-lg mt-4 w-full max-w-lg rounded-lg shadow">
    <div class="p-2">
      <h2 class="text-xl font-bold">
        Personal information
      </h2>
      <p class="mb-4 text-sm text-gray-700">
        We need some information about you to train our AI. This information will not be shared with anyone and is deleted after your photo shoot.
      </p>
      <div class="flex flex-col">
        <span
          class="mt-2 block text-sm font-medium leading-5 text-gray-900"
        >
          Gender*
        </span>
        <div class="mb-2 mt-1 grid grid-cols-3 gap-2 md:grid-cols-3">
          <button
            v-for="sex in sexOptions"
            :key="sex.value"
            class="cursor-pointer rounded-md border p-1 px-2 text-sm md:p-2"
            :class="
              selectedSex === sex.value
                ? 'border-transparent bg-brand-500 text-white shadow-sm hover:bg-brand-800 focus:outline-none focus:ring-2 focus:ring-brand-200 focus:ring-offset-2'
                : 'rounded-md border-gray-300 bg-white text-base font-medium text-gray-700 shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2'
            "
            @click="selectedSex = sex.value"
          >
            {{ sex.title }}
          </button>
        </div>
        <Input v-model="title" label="Name*" placeholder="First and/or last name" />
        <InputSelect v-model="selectedAge" label="Age*" :options="ageList" class="mt-2" />
        <InputSelect v-model="selectedEyeColor" label="Eye color*" :options="eyeColorList" class="mt-2" />
        <InputSelect v-model="ethnicity" label="Ethnicity" :options="etnicityList" class="mt-2">
          <span class="text-xs text-gray-500">Not required.</span>
        </InputSelect>
        <div class="grid grid-cols-3 gap-2 mt-4">
          <ButtonWhite class="col-span-1" @click="$emit('return')">
            <IconChevron class="w-4 h-4 text-gray-500 mr-1.5 transform rotate-180" />
            <span>Back</span>
          </ButtonWhite>
          <template v-if="title?.length > 0 && selectedSex && selectedSex.length > 0 && selectedEyeColor && selectedEyeColor.length > 0 && selectedAge && selectedAge.length > 0">
            <ButtonPrimary class="col-span-2" @click="$emit('next')">
              <span>Next</span>
              <IconChevron class="w-4 h-4 text-black ml-1.5" />
            </ButtonPrimary>
          </template>
          <template v-else>
            <ButtonDisabled class="col-span-2">
              <span v-if="!selectedSex || selectedSex.length === 0">
                Please select a gender
              </span>
              <span v-else-if="!title || title?.length === 0">
                Fill in a name first
              </span>
              <span v-else-if="!selectedAge || selectedAge?.length === 0">
                Please select your age
              </span>
              <span
                v-else-if="
                  !selectedEyeColor || selectedEyeColor.length === 0
                "
              >
                Please your eye color
              </span>
            </ButtonDisabled>
          </template>
        </div>
      </div>
    </div>
  </Card>
</template>
<script>
export default {
  data () {
    return {
      eyeColorList: [
        '',
        'blue',
        'light blue',
        'dark blue',
        'brown',
        'light brown',
        'dark brown',
        'green',
        'hazel',
        'grey',
        'amber',
        'black',
        'other'
      ],
      ageList: [
        { title: '12 - 18 years', value: 'adolescence' },
        { title: '19 - 25 years', value: 'young adult' },
        { title: '26 - 29 years', value: 'late twenties' },
        { title: '30 - 35 years', value: 'early thirties' },
        { title: '36 - 45 years', value: 'mid-life' },
        { title: '46 - 55 years', value: 'middle-aged' },
        { title: '56 - 65 years', value: 'late middle age' },
        { title: '66 - 75 years', value: 'senior' },
        { title: '76+ years', value: 'elderly' }
      ],
      sexOptions: [
        { title: 'Male', value: 'male' },
        { title: 'Female', value: 'female' }
      ],
      etnicityList: [
        '',
        'african',
        'arabic',
        'asian',
        'black-or-african-american',
        'caribbean',
        'indian',
        'melanesian',
        'polynesian',
        'european',
        'caucasian',
        'latin-american',
        'hispanic',
        'other'
      ]
    }
  },
  computed: {
    selectedSex: {
      get () {
        return this.$store.state?.onboarding?.selectedSex || null
      },
      set (value) {
        this.$store.commit('onboarding/SET_SELECTED_SEX', value)
      }
    },
    title: {
      get () {
        return this.$store.state?.onboarding?.title || null
      },
      set (value) {
        this.$store.commit('onboarding/SET_TITLE', value)
      }
    },
    selectedAge: {
      get () {
        return this.$store.state?.onboarding?.selectedAge || null
      },
      set (value) {
        this.$store.commit('onboarding/SET_SELECTED_AGE', value)
      }
    },
    selectedEyeColor: {
      get () {
        return this.$store.state?.onboarding?.selectedEyeColor || null
      },
      set (value) {
        this.$store.commit('onboarding/SET_SELECTED_EYE_COLOR', value)
      }
    },
    ethnicity: {
      get () {
        return this.$store.state?.onboarding?.ethnicity || null
      },
      set (value) {
        this.$store.commit('onboarding/SET_ETHNICITY', value)
      }
    }
  }
}
</script>

<style>

</style>
