<template>
  <div>
    <Card class="min-w-3xl mt-4 w-full max-w-3xl rounded-lg shadow">
      <div class="p-4 md:p-8 space-y-4">
        <template v-if="isSaving">
          <div class="rounded-md bg-gray-100 px-4 py-2">
            <LoadingSpinner class="mx-auto" title="Submitting your photos..." />
          </div>
        </template>
        <template v-else>
          <template v-if="isSubmitted">
            <div class="w-full text-center">
              <h2 class="text-xl font-medium text-green-500">
                🥳 Upload successful!
              </h2>
              <p class="mb-4 mt-2 text-sm text-gray-700">
                Your headshots should be done in up to 2 hours.
                <br>
                You'll get an email when they're ready.
              </p>
              <LoadingSpinner title="Redirecting you..." class="mb-4" />
              <nuxt-link to="/app" class="text-xs text-gray-600 underline">
                Click here if it takes too long.
              </nuxt-link>
            </div>
          </template>
          <template v-else>
            <div class="space-y-2">
              <div class="flex justify-between">
                <h2 class="text-base md:text-xl font-bold">
                  <span v-if="approvedPhotoPaths.length >= minimumPhotos && approvedPhotoPaths.length <= maximumPhotos">Final step: Are these your final photos?</span>
                  <span v-if="approvedPhotoPaths.length > maximumPhotos">Final step: Please pick your best photos</span>
                </h2>
                <div
                  class="flex items-center justify-center rounded-full text-white text-xs font-bold px-4"
                  :style="`background: ${
                    (approvedPhotos.length > maximumPhotos) ? 'red'
                    : (approvedPhotoPaths.length >= minimumPhotos && approvedPhotoPaths.length < maximumPhotos) ? 'green'
                      : (approvedPhotoPaths.length < minimumPhotos) ? 'red' : 'green'}`"
                >
                  {{ approvedPhotos.length }} / {{ minimumPhotos }}
                </div>
              </div>
              <p class="mb-4 text-sm text-gray-700">
                Last chance to remove low-quality photos for your headshots. <strong>Click photos to remove them.</strong>
              </p>
            </div>

            <div v-if="approvedPhotos.length > maximumPhotos" class="animate-pulse text-center flex items-center justify-center text-sm rounded-md w-full bg-orange-500/5 border border-orange-500/10 p-0.5 md:p-2 text-orange-800">
              <IconExclamation class="w-4 h-4 text-orange-500 mr-1.5" />
              <span v-if="approvedPhotos.length > maximumPhotos"> Remove <strong>{{ approvedPhotoPaths.length - maximumPhotos }} lowest quality photos </strong> (suggested: orange-marked ones).
              </span>
            </div>
            <!-- <img src="@/assets/img/good-bad-example.png"> -->
            <div class="grid grid-cols-4 md:grid-cols-8 gap-4 bg-gray-50 border border-gray-100 p-2 rounded-md">
              <!-- <img v-for="photo in approvedPhotos" :key="photo" :src="photo" class="rounded-md"> -->
              <template v-for="(file, index) in approvedPhotos">
                <div :key="index" class="group relative w-full overflow-hidden rounded-lg">
                  <span v-if="photoRatings[index]" class="top-1 left-1 absolute z-30  text-white text-[9px] rounded-full px-1 py-0.5" :class="(getLowestScores.includes(photoRatings[index])) ? 'bg-orange-500' : 'bg-black'">{{ photoRatings[index]?.toFixed(2) }}</span>
                  <img :src="file" class="relative z-10 w-full object-cover">
                  <div class="absolute left-0 top-0 z-20 hidden h-full min-h-[32px] w-full cursor-pointer items-center justify-center bg-black/80 group-hover:flex" @click="removeFile(index)">
                    <div class="flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-white">
                      <IconCross class="h-3 w-3" />
                    </div>
                  </div>
                </div>
              </template>
              <template v-for="(file, index) in declinedPhotos">
                <div :key="index" class="group relative w-full overflow-hidden rounded-lg ">
                  <img :src="file" class="relative z-10 w-full object-cover  opacity-50">
                  <div class="w-[calc(100%-16px)] h-full flex items-center justify-center top-0 absolute left-[8px] text-[9px] z-20">
                    <div class="w-16 h-6  bg-red-500  rounded-full text-white p-1 flex items-center justify-center">
                      Declined
                    </div>
                  </div>
                </div>
              </template>
            </div>
            <div class="">
              <ul class="text-sm flex flex-col md:flex-row items-start md:items-center justify-start space-y-2 md:space-y-0 md:space-x-2">
                <li>Name: <strong>{{ title }}</strong></li>
                <li>Gender: <strong>{{ selectedSex }}</strong></li>
                <li>Eye color: <strong>{{ selectedEyeColor }}</strong></li>
                <li>Ethnicity: <strong>{{ ethnicity }}</strong></li>
                <li>Age: <strong>{{ selectedAge }}</strong></li>
              </ul>
            </div>
            <div class="my-2 border-t border-gray-200 py-2">
              <div class="flex items-start md:items-center justify-start space-x-2 pb-1 pt-1">
                <input id="tos" v-model="acceptTos" type="checkbox" class="">
                <label for="tos" class="cursor-pointer text-xs md:text-sm text-gray-600">
                  I agree to the
                  <a href="/legal/terms-and-conditions" target="_blank" class="underline">terms & conditions</a>
                  ,
                  <a href="/legal/privacy-policy" target="_blank" class="underline">privacy policy</a>
                  and the
                  <span class="underline" @click="showRequirementsModal = true">upload requirements</span>
                  .
                </label>
              </div>
              <div class="flex items-start md:items-center justify-start space-x-2 pb-4 pt-1">
                <input id="toquality" v-model="acceptQuality" type="checkbox" class="">
                <label for="toquality" class="cursor-pointer text-xs md:text-sm text-gray-600">I have uploaded my best photos and understand these will influence the final result.</label>
              </div>
              <template v-if="acceptTos && acceptQuality && approvedPhotoPaths.length >= minimumPhotos">
                <div class="mb-4 flex items-center justify-center text-sm text-yellow-700 bg-orange-500/5 border border-yellow-500/10 rounded p-2 animate-pulse">
                  <template v-if="approvedPhotoPaths.length >= minimumPhotos">
                    <IconExclamation class="mr-1 h-3 w-3 text-yellow-500 hidden md:flex" />
                    No changes can be made after submitting.
                  </template>
                  <!-- <template v-else>
                    <IconExclamation class="mr-1 h-3 w-3 text-yellow-500 hidden md:flex" />
                    Warning: To ensure the best quality, please upload more than 20 photos.
                  </template> -->
                </div>
              </template>
              <div class="flex  bg-gray-50 p-2 rounded-md items-center justify-between space-x-2">
                <ButtonWhite @click="$emit('returnTwice')">
                  <IconSmallArrow class="w-4 h-4 mr-1.5 transform rotate-180" />
                  <span class="flex-shrink-0 hidden md:flex">Change photos</span>
                  <span class="flex-shrink-0 flex md:hidden">Back</span>
                </ButtonWhite>
                <LoadingWrapper v-if="approvedPhotos.length >= minimumPhotos && acceptTos && acceptQuality && approvedPhotos?.length <= maximumPhotos" :is-loading="isSaving">
                  <ButtonPrimary @click="submit">
                    <span class="hidden md:flex">Submit to create your headshots</span>
                    <span class="flex md:hidden">Submit</span>
                    <IconChevron class="w-4 h-4 text-white ml-2" />
                  </ButtonPrimary>
                </LoadingWrapper>
                <ButtonDisabled v-else class="w-full">
                  <span v-if="approvedPhotos?.length < minimumPhotos">Upload atleast {{ minimumPhotos }} photos</span>
                  <span v-else-if="approvedPhotos?.length > maximumPhotos">Please remove {{ approvedPhotos.length - maximumPhotos }} photos</span>
                  <span v-else-if="!acceptTos || !acceptQuality">Accept terms and conditions first</span>
                  <span v-else-if="!selectedSex">Please select a gender</span>
                </ButtonDisabled>
              </div>
            </div>

            <Popup v-if="showRequirementsModal" size="lg" @closeModal="showRequirementsModal = false">
              <img src="@/assets/img/good-bad-example.png" class="w-full">
              <img src="@/assets/img/usage-tips.jpg">
            </Popup>
          </template>
        </template>
      </div>
    </Card>
    <Popup v-if="showCreditWarning" @closeModal="showCreditWarning = false">
      <div class="space-y-2">
        <h2 class="text-lg font-bold">
          Not enough credits
        </h2>
        <p class="text-gray-700">
          Your team has run out of credits. Please request your team owner to purchase more to continue.
        </p>
        <ButtonDark size="sm" @click="showCreditWarning = false">
          Close
        </ButtonDark>
      </div>
    </Popup>
  </div>
</template>

<script>
import imageMixin from '@/utils/image'
export default {
  mixins: [imageMixin],
  data () {
    return {
      acceptTos: false,
      acceptQuality: false,
      showRequirementsModal: false,
      isSaving: false,
      isSubmitted: false,
      maximumPhotos: 17,
      isLoading: false,
      photoRatings: [],
      showCreditWarning: false
    }
  },
  computed: {
    trainingId: {
      get () {
        return this.$store.state?.onboarding?.trainingId || null
      },
      set (value) {
        this.$store.commit('onboarding/SET_TRAINING_ID', value)
      }
    },
    env () {
      return process.env.NODE_ENV
    },
    getLowestScores () {
      // get lowest 3 scores
      if (this.maximumPhotos - this.approvedPhotoPaths.length > 0) { return [] }
      const photoRatings = [...this.photoRatings]
      return photoRatings.sort((a, b) => a - b).slice(0, (this.approvedPhotoPaths.length - this.maximumPhotos))
    },
    minimumPhotos () {
      const { env } = this
      if (env === 'development') { return 5 }
      if (this.checkIfUserIsInTestId('12-minimum-photos')) { return 17 }
      return this.$store.state.onboarding.minimumPhotos
    },
    declinedPhotos: {
      get () {
        return this.$store.state?.onboarding?.photos.declined || []
      },
      set (value) {
        this.$store.commit('onboarding/SET_DECLINED_PHOTOS', value)
      }
    },
    // sortedApprovedPhotos () {
    //   const photos = [...this.$store.state.onboarding.photos.approved]
    //   if (this.photoRatings && this.photoRatings.length === photos.length) {
    //     return photos.sort((a, b) => {
    //       return this.photoRatings[photos.indexOf(a)] - this.photoRatings[photos.indexOf(b)]
    //     })
    //   }
    //   return photos
    // },
    approvedPhotos: {
      get () {
        return this.$store.state.onboarding.photos.approved
      },
      set (value) {
        this.$store.commit('onboarding/SET_APPROVED_PHOTOS', value)
      }
    },
    approvedPhotoPaths: {
      get () {
        return this.$store.state.onboarding.approvedPhotoPaths
      },
      set (value) {
        this.$store.commit('onboarding/SET_APPROVED_PHOTO_PATHS', value)
      }
    },
    title () { return this.$store.state.onboarding.title },
    selectedEyeColor () { return this.$store.state.onboarding.selectedEyeColor },
    selectedAge () { return this.$store.state.onboarding.selectedAge },
    selectedSex () { return this.$store.state.onboarding.selectedSex },
    style () {
      if (this.$store.state?.user?.role === 'User') {
        return this.$store.state?.onboarding?.selectedStyles || null
      } else if ((this.$store.state?.user?.role === 'TeamMember' || this.$store.state?.user?.role === 'TeamLead') && this.$store.state?.onboarding?.selectedStyles) {
        return this.$store.state?.onboarding?.selectedStyles || null
      } else {
        return this.$store.state.organization?.organization?.style || this.$store.state.organization?.organization?.allowedOptions?.styles || null
      }
    },
    ethnicity () { return this.$store.state.onboarding.ethnicity }
  },
  methods: {
    handleSuccess () {
      this.isSubmitted = true
      this.$store.commit('onboarding/SET_ALL_STYLES', [])
      this.$store.commit('user/SET_RETRY', false)
      this.$store.commit('onboarding/CLEAR_SELECTED_STYLE')
      this.$store.commit('user/DEDUCT_PACKAGE')
      this.$toast.success('Model created successfully')
      localStorage.removeItem('approvedPhotoPaths')
      setTimeout(() => {
        this.$router.push('/app')
      }, 3000)
    },
    removeFile (index) {
      if (this.approvedPhotoPaths.length <= this.minimumPhotos) {
        this.$toast.error(`This would bring you under the required ${this.minimumPhotos} photos. Go to the previous step to upload more photos first.`)
        return
      }
      this.approvedPhotos = this.approvedPhotos.filter((_, i) => {
        return i !== index
      })
      this.approvedPhotoPaths = this.approvedPhotoPaths.filter((_, i) => i !== index)
      this.photoRatings = this.photoRatings.filter((_, i) => i !== index)

      this.handleFileDeletion({
        trainingId: this.trainingId,
        image: this.approvedPhotos[index]
      })

      localStorage.setItem('approvedPhotoPaths', JSON.stringify(this.approvedPhotoPaths))
      if (this.approvedPhotoPaths.length < this.minimumPhotos) {
        this.$emit('return')
      }
    },
    handleFileDeletion ({ trainingId, image }) {
      this.$axios.$delete('/image', {
        data: {
          trainingId,
          image
        }
      }).then((data) => {
        console.log(data)
      }).catch((err) => {
        console.log(err)
      })
    },
    submit () {
      try {
        if (this.isSaving) {
          this.$toast.error('Please wait for the previous request to complete')
          return
        }
        this.isSaving = true
        if (!this.title || this.title?.length === 0) {
          throw new Error('Please fill in a name')
        }
        if (!this.selectedSex) {
          throw new Error('Please select a gender')
        }
        if (!this.selectedAge) {
          throw new Error('Please select your age')
        }
        if (!this.selectedEyeColor) {
          throw new Error('Please select an eye color')
        }
        if (!this.acceptTos || !this.acceptQuality) {
          throw new Error('Please accept the terms of service')
        }
        if (!this.$store.state.user.isAdmin) {
          if (this.approvedPhotoPaths.length < this.minimumPhotos) {
            throw new Error(`Please upload at least ${this.minimumPhotos} photos`)
          }
        }

        if (!this.style || this.style.length === 0) {
          this.$emit('returnTwice')
          throw new Error('Please select a style')
        }

        const body = {
          title: this.title,
          gender: this.selectedSex,
          trainingImages: this.approvedPhotoPaths,
          ethnicity: this.ethnicity || '',
          style: this.style,
          eyeColor: this.selectedEyeColor,
          average: this.average || 0,
          age: this.selectedAge
        }

        this.$axios.$post('/model', body)
          .then((response) => {
            const { success, errorMessage } = response
            if (!success) {
              this.handleError(errorMessage)
              this.isSaving = false
            } else {
              this.$posthog.capture('$post_checkout:model_submitted')
              this.handleSuccess()
              this.isSaving = false
            }
          })
          .catch((err) => {
            if (err?.response?.data?.errorMessage === 'Not enough credits') {
              this.showCreditWarning = true
            }
            this.handleError(err?.response?.data?.errorMessage || err)
            this.isSaving = false
          })
      } catch (err) {
        this.handleError(err)
        this.isSaving = false
      }
    }
  }
}
</script>

<style></style>
