<template>
  <Popup size="xl" @closeModal="$emit('closeModal')">
    <div class="w-full p-4 text-center">
      <h2 class="mb-2 text-2xl font-bold">
        ✨ Share to unlock extra headshots ✨
      </h2>
      <p>Share the link below and we'll create 15 bonus headshots for you.</p>

      <div class="mt-4 px-8">
        <template v-if="!hasTweeted">
          <div class="flex flex-col items-center justify-center space-y-2">
            <a :href="shareLink" target="_blank" class="w-full">
              <ButtonPrimary class="w-full bg-[#1D9BF0] hover:bg-gray-900" @click="clickedButton">
                <img src="@/assets/img/icons8-twitter.svg" class="mr-1.5 h-4 w-4">
                <span>Share on Twitter</span>
              </ButtonPrimary>
            </a>
            <!-- <a target="_blank" href="instagram://user?username=profilepictureai" class="w-full">
              <ButtonPrimary class="w-full bg-[#EA0179] hover:bg-gray-900" @click="clickedButton">
                <img src="@/assets/img/icons8-instagram.svg" class="mr-1.5 h-4 w-4">
                <span>Share on Instagram</span>
              </ButtonPrimary>
            </a> -->
            <!-- <a target="_blank" href="https://www.tiktok.com/@profilepicture.ai" class="w-full">
              <ButtonPrimary class="w-full bg-[#000] hover:bg-gray-900" @click="clickedButton">
                <img src="@/assets/img/icons8-tiktok.svg" class="mr-1.5 h-4 w-4">
                <span>Share on TikTok</span>
              </ButtonPrimary>
            </a> -->
          </div>
          <!-- <div class="block relative bg-gray-100 p-2 rounded-md space-y-2 mt-4">
            <a :href="shareImage" download="profilepicture.ai.png">
              <ButtonPrimary size="sm" class="mx-auto">

                <span>💾 Download image for sharing</span>
              </ButtonPrimary>
            </a>
            <img v-if="shareImage" :src="shareImage">
            <div v-else class="w-[300px] h-[300px] bg-gray-100 animate-pulse transition mx-auto flex items-center justify-center">
              <span class="text-gray-400 text-sm">Creating image...</span>
            </div>
          </div> -->
        </template>
        <template v-else>
          <LoadingSpinner v-if="processing" title="Processing..." />
          <ButtonGradient v-else class="mx-auto" @click="claimReward">
            Shared? Claim your reward.
          </ButtonGradient>
        </template>
      </div>
    </div>
  </Popup>
</template>

<script>
export default {
  props: {
    images: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      hasTweeted: false,
      processing: true,
      shareImage: null
    }
  },
  computed: {
    shareLink () {
      // const uid = this.$store.state.user.uid
      return 'https://twitter.com/intent/tweet?url=https://www.headshotpro.com&text=I%20just%20used%20headshotpro.com%20to%20make%20some%20professional%20headshots%20with%20AI.'
    }
  },
  // created () {
  //   this.generateShareImage()
  // },
  methods: {
    generateShareImage () {
      const urls = this.images

      const images = urls.flat()
      // Create a canvas. Add images to canvas. Convert canvas to image.
      const canvas = document.createElement('canvas')
      canvas.width = 1080
      canvas.height = 1080
      const ctx = canvas.getContext('2d')

      // Add images to canvas
      images.forEach((image, index) => {
        const img = new Image()
        img.src = image
        img.crossOrigin = 'Anonymous'
        img.onload = () => {
          // Draw images in a row, 3 per row
          const x = (index % 3) * 360
          const y = Math.floor(index / 3) * 360

          ctx.drawImage(img, x, y, 360, 360)
          // ctx.drawImage(img, 0, 0, 1200, 630)
          // When done
          if (index === images.length - 1) {
            this.convertCanvasToImage(canvas)
          }
        }
      })

      // Convert canvas to image
    },

    convertCanvasToImage (canvas) {
      // Add text to the canvas in the bottom
      const ctx = canvas.getContext('2d')
      ctx.font = 'bold 40px Arial'
      ctx.fillStyle = 'white'
      ctx.textAlign = 'center'
      // Make background behind text black
      ctx.fillStyle = 'black'
      ctx.fillRect(0, canvas.height - 100, canvas.width, 100)
      // Add text
      ctx.fillStyle = 'white'
      ctx.fillText('www.profilepicture.ai', canvas.width / 2, canvas.height - 40)

      const img = new Image()
      img.src = canvas.toDataURL('image/png')
      img.onload = () => {
        // Get base64 image
        this.shareImage = img.src
      }
    },
    clickedButton () {
      this.hasTweeted = true
      setTimeout(() => {
        this.processing = false
      }, 10000)
    },
    async claimReward () {
      try {
        this.processing = true
        const { success, errorMessage } = await this.$axios.$post('/model/reward', {
          modelId: this.$route.params.id
        })
        if (!success) {
          this.processing = false
          throw new Error(errorMessage)
        }
        this.$toast.success('Thanks for sharing! We will process your reward right away. Come back in 60 minutes')
        this.$emit('closeModal')
        this.$emit('shared')
        this.processing = false
      } catch (err) {
        this.handleError(err)
      }
    }
  }
}
</script>

<style></style>
