<template>
  <Card :key="item._id" class="border">
    <div class="space-y-4 p-4">
      <div class="relative flex w-full items-center justify-start space-x-2">
        <ImageDns :src="item.tweet.user.avatar" class="h-10 w-10 rounded-full" />
        <div class="flex flex-col items-start justify-start">
          <span class="text-base font-bold">{{ item.tweet.user.name }}</span>
          <span class="text-sm font-normal text-gray-700">{{ item.tweet.user.username }}</span>
        </div>
        <div class="absolute top-0 right-0">
          <a :href="item.tweet.url" target="_blank" class="cursor-pointer">
            <svg class="h-5 w-5 text-[#1DA1F2]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19.633 7.997c.013.175.013.349.013.523 0 5.325-4.053 11.461-11.46 11.461-2.282 0-4.402-.661-6.186-1.809.324.037.636.05.973.05a8.07 8.07 0 0 0 5.001-1.721 4.036 4.036 0 0 1-3.767-2.793c.249.037.499.062.761.062.361 0 .724-.05 1.061-.137a4.027 4.027 0 0 1-3.23-3.953v-.05c.537.299 1.16.486 1.82.511a4.022 4.022 0 0 1-1.796-3.354c0-.748.199-1.434.548-2.032a11.457 11.457 0 0 0 8.306 4.215c-.062-.3-.1-.611-.1-.923a4.026 4.026 0 0 1 4.028-4.028c1.16 0 2.207.486 2.943 1.272a7.957 7.957 0 0 0 2.556-.973 4.02 4.02 0 0 1-1.771 2.22 8.073 8.073 0 0 0 2.319-.624 8.645 8.645 0 0 1-2.019 2.083z" />
            </svg>
          </a>
        </div>
      </div>
      <div>
        <p class="font-system text-sm text-gray-700" v-html="tweetText" />
        <!-- {{ item.tweet.text }} -->
        <!-- </p> -->
      </div>
      <ImageDns v-if="item.tweet?.images?.length > 0 && item.tweet?.images[0]" :key="item.tweet.images[0]" :src="item.tweet?.images[0]" class="w-full rounded-md object-cover" @load="refreshMasonry" />
    </div>
  </Card>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  computed: {
    tweetText () {
      const tweet = this.item.tweet.text
        // remove hyperlink if it's present at the end of the tweet (similar to how Twitter does it)
        .replace(/https?:\/\/\S+$/, () => {
          return ''
        })
        // format all hyperlinks
        .replace(/https?:\/\/\S+(?=\s)/g, (match) => {
          return `<a style="color: rgb(29,161,242); font-weight:normal; text-decoration: none" href="${match}" target="_blank">${match.replace(/^https?:\/\//i, '').replace(/\/+$/, '')}</a>`
        })
        // if @ mention is at the front of the tweet, remove it completely,
        .replace(/^(@\w+\s+)+/, () => {
          return ''
        })
        .replace(/\B@([\w-]+)/gim, (match) => {
          // format all @ mentions
          return `<a style="color: rgb(29,161,242); font-weight:normal; text-decoration: none" href="https://twitter.com/${match.replace('@', '')}" target="_blank">${match}</a>`
        })
        .replace(/(#+[a-zA-Z0-9(_)]{1,})/g, (match) => {
          // format all # hashtags
          return `<a style="color: rgb(29,161,242); font-weight:normal; text-decoration: none" href="https://twitter.com/hashtag/${match.replace('#', '')}" target="_blank">${match}</a>`
        })

      return tweet
    }
  }
}
</script>

<style></style>
