<template>
  <div class="group relative cursor-pointer" @click="$emit('click')">
    <div
      class="relative w-full overflow-hidden rounded-lg hover:scale-105 transition-all duration-300"
      :class="{
        'ring-2 ring-brand-200 ring-offset-2 ring-offset-gray-100': isSelected
      }"
    >
      <div v-if="index < 5 && !isTeamMember" class="gradient-bg rounded-lg px-1.5 py-0.5 text-[9px] font-bold uppercase text-white absolute top-2 left-2 z-20">
        MOST POPULAR
      </div>
      <div class="bg-black/70 hidden group-hover:flex absolute inset-0 rounded-lg items-center justify-center">
        <span class="text-xs text-white">
          Click to select
        </span>
      </div>
      <ImageDns v-if="image" :src="image" class="h-28 w-full object-cover" />
      <div v-else-if="allImages?.male || allImages?.female">
        <div class="grid grid-cols-2">
          <ImageDns v-if="allImages?.male" :src="allImages.male" class="h-28 w-full object-cover" />
          <ImageDns v-if="allImages?.female" :src="allImages.female" class="h-28 w-full object-cover" />
        </div>
      </div>
      <div v-else class="h-28 w-full bg-gray-200" />
      <button
        type="button"
        class="absolute inset-0 focus:outline-none group-hover:bg-white/30"
        @click="$emit('click')"
      />
      <div
        v-if="isSelected"
        class="absolute bottom-0 w-full rounded-b-lg bg-black/50 p-1 text-center text-sm text-gray-600 backdrop-blur"
      >
        <p class="text-white">
          Selected
        </p>
      </div>
    </div>
    <p
      class="pointer-events-none mt-2 block truncate text-sm font-medium text-gray-900"
    >
      {{ title }}
    </p>
  </div>
</template>

<script>
export default {
  props: {
    index: {
      type: Number,
      default: 0
    },
    allImages: {
      type: Object,
      default: () => {}
    },
    image: {
      type: String,
      default: ''
    },
    maleImage: {
      type: String,
      default: ''
    },
    femaleImage: {
      type: String,
      default: ''
    },
    isSelected: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    }
  }
}
</script>

<style></style>
