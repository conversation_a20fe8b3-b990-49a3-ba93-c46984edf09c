<template>
  <div
    :class="{
      'gap-8 lg:flex-row': !card,
    }"
    class="rounded-white relative flex flex-col gap-4 rounded-lg border p-4 border-gray-200 bg-gray-50"
  >
    <!-- <div v-if="index < 3" class="gradient-bg rounded-lg px-1.5 py-0.5 text-[9px] font-bold uppercase text-white absolute top-2 left-2 z-20">
      MOST POPULAR
    </div> -->
    <div class="relative w-full lg:w-56 lg:shrink-0">
      <div class="flex w-full">
        <!-- <ImageDns :src="styleItem?.image?.maleImage || ''" class="w-1/2 md:w-28 rounded-l-md" /> -->
        <!-- <ImageDns :src="styleItem?.image?.maleImage || ''" class="w-1/2 md:w-28 rounded-l-md" /> -->
        <ImageDns :src="styleItem?.thumbnail || ''" class="w-full aspact-square rounded-r-md" />
      </div>
    </div>
    <div :class="card ? 'flex flex-1 flex-col' : ''">
      <div :class="card ? 'flex-1' : ''" class="group relative flex h-full max-w-xl flex-col justify-between space-y-2">
        <div :class="{ 'flex-1': !card }">
          <h3 class="text-base font-bold leading-6 text-gray-900">
            {{ styleItem.title || "" }}
          </h3>
          <p v-if="styleItem.description && styleItem.description !== 'null'" class="mt-1 text-sm leading-6 text-gray-700">
            {{ styleItem.description || "" }}
          </p>
        </div>
        <div :class="{ 'mt-0': !card }" class="mt-4 flex w-full items-end space-x-4">
          <!-- <InputSelect v-model="selectedClothing" :options="clothing.map((item) => item.type)" class="flex-1" label="What clothing to wear:" /> -->
          <StyleClothingSelector :clothing="clothing" @select="setClothing" />
        </div>
        <!-- <ButtonPrimary v-if="selectedClothing" size="sm" @click="select">
          <IconPlus class="w-3 h-3 text-white/50 mr-1" />
          <span>Select this option</span>
        </ButtonPrimary> -->
        <!-- <ButtonDisabled v-else size="sm">
          <IconChevron class="w-3 h-3 text-black/30 mr-1 transform -rotate-90" />
          <span>Pick clothing first</span>
          <IconChevron class="w-3 h-3 text-black/30 ml-1 transform -rotate-90" />
        </ButtonDisabled> -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    styleItem: {
      type: Object,
      required: true
    },
    clothing: {
      type: Array,
      required: false,
      default: () => []
    },
    index: {
      type: Number,
      required: false,
      default: 0
    },
    card: {
      type: Boolean,
      required: false,
      default: false
    },
    selectedStyles: {
      type: Array,
      required: true,
      default: () => []
    },
    teamMember: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  data () {
    return {
      selectedClothing: null
    }
  },
  computed: {
    selectedStyleIds () {
      return this.selectedStyles.map(item => item.style)
    }
  },
  methods: {
    select () {
      if (!this.selectedClothing || this.selectedClothing === '') {
        return this.$toast.warning('Please select a clothing type')
      }
      const clothing = this.clothing.find(item => item.type === this.selectedClothing)
      const found = this.selectedStyles.find(item => item.clothing === clothing._id && item.style === this.styleItem._id)
      if (found) {
        return this.$toast.warning('This style is already selected for this clothing type')
      }
      if (this.teamMember && this.selectedStyleIds.includes(this.styleItem._id)) {
        return this.$toast.error('You cannot select the same style twice')
      }
      this.$emit('select', clothing._id)
    },
    setClothing (clothing) {
      this.selectedClothing = clothing
      const c = this.clothing.find(item => item.type === this.selectedClothing)
      this.$emit('select', c._id)
    }
  }
}
</script>
