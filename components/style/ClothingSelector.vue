<template>
  <div class="w-full">
    <ButtonPrimary
      type="button"
      class="inline-flex w-full items-center gap-x-2 rounded-md  px-2 h-10 text-sm shadow-sm ring-1 ring-inset ring-gray-800"
      @click="modalOpen = true"
    >
      <!-- <span v-if="!selectedClothing" class="i-heroicons-plus-circle text-base" aria-hidden="true" /> -->
      <span class="i-heroicons-plus-circle text-base" aria-hidden="true" />
      <!-- <div v-else class="relative grid grid-cols-2 overflow-hidden rounded-sm">
        <ImageDns
          v-if="selectedClothing?.images[selectedSex]"
          :src="selectedClothing?.images[selectedSex]"
          class="h-6 w-6 object-cover"
        />

        <div v-else-if="selectedClothing?.images?.male || selectedClothing?.images?.female" class="h-6 w-6 bg-gray-200 grid grid-cols-2">
          <ImageDns
            v-if="selectedClothing?.images?.male"
            :src="selectedClothing?.images?.male"
            class="h-6 w-6 object-cover"
          />
          <ImageDns
            v-if="selectedClothing?.images?.female"
            :src="selectedClothing?.images?.female"
            class="h-6 w-6 object-cover"
          />
        </div>
        <div v-else class="h-6 w-6 bg-gray-200" />
      </div> -->
      <!-- <span class="flex-1 min-w-0 truncate">{{ selectedClothing ? `${selectedClothing.type}` : 'Select background' }}</span> -->
      <span class="flex-1 min-w-0 truncate">Select outfit</span>
    </ButtonPrimary>
    <portal to="modal">
      <Popup
        v-if="modalOpen"
        size="3xl"
        @closeModal="modalOpen = false"
      >
        <div class="flex flex-col space-y-4">
          <div class="flex flex-col space-y-1">
            <h2 class="font-bold text-xl">
              Choose your outfit
            </h2>
            <p class="text-sm text-gray-700">
              Pick the clothes you'd like to wear against your selected background.
            </p>
          </div>
          <div v-if="clothingForGender && clothingForGender.length > 0" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 mt-4">
            <StyleClothingButton
              v-for="(item, index) in clothingForGender"
              :key="index"
              :index="index"
              :is-selected="isSelected(item._id)"
              :image="item?.images[selectedSex]"
              :all-images="item.images"
              :title="item.type"
              @click="selectClothes(item)"
            />
          </div>
        </div>
      </Popup>
    </portal>
  </div>
</template>

<script>
export default {
  props: {
    clothing: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      selectedClothing: null,
      modalOpen: false
    }
  },
  computed: {
    selectedSex () {
      return this.$store.state?.onboarding?.selectedSex || null
    },
    clothingForGender () {
      if (!this.selectedSex) { return this.clothing }
      return this.clothing.filter((item) => {
        if (!item?.gender) { return false }
        return item.gender.includes(this.selectedSex)
      })
    }
  },
  methods: {
    selectClothes (item) {
      this.selectedClothing = item
      this.$emit('select', item.type)
      this.modalOpen = false
    },
    isSelected (clothingId) {
      return this.selectedClothing?._id === clothingId
    }
  }
}
</script>

<style></style>
