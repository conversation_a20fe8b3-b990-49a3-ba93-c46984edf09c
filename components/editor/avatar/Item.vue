<template>
  <div ref="wrapper" class="relative w-full group rounded-full overflow-hidden bg-gray-700 aspect-1 cursor-pointer" @click="$emit('click', image)">
    <ImageDns :src="image" />
    <!-- <div :key="image" ref="avatar" class="avatar-holder group rounded-full overflow-hidden flex items-center justify-center w-full h-full relative" :style="{ backgroundColor: backgroundColor, width: `${size}px`, height: `${size}px` }">
      <div class="absolute top-0 left-0 z-10 w-full h-full bg-holder" :style="{ backgroundImage: `url(${image})`}" />
    </div> -->
  </div>
</template>

<script>
export default {
  props: {
    image: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      size: 0
    }
  },
  computed: {
    avatar () {
      return this.$store.state.editor.avatar
    },
    backgroundColor () {
      return this.$store.state.editor.backgroundColor
    },
    grayscale () {
      return this.$store.state.editor.grayscale
    },
    avatarSize () {
      return this.$store.state.editor.avatarSize
    },
    avatarPosition () {
      return this.$store.state.editor.avatarPosition
    },
    wrapper () {
      return this?.$refs?.wrapper
    }
  },
  watch: {
    wrapper (newValue, oldValue) {
      console.log(newValue)
    }
  },
  mounted () {
    const node = this.$refs.wrapper
    // Get width
    const width = node.offsetWidth
    this.size = width
  }

}
</script>

<style scoped>
.avatar-holder {
  background-size: 90%;
  background-position: bottom;
  background-repeat: no-repeat;
}
.bg-holder {
  background-size: 100%;
  background-position: center;
  background-repeat: no-repeat;
}
</style>
