<template>
  <section class="bg-[#F8FCFF] py-12 sm:py-16 lg:py-20 xl:py-24">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="mx-auto flex max-w-5xl flex-col justify-center gap-12 lg:items-stretch">
        <div class="flex flex-col lg:max-w-lg text-center mx-auto">
          <div class="flex-1">
            <h2 class="text-3xl font-bold text-primary-500 sm:text-4xl">
              Frequently Asked Questions
            </h2>
            <p class="mt-4 text-base font-normal text-gray-500">
              {{ subtitle }}
            </p>
          </div>
        </div>

        <div class="w-full grid grid-cols-2 gap-8 ">
          <div v-for="(item, index) in items" :key="`faq-${index}`" role="region" class="border-b border-gray-200  w-full pb-4">
            <h4>
              <button class="flex w-full items-center justify-between text-left text-base font-bold text-gray-900" @click="active = index">
                <span>{{ item.question }}</span>
                <span v-if="active === index" aria-hidden="true" class="ml-4">
                  <svg class="h-5 w-5 text-gray-900" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </span>
                <span v-else aria-hidden="true" class="ml-4">
                  <svg class="h-5 w-5 text-gray-900" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                  </svg>
                </span>
              </button>
            </h4>
            <collapse-transition>
              <div v-if="active === index">
                <div class="space-y-5 pt-4">
                  <p class="text-base font-normal text-gray-900" v-html="item.answer" />
                </div>
              </div>
            </collapse-transition>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import { CollapseTransition } from '@ivanv/vue-collapse-transition'
export default {
  components: {
    CollapseTransition
  },
  props: {
    items: {
      type: Array,
      required: false
    },
    subtitle: {
      type: String,
      required: false,
      default: 'Answers to common questions about our free AI generated headshot maker.'
    }
  },
  data () {
    return {
      active: null
    }
  }
}
</script>

<style></style>
