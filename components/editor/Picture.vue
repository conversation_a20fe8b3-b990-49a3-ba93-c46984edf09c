<template>
  <Card class="h-full">
    <div class="p-4 h-full flex justify-between flex-col">
      <template v-if="!pfp?.src">
        <EditorSelectImage />
      </template>
      <template v-else>
        <div class="p-4 bg-gray-100 rounded-md">
          <LoadingWrapper :is-loading="isRemovingBackground" title="Removing background. Takes about 5-60 seconds.">
            <div ref="wrapper" class="relative flex items-center justify-center w-full group rounded-full overflow-hidden ">
              <div
                ref="avatar"
                class="avatar-holder group rounded-full overflow-hidden flex items-center justify-center  relative"
                :style="`width: ${avatarSize}px; height: ${avatarSize}px;`"
              >
                <div :style="`background-image:url(${$store.state.editor.pfp.backgroundSrc}); background-color:${$store.state.editor.pfp.backgroundColor}; background-size:100%` " class="w-full absolute top-0 left-0 h-full z-0 rounded-full object-cover" />
                <div id="layer-text" class="absolute z-30" :style="`pointer-events: none; transform: rotate(${pfp?.textRotation}deg); height: ${avatarSize}px; width: ${avatarSize}px;`">
                  <svg id="svgText" xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 100 100">
                    <path id="textPath" :d="textPath" stroke="none" fill="none" transform="rotate(90 50 50)" />
                    <text id="theCircularText" :fill="pfp?.textColor" font-size="12" font-family="arial" :style="`font-size: ${pfp?.textSize}px; letter-spacing: ${pfp?.textLetterSpacing}px`">
                      <textPath xlink:href="#textPath">{{ pfp.borderText }}</textPath>
                    </text>

                  </svg>
                </div>
                <div class="absolute w-full h-full z-10 rounded-full" :style="holderStyle">
                  <svg id="svgBorder" xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 100 100">
                    <defs>
                      <linearGradient id="MyGradient">
                        <stop offset="0%" :stop-color="pfp.borderColor.from" />
                        <stop offset="100%" :stop-color="pfp.borderColor.to" />
                      </linearGradient>
                    </defs>
                    <path id="p" fill="url(#MyGradient)" stroke="none" :d="borderPath" /> <!--d="M50 50m-40,0a40,40,0 1,0 80,0a 40,40 0 1,0 -80,0zM50 50m-38,0a38,38,0 0,1 76,0a 38,38 0 0,1 -76,0z"-->
                    <pattern id="img1" patternUnits="userSpaceOnUse" width="100" height="100">
                      <image
                        id="imgBorder"
                        href="https://s3-us-west-2.amazonaws.com/s.cdpn.io/222579/butterfly.jpg"
                        x="0"
                        y="0"
                        height="100"
                        width="100"
                      />
                    </pattern>
                  </svg>
                </div>
                <div
                  class="relative rounded-full w-full h-full avatar-holder z-0"
                  :style="pictureStyle"
                />
              </div>
            </div>
          </LoadingWrapper>
        </div>
        <div class="rounded-t-md bg-gray-100 p-2 mt-6">
          <LoadingWrapper v-if="inline" :is-loading="isLoading">
            <ButtonPrimary class="animation animation-pulse" size="sm" @click="confirm">
              <span>Use headshot</span>
            </ButtonPrimary>
          </LoadingWrapper>
          <LoadingWrapper v-else :is-loading="isDownloading" title="Downloading...">
            <div class="flex items-center justify-between space-x-2">
              <ButtonPrimary class="animation animation-pulse" size="sm" @click="download">
                <IconSolidDownload class="w-4 h-4 text-white mr-1.5" />
                <span>Download headshot</span>
              </ButtonPrimary>
              <ButtonDelete @click="reset">
                Reset
              </ButtonDelete>
            </div>
          </LoadingWrapper>
        </div>
      </template>
    </div>
    <portal to="modal">
      <Popup v-if="showUpsellModal" size="4xl" @closeModal="showUpsellModal = false">
        <div class="p-4 md:p-8 md:px-16 max-w-4xl">
          <div class="mx-auto text-center">
            <h2 class="text-sm md:text-lg font-bold text-teal-500">
              Looking for a professional headshot?
            </h2>
            <p class="mt-6 font-medium tracking-tight text-2xl md:text-3xl text-primary-500">
              Get your professional headshot, <br>without a physical photo shoot
            </p>
            <p class="mt-4 text-base font-normal text-gray-600">
              <strong>{{ $store.state.stats.photos }}</strong> AI headshots already created for <strong>{{ $store.state.stats.users }}</strong> happy customers!
            </p>
          </div>
          <div class="flex items-center justify-center py-4">
            <a target="_blank" title="HeadshotPro" href="https://www.headshotpro.com?ref=avatar-generator">
              <ButtonPrimary>
                Get your headshots now
              </ButtonPrimary>
            </a>
          </div>
          <div class="flex items-center justify-center w-full space-x-2 text-sm animate-pulse my-4">
            <IconChevron class="w-4 h-4 text-black/50 transform rotate-90" />
            <span>All of these photos are 100% AI generated.</span>
            <IconChevron class="w-4 h-4 text-black/50 transform rotate-90" />
          </div>
          <div class="grid grid-cols-4 gap-1 mt-4">
            <ImageDns v-for="item of reviews.slice(0, 8)" :key="item._id" :src="item.image" />
          </div>
        </div>
      </Popup>
      <Popup v-if="showEmailSignatureModal" size="4xl" @closeModal="showEmailSignatureModal = false">
        <MarketingEmailSignatureModal :photo="generatedImageSrc" @close="showEmailSignatureModal = false" />
      </Popup>
    </portal>
  </Card>
</template>

<script>
import domtoimage from 'dom-to-image'
export default {
  props: {
    inline: {
      type: Boolean,
      default: false
    },
    showsModal: {
      type: String,
      default: 'upsell'
    }
  },
  data () {
    return {
      showUpsellModal: false,
      showEmailSignatureModal: false,
      avatarSize: 400,
      isDownloading: false,
      isLoading: false,
      generatedImageSrc: null
    }
  },
  computed: {
    reviews () {
      return this.$store.state.reviews.filter(review => this.isCreatedAfterAugust21(review.createdAt))
    },
    isRemovingBackground () {
      return this.$store.state.editor.isRemovingBackground
    },
    pfp () {
      return this.$store.state.editor.pfp
    },
    holderStyle () {
      return {
        opacity: this.pfp?.borderOpacity / 100 || 100,
        transform: `rotate(${this.pfp?.borderGradientRotation}deg)`
        // 'border-color': this.pfp?.borderColor,
      }
    },
    pictureStyle () {
    //   const grayFilter = (this.pfp?.grayscale) ? 'grayscale(100%)' : ''
      const rotateFilter = 'rotate(' + parseInt(this.pfp?.imageRotation) + 'deg)'
      return {
        backgroundImage: `url(${this.pfp.src})`,
        transform: rotateFilter,
        'background-size': this.pfp?.imageZoom + '%',
        'background-position': this.pfp?.imageOffsetLeft + '% ' + this.pfp?.imageOffsetTop + '%'
      }
    },

    borderPath () {
      const radius = 50 - this.pfp.borderOffset // Calculate the radius based on the thickness
      const width = radius - this.pfp.borderWidth // Calculate the radius based on the thickness
      const whole = `M50 50m-${radius},0a${radius},${radius},0 1,0 ${radius * 2},0a ${radius},${radius} 0 1,0 -${radius * 2},0zM50 `
      const inner = `50m-${width},0a${width},${width},0 0,1 ${width * 2},0a ${width},${width} 0 0,1 -${width * 2},0z` // '50m-7,0a7,7,0 0,1 14,0a 7,7 0 0,1 -14,0z'
      return whole + inner
    },
    textPath () {
      // M30, 50a20, 20 0 1, 0 40, 0a20, 20 0 1, 0 -40, 0
    //   M0, 50a50, 50 0 1, 0 100, 0a50, 50 0 1, 0 -100, 0
    //   M${offset}, 50a50, 50 0 1, 0 100, 0a50, 50 0 1, 0 -100, 0
      const offset = 30 - this.pfp.textOffset // Calculate the radius based on the thickness
      return `M${offset}, 50a${50 - offset}, ${50 - offset} 0 1, 0 ${100 - offset * 2}, 0a${50 - offset}, ${50 - offset} 0 1, 0 -${100 - offset * 2}, 0`
    }
  },
  mounted () {
    // this.avatarSize = this.$refs?.avatar?.getBoundingClientRect()?.width || 400
    const windowWidth = window.innerWidth

    if (windowWidth >= 1024) {
      this.avatarSize = 400
    } else {
      this.avatarSize = 200
    }
  },
  methods: {
    reset () {
      this.$store.dispatch('editor/resetSettings')
    },
    isCreatedAfterAugust21 (createdAt) {
      const date = new Date(createdAt)
      const august21 = new Date('2024-08-19')
      return date > august21
    },
    async download () {
      try {
        this.generatedImageSrc = null
        this.isDownloading = true
        const { blob, newElement } = await this.generateImage()
        this.generatedImageSrc = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
        link.download = `HeadshotPro Avatar ${timestamp}.png`
        document.body.appendChild(link)
        link.click()
        this.isDownloading = false
        document.body.removeChild(link)
        // Remove newElement from DOM
        document.body.removeChild(newElement)
        this.$toast.success('Headshot downloaded, make sure to accept the download in your browser.')
        setTimeout(() => {
          if (!this.$route.query.photo) {
            if (this.showsModal === 'upsell') {
              this.showUpsellModal = true
            } else if (this.showsModal === 'email') {
              this.showUpsellModal = true
            }
          }
        }, 1000)
      } catch (err) {
        this.isDownloading = false
      }
    },
    async confirm () {
      if (this.isLoading) {
        return
      }

      this.isLoading = true
      const { blob, newElement } = await this.generateImage()
      const formData = new FormData()
      formData.append('files', blob)
      formData.append('folder', 'email')
      formData.append('requiresFullUrl', '1')
      formData.append('public', '1')
      formData.append('maxWidth', '512')

      try {
        const response = await this.$axios.post('/image/upload', formData)
        if (response.data.success) {
          if (!response.data.data?.url) {
            throw new Error('Unknown error')
          }

          this.$store.commit('editor/UPDATE_PFP', { key: 'result', value: response.data.data.url })
          this.$emit('confirm')
          return
        }

        if (response.data.errorMessage) {
          throw new Error(response.data.errorMessage)
        }

        throw new Error('Unknown error')
      } catch (err) {
        console.error(err)
        this.$toast.error('An error occurred while uploading the image. Please try again.')
      } finally {
        document.body.removeChild(newElement)
        this.isLoading = false
      }
    },
    async generateImage () {
      // Create a PNG from all content inside the .avatar-holder item and download it
      const node = this.$refs.avatar
      // Create a new element that's 2x larger
      const newElement = document.createElement('div')
      newElement.style.width = (this.avatarSize * 2) + 'px'
      newElement.style.height = (this.avatarSize * 2) + 'px'
      newElement.style.transform = 'scale(2)'
      newElement.style.transformOrigin = 'top left'
      newElement.style.display = 'block'
      //   newElement.appendChild(node)
      // Clone node
      const clone = node.cloneNode(true)
      newElement.appendChild(clone)
      clone.style.borderRadius = '0'
      document.body.appendChild(newElement)

      return {
        blob: await domtoimage.toBlob(newElement),
        newElement
      }
    }
  }

}
</script>

<style scoped>
.avatar-holder {
    background-size:100%;
  background-position: top;
  background-repeat: no-repeat;
}
.bg-holder {
  background-size: 100%;
  background-position: center;
  background-repeat: no-repeat;
}
</style>
