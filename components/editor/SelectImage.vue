<template>
  <div class=" h-full relative">
    <div class="gradient-bg opacity-5 absolute z-10 w-full h-full rounded-md" />
    <div class="relative z-20 flex items-center flex-col justify-center h-full p-16">
      <div class="space-y-1 mb-6 text-center">
        <h5 class="font-bold text-2xl">
          Upload a photo to get started
        </h5>
      </div>
      <label for="fileextra">
        <div class="cursor-pointer flex content-center items-center justify-center text-center px-6 py-3 border border-transparent text-base font-bold rounded-md shadow-sm text-white gradient-bg hover:bg-gray-900 hover:border-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-200">
          <IconPlus class="w-4 h-4 text-white mr-1.5" />
          <span>Click here to add your image</span>
        </div>
      </label>
      <input
        id="fileextra"
        ref="fileextra"
        name="fileextra"
        type="file"
        class="hidden"
        accept="image/png, image/jpeg, image/heic, image/heif, image/jpg"
        @change="addFile"
      >
      <div class="flex flex-col items-center justify-center space-x-2 mt-4">
        <p class="py-4 text-xs text-gray-600">
          Or try an example photo:
        </p>
        <div class="flex items-center justify-center  space-x-2 ml-2">
          <button @click="setImage('https://storage.googleapis.com/headshotpro-public-content/headshotpro/images/try-avatar-1.png')">
            <img src="https://storage.googleapis.com/headshotpro-public-content/headshotpro/images/try-avatar-1.png" class="w-10 border border-gray-300 h-10 rounded-full">
          </button>
          <button @click="setImage('https://storage.googleapis.com/headshotpro-public-content/headshotpro/images/try-avatar-2.png')">
            <img src="https://storage.googleapis.com/headshotpro-public-content/headshotpro/images/try-avatar-2.png" class="w-10 border border-gray-300 h-10 rounded-full">
          </button>
        </div>
      </div>
      <div class="flex items-center text-center md:text-left justify-center w-full mt-6">
        <IconShield class="hidden md:flex w-4 h-4 text-green-500 mr-1.5" />
        <span>Your privacy is <strong class="text-green-500">protected</strong>. All image processings are done on your local browser.</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    setImage (image) {
      // const image = require(imag)
      this.$store.commit('editor/UPDATE_PFP', { key: 'src', value: image })
      this.$store.commit('editor/UPDATE_PFP', { key: 'baseSrc', value: image })
      this.$store.commit('editor/UPDATE_PFP', { key: 'transparentSrc', value: image })
      this.$store.commit('editor/UPDATE_PFP', { key: 'backgroundSrc', value: 'https://storage.googleapis.com/headshotpro-public-content/headshotpro/images/backgrounds/png/6.png' })
    },
    async addFile (event, file) {
      try {
        this.isUploading = true
        const files = event?.target?.files
        // Add time out. If function takes longer than 1 second, it will be rejected
        let data = files[0]
        if (data.type === 'image/heic' || data.type === 'image/heif') {
          const heic2any = (await import('heic2any')).default
          data = await heic2any({ blob: data, toType: 'image/jpeg' })
        }
        // Resize image to be maximum of 1024px with a  native function
        // data = await this.resizeImage(data)
        // data = await this.handleImageCompression(data)
        // data to blob
        this.convertFileToBlob(data).then(async (blob) => {
          const base64 = await this.convertBlobToBase64(blob)
          this.$store.commit('editor/UPDATE_PFP', { key: 'src', value: `data:image/png;base64,${base64}` })
          this.$store.commit('editor/UPDATE_PFP', { key: 'baseSrc', value: `data:image/png;base64,${base64}` })
        })
      } catch (err) {
        this.$toast.warning(err.message || 'Something went wrong. Please try another photo.')
        this.isUploading = false
        console.log(err)
      }
    },
    convertFileToBlob (file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()

        reader.onload = () => {
          const arrayBuffer = reader.result
          const blob = new Blob([arrayBuffer], { type: file.type })
          resolve(blob)
        }

        reader.onerror = () => {
          reject(new Error('Failed to convert file to Blob.'))
        }

        reader.readAsArrayBuffer(file)
      })
    },
    convertBlobToBase64 (blob) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()

        reader.onload = () => {
          const base64String = reader.result.split(',')[1]
          resolve(base64String)
        }

        reader.onerror = () => {
          reject(new Error('Failed to convert Blob to base64.'))
        }

        reader.readAsDataURL(blob)
      })
    }

  }

}
</script>

<style>

</style>
