<template>
  <div class="max-w-5xl mx-auto">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <EditorPicture class="shadow-2xl" :class="{'col-span-2':!pfp?.src}" :inline="inline" :shows-modal="showsModal" @confirm="$emit('confirm')" />
      <EditorSettings v-if="pfp?.src" class="shadow-2xl" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    inline: {
      type: Boolean,
      default: false
    },
    showsModal: {
      type: String,
      default: 'upsell'
    }
  },
  computed: {
    pfp () {
      return this.$store.state.editor.pfp
    }
  }

}
</script>

<style>

</style>
