<template>
  <div class="p-4">
    <div class="space-y-4">
      <div class="bg-gray-50 col-span-1 p-2 px-4 flex flex-row items-center justify-start space-x-2 rounded-md border border-gray-200">
        <span class="text-sm text-gray-700 flex-shrink-0">Background color:</span>
        <div class="w-full rounded-m">
          <input type="color" :value="$store.state.editor.pfp.backgroundColor" @input="$store.commit('editor/UPDATE_PFP', { key: 'backgroundColor', value: $event.target.value })">
        </div>
      </div>
      <div ref="avatarList" class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-8">
        <EditorAvatarItem :image="$store.state.editor.pfp.baseSrc" @click="setBackground($event)" />
        <template v-for="item of 15">
          <EditorAvatarItem :key="'png' + item" :image="`https://storage.googleapis.com/headshotpro-public-content/headshotpro/images/backgrounds/png/${item}.png`" @click="setBackground($event)" />
        </template>
        <template v-for="item of 123">
          <EditorAvatarItem :key="'jpeg'+item" :image="`https://storage.googleapis.com/headshotpro-public-content/headshotpro/images/backgrounds/${item}.jpeg`" @click="setBackground($event)" />
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import imageCompression from 'browser-image-compression'
export default {
  data () {
    return {

    }
  },
  computed: {
    transparentSrc: {
      get () {
        return this.$store.state.editor.pfp.transparentSrc
      },
      set (value) {
        this.$store.commit('editor/UPDATE_PFP', { key: 'transparentSrc', value })
      }
    }
  },
  methods: {
    removeBackground (formData) {
      this.$axios.$post('/editor/remove-bg', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
        .then((data) => {
          if (data.success) {
            this.pollReplicate(data.id)
          }
        })
        .catch((err) => {
          console.log(err)
          this.isUploading = false
        })
    },
    pollReplicate (id) {
      this.$axios.$get(`/replicate/poll/${id}`)
        .then((data) => {
          if (data?.response?.status === 'failed') {
            console.log('failed')
            this.$toast.error('Failed to remove background. Please try again later.')
            this.isUploading = false
            this.$store.commit('editor/SET_IS_REMOVING_BACKGROUND', false)
          } else if (data?.response?.status === 'succeeded') {
            this.$store.commit('editor/UPDATE_PFP', { key: 'transparentSrc', value: data.response.output })
            this.$store.commit('editor/UPDATE_PFP', { key: 'src', value: data.response.output })

            this.isUploading = false
            this.$store.commit('editor/SET_IS_REMOVING_BACKGROUND', false)
          } else {
            setTimeout(() => {
              this.pollReplicate(id)
            }, 1000)
          }
        })
        .catch(err => console.log(err))
    },
    async setBackground (image) {
      if (!this.transparentSrc) {
        this.$store.commit('editor/SET_IS_REMOVING_BACKGROUND', true)
        // Check if image is base64
        if (this.$store.state.editor.pfp.src.includes('data:image')) {
          const blob = this.base64ToBlob(this.$store.state.editor.pfp.src.replace('data:image/png;base64,', ''), 'image/png')
          const file = new File([blob], 'image', { type: 'image/png' })
          // Create FileList Object
          const files = new DataTransfer()
          files.items.add(file)
          await this.addFile(files.files)
        } else {
          // Get blob from external image
          const response = await this.$axios.$get(this.$store.state.editor.pfp.src, {
            responseType: 'blob'
          })
          // Convert blob to file
          const file = new File([response], 'image', { type: response.type })
          // Create FileList Object
          const files = new DataTransfer()
          files.items.add(file)

          await this.addFile(files.files)
        }

        // this.removeBackground(this.$store.state.editor.pfp.src)
      }
      this.$store.commit('editor/UPDATE_PFP', { key: 'backgroundSrc', value: image })
      console.log(image)
    },

    base64ToBlob (base64, contentType = '', sliceSize = 512) {
      // Convert base64 to raw binary data held in a string
      const byteCharacters = atob(base64)

      // Extract slices of the byteCharacters
      const byteArrays = []

      for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
        const slice = byteCharacters.slice(offset, offset + sliceSize)

        const byteNumbers = new Array(slice.length)
        for (let i = 0; i < slice.length; i++) {
          byteNumbers[i] = slice.charCodeAt(i)
        }

        const byteArray = new Uint8Array(byteNumbers)

        byteArrays.push(byteArray)
      }

      return new Blob(byteArrays, { type: contentType })
    },

    async addFile (file) {
      try {
        this.isUploading = true
        const files = file
        for (let i = 0; i < files.length; i++) {
          try {
            // Add time out. If function takes longer than 1 second, it will be rejected
            let data = files[i]
            if (files[i].type === 'image/heic' || files[i].type === 'image/heif') {
              const heic2any = (await import('heic2any')).default
              data = await heic2any({ blob: files[i], toType: 'image/jpeg' })
            }
            // Resize image to be maximum of 1024px with a  native function
            data = await this.resizeImage(data)
            // data = await this.handleImageCompression(data)
            const formData = new FormData()
            formData.append('files', data)
            this.removeBackground(formData)
          } catch (err) {
            console.log(err)
          }
        }
      } catch (err) {
        this.$toast.warning(err.message || 'Something went wrong. Please try another photo.')
        this.isUploading = false
        console.log(err)
      }
    },
    async handleImageCompression (file) {
      const imageFile = file
      // console.log('originalFile instanceof Blob', imageFile instanceof Blob) // true
      // console.log(`originalFile size ${imageFile.size / 1024 / 1024} MB`)
      const options = {
        maxSizeMB: 1,
        maxWidthOrHeight: 1024,
        useWebWorker: true
      }
      try {
        const compressedFile = await imageCompression(imageFile, options)
        return compressedFile
      } catch (error) {
        return file
      }
    },
    resizeImage (blob) {
      // Resize image from blob and return resized blob with canvas
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(blob)
        reader.onload = (event) => {
          const img = new Image()
          img.src = event.target.result
          img.onload = () => {
            const elem = document.createElement('canvas')
            const scaleFactor = img.width / 1024
            elem.width = 1024
            elem.height = img.height / scaleFactor
            const ctx = elem.getContext('2d')
            ctx.drawImage(img, 0, 0, elem.width, elem.height)
            ctx.canvas.toBlob((blob) => {
              resolve(blob)
            }, 'image/jpeg', 1)
          }
          img.onerror = (error) => {
            reject(error)
          }
        }
      })
    }

  }

}
</script>

<style>

</style>
