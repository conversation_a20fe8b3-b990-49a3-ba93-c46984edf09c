<template>
  <div class="p-4">
    <div class="space-y-2">
      <InputRange label="Zoom" :min="100" :max="400" :value="pfp?.imageZoom" @input="updatePfp('imageZoom', $event)" />
      <InputRange label="Offset Top" :min="0" :max="100" :value="pfp?.imageOffsetTop" @input="updatePfp('imageOffsetTop', $event)" />
      <InputRange label="Offset Left" :min="0" :max="100" :value="pfp?.imageOffsetLeft" @input="updatePfp('imageOffsetLeft', $event)" />
      <InputRange label="Rotate" :min="-180" :max="180" :value="pfp?.imageRotation" @input="updatePfp('imageRotation', $event)" />
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {

    }
  },
  computed: {
    pfp () {
      return this.$store.state.editor.pfp
    }
  },
  methods: {
    updatePfp (key, value) {
      this.$store.commit('editor/UPDATE_PFP', { key, value })
    }
  }

}
</script>

<style>

</style>
