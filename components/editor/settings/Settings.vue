<template>
  <div class="flex items-start content-start justify-start h-full overflow-hidden bg-white rounded-lg shadow card max-h-[600px] relative ">
    <div class="w-full h-full">
      <Tab class="rounded-t-md">
        <TabItem v-for="tab in tabs" :key="tab.value" class="!px-1" :active="activeTab === tab.value" @click="activeTab = tab.value">
          {{ tab.title }}
        </TabItem>
      </Tab>
      <div class="h-[100%] overflow-hidden overflow-y-scroll">
        <EditorSettingsImage v-if="activeTab === 'position'" />
        <EditorSettingsBorder v-if="activeTab === 'border'" />
        <EditorSettingsText v-if="activeTab === 'text'" />
        <EditorSettingsBackground v-if="activeTab === 'background'" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      tabs: [
        { title: 'Border', value: 'border' },
        { title: 'Position', value: 'position' },
        { title: 'Text', value: 'text' },
        { title: 'Background', value: 'background' }
        // { title: 'Filters', value: 'filters' }
      ],
      activeTab: 'background'
    }
  }

}
</script>

<style>

</style>
