<template>
  <div class="p-4">
    <div class="space-y-2">
      <InputRange label="Thickness" :min="0" :value="pfp?.borderWidth" :max="50" @input="updatePfp('borderWidth', $event)" />
      <InputRange
        label="Offset"
        :min="0"
        :max="50"
        :show-value="true"
        :value="pfp?.borderOffset"
        @input="updatePfp('borderOffset', $event)"
      />
      <InputRange label="Opacity" :min="0" :value="pfp?.borderOpacity" :max="100" @input="updatePfp('borderOpacity', $event)" />
    </div>
    <hr class="w-full my-6 bg-black/50">
    <div class="space-y-2">
      <div class="grid grid-cols-3 gap-4">
        <InputStyledRadioButton class="w-full" :model="colorTab" value="gradient" label="Gradient" @click="colorTab = $event" />
        <InputStyledRadioButton class="w-full" :model="colorTab" value="solid" label="Solid" @click="colorTab = $event" />
        <!-- <InputStyledRadioButton class="w-full" :model="colorTab" value="image" label="Image" @click="colorTab = $event" /> -->
      </div>
      <div class="grid grid-cols-8 gap-2 bg-gray-100 p-2 rounded-md border border-gray-200">
        <template v-for="(color, index) in colorOptions[colorTab]">
          <button :key="index" class="border border-gray-100 w-full h-[32px] rounded-md" :style="`background-color: ${color.from};background-image: -webkit-linear-gradient(45deg, ${color.from} 0%, ${color.to} 100%)`" @click="setColor(color.from, color.to)" />
        </template>
      </div>
      <div class="grid grid-cols-4 gap-4">
        <div class="bg-gray-50 col-span-1 p-2 flex flex-col rounded-md border border-gray-200">
          <span class="text-sm text-gray-700">From:</span>
          <input type="color" class="w-full rounded-md" :value="pfp?.borderColor.from" @input="setColor($event.target.value, pfp?.borderColor.to)">
        </div>
        <div class="bg-gray-50 col-span-1 p-2 flex flex-col rounded-md border border-gray-200">
          <span class="text-sm text-gray-700">To:</span>
          <input type="color" class="w-full rounded-md" :value="pfp?.borderColor.to" @input="setColor(pfp?.borderColor.from, $event.target.value)">
        </div>
        <template v-if="colorTab === 'gradient'">
          <InputRange
            class="col-span-2"
            label="Rotation"
            :min="0"
            :value="pfp?.borderGradientRotation"
            :max="180"
            @input="updatePfp('borderGradientRotation', $event)"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      colorTab: 'gradient',
      colorOptions: {
        solid: [
          { from: '#ffffff', to: '#ffffff' },
          { from: '#000000', to: '#000000' },
          { from: '#ee0979', to: '#ee0979' },
          { from: '#e43a15', to: '#e43a15' },
          { from: '#ee9ca7', to: '#ee9ca7' },
          { from: '#ec008c', to: '#ec008c' },
          { from: '#f953c6', to: '#f953c6' },
          { from: '#cc2b5e', to: '#cc2b5e' },
          { from: '#FF0099', to: '#FF0099' },
          { from: '#cc2b5e', to: '#cc2b5e' },
          { from: '#ec008c', to: '#ec008c' },
          { from: '#8E2DE2', to: '#8E2DE2' },
          { from: '#f7ff00', to: '#f7ff00' },
          { from: '#e65c00', to: '#e65c00' },
          { from: '#ffe259', to: '#ffe259' },
          { from: '#c21500', to: '#c21500' },
          { from: '#e65c00', to: '#e65c00' },
          { from: '#FFE000', to: '#FFE000' },
          { from: '#799F0C', to: '#799F0C' },
          { from: '#00467F', to: '#00467F' },
          { from: '#414d0b', to: '#414d0b' },
          { from: '#1CD8D2', to: '#1CD8D2' },
          { from: '#3D7EAA', to: '#3D7EAA' },
          { from: '#DAE2F8', to: '#DAE2F8' },
          { from: '#FC354C', to: '#FC354C' },
          { from: '#2193b0', to: '#2193b0' },
          { from: '#12c2e9', to: '#12c2e9' },
          { from: '#5433FF', to: '#5433FF' },
          { from: '#1488CC', to: '#1488CC' },
          { from: '#b92b27', to: '#b92b27' },
          { from: '#8E2DE2', to: '#8E2DE2' },
          { from: '#9796f0', to: '#9796f0' }
        ],
        gradient: [
          { from: '#ffffff', to: '#cdcdcd' },
          { from: '#000000', to: '#cdcdcd' },
          { from: '#ee0979', to: '#ff6a00' },
          { from: '#e43a15', to: '#e65245' },
          { from: '#ee9ca7', to: '#ffdde1' },
          { from: '#ec008c', to: '#fc6767' },
          { from: '#f953c6', to: '#b91d73' },
          { from: '#cc2b5e', to: '#753a88' },
          { from: '#FF0099', to: '#493240' },
          { from: '#cc2b5e', to: '#753a88' },
          { from: '#ec008c', to: '#fc6767' },
          { from: '#8E2DE2', to: '#4A00E0' },
          { from: '#f7ff00', to: '#db36a4' },
          { from: '#e65c00', to: '#F9D423' },
          { from: '#ffe259', to: '#ffa751' },
          { from: '#c21500', to: '#ffc500' },
          { from: '#e65c00', to: '#F9D423' },
          { from: '#FFE000', to: '#799F0C' },
          { from: '#799F0C', to: '#ACBB78' },
          { from: '#00467F', to: '#A5CC82' },
          { from: '#414d0b', to: '#727a17' },
          { from: '#1CD8D2', to: '#93EDC7' },
          { from: '#3D7EAA', to: '#FFE47A' },
          { from: '#DAE2F8', to: '#D6A4A4' },
          { from: '#FC354C', to: '#0ABFBC' },
          { from: '#2193b0', to: '#6dd5ed' },
          { from: '#12c2e9', to: '#c471ed' },
          { from: '#5433FF', to: '#A5FECB' },
          { from: '#1488CC', to: '#2B32B2' },
          { from: '#b92b27', to: '#1565C0' },
          { from: '#8E2DE2', to: '#4A00E0' },
          { from: '#9796f0', to: '#fbc7d4' }
        ]

      }
    }
  },
  computed: {
    pfp () {
      return this.$store.state.editor.pfp
    }
  },
  methods: {
    updatePfp (key, value) {
      this.$store.commit('editor/UPDATE_PFP', { key, value })
    },
    setColor (from, to) {
      this.$store.commit('editor/UPDATE_PFP', { key: 'borderColor', value: { from, to } })
    }
  }

}
</script>

<style>

</style>
