<template>
  <div class="fixed inset-x-0 top-0 flex items-center gap-x-6 bg-brand-500 py-2.5 px-6 sm:px-3.5 sm:before:flex-1 z-50">
    <p class="text-sm leading-6 text-white">
      <NuxtLink to="/app/invites">
        <strong class="font-medium">Pending invites</strong>
        <svg viewBox="0 0 2 2" class="mx-2 inline h-0.5 w-0.5 fill-current" aria-hidden="true"><circle cx="1" cy="1" r="1" /></svg>
        You've been invited to join a team&nbsp;
        <span aria-hidden="true">&rarr;</span>
      </NuxtLink>
    </p>
    <div class="flex flex-1 justify-end">
      <button type="button" class="-m-3 p-3 focus-visible:outline-offset-[-4px]" @click="hideBanner">
        <span class="sr-only">Dismiss</span>
        <svg class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z" />
        </svg>
      </button>
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    hideBanner () {
      this.$store.commit('SET_SHOW_INVITE_WARNING', false)
    }
  }
}
</script>

<style></style>
