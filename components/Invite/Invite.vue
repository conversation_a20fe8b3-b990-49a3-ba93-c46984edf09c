<template>
  <Card class="min-w-xl mt-4 w-full max-w-2xl rounded-lg p-8 shadow">
    <!-- <p class="text-sm text-gray-700 mt-2">
      Be advised. Full body shots with exposed skin might result in <strong>NSFW photos</strong>.
    </p> -->

    <div>
      <div class="space-y-2">
        <div>
          <h2 class="text-xl font-bold">
            Invites
          </h2>
          <p class="pb-2 text-sm text-gray-700">
            You've been invited to join these organizations. Accept the invite to start your photo shoot.
          </p>
        </div>
        <hr class="py-2">
        <AlertWarning
          v-if="$store.state?.user?.role === 'TeamLead'"
          description="You are currently the owner of a team. Accepting an invite will remove you from your current team and make you a member of the new team."
        />
        <AlertWarning
          v-if="$store.state?.user?.role === 'TeamMember'"
          description="You are currently a member of a team. Accepting an invite will remove you from your current team and make you a member of the new team."
        />
        <template v-if="invites">
          <template v-for="invite in invites">
            <InviteItem :key="invite.uid" :invite="invite" />
          </template>
        </template>
        <template v-else>
          <p>No invites pending at the moment.</p>
          <NuxtLink to="/app">
            <ButtonPrimary size="sm" class="mt-2">
              Go back
            </ButtonPrimary>
          </NuxtLink>
        </template>
      </div>
    </div>
  </Card>
</template>

<script>
export default {
  computed: {
    invites () {
      return this.$store.state?.user?.invites || null
    }
  }
}
</script>

<style></style>
