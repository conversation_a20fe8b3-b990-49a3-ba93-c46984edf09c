<template>
  <div class="flex w-full items-center justify-between rounded-md bg-gray-50 px-6 py-4">
    <div>
      <p class="font-bold text-gray-900">
        {{ invite.organization || "Unknown organization" }}
      </p>
      <p class="text-sm font-light text-gray-500">
        {{ formatDate(invite.date) || "" }}
      </p>
    </div>
    <template v-if="isLoading">
      <LoadingSpinner class="h-5 w-5 text-gray-500" />
    </template>
    <template v-else>
      <div class="flex items-center justify-center space-x-2">
        <button class="group flex items-center rounded-md border border-gray-100 bg-white p-2" @click="acceptInvite">
          <IconCheck class="h-5 w-5 text-green-500" />
          <span class="ml-1 hidden text-xs font-light text-gray-500 group-hover:block">Accept</span>
        </button>
        <button class="group flex items-center rounded-md border border-gray-100 bg-white p-2" @click="declineInvite">
          <IconCross class="h-5 w-5 text-red-500" />
          <span class="ml-1 hidden text-xs font-light text-gray-500 group-hover:block">Decline</span>
        </button>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    invite: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      isLoading: false
    }
  },
  methods: {
    async acceptInvite () {
      try {
        this.isLoading = true
        const { success, errorMessage } = await this.$axios.$post('/invite/accept', {
          inviteUid: this.invite.uid
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.$toast.success('Invite accepted')
        this.$store.commit('user/SET_INVITES', null)
        await this.getUserData()
        this.$router.push('/app')
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = false
      }
    },
    async declineInvite () {
      try {
        this.isLoading = true
        const { success, errorMessage } = await this.$axios.$post('/invite/decline', {
          inviteUid: this.invite.uid
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.$toast.success('Invite declined')
        this.$store.commit('user/SET_INVITES', null)
        await this.getUserData()
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = false
      }
    }
  }
}
</script>

<style></style>
