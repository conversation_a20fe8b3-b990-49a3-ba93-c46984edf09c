<template>
  <div v-if="isSubmitted">
    <div class="w-full border-b border-gray-100 pb-4">
      <h2 class="font-bold text-lg">
        {{ title }}
      </h2>
    </div>
    <div class="mt-8">
      <IconCheck class="mx-auto h-[5rem] w-[5rem] text-green-500" />
      <p class="text-center text-sm text-gray-500 mt-4">
        We've got your message! We will get back to you as soon as possible.
      </p>
    </div>
  </div>
  <div v-else>
    <SupportHeaderB2C v-if="!isTeamLead" :title="title" />
    <SupportHeaderB2B v-else-if="isTeamLead" :title="title" />
    <div class="space-y-2 text-left">
      <template v-if="role === 'TeamMember' && !refundOnly">
        <div class="flex items-center space-x-1 text-sm text-left justify-left text-gray-800">
          <p>Please contact your team admin for support with your account or photo shoot. Having technical issues? Please fill in the form below.</p>
        </div>
        <hr class="my-4">
      </template>
      <!-- <template v-else-if="role === 'TeamLead' && !refundOnly">
        <div class="flex items-center space-x-1 text-sm text-left justify-left text-gray-800">
          <p>For team requests, problems, feedback or issues, please fill in the form below or send an email to <a class="underline text-blue-500" href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
        <hr class="my-4">
      </template> -->

      <LoadingWrapper :is-loading="isLoading">
        <template v-if="refundOnly && role === 'TeamMember'">
          <div class="flex items-center space-x-1 text-sm text-left justify-left text-gray-800">
            <p>Team members can\'t request refunds. Please contact your team owner to request a retry or refund.</p>
          </div>
        </template>
        <template v-else-if="refundOnly && role === 'TeamLead'">
          <div class="flex items-center space-x-1 text-sm text-left justify-left text-gray-800">
            <p>We're sorry to hear things didn't work out for you. To request a refund for your organization, please contact <a class="underline text-blue-500" href="mailto:<EMAIL>"><EMAIL></a> and mention your support request ID <strong>({{ $store.state.user.uid }})</strong> in your message.</p>
          </div>
        </template>
        <template v-else>
          <form class="mt-4" @submit.prevent="submit">
            <div class="flex flex-col space-y-2">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input v-model="request.name" :required="true" label="Your name" />
                <InputSelect
                  v-show="!refundOnly"
                  v-model="request.type"
                  :required="true"
                  :options="filteredRequestTypes"
                  label="Request type"
                />
              </div>
              <InputSelect
                v-show="request.type === 'refund'"
                v-model="request.paymentId"
                label="Which order can we help you with?"
                :options="payments"
              />
              <InputTextArea v-model="request.message" :required="true" label="How can we help you?" :placeholder="placeholder" />
            </div>
            <div class="flex justify-center items-center mt-4 w-full">
              <ButtonPrimary type="submit" class="w-full">
                <span>Submit now</span>
                <IconArrowRight class="size-4 text-white ml-1" />
              </ButtonPrimary>
            </div>
            <div class="flex items-center mt-2 space-x-1 text-xs text-center justify-center text-gray-500">
              <p>
                We'll send a response to your <span class="font-medium">{{ $store.state.user.email }}</span> address within {{ isHolidayMode ? 'as soon as possible' : 'one business day' }}.
              </p>
            </div>
          </form>
        </template>
      </LoadingWrapper>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    refundOnly: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      active: null,
      isLoading: false,
      isSubmitted: false,
      request: {
        name: '',
        message: '',
        type: 'feedback',
        paymentId: null
      },
      requestTypes: [
        {
          value: 'refund',
          title: 'Refund request'
        },
        {
          value: 'feedback',
          title: 'Submit feedback'
        },
        {
          value: 'issue',
          title: 'Report technical issue'
        },
        {
          value: 'team',
          title: 'Corporate / Team'
        },
        { value: 'sales', title: 'Sales or request a quote' },
        {
          value: 'other',
          title: 'Other'
        }
      ],
      paymentList: []
    }
  },
  computed: {
    role () {
      return this.$store.state?.user?.role
    },
    filteredRequestTypes () {
      if (this.role === 'TeamMember') {
        return this.requestTypes.filter(type => type.value !== 'refund')
      }
      if (this.role === 'TeamLead') {
        return this.requestTypes.filter(type => type.value !== 'refund')
      }
      return this.requestTypes
    },
    title () {
      if (this.refundOnly) {
        return 'Refund request'
      }
      if (this.role === 'TeamLead') {
        return 'Contact our team'
      }
      return 'Support & Feedback'
    },
    placeholder () {
      switch (this.request.type) {
        case 'refund':
          return 'Didn\'t get any keepers in your order? Share a few sentences about why your headshots are unusable and we\'ll get you sorted out.'
        case 'feedback':
          return 'Provide feedback that will be reviewed by the HeadshotPro product team.'
        case 'issue':
          return 'Please provide a detailed description of (1) what you\'re trying to do (2) what happens when you try to do that and (3) what you\'ve tried already.'
        case 'sales':
          return 'Please provide a detailed description of your request and I will get back to you as soon as possible.'
        default:
          return 'How can we help you?'
      }
    },
    isSupportOnline () {
      const now = new Date()
      // Convert local time to GMT+8
      const gmt8Time = new Date(now.getTime() + (now.getTimezoneOffset() * 60000) + (8 * 3600000))

      const hours = gmt8Time.getHours()
      const day = gmt8Time.getDay()

      // Support hours are from 13:00 to 17:00 GMT+8
      const isWeekday = day >= 1 && day <= 5 // Monday (1) to Friday (5)
      const isWorkingHours = hours >= 13 && hours < 17

      return isWeekday && isWorkingHours
    },
    nextOnlineTime () {
      const now = new Date()
      const localTime = now.getTime()
      const localOffset = now.getTimezoneOffset() * 60000
      const utc = localTime + localOffset
      const offset = 8 // GMT+8
      const supportTime = utc + (3600000 * offset)
      const supportDate = new Date(supportTime)

      const day = supportDate.getUTCDay()
      let nextOnline

      if (day >= 5) { // If it's Friday or the weekend, set to Monday
        nextOnline = new Date(supportDate)
        nextOnline.setUTCDate(supportDate.getUTCDate() + ((8 - day) % 7))
        nextOnline.setUTCHours(13, 0, 0, 0) // Set to 13:00 GMT+8
      } else {
        nextOnline = new Date(supportDate)
        nextOnline.setUTCHours(13, 0, 0, 0) // Set to 13:00 GMT+8
        if (supportDate.getUTCHours() >= 16) {
          // If after working hours, set to next day
          nextOnline.setUTCDate(supportDate.getUTCDate() + 1)
        }
      }

      // Format the date to a readable format, e.g., "Monday at 1:00 PM"
      return nextOnline.toLocaleString('en-US', { weekday: 'long', hour: 'numeric', minute: 'numeric', hour12: true, timeZone: 'UTC' })
    },
    payments () {
      return this.paymentList
        .filter(payment => payment.amountTotal > 0)
        .map(payment => ({
          value: payment._id,
          title: `${this.formatDate(payment.createdOn)} - $${(payment.amountTotal / 100).toFixed(2)}`
        }))
    }
  },
  mounted () {
    this.request.uid = this.$store.state.user.uid
    this.request.email = this.$store.state.user.email
    if (this.refundOnly) {
      this.request.type = 'refund'
    }
    this.fetchPaymentList()
  },
  methods: {
    fetchPaymentList () {
      this.$axios.$get('/user/payments/all')
        .then((response) => {
          this.paymentList = response.payments
        })
    },
    submit () {
      this.isLoading = true
      this.isSubmitted = false
      this.$axios.$post('/support/feedback/submit', {
        ...this.request,
        email: this.$store.state.user.email,
        uid: this.$store.state.user.uid,
        paymentId: this.request.type === 'refund' ? this.request.paymentId : null
      })
        .then((response) => {
          this.isLoading = false
          if (response.success) {
            this.$toast.success('Your request has been submitted. Camilla will get back to you soon.')
            this.$emit('closeModal')
            this.isSubmitted = true
          } else {
            this.$toast.error(response.message || 'Failed to submit your request. Please try again later or contact <NAME_EMAIL>')
          }
        })
        .catch((error) => {
          this.isLoading = false
          this.handleError(error)
          console.error(error)
        })
    }

  }
}
</script>
