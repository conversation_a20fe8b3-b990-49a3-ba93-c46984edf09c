<template>
  <div class="gap-2 flex flex-col border-b border-gray-100 pb-4">
    <h2 class="text-lg font-semibold">
      {{ $t('Contact our team') }}
    </h2>
    <p class="text-sm text-paragraph">
      Have a question, custom request, or want to explore a partnership? Submit the form below and me or someone from our team will get back to you - usually within 24 business hours (often sooner).
    </p>
    <p class="text-sm text-paragraph">
      {{ $t('Kind regards,') }}
    </p>
    <div class="flex flex-row justify-between items-center">
      <div class="flex space-x-2 items-center justify-start">
        <div class="w-12 h-12 rounded-full bg-gradient-to-br from-teal-500/20 to-teal-700/20 border border-black/10 flex items-center justify-center">
          <img src="@/assets/img/avatar-tam.png" class="w-12 h-12 rounded-full">
        </div>
        <div class="flex flex-col text-sm items-start justify-center ">
          <span class="font-bold text-gray-700">Tam <PERSON></span>
          <span class="text-paragraph">{{ $t('Enterprise General Manager') }}</span>
        </div>
      </div>
      <div v-if="showCallButton">
        <a href="https://cal.com/team/headshotpro/30-min?overlayCalendar=true" target="_blank">
          <ButtonDark size="xs">
            <IconMiniPhone class="size-3 text-white mr-1" />
            <span>Book a call</span>
          </ButtonDark>
        </a>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  computed: {
    showCallButton () {
      const { organization } = this.$store.state.organization
      if (!organization) {
        return false
      }
      if (organization?.totalCreditsPurchased > 1) {
        return true
      }
      if (['100-200', '200-499', '1000+', '500-999'].includes(organization.teamSize)) {
        return true
      }
      return false
    }
  }

}
</script>

<style>

</style>
