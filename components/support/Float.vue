<template>
  <div v-if="role !== 'TeamMember'" class="w-12 h-12 bg-blue-500 fixed z-50 bottom-4 right-4 rounded-full flex items-center justify-center cursor-pointer chat-button" @click="showModal()">
    <IconSolidChat class="w-8 h-8 text-white" />
  </div>
</template>

<script>
export default {
  computed: {
    role () {
      return this.$store.state.user.role || 'User'
    }
  },
  methods: {
    showModal () {
      this.$store.commit('SET_MODAL', { name: 'support', value: true })
    }
  }

}
</script>

<style>

</style>
