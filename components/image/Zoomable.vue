<template>
  <div
    class="zoom-container relative group"
    :style="{ backgroundImage: 'url(' + src + ')' }"
    @mousemove="handleMouseMove"
    @mouseenter="zoomIn"
    @mouseleave="zoomOut"
  >
    <div class="absolute hidden md:flex items-center justify-center w-full bottom-3 left-0 group-hover:opacity-0 opacity-100 transition-opacity duration-300 group-hover:invisible visible group-hover:pointer-events-none">
      <div class="rounded-full flex items-center justify-center space-x-2 bg-black/70 backdrop-blur-md px-2 py-1">
        <IconSolidSearch class="w-3 h-3 text-white/70" />
        <span class="text-xs text-white/70">Hover to zoom</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    src: String,
    zoomFactor: {
      type: Number,
      default: 1.5
    }
  },

  methods: {
    zoomIn () {
      this.$el.style.backgroundSize = `${this.zoomFactor * 100}%`
    },
    zoomOut () {
      this.$el.style.backgroundSize = 'cover'
    },
    handleMouseMove (event) {
      const { offsetX, offsetY } = event
      const { clientWidth, clientHeight } = this.$el

      const xPercent = (offsetX / clientWidth) * 100
      const yPercent = (offsetY / clientHeight) * 100

      this.$el.style.backgroundPosition = `${xPercent}% ${yPercent}%`
    }
  }
}
</script>
