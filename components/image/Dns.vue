<template>
  <v-lazy-image
    v-if="!isSafari"
    :src="dnsSrc"
    :srcset="`${dnsSrc} 1x, ${dnsSrcRetina} 2x`"
    :src-placeholder="require('~/assets/img/loading.gif')"
    :alt="alt"
    :width="(width === 0 ) ? 'auto' : width"
    :height="(height === 0 ) ? 'auto' : height"
    :fetchpriority="fetchpriority"
    onerror="this.style.display = &quot;none&quot;"
    @load="$emit('load')"
    @click="$emit('click')"
  />
  <img
    v-else
    :src="dnsSrc"
    :srcset="`${dnsSrc} 1x, ${dnsSrcRetina} 2x`"
    :alt="alt"
    :fetchpriority="fetchpriority"
    loading="lazy"
    onerror="this.style.display = &quot;none&quot;"
    @load="$emit('load')"
    @click="$emit('click')"
  >
</template>

<script>
import VLazyImage from 'v-lazy-image'
export default {
  components: {
    VLazyImage
  },
  props: {
    src: { type: String },
    crop: { type: String },
    width: { type: [Number, String] },
    height: { type: [Number, String] },
    alt: { type: String },
    fetchpriority: { type: String }
  },
  computed: {
    // https://landingfoliocom.imgix.net/1625107877901-pixeltrue-landing-page-desktop.png?lossless=true&fit=crop&w=360&h=600&crop=top,left&auto=format&q=75
    dnsSrc () {
      const { src } = this// crop, width, height } = this
      if (!src) { return '' }
      if (src.includes('.gif')) { return this.dnsImage(src) }
      // let query = ''
      // if (crop) { query = query + crop }
      // if (width) { query = query + `&w=${width}` }
      // if (height) { query = query + `&h=${height}` }
      return `${src}`
    },
    dnsSrcRetina () {
      const { src } = this// crop, width, height } = this
      if (!src) { return '' }
      if (src.includes('.gif')) { return this.dnsImage(src) }
      // let query = ''
      // if (crop) { query = query + crop }
      // if (width) { query = query + `&w=${width * 2}` }
      // if (height) { query = query + `&h=${height * 2}` }
      return `${src}`
    }
  }
}
</script>

<style>

</style>
