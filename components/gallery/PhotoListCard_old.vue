<template>
  <Card data-testid="photo-list-card">
    <header class="flex gap-4 relative flex-col xs:flex-row xs:items-center xs:justify-between">
      <div class="text-paragraph text-left">
        <div class="flex items-center space-x-2">
          <component :is="icon" class="w-6 h-6" />
          <h2 class="text-lg font-semibold text-primary-500" data-testid="photo-list-card-title">
            {{ title }}
          </h2>
        </div>
        <div v-if="description?.component" class="mt-2 xs:mt-1" data-testid="photo-list-card-description">
          <component :is="description.component" v-bind="description.props" />
        </div>
        <p v-else-if="description" class="text-sm text-slate-500 mt-2" data-testid="photo-list-card-description">
          {{ description }}
        </p>
      </div>
      <div class="order-first absolute right-0 md:top-0 top-[-4px]  xs:relative xs:order-none">
        <ButtonDropdown
          v-if="photos.length > 0"
          theme="v1"
          title="Actions"
          :items="actions"
          size="sm"
          @select="handleAction"
        />
      </div>
    </header>
    <div v-if="photos.length > 0 || (showNsfw && item?.totalNsfwNew > 0)" class="grid grid-cols-1 xs:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mt-4">
      <div v-for="(photo, index) in photos" :key="photo._id" class="contents">
        <UpsellResultCard v-if="upsellIndex === index" :item="item" />
        <GalleryPhotoCard
          :photo="photo"
          :icons="icons"
          @preview="previewPhoto(photo)"
        />
        <VotingPromotionCard
          v-if="showVotingPromotion(index)"
          class="mt-4"
        />
      </div>
      <template v-if="showNsfw && item?.totalNsfwNew > 0">
        <GalleryNsfwPhotoCard
          v-for="i in item?.totalNsfwNew"
          :key="i"
        />
      </template>
    </div>
    <div v-else class="flex items-center justify-center min-h-24 mt-2" data-testid="photo-list-card-empty-state">
      <component :is="emptyState?.component" v-if="emptyState?.component" class="w-full" v-bind="emptyState?.props" />
      <p v-else class="text-sm text-gray-500">
        {{ emptyState || 'No photos found' }}
      </p>
    </div>
    <Modal v-if="showPreviewModal" max-width="sm:max-w-5xl" data-testid="photo-list-card-preview-modal" @close="closePreviewModal">
      <GalleryPreviewModal :preview-image="previewImage" :preview-image-large="previewImageLarge" :all-photos="photos" />
    </Modal>
    <Modal v-if="showDownloadModal" data-testid="photo-list-card-download-modal" @close="closeDownloadModal">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg font-medium leading-6 text-gray-900">
          Downloading headshots
        </h3>
        <div class="mt-2 max-w-xl text-sm text-gray-500">
          <p>Your headshots are being compressed into ZIP files. This can take a little while. Due to the large photo size, every ZIP contains max. 10 photos.</p>
          <p v-if="isProcessing" class="font-medium text-orange-500 flex items-center justify-start mt-2">
            <IconExclamation class="w-4 h-4 mr-1.5" />Keep this tab active and do not switch to other tabs during compression!
          </p>
          <p v-else class="font-medium text-orange-500 flex items-center justify-start mt-2">
            <IconExclamation class="w-4 h-4 mr-1.5" />Keep this tab active during the download process - switching tabs will pause the compression!
          </p>
          <p class="font-medium text-orange-500 flex items-center justify-start mt-2">
            <IconExclamation class="w-4 h-4 mr-1.5" />If your phone does not support ZIP files, please use a desktop to download them.
          </p>
        </div>

        <!-- Progress bar -->
        <div v-if="isProcessing" class="relative mt-4 h-2 w-full overflow-hidden rounded-full bg-gray-200">
          <div
            :style="{
              width: downloadProgress + '%',
            }"
            class="absolute inset-y-0 left-0 bg-primary-500 transition-all"
          />
        </div>
        <div v-if="isProcessing" class="text-xs mt-1">
          <div v-if="pausedForVisibility" class="text-orange-600 font-medium flex items-center">
            <IconExclamation class="w-4 h-4 mr-1.5" />
            Download paused - Please keep this tab active to continue
          </div>
          <div v-else class="animate-pulse text-gray-500">
            <span v-if="downloadIndex === 0">Preparing compression...</span>
            <span v-else-if="downloadIndex < photos.length">{{ downloadIndex }}/{{ photos.length }} processed...</span>
            <span v-else-if="downloadIndex >= photos.length">Retrying failed downloads...</span>
            <span v-else>Finalizing compression...</span>
          </div>
        </div>

        <!-- Generated ZIP files list -->
        <GalleryZipDownloadList
          v-if="generatedZips.length > 0"
          :zip-files="generatedZips"
          @download-zip="downloadZip"
          @download-all="downloadAllZips"
        />

        <!-- Completion message -->
        <div v-if="!isProcessing && generatedZips.length > 0" class="flex flex-col md:flex-row items-center justify-between gap-8 rounded-md bg-muted-50 p-2 md:p-4 mt-4">
          <img src="@/assets/img/accept-download-modal.png" class="rounded-md max-w-[210px] w-full order-2 md:order-1" alt="Accept download modal">
          <div class="flex flex-col justify-center order-1 md:order-2">
            <p class="text-sm text-gray-500">
              Your browser may block the download. Make sure to accept the download. If no download modal is shown, open your browser settings and allow downloads from HeadshotPro.
            </p>
          </div>
        </div>
      </div>
    </Modal>
  </Card>
</template>

<script>
import JSZip from 'jszip'
import { saveAs } from 'file-saver'
import DownloadTrackingMixin from '../../mixins/DownloadTrackingMixin'

export default {
  mixins: [DownloadTrackingMixin],
  props: {
    title: {
      type: String,
      required: true
    },
    description: {
      required: false
    },
    icon: {
      type: String,
      required: true
    },
    emptyState: {
      required: false
    },
    photos: {
      type: Array,
      required: true
    },
    icons: {
      type: Array,
      required: false
    },
    upsellIndex: {
      type: Number,
      required: false
    },
    item: {
      type: Object,
      required: false
    },
    showNsfw: {
      type: Boolean,
      required: false
    },
    identifier: {
      type: String,
      required: false
    }
  },
  data () {
    return {
      showPreviewModal: false,
      previewImage: null,
      previewImageLarge: null,
      downloadIndex: 0,
      isProcessing: false,
      showDownloadModal: false,
      generatedZips: [],
      wantFeedback: false,
      isTabHidden: false,
      pausedForVisibility: false
    }
  },
  computed: {
    feedbackModal () {
      return this.$store.state.showReviewRequestModal
    },
    downloadProgress () {
      if (this.photos.length === 0 || this.downloadIndex === 0) {
        return 0
      }
      return (this.downloadIndex / this.photos.length) * 100
    },
    actions () {
      const baseActions = [
        {
          title: 'Download headshots',
          value: 'download',
          icon: 'IconCloudDownload'
        }
      ]

      // Add Download PFPs if user is organization member and photos have PFP versions
      if (this.hasPfpPhotos) {
        baseActions.push({
          title: 'Download PFPs',
          value: 'download-pfps',
          icon: 'IconCloudDownload'
        })
      }

      return baseActions
    },
    hasPfpPhotos () {
      return this.photos.some(photo => photo.pfp)
    },
    showVotingPromotion () {
      return (index) => {
        return this.identifier === 'new-photos' && index === 2 && !this.item?.shareToken && !this.$route.fullPath.includes('admin')
      }
    }
  },
  watch: {
    feedbackModal (newVal) {
      if (this.showDownloadModal && newVal) {
        this.$store.commit('SET_SHOW_REVIEW_REQUEST_MODAL', false)
        this.wantFeedback = true
      }
    }
  },
  mounted () {
    // Listen for visibility changes to handle tab backgrounding
    this.setupVisibilityHandling()
  },
  beforeDestroy () {
    // Clean up visibility event listeners
    this.cleanupVisibilityHandling()
  },
  methods: {
    previewPhoto (photo) {
      this.previewImage = photo?.small || photo?.image || photo?.thumbnail
      this.showPreviewModal = true
      this.previewImageLarge = photo?.image
    },
    closePreviewModal () {
      this.showPreviewModal = false
      this.previewImage = null
      this.previewImageLarge = null
    },
    closeDownloadModal () {
      this.showDownloadModal = false
      this.generatedZips = []
      this.downloadIndex = 0
      this.isProcessing = false
      if (this.wantFeedback) {
        this.$store.commit('SET_SHOW_REVIEW_REQUEST_MODAL', true)
      }
    },
    downloadZip (zipData) {
      saveAs(zipData.blob, zipData.filename)
    },
    async downloadAllZips () {
      // Download all ZIP files directly to Downloads folder
      for (const zipData of this.generatedZips) {
        saveAs(zipData.blob, zipData.filename)
        // Small delay between downloads to avoid overwhelming the browser
        await new Promise(resolve => setTimeout(resolve, 200))
      }
    },

    saveFileWithLocationChoice (blob, suggestedName) {
      // Download directly to Downloads folder for all browsers
      saveAs(blob, suggestedName)
    },

    handleAction (action) {
      if (action === 'download') {
        this.downloadAll()
      } else if (action === 'download-pfps') {
        this.downloadPFPs()
      }
    },
    async downloadAll () {
      console.log('🔄 Starting downloadAll process')
      this.showDownloadModal = true
      if (!this.photos || this.photos.length === 0 || !this.showDownloadModal) {
        console.log('❌ Early return: no photos or modal closed')
        return
      }
      this.isProcessing = true

      const images = this.photos.map(img => img.image)
      const maxConcurrentDownloads = 5
      const maxImagesPerZip = 10

      console.log(`📊 Processing ${images.length} images, will create ~${Math.ceil(images.length / maxImagesPerZip)} ZIP files`)

      // Track successful downloads and failed ones
      const successfulBlobs = []
      const failedDownloads = []

      try {
        console.log('🔄 Phase 1: Downloading images')
        // Process all images in batches
        for (let i = 0; i < images.length; i += maxConcurrentDownloads) {
          if (!this.showDownloadModal) {
            console.log('❌ Modal closed during download, aborting')
            return
          }

          // Check if tab is hidden and pause if needed
          await this.waitForTabVisibility()

          console.log(`🔄 Processing batch ${Math.floor(i / maxConcurrentDownloads) + 1}/${Math.ceil(images.length / maxConcurrentDownloads)} (images ${i + 1}-${Math.min(i + maxConcurrentDownloads, images.length)})`)

          const batch = images.slice(i, i + maxConcurrentDownloads)
          const batchPromises = batch.map(async (imageUrl, batchIndex) => {
            const globalIndex = i + batchIndex
            try {
              console.log(`⬇️ Downloading image ${globalIndex + 1}/${images.length}`)
              const response = await this.$axios.$get(imageUrl, {
                responseType: 'blob',
                headers: { 'Access-Control-Allow-Origin': '*' },
                timeout: 30000 // 30 second timeout
              })
              this.downloadIndex++
              console.log(`✅ Downloaded image ${globalIndex + 1}/${images.length}`)
              return {
                blob: response,
                originalIndex: globalIndex,
                success: true
              }
            } catch (err) {
              console.error(`❌ Failed to download image ${globalIndex + 1}/${images.length}:`, err)
              this.downloadIndex++
              failedDownloads.push(globalIndex)
              return {
                blob: null,
                originalIndex: globalIndex,
                success: false
              }
            }
          })

          const batchResults = await Promise.all(batchPromises)

          // Only add successful downloads to the blob array
          batchResults.forEach((result) => {
            if (result.success && result.blob) {
              successfulBlobs.push({
                blob: result.blob,
                originalIndex: result.originalIndex
              })
            }
          })

          console.log(`📊 Batch complete. Successful: ${batchResults.filter(r => r.success).length}, Failed: ${batchResults.filter(r => !r.success).length}`)
        }

        console.log(`📊 Download phase complete. Total successful: ${successfulBlobs.length}, Total failed: ${failedDownloads.length}`)

        // Retry failed downloads once
        if (failedDownloads.length > 0) {
          console.log(`🔄 Retrying ${failedDownloads.length} failed downloads...`)
          const retryResults = await this.retryFailedDownloads(images, failedDownloads, successfulBlobs)
          console.log(`📊 Retry complete. Additional successful: ${retryResults.additionalSuccessful}, Still failed: ${retryResults.stillFailed}`)

          if (retryResults.additionalSuccessful > 0) {
            console.log(`🎉 Successfully recovered ${retryResults.additionalSuccessful} images through retries!`)
          }
          if (retryResults.stillFailed > 0) {
            console.warn(`⚠️ ${retryResults.stillFailed} images permanently failed and will be excluded from ZIP files`)
          }
        }

        if (successfulBlobs.length === 0) {
          throw new Error('No images were successfully downloaded')
        }

        console.log('🔄 Phase 2: Creating ZIP files')
        // Create ZIP files from successful downloads
        const totalZips = Math.ceil(successfulBlobs.length / maxImagesPerZip)
        console.log(`📊 Will create ${totalZips} ZIP files from ${successfulBlobs.length} successful downloads`)

        const zipPromises = []

        for (let i = 0; i < successfulBlobs.length; i += maxImagesPerZip) {
          // Check if tab is hidden before each ZIP creation
          await this.waitForTabVisibility()

          const zipBlobs = successfulBlobs.slice(i, i + maxImagesPerZip)
          const zipNumber = Math.floor(i / maxImagesPerZip) + 1

          console.log(`🗜️ Queuing ZIP ${zipNumber}/${totalZips} with ${zipBlobs.length} images`)

          const zipPromise = this.createZipFileWithTimeout(zipBlobs, zipNumber, totalZips, i)
          zipPromises.push(zipPromise)
        }

        console.log(`🔄 Generating ${zipPromises.length} ZIP files...`)
        // Wait for all ZIP files to be generated with overall timeout
        const zipGenerationTimeout = new Promise((resolve, reject) => {
          setTimeout(() => reject(new Error('ZIP generation timed out after 5 minutes')), 5 * 60 * 1000)
        })

        const generatedZips = await Promise.race([
          Promise.all(zipPromises),
          zipGenerationTimeout
        ])

        console.log(`📊 ZIP generation complete. Generated: ${generatedZips.filter(z => z).length}, Failed: ${generatedZips.filter(z => !z).length}`)

        // Only add successfully generated ZIPs
        generatedZips.forEach((zip, index) => {
          if (zip) {
            this.generatedZips.push(zip)
            console.log(`✅ Added ZIP ${index + 1} to download list: ${zip.filename}`)
          } else {
            console.error(`❌ ZIP ${index + 1} failed to generate`)
          }
        })

        console.log(`📊 Final result: ${this.generatedZips.length} ZIP files ready for download`)

        // Only track downloads if we have successfully generated ZIPs
        if (this.generatedZips.length > 0) {
          this.showCompletion()

          // Track only the successfully downloaded photos
          const successfulPhotoIds = successfulBlobs.map(item => this.photos[item.originalIndex]?._id).filter(Boolean)
          if (successfulPhotoIds.length > 0) {
            console.log(`📊 Tracking ${successfulPhotoIds.length} successful downloads`)
            this.trackDownloads(this.photos[0]?.modelId, successfulPhotoIds)
          }
        } else {
          // Show error if no ZIPs were generated
          console.error('❌ No ZIP files were successfully generated')
          this.$toast.error('Failed to generate ZIP files. Please try again.')
        }
      } catch (error) {
        console.error('💥 Critical error in downloadAll:', error)
        this.$toast.error(`Download failed: ${error.message}. Please try again.`)
      } finally {
        console.log('🏁 Setting isProcessing to false')
        this.isProcessing = false
      }
    },

    async createZipFileWithTimeout (zipBlobs, zipNumber, totalZips, startingPhotoIndex) {
      console.log(`🗜️ Starting ZIP generation ${zipNumber}/${totalZips} with ${zipBlobs.length} images`)

      try {
        const zip = new JSZip()

        zipBlobs.forEach((blobData, index) => {
          const fileName = `#${startingPhotoIndex + index + 1} - ${this.item?.title} - HeadshotPro.png`
          zip.file(fileName, blobData.blob)
          console.log(`📁 Added file to ZIP ${zipNumber}: ${fileName}`)
        })

        const filename = `${this.item?.title || ''} - (${zipNumber}/${totalZips}) HeadshotPro.zip`
        console.log(`🔄 Generating ZIP file: ${filename}`)

        // Create timeout promise for individual ZIP generation (2 minutes per ZIP)
        const zipTimeout = new Promise((resolve, reject) => {
          setTimeout(() => reject(new Error(`ZIP ${zipNumber} generation timed out after 2 minutes`)), 1 * 60 * 1000)
        })

        const zipGeneration = zip.generateAsync({
          type: 'blob',
          compression: 'DEFLATE',
          compressionOptions: { level: 6 }
        })

        const content = await Promise.race([zipGeneration, zipTimeout])

        console.log(`✅ Successfully generated ZIP ${zipNumber}/${totalZips}: ${filename} (${(content.size / 1024 / 1024).toFixed(2)} MB)`)

        return {
          blob: content,
          filename,
          size: content.size
        }
      } catch (error) {
        console.error(`❌ Failed to create ZIP file ${zipNumber}/${totalZips}:`, error)
        return null
      }
    },

    showCompletion () {
      // This method can be used for future completion logic
      // Currently just logging success
      console.log('ZIP files generation completed successfully')
    },
    processPfpWithBranding (pfpUrl, photoId, branding) {
      console.log(`🎨 Starting PFP processing for ${photoId} with URL: ${pfpUrl}`)

      return new Promise((resolve, reject) => {
        // Add timeout to PFP processing (30 seconds per image)
        const timeout = setTimeout(() => {
          console.error(`⏰ PFP processing timeout for ${photoId}`)
          reject(new Error(`PFP processing timed out for ${photoId}`))
        }, 30000)

        try {
          // Create a canvas to merge the layers (same as ProfilePicture.vue)
          const canvas = document.createElement('canvas')
          const size = 600 // Standard size for PFP
          canvas.width = size
          canvas.height = size
          const ctx = canvas.getContext('2d')

          if (branding?.backgroundImage) {
            console.log(`🖼️ Processing PFP ${photoId} with background image: ${branding.backgroundImage}`)
            // Load and draw the background image
            const bgImage = new Image()
            bgImage.crossOrigin = 'anonymous'
            bgImage.onload = () => {
              console.log(`✅ Background image loaded for ${photoId}`)
              // Draw background image
              ctx.drawImage(bgImage, 0, 0, size, size)

              // Load and draw the PFP layer
              const pfpImage = new Image()
              pfpImage.crossOrigin = 'anonymous'
              pfpImage.onload = () => {
                console.log(`✅ PFP image loaded for ${photoId}`)
                ctx.drawImage(pfpImage, 0, 0, size, size)

                // Convert to blob
                canvas.toBlob((blob) => {
                  clearTimeout(timeout)
                  console.log(`✅ PFP blob generated for ${photoId} (${(blob?.size / 1024).toFixed(2)} KB)`)
                  resolve(blob)
                }, 'image/png')
              }
              pfpImage.onerror = () => {
                clearTimeout(timeout)
                console.error(`❌ Failed to load PFP image for ${photoId}`)
                reject(new Error(`Failed to load PFP image for ${photoId}`))
              }
              pfpImage.src = pfpUrl
            }
            bgImage.onerror = () => {
              clearTimeout(timeout)
              console.error(`❌ Failed to load background image for ${photoId}`)
              reject(new Error(`Failed to load background image for ${photoId}`))
            }
            bgImage.src = branding.backgroundImage
          } else {
            console.log(`🎨 Processing PFP ${photoId} with solid background color: ${branding?.backgroundColor || '#000'}`)
            // Draw solid background color
            ctx.fillStyle = branding?.backgroundColor || '#000'
            ctx.fillRect(0, 0, size, size)

            // Load and draw the PFP layer
            const pfpImage = new Image()
            pfpImage.crossOrigin = 'anonymous'
            pfpImage.onload = () => {
              console.log(`✅ PFP image loaded for ${photoId}`)
              ctx.drawImage(pfpImage, 0, 0, size, size)

              // Convert to blob
              canvas.toBlob((blob) => {
                clearTimeout(timeout)
                console.log(`✅ PFP blob generated for ${photoId} (${(blob?.size / 1024).toFixed(2)} KB)`)
                resolve(blob)
              }, 'image/png')
            }
            pfpImage.onerror = () => {
              clearTimeout(timeout)
              console.error(`❌ Failed to load PFP image for ${photoId}`)
              reject(new Error(`Failed to load PFP image for ${photoId}`))
            }
            pfpImage.src = pfpUrl
          }
        } catch (error) {
          clearTimeout(timeout)
          console.error(`💥 Error in PFP processing for ${photoId}:`, error)
          reject(error)
        }
      })
    },
    async downloadPFPs () {
      console.log('🔄 Starting downloadPFPs process')
      this.showDownloadModal = true

      // Filter photos that have PFP versions
      const pfpPhotos = this.photos.filter(photo => photo.pfp)

      if (!pfpPhotos || pfpPhotos.length === 0 || !this.showDownloadModal) {
        console.log('❌ Early return: no PFP photos or modal closed')
        return
      }
      this.isProcessing = true

      const branding = this.item?.branding || {}
      const maxImagesPerZip = 10
      const successfulBlobs = []
      const failedProcessing = []

      console.log(`📊 Processing ${pfpPhotos.length} PFP photos, will create ~${Math.ceil(pfpPhotos.length / maxImagesPerZip)} ZIP files`)

      try {
        console.log('🔄 Phase 1: Processing PFP images')
        // Process each PFP with canvas-based composition like ProfilePicture.vue
        for (let i = 0; i < pfpPhotos.length; i++) {
          if (!this.showDownloadModal) {
            console.log('❌ Modal closed during PFP processing, aborting')
            return
          }

          // Check if tab is hidden and pause if needed
          await this.waitForTabVisibility()

          const photo = pfpPhotos[i]
          console.log(`🖼️ Processing PFP ${i + 1}/${pfpPhotos.length}: ${photo._id}`)

          try {
            const processedBlob = await this.processPfpWithBranding(photo.pfp, photo._id, branding)
            if (processedBlob) {
              successfulBlobs.push({
                blob: processedBlob,
                photoId: photo._id,
                photoData: photo,
                originalIndex: i
              })
              console.log(`✅ Successfully processed PFP ${i + 1}/${pfpPhotos.length}`)
            } else {
              failedProcessing.push(i)
              console.error(`❌ PFP processing returned null for ${photo._id}`)
            }
            this.downloadIndex++
          } catch (error) {
            console.error(`❌ Error processing PFP ${i + 1}/${pfpPhotos.length} (${photo._id}):`, error)
            failedProcessing.push(i)
            this.downloadIndex++
          }
        }

        console.log(`📊 PFP processing complete. Successful: ${successfulBlobs.length}, Failed: ${failedProcessing.length}`)

        // Retry failed PFP processing
        if (failedProcessing.length > 0) {
          console.log(`🔄 Retrying ${failedProcessing.length} failed PFP processing...`)
          const retryResults = await this.retryFailedPfpProcessing(pfpPhotos, failedProcessing, successfulBlobs, branding)
          console.log(`📊 PFP retry complete. Additional successful: ${retryResults.additionalSuccessful}, Still failed: ${retryResults.stillFailed}`)

          if (retryResults.additionalSuccessful > 0) {
            console.log(`🎉 Successfully recovered ${retryResults.additionalSuccessful} PFPs through retries!`)
          }
          if (retryResults.stillFailed > 0) {
            console.warn(`⚠️ ${retryResults.stillFailed} PFPs permanently failed and will be excluded from ZIP files`)
          }
        }

        if (successfulBlobs.length === 0) {
          throw new Error('No PFP images were successfully processed')
        }

        console.log('🔄 Phase 2: Creating PFP ZIP files')
        // Create ZIP files from successful processing
        const totalZips = Math.ceil(successfulBlobs.length / maxImagesPerZip)
        console.log(`📊 Will create ${totalZips} PFP ZIP files from ${successfulBlobs.length} successful processings`)

        const zipPromises = []

        for (let i = 0; i < successfulBlobs.length; i += maxImagesPerZip) {
          // Check if tab is hidden before each ZIP creation
          await this.waitForTabVisibility()

          const zipBlobs = successfulBlobs.slice(i, i + maxImagesPerZip)
          const zipNumber = Math.floor(i / maxImagesPerZip) + 1

          console.log(`🗜️ Queuing PFP ZIP ${zipNumber}/${totalZips} with ${zipBlobs.length} images`)

          const zipPromise = this.createPfpZipFileWithTimeout(zipBlobs, zipNumber, totalZips, i)
          zipPromises.push(zipPromise)
        }

        console.log(`🔄 Generating ${zipPromises.length} PFP ZIP files...`)
        // Wait for all ZIP files to be generated with overall timeout
        const zipGenerationTimeout = new Promise((resolve, reject) => {
          setTimeout(() => reject(new Error('PFP ZIP generation timed out after 1 minutes')), 1 * 60 * 1000)
        })

        const generatedZips = await Promise.race([
          Promise.all(zipPromises),
          zipGenerationTimeout
        ])

        console.log(`📊 PFP ZIP generation complete. Generated: ${generatedZips.filter(z => z).length}, Failed: ${generatedZips.filter(z => !z).length}`)

        // Only add successfully generated ZIPs
        generatedZips.forEach((zip, index) => {
          if (zip) {
            this.generatedZips.push(zip)
            console.log(`✅ Added PFP ZIP ${index + 1} to download list: ${zip.filename}`)
          } else {
            console.error(`❌ PFP ZIP ${index + 1} failed to generate`)
          }
        })

        console.log(`📊 Final PFP result: ${this.generatedZips.length} ZIP files ready for download`)

        // Only track downloads if we have successfully generated ZIPs
        if (this.generatedZips.length > 0) {
          this.showCompletion()

          // Track only the successfully processed photos
          const successfulPhotoIds = successfulBlobs.map(item => item.photoId).filter(Boolean)
          if (successfulPhotoIds.length > 0) {
            console.log(`📊 Tracking ${successfulPhotoIds.length} successful PFP downloads`)
            this.trackDownloads(pfpPhotos[0]?.modelId, successfulPhotoIds)
          }
        } else {
          // Show error if no ZIPs were generated
          console.error('❌ No PFP ZIP files were successfully generated')
          this.$toast.error('Failed to generate PFP ZIP files. Please try again.')
        }
      } catch (error) {
        console.error('💥 Critical error in downloadPFPs:', error)
        this.$toast.error(`PFP download failed: ${error.message}. Please try again.`)
      } finally {
        console.log('🏁 Setting isProcessing to false (PFPs)')
        this.isProcessing = false
      }
    },

    async createPfpZipFileWithTimeout (zipBlobs, zipNumber, totalZips, startingIndex) {
      console.log(`🗜️ Starting PFP ZIP generation ${zipNumber}/${totalZips} with ${zipBlobs.length} images`)

      try {
        const zip = new JSZip()

        zipBlobs.forEach((blobData, index) => {
          const fileName = `#${startingIndex + index + 1} - ${blobData.photoId}-PFP-HeadshotPro.png`
          zip.file(fileName, blobData.blob)
          console.log(`📁 Added PFP file to ZIP ${zipNumber}: ${fileName}`)
        })

        const filename = `${this.item?.title || ''} - PFPs - (${zipNumber}/${totalZips}) HeadshotPro.zip`
        console.log(`🔄 Generating PFP ZIP file: ${filename}`)

        // Create timeout promise for individual ZIP generation (2 minutes per ZIP)
        const zipTimeout = new Promise((resolve, reject) => {
          setTimeout(() => reject(new Error(`PFP ZIP ${zipNumber} generation timed out after 2 minutes`)), 2 * 60 * 1000)
        })

        const zipGeneration = zip.generateAsync({
          type: 'blob',
          compression: 'DEFLATE',
          compressionOptions: { level: 6 }
        })

        const content = await Promise.race([zipGeneration, zipTimeout])

        console.log(`✅ Successfully generated PFP ZIP ${zipNumber}/${totalZips}: ${filename} (${(content.size / 1024 / 1024).toFixed(2)} MB)`)

        return {
          blob: content,
          filename,
          size: content.size
        }
      } catch (error) {
        console.error(`❌ Failed to create PFP ZIP file ${zipNumber}/${totalZips}:`, error)
        return null
      }
    },

    setupVisibilityHandling () {
      if (typeof document !== 'undefined') {
        // Check initial visibility state
        this.isTabHidden = document.hidden || document.visibilityState === 'hidden'

        // Listen for visibility changes
        this.handleVisibilityChange = () => {
          const wasHidden = this.isTabHidden
          this.isTabHidden = document.hidden || document.visibilityState === 'hidden'

          if (this.isProcessing) {
            if (this.isTabHidden && !wasHidden) {
              console.log('⏸️ Tab became hidden during download process')
              this.pausedForVisibility = true
            } else if (!this.isTabHidden && wasHidden && this.pausedForVisibility) {
              console.log('▶️ Tab became visible again, resuming download process')
              this.pausedForVisibility = false
            }
          }
        }

        document.addEventListener('visibilitychange', this.handleVisibilityChange)
      }
    },

    cleanupVisibilityHandling () {
      if (typeof document !== 'undefined' && this.handleVisibilityChange) {
        document.removeEventListener('visibilitychange', this.handleVisibilityChange)
      }
    },

    async retryFailedDownloads (images, failedIndices, successfulBlobs) {
      const maxRetries = 2
      let additionalSuccessful = 0
      let stillFailed = 0

      for (const failedIndex of failedIndices) {
        if (!this.showDownloadModal) {
          break
        }

        // Wait for tab visibility before retry
        await this.waitForTabVisibility()

        const imageUrl = images[failedIndex]
        let retryCount = 0
        let retrySuccess = false

        while (retryCount < maxRetries && !retrySuccess && this.showDownloadModal) {
          try {
            console.log(`🔄 Retry ${retryCount + 1}/${maxRetries} for image ${failedIndex + 1}/${images.length}`)

            const response = await this.$axios.$get(imageUrl, {
              responseType: 'blob',
              headers: { 'Access-Control-Allow-Origin': '*' },
              timeout: 45000 // Increased timeout for retries
            })

            successfulBlobs.push({
              blob: response,
              originalIndex: failedIndex
            })

            console.log(`✅ Retry successful for image ${failedIndex + 1}/${images.length}`)
            additionalSuccessful++
            retrySuccess = true
            this.downloadIndex++
          } catch (error) {
            retryCount++
            console.log(`❌ Retry ${retryCount}/${maxRetries} failed for image ${failedIndex + 1}/${images.length}: ${error.message}`)

            if (retryCount < maxRetries) {
              // Wait a bit before next retry, with exponential backoff
              const waitTime = Math.min(2000 * Math.pow(2, retryCount - 1), 10000)
              console.log(`⏳ Waiting ${waitTime}ms before next retry...`)
              await new Promise(resolve => setTimeout(resolve, waitTime))
            }
          }
        }

        if (!retrySuccess) {
          stillFailed++
          console.warn(`💔 All retries failed for image ${failedIndex + 1}/${images.length}`)
        }
      }

      return {
        additionalSuccessful,
        stillFailed
      }
    },

    async retryFailedPfpProcessing (pfpPhotos, failedIndices, successfulBlobs, branding) {
      const maxRetries = 2
      let additionalSuccessful = 0
      let stillFailed = 0

      for (const failedIndex of failedIndices) {
        if (!this.showDownloadModal) {
          break
        }

        // Wait for tab visibility before retry
        await this.waitForTabVisibility()

        const photo = pfpPhotos[failedIndex]
        let retryCount = 0
        let retrySuccess = false

        while (retryCount < maxRetries && !retrySuccess && this.showDownloadModal) {
          try {
            console.log(`🔄 PFP Retry ${retryCount + 1}/${maxRetries} for PFP ${failedIndex + 1}/${pfpPhotos.length}: ${photo._id}`)

            const processedBlob = await this.processPfpWithBranding(photo.pfp, photo._id, branding)

            if (processedBlob) {
              successfulBlobs.push({
                blob: processedBlob,
                photoId: photo._id,
                photoData: photo,
                originalIndex: failedIndex
              })

              console.log(`✅ PFP Retry successful for ${photo._id}`)
              additionalSuccessful++
              retrySuccess = true
              this.downloadIndex++
            } else {
              throw new Error('PFP processing returned null')
            }
          } catch (error) {
            retryCount++
            console.log(`❌ PFP Retry ${retryCount}/${maxRetries} failed for ${photo._id}: ${error.message}`)

            if (retryCount < maxRetries) {
              // Wait a bit before next retry, with exponential backoff
              const waitTime = Math.min(2000 * Math.pow(2, retryCount - 1), 10000)
              console.log(`⏳ Waiting ${waitTime}ms before next PFP retry...`)
              await new Promise(resolve => setTimeout(resolve, waitTime))
            }
          }
        }

        if (!retrySuccess) {
          stillFailed++
          console.warn(`💔 All PFP retries failed for ${photo._id}`)
        }
      }

      return {
        additionalSuccessful,
        stillFailed
      }
    },

    waitForTabVisibility () {
      // If tab is not hidden, continue immediately
      if (!this.isTabHidden) {
        return Promise.resolve()
      }

      console.log('⏸️ Download paused - waiting for tab to become visible')
      this.pausedForVisibility = true

      // Wait for tab to become visible
      return new Promise((resolve) => {
        const checkVisibility = () => {
          if (!this.isTabHidden) {
            console.log('▶️ Tab is visible again, resuming download')
            this.pausedForVisibility = false
            resolve()
          } else {
            // Check again in 500ms
            setTimeout(checkVisibility, 500)
          }
        }
        checkVisibility()
      })
    }
  }
}
</script>
