<template>
  <div class="pb-32 lg:pb-0 max-w-8xl mx-auto">
    <div class="max-w-[766px] px-4 mx-auto pb-8">
      <div class="pt-8 pb-12">
        <GallerySteps :current-step="2" />
      </div>
      <h1 class="text-[25px] font-bold leading-tight text-primary-500 text-center" data-testid="gallery-selection-page-heading">
        Select your keepers
      </h1>
      <p class="text-slate-500 text-center mt-4" data-testid="gallery-selection-page-description">
        Select the photos you really like. You can always access your duds later on. After picking your selection, you can download and edit your photos.
        <span v-if="!initial">
          You can still access your other photos in the next step.
        </span>
      </p>
    </div>
    <div class="px-4 w-full min-h-screen flex flex-col lg:flex-row gap-8">
      <Card class="w-full">
        <header class="flex flex-col xs:flex-row gap-8 items-center justify-between">
          <h2 class="text-lg font-medium text-primary-500 items-center hidden xs:flex">
            <IconCamera v-if="tab === 'all'" class="h-6 w-6 text-paragraph mr-[10px]" />
            <IconHeart v-else class="h-6 w-6 text-paragraph mr-[10px]" />

            {{ tab === 'all' ? 'All photos' : 'Keepers' }}
          </h2>
          <Tab :with-padding="false" class="order-first xs:order-none">
            <TabItem :active="tab === 'all'" data-testid="gallery-selection-page-tab-all" @click="tab = 'all'">
              <span>All photos</span>
            </TabItem>
            <TabItem :active="tab === 'keepers'" data-testid="gallery-selection-page-tab-keepers" @click="tab = 'keepers'">
              <span>Keepers</span>
            </TabItem>
          </Tab>
        </header>
        <AlertWarningV2 v-if="percentageOfNsfw > 30" class="max-w-8xl mx-auto my-4" :text="`You're missing ${percentageOfNsfw}% of your photos!`">
          <p class="text-sm text-black/70 leading-[18px]">
            It looks like these missing photos were deemed unprofessional by our AI detection service. <a href="mailto:<EMAIL>" class="underline">Contact support</a> now to claim a free reshoot.
          </p>
        </AlertWarningV2>
        <div class="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 lg:grid-cols-2 xl:grid-cols-3 gap-4 mt-4">
          <template v-for="photo in photos">
            <GalleryItem
              v-if="!photo?.nsfw && photo?._id"
              :key="photo?._id"
              :photo="photo"
              :selected="selected"
              :selection-screen="true"
              :watermark="!item?.removeWatermarks"
              :still-generating="isStillGenerating"
              @like="toggleSelected(photo?._id)"
              @select="toggleSelected(photo?._id)"
              @preview="openPreview(photo)"
            />
          </template>
          <template v-if="photos.length === 0 && tab === 'keepers'">
            <div
              class="col-span-2 lg:col-span-3 transition rounded-xl overflow-hidden group relative box-content flex flex-col space-y-4 items-center justify-center p-4 h-219px"
              data-testid="gallery-selection-page-empty-state"
            >
              <IconHeart class="w-10 h-10 text-teal-500" />
              <p class="text-sm text-paragraph text-center">
                <span class="font-medium block text-lg text-primary-500 mb-2">You have no keepers yet</span>
                Keepers are incredible profile-worthy photos that you'll want to keep. To get started, select your keepers in the "All photos" tab.
              </p>
            </div>
          </template>
        </div>
      </Card>
      <div class="w-full lg:w-[380px] lg:flex-shrink-0">
        <div class="fixed bottom-0 left-0 z-40 w-full lg:sticky lg:top-20 lg:bottom-auto lg:left-auto space-y-4">
          <Card :class="`${isStillGenerating ? 'opacity-50' : ''}`" data-testid="gallery-selection-page-keepers-card">
            <h3 class="text-[15px] font-medium text-primary-500 pb-4 border-b border-[#E4E4E7CC] hidden lg:block">
              Your keepers
            </h3>
            <div v-if="selectedPhotos.length > 0" class="hidden lg:flex items-center justify-start -space-x-3 lg:mt-5">
              <template v-for="photo in selectedPhotos.slice(0, 6)">
                <ImageDns :key="photo._id" :src="photo.thumbnail || photo.image" class="w-10 h-10 rounded-full shadow-upsell object-cover" />
              </template>
              <template v-for="photo in selectedPhotos.slice(6, 8)">
                <ImageDns :key="photo._id" :src="photo.thumbnail || photo.image" class="w-10 h-10 rounded-full shadow-upsell object-cover hidden xl:block" />
              </template>
              <template v-if="selectedPhotos.length > 6">
                <div class="h-10 w-10 relative xl:hidden">
                  <ImageDns :src="selectedPhotos[6]?.thumbnail || selectedPhotos[6]?.image" class="w-10 h-10 rounded-full shadow-upsell object-cover" />
                  <span class="absolute inset-0 bg-black/60 rounded-full flex items-center justify-center text-xs text-white">+{{ selectedPhotos.length - 6 }}</span>
                </div>
                <div v-if="selectedPhotos.length > 8" class="h-10 w-10 relative hidden xl:block">
                  <ImageDns :src="selectedPhotos[8]?.thumbnail || selectedPhotos[8]?.image" class="w-10 h-10 rounded-full shadow-upsell object-cover" />
                  <span class="absolute inset-0 bg-black/60 rounded-full flex items-center justify-center text-xs text-white">+{{ selectedPhotos.length - 8 }}</span>
                </div>
              </template>
            </div>
            <ButtonPrimary v-if="selected.length === 0" :is-disabled="true" class="lg:mt-5 w-full" data-testid="gallery-selection-page-keepers-card-button-disabled">
              <span>Please select at least one keeper</span>
            </ButtonPrimary>
            <ButtonPrimary v-else class="lg:mt-5 w-full" data-testid="gallery-selection-page-keepers-card-button-enabled" @click="showKeeperModel = true">
              <span>Select {{ selectedPhotos.length }} keepers</span>
              <IconSmallArrow class="w-5 h-5 ml-1.5 text-white" />
            </ButtonPrimary>
            <p class="text-xs text-slate-500 mt-5 text-center">
              You can still access all your photos later on.
            </p>
          </Card>
          <Card class="!hidden lg:!flex">
            <h3 class="font-bold text-primary-500">
              👭 Let your friends vote
            </h3>
            <p class="text-slate-500 mt-2">
              Not sure which ones you like the most? Share the link below with your friends to let them vote on your headshots!
            </p>
            <ButtonWhite v-if="!item?.shareToken" size="sm" class="mt-2" @click="$emit('shareHeadshots')">
              <IconShare class="w-4 h-4 mr-1.5" />
              <span>Create your voting link</span>
            </ButtonWhite>
            <template v-else>
              <div class="bg-gray-100 text-xs rounded-md p-2 mt-2 overflow-hidden overflow-x-scroll">
                {{ link }}
              </div>
              <ButtonWhite size="sm" class="mt-2" @click="copyString(link)">
                <IconCopy class="w-4 h-4 mr-1.5" />
                Copy your voting link
              </ButtonWhite>
            </template>
            <p class="text-xs text-slate-500 mt-2 text-left">
              After picking your keepers, you can access the voting dashboard to see the results.
            </p>
          </Card>
          <Card class="!hidden lg:!flex">
            <h3 class="font-bold text-primary-500">
              ✅ Select the keepers
            </h3>
            <p class="text-slate-500 mt-2">
              You can expect at least 1 incredible profile-worthy headshot from your order — but we let you see the entire batch, so you can decide on the keepers yourself. You can pick as many as you like, there's no limit.
            </p>
          </Card>
          <Card class="!hidden lg:!flex">
            <h3 class="font-bold text-primary-500">
              ❌ Ignore the duds
            </h3>
            <p class="text-slate-500 mt-2">
              In a 📸 real photoshoot, the duds are the photos where your eyes are closed, crossed, or just plain awkward.
            </p>
            <p class="text-slate-500 mt-2">
              In an 🤖 AI photoshoot, the duds are the photos where you have extra limbs, strange scenery, or a strong lack of resemblance.
            </p>
          </Card>
        </div>
      </div>
    </div>
    <Modal v-if="showIntroModal" :show-close-button="false" data-testid="gallery-selection-page-intro-modal">
      <div class="w-full md:w-[500px] p-4 md:p-8 flex flex-col text-gray-700 space-y-3 prose prose-h5:font-medium prose-h5:text-black md:prose-h5:text-lg prose-h5:text-base prose-p:text-sm md:prose-p:text-base prose-p:text-gray-700 prose-bold:text-gray-700">
        <!-- <template v-if="introModelStep === 1"> -->
        <h3 class="font-bold text-xl md:text-2xl text-black m-0 p-0">
          <template v-if="initial">
            Ready to reveal?
          </template>
          <template v-else>
            Your extra photos are ready!
          </template>
        </h3>
        <p class="font-medium text-gray-900 ">
          <template v-if="initial">
            Wait! Here's how to get the most out of your new photos:
          </template>
          <template v-else>
            Like before, select your favorite photos to keep and download.
          </template>
        </p>
        <h5>❌ Ignore the duds.</h5>
        <p><strong>In a 📸 real photoshoot</strong>, the duds are the photos where your eyes are closed, crossed, or just plain awkward. </p>
        <p><strong>In an 🤖 AI photoshoot</strong>, the duds are the photos where you have extra limbs, strange scenery, or a strong lack of resemblance.</p>
        <h5>✅ Select the keepers.</h5>
        <p>You can expect at least 1 incredible profile-worthy headshot from your order &mdash; but we let you see the entire batch, so you can decide on the keepers yourself. <strong>You can pick as many as you like, there's no limit</strong>.</p>
        <p>No more waiting, time to pick your keepers!</p>
        <div class="pt-4 w-full">
          <ButtonPrimary v-if="canContinue" class="w-full" @click="showIntroModal = false">
            <span>Please select at least one keeper</span>
          </ButtonPrimary>
          <ButtonDisabled v-else class="w-full">
            <LoadingSpinnerGray class="mr-1.5" />
            <span>Pick my keepers</span>
          </ButtonDisabled>
        </div>
      </div>
    </Modal>
    <Modal v-if="showStillGeneratingModal" :show-close-button="false">
      <div class="w-full md:w-[500px] p-4 md:p-8 flex flex-col text-gray-700 space-y-3 prose prose-h5:font-medium prose-h5:text-black md:prose-h5:text-lg prose-h5:text-base prose-p:text-sm md:prose-p:text-base prose-p:text-gray-700 prose-bold:text-gray-700">
        <h3 class="font-bold text-xl md:text-2xl text-black m-0 p-0">
          We're still working!
        </h3>
        <p class="font-medium text-gray-900 ">
          Your photos are still being processed, but you can already preview them. We'll let you know when they're ready to be picked!
        </p>
        <div class="pt-4 w-full">
          <ButtonPrimary class="w-full" @click="showStillGeneratingModal = false">
            <span>Preview my headshots</span>
          </ButtonPrimary>
        </div>
      </div>
    </Modal>
    <Modal v-if="showKeeperModel" data-testid="gallery-selection-page-selection-confirmation-modal" @close="showKeeperModel = false;">
      <div class="w-full p-4 md:p-8 space-y-4 text-left md:text-center">
        <div class="text-center space-y-2">
          <h3 class=" text-2xl text-brand-500">
            Confirm your selection
          </h3>
        </div>
        <div class="grid grid-cols-4 md:grid-cols-4 gap-2 bg-gray-100 rounded-md p-2 border border-gray-200 max-h-[calc(33vh)] overflow-hidden overflow-y-scroll">
          <template v-for="photo in selectedPhotos">
            <div v-if="!photo?.nsfw" :key="photo?._id" class="relative" @click="hdPhotoId = photo?._id">
              <ImageDns :src="photo.thumbnail" class="border-2 border-white rounded-md hover:scale-105 transition cursor-pointer" />
              <div v-if="hdPhotoId === photo?._id" class="absolute top-2 right-2 rounded-md bg-yellow-500 px-3 py-1.5 flex items-center justify-center">
                <span class="text-sm text-white font-bold">4K</span>
              </div>
            </div>
          </template>
        </div>
        <LoadingWrapper :is-loading="isConfirming" title="Confirming your photos...">
          <div class="flex flex-col md:flex-row items-center justify-center md:space-x-4 space-y-2 md:space-y-0">
            <ButtonWhite size="sm" class="py-3 md:py-auto w-full md:w-auto" data-testid="gallery-selection-page-selection-confirmation-modal-button-cancel" @click="showKeeperModel = false;">
              <IconCross class="hidden md:inline-flex w-5 h-5 text-black/50 mr-1.5" />
              <span>Re-select photos</span>
            </ButtonWhite>
            <ButtonPrimary size="sm" class="py-3 md:py-auto  w-full md:w-auto" data-testid="gallery-selection-page-selection-confirmation-modal-button" @click="selectInitialFavorites()">
              <span>Confirm photos</span>
              <IconSmallArrow class="hidden md:inline-flex w-5 h-5 text-white ml-1.5" />
            </ButtonPrimary>
          </div>
        </LoadingWrapper>
        <p class="text-sm text-gray-600">
          <template v-if="initial">
            On your confirmation, these {{ selected.length }} photos will be selected as keepers. <strong>The other {{ (activePhotos.length || 0) - (selected?.length || 0) }} photos will still be available.</strong>
          </template>
          <template v-else>
            On your confirmation, these {{ selected.length }} photos will be added to your keepers.
          </template>
        </p>
        <p class="text-xs text-gray-400">
          Not to worry, you can always swap out your keepers in the next results page, they won't be deleted.
        </p>
      </div>
    </Modal>
    <Modal v-if="showPreviewModal" max-width="sm:max-w-5xl" data-testid="gallery-selection-page-preview-modal" @close="showPreviewModal = false; previewImage = null;">
      <GalleryPreviewModal
        :preview-image="previewImage"
        :preview-image-large="highQualityImage"
        :all-photos="photos"
        :selected="selected.includes(openedPhoto?._id)"
        @select="toggleSelected(openedPhoto?._id)"
        @navigate="navigate"
      />
    </Modal>
  </div>
</template>

<script>
export default {
  props: {
    // Check if this is the first time user sees their results
    initial: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      tab: 'all',
      showStillGeneratingModal: false,
      showIntroModal: true,
      showPreviewModal: false,
      showKeeperModel: false,
      isConfirming: false,
      canContinue: false,
      introModelStep: 1,
      hdPhotoId: null,
      previewImage: null,
      highQualityImage: null
    }
  },
  computed: {
    link () {
      if (this.item?.shareToken) {
        return `${process.env.BASE_URL}/vote/${this.item?.shareToken}`
      }
      return null
    },
    percentageOfNsfw () {
      try {
        const totalNsfw = this.item?.totalNsfw || 0
        const totalPhotos = this.item?.images?.length + totalNsfw
        return totalPhotos > 0 ? (totalNsfw / totalPhotos * 100).toFixed(0) : 0
      } catch (e) {
        return 0
      }
    },
    item () {
      return this.$store.state.results.item
    },
    selected: {
      get () { return this.$store.state.results.selected },
      set (value) { this.$store.commit('results/SET_SELECTED', value) }
    },
    activePhotos () {
      const photos = this.item?.images || []
      return photos.filter(photo => photo?.status === 'active') || []
    },
    itemPhotos () {
      return this.activePhotos.filter(photo => photo?.status !== 'disliked')
    },
    sortedPhotos () {
      // Sort liked photos first
      const liked = this.itemPhotos.filter(photo => photo?.likedStatus === 'favorite').sort((a, b) => (b?.meta?.batchNumber || 0) - (a?.meta?.batchNumber || 0)) || []

      // Sort unliked photos by batchNumber (descending order)
      const unliked = this.itemPhotos
        .filter(photo => photo?.likedStatus === 'dud' || photo?.likedStatus === 'none')
        .sort((a, b) => (b?.meta?.batchNumber || 0) - (a?.meta?.batchNumber || 0))

      return [...liked, ...unliked]
    },
    selectedPhotos () {
      return this.itemPhotos.filter(photo => this.selected.includes(photo?._id)) || []
    },
    favoritePhotos () {
      return this.itemPhotos.filter(photo => photo?.likedStatus === 'favorite') || []
    },
    dislikedPhotos () {
      return this.itemPhotos.filter(photo => photo?.likedStatus === 'dud') || []
    },
    unprocessedPhotos () {
      const photos = this.activePhotos.filter(photo =>
        photo?.likedStatus === 'none' &&
        !photo?.nsfw
      ) || []

      return photos.sort((a, b) => {
        const batchA = parseInt(a?.meta?.batchNumber, 10) || 0
        const batchB = parseInt(b?.meta?.batchNumber, 10) || 0
        return batchB - batchA // Sort in descending order (newest first)
      })
    },
    photos () {
      let photos = this.initial ? this.sortedPhotos : this.unprocessedPhotos
      if (this.tab === 'keepers') {
        photos = photos.filter(photo => this.selected.includes(photo?._id))
      }

      return photos
    },
    isStillGenerating () {
      return this.item?.status === 'generatingHeadshots'
    },
    openedPhoto () {
      return this.sortedPhotos.find(photo => photo.preview === this.previewImage || photo.thumbnail === this.previewImage)
    }
  },
  mounted () {
    if (this.isStillGenerating) {
      this.showIntroModal = false
      this.showStillGeneratingModal = true
    } else {
      this.startContinueTimer()
    }
  },

  methods: {
    startContinueTimer () {
      this.canContinue = false
      setTimeout(() => {
        this.canContinue = true
      }, (this.isDevelopment) ? 1 : 5000)
    },
    toggleSelected (photoId) {
      if (this.isStillGenerating) {
        return
      }

      if (this.selected.includes(photoId)) {
        this.selected = this.selected.filter(id => id !== photoId)
      } else {
        this.selected = [...this.selected, photoId]// .push(photoId)
      }
      localStorage.setItem('selected', JSON.stringify(this.selected))
    },
    likePhoto (photoId) {
      const photo = this.itemPhotos.find(photo => photo?._id === photoId)
      if (photo.likedStatus === 'favorite') {
        this.$store.commit('results/SET_LIKED_STATUS', { id: photoId, likedStatus: 'dud' })
      } else {
        this.$toast.open('Liked photos are displayed at the top.')
        this.$store.commit('results/SET_LIKED_STATUS', { id: photoId, likedStatus: 'favorite' })
      }
    },
    selectInitialFavorites () {
      this.isConfirming = true
      if (this.initial) {
        const dislikedPhotoIds = this.activePhotos.filter(photo => !this.selected.includes(photo?._id))?.map(photo => photo?._id) || []
        this.$axios.$post('/model/favorite/set', {
          photoIds: this.selected,
          modelId: this.item?._id,
          hdPhotoId: null, // this.hdPhotoId,
          dislikedPhotoIds
        }).then((response) => {
          if (response.success) {
            this.showKeeperModel = false
            this.$emit('fetch')
            // Scroll to top of page
            window.scrollTo({ top: 0, behavior: 'smooth' })
          }
        }).finally(() => {
          this.isConfirming = false
        })
      } else {
        // Process from unprocessed photos
        const dislikedPhotoIds = this.unprocessedPhotos?.filter(photo => !this.selected.includes(photo?._id))?.map(photo => photo?._id) || []
        this.$axios.$post('/model/favorite/add-extras', {
          photoIds: this.selected,
          modelId: this.item?._id,
          dislikedPhotos: dislikedPhotoIds
        }).then((response) => {
          if (response.success) {
            this.showKeeperModel = false
            this.$emit('fetch')
            // Scroll to top of page
            window.scrollTo({ top: 0, behavior: 'smooth' })
          }
        }).finally(() => {
          this.isConfirming = false
        })
      }
    },
    openPreview (photo) {
      this.previewImage = photo.preview || photo.thumbnail
      this.highQualityImage = photo.image || photo.thumbnail
      this.showPreviewModal = true
    },
    navigate (index) {
      const photo = this.sortedPhotos[index]
      if (photo) {
        this.openPreview(photo)
      }
    }
  }
}
</script>
