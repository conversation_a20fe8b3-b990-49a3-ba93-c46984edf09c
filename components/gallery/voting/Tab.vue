<template>
  <LoadingWrapper :is-loading="isLoading">
    <div class="space-y-8" data-testid="gallery-result-page">
      <div class="w-full">
        <template v-if="!item?.shareToken && votedImages.length === 0">
          <Card>
            <div class="space-y-2">
              <h2 class="text-2xl font-bold">
                Not sure which headshot is your favorite?
              </h2>
              <p class="text-base text-paragraph mt-2">
                Not sure which ones you like the most? Share the link below with your friends to let them vote on your headshots!
              </p>

              <h3 class="text-lg font-bold mt-8">
                How does it work?
              </h3>
              <ul class="text-base text-paragraph space-y-2 mt-2">
                <li class="flex justify-start items-center gap-2">
                  <IconSolidCheck class="w-6 h-6 text-green-500" />
                  <p>
                    Share the link with your friends
                  </p>
                </li>
                <li class="flex justify-start items-center gap-2">
                  <IconSolidCheck class="w-6 h-6 text-green-500" />
                  <p>
                    Your friends will be able to choose 3 headshots for you
                  </p>
                </li>
                <li class="flex justify-start items-center gap-2">
                  <IconSolidCheck class="w-6 h-6 text-green-500" />
                  <p>
                    You can see the results in the voting results page
                  </p>
                </li>
              </ul>
              <LoadingWrapper v-if="!item?.shareToken" :is-loading="isLoading" class="mt-8 space-y-4">
                <ButtonGradient class="mt-8" @click="shareHeadshots">
                  <IconShare class="w-4 h-4 mr-1.5" />
                  <span>Create your voting link</span>
                </ButtonGradient>
              </LoadingWrapper>
              <div v-else class="mt-8 space-y-4">
                <div class="mt-8 bg-gray-100 p-4 rounded-md text-base text-paragraph cursor-pointer text-center" @click="copyLink">
                  {{ link }}
                </div>

                <div class="flex flex-col sm:flex-row sm:items-center justify-center gap-4 mt-8">
                  <ButtonWhite class="gap-2 w-full sm:w-auto" @click="copyLink">
                    <IconCopy class="w-4 h-4" />
                    <span>Copy link</span>
                  </ButtonWhite>
                </div>
              </div>
            </div>
          </Card>
        </template>
        <template v-else>
          <div class="flex w-full space-x-2 items-center justify-start">
            <ButtonWhite v-if="item?.shareToken" size="sm" @click="copyString(link)">
              <IconShare class="w-4 h-4 mr-1.5" />
              <span class="hidden sm:block">Copy voting link</span>
              <span class="block sm:hidden">Copy link</span>
            </ButtonWhite>
            <ButtonWhite v-if="item?.shareToken" size="sm" @click="showStopSharingModal = true">
              <IconSolidStop class="w-4 h-4 mr-1.5" />
              <span>Stop sharing</span>
            </ButtonWhite>
            <ButtonWhite v-if="!item?.shareToken" size="sm" @click="shareHeadshots">
              <IconShare class="w-4 h-4 mr-1.5" />
              <span class="hidden sm:block">Create sharable link</span>
              <span class="block sm:hidden">Create link</span>
            </ButtonWhite>
            <template v-if="!item?.shareToken">
              <span class="text-sm text-gray-400 italic hidden sm:block">You are currently not sharing your headshots</span>
              <span class="text-sm text-gray-400 italic block sm:hidden">Sharing is disabled</span>
            </template>
          </div>
          <div class="space-y-6 mt-6">
            <Card v-if="uniqueVoters === 0">
              <img src="@/static/undraw/undraw_upvote_frrh.svg" class="w-full max-w-xs h-auto block mx-auto">
              <p class="text-center text-sm text-paragraph mt-4">
                No one has voted yet. Refresh the page to see the results when they come in.
              </p>
              <p class="text-center text-sm text-paragraph mt-4">
                Your shareable link is: <button class="text-blue-500" @click="copyString(link)">
                  {{ link }}
                </button> <span class="italic">(click to copy)</span>
              </p>
            </Card>
            <template v-else>
              <div class="grid grid-cols-2 gap-4 sm:grid-cols-2 xs:gap-8 lg:grid-cols-4">
                <CardWithIcon icon="IconUserGroup" title="Voters" :value="formatNumber(uniqueVoters)" />
                <CardWithIcon icon="IconChartBar" title="Votes" :value="formatNumber(numberOfVotes)" />
                <CardWithIcon icon="IconPhoto" title="Voted images" :value="formatNumber(uniqueVotedImages)" />
                <CardWithIcon icon="IconTrophy" title="Most votes" :value="formatNumber(maxVotesInOneImage)" />
              </div>
            </template>
          </div>

          <GalleryPhotoListCard
            class="mt-8"
            title="Top voted images"
            icon="IconTrophy"
            :photos="votedImages"
            description="These are the images that received the most votes. You can mark them as keepers or duds if you want."
            :icons="['keepers']"
          />

          <GalleryPhotoListCard
            class="mt-8"
            title="Unvoted images"
            icon="IconPhoto"
            :photos="unvotedImages"
            description="These are the images that received no votes. That doesn't mean they're not good, but voters could only choose between 3 photos!"
            :icons="['keepers']"
          />
        </template>
      </div>
      <Popup v-if="showStopSharingModal" size="sm" @closeModal="showStopSharingModal = false">
        <div class="space-y-2">
          <h2 class="text-lg font-medium text-gray-900">
            Stop sharing?
          </h2>
          <p class="text-sm text-gray-500">
            Are you sure you want to stop sharing your voting results? This will stop the voting link from working and your friends will no longer be able to vote. You can still see the results.
          </p>
          <LoadingWrapper :is-loading="isDeleting">
            <div class="flex items-center justify-start gap-2 mt-4">
              <ButtonDelete size="sm" @click="deleteShareToken">
                Stop sharing
              </ButtonDelete>
              <ButtonWhite size="sm" @click="showStopSharingModal = false">
                Cancel
              </ButtonWhite>
            </div>
          </LoadingWrapper>
        </div>
      </Popup>
    </div>
  </LoadingWrapper>
</template>

<script>
import copy from 'copy-to-clipboard'
export default {
  layout: 'protected',
  data () {
    return {
      isLoading: false,
      showStopSharingModal: false,
      isDeleting: false
    }
  },
  head () {
    return {
      title: 'Your voting results | HeadshotPro'
    }
  },
  computed: {
    shareToken () {
      return this.item?.shareToken
    },
    link () {
      return `${process.env.BASE_URL}/vote/${this.shareToken}`
    },
    item () {
      return this.$store.state.results.item
    },
    activeImages () {
      return this.item?.images?.filter(photo => photo?.status === 'active') || []
    },
    uniqueVoters () {
      const voters = {}
      this.activeImages.forEach((photo) => {
        if (photo.voters) {
          photo.voters.forEach((voter) => {
            voters[voter] = true
          })
        }
      })

      return Object.keys(voters).length
    },
    numberOfVotes () {
      return this.activeImages.reduce((acc, photo) => acc + (photo.votes || 0), 0)
    },
    uniqueVotedImages () {
      return this.activeImages.filter(photo => photo.votes > 0).length
    },
    maxVotesInOneImage () {
      return Math.max(...this.activeImages.map(photo => photo.votes || 0))
    },
    votedImages () {
      const images = this.activeImages.filter(photo => photo.votes > 0)
      images.sort((a, b) => b.votes - a.votes)
      return images
    },
    unvotedImages () {
      return this.activeImages.filter(photo => photo.votes === 0 || !photo.votes)
    }
  },
  mounted () {
    // Scroll to the top
    window.scrollTo(0, 0)
  },
  methods: {
    deleteShareToken () {
      this.isDeleting = true
      this.$axios.$delete(`/model/voting/${this.item._id}/share`).then((response) => {
        if (response.success) {
          this.showStopSharingModal = false
          this.$store.commit('results/UPDATE_ITEM', { shareToken: null })
          this.$toast.success('Stopped sharing your results. No one can access them anymore.')
        } else {
          this.$toast.error('Something went wrong while stopping the sharing. Please try again later.')
        }
      }).catch((err) => {
        this.handleError(err)
      }).finally(() => {
        this.isDeleting = false
      })
    },
    async shareHeadshots () {
      try {
        this.isLoading = true
        const response = await this.$axios.$post(`/model/voting/${this.item._id}/share`)
        if (!response.success) {
          this.$toast.error('Something went wrong while sharing your headshots. Please try again later.')
          return
        }

        this.$store.commit('results/UPDATE_ITEM', { shareToken: response.data.shareToken })
        this.$toast.success('Voting link created and copied to clipboard.')
        copy(this.link)
        this.isLoading = false
      } catch (e) {
        this.isLoading = false
        this.handleError(e)
      }
    }
  }
}
</script>

  <style></style>
