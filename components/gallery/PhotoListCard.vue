<template>
  <Card data-testid="photo-list-card">
    <header class="flex gap-4 relative flex-col xs:flex-row xs:items-center xs:justify-between">
      <div class="text-paragraph text-left">
        <div class="flex items-center space-x-2">
          <component :is="icon" class="w-6 h-6" />
          <h2 class="text-lg font-semibold text-primary-500" data-testid="photo-list-card-title">
            {{ title }}
          </h2>
        </div>
        <div v-if="description?.component" class="mt-2 xs:mt-1" data-testid="photo-list-card-description">
          <component :is="description.component" v-bind="description.props" />
        </div>
        <p v-else-if="description" class="text-sm text-slate-500 mt-2" data-testid="photo-list-card-description">
          {{ description }}
        </p>
      </div>
      <div class="order-first absolute right-0 md:top-0 top-[-4px]  xs:relative xs:order-none">
        <ButtonDropdown
          v-if="photos.length > 0"
          theme="v1"
          title="Actions"
          :items="actions"
          size="sm"
          @select="handleAction"
        />
      </div>
    </header>
    <div v-if="photos.length > 0 || (showNsfw && item?.totalNsfwNew > 0)" class="grid grid-cols-1 xs:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mt-4">
      <div v-for="(photo, index) in photos" :key="photo._id" class="contents">
        <UpsellResultCard v-if="upsellIndex === index" :item="item" />
        <GalleryPhotoCard
          :photo="photo"
          :icons="icons"
          @preview="previewPhoto(photo)"
        />
        <VotingPromotionCard
          v-if="showVotingPromotion(index)"
          class="mt-4"
        />
      </div>
      <template v-if="showNsfw && item?.totalNsfwNew > 0">
        <GalleryNsfwPhotoCard
          v-for="i in item?.totalNsfwNew"
          :key="i"
        />
      </template>
    </div>
    <div v-else class="flex items-center justify-center min-h-24 mt-2" data-testid="photo-list-card-empty-state">
      <component :is="emptyState?.component" v-if="emptyState?.component" class="w-full" v-bind="emptyState?.props" />
      <p v-else class="text-sm text-gray-500">
        {{ emptyState || 'No photos found' }}
      </p>
    </div>
    <Modal v-if="showPreviewModal" max-width="sm:max-w-5xl" data-testid="photo-list-card-preview-modal" @close="closePreviewModal">
      <GalleryPreviewModal :preview-image="previewImage" :preview-image-large="previewImageLarge" :all-photos="photos" />
    </Modal>
    <Popup v-if="showDownloadModal" size="lg" data-testid="photo-list-card-download-modal" @close="closeDownloadModal">
      <div class="space-y-2">
        <template v-if="!showDownloadSuccessMessage && !showDownloadErrorMessage">
          <div class="flex flex-col gap-0.5">
            <Heading class="!font-medium">
              Download your headshots
            </Heading>
            <Paragraph size="sm">
              Enter your email to start the download process.
            </Paragraph>
          </div>
          <Input v-model="email" placeholder="Enter your email" />
          <ButtonPrimary @click="startDownload">
            Start download
          </ButtonPrimary>
        </template>
        <template v-else-if="showDownloadSuccessMessage">
          <div class="flex flex-col gap-0.5">
            <Heading class="!font-medium">
              ✅ Download started
            </Heading>
            <Paragraph size="sm">
              You'll receive a download link in your inbox ({{ email }}) within 1 to 10 minutes depending on the size of the download. You can close this window now.
            </Paragraph>
          </div>
        </template>
        <template v-else-if="showDownloadErrorMessage">
          <div class="flex flex-col gap-0.5">
            <Heading class="!font-medium">
              ❌ Download failed
            </Heading>
            <Paragraph size="sm">
              Something went wrong. Please try again later or contact support of this issue keeps happening.
            </Paragraph>
          </div>
        </template>
      </div>
    </Popup>
  </Card>
</template>

<script>
import DownloadTrackingMixin from '../../mixins/DownloadTrackingMixin'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
import Heading from '@/components/landingpage/common/H5.vue'

export default {
  components: {
    Paragraph,
    Heading
  },
  mixins: [DownloadTrackingMixin],
  props: {
    title: {
      type: String,
      required: true
    },
    description: {
      required: false
    },
    icon: {
      type: String,
      required: true
    },
    emptyState: {
      required: false
    },
    photos: {
      type: Array,
      required: true
    },
    icons: {
      type: Array,
      required: false
    },
    upsellIndex: {
      type: Number,
      required: false
    },
    item: {
      type: Object,
      required: false
    },
    showNsfw: {
      type: Boolean,
      required: false
    },
    identifier: {
      type: String,
      required: false
    }
  },
  data () {
    return {
      showPreviewModal: false,
      previewImage: null,
      previewImageLarge: null,
      showDownloadModal: false,
      wantFeedback: false,
      showDownloadSuccessMessage: false,
      showDownloadErrorMessage: false,
      email: null
    }
  },
  computed: {
    feedbackModal () {
      return this.$store.state.showReviewRequestModal
    },
    actions () {
      const baseActions = [
        {
          title: 'Download all',
          value: 'download',
          icon: 'IconCloudDownload'
        }
      ]

      // // Add Download PFPs if user is organization member and photos have PFP versions
      // if (this.hasPfpPhotos) {
      //   baseActions.push({
      //     title: 'Download PFPs',
      //     value: 'download-pfps',
      //     icon: 'IconCloudDownload'
      //   })
      // }

      return baseActions
    },
    hasPfpPhotos () {
      return this.photos.some(photo => photo.pfp)
    },
    showVotingPromotion () {
      return (index) => {
        return this.identifier === 'new-photos' && index === 2 && !this.item?.shareToken && !this.$route.fullPath.includes('admin')
      }
    }
  },
  watch: {
    feedbackModal (newVal) {
      if (this.showDownloadModal && newVal) {
        this.$store.commit('SET_SHOW_REVIEW_REQUEST_MODAL', false)
        this.wantFeedback = true
      }
    }
  },
  mounted () {
    this.email = this.$store.state.user?.email || null
  },
  methods: {
    previewPhoto (photo) {
      this.previewImage = photo?.small || photo?.image || photo?.thumbnail
      this.showPreviewModal = true
      this.previewImageLarge = photo?.image
    },
    closePreviewModal () {
      this.showPreviewModal = false
      this.previewImage = null
      this.previewImageLarge = null
    },
    closeDownloadModal () {
      this.showDownloadModal = false
      if (this.wantFeedback) {
        this.$store.commit('SET_SHOW_REVIEW_REQUEST_MODAL', true)
      }
    },

    handleAction (action) {
      if (action === 'download') {
        // this.downloadAll()
        this.showDownloadModal = true
      }
    },

    startDownload () {
      // Validate email before proceeding
      if (!this.email || !this.email.trim()) {
        this.$toast.error('Please enter your email address')
        return
      }

      if (!this.isValidEmail(this.email)) {
        this.$toast.error('Please enter a valid email address')
        return
      }

      this.$loading.show({
        text: 'Creating download...'
      })

      const sfwImages = this.photos.filter(photo => !photo.nsfw)
      this.$axios.$post('/model/download/zip/create', {
        modelId: this.item._id,
        urls: sfwImages.map(photo => photo.image),
        email: this.email,
        photoIds: sfwImages.map(photo => photo._id)
      }).then((response) => {
        if (response?.success) {
          this.showDownloadSuccessMessage = true
        } else {
          this.showDownloadErrorMessage = true
          this.$toast.error(response?.errorMessage || 'Failed to create download')
        }
      }).catch((err) => {
        this.showDownloadErrorMessage = true
        this.$toast.error('Failed to create download')
        console.log(err)
      }).finally(() => {
        this.$loading.hide()
      })
    }

  }
}
</script>
