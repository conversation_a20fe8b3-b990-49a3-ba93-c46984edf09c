<template>
  <Popup size="lg" @closeModal="$emit('close')">
    <div class="flex flex-col gap-4">
      <div class="flex flex-col gap-0.5">
        <Heading>
          Share results
        </Heading>
        <Paragraph v-if="shareToken" size="sm">
          Copy the link below to share the result of this photoshoot.
        </Paragraph>
        <Paragraph v-else size="sm">
          Share the result of this photoshoot. Be aware, they get full access to your photos.
          <strong>Want to just share a few?</strong> Use the voting feature in the sidebar instead.
        </Paragraph>
      </div>
      <template v-if="shareToken">
        <!-- Has share token setup -->
        <div class="flex flex-col sm:flex-row sm:items-center justify-start gap-2">
          <ButtonWhite size="sm" class="gap-2 w-full sm:w-auto" @click="copyLink">
            <IconCopy class="w-4 h-4" />
            <span>Copy link</span>
          </ButtonWhite>
          <ButtonDelete size="sm" @click="deleteShareToken">
            Stop sharing
          </ButtonDelete>
        </div>
      </template>
      <template v-else>
        <div class="flex flex-col sm:flex-row sm:items-center justify-start gap-2">
          <ButtonPrimary size="sm" @click="shareHeadshots">
            <IconShare class="w-4 h-4 mr-1.5" />
            <span class="hidden sm:block">Create sharable link</span>
            <span class="block sm:hidden">Create link</span>
          </ButtonPrimary>
        </div>
      </template>
      <Paragraph size="xs">
        <strong>Note:</strong> Duds are excluded from the sharable link, so if you want to hide certain photos, make sure to mark them as duds.
      </Paragraph>
    </div>
  </Popup>
</template>

<script>
import copy from 'copy-to-clipboard'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
import Heading from '@/components/landingpage/common/H5.vue'
export default {
  components: {
    Paragraph,
    Heading
  },
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  computed: {
    shareToken () {
      return this.item?.shareToken
    },
    link () {
      return `${process.env.BASE_URL}/app/results/share/${this.shareToken}`
    }
  },
  methods: {
    copyLink () {
      if (this.link && this.shareToken) {
        copy(this.link)
        this.$toast.success('Link copied!')
      }
    },
    deleteShareToken () {
      this.$loading.show({
        title: 'Stopping sharing...'
      })
      this.$axios.$delete(`/model/voting/${this.item._id}/share`).then((response) => {
        if (response.success) {
          this.$store.commit('results/UPDATE_ITEM', { shareToken: null })
          this.$toast.success('Stopped sharing your results. No one can access them anymore.')
        } else {
          this.$toast.error('Something went wrong while stopping the sharing. Please try again later.')
        }
      }).catch((err) => {
        this.handleError(err)
      }).finally(() => {
        this.$loading.hide()
      })
    },
    async shareHeadshots () {
      try {
        this.$loading.show({
          title: 'Creating sharable link...'
        })
        const response = await this.$axios.$post(`/model/voting/${this.item._id}/share`)
        if (!response.success) {
          this.$toast.error('Something went wrong while sharing your headshots. Please try again later.')
          return
        }

        this.$store.commit('results/UPDATE_ITEM', { shareToken: response.data.shareToken })
        this.$toast.success('Voting link created and copied to clipboard.')
        copy(this.link)
      } catch (e) {
        this.handleError(e)
      } finally {
        this.$loading.hide()
      }
    }
  }
}
</script>

<style>

</style>
