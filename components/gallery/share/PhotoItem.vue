<template>
  <div
    class="transition group relative box-content cursor-pointer"
    data-testid="photo-card"
    :data-photo-id="photo._id"
  >
    <ImageDns v-if="photoIsActive" :src="photo.thumbnail || photo.image" class="rounded-xl overflow-hidden w-full aspect-result transition duration-300 object-cover" />
    <ProfilePicture
      v-if="hasPfp"
      ref="profilePicture"
      class="absolute bottom-2 left-2"
      :pfp-url="photo.pfp"
      :branding="photo?.branding || null"
      :photo-id="photo._id"
    />
    <template v-if="photoIsActive">
      <div class="group-hover:opacity-100 rounded-xl transition-opacity duration-300 absolute inset-0 opacity-0 flex items-end justify-center p-3">
        <div class="hidden sm:block absolute top-0 left-0 w-full h-full bg-black/60 rounded-xl" @click="handleAction('preview')" />
        <div class="block sm:hidden absolute top-0 left-0 w-full h-full bg-black/60 rounded-xl" />
        <div class="w-full justify-between relative hidden sm:flex">
          <button
            type="button"
            class="h-[38px] w-[41px] rounded-md bg-primary-500 flex items-center justify-center border border-white/20 shadow-[0px_1px_2px_rgba(0,0,0,0.05)]"
            :class="{ 'animate-pulse': isDownloading }"
            data-testid="photo-card-download-button-desktop"
            @click="handleAction('download')"
          >
            <IconCloudDownload class="w-4 h-4 text-white" />
          </button>
          <ButtonDropdown
            theme="v1"
            title="Actions"
            :items="actions"
            size="sm"
            icon="IconEllipsisVertical"
            icon-position="before"
            data-testid="photo-card-actions-dropdown-desktop"
            @select="handleAction"
          />
        </div>
        <div class="w-full h-full justify-center items-center relative flex flex-col gap-4 sm:hidden pointer-events-none group-hover:pointer-events-auto">
          <ButtonPrimary size="base" class="w-full" data-testid="photo-card-download-button-mobile" @click="handleAction('download')">
            <IconCloudDownload class="w-4 h-4 text-white mr-2 hidden sm:block" />
            <span class="hidden sm:block">Download photo</span>
            <span class="block sm:hidden">Download</span>
          </ButtonPrimary>
          <ButtonDropdown
            full-width
            theme="v1"
            title="All actions"
            :items="actions"
            size="base"
            icon="IconEllipsisVertical"
            icon-position="before"
            data-testid="photo-card-actions-dropdown-mobile"
            @select="handleAction"
          />
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import ProfilePicture from '@/components/gallery/ProfilePicture.vue'
export default {
  components: {
    ProfilePicture
  },
  props: {
    photo: {
      type: Object,
      required: true
    }
  },
  computed: {
    photoIsActive () {
      return this.photo?.status === 'active'
    },
    hasPfp () {
      return this.photo?.pfp
    },
    actions () {
      let actions = [
        { title: 'Preview', value: 'preview', icon: 'IconEye' },
        { title: 'Download', value: 'download', icon: 'IconCloudDownload' }
        // { title: 'Mark as keeper', value: 'like', icon: 'IconHeart' },
        // { title: 'Mark as dud', value: 'dislike', icon: 'IconSignalSlash' },
      ]

      actions = [
        ...actions,
        ...(this.hasPfp ? [{ title: 'Download PFP', value: 'download-pfp', icon: 'IconSolidUserCircle' }] : [])
      ]

      return actions
    }
  },
  methods: {
    handleAction (value) {
      switch (value) {
        case 'preview':
          this.previewPhoto()
          break
        // case 'like':
        //   this.markAsFavorite()
        //   break
        // case 'dislike':
        //   this.markAsDud()
        //   break
        // case 'loved':
        //   this.markAsLoved()
        //   break
        // case 'edit':
        //   this.editPicture()
        //   break
        case 'download':
          this.downloadPhoto()
          break
        case 'download-pfp':
          this.downloadPfp()
          break
      }
    },
    previewPhoto () {
      this.$emit('preview')
    },
    downloadPfp () {
      if (this.$refs.profilePicture) {
        this.$refs.profilePicture.downloadPfp()
      }
    },
    downloadPhoto () {
      if (this.isDownloading) {
        return
      }

      this.isDownloading = true
      // Get the url from the photoId
      const url = this.photo?.retouched || this.photo?.upscaled || this.photo?.image
      this.$toast.success('Your image will download shortly. Make sure to accept the download prompt.')
      fetch(url)
        .then(response => response.blob())
        .then((blob) => {
          const url = window.URL.createObjectURL(new Blob([blob]))
          const link = document.createElement('a')
          link.href = url
          const fileType = blob.type.split('/')[1]
          const fileName = `${this.photo._id}-HeadshotPro.${fileType}`
          link.setAttribute('download', fileName)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          this.trackDownloads(this.photo.modelId, [this.photo._id])
          setTimeout(() => {
            this.$store.commit('results/SET_DOWNLOADED', [this.photo._id])
          }, 1000)
        })
        .finally(() => {
          this.isDownloading = false
        })
    }
  }

}
</script>

<style>

</style>
