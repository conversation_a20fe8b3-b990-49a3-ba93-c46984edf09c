<template>
  <LoadingWrapper :is-loading="isLoading">
    <template v-if="!isLoading && !referralLink">
      <Card>
        <div class="space-y-2 text-left">
          <h1 class="text-xl font-bold text-primary-500">
            There was an error fetching your referral data.
          </h1>
          <p class="text-sm text-paragraph">
            Please try again later or contact our support.
          </p>
        </div>
      </Card>
    </template>
    <template v-else>
      <div class="space-y-8" data-testid="gallery-referral-tab">
        <div class="w-full">
          <Card>
            <div class="space-y-6">
              <div class="flex flex-col sm:flex-row md:flex-col lg:flex-row gap-4 items-center">
                <p class="font-bold text-2xl text-primary-500 flex-shrink-0">
                  How it works
                </p>
                <div class="flex-1 w-full cursor-pointer bg-[#FAFAFA] border border-gray-300 px-3 py-2 leading-none font-medium text-gray-700 rounded-md line-clamp-1" @click="copyReferralLink">
                  {{ referralLink }}
                </div>
                <ButtonGradient
                  size="sm"
                  class="whitespace-nowrap flex-shrink-0"
                  @click="copyReferralLink"
                >
                  Copy Referral Link
                </ButtonGradient>
              </div>

              <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-1 lg:grid-cols-3 gap-16">
                <div class="text-left relative mx-auto max-w-sm">
                  <div class="space-y-4">
                    <Card>
                      <img src="@/assets/img/referral1.png" alt="" class="w-full aspect-[1.55] object-contain">
                    </Card>
                    <div class="flex flex-col space-y-1">
                      <h3 class="text-lg font-semibold text-primary-500">
                        Create your referral link
                      </h3>
                      <p class="text-sm font-medium text-paragraph">
                        We'll generate a unique link you can share with friends.
                      </p>
                    </div>
                  </div>
                  <div class="absolute -top-3 border-2 border-white left-0 right-0 w-8 h-8 bg-primary-500 text-white rounded-full flex items-center justify-center mx-auto font-semibold">
                    1
                  </div>
                </div>

                <div class="text-left relative mx-auto max-w-sm">
                  <div class="space-y-4">
                    <Card>
                      <img src="@/assets/img/referral2.png" alt="" class="w-full aspect-[1.55] scale-100 sm:scale-[1.23] md:scale-100 lg:scale-[1.23]  object-contain">
                    </Card>
                    <div class="flex flex-col space-y-1">
                      <h3 class="text-lg font-semibold text-primary-500">
                        Your friend gets $9 off
                      </h3>
                      <p class="text-sm font-medium text-paragraph">
                        They save on their first purchase — no code needed.
                      </p>
                    </div>
                  </div>
                  <div class="absolute -top-3 border-2 border-white left-0 right-0 w-8 h-8 bg-primary-500 text-white rounded-full flex items-center justify-center mx-auto font-semibold">
                    2
                  </div>
                </div>

                <div class="text-left relative mx-auto max-w-sm">
                  <div class="space-y-4">
                    <Card>
                      <img src="@/assets/img/referral3.png" alt="" class="w-full aspect-[1.55] object-contain">
                    </Card>
                    <div class="flex flex-col space-y-1">
                      <h3 class="text-lg font-semibold text-primary-500">
                        You get 20 free headshots
                      </h3>
                      <p class="text-sm font-medium text-paragraph">
                        We'll add the credits once your friend completes their order.
                      </p>
                    </div>
                  </div>
                  <div class="absolute -top-3 border-2 border-white left-0 right-0 w-8 h-8 bg-primary-500 text-white rounded-full flex items-center justify-center mx-auto font-semibold">
                    3
                  </div>
                </div>
              </div>
            </div>
          </Card>

          <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
            <Card inner-class="flex items-center justify-between">
              <div class="space-y-2">
                <h3 class="text-lg font-medium text-paragraph">
                  Your invites
                </h3>
                <span class="text-2xl font-bold text-primary-500">{{ stats.totalRefers }} friends joined</span>
              </div>
              <div>
                <IconUserGroup class="w-16 h-16 text-paragraph" />
              </div>
            </Card>

            <Card inner-class="flex items-center justify-between">
              <div class="space-y-2">
                <h3 class="text-lg font-medium text-paragraph">
                  Your rewards
                </h3>
                <span class="text-2xl font-bold text-primary-500">{{ stats.totalRewards }} credits</span>
              </div>
              <div>
                <IconCurrencyDollar class="w-16 h-16 text-paragraph" />
              </div>
            </Card>
          </div>
          <Card class="mt-8">
            <h4 class="text-lg font-bold mb-2 text-primary-500">
              Your Referral Activity
            </h4>
            <p class="text-sm text-paragraph mb-4">
              Track who signed up, when, and how many rewards you've earned.
            </p>

            <div v-if="stats.referredUsers && stats.referredUsers.length > 0" class="overflow-x-auto">
              <Table :head="['Username', 'Status', 'Date']" class="min-w-full">
                <TableRow v-for="user in stats.referredUsers" :key="user.id">
                  <TableItem>
                    <div class="font-medium text-gray-900">
                      {{ user.displayName }}
                    </div>
                    <div class="text-gray-500">
                      {{ user.email }}
                    </div>
                  </TableItem>
                  <TableItem>
                    <div class="flex items-center justify-start">
                      <div v-if="user.type === 'paid'" class="bg-green-50 py-2 px-2 gap-2 flex items-center justify-center rounded-full text-[#16A34A] font-medium">
                        <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M0.875 9C0.875 4.5125 4.5125 0.875 9 0.875C13.4875 0.875 17.125 4.5125 17.125 9C17.125 13.4875 13.4875 17.125 9 17.125C4.5125 17.125 0.875 13.4875 0.875 9ZM12.0083 7.48833C12.0583 7.42171 12.0945 7.34576 12.1147 7.26496C12.135 7.18415 12.1388 7.10012 12.1261 7.0178C12.1134 6.93547 12.0844 6.85652 12.0407 6.78558C11.9971 6.71464 11.9396 6.65315 11.8719 6.60471C11.8041 6.55627 11.7273 6.52187 11.6461 6.50352C11.5648 6.48518 11.4807 6.48326 11.3987 6.49789C11.3167 6.51251 11.2385 6.54338 11.1686 6.58868C11.0987 6.63398 11.0385 6.69279 10.9917 6.76167L8.295 10.5367L6.94167 9.18333C6.82319 9.07293 6.66648 9.01283 6.50456 9.01569C6.34265 9.01854 6.18816 9.08414 6.07365 9.19865C5.95914 9.31316 5.89354 9.46765 5.89069 9.62956C5.88783 9.79148 5.94793 9.94819 6.05833 10.0667L7.93333 11.9417C7.99749 12.0058 8.07483 12.0552 8.15999 12.0864C8.24515 12.1176 8.33608 12.1299 8.42647 12.1224C8.51686 12.115 8.60455 12.088 8.68344 12.0432C8.76233 11.9985 8.83054 11.9371 8.88333 11.8633L12.0083 7.48833Z" fill="#22C55E" />
                        </svg>
                        Purchased
                      </div>
                      <div v-else class="bg-slate-50 py-2 px-2 gap-2 flex items-center justify-center rounded-full text-slate-500 font-medium group relative">
                        <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M9 0.875C4.5125 0.875 0.875 4.5125 0.875 9C0.875 13.4875 4.5125 17.125 9 17.125C13.4875 17.125 17.125 13.4875 17.125 9C17.125 4.5125 13.4875 0.875 9 0.875ZM9.625 4C9.625 3.83424 9.55915 3.67527 9.44194 3.55806C9.32473 3.44085 9.16576 3.375 9 3.375C8.83424 3.375 8.67527 3.44085 8.55806 3.55806C8.44085 3.67527 8.375 3.83424 8.375 4V9C8.375 9.345 8.655 9.625 9 9.625H12.75C12.9158 9.625 13.0747 9.55915 13.1919 9.44194C13.3092 9.32473 13.375 9.16576 13.375 9C13.375 8.83424 13.3092 8.67527 13.1919 8.55806C13.0747 8.44085 12.9158 8.375 12.75 8.375H9.625V4Z" fill="#94A3B8" />
                        </svg>
                        Signed up
                        <div class="hidden z-[100] text-xs text-white font-medium group-hover:flex w-[200px] px-2 py-1 leading-tight bg-[#0F172A] shadow-2xl rounded-md absolute bottom-full mb-0 left-1/2 -translate-x-1/2 text-center">
                          You'll be credited for a referral once they make a purchase!
                        </div>
                      </div>
                    </div>
                  </TableItem>
                  <TableItem class="text-right">
                    {{ formatDate(user.joinedAt) }}
                  </TableItem>
                </TableRow>
              </Table>
            </div>

            <div v-else class="text-center p-8 bg-[#FAFAFA] rounded-lg">
              <div class="max-w-md mx-auto">
                <p class="text-center text-lg text-black font-semibold">
                  No signups yet — but you're just getting started!
                </p>
                <p class="text-center text-paragraph mt-2">
                  Share your referral link to invite friends and start earning 20 free headshots for each successful signup.
                </p>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </template>
  </LoadingWrapper>
</template>

<script>
import copy from 'copy-to-clipboard'

export default {
  data () {
    return {
      isLoading: false,
      referralLink: '',
      stats: {
        totalRefers: 0,
        referralCode: '',
        referredUsers: []
      }
    }
  },
  mounted () {
    if (this.isTeamMember) {
      this.$router.push('/app')
      return
    }

    this.$posthog.capture('$upsell:referral_page')
    this.fetchReferralData()
      .then(() => {
        // non blocking
      })
  },
  methods: {
    async fetchReferralData () {
      this.isLoading = true
      try {
        // Fetch referral link
        const linkResponse = await this.$axios.$get('/referral/link')
        if (linkResponse.success) {
          this.referralLink = linkResponse.data.referralLink
        }

        // Fetch referral stats
        const statsResponse = await this.$axios.$get('/referral/stats')
        if (statsResponse.success) {
          this.stats = statsResponse.data
        }
      } catch (error) {
        console.error('Error fetching referral data:', error)
        this.handleError(error)
      } finally {
        this.isLoading = false
      }
    },
    copyReferralLink () {
      copy(this.referralLink)
      this.$posthog.capture('$upsell:referral_link_copied')
      this.$toast.success('Referral link copied to clipboard!')
    }
  }
}
</script>

<style scoped>
/* Add any specific styles if needed */
</style>
