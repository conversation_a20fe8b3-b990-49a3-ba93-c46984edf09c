<template>
  <div
    class="w-full min-h-screen space-y-8 "
    data-testid="gallery-result-page"
    :class="{
      'max-w-8xl mx-auto pt-4 md:pt-8 px-4 pb-8': theme === 'default',
      'pb-20 md:pb-8': theme === 'default' // Extra bottom padding on mobile for sticky nav
    }"
  >
    <div
      class="w-full flex flex-col gap-[32px]"
      :class="{
        'md:flex-row': theme === 'default'
      }"
    >
      <div v-if="theme === 'default'" class="hidden md:block w-full md:w-[234px] flex-shrink-0">
        <div class="sticky top-20 hidden md:block">
          <div class="md:w-[192px] space-y-0">
            <GalleryResultSidebar :tabs="tabs" :selected-tab="tab" @select="moveToTab($event)" />
          </div>
          <UpsellCard v-if="shouldShowUpsell" :item="item" class="mt-4" />
        </div>
        <!-- Mobile tab navigation moved to bottom sticky bar -->
      </div>
      <div class="w-full">
        <div class="flex flex-col px-2 md:px-0">
          <h1 class="text-xl sm:text-[25px] font-bold text-brand-500" data-testid="gallery-result-page-heading">
            {{ heading }}
          </h1>
          <p class="text-sm sm:text-base text-slate-500 mt-2" data-testid="gallery-result-page-description">
            {{ description }}
          </p>
        </div>

        <div v-if="theme === 'segmented'" class="w-full mt-4 flex items-center justify-between gap-4">
          <Segment :key="tab" :items="tabs.map(t => ({ label: t.title, value: t.identifier }))" :initial-active="tab" @selected="moveToTab($event)" />
          <!-- Used for teams who want to share their results with their team without creating an account -->
          <ButtonPrimary v-if="$route.fullPath.includes('/admin/results')" size="sm" @click="showShareResultsModal = true">
            <IconShare class="w-4 h-4 text-white/70 mr-1.5" />
            <span v-if="item?.shareToken">Create share link</span>
            <span v-else>Copy share link</span>
          </ButtonPrimary>
          <GalleryShareModal v-if="showShareResultsModal" :item="item" @close="showShareResultsModal = false" />
          <!-- END -->
        </div>

        <AlertWarningV2 v-if="percentageOfNsfw > 30 && !isTeamMember" class="max-w-8xl mx-auto my-4" :text="`You're missing ${percentageOfNsfw}% of your photos!`">
          <p class="text-sm text-black/70 leading-[18px]">
            It looks like these missing photos were deemed unprofessional by our AI detection service. <a href="mailto:<EMAIL>" class="underline">Contact support</a> now to claim a free reshoot.
          </p>
        </AlertWarningV2>
        <div class="space-y-6 mt-6">
          <template v-if="!['earn-free-photos', 'you-might-like', 'voting', 'referral'].includes(tab)">
            <div v-for="(card, index) in cards" :key="card.title" :data-tab="card.identifier">
              <GalleryPhotoListCard
                :item="item"
                :title="card.title"
                :icon="card.icon"
                :photos="card.photos"
                :description="card.description"
                :empty-state="card.emptyState"
                :icons="card.showIcons || []"
                :upsell-index="shouldShowUpsell ? card.upsellIndex : null"
                :show-nsfw="card.showNsfw && item?.totalNsfwNew > 0"
                :identifier="card.identifier"
              >
                <template #description>
                  <span v-html="card.description" />
                </template>
              </GalleryPhotoListCard>

              <template v-if="index === 0 && shouldShowReferralUpsell && cards.length > 1">
                <ReferralBlock :model-id="item._id" class="mt-6" @click="moveToTab('referral')" />
              </template>
            </div>
          </template>
          <div v-else-if="tab === 'earn-free-photos' && shouldShowUpsell" data-tab="earn-free-photos" class="pb-12" data-testid="gallery-result-page-upsell">
            <UpsellEarnFreeHeadshots @referral-click="moveToTab('referral')" />
          </div>
          <div v-else-if="tab === 'you-might-like' && shouldShowUpsell" data-tab="you-might-like" class="pb-12" data-testid="gallery-result-page-upsell">
            <UpsellYouMightLike :item="item" />
          </div>
          <div v-else-if="tab === 'voting'" data-tab="voting" class="pb-12" data-testid="gallery-result-page-upsell">
            <GalleryVotingTab :item="item" />
          </div>
          <div v-else-if="tab === 'referral'" data-tab="referral" class="pb-12" data-testid="gallery-result-page-referral">
            <GalleryReferralTab />
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Bottom Tab Navigation -->
    <transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-class="transform translate-y-full opacity-0"
      enter-to-class="transform translate-y-0 opacity-100"
      leave-active-class="transition-all duration-300 ease-in"
      leave-class="transform translate-y-0 opacity-100"
      leave-to-class="transform translate-y-full opacity-0"
    >
      <div
        v-if="theme === 'default'"
        class="block md:hidden fixed bottom-0 left-0 right-0 z-[20] bg-white border-t border-gray-200 shadow-lg safe-area-pb"
      >
        <div class="overflow-x-auto scrollbar-hide">
          <div class="flex px-4 py-3 gap-2 min-w-max">
            <button
              v-for="tabItem in tabs"
              :key="tabItem.identifier"
              class="flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-all duration-200 flex-shrink-0"
              :class="{
                'bg-teal-500 text-white': tab === tabItem.identifier,
                'bg-gray-100 text-gray-700 hover:bg-gray-200': tab !== tabItem.identifier
              }"
              @click="moveToTab(tabItem.identifier)"
            >
              <component :is="tabItem.icon" class="w-4 h-4" />
              <span>{{ tabItem.title }}</span>
              <span
                v-if="tabItem.indicator"
                class="bg-white/20 text-xs px-1.5 py-0.5 rounded-full min-w-[20px] text-center"
                :class="{
                  'bg-white/20 text-white': tab === tabItem.identifier,
                  'bg-brand-500 text-white': tab !== tabItem.identifier
                }"
              >
                {{ tabItem.indicator }}
              </span>
            </button>
          </div>
        </div>
      </div>
    </transition>

    <PopupTrustpilot
      v-if="showReviewRequestModal"
      size="lg"
      :model-id="item?._id"
      @closeModal="closeReviewModal"
    />
  </div>
</template>

<script>
export default {
  props: {
    model: {
      type: Object,
      default: () => null
    },
    theme: {
      type: String,
      default: 'default'
    }
  },
  data () {
    return {
      showShareResultsModal: false
    }
  },
  computed: {
    tab: {
      get () {
        return this.$store.state.results.tab
      },
      set (value) {
        this.$store.commit('results/SET_ACTIVE_TAB', value)
      }
    },
    percentageOfNsfw () {
      try {
        const totalNsfw = this.item?.totalNsfw || 0
        const totalPhotos = this.item?.totalPhotos || 0
        return totalPhotos > 0 ? (totalNsfw / totalPhotos * 100).toFixed(0) : 0
      } catch (e) {
        return 0
      }
    },
    heading () {
      if (this.model) {
        return `Headshots results for "${this.model.title}"`
      }

      if (this.tab === 'earn-free-photos') {
        return 'Earn free headshots'
      }

      if (this.tab === 'you-might-like') {
        return 'You might like'
      }

      if (this.tab === 'voting') {
        return 'Voting results'
      }

      if (this.tab === 'referral') {
        return 'Referral Program'
      }

      return 'Your headshot results'
    },
    description () {
      if (this.model) {
        return `These are the headshots we generated for "${this.model.title}". You can download them by clicking on the "Actions" menu on each photo.`
      }

      if (this.tab === 'earn-free-photos') {
        return 'If we have any free headshot offers available, this page is where you\'ll find them.'
      }

      if (this.tab === 'you-might-like') {
        return 'We have selected some additional styles for you based on what others liked. You can add them to your cart and get them generated in a few hours.'
      }

      if (this.tab === 'voting') {
        return 'Here you can see what your friends have voted for. Let\'s dive in!'
      }

      if (this.tab === 'referral') {
        return 'Invite friends. Earn free headshots. It\'s a win-win.'
      }

      return 'It\'s time to download your keepers. Use the "Actions" menu to download your headshots.'
    },
    activeImages () {
      return this.item?.images?.filter(photo => photo?.status === 'active') || []
    },
    uniqueVoters () {
      const voters = {}
      this.activeImages.forEach((photo) => {
        if (photo.voters) {
          photo.voters.forEach((voter) => {
            voters[voter] = true
          })
        }
      })

      return Object.keys(voters).length
    },
    tabs () {
      const newPhotosTab = (this.newPhotos?.length > 0 || this.item?.totalNsfwNew > 0)
        ? [
            { identifier: 'new-photos', icon: 'IconArrowPathRoundedSquare', title: 'New photos', description: 'These are the latest photos we have generated. You can mark them as keepers or duds by clicking on the "Actions" menu on each photo.', indicator: this.newPhotos?.length || 0 }
          ]
        : []
      const remixesTab = this.remixes?.length > 0
        ? [
            { identifier: 'remixes', icon: 'IconViewfinderCircle', title: 'Remixes' }
          ]
        : []
      const earnFreePhotosTab = this.isTeamMember
        ? []
        : (!this.shouldShowUpsell) ? [] : [{ identifier: 'earn-free-photos', icon: 'IconGift', title: 'Earn free photos', onlyEmit: true }]

      const youMightLikeTab = this.isTeamMember
        ? []
        : (!this.shouldShowUpsell || !this.item?.mightLikeOptions?.length) ? [] : [{ identifier: 'you-might-like', icon: 'IconCurrencyDollar', title: 'You might like', onlyEmit: true }]

      const votingTab = this.item?.shareToken && this.canShareHeadshots
        ? [{ identifier: 'voting', icon: 'IconShare', title: 'View votes', onlyEmit: true, indicator: this.uniqueVoters }]
        : [{ identifier: 'voting', icon: 'IconShare', title: 'Share with friends', onlyEmit: true }]

      const referralTab = this.isTeamMember
        ? []
        : [{ identifier: 'referral', icon: 'IconStar', title: 'Referral Program', onlyEmit: true }]

      const studioTab = this.isTeamMember || !this.item?.meta?.canUseStudio || this.screenWidth < 768
        ? []
        : [{ identifier: 'studio', icon: 'IconCamera', title: 'Studio (new)', onlyEmit: false, link: '/studio/' + this.item?._id }]

      if (this.model || this.isTeamMember) {
        return [
          ...newPhotosTab,
          { identifier: 'favorite', icon: 'IconStar', title: 'Favorite' },
          { identifier: 'keepers', icon: 'IconHeart', title: 'Keepers' },
          ...remixesTab,
          { identifier: 'all', icon: 'IconCamera', title: 'All photos' },
          ...votingTab
        ]
      }

      // For non-team members
      return [
        ...newPhotosTab,
        { identifier: 'keepers', icon: 'IconHeart', title: 'Keepers' },
        ...remixesTab,
        { identifier: 'all', icon: 'IconCamera', title: 'All photos' },
        ...youMightLikeTab,
        ...earnFreePhotosTab,
        ...referralTab,
        ...votingTab,
        ...studioTab
      ]
    },
    item () {
      return this.$store.state.results.item
    },
    activePhotos () {
      return this.item?.images?.filter(photo => !photo?.likedStatus || photo?.status === 'active') || []
    },
    lovedPhotos () {
      return this.activePhotos.filter(photo => photo?.likedStatus === 'loved') || []
    },
    favoritePhotos () {
      return this.activePhotos.filter(photo => photo?.likedStatus === 'favorite') || []
    },
    dislikedPhotos () {
      return this.activePhotos.filter(photo => photo?.likedStatus === 'dud') || []
    },
    allPhotos () {
      const allPhotos = [...this.activePhotos.filter(photo => photo.createdAt < new Date(Date.now() - 1000 * 60 * 60 * 24 * 5) && photo.likedStatus !== 'dud')]
      if (allPhotos?.length === 0) {
        return this.item?.images
      }
      return allPhotos
    },
    loadingPhotos () {
      return this.item?.images?.filter(photo => ['edit', 'regeneration', 'additional'].includes(photo?.type) && !['active', 'failed'].includes(photo.status)) || []
    },
    newPhotos () {
      const loadingPhotosIds = this.loadingPhotos.map(photo => photo._id)
      const newPhotos = this.activePhotos
        .filter(photo => photo.likedStatus === 'none' && !loadingPhotosIds.includes(photo._id) && photo.createdAt > new Date(Date.now() - 1000 * 60 * 60 * 24 * 5))
      return [
        ...this.loadingPhotos,
        ...newPhotos
      ]
    },
    remixes () {
      return this.activePhotos.filter(photo => ['edit', 'regeneration'].includes(photo?.type)) || []
    },
    availableRegenerations () {
      return this.item?.regenerationCredits - this.item?.usedRegenerations || 0
    },
    cards () {
      const remainingCredits = this.availableRegenerations
      const remixes = {
        identifier: 'remixes',
        title: 'Remixes',
        icon: 'IconViewfinderCircle',
        photos: this.remixes,
        description: { component: 'GalleryResultRemixCardDescription', props: { credits: remainingCredits, modelId: this.$store.state.results.item?._id } },
        showIcons: ['keepers', 'favorite']
      }

      const keepersInNormalPhotoshoot = 3
      const keepersPercentage = (this.favoritePhotos.length - keepersInNormalPhotoshoot) / keepersInNormalPhotoshoot * 100
      const keepersVariation = keepersPercentage < 100 ? `${keepersPercentage.toFixed(0)}%` : `${(keepersPercentage / 100 + 1).toFixed(1)}x`
      const keepersDescription = keepersPercentage > 0
        ? (this.$route.fullPath.includes('admin')) ? `This team member selected ${this.favoritePhotos.length} keepers. That's ${keepersVariation} more than they'd get in an offline photoshoot!` : `You selected ${this.favoritePhotos.length} keepers. That's ${keepersVariation} more than you'd get in an offline photoshoot!`
        : (this.$route.fullPath.includes('admin')) ? `${this.favoritePhotos.length} keepers were selected for this team member.` : `You selected ${this.favoritePhotos.length} keepers.`
      const keepers = {
        identifier: 'keepers',
        title: 'Keepers',
        icon: 'IconHeart',
        photos: this.favoritePhotos,
        description: keepersDescription,
        emptyState: (this.$route.fullPath.includes('admin')) ? 'This team member has not yet selected their keepers yet.' : 'You have no keepers yet. Go to "All photos" to select your keepers.',
        showIcons: ['remixes'],
        upsellIndex: 3
      }

      const newPhotos = {
        identifier: 'new-photos',
        title: 'New photos',
        icon: 'IconArrowPathRoundedSquare',
        photos: this.newPhotos,
        emptyState: (this.$route.fullPath.includes('admin')) ? 'This team member has not yet generated any new photos yet.' : 'You have no new photos yet. Go to "All photos" to generate some new photos.',
        showIcons: ['remixes'],
        showNsfw: true
      }

      const all = {
        identifier: 'all',
        title: 'All photos',
        icon: 'IconCamera',
        description: 'These are all the photos we have generated for you, excluding the duds. You can download them by clicking on the "Actions" menu on each photo.',
        photos: this.allPhotos,
        showIcons: ['remixes', 'keepers', 'favorite']
      }

      const duds = {
        identifier: 'duds',
        title: 'Duds',
        icon: 'IconThumbDown',
        photos: this.dislikedPhotos,
        showIcons: ['remixes', 'keepers', 'favorite']
      }

      let favoriteText = 'Your organization needs to know which headshot you want to use as your profile picture. Please pick one now by using the "Actions" menu on your favorite photo.'
      if (this.$route.fullPath.includes('admin')) {
        if (this.favoritePhotos.length > 0) {
          favoriteText = 'This person has not yet selected their favorite yet.'
        } else {
          favoriteText = 'This person has not yet selected their keepers, which means they problably didn\'t see their results yet.'
        }
      }

      const favorite = {
        identifier: 'favorite',
        title: 'Favorite',
        icon: 'IconStar',
        photos: this.lovedPhotos,
        emptyState: {
          component: 'AlertWarningV2',
          props: {
            text: favoriteText
          }
        },
        showIcons: ['remixes']
      }

      const tabs = [newPhotos, keepers, remixes, all, duds].filter(c => c.photos?.length > 0 || (c.showNsfw && this.item?.totalNsfwNew > 0))
      if (this.isTeamMember || this.model) {
        tabs.unshift(favorite)
      }

      return tabs
    },
    purchasedUpsell () {
      return this.$store.state.user?.purchasedUpsell || false
    },
    showReviewRequestModal () {
      return this.$store.state.showReviewRequestModal
    }
  },
  mounted () {
    if (this.$route.query.tab) {
      this.moveToTab(this.$route.query.tab)
      return
    }

    if (this.newPhotos?.length > 0 || this.item?.totalNsfwNew > 0) {
      this.moveToTab('new-photos')
    } else {
      this.moveToTab('keepers')
    }
    this.$nextTick(() => {
      const supportButton = document.querySelector('.chat-button')
      if (supportButton && this.screenWidth < 768) {
        supportButton.classList.add('bump-support-button')
      }
    })
  },

  beforeDestroy () {
    const supportButton = document.querySelector('.chat-button')
    if (supportButton) {
      supportButton.classList.remove('bump-support-button')
    }
  },
  methods: {
    moveToTab (identifier) {
      const tab = this.tabs.find(t => t.identifier === identifier)
      this.tab = identifier

      if (tab?.onlyEmit) {
        return
      }

      this.$nextTick(() => {
        const element = document.querySelector(`[data-tab="${identifier}"]`)
        if (!element) { return }

        const scrollContainer = document.querySelector('.scroll-container')
        const isWindowScroll = !scrollContainer
        const container = isWindowScroll ? window : scrollContainer

        const offset = (isWindowScroll ? window.innerHeight : container.clientHeight) * 0.3
        const elementRect = element.getBoundingClientRect()

        if (isWindowScroll) {
          window.scrollTo({
            top: elementRect.top + window.pageYOffset - offset,
            behavior: 'smooth'
          })
        } else {
          container.scrollTo({
            top: elementRect.top + container.scrollTop - offset,
            behavior: 'smooth'
          })
        }
      })
    },
    closeReviewModal () {
      this.$store.commit('SET_SHOW_REVIEW_REQUEST_MODAL', false)
      this.$cookies.set('hsp:seenreviewmodal', true, { maxAge: 60 * 60 * 24 * 365 })
    }
  }
}
</script>

<style>
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}
.bump-support-button{
  bottom: 70px !important;
  right: 8px !important;
}
</style>
