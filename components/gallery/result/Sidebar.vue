<template>
  <div class="relative" data-testid="gallery-result-sidebar">
    <div
      class="absolute left-0 w-full h-[36px] bg-[#EFEEFD] rounded-lg transition-all duration-300"
      :style="{
        top: `${tabs.findIndex(t => t.identifier === activeTab) * (36 + 4)}px`
      }"
    />
    <div class="space-y-1">
      <GalleryResultSidebarItem
        v-for="t in tabs"
        :key="t.identifier"
        class="relative"
        :active="t.identifier === activeTab"
        :icon="t.icon"
        :title="t.title"
        :indicator="t.indicator"
        @click="handleTabClick(t)"
      />
    </div>
  </div>
</template>

<script>
import debounce from '../../../utils/debounce'

export default {
  props: {
    tabs: {
      type: Array,
      required: true
    },
    selectedTab: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      activeTab: this.tabs[0].identifier
    }
  },
  watch: {
    selectedTab: {
      handler (value) {
        this.activeTab = value
      },
      immediate: true
    }
  },
  mounted () {
    window.addEventListener('scroll', this.handleScroll, { passive: true })
  },
  beforeDestroy () {
    window.removeEventListener('scroll', this.handleScroll)
  },
  methods: {
    handleScroll: debounce(function () {
      const selectedTab = this.tabs.find(t => t.identifier === this.selectedTab)
      if (selectedTab?.onlyEmit) {
        this.activeTab = selectedTab.identifier
        return
      }

      const windowHeight = window.innerHeight

      let activeTab = null
      for (const tab of this.tabs) {
        const element = document.querySelector(`[data-tab="${tab.identifier}"]`)
        if (element) {
          const elementTop = element.getBoundingClientRect().top
          const elementBottom = elementTop + element.clientHeight

          const topIsVisible = elementTop >= 0 && elementTop <= (windowHeight - 300)
          const bottomIsVisible = elementBottom >= 0 && elementBottom <= windowHeight
          const topIsAboveScreen = elementTop < 0
          const bottomIsBelowScreen = elementBottom > windowHeight

          if (
            (topIsVisible && bottomIsVisible) ||
            (topIsAboveScreen && bottomIsBelowScreen) ||
            (topIsAboveScreen && bottomIsVisible) ||
            (topIsVisible && bottomIsBelowScreen)
          ) {
            activeTab = tab.identifier
          }
        }
      }

      const currentScroll = window.scrollY
      if (currentScroll < 10) {
        activeTab = this.tabs[0].identifier
      }

      this.activeTab = activeTab
    }, 50),
    handleTabClick (tab) {
      const { identifier, link } = tab
      if (link) {
        this.$router.push(link)
        return
      }
      if (identifier) {
        this.$emit('select', identifier)
        this.$nextTick(() => {
          this.activeTab = identifier
        })
      }
    }
  }
}
</script>
