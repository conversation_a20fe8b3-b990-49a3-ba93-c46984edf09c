<template>
  <p v-if="credits > 0" class="text-sm text-slate-500">
    You have {{ credits }} remix credits remaining.
    <template v-if="shouldShowUpsell">
      Click <button type="button" class="underline" @click="addMoreCredits">
        here
      </button> to buy more.
    </template>
  </p>
  <p v-else class="text-sm text-slate-500">
    You have no remix credits remaining.
    <template v-if="shouldShowUpsell">
      Click <button type="button" class="underline" @click="addMoreCredits">
        here
      </button> to buy more.
    </template>
  </p>
</template>

<script>
export default {
  props: {
    credits: {
      type: Number,
      required: true
    },
    modelId: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      loading: false
    }
  },
  methods: {
    addMoreCredits () {
      if (this.loading) {
        return
      }

      this.loading = true

      this.$store.dispatch('upsell/addRemixCredits', { amount: 10, modelId: this.modelId })
        .then(() => {
          this.$router.push(`/app/results/${this.modelId}/unlock-more`)
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>
