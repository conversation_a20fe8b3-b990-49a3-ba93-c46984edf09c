<template>
  <button
    type="button"
    class="w-full flex items-center justify-start rounded-lg px-3 py-2"
    :class="{
      'text-paragraph hover:bg-gray-100': !active,
      'text-primary-500 font-bold': active
    }"
    data-testid="gallery-result-sidebar-item"
    @click="$emit('click')"
  >
    <component :is="icon" class="w-4 h-4 text-paragraph mr-3" />
    <span class="text-sm block whitespace-nowrap text-ellipsis overflow-hidden">{{ title }}</span>
    <Badge v-if="indicator" class="whitespace-nowrap text-ellipsis overflow-hidden ml-auto">
      {{ indicator }}
    </Badge>
  </button>
</template>

<script>
export default {
  props: {
    active: {
      type: Boolean,
      required: true
    },
    icon: {
      type: String,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    indicator: {
      type: Number,
      default: 0
    }
  }
}
</script>
