<template>
  <div class="w-full">
    <GalleryPreviewPartialsEmailBody class="mt-4">
      <GalleryPreviewPartialsEmailSignature
        :photo="profilePicture"
        :name="item.title"
        :email="user.email"
      />
    </GalleryPreviewPartialsEmailBody>
    <div v-if="$store.state.results.item?.title" class="mt-4">
      <LoadingWrapper :is-loading="isLoading">
        <ButtonWhite size="sm" @click="goToCustomizeEmailSignature">
          Customize email signature
        </ButtonWhite>
      </LoadingWrapper>
      <p class="mt-2 text-gray-300">
        <a href="https://support.google.com/mail/answer/8395?hl=en" target="_blank" rel="noopener noreferrer" class="text-xs text-gray-600 hover:underline">
          How to add a signature in Gmail
        </a>
        <span>&nbsp;&nbsp;|&nbsp;&nbsp;</span>
        <a href="https://support.microsoft.com/en-us/office/create-and-add-an-email-signature-in-outlook-8ee5d4f4-68fd-464a-a1c1-0e1c80bb27f2" target="_blank" rel="noopener noreferrer" class="text-xs text-gray-600 hover:underline">
          How to add a signature in Outlook
        </a>
      </p>
    </div>
  </div>
</template>

<script>
import EmailSignatureMixin from '../../../mixins/EmailSignatureMixin'

export default {
  mixins: [EmailSignatureMixin],
  props: {
    profilePicture: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      isLoading: null
    }
  },
  computed: {
    item () {
      return this.$store.state.results.item
    },
    user () {
      return this.$fire.auth.currentUser
    }
  },
  mounted () {
    //
  },
  methods: {
    goToCustomizeEmailSignature () {
      this.isLoading = true
      this.uploadPictureToPublicBucket(this.profilePicture)
        .then((url) => {
          this.$router.push('/tools/free-email-signature-generator?photo=' + encodeURIComponent(url) + '&name=' + encodeURIComponent(this.item.title) + '&email=' + encodeURIComponent(this.user.email))
        })
        .catch((err) => {
          console.error(err)
          this.$toast.error('Failed to upload image')
        })
        .finally(() => {
          this.isLoading = false
        })
    }
  }
}
</script>
