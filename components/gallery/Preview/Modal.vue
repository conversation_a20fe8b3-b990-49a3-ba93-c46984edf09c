<template>
  <div class="w-full bg-gray-200 p-4 lg:w-[800px]">
    <Tab class="rounded-t-md" :with-padding="false">
      <TabItem :active="activeTab === 'linkedin'" @click="activeTab = 'linkedin'">
        LinkedIn Profile
      </TabItem>
      <TabItem :active="activeTab === 'mailSignature'" @click="activeTab = 'mailSignature'">
        Email Signature
      </TabItem>
      <TabItem :active="activeTab === 'preview'" @click="activeTab = 'preview'">
        Preview
      </TabItem>
    </Tab>
    <div>
      <template v-if="activeTab === 'linkedin'">
        <GalleryPreviewLinkedIn :profile-picture="activePreviewImage" />
      </template>
      <template v-if="activeTab === 'mailSignature'">
        <GalleryPreviewEmailSignature :profile-picture="activePreviewImageLarge ?? activePreviewImage" />
      </template>
      <template v-if="activeTab === 'preview'">
        <div class="mt-4 mx-auto flex justify-center relative group rounded-md overflow-hidden">
          <ImageDns
            :key="activePreviewImageLarge ?? activePreviewImage"
            :src="activePreviewImageLarge ?? activePreviewImage"
            class="border-2 border-white h-auto w-full rounded-md pointer-events-none"
            :class="{
              'border-[#21C55D]': selected,
            }"
          />
          <div v-if="selected" class="w-8 h-8 bg-green-500 rounded-bl-md absolute top-0 right-0 flex items-center justify-center">
            <IconCheck class="w-4 h-4 text-white" />
          </div>
          <div v-if="selected !== null" class="top-[2px] left-[2px] right-[2px] bottom-[2px] rounded-md overflow-hidden absolute z-10 hidden group-hover:flex cursor-pointer" @click="$emit('select')">
            <div class="w-full h-2/5 bg-gradient-to-t from-black/80 to-transparent flex items-center justify-center bottom-0 left-0 absolute">
              <span v-if="isMobile()" class="text-sm text-white">Tap to select</span>
              <span v-else class="text-sm text-white">Click to select or press Space to select</span>
            </div>
          </div>
        </div>
      </template>
    </div>
    <div class="flex flex-col gap-2 xs:flex-row xs:justify-between mt-4">
      <ButtonWhite
        :disabled="activeIndex === 0"
        :class="{
          'opacity-50': activeIndex === 0
        }"
        @click="previousPhoto"
      >
        Previous photo
      </ButtonWhite>
      <ButtonWhite
        :disabled="activeIndex === allPhotos.length - 1"
        :class="{
          'opacity-50': activeIndex === allPhotos.length - 1
        }"
        @click="nextPhoto"
      >
        Next photo
      </ButtonWhite>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    previewImage: {
      type: String,
      required: true
    },
    previewImageLarge: {
      type: String,
      required: true
    },
    allPhotos: {
      type: Array,
      required: true
    },
    selected: {
      type: Boolean,
      default: null
    }
  },
  data () {
    let activeTab = 'linkedin' // Default value
    if (typeof window !== 'undefined') {
      // Only access localStorage when window is defined (client-side)
      activeTab = localStorage.getItem('activeTab') || 'linkedin'
    }

    return {
      activeTab,
      activeIndex: this.allPhotos.findIndex(photo => [photo.preview, photo.thumbnail, photo.image, photo.small].includes(this.previewImage))
    }
  },
  computed: {
    activePreviewImage () {
      return this.allPhotos[this.activeIndex]?.small || this.allPhotos[this.activeIndex]?.preview || this.allPhotos[this.activeIndex]?.thumbnail || this.previewImage
    },
    activePreviewImageLarge () {
      return this.allPhotos[this.activeIndex]?.image || this.allPhotos[this.activeIndex]?.thumbnail || this.previewImageLarge
    }
  },
  watch: {
    activeTab (newValue) {
      if (typeof window !== 'undefined') {
        // Only access localStorage when window is defined (client-side)
        localStorage.setItem('activeTab', newValue)
      }
    }
  },
  mounted () {
    window.addEventListener('keydown', this.listenToKeys)
  },
  beforeDestroy () {
    window.removeEventListener('keydown', this.listenToKeys)
  },
  methods: {
    nextPhoto () {
      this.activeIndex = Math.min(this.allPhotos.length - 1, this.activeIndex + 1)
      this.$emit('navigate', this.activeIndex)
    },
    previousPhoto () {
      this.activeIndex = Math.max(0, this.activeIndex - 1)
      this.$emit('navigate', this.activeIndex)
    },
    listenToKeys (event) {
      if (event.key === 'ArrowRight') {
        this.nextPhoto()
      } else if (event.key === 'ArrowLeft') {
        this.previousPhoto()
      } else if (event.key === ' ' && this.selected !== null) {
        event.preventDefault()
        this.$toast.success(this.selected ? 'Unselected photo' : 'Selected photo')
        this.$emit('select')
      }
    },
    isMobile () {
      const userAgent = navigator.userAgent
      if ((/windows phone/i.test(userAgent) || /android/i.test(userAgent) || /iPad|iPhone|iPod/.test(userAgent)) && !window.MSStream) {
        return true
      }
      return false
    }
  }
}
</script>
