<template>
  <div class="w-full mt-4 border border-gray-300 bg-white rounded-xl overflow-hidden">
    <div class="aspect-[4/1] relative min-h-[150px]">
      <div class="absolute left-0 top-0 w-full h-full" :style="{ backgroundImage: 'url(' + require('@/assets/img/linkedin-bg.svg') + ')' }" />
    </div>
    <div class="px-10 relative pb-10">
      <ImageDns :key="profilePicture" :src="profilePicture" class="w-40 h-40 object-cover object-top border-4 border-white rounded-full mt-[-110px] pointer-events-none" />
      <p class="text-2xl font-bold mt-4">
        {{ item.title }}
      </p>
      <p class="text-base">
        AI Headshot Model | Professional Headshot Taker
      </p>
      <p class="text-sm text-gray-500 mt-4">
        World Wide Web and around · <span class="text-[#2D64BC] font-bold">Contact information</span>
      </p>
      <p class="text-sm text-[#2D64BC] mt-4 font-bold">
        More than 500 contacts
      </p>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    profilePicture: {
      type: String,
      required: true
    }
  },
  computed: {
    item () {
      return this.$store.state.results.item
    }
  }
}
</script>
