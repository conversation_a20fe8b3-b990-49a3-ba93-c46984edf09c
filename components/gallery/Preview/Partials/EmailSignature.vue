<template>
  <div ref="signatureWrapper">
    <table
      cellpadding="0"
      cellspacing="0"
      border="0"
      globalstyles="[object Object]"
      style="vertical-align: -webkit-baseline-middle; font-size: medium; font-family: Arial;"
    >
      <tbody>
        <tr>
          <td>
            <table
              cellpadding="0"
              cellspacing="0"
              border="0"
              globalstyles="[object Object]"
              style="vertical-align: -webkit-baseline-middle; font-size: medium; font-family: Arial;"
            >
              <tbody>
                <tr>
                  <td style="vertical-align: top;">
                    <table
                      cellpadding="0"
                      cellspacing="0"
                      border="0"
                      globalstyles="[object Object]"
                      style="vertical-align: -webkit-baseline-middle; font-size: medium; font-family: Arial;"
                    >
                      <tbody>
                        <tr>
                          <td style="text-align: center;">
                            <img
                              v-if="photo"
                              :key="photo"
                              :src="photo"
                              role="presentation"
                              width="96"
                              height="96"
                              style="display: block; width: 96px; height: 96px; object-fit: cover; object-position: top; border-radius: 9999px; pointer-events: none;"
                            >
                            <div v-else style="width: 96px; height: 96px; background-color: #f4f4f4; border-radius: 9999px;" />
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td><td width="46">
                    <div />
                  </td><td style="padding: 0px; vertical-align: middle;">
                    <h2 v-if="name" color="#000000" style="margin: 0px; font-size: 18px; color: #000000; font-weight: 600;">
                      <span>{{ name }}</span>
                    </h2>
                    <p v-if="position" color="#44403c" font-size="medium" style="margin: 0px; color: rgb(68, 64, 60); font-size: 14px; line-height: 22px;">
                      <span>{{ position }}</span>
                    </p>
                    <p v-if="phone" color="#44403c" font-size="medium" style="margin: 0px; color: rgb(68, 64, 60); font-size: 14px; line-height: 22px;">
                      <strong>T:</strong> {{ phone }}
                    </p>
                    <p v-if="email" color="#44403c" font-size="medium" style="margin: 0px; color: rgb(68, 64, 60); font-size: 14px; line-height: 22px;">
                      <strong>E:</strong> {{ email }}
                    </p>
                    <p v-if="website" color="#44403c" font-size="medium" style="margin: 0px; color: rgb(68, 64, 60); font-size: 14px; line-height: 22px;">
                      <strong>W:</strong> {{ website }}
                    </p>
                    <a
                      textcolor="#989898"
                      href="https://www.headshotpro.com/?utm_source=email-signature"
                      target="_blank"
                      rel="noopener noreferrer"
                      style="font-size: 10px; display: none; text-decoration: underline; color: rgb(152 152 152); margin-top: 4px;"
                    >Create Your Own Professional Email Pictures</a>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  props: {
    photo: {
      type: String,
      required: false,
      default: null
    },
    name: {
      type: String,
      required: false,
      default: null
    },
    email: {
      type: String,
      required: false,
      default: null
    },
    phone: {
      type: String,
      required: false,
      default: '+1234 567 890'
    },
    website: {
      type: String,
      required: false,
      default: null
    },
    position: {
      type: String,
      required: false,
      default: 'AI Headshot Model | Professional Headshot Taker'
    }
  }
}
</script>
