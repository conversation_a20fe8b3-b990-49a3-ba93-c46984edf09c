<template>
  <div
    class="transition rounded-xl overflow-hidden group relative box-content"
    :class="{
      'cursor-pointer': !disabled && !stillGenerating,
    }"
    :style="{
      // 'opacity': disabled || selected.length === 0 || selected.includes(photo?._id) ? '1' : '0.7',
      'opacity': !disabled ? '1' : '0.7',
    }"
    data-testid="gallery-item"
  >
    <ImageDns :src="(watermark) ? photo.thumbnail : photo.image" class="w-full transition duration-300" @click="$emit('select')" />
    <div v-if="selected.includes(photo?._id)" class="w-full h-full border-4 border-[#34A853] rounded-xl absolute top-0 left-0" />
    <template v-if="!disabled">
      <div v-if="screenWidth && screenWidth > 768 && !stillGenerating" class="h-full w-full top-0 left-0 absolute z-10 hidden group-hover:flex" @click="$emit('select')">
        <div class="w-full h-full bg-black/60 flex items-center justify-center inset-0 absolute">
          <span v-if="selectionScreen" class="text-lg text-white font-light">Click to select</span>
        </div>
      </div>
      <div v-else class="h-full w-full top-0 left-0 absolute z-10 block" @click="$emit('select')" />
      <div v-if="screenWidth && screenWidth > 768" class="hidden group-hover:flex justify-between bottom-0 left-0 p-3 w-full absolute z-20">
        <ButtonWhite size="sm" @click="$emit('preview')">
          <IconEye class="w-4 h-4 text-[#0F172A] mr-1" />
          <span>Preview</span>
        </ButtonWhite>
        <ButtonWhite v-if="!stillGenerating" size="sm" @click="$emit('select')">
          <IconThumbUp class="w-4 h-4 text-[#0F172A] mr-1" />
          <span>Select</span>
        </ButtonWhite>
      </div>
      <template v-if="!stillGenerating">
        <div v-if="screenWidth && screenWidth > 768" class="hidden group-hover:flex bottom-2 right-2 absolute z-20">
          <ButtonWhite v-if="!selectionScreen" size="sm" @click="$emit('download')">
            <IconSolidDownload class="w-3 h-3 text-black/70 mr-1" />
            <span>Download</span>
          </ButtonWhite>
        </div>
      </template>
    </template>
    <div v-if="selected.includes(photo?._id)" class="w-8 h-8 bg-[#34A853] rounded-bl-md absolute top-0 right-0 flex items-center justify-center z-10">
      <IconCheck class="w-4 h-4 text-white" />
    </div>
    <div v-if="photo?.likedStatus === 'favorite'" class="w-8 h-8 bg-red-500 rounded-br-md absolute top-0 left-0 flex items-center justify-center z-10">
      <IconSolidHeart class="w-4 h-4 text-white" />
    </div>
    <div v-if="photoHasVotes" class="absolute bottom-2 right-2 px-2 py-1 bg-yellow-500 rounded-lg text-xs text-white group-hover:hidden">
      <span>{{ photo?.votes }}</span>
      <span v-if="photo?.votes > 1">votes</span>
      <span v-else>vote</span>
    </div>
    <EditAdvanceModeModal v-if="isEditing" :watermark="watermark" :photo="photo" :model-id="$route.params.id" @closeModal="stopEditing" />
  </div>
</template>

<script>
export default {
  props: {
    watermark: {
      type: Boolean,
      required: false,
      default: () => true
    },
    photo: {
      type: Object,
      required: true
    },
    selected: {
      type: Array,
      required: false,
      default: () => []
    },
    selectionScreen: {
      type: Boolean,
      default: false
    },
    modelId: {
      type: String,
      required: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    stillGenerating: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      isEditing: false,
      isLoading: false
    }
  },
  computed: {
    photoHasVotes () {
      return this.photo?.votes > 0
    }
  },
  mounted () {
    //
  },
  methods: {
    editPicture () {
      this.isEditing = true
    },
    stopEditing () {
      this.isEditing = false
    }
  }
}
</script>
