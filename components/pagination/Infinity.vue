<template>
  <div class="w-full flex content-center items-center justify-center mt-8">
    <template v-if="!reachedEnd">
      <LoadingWrapper :is-loading="isLoading">
        <ButtonDark ref="infinity" size="sm" class="bg-gray-900" @click="loadMoreContent">
          <span>Load more</span>
        </ButtonDark>
      </LoadingWrapper>
    </template>
    <template v-else>
      <span class="text-gray-500 text-sm">
        You've reached the end.
      </span>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    endpoint: {
      type: String,
      required: false
    }
  },
  data () {
    return {
      page: 2,
      timer: {
        cooldown: false,
        clear: false
      },
      isLoading: false,
      loadMoreCooldownActive: false
    }
  },
  computed: {
    reachedEnd: {
      get () {
        return this.$store.state.reachedEnd
      },
      set (value) {
        this.$store.commit('SET_REACHED_END', value)
      }
    }
  },
  mounted () {
    this.reachedEnd = false
    if (document.getElementById('content')) {
      document.getElementById('content').addEventListener('scroll', this.handleScroll)
    } else {
      document.addEventListener('scroll', this.handleScroll)
    }
  },
  beforeDestroy () {
    document.removeEventListener('scroll', this.handleScroll)
    if (document.getElementById('content')) {
      document.getElementById('content').removeEventListener('scroll', this.handleScroll)
    }
  },
  methods: {
    handleScroll () {
      // Your scroll handling here
      if (this.$refs?.infinity?.$el?.getBoundingClientRect()?.top) {
        const offsetTop = this.$refs.infinity.$el.getBoundingClientRect().top
        const browserHeight = window.innerHeight
        if (((offsetTop - 500) < browserHeight) && !this.reachedEnd && !this.isLoading) {
          if (this.timer.cooldown) {
            clearTimeout(this.timer.cooldown)
            this.timer.cooldown = null
            this.timer.clear = false
          }
          this.timer.cooldown = setTimeout(() => {
            this.loadMoreContent()
          }, 400)
        }
      }
    },
    loadMoreContent () {
      // Prevent multiple calls if already loading, on cooldown, or end reached
      if (this.isLoading || this.loadMoreCooldownActive || this.reachedEnd) {
        return
      }

      this.isLoading = true
      this.loadMoreCooldownActive = true // Activate cooldown

      this.$emit('fetch', this.page)
      this.page = this.page + 1

      // Reset isLoading after a delay (simulating fetch completion)
      setTimeout(() => {
        this.isLoading = false
      }, 1000) // This timeout was pre-existing

      // Reset cooldown after 1 second
      setTimeout(() => {
        this.loadMoreCooldownActive = false
      }, 1000) // Cooldown period
    }
  }
}
</script>

<style>

</style>
