<template>
  <div>
    <client-only>
      <div v-if="currencyForLocale" class="flex justify-start md:justify-center">
        <Tab class="w-auto">
          <TabItem :active="currentSiteWideCurrencyCode?.toLowerCase() === 'usd'" @click="setCurrency('usd')">
            <span v-if="showTitle">USD ($)</span>
            <span v-else>$</span>
          </TabItem>
          <TabItem :active="currencyForLocale?.value === currentSiteWideCurrencyCode?.toLowerCase()" @click="setCurrency(currencyForLocale.value)">
            <span v-if="showTitle">{{ currencyForLocale.title }}</span>
            <span v-else>{{ currencyForLocale.symbol }}</span>
          </TabItem>
          <TabItem v-if="currentSelectedCurrencyNotUsdOrNative" :active="currentSelectedCurrencyNotUsdOrNative?.value === currentSiteWideCurrencyCode?.toLowerCase()" @click="setCurrency(currentSelectedCurrencyNotUsdOrNative.value)">
            <span v-if="showTitle">{{ currentSelectedCurrencyNotUsdOrNative.title }}</span>
            <span v-else>{{ currentSelectedCurrencyNotUsdOrNative.symbol }}</span>
          </TabItem>
        </Tab>
      </div>
    </client-only>
  </div>
</template>

<script>
export default {
  props: {
    showTitle: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      // Add a reactive property to force updates when currency changes
      currencyUpdateTrigger: 0
    }
  },
  computed: {
    availableCurrencies () {
      const allowed = this.$store.state.allowedCurrencies
      // Ensure 'allowed' is an array before attempting to filter
      if (!Array.isArray(allowed)) {
        return [] // Return empty array if not an array or undefined
      }
      return allowed.filter((currency) => {
        // Basic check for valid currency object and code
        if (!currency || typeof currency.code === 'undefined') {
          return false
        }
        if (!currency.hidden) { return true }
        // Ensure user and countryCode exist before comparison
        const userCountryCode = this.$store.state.user && this.$store.state.user.countryCode
        if (!userCountryCode) { return false }

        // Handle both string and array locales
        if (Array.isArray(currency.locale)) {
          return currency.locale.includes(userCountryCode)
        }
        return currency.locale === userCountryCode
      })
    },
    userCountry () {
      return this.$store.state.user && this.$store.state.user.countryCode
    },
    allowedCurrencyCodes () {
      return this.availableCurrencies.map(currency => currency.code.toLowerCase())
    },
    currencyForLocale () {
      // Add dependency on currencyUpdateTrigger to make it reactive
      this.currencyUpdateTrigger // eslint-disable-line no-unused-expressions

      const currency = this.availableCurrencies.find((currency) => {
        // Handle both string and array locales
        if (Array.isArray(currency.locale)) {
          return currency.locale.includes(this.userCountry)
        }
        return currency.locale === this.userCountry
      })
      if (currency) {
        return { title: currency.title + ' (' + currency.symbol + ')', value: currency.code, symbol: currency.symbol }
      }
      return null
    },
    currentSiteWideCurrencyCode () {
      // Add dependency on currencyUpdateTrigger to make it reactive
      this.currencyUpdateTrigger // eslint-disable-line no-unused-expressions

      // Priority 1: User-specific currency from Vuex store (e.g., this.$store.state.user.currency)
      const userStoreCurrency = this.$store.state.user && this.$store.state.user.currency
        ? String(this.$store.state.user.currency).toLowerCase()
        : null

      if (userStoreCurrency && this.allowedCurrencyCodes.includes(userStoreCurrency)) {
        return userStoreCurrency.toUpperCase()
      }

      // Priority 2: Currency from localStorage
      if (process.client) {
        const storedPreferredCurrency = localStorage.getItem('preferedCurrency')
        if (storedPreferredCurrency) {
          const storedLower = String(storedPreferredCurrency).toLowerCase()
          if (this.allowedCurrencyCodes.includes(storedLower)) {
            return storedLower.toUpperCase()
          }
        }
      }

      // Priority 3: Fallback logic
      // Default to 'USD' if it's an allowed currency
      if (this.allowedCurrencyCodes.includes('usd')) {
        return 'USD'
      }
      // Otherwise, use the first available currency from the allowed list
      if (this.availableCurrencies.length > 0 && this.availableCurrencies[0].code) {
        return this.availableCurrencies[0].code.toUpperCase()
      }
      // Absolute fallback if no currencies are available or 'usd' is not allowed
      return 'USD'
    },
    currentSelectedCurrencyNotUsdOrNative () {
      // Add dependency on currencyUpdateTrigger to make it reactive
      this.currencyUpdateTrigger // eslint-disable-line no-unused-expressions

      const currency = this.availableCurrencies.find(currency => currency.code === this.currentSiteWideCurrencyCode.toLowerCase())
      if (currency.code === 'usd') {
        return null
      }
      if (currency.code === this.currencyForLocale.value) {
        return null
      }
      if (currency) {
        return { title: currency.title + ' (' + currency.symbol + ')', value: currency.code, symbol: currency.symbol }
      }
      return null
    }
  },
  mounted () {
    // Listen for currency changes to update the reactive trigger
    this.$root.$on('currency-changed', this.handleCurrencyChange)
  },
  beforeDestroy () {
    // Remove event listener
    this.$root.$off('currency-changed', this.handleCurrencyChange)
  },
  methods: {
    handleCurrencyChange () {
      // Update the reactive trigger to force computed properties to re-evaluate
      this.currencyUpdateTrigger += 1
    },
    async setCurrency (currencyCode) {
      if (process.client) {
        // Show loading state
        this.$loading.show({ title: 'Switching currency...', description: 'Updating prices and content...' })

        try {
          // Update localStorage with new currency
          localStorage.setItem('preferedCurrency', currencyCode.toLowerCase())

          // Update the reactive trigger to force computed properties to re-evaluate
          this.currencyUpdateTrigger += 1

          // Force Vue to re-compute currency-dependent values
          // Since userCurrency is a computed property that depends on localStorage,
          // we need to trigger reactivity by forcing the component to update
          this.$forceUpdate()

          // Also force parent components and other components to update
          // by emitting a global event that other components can listen to
          this.$root.$emit('currency-changed', currencyCode.toLowerCase())

          // Give a small delay to ensure all computed properties and UI updates complete
          await this.$nextTick()

          // Additional small delay for smooth UX and to show the loading state briefly
          await new Promise(resolve => setTimeout(resolve, 500))
        } catch (error) {
          console.error('Error switching currency:', error)
        } finally {
          this.$loading.hide()
        }
      }
    }
  }
}
</script>

<style>

</style>
