<template>
  <div class="space-y-4">
    <Card inner-class="space-y-6">
      <div v-if="version === 'settings'">
        <h2 class="text-base font-bold text-primary-500">
          {{ title }}
        </h2>
        <p class="text-gray-500 text-sm mt-1">
          {{ subtitle }}
        </p>
      </div>
      <Input
        v-model="name"
        label="Company name"
        placeholder="Your name"
        char-regex="a-zA-Z0-9\s\-\.,\$#\@\!%\="
      />
      <div class="group">
        <Input v-model="website" type="url" label="URL" placeholder="Your company website" />
        <p v-if="!websiteIsValid && website" class="text-red-500 text-sm group-focus-within:hidden">
          Please enter a valid URL (e.g. https://www.yourcompany.com)
        </p>
      </div>
      <OnboardingTeamSizePicker v-if="version === 'onboarding'" v-model="teamSize" />
      <div v-if="version === 'settings'" class="flex w-full justify-end">
        <ButtonPrimary class="w-full xs:w-auto" size="sm" :is-disabled="!formIsFilled || isSaving" @click="saveOrganization()">
          Save information
        </ButtonPrimary>
      </div>
    </Card>
    <div v-if="version === 'onboarding'" class="flex flex-col w-full items-center justify-between gap-4 xs:flex-row">
      <p class="text-[13px] text-paragraph order-last xs:order-first">
        Need a custom deal?
        <button type="button" class="underline" @click="showSalesModal = true">
          Contact sales.
        </button>
      </p>
      <ButtonPrimary class="w-full xs:w-auto" size="sm" :is-disabled="!formIsFilled || isSaving" @click="saveOrganization()">
        Continue
      </ButtonPrimary>
    </div>
    <MarketingSalesModal v-if="showSalesModal" @close="showSalesModal = false" />
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      required: false,
      default: null
    },
    subtitle: {
      type: String,
      required: false,
      default: null
    },
    version: {
      type: String,
      required: false,
      default: 'onboarding'
    }
  },
  data () {
    return {
      name: null,
      website: null,
      description: null,
      isSaving: false,
      teamSize: '1-5',
      showSalesModal: false
    }
  },
  computed: {
    formIsFilled () {
      return this.name && this.websiteIsValid && this.teamSize
    },
    absoluteUrl () {
      const website = this.website || ''
      if (!website) { return '' }

      if (website.startsWith('http')) {
        return website
      }

      return `https://${website}`
    },
    websiteIsValid () {
      let url = this.absoluteUrl
      url = url.replace('//www.', '//')
      if (url.startsWith('www.')) {
        url = url.replace('www.', '')
      }

      const regex = /^(https?:\/\/)(www\.)?([a-zA-Z0-9][-a-zA-Z0-9]*\.)+[a-zA-Z0-9]{2,}([/\w.-]*)*\/?$/
      const isValid = url.match(regex)
      return isValid
    },
    logoUrl () {
      if (!this.websiteIsValid) {
        return null
      }

      const domain = new URL(this.absoluteUrl).hostname
      return `https://www.google.com/s2/favicons?domain=${domain}&sz=64`
    }
  },
  mounted () {
    this.fillDetails()
  },
  methods: {
    async saveOrganization () {
      if (this.isSaving) {
        return
      }

      if (!this.formIsFilled) {
        this.$toast.error('Please fill out all fields')
        return
      }

      this.$loading.show({
        title: 'Saving...'
      })

      try {
        this.isSaving = true
        const { success, data, errorMessage } = await this.$axios.$post('/organization', {
          name: this.name,
          website: this.absoluteUrl,
          teamSize: this.teamSize
        })

        if (!success) {
          throw new Error(errorMessage)
        }
        this.$store.commit('user/SET_ROLE', 'TeamLead')
        this.$store.commit('organization/SET_ORGANIZATION', data.organization)
        this.$emit('next')
      } catch (err) {
        this.handleError(err)
      } finally {
        this.$loading.hide()
        this.isSaving = false
      }
    },
    fillDetails () {
      if (this.$store.state.organization?.organization) {
        this.name = this.$store.state.organization.organization.name
        this.website = this.$store.state.organization.organization.website
        this.teamSize = this.$store.state.organization.organization.teamSize
      }

      if (this.$route.query.companyName) {
        this.name = this.$route.query.companyName
      }
    }
  }
}
</script>

<style></style>
