<template>
  <div class="grid grid-cols-1 xl:grid-cols-12 gap-4 xl:gap-16">
    <div class="xl:col-span-5">
      <div class="space-y-3">
        <!-- <PostcheckoutStepper :step="6" :max-steps="7" size="small" active="bg-primary-500" />
        <h1 class="text-lg md:text-[25px] font-bold text-primary-500 mt-6">
          Pay the way you want
        </h1>
        <p class="text-slate-500 text-sm md:text-base font-medium mt-1 sm:mt-3">
          We support company and teams of any size. Pick the payment method you prefer and we'll get you sorted.
        </p>
        <h2 class="text-lg font-bold text-primary-500 hidden xs:block">
          Select your payment method
        </h2> -->
        <div class="flex gap-4 flex-col sm:flex-row xl:flex-col">
          <CheckoutB2BPaymentMethod
            label="Purchase team credits"
            value="invoice"
            :is-active="method === 'invoice'"
            class="w-auto sm:w-full xl:w-auto"
            @select="method = 'invoice'"
          />
          <CheckoutB2BPaymentMethod
            label="Request a quote"
            value="quote"
            :is-active="method === 'quote'"
            class="w-auto sm:w-full xl:w-auto"
            @select="method = 'quote'"
          />
          <!-- <div class="hidden relative xs:flex justify-center items-center">
            <div class="absolute left-0 right-0 top-1/2 -translate-y-1/2 bg-[#E4E4E7CC] h-px" />
            <span class="text-sm text-slate-400 font-medium px-[10px] bg-gray-50 relative">OR</span>
          </div> -->
          <CheckoutB2BPaymentMethod
            label="Let me try first"
            value="trial"
            :is-active="method === 'trial'"
            class="w-auto sm:w-full xl:w-auto"
            @select="method = 'trial'"
          />
        </div>
      </div>
      <div class="mt-8 p-3 hidden xl:block">
        <h3 class="text-lg font-bold text-primary-500">
          💰 Get a lifetime bulk discount
        </h3>
        <p class="text-[15px] text-paragraph mt-2">
          Get a lifetime discount based on the amount of seats you purchase on your organisation. This includes repurchases.
        </p>
        <ul class="space-y-2 mt-4">
          <template v-for="option in teamDiscountOptions">
            <li v-if="option.discount > 0" :key="option.value" class="text-paragraph flex items-center justify-start gap-3">
              <span class="w-[90px] font-medium text-[15px]">{{ option.label }} users</span>
              <IconArrowRight class="w-4 h-4" />
              <CheckoutPercentageOffCircle :percentage="option.discount" />
            </li>
          </template>
        </ul>
      </div>
    </div>
    <div class="xl:col-span-7">
      <CheckoutB2BPayByInvoiceCard v-if="method === 'invoice'" ref="payByInvoice" title="Purchase team credits" />
      <CheckoutB2BLetMeTryFirst v-if="method === 'trial'" ref="letMeTryFirst" />
      <CheckoutB2BRequestQuoteCard v-if="method === 'quote'" ref="requestQuote" />
    </div>
  </div>
</template>

<script>

export default {
  data () {
    return {
      method: 'invoice'
    }
  },
  methods: {
    payNow () {
      if (this.$refs.payByInvoice) {
        this.$refs.payByInvoice.selectPaymentProvider()
      } else if (this.$refs.letMeTryFirst) {
        this.$refs.letMeTryFirst.selectPaymentProvider()
      }
    }
  }
}
</script>
