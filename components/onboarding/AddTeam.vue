<template>
  <Card class="sm:min-w-[400px] md:min-w-[600px] lg:min-w-[768px]">
    <template v-if="!onlyInviteLink">
      <h2 class="text-lg font-semibold text-primary-500">
        Invite team members
      </h2>
      <p class="font-medium text-slate-500 mt-1">
        On invite, we'll send your team member an email to start their shoot.
      </p>
      <div class="py-5">
        <div class="space-y-4">
          <InputTextArea
            v-model="emails"
            label="Email addresses"
            placeholder="Type in your team members emails. You can add multiple by adding a line break or a comma."
          />
          <div v-if="teams.length > 1">
            <InputSelect
              :value="selectedTeam"
              :options="teams"
              label="To which team should they be invited?"
              class="w-full"
              placeholder="No team"
              @input="updateSelectedTeam($event)"
            />
          </div>
          <div class="flex w-full items-center justify-end space-x-4">
            <ButtonWhite size="sm" :disabled="isInviting || numberOfPeopleInvited === 0" class="w-full shadow-sm" :class="{ 'opacity-50 cursor-not-allowed': isInviting || numberOfPeopleInvited === 0 }" @click="inviteTeamMembers">
              <template v-if="numberOfPeopleInvited === 0">
                <span>Enter at least one valid email address</span>
              </template>
              <template v-else>
                <span>Invite {{ numberOfPeopleInvited }} {{ numberOfPeopleInvited !== 1 ? 'members' : 'member' }}</span>
                <IconChevron class="w-4 h-4 ml-4" />
              </template>
            </ButtonWhite>
          </div>
          <div class="flex items-center justify-start gap-4">
            <p class="text-sm text-paragraph font-semibold">
              Rather invite everyone at once?
            </p>
            <ButtonWhite size="sm" class="shadow-sm" @click="showCreateInviteForm = !showCreateInviteForm">
              <IconLink class="w-4 h-4 mr-1.5" />
              <span>Create invite link</span>
            </ButtonWhite>
          </div>
          <div v-if="showCreateInviteForm" class="border border-gray-200 rounded-lg p-4 bg-gray-50">
            <h4 class="text-sm font-semibold text-gray-700 mb-3">
              Create new invite link
            </h4>
            <div class="space-y-3">
              <InputSelect
                :value="newInviteTeamId"
                :options="teamsForInvite"
                label="Assign to team"
                class="w-full"
                placeholder="No team"
                @input="newInviteTeamId = $event"
              />
              <div class="flex justify-end gap-2">
                <ButtonWhite size="sm" @click="cancelCreateInvite">
                  Cancel
                </ButtonWhite>
                <ButtonPrimary size="sm" :disabled="isCreatingInvite" @click="createGenericInvite">
                  <template v-if="isCreatingInvite">
                    Creating...
                  </template>
                  <template v-else>
                    Create link
                  </template>
                </ButtonPrimary>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template v-else-if="inviteLinks.length === 0">
      <h2 class="text-lg font-semibold text-primary-500">
        Invite link
      </h2>
      <p class="font-medium text-slate-500 mt-1">
        Create a bulk invite link you can send to the whole organisation. You can always easily invalidate this link later on for security.
      </p>
      <div class="mt-4 space-y-4">
        <InputSelect
          :value="newInviteTeamId"
          :options="teamsForInvite"
          label="Assign to team"
          class="w-full"
          placeholder="No team"
          @input="newInviteTeamId = $event"
        />
        <ButtonWhite size="sm" class="shadow-sm w-full" :disabled="isCreatingInvite" @click="createGenericInvite">
          <IconLink class="w-4 h-4 mr-1.5" />
          <template v-if="isCreatingInvite">
            Creating link...
          </template>
          <template v-else>
            Create invite link
          </template>
        </ButtonWhite>
      </div>
    </template>
    <div v-if="inviteLinks.length > 0">
      <h3 class="text-lg font-bold text-primary-500">
        Invite links
      </h3>
      <p class="font-medium text-paragraph mt-1">
        Here's a list of your custom created invite links.
      </p>
      <div class="mt-4">
        <table class="w-full">
          <thead>
            <tr>
              <th class="py-3 px-6 text-left bg-[#FCFCFD] text-[#667085] text-xs font-medium border-b border-[#EAECF0]">
                Team
              </th>
              <th class="py-3 px-6 text-left bg-[#FCFCFD] text-[#667085] text-xs font-medium border-b border-[#EAECF0]">
                Valid until
              </th>
              <th class="py-3 px-6 text-left bg-[#FCFCFD] text-[#667085] text-xs font-medium border-b border-[#EAECF0]">
                Usage
              </th>
              <th class="py-3 px-6 text-left bg-[#FCFCFD] text-[#667085] text-xs font-medium border-b border-[#EAECF0]">
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="invite in inviteLinks" :key="invite.uid" class="border-b border-[#EAECF0] text-sm text-paragraph">
              <td class="p-6">
                {{ getTeamName(invite.teamId) }}
              </td>
              <td class="p-6">
                Forever
              </td>
              <td class="p-6">
                {{ invite?.joinedBy?.length || 0 }}/&infin;
              </td>
              <td class="p-6 pr-2">
                <div class="flex items-center gap-2">
                  <ButtonWhite size="xs" class="shadow-sm" @click="copyLink(invite)">
                    Copy
                  </ButtonWhite>
                  <ButtonWhiteDelete size="xs" class="shadow-sm" @click="revokeGenericInvite(invite)">
                    Remove
                  </ButtonWhiteDelete>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </Card>
</template>

<script>
import copy from 'copy-to-clipboard'

export default {
  props: {
    title: {
      type: String,
      default: null
    },
    subTitle: {
      type: String,
      default: null
    },
    onlyInviteLink: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      emails: null,
      isInviting: false,
      inviteLinks: [],
      isLoading: false,
      selectedTeam: null,
      showCreateInviteForm: false,
      newInviteTeamId: null,
      isCreatingInvite: false
    }
  },
  computed: {
    credits () {
      return this.$store.state.organization.organization.credits || 0
    },
    numberOfPeopleInvited () {
      return this.validEmails.length
    },
    emailsArray () {
      if (!this.emails) {
        return []
      }

      const text = this.emails.trim().replace(/,\s*$/, '')
      const emails = text
        .split('\n')
        .map(email => email.trim())
        .flatMap(email => email.split(','))
        .filter(email => email.trim() !== '')
        .map(email => email.toLowerCase().trim())

      return emails
    },
    validEmails () {
      return this.emailsArray.filter(email => this.isValidEmail(email))
    },
    teams () {
      const teams = (this.$store.state.organization.organization?.teams || []).map(team => ({
        title: team.name,
        value: team.id
      }))

      return [
        {
          title: 'No team',
          value: 'none'
        },
        ...teams
      ]
    },
    teamsForInvite () {
      const organizationTeams = this.$store.state.organization.organization?.teams || []
      const existingTeamIds = this.inviteLinks.map(invite => invite.teamId).filter(Boolean)
      const hasOrganizationWideInvite = this.inviteLinks.some(invite => !invite.teamId)

      const availableTeams = organizationTeams
        .filter(team => !existingTeamIds.includes(team.id))
        .map(team => ({
          title: team.name,
          value: team.id
        }))

      const options = []

      if (!hasOrganizationWideInvite) {
        options.push({
          title: 'No team',
          value: null
        })
      }

      options.push(...availableTeams)

      return options
    }
  },
  mounted () {
    this.getGenericInvites()
  },
  methods: {
    finishOnboarding () {
      this.$emit('success')
      this.$router.push('/app/admin')
    },
    async inviteTeamMembers () {
      try {
        this.isInviting = true
        this.$loading.show({
          title: 'Inviting team members...'
        })

        if (!this.emails) {
          this.$toast.error('Please enter at least one email')
          return
        }

        if (!this.validateEmails()) {
          return
        }

        const { success, errorMessage } = await this.$axios.$post('/organization/invite', {
          emails: this.validEmails,
          teamId: this.selectedTeam
        })

        if (!success) {
          this.$toast.open({
            message: errorMessage,
            type: 'error',
            duration: 5000
          })
          throw new Error(errorMessage)
        }

        this.$store.commit('organization/ADD_MANUAL_INVITES', { emails: this.validEmails, teamId: this.selectedTeam })
        this.$toast.success('Invitations sent')
        this.emails = null
        this.$emit('success')
      } catch (err) {
        this.handleError(err)
      } finally {
        this.$loading.hide()
        this.isInviting = false
      }
    },
    getGenericInvites () {
      this.inviteLinks = []
      this.$loading.show({
        title: 'Loading invite links...'
      })
      this.$axios.$get('/organization/team/generic')
        .then((res) => {
          if (res?.data?.invites && Array.isArray(res.data.invites)) {
            this.inviteLinks = res.data.invites
          }
        })
        .catch((err) => {
          this.handleError(err)
        })
        .finally(() => {
          this.$loading.hide()
        })
    },
    createGenericInvite () {
      this.isCreatingInvite = true
      this.$axios.$post('/organization/team/create-generic-invite', {
        teamId: this.newInviteTeamId
      })
        .then((res) => {
          const genericInviteLink = res?.data?.invite || null
          if (genericInviteLink) {
            if (!this.inviteLinks.find(invite => invite.uid === genericInviteLink.uid)) {
              this.inviteLinks.push(genericInviteLink)
              this.$toast.success('Invite link created successfully!')
            }

            this.cancelCreateInvite()
          } else {
            this.$toast.error('There was an error generating the link. Please, try again later.')
          }
        })
        .catch((err) => {
          this.handleError(err)
        })
        .finally(() => {
          this.isCreatingInvite = false
        })
    },
    cancelCreateInvite () {
      this.showCreateInviteForm = false
      this.newInviteTeamId = null
    },
    revokeGenericInvite (invite) {
      this.$loading.show({
        title: 'Removing invite link...'
      })
      this.$axios.$delete(`/organization/team/generic-invite/${invite.uid}`)
        .then((res) => {
          this.inviteLinks = this.inviteLinks.filter(i => i.uid !== invite.uid)
          this.$toast.success('Invite link removed successfully!')
        })
        .catch((err) => {
          this.handleError(err)
        })
        .finally(() => {
          this.$loading.hide()
        })
    },
    copyLink (invite) {
      if (!invite) {
        return
      }

      copy(this.inviteLinkUrl(invite))
      this.$toast.success('Invite URL copied to clipboard!')
    },
    validateEmails () {
      return this.emailsArray.every((email) => {
        if (!this.isValidEmail(email)) {
          this.$toast.error(`${email}` + ' is not a valid email')
          return false
        }
        return true
      })
    },
    inviteLinkUrl (invite) {
      if (!invite) {
        return null
      }

      return `${process.env.BASE_URL}/auth/signup?invite=${invite.uid}`
    },
    updateSelectedTeam (team) {
      this.selectedTeam = team
    },
    getTeamName (teamId) {
      if (!teamId) {
        return 'No team'
      }

      const team = (this.$store.state.organization.organization?.teams || []).find(t => t.id === teamId)
      return team ? team.name : 'Unknown Team'
    }
  }
}
</script>

<style></style>
