<template>
  <div class="flex flex-col lg:h-[100dvh] bg-gray-50 lg:overflow-hidden">
    <header class="py-4 bg-white border-b border-gray-100 w-full">
      <div class="w-full max-w-[1400px] flex items-center justify-between px-4 mx-auto sm:px-6 lg:px-8">
        <div class="hidden md:flex justify-start items-center gap-2">
          <slot name="left" />
        </div>
        <div class="flex-shrink-0 items-center justify-start flex gap-12">
          <nuxt-link to="/app" class="block">
            <Logo class="w-auto h-6" />
          </nuxt-link>
          <slot name="header" />
        </div>
        <div class="hidden md:flex justify-end items-center gap-2">
          <slot name="actions" />
        </div>
        <div class="flex md:hidden justify-end items-center gap-2">
          <slot name="mobile-nav-actions" />
        </div>
      </div>
    </header>
    <div class="lg:pb-0 lg:flex lg:h-[calc(100dvh_-_3.7rem_+_1px)]" :class="{ [mobilePadding]: withMobileActions }">
      <div
        class="flex w-full min-h-full flex-col items-start justify-start p-4 pt-0 antialiased flex-1 lg:overflow-y-auto md:justify-start md:p-8"
        :class="{ 'md:items-center': centered }"
      >
        <!-- TODO: Fix this max width to be dynamic so content is always aligned with logo on the left -->
        <div class="w-full ml-0 [@media(min-width:1400px)]:ml-[calc(50vw_-_692px)] [@media(min-width:1400px)]:w-auto max-w-[1336px]">
          <slot />
        </div>
      </div>
      <aside class="hidden w-[250px] xl:w-[340px] border-l border-gray-100 bg-white p-4 xl:p-8 flex-col lg:flex">
        <div class="w-full space-y-3">
          <div v-for="group in steps" :key="`group-${group.groupId}`" class="flex gap-[8px] xl:gap-[14px]">
            <div
              class="flex-shrink-0 flex flex-col items-center justify-start"
              :class="{
                'text-[#0EB567]': groupIsCompleted(group.groupId),
                'text-primary-500': !groupIsCompleted(group.groupId) && currentGroup === group.groupId,
                'text-[#E4E4E7]': !groupIsCompleted(group.groupId) && currentGroup < group.groupId,
              }"
            >
              <div class="w-[16px] xl:w-[26px] h-[16px] xl:h-[26px] rounded-full flex items-center justify-center bg-current">
                <IconCheck class="!w-4 !h-4 xl:!w-6 xl:!h-6 text-white" />
              </div>
              <div class="bg-current w-0.5 h-[35px]" />
            </div>
            <div>
              <div
                class="text-sm xl:text-base font-medium"
                :class="{
                  'text-paragraph': currentGroup !== group.groupId,
                  'text-primary-500': currentGroup === group.groupId,
                }"
              >
                {{ group.group }}
              </div>
              <div class="flex flex-col gap-1 mt-1">
                <div v-for="step in group.steps" :key="`step-${step.id}`" class="flex items-center gap-1.5 text-sm text-paragraph font-medium">
                  <IconCheck v-if="!(currentGroup === group.groupId && currentStep === step.id) && step.isCompleted()" class="!w-3 !h-3 xl:!w-4 xl:!h-4 text-paragraph flex-shrink-0" />
                  <div v-else class="w-3 xl:w-4 h-3 xl:h-4 rounded-full bg-transparent flex items-center justify-center border border-paragraph flex-shrink-0">
                    <div v-if="currentGroup === group.groupId && currentStep === step.id" class="w-2 h-2 xl:w-3 xl:h-3 rounded-full bg-primary-500" />
                  </div>
                  <button v-if="step.canNavigate()" type="button" class="text-xs xl:text-sm hover:underline" @click="navigate(step)">
                    {{ step.label }}
                  </button>
                  <span v-else class="text-xs xl:text-sm cursor-not-allowed">
                    {{ step.label }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="mt-auto w-full">
          <div v-if="completionData.hasDetails" class="py-8 border-y border-black/10">
            <p class="text-sm xl:text-base font-bold text-paragraphlp">
              In a hurry?
            </p>
            <p class="text-xs xl:text-sm font-medium text-paragraph mt-2">
              You can skip any step in this onboarding.
              <button type="button" class="underline" @click="$router.push('/app/admin')">
                Skip full onboarding
              </button>
            </p>
          </div>
          <div class="mt-[66px] cursor-pointer group" @click="openLiveChat">
            <IconLifebuoy class="w-6 h-6 text-paragraph group-hover:text-teal-500 group-hover:rotate-45 transition" />
            <p class="text-sm font-medium text-paragraph group-hover:underline">
              Having trouble?
            </p>
            <p class="text-xs xl:text-sm font-medium mt-1 text-paragraph opacity-[0.78]">
              We'll get you sorted out. Click here to submit an email to our customer support team.
            </p>
          </div>
        </div>
      </aside>
    </div>
    <div v-if="withMobileActions" class="fixed bottom-0 left-0 w-full bg-white p-4 shadow-lg border-t border-gray-100 md:hidden">
      <slot name="mobile-actions" />
    </div>
  </div>
</template>

<script>
import OrganizationMixin from '../../../mixins/OrganizationMixin'

export default {
  mixins: [OrganizationMixin],
  props: {
    currentGroup: {
      type: Number,
      required: true
    },
    currentStep: {
      type: Number,
      required: true
    },
    withMobileActions: {
      type: Boolean,
      default: true
    },
    mobilePadding: {
      type: String,
      default: 'pb-16'
    },
    centered: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      steps: [
        {
          groupId: 1,
          group: 'Complete your signup',
          steps: [
            {
              id: 1,
              label: 'Your company details',
              url: '/app/admin/team/new/company-details',
              canNavigate: () => true,
              isCompleted: () => {
                return this.completionData.hasDetails
              }
            }
          ]
        },
        {
          groupId: 2,
          group: 'Choose your styles',
          steps: [
            {
              id: 1,
              label: 'Pick your backdrops',
              url: '/app/admin/team/new/select-style',
              canNavigate: () => this.completionData.hasDetails,
              isCompleted: () => {
                return this.completionData.hasStyles
              }
            },
            {
              id: 2,
              label: 'Pick your clothing',
              url: '/app/admin/team/new/select-clothing',
              canNavigate: () => this.completionData.hasDetails,
              isCompleted: () => {
                return this.completionData.hasClothing
              }
            },
            {
              id: 3,
              label: 'Your PFP style',
              url: '/app/admin/team/new/branding',
              canNavigate: () => this.completionData.hasDetails,
              isCompleted: () => {
                return this.completionData.hasBranding
              }
            }
          ]
        },
        {
          groupId: 3,
          group: 'Invite your team',
          steps: [
            {
              id: 1,
              label: 'Setup your billing',
              url: '/app/admin/team/new/payment',
              canNavigate: () => this.completionData.hasDetails,
              isCompleted: () => {
                return this.completionData.hasBilling
              }
            },
            {
              id: 2,
              label: 'Invite your team members',
              url: '/app/admin/team/new/invite-team',
              canNavigate: () => this.completionData.hasBilling,
              isCompleted: () => {
                return this.completionData.hasInvitedTeam
              }
            }
          ]
        }
      ],
      navigationPromise: null
    }
  },
  mounted () {
    if (!this.isTeamLead && this.$store.state.organization?.organization) {
      return this.$router.push('/app')
    }

    // if (this.completedAllMandatorySteps && this.env !== 'development') { // (For testing purposes)
    if (this.completedAllMandatorySteps) {
      return this.$router.push('/app/admin')
    }
  },
  methods: {
    groupIsCompleted (groupId) {
      return this.steps.filter(group => group.groupId === groupId).every(group => group.steps.every(step => step.isCompleted()))
    },
    async navigate (step) {
      if (!step.canNavigate()) {
        return
      }

      const navigationEvent = new CustomEvent('onboarding:beforeNavigate', {
        detail: {
          step,
          // Add a promise resolver to the event detail
          waitUntil: (promise) => {
            this.navigationPromise = promise
          }
        },
        cancelable: true
      })

      const shouldProceed = window.dispatchEvent(navigationEvent)
      if (!shouldProceed) {
        return
      }

      try {
        if (this.navigationPromise) {
          await this.navigationPromise
        }

        this.$router.push(step.url)
      } catch (error) {
        console.error(error)
      } finally {
        this.navigationPromise = null
      }
    }
  }
}
</script>

<style>
  .chat-button {
    display: none;
  }
</style>
