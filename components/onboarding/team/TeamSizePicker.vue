<template>
  <div class="w-full">
    <p class="text-sm font-semibold leading-5 text-primary-500 flex items-center gap-2">
      Approximate team count
      <Tooltip info="Your best guess is fine! You'll select a more specific number later." />
    </p>

    <div class="mt-2 pb-1 sm:hidden">
      <InputSelect :value="value" :options="teamSizeOptions.map(option => ({ title: `${option.label} members`, value: option.value }))" @input="updateValue" />
    </div>

    <div class="hidden sm:flex items-center pb-6 mt-2" @mouseleave="hoverValue = null">
      <template v-for="(option, index) in teamSizeOptions">
        <div :key="option.value" class="relative flex items-center" :class="{ 'w-full': index < teamSizeOptions.length - 1 }">
          <button
            class="flex flex-col items-center cursor-pointer focus:outline-none group flex-shrink-0"
            @click="updateValue(option.value)"
            @mouseover="hoverValue = option.value"
          >
            <div class="w-[18px] h-[18px] rounded-full border-[4px] border-white flex items-center justify-center">
              <div
                :class="[
                  'bg-white w-[10px] h-[10px] rounded-full border-[2.5px] transition-colors duration-200',
                  currentOption?.value === option.value
                    ? 'border-primary-500 outline-[2px] outline-primary-500 outline'
                    : index < currentIndex
                      ? 'bg-[#20ACA0] border-[#20ACA0]'
                      : 'border-[#E9E9EC]'
                ]"
              />
            </div>

            <!-- Label -->
            <span class="text-xs absolute top-full" :class="{ 'text-primary-500 font-bold': currentOption?.value === option.value, 'text-paragraph': currentOption?.value !== option.value }">
              {{ option.label }}
            </span>
          </button>
          <div
            v-if="index < teamSizeOptions.length - 1"
            :key="'progress-bar-' + option.value"
            class="h-[3px] rounded-full w-full cursor-pointer"
            :class="{
              'bg-[#20ACA0]': index < currentIndex,
              'bg-[#E9E9EC]': index >= currentIndex
            }"
            @mouseover="teamSizeOptions[index + 1] && (hoverValue = teamSizeOptions[index + 1].value)"
            @click="updateValue(teamSizeOptions[index + 1].value)"
          />
        </div>
      </template>
    </div>
    <p class="text-sm text-paragraph">
      <template v-if="currentOption?.discount > 0">
        {{ currentOption.label }} team members would unlock a <span class="font-bold text-[#34A853]">{{ currentOption.discount }}% discount</span> for your company.
      </template>
      <template v-else>
        The more people you onboard to HeadshotPro, the larger the discount.
      </template>
    </p>
  </div>
</template>

<script>

export default {
  name: 'TeamSizePicker',
  props: {
    value: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      hoverValue: null
    }
  },
  computed: {
    teamSizeOptions () {
      return this.teamDiscountOptions
    },
    currentOption () {
      if (this.hoverValue) {
        return this.teamSizeOptions.find(option => option.value === this.hoverValue)
      }

      return this.teamSizeOptions.find(option => option.value === this.value)
    },
    currentIndex () {
      if (this.hoverValue) {
        return this.teamSizeOptions.findIndex(option => option.value === this.hoverValue)
      }

      return this.teamSizeOptions.findIndex(option => option.value === this.value)
    }
  },
  methods: {
    updateValue (newValue) {
      this.$emit('input', newValue)
    }
  }
}
</script>
