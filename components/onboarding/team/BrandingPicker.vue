<template>
  <div class="md:p-2 space-y-4">
    <OrganizationBrandingConfigCard />
    <div class="hidden md:flex w-full items-center justify-end space-x-4">
      <ButtonWhite size="sm" @click="$emit('skip')">
        <span>Skip this step</span>
        <IconChevron class="ml-1.5 h-4 w-4" />
      </ButtonWhite>
      <ButtonPrimary size="sm" :is-disabled="!hasBranding" @click="$emit('next')">
        <span v-if="hasBranding">Continue</span>
        <span v-else>Please select branding</span>
        <IconChevron v-if="hasBranding" class="ml-1.5 h-4 w-4" />
      </ButtonPrimary>
    </div>
  </div>
</template>

<script>
export default {
  computed: {
    hasBranding () {
      const { organization } = this.$store.state.organization
      return (organization?.branding?.backgroundImage || organization?.branding?.backgroundColor)
    }
  }
}
</script>

<style>

</style>
