<template>
  <div class="relative cursor-pointer group" @click="handleClick">
    <div class="rounded-t-lg border border-black/10 border-b-0 relative overflow-hidden">
      <ImageDns :src="imageUrl" class="w-full aspect-square group-hover:scale-105 transition-all duration-300" />
      <div class="absolute bottom-0 left-0 w-full h-1/2 bg-gradient-to-t from-black/[0.82] to-transparent p-4 flex items-end justify-center">
        <div class="text-white text-center text-sm font-medium opacity-90 line-clamp-2">
          {{ item.type.replace(/^a |^an /i, '') }}
        </div>
      </div>
    </div>
    <div class="border border-[#E4E4E7] rounded-b-lg p-3 text-sm font-medium text-slate-700 flex items-center justify-center gap-[7px] group-hover:bg-gray-50 transition-all duration-300">
      <template v-if="isSelected">
        <span>Remove</span>
        <IconNoIcon class="size-4 translate-y-0.5 text-paragraph" />
      </template>
      <template v-else>
        <span>Approve</span>
        <IconPlus class="!size-4 scale-125 translate-y-0.5 text-paragraph" />
      </template>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true
    },
    isSelected: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    imageUrl () {
      if ((this.item.gender === 'male') || (Array.isArray(this.item.gender) && this.item.gender.includes('male'))) {
        return this.item.images.male
      }

      return this.item.images.female
    }
  },
  methods: {
    handleClick () {
      if (this.isSelected) {
        this.$emit('remove', this.item)
      } else {
        this.$emit('add', this.item)
      }
    }
  }
}
</script>
