<template>
  <div>
    <div class="relative overflow-hidden rounded-t-lg cursor-pointer group" @click="selectedStyle">
      <div class="overflow-hidden rounded-t-lg">
        <ImageDns class="object-cover w-full h-full group-hover:scale-105 transition-all duration-150" :src="image" alt="" />
      </div>

      <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 via-gray-900/10 to-transparent" />

      <span
        v-if="selected"
        class="absolute text-[10px] px-2 py-0.5 font-extrabold tracking-wide text-white bg-teal-500 rounded-full top-2 right-2"
      >
        <span v-if="timesSelected > 1">{{ timesSelected }}x</span>
        SELECTED
      </span>

      <div class="absolute inset-x-0 bottom-0 flex items-center justify-center p-4">
        <p class="text-base font-medium tracking-tight text-white text-center">
          {{ item.title }}
        </p>
      </div>
    </div>

    <button
      type="button"
      class="text-sm w-full font-medium text-slate-700 rounded-b-lg shadow-sm bg-white border border-gray-200 transition-all duration-150 hover:bg-gray-50 pt-1.5 pb-2 px-2.5 inline-flex items-center gap-1.5 justify-center"
      @click="selectedStyle"
    >
      <span class="sm:hidden">
        Select
      </span>
      <span class="hidden sm:inline">
        {{ selected ? 'Select again' : 'Select' }}
      </span>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 20 20"
        fill="currentColor"
        class="size-5 -mb-0.5"
      >
        <path
          d="M10.75 4.75a.75.75 0 0 0-1.5 0v4.5h-4.5a.75.75 0 0 0 0 1.5h4.5v4.5a.75.75 0 0 0 1.5 0v-4.5h4.5a.75.75 0 0 0 0-1.5h-4.5v-4.5Z"
        />
      </svg>
    </button>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true
    },
    selectedStyles: {
      type: Array,
      required: true
    }
  },
  computed: {
    image () {
      return this.item.image?.femaleImage || this.item.image?.maleImage || ''
    },
    selected () {
      return this.selectedStyles.find(item => item?._id === this.item?._id)
    },
    timesSelected () {
      return this.selectedStyles.filter(item => item?._id === this.item?._id).length
    }
  },
  methods: {
    selectedStyle () {
      this.$emit('select-style', this.item)
    }
  }
}
</script>
