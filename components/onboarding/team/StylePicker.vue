<template>
  <div>
    <div class="gap-5 flex flex-col xl:flex-row" :class="{ 'justify-center': finished }">
      <div v-if="!finished" class="flex-1 p-5 bg-white border border-gray-200 rounded-lg shadow-sm order-2 xl:order-1">
        <div class="flex items-center justify-between gap-2">
          <p class="flex-1 text-base font-medium tracking-tight text-primary-500">
            Available backdrops
          </p>
        </div>

        <hr class="mt-4 border-gray-200">

        <div class="grid grid-cols-2 mt-5 md:grid-cols-3 gap-x-4 gap-y-5">
          <div v-for="style in styles" :key="style._id">
            <OnboardingTeamStyleItem
              :item="style"
              :selected-styles="selectedStyles"
              @select-style="selectStyle"
            />
          </div>
        </div>
      </div>

      <div class="w-full xl:max-w-md order-1 xl:order-2">
        <div class="w-full p-5 bg-white border border-gray-200 rounded-lg shadow-sm top-0 [@media(min-height:850px)]:sticky">
          <div class="flex justify-between items-center gap-0.5 flex-row">
            <div class="flex items-center gap-4 flex-row lg:justify-between xl:w-full">
              <p class="text-base font-medium tracking-tight text-primary-500">
                Selected
                <span class="hidden sm:inline"> backdrops</span>
              </p>
              <PostcheckoutProgressIndicator :current="totalNumberOfStyles" :total="maxSelectedStyles" bg="bg-gray-400" />
            </div>
            <ButtonWhite size="sm" class="xl:hidden" @click="showSelectedBackdrops = !showSelectedBackdrops">
              {{ showSelectedBackdrops ? 'Hide' : 'Expand' }}
            </ButtonWhite>
          </div>

          <div :class="{ 'hidden xl:block': !showSelectedBackdrops }">
            <hr class="mt-4 border-gray-200">

            <div v-if="finished" class="hidden mt-6 lg:block">
              <ButtonPrimary class="w-full" @click="goToNextStep">
                Continue to next step
                <IconArrowRight class="size-5 -mb-0.5" />
              </ButtonPrimary>
            </div>

            <div class="mt-6">
              <div class="space-y-2.5">
                <OnboardingTeamSelectedStyleListItem
                  v-for="item in preselectedOptions"
                  :key="`preselected-${item._id}`"
                  :item="item"
                  :deletable="false"
                />
                <OnboardingTeamSelectedStyleListItem
                  v-for="(item, index) in selectedStyles"
                  :key="`selected-${item._id}-${index}`"
                  :item="item"
                  :deletable="true"
                  @remove-style="removeStyle(index)"
                />

                <OnboardingTeamStyleNotSelected v-for="i in Math.max(0, maxSelectedStyles - totalNumberOfStyles)" :key="'not-selected-' + i" />
              </div>
            </div>

            <div v-if="!finished && canFinish" class="hidden mt-6 lg:block">
              <ButtonPrimary :is-disabled="!canContinue" class="w-full" @click="setWantsToContinue(true)">
                Continue to next step
                <IconArrowRight class="size-5 -mb-0.5" />
              </ButtonPrimary>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    styles: {
      type: Array,
      default: () => []
    },
    selectedStyles: {
      type: Array,
      default: () => []
    },
    maxSelectedStyles: {
      type: Number,
      default: 8
    },
    minSelectedStyles: {
      type: Number,
      default: 3
    },
    preselectedOptions: {
      type: Array,
      default: () => []
    },
    wantsToContinue: {
      type: Boolean,
      default: false
    },
    canFinish: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      isSaving: false,
      showSelectedBackdrops: false
    }
  },
  computed: {
    totalNumberOfStyles () {
      return this.preselectedOptions.length + this.selectedStyles.length
    },
    canContinue () {
      return this.totalNumberOfStyles >= this.minSelectedStyles
    },
    finished () {
      return this.canFinish && (this.wantsToContinue || this.totalNumberOfStyles >= this.maxSelectedStyles)
    }
  },
  watch: {
    totalNumberOfStyles (value) {
      if (value < this.minSelectedStyles) {
        this.setWantsToContinue(false)
      }
    },
    finished (value) {
      this.showSelectedBackdrops = value
    }
  },
  methods: {
    selectStyle (style) {
      if (this.totalNumberOfStyles >= this.maxSelectedStyles) {
        return
      }

      this.$emit('select-style', style)
    },
    removeStyle (index) {
      this.$emit('remove-style', index)
    },
    setWantsToContinue (value) {
      this.$emit('wants-to-continue', value)
    },
    async goToNextStep (silent = false) {
      if (this.isSaving) {
        return
      }

      try {
        this.isSaving = true

        if (!this.selectedStyles || !this.canContinue) {
          if (silent) {
            return false
          }

          throw new Error(`Please select at least ${this.minSelectedStyles} style${this.minSelectedStyles > 1 ? 's' : ''}`)
        }

        this.$loading.show({
          title: 'Saving styles...'
        })

        const { success, errorMessage } = await this.$axios.$post('/organization/style', { styleArray: this.selectedStyles })
        if (!success) {
          if (silent) {
            return false
          }

          throw new Error(errorMessage)
        }

        this.$store.commit('organization/SET_STYLE', JSON.parse(JSON.stringify(this.selectedStyles)))
        this.$store.commit('organization/SET_ORGANIZATION_ALLOWED_OPTIONS', JSON.parse(JSON.stringify({
          ...(this.$store.state.organization?.organization?.allowedOptions || {}),
          styles: this.selectedStyles.map(style => style._id)
        })))

        if (!silent) {
          this.$emit('next')
        }
      } catch (err) {
        this.handleError(err)
      } finally {
        this.$loading.hide()
        this.isSaving = false
      }
    }
  }
}
</script>
