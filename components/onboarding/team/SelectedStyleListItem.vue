<template>
  <div
    class="flex items-center justify-between gap-3 px-4 py-3 bg-white border border-[#E4E4E7] rounded-lg shadow-sm"
  >
    <ImageDns class="w-8 h-8 rounded-lg border border-black/10" :src="item.image?.femaleImage || item.image?.maleImage" alt="" />
    <p class="text-base font-semibold text-primary-500">
      {{ item.title }}
    </p>
    <template v-if="deletable">
      <button type="button" class="p-1 ml-auto text-red-600" @click="removeStyle(item)">
        <span class="sr-only">
          {{ $t('Remove') }}
        </span>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-4">
          <path
            d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z"
          />
        </svg>
      </button>
    </template>
    <template v-else>
      <p class="text-sm text-paragraph ml-auto">
        Pre-selected
      </p>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true
    },
    deletable: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    removeStyle () {
      this.$emit('remove-style')
    }
  }
}
</script>
