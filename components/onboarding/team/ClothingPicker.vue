<template>
  <div>
    <div
      class="mt-6 mb-6 gap-4 flex flex-col sm:flex-row items-center justify-between"
    >
      <Tab :with-padding="false">
        <TabItem :active="tab === 'selected'" class="gap-2" @click="setTab('selected')">
          <IconCheck class="!size-4" />
          <span>Approved</span>
        </TabItem>
        <TabItem :active="tab === 'removed'" class="gap-2" @click="setTab('removed')">
          <IconNoIcon class="size-4" />
          <span>Removed</span>
        </TabItem>
      </Tab>
      <div
        class="flex-col sm:flex-row items-center justify-between"
        :class="{
          'hidden sm:flex': !expand,
          'flex flex-col': expand
        }"
      >
        <div class="flex items-center justify-center w-full sm:w-auto sm:justify-end">
          <InputSelect :value="selectedGender" :options="[{ title: 'All genders', value: 'all' }, { title: 'Male', value: 'male' }, { title: 'Female', value: 'female' }]" @input="updateGender($event)" />
        </div>
      </div>
    </div>
    <div class="mt-5">
      <template v-if="tab === 'selected'">
        <template v-if="isAnySelected">
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-x-3 gap-y-5">
            <OnboardingTeamClothingItem
              v-for="item in selectedClothingItems"
              :key="item.gender+'_'+item._id+'_selected'"
              :item="item"
              :is-selected="true"
              @remove="disallow(item)"
            />
          </div>
        </template>
        <template v-else>
          <div class="bg-[#F5F5F5] rounded-[10px] py-14 px-6 flex items-center justify-center">
            <div class="flex items-center justify-center flex-col gap-2 w-full max-w-[378px]">
              <IconExclamation class="size-10 text-[#EA4335]" />
              <p class="text-base text-paragraph text-center">
                Please select at least one attire for your team to choose from.
              </p>
            </div>
          </div>
        </template>
      </template>
      <template v-if="tab === 'removed'">
        <template v-if="isAnyRemoved">
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-x-3 gap-y-5">
            <OnboardingTeamClothingItem
              v-for="item in removedClothingItems"
              :key="item.gender+'_'+item._id+'_removed'"
              :item="item"
              @add="allow(item)"
            />
          </div>
        </template>
        <template v-else>
          <div class="bg-[#F5F5F5] rounded-[10px] py-14 px-6 flex items-center justify-center">
            <div class="flex items-center justify-center flex-col gap-2 w-full max-w-[378px]">
              <IconExclamation class="size-10 text-[#EA4335]" />
              <p class="text-base text-paragraph text-center">
                You've selected all available attires.
              </p>
            </div>
          </div>
        </template>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      tab: 'selected',
      expand: false,
      selectedGender: 'all',
      selected: {
        male: [],
        female: []
      }
    }
  },
  computed: {
    clothingItems () {
      const maleClothing = this.$store.state.clothing
        .filter(item => item.gender?.includes('male') && !!item.images?.male)
        .map((item) => {
          const newItem = { ...item }
          newItem.gender = 'male'
          return newItem
        })
      const femaleClothing = this.$store.state.clothing
        .filter(item => item.gender?.includes('female') && !!item.images?.female)
        .map((item) => {
          const newItem = { ...item }
          newItem.gender = 'female'
          return newItem
        })
      let allClothing = []

      if (this.selectedGender === 'male') {
        allClothing = [...maleClothing]
      } else if (this.selectedGender === 'female') {
        allClothing = [...femaleClothing]
      } else {
        allClothing = [...maleClothing, ...femaleClothing]
      }

      allClothing.sort((a, b) => a.type.toLowerCase().localeCompare(b.type.toLowerCase()))

      return allClothing
    },
    removedClothingItems () {
      return this.clothingItems.filter(item => !this.selected[item.gender].includes(item._id))
    },
    selectedClothingItems () {
      return this.clothingItems.filter(item => this.selected[item.gender].includes(item._id))
    },
    isAnySelected () {
      return this.selectedClothingItems.length > 0
    },
    isAnyRemoved () {
      return this.removedClothingItems.length > 0
    },
    hasValidClothingSelections () {
      return this.selected.male.length > 0 && this.selected.female.length > 0
    },
    preselectedClothingItems () {
      const preselected = this.$store.state.organization.preselectedOptions?.clothing || {
        male: [],
        female: []
      }

      return this.clothingItems.filter(item => preselected[item.gender]?.includes(item._id))
    }
  },
  mounted () {
    if (this.$store.state.organization?.organization?.allowedOptions?.clothing?.male?.length > 0) {
      this.selected = JSON.parse(JSON.stringify(this.$store.state.organization.organization.allowedOptions.clothing))
      this.emitCount()
      this.$store.dispatch('organization/fetchPreselectedOptions')
    } else {
      this.$store.dispatch('organization/fetchPreselectedOptions')
        .then(() => {
          this.addRecommended()
        })
    }
  },
  methods: {
    allow (item) {
      if (!this.selected[item.gender].includes(item._id)) {
        this.selected[item.gender].push(item._id)
      }

      this.emitCount()
      this.$emit('selected')
    },
    disallow (item) {
      if (this.selected[item.gender].includes(item._id)) {
        this.selected[item.gender] = this.selected[item.gender].filter(id => id !== item._id)
      }

      this.emitCount()
      this.$emit('selected')
    },
    removeAll () {
      if (!confirm('Are you sure you want to remove all clothing selections?')) {
        return
      }

      this.selected = {
        male: [],
        female: []
      }

      this.emitCount()
      this.$emit('selected')
    },
    addAll () {
      if (!confirm('Are you sure you want to add all the available clothing options?')) {
        return
      }

      this.selected = {
        male: this.clothingItems.filter(item => item.gender === 'male').map(item => item._id),
        female: this.clothingItems.filter(item => item.gender === 'female').map(item => item._id)
      }

      this.emitCount()
      this.$emit('selected')
    },
    addRecommended (breakOnError = false) {
      if (this.$store.state.organization.preselectedOptions?.clothing?.male) {
        this.selected = JSON.parse(JSON.stringify(this.$store.state.organization.preselectedOptions.clothing))
        this.emitCount()
      } else if (!breakOnError) {
        this.$store.dispatch('organization/fetchPreselectedOptions')
          .then(() => {
            this.addRecommended(true)
          })
      }
    },
    async confirm (silent = false) {
      if (!this.hasValidClothingSelections) {
        if (!silent) {
          this.$toast.error('Please select at least one attire for each gender.')
        }
        return
      }

      this.$loading.show({
        title: 'Saving clothing selections...'
      })

      try {
        const currentStyles = this.$store.state.organization?.organization?.allowedOptions?.styles || this.$store.state.organization.organization.style?.map(style => style._id)?.filter(id => !!id) || []
        const payload = JSON.parse(JSON.stringify({
          styles: currentStyles,
          clothing: {
            male: this.selected.male,
            female: this.selected.female
          }
        }))
        const { success, errorMessage } = await this.$axios.$post(
          '/organization/allowed-style-options',
          { allowedOptions: payload }
        )
        if (!success) {
          if (silent) {
            return false
          }

          throw new Error(errorMessage)
        }

        this.$store.commit('organization/SET_ORGANIZATION_ALLOWED_OPTIONS', payload)
        if (!silent) {
          this.$emit('next')
        }
      } catch (err) {
        this.handleError(err)
      } finally {
        this.$loading.hide()
      }
    },
    updateGender (event) {
      this.selectedGender = event
    },
    expandFilters () {
      this.expand = !this.expand
    },
    emitCount () {
      const count = this.selected.male.length + this.selected.female.length
      this.$emit('count', count)
    },
    setTab (tab) {
      this.tab = tab
      this.$emit('tab', tab)
    }
  }
}
</script>
