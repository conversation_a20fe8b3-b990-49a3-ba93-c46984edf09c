<template>
  <div>
    <ModalTitle :title="title" :sub-title="subTitle">
      <LoadingWrapper :is-loading="isSaving" title="Saving..." class="text-black">
        <ButtonPrimary :disabled="isSaving" class="ml-4" @click="saveSelectedStyle">
          <span>Next</span>
          <IconChevron class="w-4 h-4 ml-1.5" />
        </ButtonPrimary>
      </LoadingWrapper>
    </ModalTitle>
    <div class="px-4 py-5 sm:px-6">
      <div class="mx-auto mt-4 max-w-7xl">
        <template v-if="selectedStyle && selectedStyle.length > 0">
          <h2 class="text-base font-medium leading-6 text-gray-900">
            Selected Styles
          </h2>
          <p class="text-sm font-light text-gray-600">
            These are the styles and clothing items that you selected for your perfect headshot.
          </p>
          <div class="mt-4 mb-8 grid grid-cols-1 gap-2 md:grid-cols-2 lg:grid-cols-3">
            <template v-for="(item, index) in selectedStyle">
              <div :key="item.clothing + item.style" class="group relative flex items-center rounded-md border border-brand-500 bg-gray-100 px-4 py-2">
                <span class="mr-4 text-sm font-light text-green-500">{{ numberOfItems[index] }}x</span>
                <div>
                  <p class="font-medium text-gray-900">
                    {{ styleName(item.style) }}
                  </p>
                  <span class="text-sm font-normal text-gray-400">{{ clothingName(item.clothing) }}</span>
                </div>

                <button class="absolute right-4 ml-auto rounded-lg border border-gray-200 bg-white p-2 text-gray-600 group-hover:block lg:hidden" @click="deleteStyle(index)">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="h-4 w-4 hover:text-red-500"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                  </svg>
                </button>
              </div>
            </template>
          </div>
        </template>

        <h2 class="text-base font-medium leading-6 text-gray-900">
          Available Styles
        </h2>
        <p class="text-sm font-light text-gray-600">
          These are all available styles and clothing items for your perfect headshot.
        </p>
        <div class="mt-4 grid grid-cols-1 gap-2 md:grid-cols-2 xl:grid-cols-3">
          <template v-for="style in styles">
            <StyleCard
              v-if="style"
              :key="style._id"
              :card="true"
              :style-item="style"
              :clothing="clothingItems"
              :selected-styles="selectedStyle"
              @select="selectStyle($event, style._id)"
            />
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: null
    },
    subTitle: {
      type: String,
      default: null
    }
  },
  data () {
    return {
      isSaving: false,
      isLoading: true
    }
  },
  computed: {
    styles () {
      return this.$store.state.styles
    },
    clothingItems () {
      return this.$store.state.clothing || []
    },
    selectedStyle: {
      get () {
        return this.$store.state.organization.organization.style || null
      },
      set (style) {
        this.$store.commit('organization/SET_STYLE', style)
      }
    },
    numberOfItems () {
      const numberOfHeadshots = this.$store.state.numberOfHeadshots || 120
      if (!this.selectedStyle) {
        return []
      } else {
        return this.selectedStyle.map((item) => {
          return numberOfHeadshots / this.selectedStyle.length
        })
      }
    }
  },
  mounted () {
    // this.fetch()
  },
  methods: {
    styleName (id) {
      const style = this.styles.find(style => style._id === id)
      return style?.title || ''
    },
    clothingName (id) {
      const clothing = this.clothingItems.find(clothing => clothing._id === id)
      return clothing?.type || ''
    },
    // async fetch () {
    //   try {
    //     this.isLoading = true
    //     const { success, errorMessage, data } = await this.$axios.$get('/clothing')
    //     if (!success) {
    //       throw new Error(errorMessage)
    //     }
    //     if (data && data.length > 0) {
    //       this.$store.commit('SET_CLOTHING', data)
    //     }
    //   } catch (err) {
    //     this.handleError(err)
    //   } finally {
    //     this.isLoading = false
    //   }
    // },
    selectStyle (clothing, style) {
      try {
        this.isSaving = true
        if (this.selectedStyle && this.selectedStyle.length >= 3) {
          throw new Error('You can only select 3 styles')
        }
        this.selectedStyle = {
          style,
          clothing
        }
        this.$toast.success('Style added.')
        window?.scrollTo({
          top: 0,
          behavior: 'smooth'
        })
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isSaving = false
      }
    },
    async deleteStyle (index) {
      try {
        this.$store.commit('organization/REMOVE_STYLE', index)

        const { success, errorMessage } = await this.$axios.$post('/organization/style', { styleArray: this.selectedStyle })
        if (!success) {
          throw new Error(errorMessage)
        }
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isSaving = false
      }
    },
    async saveSelectedStyle () {
      try {
        this.isSaving = true
        if (!this.selectedStyle || !this.selectedStyle.length > 0) {
          throw new Error('Please select at least one style')
        }
        const { success, errorMessage } = await this.$axios.$post('/organization/style', { styleArray: this.selectedStyle })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.$emit('next')
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isSaving = false
      }
    }
    // async saveSelectedStyle () {
    //   try {
    //     this.isSaving = true
    //     if (!this.selectedStyle) {
    //       throw new Error('Please select a style')
    //     }
    //     const { success, errorMessage } = await this.$axios.$post('/organization/style', { _id: this.selectedStyle._id })
    //     if (!success) {
    //       throw new Error(errorMessage)
    //     }
    //     this.$store.commit('organization/SET_STYLE', this.selectedStyle._id)
    //     this.$emit('next')
    //   } catch (err) {
    //     this.handleError(err)
    //   } finally {
    //     this.isSaving = false
    //   }
    // }
  }
}
</script>

<style></style>
