<template>
  <div class="grid grid-cols-1 gap-4 lg:grid-cols-12 lg:gap-6">
    <div class="lg:col-span-8">
      <Card>
        <h2 class="text-[15px] font-bold text-primary-500">
          Available styles
        </h2>
        <hr class="border-[#E4E4E7] my-5">
        <div class="grid grid-cols-2 mt-5 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-5">
          <template v-for="(style, index) in styles">
            <PostcheckoutStyleItem
              v-if="style?._id"
              :key="style._id"
              :item="style"
              :selected-styles="selectedStyles"
              :popular="index < 3"
              @select="selectStyle($event, style._id)"
            />
          </template>
        </div>
      </Card>
    </div>
    <div class="order-first lg:order-last lg:col-span-4">
      <div class="space-y-3">
        <UpsellCollapsibleCard title="Selected styles">
          <!-- <template #after-title v-if="nextDiscount">
            <div v-if="price !== null" class="text-xs font-bold text-white ml-2 px-2 py-0.5 bg-teal-500 rounded-full space-x-1 flex items-center">
              <span class="">
                {{ price ? `$${price}` : 'Free' }}
              </span>
              <span v-if="selectedStyles.length > 0" class="hidden md:block">
                for {{ selectedStyles.length * 10 }} photos
              </span>
            </div>
          </template> -->
          <div class="space-y-2.5">
            <div v-if="nextDiscount && price !== null && selectedStyles.length > 0 && totalFreeStyles === 0" class="text-xs gradient-bg font-bold text-white text-center px-3 py-1  rounded-full space-x-1 inline-flex">
              Add {{ leftUntilNextDiscount }} more styles to get {{ nextDiscount }}% off
            </div>
            <div v-if="totalFreeStyles > 0" class="text-xs font-semibold text-green-500">
              You have {{ totalFreeStyles }} free styles to unlock
            </div>
            <div
              v-for="(styleItem, index) in selectedStyles"
              :key="styleItem.style + '_' + styleItem.clothing + '_' + index"
              class="flex items-center justify-between gap-3 px-3 pt-1.5 pb-2 bg-white border border-gray-200 rounded-lg shadow-sm"
            >
              <div class="flex flex-col">
                <div class=" tracking-tight text-primary-500 space-x-2 items-center justify-start flex">
                  <span class=" font-medium text-primary-500 text-base">{{ styleName(styleItem.style) }}</span>
                  <span v-if="index+1 > totalFreeStyles" class="text-gray-400 text-xs" :class="{'old-price': currentDiscount}">
                    {{ formatPrice((itemPrice/100), userCurrency, 0, false) }}
                  </span>
                  <span v-if="index+1 <= totalFreeStyles" class="text-gray-400 text-xs">
                    Free
                  </span>
                  <!-- <span v-if="currentDiscount" class="text-gray-400 text-xs">${{ parseFloat((itemPrice/100) * (1 - currentDiscount / 100)).toFixed(2) }}</span> -->
                  <span v-if="currentDiscount && index+1 > totalFreeStyles" class="text-gray-400 text-xs">{{ formatPrice((itemPrice/100) * (1 - currentDiscount / 100), userCurrency, 0, false) }}</span>
                </div>
                <p class="text-xs font-normal text-gray-500">
                  {{ clothingName(styleItem.clothing) }}
                </p>
              </div>
              <button type="button" class="p-1 ml-auto text-red-500 hover:text-red-600" @click="removeStyle(index)">
                <span class="sr-only">
                  {{ $t('Remove') }}
                </span>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-5">
                  <path
                    d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z"
                  />
                </svg>
              </button>
            </div>

            <div
              v-if="selectedStyles.length === 0"
              class="flex items-center justify-between gap-3 px-3 pt-2.5 pb-3 border-2 border-gray-300 border-dashed rounded-lg bg-gray-50"
            >
              <p class="text-sm font-medium text-gray-500">
                No combination selected
              </p>
            </div>
            <ButtonPrimary
              class="w-full hidden !px-4 !text-[15px] lg:flex"
              :is-disabled="selectedStyles.length === 0 || isLoading"
              @click="goToNextStep"
            >
              <span>
                {{ selectedStyles.length === 0 ?
                  'Please select at least one combination'
                  : `Get ${selectedStyles.length * 10} extra ${selectedStyles.length === 1
                    ? 'photos'
                    : 'photos'} for ${price
                    ? `${formatPrice(price * (1 - currentDiscount / 100), userCurrency, 0, false)}`
                    : 'free'}` }}
              </span>
              <IconSmallArrow v-if="selectedStyles.length > 0" class="!w-6 !h-6 translate-y-px" />
            </ButtonPrimary>
          </div>
        </UpsellCollapsibleCard>
        <UpsellCollapsibleCard title="Personal information">
          <div class="space-y-5">
            <div>
              <InputSelect v-model="info.bodyType" :options="bodyTypeList" label="Body type" />
            </div>
            <div>
              <InputSelect v-model="info.glasses" :options="glassesEditList" label="Glasses" />
            </div>
            <div>
              <InputSelect v-model="info.age" :options="ageList" label="Age" />
            </div>
            <div>
              <InputSelect v-model="info.height" :options="heightList" label="Height" />
            </div>
            <div>
              <InputSelect v-model="info.ethnicity" :options="ethnicityList" label="Ethnicity" />
            </div>
          </div>
        </UpsellCollapsibleCard>
      </div>
    </div>
    <div class="fixed bottom-0 left-0 right-0 bg-white px-5 py-4 shadow-lg border-t border-[#E4E4E7] lg:hidden">
      <ButtonPrimary
        class="w-full"
        :is-disabled="selectedStyles.length === 0 || isLoading"
        size="sm"
        @click="goToNextStep"
      >
        <span>{{ selectedStyles.length === 0 ? 'Select at least one combination' : `Get ${selectedStyles.length * 10} extra ${selectedStyles.length === 1 ? 'photos' : 'photos'}` }}</span>
        <IconSmallArrow v-if="selectedStyles.length > 0" class="!w-6 !h-6" />
      </ButtonPrimary>
    </div>
  </div>
</template>

<script>
import UpsellMixin from '@/mixins/UpsellMixin'

export default {
  mixins: [UpsellMixin],
  props: {
    item: {
      type: Object,
      required: true
    },
    tieredDiscount: {
      type: Object,
      required: false
    }
  },
  data () {
    return {
      isSaving: false,
      isLoading: false,
      info: {
        bodyType: null,
        glasses: null,
        age: null,
        height: null,
        ethnicity: null
      }
    }
  },
  computed: {
    totalFreeStyles () {
      if (!this.$store.state.results?.item?.freeStyles) {
        return 0
      }
      return this.$store.state.results?.item?.freeStyles
    },
    itemPrice () {
      const prices = this.$store.state.upsell.prices
      const currency = this.userCurrency ?? 'usd'
      const stylePrice = (prices.style?.currency[currency]) ? prices.style?.currency[currency] : prices.style.price
      return stylePrice
    },
    currentDiscount () {
      const selectedStylesCount = this.$store.state.upsell.selectedStyles?.length || 0
      const discountRange = this.tieredDiscount.ranges.find(range =>
        selectedStylesCount >= range.min && selectedStylesCount < range.max
      )
      return discountRange ? discountRange.discount : 0
    },
    nextDiscount () {
      const selectedStylesCount = this.$store.state.upsell.selectedStyles?.length || 0
      const currentRangeIndex = this.tieredDiscount.ranges.findIndex(range =>
        selectedStylesCount >= range.min && selectedStylesCount < range.max
      )

      // If we're in the last range or no valid range found, return null
      if (currentRangeIndex === -1 || currentRangeIndex === this.tieredDiscount.ranges.length - 1) {
        return null
      }

      // Return the next range's discount
      return this.tieredDiscount.ranges[currentRangeIndex + 1].discount
    },
    leftUntilNextDiscount () {
      const selectedStylesCount = this.$store.state.upsell.selectedStyles?.length || 0
      const currentRangeIndex = this.tieredDiscount.ranges.findIndex(range =>
        selectedStylesCount >= range.min && selectedStylesCount < range.max
      )

      // If we're in the last range or no valid range found, return null
      if (currentRangeIndex === -1 || currentRangeIndex === this.tieredDiscount.ranges.length - 1) {
        return null
      }

      // Calculate how many more styles needed to reach next tier
      const nextRange = this.tieredDiscount.ranges[currentRangeIndex + 1]
      return nextRange.min - selectedStylesCount
    },
    styles () {
      return this.$store.state.styles.filter(style => !style?.organizationIds)
    },
    clothingItems () {
      return this.$store.state.clothing || []
    },
    price () {
      const prices = this.$store.state.upsell.prices
      if (!prices || this.selectedStyles.length === 0) { return null }

      const paidStyles = Math.max(this.selectedStyles.length - (this.$store.state.results.item.freeStyles || 0), 0)
      if (paidStyles === 0) { return 0 }

      const currency = this.userCurrency ?? 'usd'

      const stylePrice = (prices.style?.currency[currency]) ? prices.style?.currency[currency] : prices.style.price

      return paidStyles * stylePrice / 100
    }
  },
  mounted () {
    this.info.age = this.item.age || null
    this.info.bodyType = this.item.appearance?.bodyType || null
    this.info.glasses = this.item.appearance?.glassesPreference && this.item.appearance?.glassesPreference !== 'none' ? 'yes' : 'no'
    this.info.ethnicity = this.item.ethnicity || null
    this.info.height = this.item.appearance?.height || null

    if (this.$store.getters['upsell/appearance']) {
      this.info.age = this.$store.getters['upsell/appearance'].age || this.info.age
      this.info.bodyType = this.$store.getters['upsell/appearance'].bodyType || this.info.bodyType
      this.info.glasses = this.$store.getters['upsell/appearance'].glasses || this.info.glasses
      this.info.ethnicity = this.$store.getters['upsell/appearance'].ethnicity || this.info.ethnicity
      this.info.height = this.$store.getters['upsell/appearance'].height || this.info.height
    }

    this.$emit('set-can-continue', this.canContinue())
    this.$posthog.capture('$upsell:style_selection_page')
  },
  methods: {
    styleName (id) {
      const style = this.styles.find(style => style._id === id)
      return style?.title || ''
    },
    clothingName (id) {
      const clothing = this.clothingItems.find(clothing => clothing._id === id)
      return clothing?.type || ''
    },
    selectStyle (clothing, style) {
      try {
        this.$store.commit('upsell/SET_SELECTED_STYLE', {
          style,
          clothing
        })
        this.$toast.success(this.$t('Style added.'))
        window?.scrollTo({
          top: 0,
          behavior: 'smooth'
        })
        this.$emit('set-can-continue', this.canContinue())
      } catch (err) {
        this.handleError(err)
      }
    },
    removeStyle (index) {
      this.$store.commit('upsell/REMOVE_STYLE', index)
      this.$toast.success(this.$t('Style removed.'))
      this.$emit('set-can-continue', this.canContinue())
    },
    goToNextStep () {
      if (!this.canContinue()) {
        this.$toast.error(this.$t('Please select at least one combination'))
        return
      }

      this.$store.commit('upsell/SET_ETHNICITY', this.info.ethnicity)
      this.$store.commit('upsell/SET_SELECTED_BODY_TYPE', this.info.bodyType)
      this.$store.commit('upsell/SET_SELECTED_HEIGHT', this.info.height)
      this.$store.commit('upsell/SET_SELECTED_AGE', this.info.age)
      this.$store.commit('upsell/SET_SELECTED_GLASSES', this.info.glasses)
      this.$emit('next')
    },
    canContinue () {
      return this.selectedStyles.length > 0
    }
  }
}
</script>

<style scoped>
.old-price {
  @apply line-through opacity-50;
}
</style>
