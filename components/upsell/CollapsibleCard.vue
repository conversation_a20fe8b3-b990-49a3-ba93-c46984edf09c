<template>
  <Card>
    <div class="flex justify-between items-center">
      <div class="flex items-center space-x-2 justify-start">
        <h2 class="text-[15px] font-bold text-primary-500">
          {{ title }}
        </h2>
        <slot name="after-title" />
      </div>
      <ButtonWhite size="sm" class="lg:hidden" @click="isOpen = !isOpen">
        {{ isOpen ? 'Hide' : 'Expand' }}
      </ButtonWhite>
    </div>
    <hr class="border-[#E4E4E7] my-5" :class="{ 'hidden lg:block': !isOpen }">
    <div :class="{ 'hidden lg:block': !isOpen }">
      <slot />
    </div>
  </Card>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      isOpen: false
    }
  }
}
</script>

<style scoped>
.card{
  padding: 0.8rem;
}

@media (min-width: 1024px) {
  .card {
    padding: 1.5rem;
  }
}
</style>
