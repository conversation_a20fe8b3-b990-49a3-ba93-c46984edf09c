<template>
  <div>
    <div>
      <div class="mx-auto grid max-w-5xl grid-cols-1 gap-6 sm:mt-8 md:grid-cols-2 md:gap-12 lg:mt-10 xl:gap-24">
        <div class="space-y-8 md:order-2">
          <div>
            <Card inner-class="space-y-5">
              <LoadingWrapper :is-loading="isLoading">
                <div class="flex justify-between">
                  <p class="text-lg font-bold tracking-tight text-primary-500">
                    Order Summary
                  </p>

                  <button type="button" class="text-sm font-medium text-paragraph underline" @click="$emit('back')">
                    Change
                  </button>
                </div>
                <div class="bg-[#F5F5F580] p-4 rounded-lg mt-5">
                  <div class="flex flex-col gap-4">
                    <UpsellCheckoutItem
                      v-for="(basketItem, index) of checkoutItems"
                      ref="checkoutItems"
                      :key="basketItem.identifier"
                      :tiered-discount="tieredDiscount"
                      :index="index"
                      :item="basketItem"
                      :currency="currency"
                      class="w-full"
                      :class="{ 'hidden': !isShowingAllCheckoutItems && index >= visibleCheckoutItems.length }"
                    />
                    <template v-if="visibleCheckoutItems.length < checkoutItems.length">
                      <div>
                        <button type="button" class="text-sm font-medium tracking-tight text-paragraph italic block text-left" @click="showAllCheckoutItems">
                          And {{ checkoutItems.length - visibleCheckoutItems.length }} more. <span class="underline">Show all</span>
                        </button>
                      </div>
                    </template>
                  </div>

                  <template v-if="discountValue > 0">
                    <hr class="border-gray-200 my-4">

                    <div class="items-center justify-between gap-4 flex">
                      <p class="text-base font-medium tracking-tight text-primary-500">
                        Subtotal
                      </p>
                      <p class="text-base font-medium tracking-tight text-teal-500">
                        {{ formatPrice(subtotal, currency, 2, false) }}
                      </p>
                    </div>

                    <div class="flex items-start justify-between gap-4">
                      <div class="flex-1">
                        <p class="text-base font-medium tracking-tight text-primary-500">
                          Discount
                        </p>
                      </div>

                      <p class="text-base font-medium tracking-tight text-primary-500">
                        <template v-if="discountPercentage">
                          -{{ discountPercentage }}%
                        </template>
                        <template v-else>
                          -{{ formatPrice(discountAmount / 100, currency, 2, false) }}
                        </template>
                      </p>
                    </div>
                  </template>

                  <hr class="border-gray-200 my-4">

                  <div class="flex items-center justify-between gap-4">
                    <p class="text-xl font-medium tracking-tight text-primary-500">
                      Total
                    </p>
                    <div class="flex items-center gap-2">
                      <p class="text-xl font-medium tracking-tight text-teal-500">
                        {{ formatPrice(total, currency, 2, false) }}
                      </p>
                    </div>
                  </div>
                </div>
                <LoadingWrapper :is-loading="isPaying" class="hidden md:flex mt-5">
                  <ButtonPrimary v-if="total > 0" :is-disabled="!canPay" class="w-full" @click="redirectToPayment()">
                    <span>Pay now</span>
                  </ButtonPrimary>
                  <ButtonPrimary v-if="total === 0" class="w-full" @click="confirmWithoutPayment()">
                    <span>Continue</span>
                  </ButtonPrimary>
                </LoadingWrapper>
                <div class="hidden md:block mt-5">
                  <div class="flex items-center justify-center gap-1.5">
                    <svg aria-hidden="true" class="size-5 shrink-0 text-teal-500" viewBox="0 0 18 19" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M15.8836 7.24277C15.6164 6.96855 15.3422 6.68027 15.2367 6.43418C15.1312 6.18809 15.1383 5.82246 15.1312 5.4498C15.1242 4.76777 15.1102 3.9873 14.5687 3.4459C14.0273 2.90449 13.2469 2.89043 12.5648 2.8834C12.1922 2.87637 11.8125 2.86934 11.5805 2.77793C11.3484 2.68652 11.0461 2.39824 10.7719 2.13105C10.2867 1.66699 9.73125 1.13965 9 1.13965C8.26875 1.13965 7.71328 1.66699 7.22813 2.13105C6.95391 2.39824 6.66563 2.67246 6.41953 2.77793C6.17344 2.8834 5.80781 2.87637 5.43516 2.8834C4.75313 2.89043 3.97266 2.90449 3.43125 3.4459C2.88984 3.9873 2.87578 4.76777 2.86875 5.4498C2.86172 5.82246 2.85469 6.20215 2.76328 6.43418C2.67188 6.66621 2.38359 6.96855 2.11641 7.24277C1.65234 7.72793 1.125 8.2834 1.125 9.01465C1.125 9.7459 1.65234 10.3014 2.11641 10.7865C2.38359 11.0607 2.65781 11.349 2.76328 11.5951C2.86875 11.8412 2.86172 12.2068 2.86875 12.5795C2.87578 13.2615 2.88984 14.042 3.43125 14.5834C3.97266 15.1248 4.75313 15.1389 5.43516 15.1459C5.80781 15.1529 6.1875 15.16 6.41953 15.2514C6.65156 15.3428 6.95391 15.6311 7.22813 15.8982C7.71328 16.3623 8.26875 16.8896 9 16.8896C9.73125 16.8896 10.2867 16.3623 10.7719 15.8982C11.0461 15.6311 11.3344 15.3568 11.5805 15.2514C11.8266 15.1459 12.1922 15.1529 12.5648 15.1459C13.2469 15.1389 14.0273 15.1248 14.5687 14.5834C15.1102 14.042 15.1242 13.2615 15.1312 12.5795C15.1383 12.2068 15.1453 11.8271 15.2367 11.5951C15.3281 11.3631 15.6164 11.0607 15.8836 10.7865C16.3477 10.3014 16.875 9.7459 16.875 9.01465C16.875 8.2834 16.3477 7.72793 15.8836 7.24277ZM12.4805 7.73496L8.36016 11.6725C8.25363 11.7727 8.11265 11.8281 7.96641 11.8271C7.82232 11.8277 7.68365 11.7722 7.57969 11.6725L5.51953 9.70371C5.46239 9.65386 5.41592 9.59297 5.38291 9.52469C5.3499 9.45642 5.33104 9.38218 5.32746 9.30644C5.32387 9.23069 5.33564 9.155 5.36206 9.08392C5.38847 9.01283 5.42899 8.94783 5.48116 8.8928C5.53334 8.83778 5.59611 8.79387 5.66569 8.76372C5.73527 8.73357 5.81023 8.7178 5.88606 8.71736C5.96189 8.71692 6.03703 8.73182 6.10696 8.76116C6.17688 8.7905 6.24015 8.83367 6.29297 8.88809L7.96641 10.4842L11.707 6.91934C11.8164 6.82391 11.9584 6.77446 12.1034 6.78132C12.2484 6.78818 12.3851 6.85082 12.485 6.95614C12.5849 7.06147 12.6402 7.20132 12.6393 7.34646C12.6385 7.49161 12.5816 7.63081 12.4805 7.73496Z"
                      />
                    </svg>
                    <span class="text-sm font-extrabold uppercase tracking-wide text-teal-500">
                      14-day money back guarantee
                    </span>
                  </div>
                </div>
              </LoadingWrapper>
            </Card>
          </div>
        </div>

        <div class="md:order-1 xl:pr-12">
          <p class="text-lg font-bold tracking-tight text-primary-500">
            Select your preferred payment method
          </p>

          <div class="mt-5">
            <CheckoutPaymentMethodSelection
              :payment-providers="paymentProviders"
              :selected-payment-provider="selectedPaymentProvider"
              @select="selectedPaymentProvider = $event"
            />
          </div>

          <div class="mt-5 block">
            <CheckoutCoupon :discount-amount="discountAmount" :discount-percentage="discountPercentage" @couponApplied="applyDiscount($event)" />
          </div>

          <div class="mt-8 block">
            <MarketingTrustpilot size="h-5 w-5" />

            <ul class="mt-5 space-y-5 text-sm font-medium tracking-tight text-gray-500">
              <CheckoutBenefitItem icon="IconLock" text="Secure checkout - SSL encrypted" />
              <CheckoutBenefitItem icon="IconHeartAlt" :text="'Trusted by more than ' + happyCustomers + ' customers'" />
              <CheckoutBenefitItem icon="IconPayment" text="Money-back guarantee" />
              <CheckoutBenefitItem icon="IconSolidShield" text="We respect your privacy" />
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div class="fixed inset-x-0 bottom-0 border-t border-gray-100 bg-white py-3 shadow-sm md:hidden">
      <div class="mx-auto max-w-screen-xl px-5 sm:px-6 lg:px-8">
        <LoadingWrapper :is-loading="isLoading" title="Calculation your package">
          <div class="flex items-center justify-between gap-2 sm:gap-4">
            <div>
              <p class="text-base font-medium tracking-tight text-primary-500">
                Total
              </p>
              <div class="flex items-center gap-1.5">
                <p class="text-xl font-medium tracking-tight text-teal-500">
                  {{ formatPrice(total, currency, 2, false) }}
                </p>
              </div>
            </div>
            <LoadingWrapper :is-loading="isPaying" title="Redirecting to payment">
              <ButtonPrimary type="button" class="min-w-40" :is-disabled="!canPay" @click="redirectToPayment()">
                Pay now
              </ButtonPrimary>
            </LoadingWrapper>
          </div>
        </LoadingWrapper>
      </div>
    </div>
    <Popup v-if="isFulfilling">
      <div class="flex justify-center items-center">
        <LoadingSpinnerWhite class="!text-[#34A853] !w-5 !h-5" />
      </div>
      <div class="text-center mt-6">
        <p class="text-lg font-bold text-primary-500">
          {{ hasStyleCheckoutItems ? 'Starting your photo shoot' : 'Completing your order' }}
        </p>
        <nuxt-link v-if="showForceRedirect" :to="`/app/results/${$store.state.upsell.basket.modelId}`" class="text-sm font-medium text-[#807D96] underline mt-4">
          Click here to force redirect
        </nuxt-link>
      </div>
    </Popup>
    <Popup v-if="isPaying">
      <div class="flex justify-center items-center">
        <LoadingSpinnerWhite class="!text-[#34A853] !w-5 !h-5" />
      </div>
      <div class="text-center mt-6">
        <p class="text-lg font-bold text-primary-500">
          Redirecting to payment
        </p>
      </div>
    </Popup>
  </div>
</template>

<script>
import UpsellMixin from '@/mixins/UpsellMixin'
import CheckoutMixin from '@/mixins/CheckoutMixin'

export default {
  mixins: [UpsellMixin, CheckoutMixin],
  props: {
    tieredDiscount: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      productId: 'upsell',
      checkoutItemComponents: [],
      isShowingAllCheckoutItems: false,
      isFulfilling: false,
      isPaying: false,
      showForceRedirect: false
    }
  },
  computed: {
    currency () {
      return this.userCurrency || 'usd'
    },
    currentDiscount () {
      const selectedStylesCount = this.$store.state.upsell.selectedStyles?.length || 0
      const discountRange = this.tieredDiscount.ranges.find(range =>
        selectedStylesCount >= range.min && selectedStylesCount < range.max
      )
      return discountRange ? discountRange.discount : 0
    },
    basket () {
      return this.$store.state.upsell.basket
    },
    hasStyleCheckoutItems () {
      return this.checkoutItems.some(item => item.type === 'style')
    },
    subtotal () {
      let subtotal = 0

      // take the price property from each component directly
      this.checkoutItemComponents.forEach((item) => {
        subtotal += item.price
      })

      return subtotal
    },
    discountValue () {
      if (this.discountPercentage) {
        return this.subtotal * (this.discountPercentage / 100)
      } else if (this.discountAmount) {
        return this.discountAmount / 100
      }

      return 0
    },
    total () {
      return Math.max(this.subtotal - this.discountValue, 0)
    },
    checkoutItems () {
      const remixes = this.$store.state.upsell.basket.items.filter(item => item.type === 'remix')
      const styles = this.$store.state.upsell.basket.items.filter(item => item.type === 'style')
      return [...remixes, ...styles]
    },
    visibleCheckoutItems () {
      const remixes = this.checkoutItems.filter(item => item.type === 'remix')
      const visibleStyles = 2
      return this.isShowingAllCheckoutItems ? this.checkoutItems : this.checkoutItems.slice(0, remixes.length + visibleStyles)
    }
  },
  watch: {
    '$store.state.upsell.basket.items': {
      handler (newItems) {
        this.$nextTick(() => {
          this.checkoutItemComponents = this.$refs.checkoutItems || []
          if (newItems.length === 0) {
            this.$emit('back')
          }
        })
      },
      immediate: true
    }
  },
  mounted () {
    this.$posthog.capture('$upsell:checkout_page')
    this.$facebook.trackEvent('AddToUpsellCart')

    if (this.$route.query.payment === 'failed') {
      this.handleError('Payment failed, please try again or contact support.')
    }
    this.isLoading = false

    // true because the payment method is pre-selected
    this.$emit('set-can-continue', true)
  },
  methods: {
    confirmWithoutPayment () {
      if (this.isLoading) { return }

      this.$posthog.capture('$upsell:checkout_without_payment')
      this.isFulfilling = true
      this.isLoading = true
      this.$axios.post(`/upsell/basket/fulfill?modelId=${this.$store.state.upsell.basket.modelId}`, {
        discountCode: this.couponCode
      })
        .then((response) => {
          this.isLoading = false
          setTimeout(() => {
            this.showForceRedirect = true
          }, 1500)
          setTimeout(() => {
            this.isFulfilling = false
            this.$store.dispatch('upsell/load', { modelId: this.$store.state.upsell.basket.modelId })
            this.$router.push(`/app/results/${this.$store.state.upsell.basket.modelId}`)
          }, 5000)
        })
        .catch((error) => {
          this.isLoading = false
          this.isFulfilling = false
          this.handleError(error)
        })
    },
    showAllCheckoutItems () {
      this.isShowingAllCheckoutItems = true
    },
    async redirectToPayment () {
      this.$posthog.capture('$upsell:payment')
      this.isPaying = true
      try {
        await this.pay('upsell')
      } catch (error) {
        this.handleError(error)
      }

      this.isPaying = false
    },
    handleContinue () {
      if (this.total === 0) {
        this.confirmWithoutPayment()
      } else if (this.canPay) {
        this.redirectToPayment()
      }
    }
  }
}
</script>

<style>
@media screen and (max-width: 767px) {
  .chat-button {
    bottom: 10rem;
  }
}
</style>
