<template>
  <LoadingWrapper :is-loading="isLoading">
    <div class="flex items-center justify-between gap-3 group">
      <div class="w-[51px] h-[45px] rounded-lg relative overflow-hidden flex items-center justify-center" :class="{ 'bg-teal-500': item.type === 'remix' }">
        <img v-if="item.type === 'style'" :src="styleImage(item.styleId)" :alt="styleName(item.styleId)" class="w-full h-full object-cover">
        <IconViewfinderCircle v-else-if="item.type === 'remix'" class="text-white size-6" />
      </div>
      <div class="flex-1">
        <p class="text-base font-medium tracking-tight text-primary-500">
          <span v-if="item.type === 'style'">
            {{ styleName(item.styleId) }}
          </span>
          <span v-else-if="item.type === 'remix'">
            <InputSelect v-if="isEditing" :value="item.amount" :options="remixOptions" @input="updateRemixAmount" />
            <span v-else>{{ item.amount }} remix credits</span>
          </span>
        </p>
        <p v-if="item.type === 'style'" class="text-sm font-medium tracking-tight text-paragraph mt-[2px]">
          {{ clothingName(item.clothingId) }}
        </p>
      </div>
      <div class="flex items-start justify-end gap-2">
        <p class="text-sm font-medium tracking-tight text-primary-500" :class="{ 'hidden': isEditing, 'sm:group-hover:hidden': !isEditing }">
          {{ price > 0 ? formatPrice(price, currency, (currentDiscount > 0) ? 2 : 0, false) : 'Free' }}
        </p>
        <button v-if="item.type === 'remix'" type="button" class="text-sm font-medium tracking-tight text-primary-500" :class="{ 'sm:group-hover:block hidden': !isEditing }" @click="editItem">
          {{ isEditing ? 'Cancel' : 'Edit' }}
        </button>
        <button type="button" class="text-sm font-medium tracking-tight text-red-500" :class="{ 'sm:group-hover:block hidden': !isEditing }" @click="removeItem">
          Remove
        </button>
      </div>
    </div>
    <div class="sm:hidden mt-2 flex gap-4 items-center justify-end">
      <button v-if="item.type === 'remix'" type="button" class="text-sm font-medium tracking-tight text-primary-500" @click="editItem">
        {{ isEditing ? 'Cancel' : 'Edit' }}
      </button>
      <button type="button" class="text-sm font-medium tracking-tight text-red-500" @click="removeItem">
        Remove
      </button>
    </div>
  </LoadingWrapper>
</template>

<script>
import ModelPersonalizationMixin from '@/mixins/ModelPersonalizationMixin'
import UpsellMixin from '@/mixins/UpsellMixin'

export default {
  mixins: [ModelPersonalizationMixin, UpsellMixin],
  props: {
    item: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      required: true
    },
    currency: {
      type: String,
      required: true
    },
    tieredDiscount: {
      type: Object,
      default: null

    }
  },
  data () {
    return {
      isLoading: false,
      isEditing: false,
      remixOptions: [
        { value: 10, title: '10 remix credits' },
        { value: 20, title: '20 remix credits' },
        { value: 30, title: '30 remix credits' },
        { value: 40, title: '40 remix credits' },
        { value: 50, title: '50 remix credits' },
        { value: 60, title: '60 remix credits' },
        { value: 70, title: '70 remix credits' },
        { value: 80, title: '80 remix credits' },
        { value: 90, title: '90 remix credits' },
        { value: 100, title: '100 remix credits' }
      ]
    }
  },
  computed: {
    currentDiscount () {
      const selectedStylesCount = this.$store.state.upsell.selectedStyles?.length || 0
      const discountRange = this.tieredDiscount.ranges.find(range =>
        selectedStylesCount >= range.min && selectedStylesCount < range.max
      )
      return discountRange ? discountRange.discount : 0
    },
    gender () {
      return this.$store.state.results.item.trigger
    },
    price () {
      const prices = this.$store.state.upsell.prices
      if (!prices) { throw new Error('Prices not found') }

      const currency = this.userCurrency ?? 'usd'

      if (this.item.type === 'style') {
        const stylePrice = (prices.style?.currency[currency]) ? prices.style?.currency[currency] : prices.style.price
        if (this.index < this.$store.state.results.item.freeStyles) {
          return 0
        }

        return (stylePrice - (stylePrice * (this.currentDiscount / 100))) / 100
      }

      if (this.item.type === 'remix') {
        const remixPrice = (prices.remix?.currency[currency]) ? prices.remix?.currency[currency] : prices.remix.price
        return this.item.amount * remixPrice / 100
      }

      throw new Error('Invalid item type')
    }
  },
  methods: {
    async removeItem () {
      if (!confirm('Are you sure you want to remove this item?')) {
        return
      }

      this.isLoading = true
      const newItems = this.$store.state.upsell.basket.items.filter(item => item.identifier !== this.item.identifier)
      try {
        await this.$store.dispatch('upsell/updateBasket', { items: newItems, modelId: this.$store.state.upsell.basket.modelId })
      } catch (error) {
        console.error(error)
      } finally {
        this.isLoading = false
      }
    },
    editItem () {
      this.isEditing = !this.isEditing
    },
    async updateRemixAmount (amount) {
      const newItems = this.$store.state.upsell.basket.items.map((item) => {
        if (item.identifier === this.item.identifier) {
          return { ...item, amount }
        }

        return item
      })

      this.isLoading = true

      try {
        await this.$store.dispatch('upsell/updateBasket', { items: newItems, modelId: this.$store.state.upsell.basket.modelId })
      } catch (error) {
        console.error(error)
      } finally {
        this.isLoading = false
        this.isEditing = false
      }
    }
  }
}
</script>
