<template>
  <div class="bg-green-50 rounded-md border border-green-100 w-full">
    <div class="p-4 px-6 flex flex-col justify-center items-center">
      <h3 class="font-bold">
        ✨ Get more headshots for just $1!
      </h3>
      <p class="mb-3 mt-1.5 font-normal text-sm text-gray-700">
        <strong>Limited time:</strong> Get 20 extra headshots in any style for just a dollar
      </p>
      <div class="w-full flex flex-row items-center justify-center space-x-2">
        <nuxt-link :to="`/app/styles/${id}`">
          <ButtonDark class="w-[fit-content]" size="sm">
            Get your headshots now
          </ButtonDark>
        </nuxt-link>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    id: {
      type: String,
      required: true
    }
  }
}
</script>
