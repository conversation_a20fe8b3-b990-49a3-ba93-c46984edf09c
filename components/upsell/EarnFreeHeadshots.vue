<template>
  <Card>
    <header class="flex items-center justify-between">
      <div class="text-paragraph">
        <div class="flex items-center space-x-2">
          <IconGift class="w-6 h-6" />
          <h2 class="text-lg font-semibold text-primary-500">
            Earn free photos
          </h2>
        </div>
        <p>There {{ activeOffers === 1 ? 'is' : 'are' }} currently {{ activeOffers }} active {{ activeOffers === 1 ? 'offer' : 'offers' }}.</p>
      </div>
    </header>
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mt-4">
      <div v-if="shouldShowUpsell">
        <div>
          <Card>
            <div>
              <span class="bg-teal-500 text-white px-2 py-1 rounded-full text-xs uppercase font-medium">
                20 free headshots
              </span>
              <h3 class="text-lg font-bold text-black mt-4">
                Refer & unlock 20 free headshots
              </h3>
              <p class="text-paragraph text-sm mt-2">
                Love your new headshots? Share the experience and get 20 free credits for every friend who joins.
              </p>
            </div>
            <div class="pt-4 mt-auto">
              <ButtonDark size="sm" @click="handleReferralClick">
                <span>Get extra headshots</span>
                <IconChevron class="w-3 h-3 ml-2" />
              </ButtonDark>
            </div>
          </Card>
        </div>
      </div>
      <div v-if="!hasSubmittedFeedback">
        <div>
          <Card>
            <div>
              <span class="bg-teal-500 text-white px-2 py-1 rounded-full text-xs uppercase font-medium">
                10 free photos
              </span>
              <h3 class="text-lg font-bold text-black mt-4">
                Get free headshots for your feedback
              </h3>
              <p class="text-paragraph text-sm mt-2">
                Limited time: Get 10 extra headshots in any style for leaving your feedback. Only takes 2 minutes and helps us improve our service.
              </p>
            </div>
            <div class="pt-4 mt-auto">
              <ButtonDark size="sm" @click="$router.push('/app/feedback')">
                <span>Get extra headshots</span>
                <IconChevron class="w-3 h-3 ml-2" />
              </ButtonDark>
            </div>
          </Card>
        </div>
      </div>
      <div v-if="eligibleForTrustpilotReview">
        <div>
          <Card>
            <div>
              <img src="@/assets/img/logo-trustpilot.png" class="w-[100px]">
              <h3 class="text-lg font-bold text-black mt-4">
                Review us on Trustpilot
              </h3>
              <p class="text-paragraph text-sm mt-2">
                Help us spread the word and leave a review on Trustpilot. It takes 30 seconds and it really helps us spread the word.
              </p>
            </div>
            <div class="pt-4 mt-auto">
              <ButtonDark size="sm" @click="openTrustpilotPopup">
                <span>Leave a review</span>
                <IconChevron class="w-3 h-3 ml-2" />
              </ButtonDark>
            </div>
          </Card>
        </div>
      </div>
      <!-- <div>
        <Card>
          <div>
            <span class="bg-teal-500 text-white px-2 py-1 rounded-full text-xs uppercase font-medium">
              Full refund
            </span>
            <h3 class="text-lg font-bold text-black mt-4">
              Review HeadshotPro for Free<sup>*</sup>
            </h3>
            <p class="text-paragraph text-sm mt-2">
              We're giving out discounts of up to 100% off* to test our AI headshot generator, ensuring that they have all the information they need to deliver a complete review.
              <ol class="list-decimal list-inside pl-4 block mt-2">
                <li>Complete a simple form (estimate time to complete: 2 minutes)</li>
                <li>Give us 72 business hours to review your submission</li>
                <li>If approved, you'll receive a special Review discount code in your inbox</li>
              </ol>
            </p>
          </div>
          <div class="pt-4 mt-auto">
            <ButtonWhite size="sm" class="w-full" @click="fillInForm">
              <span>Fill in form</span>
              <IconChevron class="w-3 h-3 ml-2" />
            </ButtonWhite>
          </div>
        </Card>
      </div> -->
    </div>
  </Card>
</template>

<script>
export default {
  computed: {
    hasSubmittedFeedback () {
      return this.$store.state.user.rewards?.feedback
    },
    activeOffers () {
      let totalOffers = 0
      if (!this.hasSubmittedFeedback) {
        totalOffers += 1
      }
      if (this.eligibleForTrustpilotReview) {
        totalOffers += 1
      }

      if (this.shouldShowUpsell) {
        totalOffers += 1
      }

      return totalOffers
    },
    eligibleForTrustpilotReview () {
      if (this.$store.state.user.rewards?.trustpilot) { return false }
      if (this.$store.state.results.length === 0) { return false }
      const item = this.$store.state.results.item
      if (item.downloads < 4) { return false }
      if (item.images.filter(image => image.likedStatus === 'favorite').length < 5) { return false }
      return true
    }
  },
  mounted () {
    this.$posthog.capture('$upsell:earn_free_headshots_page')
  },
  methods: {
    fillInForm () {
      window.open('https://tally.so/r/3N4zYj', '_blank')
    },
    handleReferralClick () {
      this.$emit('referral-click')
    },
    openTrustpilotPopup () {
      this.$store.commit('SET_SHOW_REVIEW_REQUEST_MODAL', true)
    }
  }
}
</script>
