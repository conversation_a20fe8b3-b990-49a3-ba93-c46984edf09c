<template>
  <Card v-if="item.mightLikeOptions?.length > 0">
    <header class="flex items-center justify-between">
      <div class="text-paragraph">
        <div class="flex items-center space-x-2">
          <IconCurrencyDollar class="w-6 h-6" />
          <h2 class="text-lg font-semibold text-primary-500">
            You might like
          </h2>
        </div>
        <p>
          We’ve created some additional photos for you based on what others liked.
        </p>
      </div>
      <div class="flex-shrink-0 ml-4">
        <ButtonPrimary size="sm" @click="unlockAll">
          Unlock all
          <IconPlus class="!w-4 !h-4 ml-2" />
        </ButtonPrimary>
      </div>
    </header>
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4 mt-4">
      <div
        v-for="option in item.mightLikeOptions"
        :key="option.thumbnail"
        class="transition group relative box-content cursor-pointer overflow-hidden rounded-xl group"
        @click="unlock(option)"
      >
        <ImageDns :src="option.thumbnail" class="overflow-hidden w-full aspect-result transition duration-300 object-cover blur-sm" />
        <div class="bg-black/50 absolute inset-0 flex flex-col items-center justify-end px-8 py-6 text-center group-hover:bg-black/15 transition duration-300">
          <div class="absolute top-[10px] right-[10px] px-2 py-[6px] bg-teal-500 rounded-full text-[9px] text-white">
            POPULAR
          </div>
          <p class="text-white text-base font-bold">
            {{ clothingName(option.clothing) }}
          </p>
          <IconPlus class="w-4 h-4 text-white" />
          <p class="text-white text-base font-bold">
            {{ styleName(option.style) }}
          </p>
          <ButtonWhite size="sm" class="w-full mt-4">
            Unlock 10 photos
            <svg
              width="6"
              height="10"
              viewBox="0 0 6 10"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              class="ml-4 translate-y-px"
            >
              <path fill-rule="evenodd" clip-rule="evenodd" d="M0.397899 0.697703C0.661502 0.434099 1.08889 0.434099 1.35249 0.697703L5.17749 4.5227C5.4411 4.78631 5.4411 5.21369 5.17749 5.4773L1.35249 9.3023C1.08889 9.5659 0.661502 9.5659 0.397898 9.3023C0.134294 9.03869 0.134294 8.61131 0.397898 8.3477L3.7456 5L0.397899 1.6523C0.134295 1.38869 0.134295 0.961307 0.397899 0.697703Z" fill="#0F172A" />
            </svg>
          </ButtonWhite>
        </div>
      </div>
    </div>
  </Card>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  mounted () {
    this.$posthog.capture('$upsell:you_might_like_page')
  },
  methods: {
    unlockAll () {
      const items = this.item.mightLikeOptions.map(option => ({
        type: 'style',
        styleId: option.style,
        clothingId: option.clothing
      }))
      this.setBasket(items)
    },
    unlock (option) {
      const items = [
        {
          type: 'style',
          styleId: option.style,
          clothingId: option.clothing
        }
      ]
      this.setBasket(items)
    },
    styleName (id) {
      const style = (this.$store.state.styles || []).find(style => style._id === id)
      return this.slugToTitle(style?.title || '')
    },
    clothingName (id) {
      const clothing = (this.$store.state.clothing || []).find(clothing => clothing._id === id)
      return this.slugToTitle(clothing?.type || '')
    },
    setBasket (items) {
      const appearance = this.$store.getters['upsell/appearance']

      this.$store.dispatch('upsell/updateBasket', {
        items,
        modelId: this.item._id,
        appearance
      }).then(() => {
        this.$router.push(`/app/results/${this.item._id}/unlock-more?step=1`)
      }).catch((err) => {
        this.handleError(err)
      })
    }
  }
}
</script>
