<template>
  <div v-if="showWarning" class="flex items-center gap-x-6 border-y border-orange-400 bg-orange-300 py-2.5 px-6 sm:px-3.5 sm:before:flex-1">
    <p class="text-sm leading-6 text-gray-900">
      In AI-photography, not every shot is a winner. We provide a variety of options to ensure that you can pick the highest quality image.
    </p>
    <div class="flex flex-1 justify-end">
      <button type="button" class="-m-3 p-3 focus-visible:outline-offset-[-4px]" @click="hideWarning">
        <span class="sr-only">Dismiss</span>
        <svg class="h-5 w-5 text-gray-900" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z" />
        </svg>
      </button>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      showWarning: false
    }
  },
  mounted () {
    if (!this.$cookies.get('show-warning') === 'true' && !this.$cookies.get('show-warning') === 'false') {
      this.$cookies.set('show-warning', 'true')
    }
    this.showWarning = this.$cookies.get('show-warning')
  },
  methods: {
    hideWarning () {
      this.$cookies.set('show-warning', 'false')
      this.showWarning = false
    }
  }
}
</script>

<style></style>
