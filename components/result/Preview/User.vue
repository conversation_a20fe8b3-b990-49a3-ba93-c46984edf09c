<template>
  <div>
    <!-- <ResultUserHeader :user="user" :downloads="downloads" /> -->
    <div class="w-full">
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6">
        <template v-if="isLoading">
          <div v-for="index in 70" :key="index" class="p-1">
            <div class="h-[calc(100vh/6)] w-full animate-pulse bg-white/10" />
          </div>
        </template>
        <template v-else>
          <template v-for="(photo, index) in user.images">
            <!-- Desktop -->
            <div :key="photo._id" class="hidden md:flex item group relative w-full cursor-pointer p-1">
              <div :class="isSelected(photo._id) ? ' border-yellow-500 hover:border-green-600' : 'border-gray-900 hover:border-white/20'" class="shadow relative cursor-pointer overflow-hidden border-[4px]">
                <ImageDns :src="photo?.thumbnail" class="w-full transition duration-300 group-hover:rotate-2 group-hover:scale-105 group-hover:bg-black/30 group-hover:blur-sm" @click="select(photo._id)" />
              </div>
              <div class="absolute inset-0 hidden items-center justify-center group-hover:flex bg-black/20" @click="select(photo._id)">
                <span class="text-white/80"> Click to {{ isSelected(photo._id) ? "deselect" : "select" }}</span>
              </div>
              <div class="absolute w-full bottom-1 p-4 left-0">
                <div class="grid grid-cols-2 gap-2 ">
                  <ButtonDark class="group-hover:flex hidden" size="sm" @click="toggleModal(photo?.preview || photo.image)">
                    <IconSolidEye class="w-4 h-4 text-white/50 mr-1.5" />
                    <span class="flex-shrink-0">Bigger</span>
                  </ButtonDark>
                  <ButtonDark class=" group-hover:flex hidden" size="sm" @click="downloadSingleFromUrl(photo.image, photo._id, photo.thumbnail)">
                    <IconSolidDownload class="w-4 h-4 text-white/50 mr-1.5" />
                    <span class="flex-shrink-0">Download</span>
                  </ButtonDark>
                </div>
              </div>
            </div>
            <!-- Mobile -->
            <div :key="photo._id" class="flex md:hidden item  relative w-full cursor-pointer p-1">
              <div class="shadow relative cursor-pointer overflow-hidden border-[4px] border-gray-900 hover:border-white/20">
                <ImageDns :src="photo?.thumbnail" class="w-full transition duration-300" />
              </div>
              <div class="absolute w-full bottom-0.5 p-4 left-0">
                <div class="grid grid-cols-1 gap-2 ">
                  <ButtonDark class="flex" size="xs" @click="downloadSingleFromUrl(photo.image, photo._id, photo.thumbnail)">
                    <IconSolidDownload class="w-3 h-3 text-white/50 mr-1.5" />
                    <span class="flex-shrink-0">Download</span>
                  </ButtonDark>
                </div>
              </div>
            </div>
            <ResultUpsaleBlock v-if="!isTeamMember && (index === 9 || index === 33 || index === 103) && !isLoading" :key="index + '-upsell'" />
            <!-- <ResultShareBlock v-if="!isTeamMember && (index === 15 || index === 115) && !isLoading && !user.rewards.share" :key="index" @click="showTweetModal = true" /> -->
          </template>
        </template>
      </div>
      <ShareTweetModal v-if="showTweetModal" @closeModal="showTweetModal = false" @shared="$emit('shared')" />
      <Modal
        v-if="showModal"
        @close="
          showModal = false;
          previewImage = null;
        "
      >
        <ImageDns :src="previewImage" class="w-full max-w-xl" />
      </Modal>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ResultUser',
  props: {
    downloads: {
      type: Number,
      default: 0
    },
    user: {
      type: Object,
      default: () => {}
    },
    isLoading: {
      type: Boolean,
      default: false
    },
    selected: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      showModal: false,
      previewImage: null,
      showTweetModal: false
    }
  },
  computed: {
    tweetImages () {
      return this.user.images.slice(0, 4).map(image => image.thumbnail)
    }
  },
  methods: {
    select (id) {
      this.$emit('select', id)
    },
    isSelected (id) {
      return this.selected.includes(id)
    },
    downloadSingleFromUrl (url, photoId, thumbnailPath) {
      this.$toast.success('Your image will download shortly. Make sure to accept the download prompt.')
      fetch(url)
        .then(response => response.blob())
        .then((blob) => {
          const url = window.URL.createObjectURL(new Blob([blob]))
          const link = document.createElement('a')
          link.href = url
          const fileType = blob.type.split('/')[1]
          const fileName = `${photoId}-HeadshotPro.${fileType}`
          link.setAttribute('download', fileName)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          this.trackDownload(photoId)
          this.$emit('download')
        })
    },
    toggleModal (image) {
      this.showModal = true
      this.previewImage = image
    },
    trackDownload (photoId) {
      this.$axios.$post('/model/download/track/' + this.$route.params.id, { photoId })
    }
  }
}
</script>

<style></style>
