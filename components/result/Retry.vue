<template>
  <div class="max-w-lg px-6 py-4 text-sm">
    <div class="space-y-2">
      <h2 class="text-xl font-bold">
        Retry shoot
      </h2>
      <p class="pb-2 text-gray-700">
        Not happy with your team member's result? Give them another chance.
      </p>
      <strong>Here's what happens next:</strong>
      <ul class="list-decimal ml-4 space-y-1">
        <li>Your team member's results will be deleted.</li>
        <li>Your team member's will be notified via email to retry.</li>
      </ul>
      <p>Please make sure to inform them to <strong>upload their best photos</strong> this time. You have one free retry per team member.</p>
    </div>
    <div class="mt-4 flex w-full items-center justify-end">
      <LoadingWrapper :is-loading="isLoading">
        <ButtonDelete size="sm" @click="retryShoot">
          <span>Delete results and retry</span>
          <IconChevron class="w-4 h-4 ml-1.5" />
        </ButtonDelete>
        <!-- <ButtonDisabled v-else size="sm">
          Not enough credits
        </ButtonDisabled> -->
      </LoadingWrapper>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    userId: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      isLoading: false
    }
  },
  computed: {
    credits () {
      return this.$store.state?.organization?.organization?.credits || 0
    }
  },
  methods: {
    async retryShoot () {
      try {
        this.isLoading = true
        if (!this.userId) {
          throw new Error('User ID is required')
        }
        // if (this.credits <= 0) {
        //   throw new Error('You do not have enough credits to retry a shoot')
        // }
        const { success, errorMessage } = await this.$axios.$post('/organization/retry-shoot/', {
          userId: this.userId
        })
        if (!success) {
          throw new Error(errorMessage)
        }

        this.$toast.success('Shoot is ready to be retried')
        // this.$store.commit('organization/SET_CREDITS', this.credits - 1)
        this.$emit('success')
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = false
      }
    }
  }
}
</script>

<style></style>
