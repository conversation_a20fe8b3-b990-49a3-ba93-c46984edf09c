<template>
  <div v-if="nextPackage && !upselled && !isNewReferredUser" class="bg-green-50 rounded-md py-3.5 px-2.5 flex flex-col space-y-2 border border-green-100">
    <h3 class="text-green-700 font-bold text-sm tracking-tight">
      <IconSmallTag class="text-green-700 w-4 h-4 inline-block mr-0.5" />
      {{ $t('getMoreHeadshots', { percentage: moreHeadshotsPercentage, price: formatPrice(priceDifference/2, currency, 0, false) }) }}
    </h3>
    <p class="text-black/70 font-normal text-xs hidden md:block">
      <span

        v-html="$t('secretSale', {
          oldPrice: formatPrice(priceDifference, currency, 0, false),
          newPrice: formatPrice(priceDifference/2, currency, 0, false),
          package: nextPackage.title,
          headshots: nextHeadshots,
          combinations: nextPackage.meta.styles + nextPackage.meta.additional
        })"
      />
      <span v-if="oldTurnaround != newTurnaround" class="line-through">{{ oldTurnaround }}</span>
      <span>{{ newTurnaround.replace('hour', $t('hour')) }}</span>.
    </p>
    <button type="button" class="flex content-center items-center justify-center rounded-md border border-black/10 bg-black/5 hover:bg-black/10 px-4 py-2 text-center text-xs font-medium text-black/80 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2" @click="upgradePackage()">
      {{ $t('Upgrade now') }}
    </button>
  </div>
  <div v-else-if="activePackage && upselled" class="bg-green-50 rounded-md py-3.5 px-2.5 flex flex-col space-y-2 border border-green-100">
    <h3 class="text-green-700 font-bold text-sm tracking-tight">
      <IconSolidCheck class="text-green-700 w-4 h-4 inline-block mr-0.5" />
      {{ $t('You\'ve upgraded your order') }}
    </h3>
    <p class="text-black/70 font-normal text-xs">
      {{ $t('goodChoice', { package: activePackage.title }) }}
    </p>
    <button type="button" class="flex content-center items-center justify-center rounded-md border border-black/10 bg-black/5 hover:bg-black/10 px-4 py-2 text-center text-xs font-medium text-black/80 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2" @click="downgradePackage()">
      {{ $t('Click here to undo') }}
    </button>
  </div>
</template>

<script>
export default {
  props: {
    package: {
      type: String,
      required: false
    },
    upselled: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  // data () {
  //   return {
  //     currency: 'usd'
  //   }
  // },
  computed: {
    currency () {
      return this.userCurrency
    },
    activePackage () {
      if (!this.package) {
        return null
      }

      const p = Object.values(this.$store.state.packages)
        .filter(p => p.id === this.package)
        .pop()

      return p
    },
    nextPackage () {
      if (!this.activePackage) {
        return null
      }

      let found = false
      const orderedPackages = Object.values(this.$store.state.packages).sort((a, b) => this.getLocalizedPrice(a.id).price - this.getLocalizedPrice(b.id).price)
      for (const p of orderedPackages) {
        if (found && p.meta.upsellDiscount) {
          return p
        }

        if (p.id === this.activePackage.id) {
          found = true
        }
      }

      return null
    },
    prevPackage () {
      if (!this.activePackage) {
        return null
      }

      let found = false
      const orderedPackages = Object.values(this.$store.state.packages).sort((a, b) => this.getLocalizedPrice(b.id).price - this.getLocalizedPrice(a.id).price)
      for (const p of orderedPackages) {
        if (found) {
          return p
        }

        if (p.id === this.activePackage.id) {
          found = true
        }
      }

      return null
    },
    nextHeadshots () {
      if (!this.activePackage || !this.nextPackage) {
        return 0
      }

      const newPhotos = (this.nextPackage.meta.styles + this.nextPackage.meta.additional) * this.photoPerStyle

      return newPhotos
    },
    moreHeadshotsPercentage () {
      if (!this.activePackage || !this.nextPackage) {
        return 0
      }

      const currentPhotos = (this.activePackage.meta.styles + this.activePackage.meta.additional) * this.photoPerStyle
      const newPhotos = (this.nextPackage.meta.styles + this.nextPackage.meta.additional) * this.photoPerStyle

      return Math.round((newPhotos - currentPhotos) / currentPhotos * 100)
    },
    priceDifference () {
      if (!this.activePackage || !this.nextPackage) {
        return 0
      }

      return (this.getLocalizedPrice(this.nextPackage.id).price - this.getLocalizedPrice(this.activePackage.id).price) / 100
    },
    oldTurnaround () {
      if (!this.activePackage) {
        return null
      }

      return (this.activePackage.turnAroundTime || '2 hours').replace('hour', this.$t('hour'))
    },
    newTurnaround () {
      if (!this.nextPackage) {
        return null
      }

      return (this.nextPackage.turnAroundTime || '2 hours').replace('hour', this.$t('hour'))
    }
  },
  mounted () {
    //
  },
  methods: {
    upgradePackage () {
      this.$emit('change', this.nextPackage.id)
    },
    downgradePackage () {
      this.$emit('downgrade', this.prevPackage.id)
    }
  }

}
</script>

<i18n>
  {
    "en": {
      "hour": "hour",
      "getMoreHeadshots": "Get {percentage}% More Headshots for {price}",
      "Upgrade now": "Upgrade now",
      "You've upgraded your order": "You've upgraded your order",
      "goodChoice": "Good choice. You've upgraded to the {package}.",
      "Click here to undo": "Click here to undo",
      "secretSale": "Limited time offer: Upgrade to the {package} pack for <span class=\"line-through\">{oldPrice}</span> {newPrice} and get {headshots} headshots with {combinations} backdrops and outfits, delivered in"
    },
    "es": {
      "hour": "hora",
      "getMoreHeadshots": "Obtén un {percentage}% más de fotos por {price}",
      "Upgrade now": "Actualizar ahora",
      "You've upgraded your order": "Has actualizado tu pedido",
      "goodChoice": "Buena elección. Has actualizado a {package}.",
      "Click here to undo": "Haz clic aquí para deshacer",
      "secretSale": "Oferta por tiempo limitado: Actualiza al pack {package} por <span class=\"line-through\">{oldPrice}</span> {newPrice} y obtén {headshots} fotos con {combinations} backdrops y outfits, entregadas en"
    },
    "de": {
      "hour": "Stunde",
      "getMoreHeadshots": "{percentage}% mehr Bewerbungsfotos für {price}",
      "Upgrade now": "Jetzt upgraden",
      "You've upgraded your order": "Du hast deine Bestellung erweitert",
      "goodChoice": "Gute Wahl. Du hast auf das {package} upgegradet.",
      "Click here to undo": "Hier klicken zum Rückgängigmachen",
      "secretSale": "Zeitlich begrenztes Angebot: Upgrade auf das {package} Paket für <span class=\"line-through\">{oldPrice}</span> {newPrice} und erhalte {headshots} Bewerbungsfotos mit {combinations} Hintergründen und Outfits, geliefert in"
    }
  }
</i18n>
