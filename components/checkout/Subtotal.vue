<template>
  <div class="rounded-md bg-gray-50 border border-gray-100">
    <div class="p-2 px-4">
      <div class="flex-col space-y-2 flex  items-start justify-start">
        <slot />
        <div class="border-t border-black/5 pt-2.5 pb-1 flex flex-1 items-center justify-between w-full">
          <p class="text-sm font-normal text-gray-900">
            <span class="flex md:hidden">Checkout total:</span>
            <span class="hidden md:flex">Total checkout price:</span>
          </p>
          <span class="font-bold text-green-600">{{ price }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    price: {
      type: String,
      default: null,
      required: true
    },
    trial: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style></style>
