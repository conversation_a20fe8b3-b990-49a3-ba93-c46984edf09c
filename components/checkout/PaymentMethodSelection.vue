<template>
  <div class="space-y-2.5">
    <template v-for="paymentProvider in paymentProviders">
      <template v-if="!paymentProvider.currencies || paymentProvider.currencies.includes(userCurrency?.toLowerCase() || 'usd')">
        <div
          v-if="paymentProvider.name !== 'Apple Pay' || isSafariBrowser"
          :key="paymentProvider.id"
          class="relative block w-full text-left"
          :class="{
            'cursor-pointer transition-transform duration-150 ease-in-out hover:scale-105': theme === 'radio'
          }"
          @click="theme === 'radio' ? $emit('select', paymentProvider.id) : undefined"
        >
          <div
            v-if="theme === 'radio'"
            class="absolute right-5 top-1/2 flex size-5 -translate-y-1/2 items-center justify-center rounded-full border border-gray-300"
            :class="{
              'border-teal-500 bg-teal-500': selectedPaymentProvider === paymentProvider.id,
              'bg-white': selectedPaymentProvider !== paymentProvider.id
            }"
          >
            <div class="h-2 w-2 rounded-full bg-white" />
          </div>
          <div
            v-else-if="theme === 'button'"
            class="hidden sm:absolute sm:right-5 sm:top-1/2 sm:flex sm:-translate-y-1/2 sm:items-center sm:justify-end"
          >
            <ButtonPrimary v-if="paymentProvider.isPrimary" size="sm" @click="$emit('select', paymentProvider.id)">
              Pay now
            </ButtonPrimary>
            <ButtonWhite v-else size="sm" @click="$emit('select', paymentProvider.id)">
              Pay now
            </ButtonWhite>
          </div>

          <div
            class="border-ring-100 rounded-xl pb-5 pl-5 pt-5 shadow-sm ring-1"
            :class="{
              'bg-teal-50 ring-teal-500': selectedPaymentProvider === paymentProvider.id,
              'bg-white ring-black/5': selectedPaymentProvider !== paymentProvider.id,
              'cursor-pointer hover:bg-blue-50 pr-12': theme === 'radio',
              'pr-5 sm:pr-12': theme === 'button'
            }"
          >
            <div class="flex items-center justify-between">
              <div
                class="flex items-center gap-2.5 lg:gap-5"
                :class="{
                  'flex-wrap': paymentProvider.images.length > 1
                }"
              >
                <span class="block text-sm font-bold tracking-tight text-primary-500 sm:text-lg">{{ $t('Pay with') }} {{ paymentProvider.name }}</span>
                <span
                  v-if="paymentProvider.description"
                  class="block text-xs font-normal italic text-gray-500 md:text-sm"
                >
                  {{ paymentProvider.description }}
                </span>
                <div class="flex items-center gap-2.5">
                  <img v-for="(image, idx) in paymentProvider.images" :key="idx" class="h-6 w-auto" :src="image" alt="">
                </div>
              </div>
            </div>
            <div
              v-if="theme === 'button'"
              class="block sm:hidden mt-4"
            >
              <ButtonPrimary v-if="paymentProvider.isPrimary" size="sm" class="w-full" @click="$emit('select', paymentProvider.id)">
                Pay now
              </ButtonPrimary>
              <ButtonWhite v-else size="sm" class="w-full" @click="$emit('select', paymentProvider.id)">
                Pay now
              </ButtonWhite>
            </div>
          </div>
        </div>
      </template>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    theme: {
      type: String,
      default: 'radio'
    },
    paymentProviders: {
      type: Array,
      required: true
    },
    selectedPaymentProvider: {
      type: String,
      required: false
    }
  },
  computed: {
    isSafariBrowser () {
      return /^((?!chrome|android).)*safari/i.test(navigator.userAgent)
    }
  }
}
</script>
