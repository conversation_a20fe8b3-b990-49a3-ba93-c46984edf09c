<template>
  <div class="flex py-5 space-x-4">
    <div class="flex-shrink-0">
      <img
        :src="item.imageUrl"
        class="object-cover w-16 h-12 rounded-lg border border-gray-200"
        alt=""
      >
    </div>
    <div class="relative flex flex-col justify-between flex-1">
      <div class="sm:grid sm:grid-cols-3 sm:gap-x-5 w-full">
        <div class="pr-9 sm:pr-5 col-span-2">
          <p class="text-sm font-bold text-gray-900">
            {{ item.title }}
          </p>
          <p v-if="item.description" class="mt-1.5 text-xs font-medium text-gray-500">
            {{ stripLength(item.description, 40) }}
          </p>
        </div>

        <div class="flex items-end justify-between mt-4 sm:items-start sm:justify-end sm:mt-0">
          <p class="flex-shrink-0 w-20 text-sm font-bold text-left text-gray-900 sm:text-right sm:ml-8 sm:order-2">
            {{ formatPrice(item.price/100, 'USD') }}
          </p>
        </div>
      </div>

      <div class="absolute top-0 right-0 flex sm:bottom-0 sm:top-auto">
        <button type="button" class="inline-flex p-2 -m-2 text-gray-400 transition-all duration-200 rounded focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-900 hover:text-gray-900" @click="removeFromCart(item._id)">
          <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true
    },
    showImage: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    removeFromCart (id) {
      this.$store.dispatch('cart/removeItemFromCart', id)
    }
  }

}
</script>

<style>

</style>
