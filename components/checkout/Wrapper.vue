<template>
  <div>
    <!-- <nuxt-link :to="`/checkout/?priceId=${priceId}`" class="block"> -->
    <button @click="showModal = true">
      <slot />
    </button>
    <!-- </nuxt-link> -->
    <portal to="modal">
      <CheckoutModal v-if="showModal" @closeModal="showModal = false" />
    </portal>
  </div>
</template>

<script>
export default {
  data () {
    return {
      showModal: false
    }
  },
  computed: {
    priceId () {
      return (this.$config.NODE_ENV === 'development') ? 'price_1LxoECDeLNYZ5KRAFhDctm6G' : 'price_1Lz6NDDeLNYZ5KRAz8o4Gueq'
    }
  }

}
</script>

<style>

</style>
