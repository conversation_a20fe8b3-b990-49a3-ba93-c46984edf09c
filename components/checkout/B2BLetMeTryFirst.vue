<template>
  <CheckoutB2BPaymentCard
    title="Let me try first"
    subtitle="Rather try yourself first before paying for your whole team."
    :how-it-works="[
      'Purchase one seat for yourself to try out.',
      'Whenever you\'re ready, purchase seats for your team based on the payment methods on the left.',
    ]"
  >
    <CheckoutB2BSummaryCard
      title="Price per person"
      subtitle="80 headshots per person, with 8 unique backdrops and clothing items."
      total-text="Total now"
      :quantity="1"
      :unit-price="price/100"
    />
    <div class="mt-4">
      <CheckoutPaymentMethodSelection
        :payment-providers="[{ ...paymentProviders[0], isPrimary: true }, ...paymentProviders.slice(1)]"
        theme="button"
        @select="selectPaymentProvider"
      />
    </div>
  </CheckoutB2BPaymentCard>
</template>

<script>
import CheckoutMixin from '@/mixins/CheckoutMixin'

export default {
  mixins: [CheckoutMixin],
  data () {
    return {
      selectedPaymentProvider: null,
      priceDetails: null,
      product: null
    }
  },
  computed: {
    priceId () {
      return 'medium'
    },
    price () {
      try {
        return this.priceDetails?.currency[this.userCurrency] ?? this.priceDetails?.price
      } catch (err) {
        return this.priceDetails?.price
      }
    }
  },
  mounted () {
    this.getPackage()
  },
  methods: {
    async getPackage (id = null) {
      try {
        this.$loading.show({
          title: 'Loading price...'
        })
        const { success, data, errorMessage } = await this.$axios.$get(`/checkout/price?productId=package&priceId=${this.priceId}`)

        if (!success) {
          throw new Error(errorMessage)
        }

        if (!data?.product) {
          throw new Error('Product not found')
        }

        if (!data?.price) {
          throw new Error('Price not found')
        }

        const { product, price } = data

        this.priceDetails = price

        if (product) {
          const { description, images, name } = product
          this.product = { description, name, image: images[0] }
        }
      } catch (err) {
        this.handleError(err)
      } finally {
        this.$loading.hide()
      }
    },
    selectPaymentProvider (paymentProviderId) {
      this.selectedPaymentProvider = paymentProviderId
      this.toTeamCheckout()
    }
  }
}
</script>
