<template>
  <div class="space-y-2.5">
    <button type="button" class="relative block w-full text-left transition-transform duration-150 ease-in-out hover:scale-105" @click="$emit('select', value)">
      <div
        class="absolute right-5 top-1/2 flex size-5 -translate-y-1/2 items-center justify-center rounded-full border border-gray-300"
        :class="{
          'border-teal-500 bg-teal-500': isActive,
          'bg-white': !isActive
        }"
      >
        <div class="h-2 w-2 rounded-full bg-white" />
      </div>

      <div
        class="border-ring-100 cursor-pointer rounded-xl pb-5 pl-5 pr-12 pt-5 shadow-sm ring-1"
        :class="{
          'bg-teal-50 ring-teal-500': isActive,
          'bg-white ring-black/5 hover:bg-blue-50': !isActive
        }"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2.5 lg:gap-3">
            <div class="bg-white rounded-lg border border-[#E4E4E7] flex items-center justify-center h-8 w-8">
              <IconDollarPaper class="h-5 w-4 text-paragraph" />
            </div>
            <span class="block text-sm font-bold tracking-tight text-primary-500 sm:text-lg">{{ label }}</span>
          </div>
        </div>
      </div>
    </button>
  </div>
</template>

<script>
export default {
  props: {
    label: {
      type: String,
      required: true
    },
    value: {
      type: String,
      required: true
    },
    isActive: {
      type: Boolean,
      required: true
    }
  }
}
</script>
