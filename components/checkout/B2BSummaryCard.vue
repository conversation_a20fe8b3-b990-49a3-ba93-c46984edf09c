<template>
  <div class="bg-[#FAFAFA] border border-black/10 rounded-lg p-4">
    <div class="flex justify-between items-start">
      <div
        class="flex flex-col items-start justify-start"
        :class="{
          'max-w-[306px]': discountPercentage === 0
        }"
      >
        <p class="text-base font-semibold text-primary-500">
          {{ title }}
        </p>
        <p class="text-sm font-medium text-paragraph">
          {{ subtitle }}
        </p>
      </div>
      <div class="flex items-center justify-end">
        <p class="text-base font-medium text-primary-500">
          {{ formatPrice(unitPrice, userCurrency, 2, false) }}
        </p>
      </div>
    </div>
    <hr class="my-4 border-[#E4E4E7CC]">
    <div class="flex justify-between items-center mb-4 text-base font-semibold text-primary-500">
      <p>
        Subtotal
      </p>
      <p>
        {{ formatPrice(subtotal, userCurrency, 2, false) }}
      </p>
    </div>
    <div v-if="discount > 0" class="flex justify-between items-center text-base font-normal text-primary-500">
      <p>
        Discount
      </p>
      <p class="text-green-500">
        -{{ discountPercentage }}
      </p>
    </div>
    <hr class="my-4 border-[#E4E4E7CC]">
    <div class="flex justify-between items-center">
      <p class="text-xl font-semibold text-primary-500">
        {{ totalText }}
      </p>
      <div class="flex items-center gap-3">
        <p v-if="discount > 0" class="hidden sm:block text-base font-normal text-paragraph">
          {{ quantity }} x {{ formatPrice(discountedPrice, userCurrency, 2, false) }} =
        </p>
        <p class="text-base font-semibold text-[#21B8BA]">
          {{ formatPrice(total, userCurrency, 2, false) }}
        </p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      required: true
    },
    subtitle: {
      type: String,
      required: true
    },
    totalText: {
      type: String,
      required: true,
      default: 'Total'
    },
    unitPrice: {
      type: Number,
      required: true
    },
    discount: {
      type: Number,
      required: false,
      default: 0
    },
    quantity: {
      type: Number,
      required: true,
      default: 1
    }
  },
  computed: {
    subtotal () {
      return this.unitPrice * this.quantity
    },
    discountPercentage () {
      if (this.discount === 0) {
        return null
      }

      return this.discount + '%'
    },
    discountedPrice () {
      if (this.discount === 0) {
        return this.unitPrice
      }

      return this.unitPrice * (1 - this.discount / 100)
    },
    total () {
      return this.discountedPrice * this.quantity
    }
  }
}
</script>
