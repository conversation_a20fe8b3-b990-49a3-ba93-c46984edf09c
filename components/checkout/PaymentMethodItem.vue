<template>
  <div class="border-ring-100 rounded-xl pb-3.5 pl-5 pt-3.5 shadow-sm ring-1 bg-white ring-black/5 pr-5">
    <div class="flex items-center justify-between">
      <div class="flex flex-col items-start gap-0.5s">
        <span class="block text-sm font-bold tracking-tight text-primary-500 sm:text-lg">
          {{ name }}
        </span>
        <Paragraph v-if="description" size="xs" class="text-slate-500">
          {{ description }}
        </Paragraph>
      </div>
      <ButtonDark size="sm" @click="$emit('select')">
        Select
      </ButtonDark>
    </div>
  </div>
</template>

<script>
import Paragraph from '@/components/landingpage/common/Paragraph.vue'

export default {
  components: {
    Paragraph
  },
  props: {
    name: {
      type: String,
      required: true
    },
    description: {
      type: String,
      required: false
    }
  }

}
</script>

<style>

</style>
