<template>
  <div class="flex items-center">
    <div class="h-[24px] w-[24px] rounded-full block border-2 border-[#0EB567]" />
    <div class="bg-white h-[13px] flex items-center gap-1 ml-[-19px] mt-[1.5px]">
      <span class="text-[11px] font-bold text-[#0EB567] -translate-y-px">{{ percentage }}%</span>
      <span class="text-[11px] font-medium text-[#0EB567] -translate-y-px">OFF</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    percentage: {
      type: Number,
      required: true
    }
  }
}
</script>
