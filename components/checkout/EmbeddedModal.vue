<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto p-2 md:p-0 h-screen w-full" role="dialog" aria-modal="true">
    <div class="transition-opacity fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm" aria-hidden="true" @click="closeCheckout" />
    <div ref="checkoutContainer" class="h-[calc(100vh)] fixed top-0 right-0 w-[calc(30vw)] bg-white">
      <button class="bg-black rounded-full w-8 h-8 flex items-center justify-center" @click="closeCheckout">
        <IconMiniXMark class="size-4 text-white" />
      </button>
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    closeCheckout () {
      this.$emit('close')
    }
  }

}
</script>

<style>

</style>
