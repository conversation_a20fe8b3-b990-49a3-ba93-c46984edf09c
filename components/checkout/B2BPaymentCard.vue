<template>
  <Card>
    <h2 class="text-lg font-bold text-primary-500">
      {{ title }}
    </h2>
    <p class="text-slate-500 font-medium text-base mt-1">
      {{ subtitle }}
    </p>
    <!-- <template v-if="howItWorks.length > 0">
      <div class="hidden xs:block">
        <p class="text-slate-500 font-medium text-base mt-3">
          How it works
        </p>
        <ul class="space-y-1 mt-2 pl-2 list-disc list-inside text-paragraph">
          <li v-for="item in howItWorks" :key="item" class="text-sm font-medium">
            {{ item }}
          </li>
        </ul>
      </div>
    </template> -->
    <div class="mt-6">
      <slot />
    </div>
  </Card>
</template>

<script>
export default {
  props: {
    title: String,
    subtitle: String,
    howItWorks: Array
  }
}
</script>
