<template>
  <div class="flex flex-col items-center">
    <div class="number-container">
      <div :key="value" class="number-slot">
        {{ value.toString().padStart(2, '0') }}{{ unit }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: Number,
      required: true
    },
    unit: {
      type: String,
      required: true
    }
  }
}
</script>

<style >
.number-container {
  @apply bg-gray-800 rounded-md px-2 py-1 text-sm;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  position: relative;
  overflow: hidden;
}

.number-slot {
  animation: dropIn 0.3s ease-in-out;
}

@keyframes dropIn {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
