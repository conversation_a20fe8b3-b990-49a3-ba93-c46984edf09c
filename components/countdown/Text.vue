<template>
  <span>
    {{ timerOutput }}
  </span>
</template>

<script>
export default {
  props: {
    date: {
      type: String,
      default: 'November 27, 2023 10:00:00'
    },
    size: {
      type: String,
      default: 'md'
    }
  },
  data () {
    return {
      countDownToTime: new Date(this.date).getTime(),
      timerOutput: null,
      interval: null
    }
  },
  created () {
    this.interval = setInterval(() => { this.startTimer() }, 1000)
  },
  beforeDestroy () {
    clearInterval(this.interval)
  },
  methods: {
    startTimer () {
      const timeNow = new Date().getTime()
      const timeDifference = this.countDownToTime - timeNow
      const millisecondsInOneSecond = 1000
      const millisecondsInOneMinute = millisecondsInOneSecond * 60
      const millisecondsInOneHour = millisecondsInOneMinute * 60
      const millisecondsInOneDay = millisecondsInOneHour * 24
      const remainderDifferenceInDays = timeDifference / millisecondsInOneDay
      // const remainderDifferenceInHoursSmall = Math.floor(timeDifference / millisecondsInOneHour)
      const remainderDifferenceInHours = (timeDifference % millisecondsInOneDay) / millisecondsInOneHour
      const remainderDifferenceInMinutes = (timeDifference % millisecondsInOneHour) / millisecondsInOneMinute
      const remainderDifferenceInSeconds = (timeDifference % millisecondsInOneMinute) / millisecondsInOneSecond
      const remainingDays = Math.floor(remainderDifferenceInDays)
      let remainingHours = Math.floor(remainderDifferenceInHours)
      let remainingMinutes = Math.floor(remainderDifferenceInMinutes)
      let remainingSeconds = Math.floor(remainderDifferenceInSeconds)
      // Display all numbers in 00 format
      if (remainingHours < 10) {
        remainingHours = '0' + remainingHours
      }
      if (remainingMinutes < 10) {
        remainingMinutes = '0' + remainingMinutes
      }
      if (remainingSeconds < 10) {
        remainingSeconds = '0' + remainingSeconds
      }

      // if (this.size === 'sm') {
      //   this.timerOutput = (remainingHours <= 0 && remainingMinutes <= 0) ? '00:00:00' : `${remainingHours}h:${remainingMinutes}m:${remainingSeconds}s`
      // } else {
      //   this.timerOutput = (remainingDays <= 0 && remainingHours <= 0 && remainingMinutes <= 0) ? '00:00:00' : `${remainingDays}d:${remainingHours}h:${remainingMinutes}m:${remainingSeconds}s`
      // }
      this.timerOutput = (remainingDays <= 0 && remainingHours <= 0 && remainingMinutes <= 0) ? '00:00:00' : `${remainingHours} hours ${remainingMinutes} minutes left`
    }
  }
}
</script>

<style>

</style>
