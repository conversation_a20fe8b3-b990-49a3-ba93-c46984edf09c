<template>
  <div v-if="width > 0" class="w-[300px] relative">
    <p class="text-sm text-black/70 mb-1 text-left">
      ⏳ Limited deal: Get 40 bonus photos - {{ daysLeft }} days left
    </p>
    <div class="w-[300px] bg-black/5 h-[10px] rounded-full border border-black/25 flex items-center justify-start px-[2px]">
      <div :style="`width:${width}px;`" class=" bg-green-500 h-[4px] rounded-full" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    endDate: {
      type: String,
      required: true
    },
    startDate: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      startTime: null,
      endTime: null
    }
  },
  computed: {
    // Difference between startTime and endTime where endTime is 0
    // if endTime is null
    difference () {
      return this.endTime - this.startTime
    },
    timeLeft () {
      return this.endTime - Date.now()
    },
    // Amount of days lefts
    daysLeft () {
      return Math.floor(this.timeLeft / (1000 * 60 * 60 * 24))
    },
    width () {
      return (this.timeLeft / this.difference) * 300
    }

  },
  created () {
    this.startTime = new Date(this.startDate).getTime()
    // End time is 7 days from start time
    this.endTime = new Date(this.endDate).getTime()
  }

}
</script>

<style>

</style>
