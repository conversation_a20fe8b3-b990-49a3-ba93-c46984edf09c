<template>
  <div v-if="width > 0" class="group relative cursor-help">
    <p class="text-sm text-white/50 mb-1 text-center">
      <!-- You are #{{ queuePosition }} in the queue. -->
      {{ daysLeft }} days left to get your photos
    </p>
    <div class="w-[200px] bg-white/20 h-[10px] rounded-full border border-white/25 flex items-center justify-start px-[2px]">
      <div :style="`width:${width}px;`" class=" bg-yellow-500 h-[4px] rounded-full" />
    </div>
    <div class="absolute bg-white tooltip w-[250px] text-xs rounded p-1 top-10 z-20 group-hover:block hidden">
      For security, your training model gets deleted after 7 days. Photos are still accessible.
    </div>
  </div>
</template>

<script>
export default {
  props: {
    startDate: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      startTime: null,
      endTime: null
    }
  },
  computed: {
    // Difference between startTime and endTime where endTime is 0
    // if endTime is null
    difference () {
      return this.endTime - this.startTime
    },
    timeLeft () {
      return this.endTime - Date.now()
    },
    // Amount of days lefts
    daysLeft () {
      return Math.floor(this.timeLeft / (1000 * 60 * 60 * 24))
    },
    width () {
      return (this.timeLeft / this.difference) * 200
    }

  },
  created () {
    this.startTime = new Date(this.startDate).getTime()
    // End time is 7 days from start time
    this.endTime = this.startTime + (7 * 24 * 60 * 60 * 1000)
  }

}
</script>

<style>

</style>
