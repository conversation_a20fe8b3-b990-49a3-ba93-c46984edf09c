<template>
  <div v-if="!isLoading" class="h-full">
    <slot />
  </div>
  <div v-else class="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 p-4">
    <template v-for="(item, index) in amount">
      <SkeletonBlock :key="index" height="300px" />
    </template>
  </div>
</template>

<script>
export default {
  name: 'LoadingWrapper',
  props: {
    amount: {
      type: Number,
      default: 12
    },
    isLoading: {
      type: Boolean,
      required: true,
      default: false
    },
    title: {
      type: String,
      required: false,
      default: 'Loading...'
    }
  }
}
</script>

<style>

</style>
