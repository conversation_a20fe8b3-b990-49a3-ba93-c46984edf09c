<template>
  <div class="sticky inset-x-0 top-0 z-50">
    <header class="border-b border-gray-100 bg-white/90 py-4" style="backdrop-filter: blur(16px)">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between">
          <div class="flex shrink-0 items-center space-x-2">
            <nuxt-link to="/" title="" class="flex">
              <img width="181" height="28" class="h-6 w-auto sm:h-7" src="@/assets/img/logo.svg" alt="HeadshotPro logo">
            </nuxt-link>
          </div>
          <div class="hidden md:flex items-center justify-start space-x-10 px-8">
            <div class="flex items-center justify-start">
              <IconCheck class="w-3 h-3 text-green-500 mr-1.5" />
              <span class="text-xs text-gray-400">Founded in Holland. We respect your privacy.</span>
            </div>
            <div class="hidden xl:flex items-center justify-start">
              <IconCheck class="w-3 h-3 text-green-500 mr-1.5" />
              <span class="text-xs text-gray-400">{{ $store.state.stats.photos }}+ professional headshots already created</span>
            </div>
          </div>
          <div class="flex items-center justify-end">
            <nuxt-link to="/app/add">
              <ButtonPrimary size="sm">
                <span class="hidden md:block">Get your headshots</span>
                <span class="block md:hidden">Get started</span>
              </ButtonPrimary>
            </nuxt-link>
          </div>
        </div>
      </div>
    </header>
  </div>
</template>

<script>
export default {
  data () {
    return {
      expanded: false,
      showMobileMenu: false
    }
  }
}
</script>

<style></style>
