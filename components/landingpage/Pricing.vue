<template>
  <section id="pricing" class=" py-12 sm:py-16 lg:py-20 xl:py-24">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="mx-auto max-w-3xl text-center">
        <span class="text-base md:text-lg font-bold text-teal-500">
          Pricing
        </span>
        <h2 class="mt-6 text-2xl font-bold  tracking-tight text-primary-500 sm:text-4xl">
          {{ title }}
        </h2>
        <p class="mt-4 text-base font-normal text-gray-600">
          Get high-quality professional corporate business headshots for you or your team without breaking the bank. Our pricing is an affordable option for small and large teams alike.
        </p>
        <!-- <div class="w-auto mx-auto bg-green-500/10 rounded-md py-2 px-4 inline-block  mt-8">
          <div class="flex items-center justify-center">
            <IconSolidCheckBadge class="text-[#17A400] w-5 h-5 mr-1.5 hidden md:inline-flex" />
            <p class="font-medium text-center md:text-left text-green-900">
              <strong>Black Friday Offer:</strong> Get up to 120 extra photos until this Saturday
              <span class="text-black/70 text-sm"><CountdownText date="'November 27, 2023 10:00:00" /></span>
            </p>
          </div>
        </div> -->
        <!-- <ExperimentWrapper id="7-profile-worthy-refund">
          <template #variant>
            <div class="w-auto mx-auto bg-green-500/10 rounded-md py-2 px-4 inline-block  mt-8">
              <div class="flex items-center justify-center">
                <IconSolidCheckBadge class="text-[#17A400] w-5 h-5 mr-1.5 hidden md:inline-flex" />
                <p class="font-medium text-center md:text-left text-green-900">
                  Try risk free now with our <nuxt-link to="/refund" target="_blank" class="underline">
                    ‘Profile-Worthy’ Money Back Guarantee
                  </nuxt-link>
                </p>
              </div>
            </div>
          </template>
        </ExperimentWrapper> -->
        <div class="mt-8 flex justify-center">
          <fieldset class="grid grid-cols-2 gap-x-1 rounded-full p-1 text-center text-xs font-medium leading-5 ring-1 ring-inset ring-gray-200">
            <legend class="sr-only">
              For teams or individuals
            </legend>

            <label :class="type === 'team' ? 'bg-brand-500 text-white' : 'text-gray-500'" class="cursor-pointer rounded-full py-1 px-2.5">
              <input v-model="type" type="radio" name="type" value="team" class="sr-only">
              <span>Team</span>
            </label>

            <label :class="type === 'individual' ? 'bg-brand-500 text-white' : 'text-gray-500'" class="cursor-pointer rounded-full py-1 px-2.5">
              <input v-model="type" type="radio" name="type" value="individual" class="sr-only">
              <span>Individual</span>
            </label>
          </fieldset>
        </div>
      </div>

      <div v-show="type === 'team'" class="mx-auto mt-4 max-w-2xl rounded-lg border border-gray-200 bg-white shadow-sm sm:mt-6 lg:mt-8">
        <div class="p-8">
          <div class="flex flex-col justify-center gap-8 sm:flex-row sm:items-center sm:gap-12 lg:gap-20">
            <div class="text-center">
              <p class="text-7xl font-bold text-teal-500">
                $39
              </p>
              <p class="mt-1 text-base font-normal text-primary-500">
                per person
              </p>

              <p class="mt-2 text-xs font-normal text-gray-500">
                5+ people: 20% off <br>
                10+ people: 30% off <br>
                50+ people: 50% off
              </p>

              <div class="mt-4">
                <a href="/app/admin/team/new" title="" class="inline-flex h-12 w-full items-center justify-center gap-2 rounded-lg border border-transparent bg-primary-500 px-4 py-2 text-base font-medium leading-6 text-white shadow-sm transition-all duration-150 hover:bg-gray-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-500 sm:w-auto" role="button">
                  Create your team
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </a>
              </div>

              <div class="mt-4">
                <a href="/app/add" title="" class="text-sm font-normal text-gray-500 underline transition-all duration-150 hover:text-gray-900">Or create just for yourself</a>
              </div>
            </div>

            <div class="h-px w-full bg-gray-300 sm:h-40 sm:w-px" />

            <div>
              <h3 class="text-lg font-bold text-primary-500">
                All photoshoots include
              </h3>

              <ul class="mt-4 space-y-3 text-primary-500">
                <li class="flex items-center gap-2">
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  {{ photoPerStyle * 3 }} headshots/person
                </li>

                <li class="flex items-center gap-2">
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  {{ photoPerStyle }}+ different poses/shoot
                </li>

                <li class="flex items-center gap-2">
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  2 hours turnaround time
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <div v-show="type === 'individual'" class="g:mt-8 mx-auto mt-4 space-y-4 md:space-y-0 grid grid-cols-1 gap-0 sm:mt-6 sm:grid-cols-3 md:max-w-5xl">
        <template v-for="price in packages">
          <div v-if="price?.meta?.visible" :key="price.packageId" :class="price.tag === 'popular' ? 'border-brand-200' : 'border-gray-200'" class="mx-auto max-w-2xl rounded-lg border bg-white shadow-sm">
            <div class="p-8">
              <div class="flex flex-col gap-6 sm:gap-8 lg:gap-8">
                <div class="">
                  <p class="text-3xl md:text-4xl font-bold text-teal-500">
                    {{ getLocalizedPrice(price.id, true, 0) }}
                    <span class="text-base font-normal text-primary-500">/ shoot</span>
                  </p>
                </div>

                <div>
                  <h3 class="text-md md:text-lg font-bold text-primary-500">
                    {{ price.title }} shoot includes
                  </h3>

                  <ul class="mt-4 space-y-1 md:space-y-3 text-primary-500">
                    <li class="flex items-center gap-2">
                      <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                      <b>
                        {{ price.meta.styles * 40 }}
                      </b>
                      headshots
                    </li>

                    <li class="flex items-center gap-2">
                      <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                      <b>
                        {{ price.meta.styles }}
                      </b>
                      unique backgrounds
                    </li>
                    <li class="flex items-center gap-2">
                      <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                      <b>
                        {{ price.meta.styles }}
                      </b>
                      clothing styles
                    </li>

                    <li class="flex items-center gap-2">
                      <svg class="h-5 w-5 text-gray-900" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                      2 hours turnaround time
                    </li>
                  </ul>
                </div>
                <div class="mt-4">
                  <a href="/app/add" title="" :class="price?.meta?.tag === 'popular' ? 'border border-transparent bg-brand-500 text-white' : 'border border-brand-500 bg-white text-brand-500 hover:bg-brand-500 hover:text-white'" class="inline-flex h-12 w-full items-center justify-center gap-2 rounded-lg px-4 py-2 text-base font-medium leading-6 shadow-sm transition-all duration-150 hover:bg-gray-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-500 sm:w-auto" role="button">
                    Get your headshots
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>

      <div class="relative mx-auto mt-8 max-w-lg space-y-4 text-center">
        <div class="absolute -top-4 left-14 hidden sm:block">
          <img class="h-12 w-auto" src="@/assets/img/arrow-up.svg" alt="">
        </div>

        <div class="flex items-center justify-center gap-3">
          <div class="flex -space-x-3 overflow-hidden">
            <ImageDns class="inline-block h-10 w-10 rounded-full ring-[3px] ring-white" :src="require('@/assets/img/avatar-01.png')" alt="" />
            <ImageDns class="inline-block h-10 w-10 rounded-full ring-[3px] ring-white" :src="require('@/assets/img/avatar-02.png')" alt="" />
            <ImageDns class="inline-block h-10 w-10 rounded-full ring-[3px] ring-white" :src="require('@/assets/img/avatar-03.png')" alt="" />
            <ImageDns class="inline-block h-10 w-10 rounded-full ring-[3px] ring-white" :src="require('@/assets/img/avatar-04.png')" alt="" />
            <ImageDns class="inline-block h-10 w-10 rounded-full ring-[3px] ring-white" :src="require('@/assets/img/avatar-05.png')" alt="" />
          </div>

          <div class="flex">
            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
          </div>
        </div>

        <p class="text-sm text-gray-500">
          Tens of thousands of people have created their professional photos with our tools. Get matching team photos today.
        </p>
      </div>
      <MarketingLogoCloud class="w-full max-w-2xl mx-auto mt-8" />
    </div>
  </section>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: 'Professional headshots for 10x less than a physical photo shoot'
    }
  },
  data () {
    return {
      type: 'individual'
    }
  },
  computed: {
    packages () {
      return this.$store.state.packages
    }
  }
}
</script>

<style></style>
