<template>
  <LandingpageCommonSection id="for-admins" class="bg-muted-50">
    <LandingpageCommonTitleWrapper class="text-center items-center justify-center mx-auto max-w-2xl">
      <Pill>{{ $t('For managers') }}</Pill>
      <H2>
        {{ $t('Easily manage your entire team\'s headshots from one platform') }}
      </H2>
      <Paragraph size="lg">
        {{ $t('Professional headshots for your entire team no matter the size. Save hours on coordination and give your team a polished, on-brand presence without hassle.') }}
      </Paragraph>
      <a href="#features">
        <ButtonText>
          {{ $t('See all features') }}
        </ButtonText>
      </a>
    </LandingpageCommonTitleWrapper>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <StepCard
        v-for="(step, index) in steps"
        :key="step.title"
        :step="index+1"
        :class="(activeStep===index+1) ? 'active' : 'inactive'"
        :title="step.title"
        :description="step.description"
        class="cursor-pointer transition-all duration-200"
        :is-active="activeStep === index + 1"
        :progress="activeStep === index + 1 ? videoProgress : 0"
        @click="handleStepClick(index + 1)"
      />
    </div>
    <div class="w-full h-auto pt-4 px-4 md:px-0 md:h-[600px] lg:h-[700px] rounded-md flex flex-col justify-end items-center gradient-background mt-6 md:mt-12">
      <video
        v-if="steps[activeStep-1].video"
        ref="videoPlayer"
        :key="steps[activeStep-1].video"
        autoplay
        muted
        playsinline
        :src="steps[activeStep-1].video"
        class="rounded-t w-full max-w-[987px] h-auto object-contain mx-auto"
        @ended="goToNextStep"
        @timeupdate="handleTimeUpdate"
        @loadedmetadata="handleLoadedMetadata"
      />
      <ImageDns v-else :src="steps[activeStep-1].image" class="rounded-t w-full max-w-[987px] h-auto object-contain mx-auto" />
    </div>
  </LandingpageCommonSection>
</template>

<script>
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
import H2 from '@/components/landingpage/common/H2.vue'
import Pill from '@/components/landingpage/common/Pill.vue'
import StepCard from '@/components/landingpage/b2b/StepCard.vue'
export default {
  components: { Paragraph, H2, Pill, StepCard },
  data () {
    return {
      activeStep: 1,
      videoProgress: 0,
      videoDuration: 0,
      steps: [
        {
          title: this.$t('Define your look'),
          description: this.$t('Choose from diverse backgrounds and outfits to establish your team\'s uniform headshot style.'),
          image: require('@/assets/img/b2b/admin-usecase-1.jpg'),
          video: 'https://cdn2.headshotpror2.com/demo-styles-compressed.mp4'
        },
        {
          title: this.$t('Apply your branding'),
          description: this.$t('Every headshot includes a profile picture with your company\'s color or custom background image.'),
          video: 'https://cdn2.headshotpror2.com/demo-branding-compressed.mp4'
        },
        {
          title: this.$t('Manage your team'),
          description: this.$t('Invite team members via link or email, easily monitor their progress through the dashboard, and view everybody\'s final results.'),
          video: 'https://cdn2.headshotpror2.com/demo-management-compressed.mp4'
        }
      ]
    }
  },
  methods: {
    handleStepClick (stepNumber) {
      this.activeStep = stepNumber
      this.videoProgress = 0
      this.videoDuration = 0
      this.$nextTick(() => {
        if (this.$refs.videoPlayer) {
          this.$refs.videoPlayer.currentTime = 0
          this.$refs.videoPlayer.play()
        }
      })
    },
    goToNextStep () {
      console.log('goToNextStep')
      console.log(this.activeStep)
      this.activeStep = (this.activeStep % this.steps.length) + 1
      console.log(this.activeStep)
      this.videoProgress = 0
      this.videoDuration = 0
    },
    handleLoadedMetadata () {
      if (this.$refs.videoPlayer) {
        this.videoDuration = this.$refs.videoPlayer.duration
      }
    },
    handleTimeUpdate () {
      if (this.$refs.videoPlayer && this.videoDuration > 0) {
        const progress = (this.$refs.videoPlayer.currentTime / this.videoDuration) * 100
        this.videoProgress = progress
      } else {
        this.videoProgress = 0
      }
    }
  }
}
</script>

<i18n>
  {
    "en": {
      "For managers": "For managers",
      "Easily manage your entire team's headshots from one platform": "Easily manage your entire team's headshots from one platform",
      "Professional headshots for your entire team no matter the size. Save hours on coordination and give your team a polished, on-brand presence without hassle.": "Professional headshots for your entire team no matter the size. Save hours on coordination and give your team a polished, on-brand presence without hassle.",
      "See all features": "See all features",
      "Define your look": "Define your look",
      "Choose from diverse backgrounds and outfits to establish your team's uniform headshot style.": "Choose from diverse backgrounds and outfits to establish your team's uniform headshot style.",
      "Apply your branding": "Apply your branding",
      "Every headshot includes a profile picture with your company's color or custom background image.": "Every headshot includes a profile picture with your company's color or custom background image.",
      "Manage your team": "Manage your team",
      "Invite team members via link or email, easily monitor their progress through the dashboard, and view everybody's final results.": "Invite team members via link or email, easily monitor their progress through the dashboard, and view everybody's final results."
    },
    "es": {
      "For managers": "Para gerentes",
      "Easily manage your entire team's headshots from one platform": "Gestiona fácilmente las fotos de todo tu equipo desde una plataforma",
      "Professional headshots for your entire team no matter the size. Save hours on coordination and give your team a polished, on-brand presence without hassle.": "Fotos profesionales para todo tu equipo sin importar el tamaño. Ahorra horas en coordinación y dale a tu equipo una presencia pulida y de marca sin complicaciones.",
      "See all features": "Ver todas las características",
      "Define your look": "Define tu estilo",
      "Choose from diverse backgrounds and outfits to establish your team's uniform headshot style.": "Elige entre diversos fondos y atuendos para establecer el estilo uniforme de fotos de tu equipo.",
      "Apply your branding": "Aplica tu marca",
      "Every headshot includes a profile picture with your company's color or custom background image.": "Cada foto incluye una foto de perfil con el color de tu empresa o imagen de fondo personalizada.",
      "Manage your team": "Gestiona tu equipo",
      "Invite team members via link or email, easily monitor their progress through the dashboard, and view everybody's final results.": "Invita miembros del equipo por enlace o email, monitorea fácilmente su progreso a través del panel y ve los resultados finales de todos."
    },
    "de": {
      "For managers": "Für Manager",
      "Easily manage your entire team's headshots from one platform": "Verwalte einfach die Bewerbungsfotos deines gesamten Teams von einer Plattform aus",
      "Professional headshots for your entire team no matter the size. Save hours on coordination and give your team a polished, on-brand presence without hassle.": "Professionelle Bewerbungsfotos für dein gesamtes Team, egal welche Größe. Spare Stunden bei der Koordination und gib deinem Team eine professionelle, markengerechte Präsenz ohne Aufwand.",
      "See all features": "Alle Features ansehen",
      "Define your look": "Definiere deinen Look",
      "Choose from diverse backgrounds and outfits to establish your team's uniform headshot style.": "Wähle aus verschiedenen Hintergründen und Outfits, um den einheitlichen Bewerbungsfoto-Stil deines Teams zu etablieren.",
      "Apply your branding": "Wende dein Branding an",
      "Every headshot includes a profile picture with your company's color or custom background image.": "Jedes Bewerbungsfoto enthält ein Profilbild mit der Farbe deines Unternehmens oder einem individuellen Hintergrundbild.",
      "Manage your team": "Verwalte dein Team",
      "Invite team members via link or email, easily monitor their progress through the dashboard, and view everybody's final results.": "Lade Teammitglieder per Link oder E-Mail ein, überwache einfach ihren Fortschritt über das Dashboard und sieh dir die finalen Ergebnisse aller an."
    }
  }
</i18n>

<style scoped>
.active{
  opacity:1;
}

.inactive{
  opacity:0.5
}

.gradient-background {
  /* Equivalent to bg-gradient-to-br from-[#75DBD8] to-[#61B7EB] bg-opacity-30 */
  background-image: linear-gradient(to bottom right, rgba(117, 219, 216, 0.3), rgba(97, 183, 235, 0.3));
}
.inactive:hover{
  opacity:1;
}
</style>
