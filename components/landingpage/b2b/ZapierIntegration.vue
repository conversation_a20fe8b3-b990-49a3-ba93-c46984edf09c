<template>
  <LandingpageCommonSection id="zapier-integration" class="bg-muted-50">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center">
      <div class="flex flex-col gap-6 h-full items-start justify-center">
        <Pill>{{ $t('Automation') }}</Pill>
        <H2>
          {{ $t('Automate your headshot workflow with Zapier') }}
        </H2>
        <Paragraph>
          {{ $t('Connect HeadshotPro to your existing HR systems, CRM, or any of 6,000+ apps through Zapier. Automatically invite new hires, sync team data, and streamline your headshot process without manual work.') }}
        </Paragraph>
        <div class="flex flex-col gap-4">
          <div v-for="feature in zapierFeatures" :key="feature.title" class="flex items-start gap-3">
            <div class="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <IconCheck class="w-4 h-4 text-primary-600" />
            </div>
            <div>
              <H6 class="mb-1">
                {{ feature.title }}
              </H6>
              <Paragraph size="sm" class="text-slate-600">
                {{ feature.description }}
              </Paragraph>
            </div>
          </div>
        </div>
        <div class="flex flex-row gap-3 w-full sm:w-auto">
          <ButtonPrimary class="w-1/2 sm:w-auto" @click="$router.push('/app/admin')">
            <span class="flex-shrink-0">{{ $t('Get started') }}</span>
          </ButtonPrimary>
          <ButtonWhite class="w-1/2 sm:w-auto" @click="$emit('showSalesModal')">
            <span class="flex-shrink-0">{{ $t('Get in touch') }}</span>
          </ButtonWhite>
        </div>
      </div>
      <div class="flex flex-col gap-4">
        <Card class="p-6">
          <div class="flex flex-row justify-between items-start">
            <div class="flex items-center gap-4 mb-4">
              <div class="w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-600 rounded-lg flex items-center justify-center">
                <IconBolt class="w-6 h-6 text-white" />
              </div>
              <div>
                <H6>{{ $t('Zapier Integration') }}</H6>
                <Paragraph size="sm" class="text-slate-600">
                  {{ $t('Connect to 6,000+ apps') }}
                </Paragraph>
              </div>
            </div>
            <img src="@/assets/img/logo-zapier.svg" alt="Zapier Integration" class="w-auto h-[30px]">
          </div>
          <div class="space-y-3">
            <div v-for="workflow in sampleWorkflows" :key="workflow.title" class="flex items-center gap-3 p-3 bg-slate-50 rounded-lg">
              <component :is="workflow.icon" class="w-5 h-5 text-slate-600 flex-shrink-0" />
              <Paragraph size="sm" class="font-medium">
                {{ workflow.title }}
              </Paragraph>
            </div>
          </div>
        </Card>
        <div class="grid grid-cols-2 gap-4">
          <div class="text-center p-4 bg-white border border-black/5 rounded-lg">
            <H6 class="text-primary-600">
              6,000+
            </H6>
            <Paragraph size="sm" class="text-slate-600">
              {{ $t('App integrations') }}
            </Paragraph>
          </div>
          <div class="text-center p-4 bg-white border border-black/5 rounded-lg">
            <H6 class="text-primary-600">
              {{ $t('Zero-code') }}
            </H6>
            <Paragraph size="sm" class="text-slate-600">
              {{ $t('Setup required') }}
            </Paragraph>
          </div>
        </div>
        <div class="grid grid-cols-5 gap-2 items-center">
          <div v-for="logo in integrationLogos" :key="logo" class="p-4">
            <img :src="logo" alt="Integration Logo" class="w-auto h-[24px] grayscale opacity-80 object-contain">
          </div>
          <div class="flex items-center gap-2 flex-shrink-0 justify-center">
            <IconPlus class="w-5 h-5 text-slate-600" />
            <Paragraph size="sm" class="text-slate-600">
              {{ $t('many more') }}
            </Paragraph>
          </div>
        </div>
      </div>
    </div>
  </LandingpageCommonSection>
</template>

<script>
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
import H2 from '@/components/landingpage/common/H2.vue'
import H6 from '@/components/landingpage/common/H6.vue'
import Pill from '@/components/landingpage/common/Pill.vue'

export default {
  components: {
    Paragraph,
    H2,
    H6,
    Pill
  },
  data () {
    return {
      zapierFeatures: [
        {
          title: this.$t('Automatic team member invites'),
          description: this.$t('Trigger headshot invites when new employees are added to your HRIS or CRM')
        },
        {
          title: this.$t('HRIS system integration'),
          description: this.$t('Connect with BambooHR, Workday, ADP, and other HR systems for seamless employee data sync')
        },
        {
          title: this.$t('Seamless data sync'),
          description: this.$t('Keep employee information synchronized across all your business tools')
        }
      ],
      sampleWorkflows: [
        {
          title: this.$t('New hire in BambooHR → Invite to HeadshotPro'),
          icon: 'IconUserGroup'
        },
        {
          title: this.$t('Employee added in Workday → Auto-invite'),
          icon: 'IconStar'
        },
        {
          title: this.$t('New user joined Google Workspace → Invite for photo shoot'),
          icon: 'IconUserGroup'
        }
      ],
      integrationLogos: [
        require('@/assets/img/zapier/bamboohr.png'),
        require('@/assets/img/zapier/deel.svg'),
        require('@/assets/img/zapier/greenhouse.png'),
        require('@/assets/img/zapier/hibob.webp'),
        require('@/assets/img/zapier/personio.png'),
        require('@/assets/img/zapier/remote.png'),
        require('@/assets/img/zapier/workday.png'),
        require('@/assets/img/zapier/ripling.png'),
        require('@/assets/img/zapier/namely.png')
      ]
    }
  },
  methods: {
    openApiDocs () {
      window.open('/content/api', '_blank')
    }
  }
}
</script>

<i18n>
  {
    "en": {
      "Automation": "Automation",
      "Automate your headshot workflow with Zapier": "Automate your headshot workflow with Zapier",
      "Connect HeadshotPro to your existing HR systems, CRM, or any of 6,000+ apps through Zapier. Automatically invite new hires, sync team data, and streamline your headshot process without manual work.": "Connect HeadshotPro to your existing HR systems, CRM, or any of 6,000+ apps through Zapier. Automatically invite new hires, sync team data, and streamline your headshot process without manual work.",
      "Automatic team member invites": "Automatic team member invites",
      "Trigger headshot invites when new employees are added to your HRIS or CRM": "Trigger headshot invites when new employees are added to your HRIS or CRM",
      "HRIS system integration": "HRIS system integration",
      "Connect with BambooHR, Workday, ADP, and other HR systems for seamless employee data sync": "Connect with BambooHR, Workday, ADP, and other HR systems for seamless employee data sync",
      "Seamless data sync": "Seamless data sync",
      "Keep employee information synchronized across all your business tools": "Keep employee information synchronized across all your business tools",
      "Get started": "Get started",
      "View API docs": "View API docs",
      "Zapier Integration": "Zapier Integration",
      "Connect to 6,000+ apps": "Connect to 6,000+ apps",
      "New hire in BambooHR → Invite to HeadshotPro": "New hire in BambooHR → Invite to HeadshotPro",
      "Employee added in Workday → Auto-invite": "Employee added in Workday → Auto-invite",
      "ADP roster change → Update team assignments": "ADP roster change → Update team assignments",
      "App integrations": "App integrations",
      "Zero-code": "Zero-code",
      "Setup required": "Setup required"
    },
    "es": {
      "Automation": "Automatización",
      "Automate your headshot workflow with Zapier": "Automatiza tu flujo de trabajo de fotos con Zapier",
      "Connect HeadshotPro to your existing HR systems, CRM, or any of 6,000+ apps through Zapier. Automatically invite new hires, sync team data, and streamline your headshot process without manual work.": "Conecta HeadshotPro a tus sistemas de RRHH existentes, CRM, o cualquiera de más de 6,000 aplicaciones a través de Zapier. Invita automáticamente a nuevos empleados, sincroniza datos del equipo y optimiza tu proceso de fotos sin trabajo manual.",
      "Automatic team member invites": "Invitaciones automáticas de miembros del equipo",
      "Trigger headshot invites when new employees are added to your HRIS or CRM": "Activa invitaciones de fotos cuando se agregan nuevos empleados a tu HRIS o CRM",
      "HRIS system integration": "Integración con sistemas HRIS",
      "Connect with BambooHR, Workday, ADP, and other HR systems for seamless employee data sync": "Conecta con BambooHR, Workday, ADP y otros sistemas de RRHH para sincronización perfecta de datos de empleados",
      "Seamless data sync": "Sincronización de datos perfecta",
      "Keep employee information synchronized across all your business tools": "Mantén la información de empleados sincronizada en todas tus herramientas de negocio",
      "Get started": "Comenzar",
      "View API docs": "Ver documentación API",
      "Zapier Integration": "Integración con Zapier",
      "Connect to 6,000+ apps": "Conecta con más de 6,000 apps",
      "New hire in BambooHR → Invite to HeadshotPro": "Nuevo empleado en BambooHR → Invitar a HeadshotPro",
      "Employee added in Workday → Auto-invite": "Empleado agregado en Workday → Auto-invitar",
      "ADP roster change → Update team assignments": "Cambio de lista ADP → Actualizar asignaciones de equipo",
      "App integrations": "Integraciones de apps",
      "Zero-code": "Sin código",
      "Setup required": "Configuración requerida"
    },
    "de": {
      "Automation": "Automatisierung",
      "Automate your headshot workflow with Zapier": "Automatisiere deinen Bewerbungsfoto-Workflow mit Zapier",
      "Connect HeadshotPro to your existing HR systems, CRM, or any of 6,000+ apps through Zapier. Automatically invite new hires, sync team data, and streamline your headshot process without manual work.": "Verbinde HeadshotPro mit deinen bestehenden HR-Systemen, CRM oder einer von über 6.000 Apps über Zapier. Lade automatisch neue Mitarbeiter ein, synchronisiere Teamdaten und optimiere deinen Bewerbungsfoto-Prozess ohne manuelle Arbeit.",
      "Automatic team member invites": "Automatische Teammitglieder-Einladungen",
      "Trigger headshot invites when new employees are added to your HRIS or CRM": "Löse Bewerbungsfoto-Einladungen aus, wenn neue Mitarbeiter zu deinem HRIS oder CRM hinzugefügt werden",
      "HRIS system integration": "HRIS-System-Integration",
      "Connect with BambooHR, Workday, ADP, and other HR systems for seamless employee data sync": "Verbinde mit BambooHR, Workday, ADP und anderen HR-Systemen für nahtlose Mitarbeiterdaten-Synchronisation",
      "Seamless data sync": "Nahtlose Datensynchronisation",
      "Keep employee information synchronized across all your business tools": "Halte Mitarbeiterinformationen über alle deine Geschäftstools synchronisiert",
      "Get started": "Loslegen",
      "View API docs": "API-Dokumentation ansehen",
      "Zapier Integration": "Zapier-Integration",
      "Connect to 6,000+ apps": "Verbinde mit über 6.000 Apps",
      "New hire in BambooHR → Invite to HeadshotPro": "Neuer Mitarbeiter in BambooHR → Einladung zu HeadshotPro",
      "Employee added in Workday → Auto-invite": "Mitarbeiter in Workday hinzugefügt → Auto-Einladung",
      "ADP roster change → Update team assignments": "ADP-Personaländerung → Team-Zuweisungen aktualisieren",
      "App integrations": "App-Integrationen",
      "Zero-code": "Ohne Code",
      "Setup required": "Setup erforderlich"
    }
  }
</i18n>

<style>

</style>
