<template>
  <Card class="max-w-3xl mx-auto">
    <div class="p-[16px]">
      <div class="flex flex-col md:flex-row md:gap-[48px] md:items-start">
        <div class="flex-1 min-w-0 md:w-[55%] w-full gap-2">
          <Pill v-if="currentDiscountTier?.discount > 0">
            {{ currentDiscountTier?.discount || 0 }}% {{ $t('discount') }}
          </Pill>

          <div class="flex items-end gap-0.5 mt-4">
            <H2>
              <span v-if="getFormattedPriceParts(formattedPrice).smallTextPrefix" class="text-2xl font-normal">
                {{ getFormattedPriceParts(formattedPrice).smallTextPrefix }}
              </span>
              <span v-if="getFormattedPriceParts(formattedPrice).largePart">
                {{ getFormattedPriceParts(formattedPrice).largePart }}
              </span>
            </H2>
            <Paragraph size="lg">
              <span>/{{ $t('member') }}</span>
            </Paragraph>
          </div>
          <div class="mt-2 space-y-2">
            <Paragraph size="xs">
              <!-- Save <strong class="text-green-600">${{ (total * 232.5 - total * price).toFixed(2) }}</strong> compared to a traditional shoot
              <span v-if="total > 4">and <strong class="text-green-600">${{ (totalPrice * total - total * price).toFixed(2) }}</strong> with bulk discount.</span> -->
              {{ $t("That's") }} <strong class="text-green-600">{{ totalFormattedPrice }}</strong> {{ $t('total for') }} <strong class="text-slate-900">{{ total }}</strong> {{ $t('members') }}.
            </Paragraph>
          </div>
          <div class="flex flex-col gap-4 bg-slate-100 p-4 rounded-lg my-4">
            <div class=" space-y-2">
              <div class="flex flex-col space-y-3">
                <div class="flex space-x-2 w-full items-center justify-start">
                  <Paragraph size="sm">
                    {{ $t('Total members:') }}
                  </Paragraph>
                  <Input v-model="total" type="number" :min="1" class="w-[80px]" @input="checkForQuantity" />
                </div>
                <div class="flex flex-col space-y-2 w-full">
                  <input v-model="total" type="range" class="pricing-slider" min="1" :max="teamMaxSeats">
                </div>
              </div>
            </div>

            <div class=" w-full">
              <nuxt-link to="/app" role="button" class="w-full">
                <ButtonPrimary class="w-full">
                  {{ $t('Get started now') }}
                </ButtonPrimary>
              </nuxt-link>
            </div>
          </div>
          <Paragraph size="xs">
            {{ $t('Save') }} <strong class="text-primary-500">{{ formatPrice((totalPrice * total) - (total * price), userCurrency) }}</strong> {{ $t('off our regular price, and') }} <strong class="strong">{{ formatPrice(total * 95 - (total * totalPrice) + 500, userCurrency) }}</strong> {{ $t('compared to a traditional photoshoot.') }}
          </Paragraph>
          <!-- <div class="gap-1 flex flex-col mt-4"> -->
          <!-- <Paragraph size="sm">
              <span>Traditional shoot:</span>
              <span class=" tabular-nums text-sm text-red-800"><strike>${{ formatCurrency(total * 232.5) }}</strike></span>
            </Paragraph>
            <Paragraph size="sm">
              <span>Total price:</span>
              <span class="tabular-nums font-bold">${{ formatCurrency(total * price) }}</span>
              <span v-if="total > 9" class="tabular-nums font-normal text-gray-500"><strike>${{ formatCurrency(totalPrice * total) }}</strike></span>
            </Paragraph> -->
          <!-- </div> -->
          <div class="mt-6">
            <Paragraph size="xs">
              {{ $t('Looking for a custom plan for your use-case?') }} <button class="underline" @click="$emit('showSalesModal')">
                {{ $t("Talk to our team and we'll help.") }}
              </button>
            </Paragraph>
          </div>
        </div>
        <div class="w-full h-[1px] md:w-[1px] md:h-[300px] bg-gray-200 my-8 md:my-0" />
        <div class="flex flex-col gap-8 md:w-[43%] w-full">
          <!-- <div class="bg-[#F8FCFF] border border-[#ECEAFF] rounded-lg p-4 space-y-[8px]">
            <Paragraph size="sm">
              <span>Traditional shoot:</span>
              <span class=" tabular-nums text-sm text-red-800"><strike>${{ formatCurrency(total * 232.5) }}</strike></span>
            </Paragraph>
            <H5 class="!text-[#474368]">
              <span>Total price:</span>
              <span v-if="total > 9" class="tabular-nums font-normal text-gray-500"><strike>${{ formatCurrency(totalPrice * total) }}</strike></span>
              <span class="tabular-nums font-bold">${{ formatCurrency(total * price) }}</span>
            </H5>
            <Paragraph size="xs" class="!text-[11px] italic">
              The average cost of professional headshots in the United States is <a
                href="/blog/how-much-does-a-headshot-cost"
                class="underline"
                target="_blank"
              >$232.50 per session</a>* Our packages
              start from $29.
            </Paragraph>
          </div> -->
          <div class="flex flex-col gap-2">
            <H5>
              {{ $t('Your discount never expires') }}
            </H5>
            <Paragraph size="sm">
              {{ $t('Get a forever discount based on the amount of seats you purchase on your organisation. This includes repurchases.') }}
            </Paragraph>
            <!-- <div class="flex flex-row gap-2 items-center">
              <Paragraph size="sm">
                Savings up to
              </Paragraph>
              <CheckoutPercentageOffCircle :percentage="50" />
            </div> -->
            <ul class="space-y-2 mt-4">
              <template v-for="option in teamDiscountOptions">
                <li v-if="option.discount > 0" :key="option.value" class="text-paragraph flex items-center justify-start gap-3">
                  <span class="w-[90px] font-medium text-[15px]">{{ option.label }} {{ $t('users') }}</span>
                  <IconArrowRight class="w-4 h-4" />
                  <CheckoutPercentageOffCircle :percentage="option.discount" />
                </li>
              </template>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </Card>
</template>

<script>
import H5 from '@/components/landingpage/common/H5.vue'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
import Pill from '@/components/landingpage/common/Pill.vue'
import H2 from '@/components/landingpage/common/H2.vue'

export default {
  components: {
    H5,
    Paragraph,
    Pill,
    H2
  },
  data () {
    return {
      total: 10
    }
  },
  computed: {
    totalPrice () {
      try {
        const packages = this.packages
        const price = packages.medium?.currency[this.userCurrency.toLowerCase() || 'usd']
        return price / 100
      } catch (error) {
        return 39
      }
    },
    price () {
      // teamDiscountOptions
      const { teamDiscountOptions, total } = this
      const activeBulkDiscount = teamDiscountOptions.find(item => total >= item.from && total <= item.to)
      if (activeBulkDiscount) {
        return this.totalPrice * (1 - activeBulkDiscount.discount / 100)
      }
      return this.totalPrice
    },
    currentDiscountTier () {
      const { teamDiscountOptions, total } = this
      const activeBulkDiscount = teamDiscountOptions.find(item => total >= item.from && total <= item.to)
      return activeBulkDiscount
    },
    formattedPrice () {
      // use 0 or 2 decimals
      return this.getLocalizedPrice('medium', true, (this.price % 1 === 0 ? 0 : 2), false, this.currentDiscountTier?.discount, 1, true)
    },
    totalFormattedPrice () {
      return this.getLocalizedPrice('medium', true, 2, false, this.currentDiscountTier?.discount, this.total, true)
    }
  },
  methods: {
    formatCurrency (value) {
      if (typeof value !== 'number') {
        return value
      }
      return value.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      })
    },
    checkForQuantity ($event) {
      const quantity = parseInt($event)
      if (quantity < 1 || $event === '') {
        this.total = 1
      }
    }
  }

}
</script>

<style scoped>
.pricing-slider {
   @apply w-full bg-transparent cursor-pointer appearance-none disabled:opacity-50 disabled:pointer-events-none focus:outline-none [&::-webkit-slider-thumb]:w-6 [&::-webkit-slider-thumb]:h-6
        [&::-webkit-slider-thumb]:-mt-2
        [&::-webkit-slider-thumb]:appearance-none
        [&::-webkit-slider-thumb]:bg-[#1B145D]
        [&::-webkit-slider-thumb]:shadow-[0_0_0_3px_rgba(255,255,255,1)]
        [&::-webkit-slider-thumb]:rounded-full
        [&::-webkit-slider-thumb]:transition-all
        [&::-webkit-slider-thumb]:duration-150
        [&::-webkit-slider-thumb]:ease-in-out

        [&::-moz-range-thumb]:w-6
        [&::-moz-range-thumb]:h-6
        [&::-moz-range-thumb]:appearance-none
        [&::-moz-range-thumb]:bg-[#1B145D]
        [&::-moz-range-thumb]:border-2
        [&::-moz-range-thumb]:border-white
        [&::-moz-range-thumb]:rounded-full
        [&::-moz-range-thumb]:transition-all
        [&::-moz-range-thumb]:duration-150
        [&::-moz-range-thumb]:ease-in-out

        [&::-webkit-slider-runnable-track]:w-full
        [&::-webkit-slider-runnable-track]:h-2.5
        [&::-webkit-slider-runnable-track]:bg-[#EAECF0]
        [&::-webkit-slider-runnable-track]:rounded-full

        [&::-moz-range-track]:w-full
        [&::-moz-range-track]:h-2.5
        [&::-moz-range-track]:bg-[#EAECF0]
        [&::-moz-range-track]:rounded-full
}
</style>

<i18n>
  {
    "de": {
      "Get started now": "Jetzt starten",
      "That's": "Das sind",
      "total for": "gesamt für",
      "members": "Mitglieder",
      "Save": "Spare",
      "off our regular price, and": "im Vergleich zu unserem regulären Preis und",
      "compared to a traditional photoshoot.": "im Vergleich zu einem traditionellen Fotoshooting.",
      "Looking for a custom plan for your use-case?": "Benötigst du einen individuellen Plan?",
      "Talk to our team and we'll help.": "Sprich mit unserem Team – wir helfen dir gern.",
      "discount": "Rabatt",
      "member": "Mitglied",
      "Total members:": "Gesamtanzahl Mitglieder:",
      "users": "Nutzer",
      "Your discount never expires": "Dein Rabatt läuft nie ab",
      "Get a forever discount based on the amount of seats you purchase on your organisation. This includes repurchases.": "Sichere dir dauerhaft Rabatte abhängig von der Anzahl der Plätze, die du für dein Unternehmen kaufst – gilt auch für Nachkäufe."
    }
  }
</i18n>
