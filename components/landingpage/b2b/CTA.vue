<template>
  <LandingpageCommonSection class="bg-white">
    <LandingpageCommonTitleWrapper class="text-center items-center justify-center mx-auto max-w-2xl">
      <H2>
        {{ $t('Get headshots for your team in minutes instead of weeks') }}
      </H2>
      <Paragraph size="lg">
        {{ $t('Ditch inconsistent selfies & costly photoshoots. Get professional, matching headshots online for your whole team, easily managed and affordable.') }}
      </Paragraph>
      <div class="flex flex-col sm:flex-row gap-4 pt-4">
        <nuxt-link to="/app/add">
          <ButtonOrange size="base">
            <span class="flex items-center justify-center">
              {{ $t('Create your organization') }}
              <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" /></svg>
            </span>
          </ButtonOrange>
        </nuxt-link>
        <ButtonWhite size="base" @click="$emit('showSalesModal')">
          <span class="flex items-center justify-center space-x-2">
            <img class="size-6 rounded-full" src="@/assets/img/headshot-danny.png">
            <span>{{ $t('Contact sales') }}</span>
          </span>
        </ButtonWhite>
      </div>
    </LandingpageCommonTitleWrapper>
  </LandingpageCommonSection>
</template>

<script>
import H2 from '@/components/landingpage/common/H2.vue'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'

export default {
  components: {
    H2,
    Paragraph
  }
}
</script>

<i18n>
  {
    "en": {
      "Get headshots for your team in minutes instead of weeks": "Get headshots for your team in minutes instead of weeks",
      "Ditch inconsistent selfies & costly photoshoots. Get professional, matching headshots online for your whole team, easily managed and affordable.": "Ditch inconsistent selfies & costly photoshoots. Get professional, matching headshots online for your whole team, easily managed and affordable.",
      "Create your organization": "Create your organization",
      "Contact sales": "Contact sales"
    },
    "es": {
      "Get headshots for your team in minutes instead of weeks": "Obtén fotos para tu equipo en minutos en lugar de semanas",
      "Ditch inconsistent selfies & costly photoshoots. Get professional, matching headshots online for your whole team, easily managed and affordable.": "Abandona los selfies inconsistentes y las sesiones fotográficas costosas. Obtén fotos profesionales y uniformes online para todo tu equipo, fácilmente gestionadas y asequibles.",
      "Create your organization": "Crea tu organización",
      "Contact sales": "Contactar ventas"
    },
    "de": {
      "Get headshots for your team in minutes instead of weeks": "Bewerbungsfotos für dein Team in Minuten statt Wochen",
      "Ditch inconsistent selfies & costly photoshoots. Get professional, matching headshots online for your whole team, easily managed and affordable.": "Vergiss inkonsistente Selfies und teure Fotoshootings. Erhalte professionelle, einheitliche Bewerbungsfotos online für dein gesamtes Team, einfach verwaltet und erschwinglich.",
      "Create your organization": "Erstelle deine Organisation",
      "Contact sales": "Vertrieb kontaktieren"
    }
  }
</i18n>

<style>

</style>
