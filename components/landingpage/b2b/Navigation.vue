<template>
  <div class="sticky z-50 top-0 inset-x-0">
    <div class="py-2 hidden lg:block border-b border-gray-200 bg-muted-50">
      <div class="max-w-7xl mx-auto px-4">
        <div class="flex items-center justify-between space-x-16">
          <div>
            <ul class="flex items-center gap-4">
              <li class="text-xs font-normal text-black/50 gap-2 flex items-center justify-start">
                <IconMiniShield class="w-4 h-4 text-black/30" />
                <span>SOC2 Type 2 Certified</span>
              </li>
              <li class="text-xs font-normal text-black/50 gap-2 flex items-center justify-start">
                <IconMiniUsers class="w-4 h-4 text-black/30" />
                <span>Used by {{ $store.state.stats.users }}+ happy customers</span>
              </li>
            </ul>
          </div>
          <div>
            <ul class="flex items-center gap-4">
              <li v-for="item in secondaryNavigation" :key="item.title" class="text-xs font-medium text-gray-600 hover:text-primary-500 hover:underline">
                <nuxt-link :to="item.url">
                  {{ item.title }}
                </nuxt-link>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <header class="relative w-full bg-white border-b border-gray-200 px-4 py-3.5">
      <div class="max-w-7xl mx-auto px-4">
        <div class="flex items-center justify-between space-x-16">
          <nuxt-link :to="localePath('/')" class="flex flex-shrink-0">
            <img width="181" height="28" class="h-6 w-auto" src="@/assets/img/logo.svg" alt="HeadshotPro logo">
          </nuxt-link>
          <!-- Desktop Menu -->
          <div class="hidden lg:flex items-center gap-4">
            <ul class="flex items-center gap-6">
              <li v-for="item in mainNavigation" :key="item.title" class="text-sm font-medium text-gray-600 hover:text-primary-500 hover:underline">
                <nuxt-link :to="item.url">
                  {{ item.title }}
                </nuxt-link>
              </li>
            </ul>
          </div>
          <div class="hidden lg:flex items-center gap-2 justify-end">
            <client-only>
              <HeaderLanguageSelector />
            </client-only>
            <nuxt-link :to="localePath('/app/add')">
              <ButtonPrimary size="sm" class="flex-shrink-0">
                <span class="flex items-center justify-center flex-shrink-0">
                  Start now
                </span>
              </ButtonPrimary>
            </nuxt-link>
            <ButtonWhite size="sm" class="flex-shrink-0" @click="$emit('showSalesModal')">
              <span>Contact sales</span>
            </ButtonWhite>
          </div>
          <!-- Mobile Menu Button -->
          <div class="lg:hidden flex items-center">
            <button type="button" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500" aria-controls="mobile-menu" aria-expanded="false" @click="toggleMobileMenu">
              <span class="sr-only">Open main menu</span>
              <!-- Heroicon name: outline/menu -->
              <svg
                v-if="!isMobileMenuOpen"
                class="block h-6 w-6"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
              </svg>
              <!-- Heroicon name: outline/x -->
              <svg
                v-else
                class="block h-6 w-6"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>
      <!-- Mobile Menu -->
      <div v-if="isMobileMenuOpen" id="mobile-menu" class="lg:hidden absolute top-full left-0 w-full bg-white shadow-lg border-t border-gray-200">
        <div class="pt-2 pb-3 space-y-1 px-4">
          <nuxt-link v-for="item in mainNavigation" :key="item.title" :to="item.url" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50" @click.native="closeMobileMenu">
            {{ item.title }}
          </nuxt-link>
        </div>
        <div class="pt-4 pb-3 border-t border-gray-200 px-4">
          <div class="flex items-center justify-start gap-2 mb-3">
            <nuxt-link :to="localePath('/app/add')" @click.native="closeMobileMenu">
              <ButtonPrimary size="sm" class="flex-shrink-0">
                <span class="flex items-center justify-center flex-shrink-0">
                  Start now
                </span>
              </ButtonPrimary>
            </nuxt-link>
            <ButtonWhite size="sm" class="flex-shrink-0" @click="handleContactSalesClick">
              <span>Contact sales</span>
            </ButtonWhite>
          </div>
          <div class="space-y-1">
            <nuxt-link v-for="item in secondaryNavigation" :key="item.title" :to="item.url" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50" @click.native="closeMobileMenu">
              {{ item.title }}
            </nuxt-link>
          </div>
        </div>
      </div>
    </header>
  </div>
</template>

<script>
export default {
  data () {
    return {
      isMobileMenuOpen: false // State for mobile menu visibility
    }
  },
  computed: {
    mainNavigation () {
      return [
        { title: 'What you get', url: '#team-headshots' },
        { title: 'How it works', url: '#for-admins' },
        { title: 'Use cases', url: '#use-cases' },
        { title: 'Security', url: '#security' },
        { title: 'Pricing', url: '#pricing' },
        { title: 'Features', url: '#features' },
        { title: 'FAQ', url: '#faq' }
      ]
    },
    secondaryNavigation () {
      return [
        { title: 'For individuals', url: this.localePath('/') },
        { title: 'Reviews', url: this.localePath('/reviews') },
        { title: 'Pricing', url: this.localePath('/pricing') },
        { title: 'Blog', url: this.localePath('/blog') },
        { title: 'Catalog', url: this.localePath('/backdrop-and-outfit') },
        { title: 'Login', url: this.localePath('/auth/login') }
      ]
    }
  },
  methods: {
    toggleMobileMenu () {
      this.isMobileMenuOpen = !this.isMobileMenuOpen
    },
    closeMobileMenu () {
      this.isMobileMenuOpen = false
    },
    handleContactSalesClick () {
      this.closeMobileMenu()
      this.$emit('showSalesModal')
    }
  }
}
</script>

<style>
/* Optional: Add transitions for smoother open/close */
#mobile-menu {
  transition: max-height 0.3s ease-out;
  overflow: hidden;
}
</style>
