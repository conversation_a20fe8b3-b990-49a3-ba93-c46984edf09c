<template>
  <div class="px-4 pt-12 sm:px-6 sm:pt-16 lg:pt-20 bg-[#F8FCFF]">
    <div
      id="review-container"
      class="flex flex-row flex-nowrap overflow-hidden overflow-x-auto space-x-4 cursor-move"
      @mousedown="startDrag"
      @mouseup="stopDrag"
      @mouseleave="stopDrag"
      @mousemove="onDrag"
      @touchstart="startDrag"
      @touchend="stopDrag"
      @touchmove="onDrag"
    >
      <template v-for="index in reviews.length">
        <template v-if="index % 2">
          <div :key="index" class="space-y-4 min-w-[250px] flex-shrink-0 w-[250px]">
            <template v-if="reviews[index-1]?.type === 'trustpilot'">
              <LandingpageV2ReviewTrustpilot :item="reviews[index-1]" />
            </template>
            <template v-if="reviews[index-1]?.type === 'example'">
              <LandingpageV2ReviewExample :item="reviews[index-1]" />
            </template>
            <template v-if="reviews[index]?.type === 'trustpilot'">
              <LandingpageV2ReviewTrustpilot :item="reviews[index]" />
            </template>
            <template v-if="reviews[index]?.type === 'example'">
              <LandingpageV2ReviewExample :item="reviews[index]" />
            </template>
          </div>
        </template>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      isDragging: false,
      scrollLeft: 0,
      startX: 0,
      startY: 0
    }
  },
  computed: {
    trustpilotReviews () {
      return [
        {
          type: 'trustpilot',
          rating: 5,
          name: 'Terry Feinberg',
          avatar: 'https://user-images.trustpilot.com/63e583e1cc5b720012618c69/73x73.png',
          excerpt: 'I needed a quick headshot for a project…',
          quote: 'I needed a quick headshot for a project and didn\'t have time to go to a pro or to take one at home. I took about 10 minutes placing the order and taking some selfies (wearing a t-shirt). This really works - I selected 19 of the AI generated headshots - any one of which I can use.',
          date: 'June 05, 2024'
        },
        {
          type: 'trustpilot',
          rating: 5,
          name: '-private-',
          excerpt: 'Absolutely brilliant AI photos',
          quote: 'Absolutely brilliant AI photos! You should allow for a longer waiting time to generate the photos (~ 2-3h), but in return you get extremely high quality! And for a fraction of the price of a photographer. I now use one of the photos for my LinkedIn profile and two for dating websites. A+*'
        },
        {
          type: 'trustpilot',
          rating: 5,
          name: 'Michael Pimentel',
          avatar: 'https://user-images.trustpilot.com/66747e40f6dae6528741d429/73x73.png',
          excerpt: 'Far Better Than Expected',
          quote: 'The results were actually far better than I could have imagined, especially considering my less-than-inspiring seed photos. Of course, as an A.I. tool, not every photo is perfect, but this is why they give you so many photo results. Roughly 70% of the photos look exactly like me, and about 20 photos were simply perfect and exactly what I was looking for. It was difficult to choose my favorite photo, which is a great dilemma to have.<br><br>Not only were the images of superb quality, but so was the interface. I did not experience any bugs whatsoever in the entire process, which I found pleasantly surprising.',
          date: 'June 19, 2024'
        },
        {
          type: 'trustpilot',
          rating: 5,
          name: 'Nic Reagan',
          avatar: 'https://user-images.trustpilot.com/666f89f0bcdfbf666b46da64/73x73.png',
          excerpt: 'Super easy and amazing results',
          quote: 'Super easy and amazing results! I can\'t speak highly enough of just how great these images turned out. I\'ll never have to use a live photographer again for my professional headshots! Thanks so much, HeadshotPro!',
          date: 'June 16, 2024'
        },
        {
          type: 'trustpilot',
          rating: 5,
          name: 'James Bane',
          avatar: 'https://user-images.trustpilot.com/6668d87bc485c0c33c1ea797/73x73.png',
          excerpt: 'Best, most realistic portrait generations so far.',
          quote: 'I tried two other sites and neither came close to this one. The other sites made images that looked like completely different people and there were no refunds available. On HeadshotPro, I went with the 100-image package and immediately identified 32 for closer review. Of those I have several photos I\'ll be using for my work and professional social profiles.',
          date: 'June 11, 2024'
        },
        {
          type: 'trustpilot',
          rating: 5,
          name: 'Omar A',
          excerpt: 'I am beyond happy with HeadshotPro',
          quote: 'I am beyond happy with HeadshotPro, my photos came out wonderfully. The entire process was very easy, you need to upload at least 14 pictures - and the website goes over the clear expectations of what the requirements are for these photos. While uploading it will show what is accepted and what is rejected. After that you just hit done and your headshots will be generated. After generation you can chose favorites and make some edits, like removing blemishes, changes the poses or even the outfits and backgrounds. Everything is very customizable and super straight forward. You\'ll spend much less on this than a professional photographer for a headshot. I highly recommend this to anyone who needs a good headshot. Results are very realistic!',
          date: 'June 10, 2024'
        },
        {
          type: 'trustpilot',
          rating: 4,
          name: 'Kimberly Anne Ambito',
          avatar: 'https://user-images.trustpilot.com/66676c345e648b101214584c/73x73.png',
          excerpt: 'I recently tried the HeadshotPro AI app…',
          quote: 'I recently tried the HeadshotPro AI app to generate professional headshots, and I was quite impressed with the results. The app\'s interface is user-friendly, making it easy to upload photos and navigate through the different features. The AI did an excellent job at analyzing my photos and generating headshots that looked polished and professional.<br><br>The quality of the headshots was surprisingly good, with natural-looking lighting and retouching. The app also offers a variety of styles and backgrounds, allowing for a level of customization that I appreciated. The processing time was quick, and I received my final images within minutes.<br><br>The only reason I\'m not giving a full five stars is that the pricing for some premium features seemed a bit high. Additionally, while the AI is impressive, it occasionally produced minor artifacts that required some manual editing.<br><br>Overall, HeadshotPro is a fantastic tool for anyone needing high-quality headshots quickly and conveniently. I would definitely recommend it for its ease of use and the quality of the output.',
          date: 'June 10, 2024'
        },
        {
          type: 'trustpilot',
          rating: 5,
          name: 'Megan Isbell',
          avatar: 'https://user-images.trustpilot.com/6667325e727d372a1de1fb3b/73x73.png',
          excerpt: 'I have used 3 different AI generating…',
          quote: 'I have used 3 different AI generating Headshots and they all look like AI-generated headshots, meaning they are very airbrushed and look like avatars. <br><br>Headshot Pro gives much more realistic options, and to be honest I didn\'t have to leave my house or put on makeup.',
          date: 'June 10, 2024'
        },
        {
          type: 'trustpilot',
          rating: 4,
          name: 'Toyin Frank-Lawale',
          avatar: 'https://user-images.trustpilot.com/666375da730fbf27826854ff/73x73.png',
          excerpt: 'The company gave very detailed directions on getting best results',
          quote: 'The company gave very detailed directions and step by step instructions on how to get best results. I am pleased with the results.',
          date: 'June 06, 2024'
        },
        {
          type: 'trustpilot',
          rating: 5,
          name: 'Rachel',
          avatar: '', // Add the avatar URL if available
          excerpt: 'What a FUN experience!',
          quote: 'I needed a headshot with extremely short notice and had no time to get all dolled up and find a professional photographer. HeadshotPro was so easy to navigate and after uploading just a few selfies I had a whole photo shoot to choose from. There were some WILD shots in the final review, but I had 9 high quality options that my company could choose from for my website profile. Overall, it was a fun and exciting process! I will definitely be using HeadshotPro again in the future!',
          date: 'June 07, 2024'
        },
        {
          type: 'trustpilot',
          rating: 5,
          name: 'Pete',
          avatar: '', // Add the avatar URL if available
          excerpt: 'Select your photos carefully',
          quote: 'This is an excellent value in comparison to taking the time and effort to get headshots done in a studio or trying to find the perfect backdrop around town. The process is super easy. The biggest thing to be aware of is selecting the right photos to begin with. I would suggest to only select the photos that you are truly happy with your expression, lighting, outfit, hairstyle. I added a few where I had a clean shaven face, goatee, and full beard. The results I got were all over the place. Some were downright comical. I failed to really put a lot of effort in collecting the best photos but I still got at least a dozen contenders for a good profile picture on social media or a website. I would certainly use this service again.',
          date: 'June 07, 2024'
        },
        {
          type: 'trustpilot',
          rating: 5,
          name: 'Aliya',
          avatar: '', // Add the avatar URL if available
          excerpt: 'Great AI headshot tool',
          quote: 'Great AI headshot tool, the best I could find. Worth its price 💯 <br><br>However, for me personally, all headshots were a bit off as the eye color didn’t match. Also, some photos made me look like a baby, while others made me look like a lady in her 50s 😄. But it might have been my mistake as uploaded pictures were very different. It might make sense to just follow the selfies guide. <br><br>The process of selecting favorite photos is not very user-friendly—you need to expand every photo separately.<br>BUT! Customer support and orientation is incredible.<br><br>I will totally recommend this to my family and friends.',
          date: 'June 06, 2024'
        },
        {
          type: 'trustpilot',
          rating: 5,
          name: 'Bob Highbaugh',
          avatar: 'https://user-images.trustpilot.com/66604d667959c9df36a789ca/73x73.png',
          excerpt: 'Great Service, Great Value',
          quote: 'The process was so easy, and the results were excellent. I was amazed at the picture quality. What a time saver!',
          date: 'June 05, 2024'
        }

      ]
    },
    exampleReviews () {
      // Create a copy of reviews array and sort it
      return [...this.$store.state.reviews].slice(0, 50).map((item) => {
        if (item?.review?.quote) {
          return {
            type: 'example',
            rating: item.review.rating,
            name: item.title,
            avatar: item.thumbnail,
            image: item.thumbnail,
            quote: item.review.quote

          }
        } else {
          return null
        }
      }).filter(item => item !== null)
    },
    reviews () {
      const trustpilot = this.trustpilotReviews
      const example = this.exampleReviews
      const mixedReviews = []

      const maxLength = Math.max(trustpilot.length, example.length * 3) // Adjust for 1:3 ratio
      for (let i = 0, j = 0; i < maxLength; i++) {
        if (i % 4 === 0) { // For every fourth iteration, add one trustpilot review
          if (j < trustpilot.length) { mixedReviews.push(trustpilot[j]) }
          j++ // Increment trustpilot index separately
        } else if ((i - Math.floor(i / 4) - 1) < example.length) { // Adjust index for example reviews
          // This line replaces the previous nested if statement
          mixedReviews.push(example[i - Math.floor(i / 4) - 1])
        }
      }
      return mixedReviews
    }
  },
  mounted () {
    this.startScrolling()
  },
  methods: {
    startScrolling () {
      const container = document.getElementById('review-container')
      const scrollStep = 1 // Adjust this value to control the speed
      const scrollInterval = 20 // Adjust this value to control the smoothness

      setInterval(() => {
        if (this.isDragging) { return } // Pause scrolling if dragging
        this.scrollLeft += scrollStep
        container.scrollLeft = this.scrollLeft
        if (this.scrollLeft >= container.scrollWidth - container.clientWidth) {
          this.scrollLeft = 0 // Reset scroll amount to create a loop
        }
      }, scrollInterval)
    },
    startDrag (e) {
      this.isDragging = true
      this.startX = e.pageX || e.touches[0].pageX
      this.startY = e.pageY || e.touches[0].pageY // Initialize startY here
      this.scrollLeft = document.getElementById('review-container').scrollLeft
    },
    stopDrag () {
      this.isDragging = false
      // Update this.scrollLeft to the current scroll position of the container
      this.scrollLeft = document.getElementById('review-container').scrollLeft
    },
    onDrag (e) {
      if (!this.isDragging) { return }

      const x = e.pageX || e.touches[0].pageX
      const y = e.pageY || e.touches[0].pageY // Get the current Y position

      const walkX = (x - this.startX) * 2 // Calculate horizontal movement
      const walkY = y - this.startY // Calculate vertical movement

      console.log(walkX, walkY)

      // Only allow horizontal scrolling by checking if horizontal movement is greater than vertical
      if (Math.abs(walkX) > Math.abs(walkY)) {
        e.preventDefault()
        const newScrollPosition = this.scrollLeft - walkX
        document.getElementById('review-container').scrollLeft = newScrollPosition
      }
    }
  }
}
</script>

<style scoped>

#review-container {
  user-select: none;
}

#review-container img {
  -webkit-user-drag: none;
  user-select: none;
  pointer-events: none;
}

</style>
