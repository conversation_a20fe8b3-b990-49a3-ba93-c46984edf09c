<template>
  <div>
    <h6 v-if="title" class="text-base font-bold text-white">
      {{ title }}
    </h6>
    <ul class="mt-6 space-y-5 text-sm font-normal text-white">
      <li v-for="item in items" :key="item.href">
        <template v-if="item.external">
          <a :href="item.href" target="_blank" title="" class="transition-all duration-150 hover:opacity-80">
            {{ item.title }}
          </a>
        </template>
        <template v-else>
          <nuxt-link :to="item.href" title="" class="transition-all duration-150 hover:opacity-80">
            {{ item.title }}
          </nuxt-link>
        </template>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    items: {
      type: Array,
      default: () => []
    }
  }

}
</script>

<style>

</style>
