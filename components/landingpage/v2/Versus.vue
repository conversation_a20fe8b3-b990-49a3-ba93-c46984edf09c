<template>
  <LandingpageCommonSection class="bg-gray-50">
    <div class="max-w-screen-xl px-5 mx-auto sm:px-6 lg:px-8">
      <div class="text-center">
        <h2 class="text-2xl sm:text-4xl xl:text-[42px] leading-tight font-bold tracking-tighter text-primary-500">
          Professional vs. Free Headshots
        </h2>
        <p class="mt-4 text-base font-medium text-gray-500 sm:text-lg">
          Free tools are tempting, but see why quality matters for your brand image.
        </p>
      </div>

      <div class="flex flex-col items-center justify-center max-w-4xl gap-6 mx-auto mt-8 sm:mt-12 lg:flex-row">
        <div class="w-full p-6 space-y-6 bg-white border border-gray-200 shadow-xs shrink rounded-2xl">
          <img class="object-cover w-full rounded-xl aspect-square opacity-80" src="@/assets/img/versus-block/headshot-chatgpt.png" alt="">
          <img class="object-contain w-auto h-8 mx-auto" src="@/assets/img/versus-block/logo-chatgpt.svg" alt="">

          <ul class="space-y-3 text-base font-medium text-gray-600">
            <li class="flex items-center gap-2">
              <svg aria-hidden="true" class="text-red-500 size-5 shrink-0" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M4.55833 5.4275C4.67552 5.31046 4.83437 5.24472 5 5.24472C5.16562 5.24472 5.32448 5.31046 5.44167 5.4275L10 9.98583L14.5583 5.4275C14.6156 5.36609 14.6846 5.31684 14.7612 5.28268C14.8379 5.24852 14.9206 5.23015 15.0046 5.22867C15.0885 5.22719 15.1718 5.24263 15.2497 5.27406C15.3275 5.3055 15.3982 5.35229 15.4575 5.41163C15.5169 5.47098 15.5637 5.54168 15.5951 5.6195C15.6265 5.69733 15.642 5.78068 15.6405 5.8646C15.639 5.94852 15.6206 6.03128 15.5865 6.10795C15.5523 6.18461 15.5031 6.25362 15.4417 6.31083L10.8833 10.8692L15.4417 15.4275C15.5031 15.4847 15.5523 15.5537 15.5865 15.6304C15.6206 15.7071 15.639 15.7898 15.6405 15.8737C15.642 15.9577 15.6265 16.041 15.5951 16.1188C15.5637 16.1967 15.5169 16.2673 15.4575 16.3267C15.3982 16.386 15.3275 16.4328 15.2497 16.4643C15.1718 16.4957 15.0885 16.5111 15.0046 16.5097C14.9206 16.5082 14.8379 16.4898 14.7612 16.4557C14.6846 16.4215 14.6156 16.3722 14.5583 16.3108L10 11.7525L5.44167 16.3108C5.32319 16.4212 5.16648 16.4813 5.00456 16.4785C4.84265 16.4756 4.68816 16.41 4.57365 16.2955C4.45914 16.181 4.39354 16.0265 4.39069 15.8646C4.38783 15.7027 4.44793 15.546 4.55833 15.4275L9.11667 10.8692L4.55833 6.31083C4.44129 6.19365 4.37555 6.03479 4.37555 5.86917C4.37555 5.70354 4.44129 5.54469 4.55833 5.4275Z"
                />
              </svg>
              Low resemblance
            </li>
            <li class="hidden md:flex items-center gap-2">
              <svg aria-hidden="true" class="text-red-500 size-5 shrink-0" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M4.55833 5.4275C4.67552 5.31046 4.83437 5.24472 5 5.24472C5.16562 5.24472 5.32448 5.31046 5.44167 5.4275L10 9.98583L14.5583 5.4275C14.6156 5.36609 14.6846 5.31684 14.7612 5.28268C14.8379 5.24852 14.9206 5.23015 15.0046 5.22867C15.0885 5.22719 15.1718 5.24263 15.2497 5.27406C15.3275 5.3055 15.3982 5.35229 15.4575 5.41163C15.5169 5.47098 15.5637 5.54168 15.5951 5.6195C15.6265 5.69733 15.642 5.78068 15.6405 5.8646C15.639 5.94852 15.6206 6.03128 15.5865 6.10795C15.5523 6.18461 15.5031 6.25362 15.4417 6.31083L10.8833 10.8692L15.4417 15.4275C15.5031 15.4847 15.5523 15.5537 15.5865 15.6304C15.6206 15.7071 15.639 15.7898 15.6405 15.8737C15.642 15.9577 15.6265 16.041 15.5951 16.1188C15.5637 16.1967 15.5169 16.2673 15.4575 16.3267C15.3982 16.386 15.3275 16.4328 15.2497 16.4643C15.1718 16.4957 15.0885 16.5111 15.0046 16.5097C14.9206 16.5082 14.8379 16.4898 14.7612 16.4557C14.6846 16.4215 14.6156 16.3722 14.5583 16.3108L10 11.7525L5.44167 16.3108C5.32319 16.4212 5.16648 16.4813 5.00456 16.4785C4.84265 16.4756 4.68816 16.41 4.57365 16.2955C4.45914 16.181 4.39354 16.0265 4.39069 15.8646C4.38783 15.7027 4.44793 15.546 4.55833 15.4275L9.11667 10.8692L4.55833 6.31083C4.44129 6.19365 4.37555 6.03479 4.37555 5.86917C4.37555 5.70354 4.44129 5.54469 4.55833 5.4275Z"
                />
              </svg>
              Lacks photo realism
            </li>
            <li class="hidden md:flex items-center gap-2">
              <svg aria-hidden="true" class="text-green-500 size-5 shrink-0" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M16.7041 5.02169C16.7826 5.08131 16.8486 5.15584 16.8984 5.24101C16.9481 5.32618 16.9805 5.42032 16.9938 5.51803C17.007 5.61574 17.0009 5.71512 16.9758 5.81046C16.9506 5.90581 16.9069 5.99526 16.8471 6.07369L8.84709 16.5737C8.7822 16.6587 8.69986 16.7289 8.60561 16.7795C8.51136 16.8301 8.40737 16.86 8.30062 16.8671C8.19388 16.8742 8.08685 16.8584 7.98672 16.8207C7.88659 16.783 7.79568 16.7244 7.72009 16.6487L3.22009 12.1487C3.08761 12.0065 3.01549 11.8185 3.01892 11.6242C3.02234 11.4299 3.10106 11.2445 3.23847 11.1071C3.37588 10.9697 3.56127 10.8909 3.75557 10.8875C3.94987 10.8841 4.13792 10.9562 4.28009 11.0887L8.17409 14.9817L15.6541 5.16469C15.7745 5.00664 15.9527 4.90281 16.1495 4.876C16.3464 4.84919 16.5458 4.90159 16.7041 5.02169Z"
                />
              </svg>
              Free to try
            </li>
          </ul>
        </div>

        <div class="relative w-full lg:w-[320px] shrink-0">
          <div class="absolute inset-0 bg-[#21B8BA] blur-[32px] opacity-15" />

          <div class="p-8 relative space-y-8 border-4 border-[#21B8BA] shadow-xs bg-gradient-to-b from-white to-[#F1FCFB] shadow-xl rounded-2xl">
            <img class="object-cover w-full rounded-xl aspect-square" src="@/assets/img/versus-block/headshot-headshotpro.png" alt="">
            <img class="object-contain w-auto h-8 mx-auto" src="@/assets/img/logo.svg" alt="">

            <ul class="space-y-3 text-base font-bold text-primary-500">
              <li class="flex items-center gap-2">
                <svg aria-hidden="true" class="text-green-500 size-5 shrink-0" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M16.7041 5.02169C16.7826 5.08131 16.8486 5.15584 16.8984 5.24101C16.9481 5.32618 16.9805 5.42032 16.9938 5.51803C17.007 5.61574 17.0009 5.71512 16.9758 5.81046C16.9506 5.90581 16.9069 5.99526 16.8471 6.07369L8.84709 16.5737C8.7822 16.6587 8.69986 16.7289 8.60561 16.7795C8.51136 16.8301 8.40737 16.86 8.30062 16.8671C8.19388 16.8742 8.08685 16.8584 7.98672 16.8207C7.88659 16.783 7.79568 16.7244 7.72009 16.6487L3.22009 12.1487C3.08761 12.0065 3.01549 11.8185 3.01892 11.6242C3.02234 11.4299 3.10106 11.2445 3.23847 11.1071C3.37588 10.9697 3.56127 10.8909 3.75557 10.8875C3.94987 10.8841 4.13792 10.9562 4.28009 11.0887L8.17409 14.9817L15.6541 5.16469C15.7745 5.00664 15.9527 4.90281 16.1495 4.876C16.3464 4.84919 16.5458 4.90159 16.7041 5.02169Z"
                  />
                </svg>
                Studio-quality headshots
              </li>
              <li class="flex items-center gap-2">
                <svg aria-hidden="true" class="text-green-500 size-5 shrink-0" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M16.7041 5.02169C16.7826 5.08131 16.8486 5.15584 16.8984 5.24101C16.9481 5.32618 16.9805 5.42032 16.9938 5.51803C17.007 5.61574 17.0009 5.71512 16.9758 5.81046C16.9506 5.90581 16.9069 5.99526 16.8471 6.07369L8.84709 16.5737C8.7822 16.6587 8.69986 16.7289 8.60561 16.7795C8.51136 16.8301 8.40737 16.86 8.30062 16.8671C8.19388 16.8742 8.08685 16.8584 7.98672 16.8207C7.88659 16.783 7.79568 16.7244 7.72009 16.6487L3.22009 12.1487C3.08761 12.0065 3.01549 11.8185 3.01892 11.6242C3.02234 11.4299 3.10106 11.2445 3.23847 11.1071C3.37588 10.9697 3.56127 10.8909 3.75557 10.8875C3.94987 10.8841 4.13792 10.9562 4.28009 11.0887L8.17409 14.9817L15.6541 5.16469C15.7745 5.00664 15.9527 4.90281 16.1495 4.876C16.3464 4.84919 16.5458 4.90159 16.7041 5.02169Z"
                  />
                </svg>
                Looks just like you
              </li>
              <li class="flex items-center gap-2">
                <svg aria-hidden="true" class="text-green-500 size-5 shrink-0" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M16.7041 5.02169C16.7826 5.08131 16.8486 5.15584 16.8984 5.24101C16.9481 5.32618 16.9805 5.42032 16.9938 5.51803C17.007 5.61574 17.0009 5.71512 16.9758 5.81046C16.9506 5.90581 16.9069 5.99526 16.8471 6.07369L8.84709 16.5737C8.7822 16.6587 8.69986 16.7289 8.60561 16.7795C8.51136 16.8301 8.40737 16.86 8.30062 16.8671C8.19388 16.8742 8.08685 16.8584 7.98672 16.8207C7.88659 16.783 7.79568 16.7244 7.72009 16.6487L3.22009 12.1487C3.08761 12.0065 3.01549 11.8185 3.01892 11.6242C3.02234 11.4299 3.10106 11.2445 3.23847 11.1071C3.37588 10.9697 3.56127 10.8909 3.75557 10.8875C3.94987 10.8841 4.13792 10.9562 4.28009 11.0887L8.17409 14.9817L15.6541 5.16469C15.7745 5.00664 15.9527 4.90281 16.1495 4.876C16.3464 4.84919 16.5458 4.90159 16.7041 5.02169Z"
                  />
                </svg>
                Fast turn-around (~2hr)
              </li>
            </ul>
          </div>

          <div class="absolute inset-x-0 flex items-center justify-center -top-[16px]">
            <span class="flex items-center justify-center text-xs font-bold tracking-wide text-white uppercase px-4 py-1.5 rounded-full bg-[#21B8BA] border-4 border-white">
              <svg aria-hidden="true" class="size-4" viewBox="0 0 17 17" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M8.50002 1.66911C7.03762 1.66911 5.60162 1.78831 4.20322 2.01711C4.06276 2.04008 3.93503 2.11222 3.84286 2.22067C3.75068 2.32911 3.70005 2.46679 3.70002 2.60911V2.91871C3.03842 3.04431 2.38642 3.19471 1.74402 3.36991C1.62045 3.40366 1.51091 3.47607 1.43145 3.57654C1.35199 3.67702 1.30678 3.80029 1.30242 3.92831C1.26654 4.94451 1.61901 5.93623 2.28817 6.70184C2.95734 7.46745 3.89296 7.94948 4.90482 8.04991C5.53004 8.75692 6.34981 9.26392 7.26162 9.50751C7.18649 10.1228 7.00113 10.7195 6.71442 11.2691H6.50002C6.18176 11.2691 5.87654 11.3955 5.65149 11.6206C5.42645 11.8456 5.30002 12.1509 5.30002 12.4691V14.4691H4.70002C4.14802 14.4691 3.70002 14.9171 3.70002 15.4691C3.70002 15.8003 3.96882 16.0691 4.30002 16.0691H12.7C12.8592 16.0691 13.0118 16.0059 13.1243 15.8934C13.2368 15.7809 13.3 15.6282 13.3 15.4691C13.3 14.9171 12.852 14.4691 12.3 14.4691H11.7V12.4691C11.7 12.1509 11.5736 11.8456 11.3485 11.6206C11.1235 11.3955 10.8183 11.2691 10.5 11.2691H10.2856C9.99894 10.7195 9.81358 10.1228 9.73842 9.50751C10.6502 9.26392 11.47 8.75692 12.0952 8.04991C13.1071 7.94948 14.0427 7.46745 14.7119 6.70184C15.381 5.93623 15.7335 4.94451 15.6976 3.92831C15.6933 3.80029 15.648 3.67702 15.5686 3.57654C15.4891 3.47607 15.3796 3.40366 15.256 3.36991C14.6102 3.19384 13.9577 3.04333 13.3 2.91871V2.60911C13.3 2.46679 13.2494 2.32911 13.1572 2.22067C13.065 2.11222 12.9373 2.04008 12.7968 2.01711C11.3763 1.78498 9.93935 1.66859 8.50002 1.66911ZM2.52002 4.40671C2.90962 4.30911 3.30322 4.22112 3.70002 4.14111V4.86912C3.70002 5.46112 3.80722 6.02752 4.00402 6.55152C3.60142 6.34117 3.25599 6.03612 2.99747 5.66262C2.73896 5.28913 2.5751 4.8576 2.52002 4.40671ZM14.48 4.40671C14.425 4.85752 14.2613 5.2882 14.0029 5.66169C13.7445 6.03518 13.3993 6.34027 12.9968 6.55072C13.1928 6.02832 13.3 5.46112 13.3 4.86912V4.14111C13.6968 4.22031 14.0904 4.30911 14.48 4.40671Z"
                />
              </svg>
              Best results by far
            </span>
          </div>
        </div>

        <div class="w-full p-6 space-y-6 bg-white border border-gray-200 shadow-xs shrink rounded-2xl">
          <img class="object-cover w-full rounded-xl aspect-square opacity-80" src="@/assets/img/versus-block/headshot-canva.png" alt="">
          <img class="object-contain w-auto h-8 mx-auto" src="@/assets/img/versus-block/logo-canva.svg" alt="">

          <ul class="space-y-3 text-base font-medium text-gray-600">
            <li class="flex items-center gap-2">
              <svg aria-hidden="true" class="text-red-500 size-5 shrink-0" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M4.55833 5.4275C4.67552 5.31046 4.83437 5.24472 5 5.24472C5.16562 5.24472 5.32448 5.31046 5.44167 5.4275L10 9.98583L14.5583 5.4275C14.6156 5.36609 14.6846 5.31684 14.7612 5.28268C14.8379 5.24852 14.9206 5.23015 15.0046 5.22867C15.0885 5.22719 15.1718 5.24263 15.2497 5.27406C15.3275 5.3055 15.3982 5.35229 15.4575 5.41163C15.5169 5.47098 15.5637 5.54168 15.5951 5.6195C15.6265 5.69733 15.642 5.78068 15.6405 5.8646C15.639 5.94852 15.6206 6.03128 15.5865 6.10795C15.5523 6.18461 15.5031 6.25362 15.4417 6.31083L10.8833 10.8692L15.4417 15.4275C15.5031 15.4847 15.5523 15.5537 15.5865 15.6304C15.6206 15.7071 15.639 15.7898 15.6405 15.8737C15.642 15.9577 15.6265 16.041 15.5951 16.1188C15.5637 16.1967 15.5169 16.2673 15.4575 16.3267C15.3982 16.386 15.3275 16.4328 15.2497 16.4643C15.1718 16.4957 15.0885 16.5111 15.0046 16.5097C14.9206 16.5082 14.8379 16.4898 14.7612 16.4557C14.6846 16.4215 14.6156 16.3722 14.5583 16.3108L10 11.7525L5.44167 16.3108C5.32319 16.4212 5.16648 16.4813 5.00456 16.4785C4.84265 16.4756 4.68816 16.41 4.57365 16.2955C4.45914 16.181 4.39354 16.0265 4.39069 15.8646C4.38783 15.7027 4.44793 15.546 4.55833 15.4275L9.11667 10.8692L4.55833 6.31083C4.44129 6.19365 4.37555 6.03479 4.37555 5.86917C4.37555 5.70354 4.44129 5.54469 4.55833 5.4275Z"
                />
              </svg>
              Limited quality
            </li>
            <li class="hidden md:flex items-center gap-2">
              <svg aria-hidden="true" class="text-red-500 size-5 shrink-0" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M4.55833 5.4275C4.67552 5.31046 4.83437 5.24472 5 5.24472C5.16562 5.24472 5.32448 5.31046 5.44167 5.4275L10 9.98583L14.5583 5.4275C14.6156 5.36609 14.6846 5.31684 14.7612 5.28268C14.8379 5.24852 14.9206 5.23015 15.0046 5.22867C15.0885 5.22719 15.1718 5.24263 15.2497 5.27406C15.3275 5.3055 15.3982 5.35229 15.4575 5.41163C15.5169 5.47098 15.5637 5.54168 15.5951 5.6195C15.6265 5.69733 15.642 5.78068 15.6405 5.8646C15.639 5.94852 15.6206 6.03128 15.5865 6.10795C15.5523 6.18461 15.5031 6.25362 15.4417 6.31083L10.8833 10.8692L15.4417 15.4275C15.5031 15.4847 15.5523 15.5537 15.5865 15.6304C15.6206 15.7071 15.639 15.7898 15.6405 15.8737C15.642 15.9577 15.6265 16.041 15.5951 16.1188C15.5637 16.1967 15.5169 16.2673 15.4575 16.3267C15.3982 16.386 15.3275 16.4328 15.2497 16.4643C15.1718 16.4957 15.0885 16.5111 15.0046 16.5097C14.9206 16.5082 14.8379 16.4898 14.7612 16.4557C14.6846 16.4215 14.6156 16.3722 14.5583 16.3108L10 11.7525L5.44167 16.3108C5.32319 16.4212 5.16648 16.4813 5.00456 16.4785C4.84265 16.4756 4.68816 16.41 4.57365 16.2955C4.45914 16.181 4.39354 16.0265 4.39069 15.8646C4.38783 15.7027 4.44793 15.546 4.55833 15.4275L9.11667 10.8692L4.55833 6.31083C4.44129 6.19365 4.37555 6.03479 4.37555 5.86917C4.37555 5.70354 4.44129 5.54469 4.55833 5.4275Z"
                />
              </svg>
              No fine-tuning
            </li>
            <li class="hidden md:flex items-center gap-2">
              <svg aria-hidden="true" class="text-green-500 size-5 shrink-0" viewBox="0 0 20 21" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M16.7041 5.02169C16.7826 5.08131 16.8486 5.15584 16.8984 5.24101C16.9481 5.32618 16.9805 5.42032 16.9938 5.51803C17.007 5.61574 17.0009 5.71512 16.9758 5.81046C16.9506 5.90581 16.9069 5.99526 16.8471 6.07369L8.84709 16.5737C8.7822 16.6587 8.69986 16.7289 8.60561 16.7795C8.51136 16.8301 8.40737 16.86 8.30062 16.8671C8.19388 16.8742 8.08685 16.8584 7.98672 16.8207C7.88659 16.783 7.79568 16.7244 7.72009 16.6487L3.22009 12.1487C3.08761 12.0065 3.01549 11.8185 3.01892 11.6242C3.02234 11.4299 3.10106 11.2445 3.23847 11.1071C3.37588 10.9697 3.56127 10.8909 3.75557 10.8875C3.94987 10.8841 4.13792 10.9562 4.28009 11.0887L8.17409 14.9817L15.6541 5.16469C15.7745 5.00664 15.9527 4.90281 16.1495 4.876C16.3464 4.84919 16.5458 4.90159 16.7041 5.02169Z"
                />
              </svg>
              Decent speed
            </li>
          </ul>
        </div>
      </div>

      <div class="mt-8 text-center sm:mt-12">
        <p class="text-sm font-semibold text-primary-500">
          Input photos used to create these headshots:
        </p>
        <div class="flex items-center justify-center gap-4 mt-4">
          <img class="border border-gray-300 rounded-lg size-16 -rotate-3" src="@/assets/img/versus-block/input-photo-1.jpeg" alt="">
          <img class="border border-gray-300 rounded-lg size-16" src="@/assets/img/versus-block/input-photo-2.jpeg" alt="">
          <img class="border border-gray-300 rounded-lg size-16 rotate-1" src="@/assets/img/versus-block/input-photo-3.jpeg" alt="">
          <img class="border border-gray-300 rounded-lg size-16 -rotate-2" src="@/assets/img/versus-block/input-photo-4.jpeg" alt="">
        </div>
      </div>
    </div>
  </LandingpageCommonSection>
</template>

<script>
export default {

}
</script>

<style>

</style>
