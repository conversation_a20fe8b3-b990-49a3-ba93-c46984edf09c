<template>
  <LandingpageV2ReviewItemWrapper>
    <div class="flex items-center gap-3">
      <ImageDns
        v-if="item.avatar"
        class="size-6 shrink-0 rounded-full object-cover"
        :src="item.avatar"
        alt=""
      />

      <p class="text-sm font-bold leading-[18px] tracking-[-0.056px] text-[#111111] capitalize">
        {{ item.name }}
      </p>
    </div>

    <blockquote class="text-[13px] font-normal tracking-[-0.056px] text-[#111111]">
      "{{ item.quote }}"
    </blockquote>
    <div class="relative">
      <div class="absolute left-2 top-2 flex">
        <span class="rounded-full bg-gray-900/60 px-2 pb-0.5 pt-px text-[9px] font-bold uppercase tracking-normal text-white">100% AI GENERATED</span>
      </div>
      <div class="relative group cursor-pointer rounded-lg overflow-hidden" @click="showEnlargedImage(item?.thumbnail || item.image)">
        <ImageDns v-if="item.image" class="w-full object-cover rounded-md" :src="item.image" alt="" />
        <div class="hidden group-hover:flex absolute inset-0 bg-black bg-opacity-50 items-center justify-center z-50">
          <span class="text-white text-sm">Click to enlarge</span>
        </div>
      </div>
    </div>

    <portal to="modal">
      <div
        v-if="enlargedImage"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 cursor-pointer"
        @click="enlargedImage = null"
      >
        <div class="max-h-[90vh] max-w-[768px] object-contain relative">
          <img :src="enlargedImage" class="max-h-[90vh] max-w-[768px] object-contain">
          <span class="absolute top-2 right-2 text-white text-sm cursor-pointer" @click="enlargedImage = null">
            Close
          </span>
          <div class="absolute text-xs font-medium top-2 left-2 text-white  bg-teal-600/70 backdrop-blur-md px-1.5 py-0.5 rounded-md">
            100% AI generated
          </div>
        </div>
      </div>
    </portal>
  </LandingpageV2ReviewItemWrapper>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      enlargedImage: null
    }
  },
  methods: {
    showEnlargedImage (image) {
      this.enlargedImage = image
    }
  }
}
</script>
