<template>
  <div class="space-y-8">
    <client-only>
      <div class="w-full flex justify-start md:justify-center gap-4">
        <Tab class="w-auto order-first xs:order-none">
          <TabItem :active="pricingTab === 'individual'" @click="pricingTab = 'individual'">
            <span>Individual</span>
          </TabItem>
          <TabItem :active="pricingTab === 'teams'" @click="pricingTab = 'teams'">
            <span>Teams</span>
          </TabItem>
        </Tab>
        <CurrencyToggle :show-title="(screenWidth > 768)" />
      </div>
      <PrecheckoutPricingTiers v-if="pricingTab === 'individual'" :key="currencyKey" />
      <client-only>
        <div v-if="pricingTab === 'individual'" class="flex flex-row gap-3 items-center justify-center">
          <div v-for="provider in paymentProviders" :key="provider.id">
            <img :src="provider.images[0]" class="h-6">
          </div>
        </div>
      </client-only>
      <LandingpageB2bPricingBlock v-if="pricingTab === 'teams'" @showSalesModal="showSalesModal = true" />
      <MarketingSalesModal v-if="showSalesModal" @close="showSalesModal = false" />
    </client-only>
  </div>
</template>

<script>
import CheckoutMixin from '@/mixins/CheckoutMixin'
export default {
  mixins: [CheckoutMixin],
  props: {
    recommended: {
      type: String,
      required: false,
      default: '100% Money Back Guarantee'
    }
  },
  data () {
    return {
      pricingTab: 'individual',
      showSalesModal: false,
      currencyKey: 0
    }
  },
  mounted () {
    // If route contains ?tab=teams set pricingTab to teams
    if (this.$route.query.tab === 'teams') {
      this.pricingTab = 'teams'
    }

    this.$root.$on('currency-changed', this.handleCurrencyChange)
  },
  beforeDestroy () {
    this.$root.$off('currency-changed', this.handleCurrencyChange)
  },
  methods: {
    handleCurrencyChange () {
      this.currencyKey += 1

      this.$forceUpdate()

      this.$nextTick(() => {
        this.$children.forEach((child) => {
          if (child.$forceUpdate) {
            child.$forceUpdate()
          }
        })
      })
    }
  }
}
</script>

<i18n>
  {
    "es": {
      "Get 40 headshots with 4 unique backdrops and outfits.": "Obtén 40 fotos con 4 fondos y atuendos únicos.",
      "hours turnaround time": "horas para entrega",
      "headshots": "fotos",
      "unique styles": "estilos únicos",
      "backdrops": "fondos",
      "outfits": "atuendos",
      "Choice of": "Escoge de",
      "edit credits": "créditos de edición",
      "Get 40 headshots in 3 hours": "Obtén 40 fotos en 3 horas",
      "100% Money Back Guarantee": "Garantía de devolución del 100%",
      "Get 100 headshots with 10 unique backdrops and outfits.": "Obtén 100 fotos con 10 fondos y atuendos únicos.",
      "unique clothing options": "estilos de ropa únicos",
      "Get 100 headshots in 2 hours!": "¡Obtén 100 fotos en 2 horas!",
      "Get 200 headshots with 20 unique backdrops and outfits.": "Obtén 200 fotos con 20 fondos y atuendos únicos.",
      "hour turnaround time": "hora para entrega",
      "Get 200 headshots in 1 hour": "Obtén 200 fotos en 1 hora"
    },
    "de": {
      "Get 40 headshots with 4 unique backdrops and outfits.": "Erhalte 40 Headshots mit 4 einzigartigen Hintergründen und Outfits.",
      "hours turnaround time": "Stunden Bearbeitungszeit",
      "headshots": "Headshots",
      "unique styles": "einzigartige Stile",
      "backdrops": "Hintergründe",
      "outfits": "Outfits",
      "Choice of": "Auswahl von",
      "edit credits": "Bearbeitungsguthaben",
      "Get 40 headshots in 3 hours": "Erhalte 40 Headshots in 3 Stunden",
      "100% Money Back Guarantee": "100 % Geld-zurück-Garantie",
      "Get 100 headshots with 10 unique backdrops and outfits.": "Erhalte 100 Headshots mit 10 einzigartigen Hintergründen und Outfits.",
      "unique clothing options": "einzigartige Outfit-Optionen",
      "Get 100 headshots in 2 hours!": "Erhalte 100 Headshots in 2 Stunden!",
      "Get 200 headshots with 20 unique backdrops and outfits.": "Erhalte 200 Headshots mit 20 einzigartigen Hintergründen und Outfits.",
      "hour turnaround time": "Stunde Bearbeitungszeit",
      "Get 200 headshots in 1 hour": "Erhalte 200 Headshots in 1 Stunde"
    }
  }
</i18n>
