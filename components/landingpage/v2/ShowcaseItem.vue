<template>
  <div class="w-full shrink-0 snap-start">
    <div class="flex items-center gap-3">
      <img class="size-8 shrink-0" src="@/assets/img/landing-page/icon-profile.svg" alt="">
      <p v-if="item.usecase" class="text-base font-medium tracking-tight text-primary-500">
        {{ item.usecase }}
        <!-- Use on your LinkedIn profile -->
      </p>
    </div>

    <div class="relative mt-3 overflow-hidden rounded-lg">
      <img
        v-if="item.photo"
        class="h-full w-full object-cover"
        :src="item.photo"
        alt=""
      >
      <!-- src="@/assets/img/landing-page/portrait-1.jpg"  -->

      <div class="absolute inset-x-0 bottom-0 h-1/2 bg-gradient-to-t from-black to-transparent" />

      <div class="absolute inset-x-0 bottom-0 p-6">
        <svg aria-hidden="true" class="h-2.5 w-auto text-white" viewBox="0 0 13 10" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M6.496 9.152L6.88 6.4C7.11467 4.69333 7.72267 3.28533 8.704 2.176C9.68533 1.06667 10.9973 0.341333 12.64 0L12.352 2.048C11.6053 2.21867 10.976 2.53867 10.464 3.008C9.97333 3.47733 9.62133 4.032 9.408 4.672H11.36L10.72 9.152H6.496ZM0 9.152L0.384 6.4C0.618667 4.69333 1.22667 3.28533 2.208 2.176C3.18933 1.06667 4.50133 0.341333 6.144 0L5.888 2.048C5.14133 2.21867 4.512 2.53867 4 3.008C3.488 3.47733 3.12533 4.032 2.912 4.672H4.864L4.256 9.152H0Z"
          />
        </svg>

        <blockquote class="mt-3">
          <p v-if="item.quote" class="text-sm font-medium italic tracking-tight text-white">
            {{ item.quote }}
            <!-- I was looking for professional work photos for my LinkedIn so this is perfect -->
          </p>

          <div class="mt-3 flex items-center gap-4">
            <img
              v-if="item.avatar"
              class="size-16 shrink-0 rounded-full object-cover"
              :src="item.avatar"
              alt=""
            >
            <!-- :src="@/assets/img/landing-page/avatar-4.jpg"  -->
            <div class="min-w-0 flex-1">
              <p v-if="item.name" class="text-base font-bold tracking-tight text-white">
                <!-- D. Burd -->
                {{ item.name }}
              </p>
              <p v-if="item.title" class="text-sm font-normal tracking-tight text-white">
                {{ item.title }}
                <!-- Master of Business Administration, MBA at Universidad de la Tercera Edad -->
              </p>
            </div>
          </div>
        </blockquote>
      </div>

      <div class="absolute left-2.5 top-2.5">
        <span class="rounded-full bg-gray-900/60 px-3 pb-0.5 pt-px text-xs font-bold uppercase tracking-normal text-white">100% AI GENERATED</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true
    }
  }

}
</script>

<style>

</style>
