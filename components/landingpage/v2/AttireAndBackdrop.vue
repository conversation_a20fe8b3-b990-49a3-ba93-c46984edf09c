<template>
  <section id="features" class="bg-[#F8FCFF] py-12 sm:py-16 lg:py-20 xl:py-24">
    <div class="mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8 2xl:px-0">
      <div class="flex-col md:flex-row flex gap-4 md:gap-32 items-center content-center justify-between">
        <div class="w-full md:max-w-[600px]">
          <video
            ref="demoVideo"
            src="https://storage.googleapis.com/headshotpro-public-content/headshotpro/videos/demo-clothingpicker-v2.mp4"
            :placeholder="require('@/assets/img/style-picker-demo-wallpaper.jpg')"
            class="rounded-lg shadow-lg"
            :autoplay="!isMobile()"
            loop
            muted
            playsinline
          />
        </div>
        <div class="flex flex-col space-y-4">
          <div class=" flex items-center justify-start w-full">
            <img src="@/assets/img/backdrop-clothing-combo.png" alt="Backdrop and Attire" class="h-full object-cover w-[250px]">
          </div>
          <div class="space-y-2">
            <!-- <p class="text-sm font-normal tracking-[-0.3px] text-paragraph sm:text-base">
              ⚡ Choose your backdrop and attire
            </p> -->
            <h2 class="mt-3 text-2xl font-bold tracking-[-1.05px] sm:text-3xl lg:text-4xl text-primary-500 max-w-xl ">
              {{ $t('Choose your backdrop and outfit') }}
            </h2>
            <p class="mt-3 text-base font-medium text-[#474368] sm:text-lg">
              {{ $t("Choose the location of your shoot and pick an outfit from a wide range of options. You'll get 10 headshots per combination, to make sure you get the perfect shot.") }}
            </p>
          </div>
          <ol class="list-decimal list-inside  space-y-2 text-gray-700/70 font-medium">
            <li>
              {{ $t('Pick the backdrop of your choice') }}
            </li>
            <li>
              {{ $t('Select your matching outfit') }}
            </li>
            <li>
              {{ $t('Get 10 headshots per combination') }}
            </li>
          </ol>

          <div class="flex flex-col md:flex-row items-center justify-start pt-2 md:space-x-4 space-y-2 md:space-y-0">
            <nuxt-link to="/backdrop-and-outfit" class="w-full md:w-auto">
              <ButtonWhite class="w-full md:w-auto">
                <span class="flex-shrink-0">{{ $t('View all options') }}</span>
              </ButtonWhite>
            </nuxt-link>
            <div class="flex md:space-x-2 items-center justify-start">
              <div class="-space-x-3 overflow-hidden hidden md:flex">
                <img
                  v-for="item in $store.state.clothing.slice(0, 3)"
                  :key="item?.type"
                  class="inline-block h-6 w-6 rounded-full border-[1px] border-white"
                  :src="item?.images?.male || item?.images?.female"
                  :alt="item?.type"
                >
                <img
                  v-for="item in $store.state.styles.slice(0, 3)"
                  :key="item?.title"
                  class="inline-block h-6 w-6 rounded-full border-[1px] border-white"
                  :src="item?.image?.femaleImage || item?.image?.maleImage"
                  :alt="item?.title"
                >
              </div>
              <span class="text-sm text-gray-600">
                {{ $t('Choose from') }} {{ $store.state.clothing.length }}+ {{ $t('outfits') }} {{ $t('and') }} {{ $store.state.styles.length }}+ {{ $t('backdrops') }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  methods: {
    isMobile () {
      if (typeof window === 'undefined') {
        return false
      }

      const userAgent = navigator.userAgent
      if ((/windows phone/i.test(userAgent) || /android/i.test(userAgent) || /iPad|iPhone|iPod/.test(userAgent)) && !window.MSStream) {
        return true
      }
      return false
    }
  }
}
</script>

<style>

</style>

<i18n>
  {
    "de": {
      "Choose your backdrop and outfit": "Wähle Hintergrund und Outfit",
      "Choose the location of your shoot and pick an outfit from a wide range of options. You'll get 10 headshots per combination, to make sure you get the perfect shot.": "Wähle deinen Hintergrund und ein Outfit aus vielen Optionen. Du bekommst 10 Bewerbungsfotos pro Kombination - so ist garantiert das perfekte Bild dabei.",
      "Pick the backdrop of your choice": "Wähle den Hintergrund deiner Wahl",
      "Select your matching outfit": "Such dir das passende Outfit aus",
      "Get 10 headshots per combination": "Bekomme 10 Fotos pro Kombination",
      "View all options": "Alle Optionen ansehen",
      "Choose from": "Wähle aus",
      "outfits": "Outfits",
      "backdrops": "Hintergründe",
      "and": "und"
    }
  }
</i18n>
