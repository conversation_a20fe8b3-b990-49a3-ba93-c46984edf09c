<template>
  <div class="bg-muted-50 pb-4 border-b border-gray-200">
    <div class="w-full flex flex-col px-4 py-6 gap-5">
      <div class="flex flex-row w-full items-center justify-center gap-2 bg-green-500/5 p-2 rounded-md">
        <img class="h-4 w-auto" src="@/assets/img/trustpilot-stars-5.svg" alt="" loading="lazy">
        <span class="text-sm text-[#191919] font-semibold">{{ $t('2,535 reviews') }}</span>
        <img class="h-5 w-auto" src="@/assets/img/landing-page/logo-trustpilot.png" alt="" loading="lazy">
      </div>
      <ExperimentWrapper id="30-new-homepage-hero-h1-copy" type="h3" class="text-[28px] text-center font-bold text-primary-500 leading-[28px]">
        <template #control>
          {{ $t('Professional business headshots, without a physical photo shoot') }}
        </template>
        <template #400_photographer>
          Look like you hired a $400 photographer without leaving home
        </template>
        <template #realistic_ai>
          Realistic AI headshots that look exactly like you
        </template>
        <template #skip_photoshoot>
          Skip the photoshoot. Keep the professional look
        </template>
        <template #linkedin-ready>
          From selfie to LinkedIn‑ready in 2 hours
        </template>
      </ExperimentWrapper>

      <ul class="flex flex-col items-center justify-center gap-3">
        <li class="flex flex-row items-center justify-center gap-2">
          <IconMiniCamera class="w-5 h-5 text-[#21B8BA]" />
          <span class="text-[15px] text-primary-500 font-medium">{{ $t('8x cheaper than a photographer') }}</span>
        </li>
        <li class="flex flex-row items-center justify-center gap-2">
          <IconMiniStar class="w-5 h-5 text-[#FFBA00]" />
          <span class="text-[15px] text-primary-500 font-medium">{{ $t('Indistinguishable from real photos') }}</span>
        </li>
        <li class="flex flex-row items-center justify-center gap-2">
          <IconMiniThumbUp class="w-5 h-5 text-[#F08B2D]" />
          <span class="text-[15px] text-primary-500 font-medium">{{ $t('Used by 102,207 happy customers') }}</span>
        </li>
      </ul>

      <client-only>
        <div class="flex flex-col gap-2">
          <template v-if="!isLoggedIn">
            <nuxt-link :to="'/auth/login?redirect=' + encodeURIComponent('/app')" class="w-full">
              <ButtonPrimary class="!bg-[#ff6600] border border-black/30 w-full">
                <span class="font-bold">{{ $t('Create your headshots') }}</span>
                <IconSmallArrow class="w-5 h-5 text-white" />
              </ButtonPrimary>
            </nuxt-link>
            <nuxt-link to="/company-headshots" class="w-full">
              <ButtonWhite class="w-full">
                <span>{{ $t('View team packages') }}</span>
              </ButtonWhite>
            </nuxt-link>
          </template>
          <nuxt-link v-else to="/app" class="w-full">
            <ButtonPrimary class="w-full">
              {{ $t('Access your photos') }}
            </ButtonPrimary>
          </nuxt-link>
        </div>
      </client-only>
      <h1 class="font-medium text-[#474368]/70 text-base text-center">
        {{ $t('The #1 AI Headshot Generator for Professional Headshots') }}
      </h1>
      <div class="flex items-center justify-center gap-1.5">
        <svg aria-hidden="true" class="size-5 shrink-0 text-teal-500" viewBox="0 0 18 19" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M15.8836 7.24277C15.6164 6.96855 15.3422 6.68027 15.2367 6.43418C15.1312 6.18809 15.1383 5.82246 15.1312 5.4498C15.1242 4.76777 15.1102 3.9873 14.5687 3.4459C14.0273 2.90449 13.2469 2.89043 12.5648 2.8834C12.1922 2.87637 11.8125 2.86934 11.5805 2.77793C11.3484 2.68652 11.0461 2.39824 10.7719 2.13105C10.2867 1.66699 9.73125 1.13965 9 1.13965C8.26875 1.13965 7.71328 1.66699 7.22813 2.13105C6.95391 2.39824 6.66563 2.67246 6.41953 2.77793C6.17344 2.8834 5.80781 2.87637 5.43516 2.8834C4.75313 2.89043 3.97266 2.90449 3.43125 3.4459C2.88984 3.9873 2.87578 4.76777 2.86875 5.4498C2.86172 5.82246 2.85469 6.20215 2.76328 6.43418C2.67188 6.66621 2.38359 6.96855 2.11641 7.24277C1.65234 7.72793 1.125 8.2834 1.125 9.01465C1.125 9.7459 1.65234 10.3014 2.11641 10.7865C2.38359 11.0607 2.65781 11.349 2.76328 11.5951C2.86875 11.8412 2.86172 12.2068 2.86875 12.5795C2.87578 13.2615 2.88984 14.042 3.43125 14.5834C3.97266 15.1248 4.75313 15.1389 5.43516 15.1459C5.80781 15.1529 6.1875 15.16 6.41953 15.2514C6.65156 15.3428 6.95391 15.6311 7.22813 15.8982C7.71328 16.3623 8.26875 16.8896 9 16.8896C9.73125 16.8896 10.2867 16.3623 10.7719 15.8982C11.0461 15.6311 11.3344 15.3568 11.5805 15.2514C11.8266 15.1459 12.1922 15.1529 12.5648 15.1459C13.2469 15.1389 14.0273 15.1248 14.5687 14.5834C15.1102 14.042 15.1242 13.2615 15.1312 12.5795C15.1383 12.2068 15.1453 11.8271 15.2367 11.5951C15.3281 11.3631 15.6164 11.0607 15.8836 10.7865C16.3477 10.3014 16.875 9.7459 16.875 9.01465C16.875 8.2834 16.3477 7.72793 15.8836 7.24277ZM12.4805 7.73496L8.36016 11.6725C8.25363 11.7727 8.11265 11.8281 7.96641 11.8271C7.82232 11.8277 7.68365 11.7722 7.57969 11.6725L5.51953 9.70371C5.46239 9.65386 5.41592 9.59297 5.38291 9.52469C5.3499 9.45642 5.33104 9.38218 5.32746 9.30644C5.32387 9.23069 5.33564 9.155 5.36206 9.08392C5.38847 9.01283 5.42899 8.94783 5.48116 8.8928C5.53334 8.83778 5.59611 8.79387 5.66569 8.76372C5.73527 8.73357 5.81023 8.7178 5.88606 8.71736C5.96189 8.71692 6.03703 8.73182 6.10696 8.76116C6.17688 8.7905 6.24015 8.83367 6.29297 8.88809L7.96641 10.4842L11.707 6.91934C11.8164 6.82391 11.9584 6.77446 12.1034 6.78132C12.2484 6.78818 12.3851 6.85082 12.485 6.95614C12.5849 7.06147 12.6402 7.20132 12.6393 7.34646C12.6385 7.49161 12.5816 7.63081 12.4805 7.73496Z"
          />
        </svg>
        <span class="text-xs font-bold uppercase tracking-wider text-teal-500">
          {{ $t('14-day money back guarantee') }}
        </span>
      </div>
    </div>
    <div class="gap-2 flex flex-col">
      <div class="relative overflow-hidden px-2">
        <div class="flex flex-row gap-2 animate-infinite-scroll">
          <!-- First set of reviews -->
          <div v-for="review in displayReviews" :key="`first-${review._id}`" class="flex-shrink-0">
            <img :src="review.image" class="w-[150px] h-[120px] object-cover rounded-md">
          </div>
          <!-- Duplicate set for seamless loop -->
          <div v-for="review in displayReviews" :key="`second-${review._id}`" class="flex-shrink-0">
            <img :src="review.image" class="w-[150px] h-[120px] object-cover rounded-md">
          </div>
        </div>
      </div>
      <div class="max-w-[100%] w-[600px] mx-auto overflow-hidden relative">
        <div class="absolute inset-y-0 left-0 w-16 bg-gradient-to-r from-[#f5f5f5] md:from-white to-transparent z-10" />
        <div class="absolute inset-y-0 right-0 w-16 bg-gradient-to-l from-[#f5f5f5] md:from-white to-transparent z-10" />
        <img
          src="@/assets/img/logo-cloud-horizontal.png"
          class="w-[300%] max-w-[300%] grayscale opacity-60 animate-scroll-slow"
        >
      </div>
    </div>
    <!-- <div class="bg-gray-100 py-2 w-full mt-8">
      <p class="uppercase tracking-wider text-xs text-gray-500 font-bold w-full text-center mb-2">
        As seen on
      </p>
      <div class="max-w-[100%] w-[600px] mx-auto overflow-hidden relative">
        <div class="absolute inset-y-0 left-0 w-16 bg-gradient-to-r from-[#f5f5f5] md:from-white to-transparent z-10" />
        <div class="absolute inset-y-0 right-0 w-16 bg-gradient-to-l from-[#f5f5f5] md:from-white to-transparent z-10" />
        <img
          src="@/assets/img/logo-cloud-horizontal.png"
          class="w-[300%] max-w-[300%] grayscale opacity-60 animate-scroll-slow"
        >
      </div>
    </div> -->

    <!-- Fixed bottom CTA -->
    <transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-class="transform translate-y-full opacity-0"
      enter-to-class="transform translate-y-0 opacity-100"
      leave-active-class="transition-all duration-300 ease-in"
      leave-class="transform translate-y-0 opacity-100"
      leave-to-class="transform translate-y-full opacity-0"
    >
      <div
        v-if="showFixedCTA"
        class="fixed bottom-0 left-0 right-0 z-[200] bg-white border-t border-gray-200 shadow-lg p-4 safe-area-pb"
      >
        <nuxt-link to="/auth/login?redirect=/app" class="w-full">
          <ButtonPrimary class="!bg-[#ff6600] border border-black/30 w-full">
            <span>{{ $t('Create your headshots now') }}</span>
            <IconSmallArrow class="w-5 h-5 text-white" />
          </ButtonPrimary>
        </nuxt-link>
      </div>
    </transition>
  </div>
</template>

<script>
import PosthogMixin from '@/mixins/PosthogMixin'

export default {
  mixins: [PosthogMixin],
  data () {
    return {
      scrollY: 0
    }
  },

  computed: {
    reviews () {
      const reviews = JSON.parse(JSON.stringify(this.$store.state.reviews))

      // Separate reviews by gender
      const maleReviews = reviews.filter(review => review.trigger === 'male')
      const femaleReviews = reviews.filter(review => review.trigger === 'female')

      // Interleave male and female reviews
      const mixedReviews = []
      const maxLength = Math.max(maleReviews.length, femaleReviews.length)
      for (let i = 0; i < maxLength; i++) {
        if (maleReviews[i]) { mixedReviews.push(maleReviews[i]) }
        if (femaleReviews[i]) { mixedReviews.push(femaleReviews[i]) }
      }

      return mixedReviews
    },
    displayReviews () {
      return this.reviews.slice(0, 20)
    },
    showFixedCTA () {
      return this.scrollY > 500
    }
  },

  mounted () {
    window.addEventListener('scroll', this.handleScroll, { passive: true })
  },

  beforeDestroy () {
    window.removeEventListener('scroll', this.handleScroll)
  },

  methods: {
    handleScroll () {
      this.scrollY = window.scrollY
    }
  }

}
</script>

<i18n>
  {
    "en": {
      "2,535 reviews": "2,535 reviews",
      "The #1 AI Headshot Generator for Professional Headshots": "The #1 AI Headshot Generator for Professional Headshots",
      "8x cheaper than a photographer": "8x cheaper than a photographer",
      "Indistinguishable from real photos": "Indistinguishable from real photos",
      "Used by 102,207 happy customers": "Used by 102,207 happy customers",
      "Create your headshots": "Create your headshots",
      "Access your photos": "Access your photos",
      "14-day money back guarantee": "14-day money back guarantee"
    },
    "es": {
      "2,535 reviews": "2.535 reseñas",
      "The #1 AI Headshot Generator for Professional Headshots": "El generador de Fotos Profesionales IA #1",
      "8x cheaper than a photographer": "8x más barato que un fotógrafo",
      "Indistinguishable from real photos": "Indistinguible de fotos reales",
      "Used by 102,207 happy customers": "Usado por 102.207 clientes satisfechos",
      "Create your headshots": "Crea tus fotos",
      "Access your photos": "Accede a tus fotos",
      "14-day money back guarantee": "Garantía de devolución de 14 días",
      "Team Packages": "Paquetes de Equipo",
      "View team packages": "Ver paquetes de equipo",
      "Professional business headshots, without a physical photo shoot": "Fotos profesionales para empresas, sin una sesión física"
    },
    "de": {
      "2,535 reviews": "2.535 Bewertungen",
      "The #1 AI Headshot Generator for Professional Headshots": "Der Nr. 1 KI-Generator für professionelle Bewerbungsfotos",
      "8x cheaper than a photographer": "8 × günstiger als ein Fotograf",
      "Indistinguishable from real photos": "Von echten Fotos nicht zu unterscheiden",
      "Used by 102,207 happy customers": "Von 102.207 zufriedenen Kunden genutzt",
      "Create your headshots": "Bewerbungsfotos erstellen",
      "Access your photos": "Zu deinen Fotos",
      "14-day money back guarantee": "14-tägige Geld-zurück-Garantie",
      "Team Packages": "Team-Pakete",
      "View team packages": "Team-Pakete ansehen",
      "Professional business headshots, without a physical photo shoot": "Bewerbungsfotos vom Profi, ohne Studio-Termin"
    }
  }
</i18n>

<style>
.animate-infinite-scroll {
  animation: scroll-left 80s linear infinite;
}

@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(calc(-158px * 20));
  }
}

/* Pause animation on hover for better UX */
.animate-infinite-scroll:hover {
  animation-play-state: paused;
}

.animate-scroll-slow {
  animation: scroll-horizontal 200s linear infinite;
}

@keyframes scroll-horizontal {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-200%);
  }
}
</style>
