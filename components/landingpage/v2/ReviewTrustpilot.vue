<template>
  <LandingpageV2ReviewItemWrapper>
    <div class="flex items-center gap-3 relative">
      <ImageDns
        v-if="getProfileImageUrl(item.consumer?.links)"
        class="size-6 shrink-0 rounded-full object-cover"
        :src="getProfileImageUrl(item.consumer?.links)"
        alt=""
      />
      <div v-else-if="item.consumer?.displayName" :style="`background: #${getRandomAvatarColor()}`" class="uppercase size-6 shrink-0 rounded-full object-cover font-bold text-xs flex items-center justify-center text-gray-700">
        {{ item.consumer.displayName ? item.consumer.displayName.charAt(0) + item.consumer.displayName.charAt(1) : '' }}
      </div>
      <p class=" font-semibold text-[13px] leading-[18px] tracking-[-0.056px] text-[#111111]">
        {{ item.consumer.displayName }}
      </p>
      <a v-if="clickable" href="https://www.trustpilot.com/review/headshotpro.com" target="_blank" class=" absolute top-1 right-0">
        <img src="@/assets/img/trustpilot-logo.png" class="h-4 w-auto">
      </a>
      <div v-else class="absolute top-1 right-0">
        <img src="@/assets/img/trustpilot-logo.png" class="h-4 w-auto">
      </div>
    </div>

    <ImageDns v-if="item.stars" class="h-4 w-auto" :src="`https://images-static.trustpilot.com/api/stars/${item.stars}/128x24.png`" alt="" />

    <p class="text-sm font-bold leading-5 text-[15px] tracking-[-0.056px] text-[#121212]">
      {{ item.title }}
      <!-- I needed a quick headshot for a project… -->
    </p>
    <p class="text-[13px] font-normal tracking-[-0.056px] text-[#474368]" :style="(capLength) ? 'display: -webkit-box; -webkit-line-clamp: 4; -webkit-box-orient: vertical; overflow: hidden;' : ''">
      {{ item.text }}
      <!-- I needed a quick headshot for a project and didn't have time to go to a pro or to take one at home. I took about 10 minutes placing the order and taking some selfies (wearing a t-shirt). This really works - I selected 19 of the AI generated headshots - any one of which I can use. -->
    </p>
    <p class="text-[12px] text-paragraph">
      Date of experience: {{ formatDate(item.createdAt) }}
    </p>
  </LandingpageV2ReviewItemWrapper>
</template>

<script>
export default {
  props: {
    clickable: {
      type: Boolean,
      required: false,
      default: true
    },
    capLength: {
      type: Boolean,
      required: false,
      default: false
    },
    item: {
      type: Object,
      required: true
    }
  },
  methods: {
    getRandomAvatarColor () {
      const color = ['D1F9EA', 'FFDEBF', 'CCCCCC']
      return color[Math.floor(Math.random() * color.length)]
    },
    getProfileImageUrl (links) {
      if (!links) { return null }
      const profileImageLink = links.find(link => link.rel === 'profile-image')
      return profileImageLink ? profileImageLink.href : null
    }
  }
}
</script>

<style></style>
