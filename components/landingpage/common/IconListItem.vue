<template>
  <div class="flex flex-col gap-1">
    <div class="flex items-center">
      <IconSquare :icon="icon" :color="color" />
      <div class="ml-3">
        <h3 v-if="title" class="font-bold text-[16px] leading-[20px] tracking-[-0.4%] text-primary-500 print:text-primary-500">
          {{ title }}
        </h3>
      </div>
    </div>
    <p v-if="description" class="text-[14px] leading-[20px] text-slate-500 print:text-slate-500 tracking-[-0.4%]">
      {{ description }}
    </p>
  </div>
</template>

<script>
import IconSquare from '@/components/landingpage/common/IconSquare.vue'
export default {
  components: {
    IconSquare
  },
  props: {
    icon: {
      type: String,
      required: true
    },
    color: {
      type: String,
      default: '#73D9D8'
    },
    title: {
      type: String,
      required: true
    },
    description: {
      type: String,
      required: true
    }
  }
}
</script>

<style>

</style>
