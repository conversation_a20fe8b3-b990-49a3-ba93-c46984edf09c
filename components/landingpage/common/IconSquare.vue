<template>
  <div class="w-[28px] h-[28px] flex items-center justify-center rounded-[8px] print:color-adjust-exact" :style="{ backgroundColor: color }">
    <component :is="icon" class="w-4 h-4 text-white print:text-white" />
  </div>
</template>

<script>
export default {
  props: {
    icon: {
      type: String,
      default: ''
    },
    color: {
      type: String,
      default: '#000'
    }
  }

}
</script>

<style>

</style>
