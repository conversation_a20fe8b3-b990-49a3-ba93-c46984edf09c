<template>
  <div
    class="gradient-background rounded-lg shadow-lg overflow-hidden relative group"
    @mouseover="!isMobile && (showControls = true)"
    @mouseleave="!isMobile && (showControls = false)"
  >
    <video
      ref="videoElement"
      :src="src"
      :poster="placeholder"
      class="rounded-lg w-full h-full object-cover block cursor-pointer"
      :autoplay="autoplay && !isMobile"
      loop
      :muted="isMuted"
      playsinline
      :controls="isMobile"
      @loadedmetadata="updateDuration"
      @timeupdate="updateTime"
      @play="isPlaying = true"
      @pause="isPlaying = false"
      @volumechange="isMuted = videoElement ? videoElement.muted : true"
      @click="togglePlay"
    />

    <!-- Custom Controls: Show only on desktop -->
    <div
      v-show="showControls && !isMobile"
      class="absolute bottom-0 left-0 right-0 p-3 bg-gradient-to-t from-black/60 to-transparent transition-opacity duration-300 opacity-0 group-hover:opacity-100 flex items-center space-x-3"
    >
      <!-- Play/Pause Button -->
      <button class="video-control-button flex-shrink-0" @click.stop="togglePlay">
        <svg v-if="isPlaying" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /></svg>
        <svg v-else class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" /></svg>
      </button>

      <!-- Mute/Unmute Button -->
      <button class="video-control-button flex-shrink-0" @click.stop="toggleMute">
        <svg v-if="isMuted" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" clip-rule="evenodd" /></svg>
        <svg v-else class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM14.657 2.929a.75.75 0 011.06 0l.707.707a.75.75 0 010 1.061L15.707 5.414a5.001 5.001 0 010 9.172l.707.707a.75.75 0 11-1.06 1.06l-.707-.707a6.5 6.5 0 000-11.893l.707-.707a.75.75 0 010-1.06z" clip-rule="evenodd" /><path d="M11.75 6.01a.75.75 0 011.06 0l.707.707a.75.75 0 010 1.06L12.8 8.586a2.502 2.502 0 000 2.828l.707.707a.75.75 0 11-1.06 1.06l-.707-.707a4 4 0 010-4.95l.707-.707a.75.75 0 011.06 0z" /></svg>
      </button>

      <!-- Progress Bar Container (for relative positioning of markers) -->
      <div class="flex-grow mx-2 relative flex items-center">
        <input
          type="range"
          min="0"
          :max="duration"
          :value="currentTime"
          class="video-progress-bar w-full"
          @input="seek"
        >
        <!-- Markers -->
        <template v-if="markers && markers.length > 0 && duration > 0">
          <div
            v-for="(marker, index) in markers"
            :key="index"
            class="video-marker-container"
            :style="{ left: `${(marker.time / duration) * 100}%` }"
            @click.stop="seekToMarker(marker.time)"
          >
            <div class="video-marker" />
            <span class="video-marker-label group-hover:opacity-100 group-hover:visible">
              {{ marker.label }}
            </span>
          </div>
        </template>
      </div>

      <!-- Time Display -->
      <span class="text-white text-xs font-mono flex-shrink-0">{{ formatTime(currentTime) }} / {{ formatTime(duration) }}</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    src: {
      type: String,
      required: true
    },
    placeholder: {
      type: String,
      default: ''
    },
    autoplay: {
      type: Boolean,
      default: true
    },
    markers: {
      type: Array,
      default: () => [],
      validator: (value) => {
        return Array.isArray(value) && value.every(m => typeof m.time === 'number' && typeof m.label === 'string')
      }
    }
  },
  data () {
    return {
      videoElement: null, // Reference to the video element
      isMuted: true,
      isPlaying: false,
      duration: 0,
      currentTime: 0,
      showControls: false
    }
  },
  computed: {
    isMobile () {
      if (typeof window !== 'undefined') {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
      }
      return false
    }
  },
  mounted () {
    // Assign the ref to the data property
    this.videoElement = this.$refs.videoElement
    if (this.videoElement) {
      // Initial state sync
      this.isPlaying = !this.videoElement.paused
      this.isMuted = this.videoElement.muted

      // Set initial playing state based on autoplay
      if (!this.isMobile && this.videoElement.autoplay) {
        this.videoElement.play().catch(() => {
          this.isPlaying = false // Autoplay might be blocked
        })
      }
    }
  },
  methods: {
    togglePlay () {
      if (!this.videoElement) { return }
      if (this.videoElement.paused) {
        this.videoElement.play()
      } else {
        this.videoElement.pause()
      }
    },
    toggleMute () {
      if (!this.videoElement) { return }
      this.videoElement.muted = !this.videoElement.muted
    },
    updateDuration () {
      if (!this.videoElement) { return }
      this.duration = this.videoElement.duration
    },
    updateTime () {
      if (!this.videoElement) { return }
      this.currentTime = this.videoElement.currentTime
    },
    seek (event) {
      if (!this.videoElement) { return }
      const time = parseFloat(event.target.value)
      this.videoElement.currentTime = time
      this.currentTime = time // Update reactive property immediately
    },
    seekToMarker (time) {
      if (!this.videoElement) { return }
      this.videoElement.currentTime = time
      this.currentTime = time // Update reactive property immediately
      if (this.videoElement.paused) {
        this.videoElement.play()
      }
    },
    formatTime (timeInSeconds) {
      if (isNaN(timeInSeconds) || timeInSeconds === Infinity || timeInSeconds < 0) {
        return '0:00'
      }
      const minutes = Math.floor(timeInSeconds / 60)
      const seconds = Math.floor(timeInSeconds % 60).toString().padStart(2, '0')
      return `${minutes}:${seconds}`
    }
  }
}
</script>

<style scoped>
.gradient-background {
  /* Equivalent to bg-gradient-to-br from-[#75DBD8] to-[#61B7EB] bg-opacity-30 */
  background-image: linear-gradient(to bottom right, rgba(117, 219, 216, 0.3), rgba(97, 183, 235, 0.3));
}

.video-control-button {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px; /* Adjust size as needed */
  height: 32px; /* Adjust size as needed */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.video-control-button:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

/* Custom styling for the range input */
.video-progress-bar {
  -webkit-appearance: none; /* Override default look */
  appearance: none;
  width: 100%; /* Full width */
  height: 6px; /* Progress bar height */
  background: rgba(255, 255, 255, 0.3); /* Track color */
  border-radius: 5px;
  cursor: pointer;
  outline: none;
  position: relative;
  z-index: 10;
}

/* Style for the thumb (the draggable part) in WebKit browsers (Chrome, Safari) */
.video-progress-bar::-webkit-slider-thumb {
  -webkit-appearance: none; /* Override default look */
  appearance: none;
  width: 14px; /* Thumb width */
  height: 14px; /* Thumb height */
  background: #fff; /* Thumb color */
  border-radius: 50%;
  cursor: pointer;
}

/* Style for the thumb in Firefox */
.video-progress-bar::-moz-range-thumb {
  width: 14px; /* Thumb width */
  height: 14px; /* Thumb height */
  background: #fff; /* Thumb color */
  border-radius: 50%;
  cursor: pointer;
  border: none; /* Remove default border */
}

/* Style for the track in Firefox */
.video-progress-bar::-moz-range-track {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 5px;
  cursor: pointer;
}

.video-marker-container {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%); /* Center the marker visually on the line */
  z-index: 20; /* Ensure markers are above the progress bar */
  cursor: pointer;
  display: flex; /* Use flex to position label relative to marker dot */
  flex-direction: column;
  align-items: center;
}

.video-marker {
  width: 10px; /* Size of the marker dot */
  height: 10px;
  background-color: white; /* Marker color */
  border-radius: 50%;
  border: 1px solid rgba(0,0,0,0.5); /* Optional border */
  transition: transform 0.2s ease;
}

.video-marker-container:hover .video-marker {
  transform: scale(1.3); /* Slightly enlarge marker on hover */
}

.video-marker-label {
  position: absolute;
  bottom: 15px; /* Position label above the marker */
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  z-index: 30; /* Ensure label is above everything */
  pointer-events: none; /* Prevent label from interfering with clicks */
}

/* Show label on hover of the marker container */
/* Using group-hover/marker requires Tailwind v3.1+ */
/* If using older Tailwind, you might need custom CSS or JS for hover */

/* Ensure container div for progress bar takes full width */
.flex-grow {
  flex-grow: 1;
}
</style>
