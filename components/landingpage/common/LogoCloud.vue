<template>
  <div>
    <div class="max-w-[100%] w-full mx-auto overflow-hidden relative">
      <div class="absolute inset-y-0 left-0 w-16 bg-gradient-to-r from-[#f5f5f5] md:from-white to-transparent z-10" />
      <div class="absolute inset-y-0 right-0 w-16 bg-gradient-to-l from-[#f5f5f5] md:from-white to-transparent z-10" />
      <img
        src="@/assets/img/logo-cloud-horizontal.png"
        class="w-[140%] max-w-[140%] grayscale opacity-60 animate-scroll"
      >
    </div>
  </div>
</template>

<script setup>
// If logos become dynamic, define props here
// e.g., const props = defineProps({ logos: Array });
</script>

<style scoped>
/* Add any component-specific styles here if needed */
</style>
