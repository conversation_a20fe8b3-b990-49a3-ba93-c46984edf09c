<template>
  <p v-if="size === 'lg'" class="text-[16px] md:text-[18px] leading-[24px] md:leading-[28px] text-slate-500">
    <slot />
  </p>
  <p v-else-if="size === 'md'" class="text-[15px] md:text-[16px] leading-[22px] md:leading-[24px] text-slate-500">
    <slot />
  </p>
  <p v-else-if="size === 'sm'" class="text-[14px] md:text-[15px] leading-[20px] md:leading-[22px] text-slate-500">
    <slot />
  </p>
  <p v-else-if="size === 'xs'" class="text-[13px] md:text-[14px] leading-[18px] md:leading-[20px] text-slate-500">
    <slot />
  </p>
  <p v-else-if="size === 'xxs'" class="text-[11px] md:text-[11px] leading-[14px] md:leading-[14px] text-slate-500">
    <slot />
  </p>
  <p v-else class="text-[12px] md:text-[14px] leading-[16px] md:leading-[20px] text-slate-500 print:text-slate-500">
    <slot />
  </p>
</template>

<script>
export default {
  props: {
    size: {
      type: String,
      default: 'lg'
    }
  }
}
</script>

<style>

</style>
