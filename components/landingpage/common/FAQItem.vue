<template>
  <div class="shadow-sm cursor-pointer hover:scale-105 transition-all duration-200 border bg-white rounded-md border-black/10 py-[12px] px-[16px]" @click="toggleOpen">
    <div
      class="flex cursor-pointer items-center justify-between"
    >
      <H6 class="!font-normal !text-paragraphlp">
        {{ question }}
      </H6>
      <Paragraph>
        {{ isOpen ? '-' : '+' }}
      </Paragraph>
    </div>
    <hr v-if="isOpen" class="my-[12px] border-black/10">
    <div v-if="isOpen" class="mt-2 text-gray-600">
      <Paragraph size="small" class="!tracking-[0.01em]">
        {{ answer }}
      </Paragraph>
    </div>
  </div>
</template>

<script>
import H6 from '@/components/landingpage/common/H6.vue'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
export default {
  components: {
    H6,
    Paragraph
  },
  props: {
    question: {
      type: String,
      required: true
    },
    answer: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      isOpen: false
    }
  },
  methods: {
    toggleOpen () {
      this.isOpen = !this.isOpen
    }
  }
}
</script>

<style scoped>
/* Add any additional component-specific styles here if needed */
</style>
