<template>
  <Card>
    <div class="flex flex-col space-y-[8px]">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div class="flex h-[32px] w-[32px] items-center justify-center rounded-full bg-sky-400 text-white">
            <span class="text-sm font-medium leading-none">{{ initial }}</span>
          </div>
          <Paragraph size="small">
            {{ name }}
          </Paragraph>
        </div>
        <div class="flex items-center space-x-1">
          <img src="@/assets/img/trustpilot-logo.png" class="h-5 w-auto">
        </div>
      </div>
      <div class="mt-4 flex items-center">
        <ImageDns class="h-4 w-auto" src="https://images-static.trustpilot.com/api/stars/5/128x24.png" alt="" />
      </div>
      <H6>
        {{ reviewTitle }}
      </H6>
      <Paragraph size="tiny">
        {{ reviewText }}
      </Paragraph>
    </div>
  </Card>
</template>

<script>
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
import H6 from '@/components/landingpage/common/H6.vue'
export default {
  components: {
    Paragraph,
    H6
  },
  props: {
    initial: {
      type: String,
      required: true
    },
    name: {
      type: String,
      required: true
    },
    reviewTitle: {
      type: String,
      required: true
    },
    reviewText: {
      type: String,
      required: true
    }
  }
}
</script>
