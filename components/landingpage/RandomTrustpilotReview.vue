<template>
  <div v-if="item && item?.id">
    <a :href="`https://www.trustpilot.com/reviews/${item.id}`" target="_blank" rel="nofollow" class="block">
      <div class="max-w-[400px] bg-slate-800/5  p-4 text-left rounded-md space-y-2 opacity-70 hover:opacity-100 transition-opacity duration-300">
        <div class="flex items-center gap-3 relative">
          <ImageDns
            v-if="getProfileImageUrl(item.consumer?.links)"
            class="size-6 shrink-0 rounded-full object-cover"
            :src="getProfileImageUrl(item.consumer?.links)"
            alt=""
          />

          <div v-else-if="item.consumer?.displayName" :style="`background: #${getRandomAvatarColor()}`" class="uppercase size-6 shrink-0 rounded-full object-cover font-bold text-xs flex items-center justify-center text-gray-700">
            {{ item.consumer.displayName ? item.consumer.displayName.charAt(0) + item.consumer.displayName.charAt(1) : '' }}
          </div>
          <p class=" font-semibold text-[13px] leading-[18px] tracking-[-0.056px] text-[#111111]">
            {{ item.consumer.displayName }}
          </p>
          <a href="https://www.trustpilot.com/review/headshotpro.com" target="_blank" rel="nofollow" class=" absolute top-1 right-0">
            <img src="@/assets/img/trustpilot-logo.png" class="h-4 w-auto">
          </a>
        </div>

        <ImageDns v-if="item.stars" class="h-4 w-auto" :src="`https://images-static.trustpilot.com/api/stars/${item.stars}/128x24.png`" alt="" />

        <p class="text-sm font-bold leading-5 text-[15px] tracking-[-0.056px] text-[#121212]">
          {{ item.title }}
          <!-- I needed a quick headshot for a project… -->
        </p>
        <p class="text-[13px] font-normal tracking-[-0.056px] text-[#474368]" style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden">
          {{ item.text }}
          <!-- I needed a quick headshot for a project and didn't have time to go to a pro or to take one at home. I took about 10 minutes placing the order and taking some selfies (wearing a t-shirt). This really works - I selected 19 of the AI generated headshots - any one of which I can use. -->
        </p>
        <p class="text-[12px] text-paragraph">
          Date of experience: {{ formatDate(item.createdAt) }}
        </p>
      </div>
    </a>
  </div>
</template>

<script>
export default {
  computed: {
    trustPilotReviews () {
      return this.$store.state.trustpilotReviews
    },
    item () {
      const twoWeeksAgo = new Date()
      twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14)

      const recentReviews = this.trustPilotReviews.filter(review =>
        new Date(review.createdAt) >= twoWeeksAgo
      )
      const longReviews = recentReviews.filter(review => review.text.length > 100)
      const fiveStarReviews = longReviews.filter(review => review.stars === 5)
      return fiveStarReviews[Math.floor(Math.random() * fiveStarReviews.length)]
    }
  },
  methods: {
    getRandomAvatarColor () {
      const color = ['D1F9EA', 'FFDEBF', 'CCCCCC']
      return color[Math.floor(Math.random() * color.length)]
    },
    getProfileImageUrl (links) {
      if (!links) { return null }
      const profileImageLink = links.find(link => link.rel === 'profile-image')
      return profileImageLink ? profileImageLink.href : null
    }
  }
}
</script>
