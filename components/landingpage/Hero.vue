<template>
  <div>
    <section class="bg-white">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 md:pt-[56px]">
        <div class="flex flex-col lg:flex-row lg:items-start">
          <div class="mx-auto text-left max-w-lg shrink-0 space-y-4 pb-4 sm:pb-12 xl:pb-8 md:text-center lg:mx-0 lg:text-left xl:max-w-[620px]  pt-[24px]">
            <h1 class="text-2xl leading-[1.6rem] font-medium text-primary-500 sm:text-4xl sm:leading-[3rem] lg:leading-[38px] lg:text-[36px] xl:leading-[48px] xl:text-[42px] tracking-[-1.5px]">
              {{ title }}
            </h1>
            <h2 class="text-sm font-medium text-gray-800 sm:text-lg">
              {{ subtitle }}
            <!-- Get professional headshots in minutes with our AI-photographer -->
            </h2>
            <p id="convert-3-v1-p" class="text-sm leading-[1.4rem] md:text-sm font-normal md:leading-[1.6rem] text-gray-600 pb-4">
              {{ excerpt }}
            </p>
            <div class="relative space-y-5">
              <div class="flex items-center justify-center gap-3 lg:justify-start">
                <div class="items-center space-x-2 flex">
                  <span class="text-xs text-gray-400  font-medium flex uppercase">Trusted by: </span>
                  <template v-for="index of 4">
                    <img
                      :key="index"
                      alt="Logo"
                      class="w-[15vw] h-auto md:w-[100px] md:h-[32px]"
                      :src="require('@/assets/img/cloud/logo-' + index + '.png')"
                    >
                  </template>
                </div>
              </div>
              <div class="flex items-start md:items-center justify-start flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
                <nuxt-link to="/app/add" class="w-full md:w-auto">
                  <ButtonOrange class="w-full md:w-auto">
                    <span class="flex-shrink-0">Get your headshots now</span>
                    <IconSmallArrow class="flex-shrink-0 w-5 h-5 text-white ml-1.5" />
                  </ButtonOrange>
                </nuxt-link>
                <div class="flex flex-row space-x-4">
                  <div class="hidden xl:flex -space-x-3 overflow-hidden">
                    <img class="inline-block h-8 w-8 rounded-full ring-[3px] ring-white" :src="require('@/assets/img/avatar-01.png')" alt="">
                    <img class="inline-block h-8 w-8 rounded-full ring-[3px] ring-white" :src="require('@/assets/img/avatar-02.png')" alt="">
                    <img class="inline-block h-8 w-8 rounded-full ring-[3px] ring-white" :src="require('@/assets/img/avatar-05.png')" alt="">
                  </div>
                  <div class="flex flex-row lg:flex-col items-start justify-start space-x-4 md:space-x-0 md:space-y-0.5">
                    <div class="flex">
                      <IconSolidStar v-for="index in 5" :key="index" class="text-yellow-400 w-4 h-4" />
                    </div>
                    <span class="text-gray-600 text-xs">Used by {{ $store.state.stats.users }} happy customers.</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="lg:pl-8">
            <img
              fetchpriority="high"
              width="343"
              height="274"
              class="relative  mt-4 md:mt-0 w-full"
              :src="require('@/assets/img/teams/hero-image.png')"
              alt="Headshot Examples"
            >
          </div>
        </div>
      </div>
    </section>
    <div class="hidden md:block relative">
      <div class="hidden sm:grid absolute inset-0" aria-hidden="true">
        <!-- <div class="bg-transparent" /> -->
        <div class="bg-teal-100" />
      </div>

      <div class=" mx-auto max-w-5xl xl:max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="isolate mx-auto max-w-5xl rounded-lg border-2 border-primary-500 bg-white px-6 py-5 shadow-lg sm:px-8 top-[-30px] relative z-20">
          <div class="flex flex-col gap-6 lg:flex-row lg:items-end">
            <img class="w-full" :src="require('@/assets/img/steps-hero.png')">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: { type: String, required: true },
    subtitle: { type: String, required: true },
    excerpt: { type: String, required: true }
  }

}
</script>

<style>

</style>
