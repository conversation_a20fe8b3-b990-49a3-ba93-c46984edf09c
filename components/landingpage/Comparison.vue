<template>
  <section id="styles" class="py-12  sm:py-16 xl:py-12 lg:py-12">
    <div class="px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl">
      <div class="max-w-3xl mx-auto text-center">
        <h2 class="mt-6 text-2xl font-bold  tracking-tight text-primary-500 sm:text-4xl">
          We take our quality very seriously
        </h2>
        <p class="mt-4 text-base font-normal text-gray-600">
          With the same uploaded photos, HeadshotPro's AI headshot generator performs far better than alternatives. Why? Because we use our own in-house developed, cutting edge AI technology. See the difference.
        </p>
        <div class="hidden md:flex w-full items-center justify-center pt-12 pb-4">
          <ImageDns class="w-[300px]" width="300" :src="require('@/assets/img/comparison/before.png')" />
        </div>
      </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto mt-8 md:mt-0 px-4 md:px-0">
      <div class="space-y-8">
        <div class="flex flex-col justify-center items-center space-y-3">
          <img width="181" height="28" class="h-6 w-auto" src="@/assets/img/logo.svg" alt="HeadshotPro logo">
          <ImageDns :src="require('@/assets/img/comparison/us.png')" class="w-full object-cover" />
        </div>
        <div>
          <ul class="grid grid-cols-2 gap-3 md:gap-4">
            <ListCheckSolid>Indistinguishable from real</ListCheckSolid>
            <ListCheckSolid>14 days money back guaranteed</ListCheckSolid>
            <ListCheckSolid>Choose clothing and location</ListCheckSolid>
            <ListCheckSolid>High resemblance</ListCheckSolid>
            <ListCheckSolid>Clear and sharp</ListCheckSolid>
            <ListCheckSolid>Matching poses</ListCheckSolid>
          </ul>
        </div>
      </div>

      <div class="space-y-8">
        <div class="flex flex-col justify-center items-center space-y-3">
          <span class="font-bold text-[#AE051A] text-[17px]">Known alternatives</span>
          <ImageDns :src="require('@/assets/img/comparison/them.jpg')" class="w-full object-cover" />
        </div>
        <div>
          <ul class="grid grid-cols-2 gap-3 md:gap-4 opacity-[0.7]">
            <ListCrossSolid>Obvious AI generated</ListCrossSolid>
            <ListCrossSolid>No refund after usage</ListCrossSolid>
            <ListCrossSolid>Random clothing and location</ListCrossSolid>
            <ListCrossSolid>Deformed faces</ListCrossSolid>
            <ListCrossSolid>Unsharp and blurry</ListCrossSolid>
            <ListCrossSolid>Random poses</ListCrossSolid>
          </ul>
        </div>
      </div>
    </div>
    <!-- <div class="mt-16 w-full mx-auto max-w-3xl bg-[#E8F6E5] rounded-md p-3 px-6 flex flex-col md:flex-row items-center justify-between space-y-2 md:space-y-0">
      <div class="flex items-center justify-center md:justify-start">
        <IconSolidCheckBadge class="text-[#17A400] w-5 h-5 mr-1.5 hidden md:inline-flex" />
        <p class="font-medium text-center md:text-left text-green-900">
          Try risk free now with our <nuxt-link to="/refund" target="_blank" class="underline">
            ‘Profile-Worthy’ Money Back Guarantee
          </nuxt-link>
        </p>
      </div>
      <nuxt-link to="/auth/login?redirect=%2Fapp%2Fadd">
        <ButtonPrimary size="sm">
          <span class="font-medium">Get your headshots</span>
          <IconChevron class="w-4 h-4 ml-1.5" />
        </ButtonPrimary>
      </nuxt-link>
    </div> -->
  </section>
</template>

<script>
export default {
  // props: {
  //   styles: {
  //     type: Array,
  //     default: () => []
  //   },
  //   clothing: {
  //     type: Array,
  //     default: () => []
  //   }
  // }
  computed: {
    styles () {
      return this.$store.state.styles
    },
    clothing () {
      return this.$store.state.clothing
    }
  }

}
</script>

<style>

</style>
