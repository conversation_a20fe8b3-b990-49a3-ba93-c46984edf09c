<template>
  <section class="overflow-hidden bg-[#F8FCFF]">
    <div class="mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8 2xl:px-0">
      <div class="flex flex-col gap-2 xl:flex-row xl:items-end">
        <div class="pt-8 sm:pt-12 xl:max-w-2xl xl:py-20">
          <p class="text-xs font-bold text-[#474368] md:hidden">
            The #1 AI Headshot Generator for Professional Headshots
          </p>
          <h1 class="mt-3 text-2xl font-bold leading-tight tracking-tighter text-primary-500 sm:text-4xl md:mt-0 xl:text-[42px]">
            {{ title }}
          </h1>
          <p class="mt-4 text-base font-medium text-paragraph sm:text-lg md:text-[#474368]">
            {{ subtitle }}
          </p>

          <ul class="mt-3 space-y-2.5 text-sm font-normal tracking-[-0.056px] text-paragraph sm:mt-7 sm:text-base">
            <li v-for="usp in usps" :key="usp" class="hidden md:flex md:items-center md:gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="-mb-0.5 h-5 w-5 shrink-0 text-[#00B67A]">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              {{ usp }}
            </li>
          </ul>

          <div class="mt-7 flex flex-col sm:flex-row sm:gap-6">
            <nuxt-link
              to="/"
              title=""
              class="inline-flex h-12 w-full items-center justify-center gap-1.5 rounded-lg border border-primary-600 bg-primary-500 px-12 pb-3.5 pt-2.5 text-lg font-bold leading-6 text-white shadow-[0_0px_24px_0px_rgba(0,0,0,0.25)] transition-all duration-150 hover:bg-opacity-90 disabled:bg-opacity-20 sm:w-auto"
              role="button"
            >
              <span class="md:hidden">Get your headshots now</span>
              <svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="-mb-1 size-5 md:hidden">
                <path fill-rule="evenodd" d="M3 10a.75.75 0 0 1 .75-.75h10.638L10.23 5.29a.75.75 0 1 1 1.04-1.08l5.5 5.25a.75.75 0 0 1 0 1.08l-5.5 5.25a.75.75 0 1 1-1.04-1.08l4.158-3.96H3.75A.75.75 0 0 1 3 10Z" clip-rule="evenodd" />
              </svg>
              <span class="hidden md:block">Generate your AI headshots</span>
            </nuxt-link>

            <nuxt-link
              to="/reviews"
              title=""
              class="mt-4 hidden h-12 w-full items-center justify-center gap-1.5 rounded-lg border border-transparent bg-gray-200 px-4 pb-3.5 pt-2.5 text-base font-semibold text-gray-600 shadow-sm transition-all duration-150 hover:bg-opacity-90 disabled:bg-opacity-20 sm:mt-0 sm:inline-flex sm:w-auto"
              role="button"
            >
              See more examples
            </nuxt-link>
          </div>

          <div class="mt-8 flex flex-col items-center gap-4 sm:mt-12 sm:flex-row sm:gap-6">
            <div class="flex items-center gap-3">
              <img class="h-6 w-auto" src="@/assets/img/trustpilot-stars-4.5.svg" alt="">
              <img class="h-6 w-auto" src="@/assets/img/landing-page/logo-trustpilot.png" alt="">
            </div>

            <div class="flex items-center gap-2">
              <div class="flex -space-x-2 overflow-hidden">
                <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="@/assets/img/landing-page/avatar-1.jpg" alt="">
                <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="@/assets/img/landing-page/avatar-2.jpg" alt="">
                <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="@/assets/img/landing-page/avatar-3.jpg" alt="">
              </div>
              <p class="-mt-0.5 text-sm font-normal text-[#474368]">
                <span class="font-bold">{{ $store.state.stats.users }}+</span>
                happy customers
              </p>
            </div>
          </div>

          <!-- <div class="flex items-center justify-center mt-4 sm:justify-start md:hidden gap-x-5 sm:mt-8">
                <img class="h-3 sm:h-5" src="images/logo-cnn.svg">
                <img class="h-2.5 sm:h-3.5" src="images/logo-new-york-post.svg">
                <img class="h-3 sm:h-5" src="images/logo-vice.svg">
                <img class="h-3 sm:h-5" src="images/logo-bloomberg.svg">
                <img class="h-3 sm:h-5" src="images/logo-fashionista.svg">
              </div> -->

          <div class="-mb-6 mt-8 sm:mt-16 md:px-8 xl:hidden">
            <img class="w-full" src="@/assets/img/headshot-type-hero-img.png" alt="">
          </div>
        </div>

        <div class="hidden flex-col items-end justify-end xl:flex max-w-[50%]">
          <img class="w-full" src="@/assets/img/headshot-type-hero-img.png" alt="">
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      required: true
    },
    subtitle: {
      type: String,
      required: true
    },
    usps: {
      type: Array,
      required: true,
      default: () => [
        'Indistinguishable from real photos',
        'Business expense-ready invoice',
        'Done in 2 hours or less, 8x cheaper than a physical shoot'
      ]
    }
  }
}
</script>

<style></style>
