<template>
  <section id="how-it-works" class="bg-white py-12 sm:py-16 lg:py-20 xl:py-24">
    <div class="mx-auto max-w-screen-xl px-4 sm:px-6 lg:px-8 2xl:px-0">
      <div class="text-left md:text-center">
        <div class="flex items-center justify-start gap-x-3 md:justify-center">
          <img class="h-4 w-auto" src="@/assets/img/trustpilot-stars-4.5.svg" alt="">
          <img class="h-4 w-auto" src="@/assets/img/landing-page/logo-trustpilot.png" alt="">
        </div>
        <h2 class="mt-3 text-2xl font-bold tracking-[-1.05px] text-primary-500 sm:text-3xl lg:text-[42px] lg:leading-[48px]">
          {{ title }}
        </h2>
        <p class="mt-3 text-base font-medium text-[#474368] sm:text-lg md:mx-auto md:max-w-2xl lg:text-xl">
          {{ subtitle }}
        </p>
      </div>

      <div class="mt-8 gap-6 sm:mt-12 md:flex md:justify-center">
        <div class="w-full rounded-lg border border-primary-500/15 bg-white p-8 shadow-[0px_0px_75px_0px_rgba(0,0,0,0.07)] lg:max-w-lg">
          <img class="w-full rounded-lg ring-1 ring-gray-200" src="@/assets/img/landing-page/with-headshotpro.png" alt="A calendar showing HeadshotPro only takes 10 minutes to generate AI headshots">

          <div class="mt-6 flex items-center gap-2">
            <p class="text-xl font-bold text-primary-500">
              {{ $t('With') }}
            </p>
            <img class="-mb-0.5 h-6 w-auto" src="@/assets/img/logo.svg" alt="">
          </div>

          <ul class="mt-6 space-y-8">
            <li class="flex items-start gap-4">
              <img class="hidden size-8 shrink-0 sm:block" src="@/assets/img/landing-page/icon-profile.svg" alt="">
              <div class="min-w-0 flex-1">
                <p class="text-lg font-bold leading-none tracking-tighter text-primary-500">
                  {{ $t('1. Upload your photos') }}
                  <span class="font-normal">{{ $t('(5 minutes)') }}</span>
                </p>
                <p class="mt-1 text-base font-normal tracking-[-0.056px] text-paragraph">
                  {{ $t('Use your favorite existing photos or take fresh selfies on the spot.') }}
                </p>
              </div>
            </li>

            <li class="flex items-start gap-4">
              <img class="hidden size-8 shrink-0 sm:block" src="@/assets/img/landing-page/icon-magic.svg" alt="">
              <div class="min-w-0 flex-1">
                <p class="text-lg font-bold leading-none tracking-[-0.056px] text-primary-500">
                  {{ $t('2. Let our AI work its magic') }}
                  <span class="font-normal">{{ $t('(1-2 hours)') }}</span>
                </p>
                <p class="mt-1 text-base font-normal tracking-[-0.056px] text-paragraph">
                  {{ $t('Our AI will pull your most photogenic qualities from the photos you uploaded.') }}
                </p>
              </div>
            </li>

            <li class="flex items-start gap-4">
              <img class="hidden size-8 shrink-0 sm:block" src="@/assets/img/landing-page/icon-user.svg" alt="">
              <div class="min-w-0 flex-1">
                <p class="text-lg font-bold leading-none tracking-[-0.056px] text-primary-500">
                  {{ $t('3. Download your favorites') }}
                  <span class="font-normal">{{ $t('(5 minutes)') }}</span>
                </p>
                <p class="mt-1 text-base font-normal tracking-[-0.056px] text-paragraph">
                  {{ $t('That was easy! Download your keepers and enjoy your new professional photos.') }}
                </p>
              </div>
            </li>
          </ul>
        </div>

        <div class="relative hidden min-h-full w-px shrink-0 bg-[#92A0B5] bg-opacity-45 md:block">
          <span class="absolute left-1/2 top-1/2 w-5 -translate-x-1/2 -translate-y-1/2 bg-[#F5F5F5] py-2.5 text-center text-sm font-medium tracking-[-0.3px] text-slate-400">vs</span>
        </div>

        <div class="mt-4 w-full rounded-lg border border-primary-500/15 bg-white p-8 shadow-[0px_0px_75px_0px_rgba(0,0,0,0.07)] md:mt-0 lg:max-w-lg">
          <img class="w-full rounded-lg ring-1 ring-gray-200" src="@/assets/img/landing-page/physical-photoshoot.png" alt="A calendar showing a physical photoshoot can take days before you get your headshots back">

          <p class="mt-5 text-xl font-bold text-primary-500">
            {{ $t('Physical photoshoot') }}
          </p>

          <ul class="mt-4 space-y-3 text-base font-normal tracking-[-0.056px] text-paragraph">
            <li class="flex items-center gap-2.5">
              <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
              </svg>
              {{ $t('Find a photographer you like') }}
            </li>

            <li class="flex items-center gap-2.5">
              <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
              </svg>
              {{ $t('Contact them and wait for a reply') }}
            </li>

            <li class="flex items-center gap-2.5">
              <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
              </svg>
              {{ $t('Decide on a date and time you’re both available') }}
            </li>

            <li class="flex items-center gap-2.5">
              <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
              </svg>
              {{ $t('Find the right clothing to wear to the photo shoot') }}
            </li>

            <li class="flex items-center gap-2.5">
              <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
              </svg>
              {{ $t('Get in your car and drive to the photo studio') }}
            </li>

            <li class="flex items-center gap-2.5">
              <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
              </svg>
              {{ $t('Pose for your photos and pick your favorite shots') }}
            </li>

            <li class="flex items-center gap-2.5">
              <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
              </svg>
              {{ $t('Wait for the photographer to send you the photos') }}
            </li>

            <li class="flex items-center gap-2.5">
              <svg class="-mb-0.5 size-5 shrink-0 text-paragraph" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
              </svg>
              {{ $t('Update your professional profiles with your new photos') }}
            </li>
          </ul>
        </div>
      </div>

      <div class="relative mt-8 text-center sm:mt-12">
        <nuxt-link
          :to="`/auth/login?redirect=${encodeURIComponent(localePath('/app'))}`"
          title=""
          class="inline-flex h-12 w-full items-center justify-center gap-1.5 rounded-lg border border-primary-600 bg-primary-500 px-6 pb-3.5 pt-2.5 text-lg font-bold leading-6 text-white shadow-[0_0px_24px_0px_rgba(0,0,0,0.25)] transition-all duration-150 hover:bg-opacity-90 disabled:bg-opacity-20 sm:w-auto"
          role="button"
        >
          {{ $t('Create your AI headshots within 2 hours') }}
        </nuxt-link>

        <blockquote class="mx-auto mt-6 max-w-md">
          <img class="mx-auto h-4 w-auto" src="@/assets/img/landing-page/ratings-5.png" alt="">
          <p class="mt-3 text-sm font-medium italic tracking-[-0.2px] text-paragraph">
            “{{ $t('I updated my Linkedin profile image with this, cheaper than a studio pro with quality level better than home pro.') }}”
          </p>
          <div class="mt-3 flex items-center justify-center gap-2">
            <img class="size-8 rounded-full object-cover" src="@/assets/img/landing-page/avatar-13.jpg" alt="">
            <p class="text-sm font-bold tracking-[-0.2px] text-paragraph">
              Mark
            </p>
          </div>
        </blockquote>

        <div class="absolute left-auto top-0 hidden gap-4 lg:flex lg:translate-x-8 lg:items-center xl:translate-x-40">
          <p class="rotate-[-12deg] py-4 text-right font-cursive text-base leading-4 tracking-[-0.056px] text-paragraph">
            {{ $t('Same day results!') }}
          </p>
          <svg class="-mt-12 h-12 w-auto text-paragraph" viewBox="0 0 56 51" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M47.1541 44.8739C44.8708 46.3571 42.6765 47.8339 40.156 49.466C41.3327 50.3929 42.7167 50.2496 43.8436 49.641C47.2242 47.8151 50.6146 45.9006 53.8566 43.7329C55.7149 42.5001 55.6452 40.7462 53.9247 39.9462C51.0771 38.5676 48.1207 37.3728 45.1742 36.0892C45.0061 36.0133 44.7985 35.8963 44.6601 35.9502C44.1164 36.0772 43.6024 36.334 43.0488 36.5497C43.1775 36.9802 43.1084 37.6008 43.3952 37.8C44.3742 38.5213 45.4124 39.1064 46.4012 39.7389C46.9352 40.0964 47.479 40.3651 47.8648 41.2609C46.8367 41.3787 45.7591 41.544 44.731 41.6618C23.5551 43.1825 6.96224 30.8738 3.20946 11.047C2.74404 8.4859 2.59496 5.85814 2.26794 3.24315C2.14903 2.32823 1.98071 1.46082 1.8618 0.545898C1.6443 0.517543 1.41693 0.577842 1.19943 0.549487C0.873343 1.1006 0.319895 1.71201 0.300293 2.28508C0.132791 4.18802 -0.0347098 6.09095 0.0647132 7.97471C0.979821 26.7301 14.2812 41.2539 33.3427 44.0627C36.9909 44.5923 40.8465 44.4473 44.5836 44.5747C45.3745 44.6059 46.1357 44.5072 46.9365 44.4497C47.065 44.4845 47.1342 44.6554 47.1541 44.8739Z"
            />
          </svg>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: 'How to Create Headshots with HeadshotPro'
    },
    subtitle: {
      type: String,
      default: 'Save hundreds of dollars and hours of time by using HeadshotPro to generate your new favorite professional headshots.'
    }
  }

}
</script>

<style>

</style>

<i18n>
  {
    "es": {
      "How Your Selfies Become Professional Headshots": "Convertimos tus selfies en fotos profesionales",
      "Save hundreds of dollars and hours of time by using HeadshotPro to generate your new favorite professional headshots.": "Ahorra dinero y tiempo utilizando HeadshotPro para generar tus nuevas fotos profesionales favoritas.",
      "With": "Con",
      "1. Upload your photos": "1. Sube tus fotos",
      "(5 minutes)": "(5 minutos)",
      "Use your favorite existing photos or take fresh selfies on the spot.": "Utiliza tus fotos favoritas existentes o sácate selfies en el momento.",
      "2. Let our AI work its magic": "2. Deja que nuestra IA haga su magia",
      "(1-2 hours)": "(1-2 horas)",
      "Our AI will pull your most photogenic qualities from the photos you uploaded.": "Nuestra IA extraerá tus cualidades más fotogénicas de las fotos que subiste.",
      "3. Download your favorites": "3. Descarga tus favoritas",
      "That was easy! Download your keepers and enjoy your new professional photos.": "¡Fácil! Descarga tus favoritas y disfruta de tus nuevas fotos profesionales.",
      "Physical photoshoot": "Sesión de fotos física",
      "Find a photographer you like": "Encontrar un fotógrafo que te guste",
      "Contact them and wait for a reply": "Contactarle y esperar a que responda",
      "Decide on a date and time you’re both available": "Decidir una fecha y hora en la que ambos estéis disponibles",
      "Find the right clothing to wear to the photo shoot": "Encontrar la ropa adecuada para llevar a la sesión de fotos",
      "Get in your car and drive to the photo studio": "Subirte al coche y conducir al estudio de fotos",
      "Pose for your photos and pick your favorite shots": "Posar para tus fotos y elegir tus tomas favoritas",
      "Wait for the photographer to send you the photos": "Esperar a que el fotógrafo te envíe las fotos",
      "Update your professional profiles with your new photos": "Actualizar tus perfiles profesionales con tus nuevas fotos",
      "Create your AI headshots within 2 hours": "Obtén tus fotos con IA en 2 horas",
      "I updated my Linkedin profile image with this, cheaper than a studio pro with quality level better than home pro.": "Actualicé mi imagen de perfil de Linkedin con esto, más barato que un profesional de estudio con un nivel de calidad mejor que un profesional de casa.",
      "Same day results!": "¡Resultados en el mismo día!",
      "Professional headshots for 8x less than a physical photo shoot": "Fotos profesionales por 8 veces menos que una sesión de fotos física",
      "The average cost of professional headshots in the United States is": "El costo promedio de las fotos profesionales en los Estados Unidos es",
      "Our packages start from $29.": "Nuestros paquetes comienzan desde $29.",
      "We won't let you leave without at least": "No te dejaremos irte sin",
      "a handful of good headshots": "un buen puñado de fotos"
    },
    "de": {
      "How Your Selfies Become Professional Headshots": "Wie aus deinen Selfies professionelle Bewerbungsfotos werden",
      "Save hundreds of dollars and hours of time by using HeadshotPro to generate your new favorite professional headshots.": "Spare hunderte Euro und viele Stunden, indem du HeadshotPro nutzt, um deine neuen Lieblings-Bewerbungsfotos zu erstellen.",
      "With": "Mit",
      "1. Upload your photos": "1. Lade deine Fotos hoch",
      "(5 minutes)": "(5 Minuten)",
      "Use your favorite existing photos or take fresh selfies on the spot.": "Nutze deine besten vorhandenen Fotos oder mache spontan neue Selfies.",
      "2. Let our AI work its magic": "2. Lass unsere KI ihre Magie wirken",
      "(1-2 hours)": "(1-2 Stunden)",
      "Our AI will pull your most photogenic qualities from the photos you uploaded.": "Unsere KI extrahiert deine fotogensten Eigenschaften aus den hochgeladenen Fotos.",
      "3. Download your favorites": "3. Lade deine Favoriten herunter",
      "That was easy! Download your keepers and enjoy your new professional photos.": "Das war einfach! Lade deine Favoriten herunter und genieße deine neuen professionellen Fotos.",
      "Physical photoshoot": "Klassisches Fotoshooting",
      "Find a photographer you like": "Einen Fotografen finden, der dir gefällt",
      "Contact them and wait for a reply": "Ihn kontaktieren und auf eine Antwort warten",
      "Decide on a date and time you're both available": "Einen Termin finden, der für beide passt",
      "Find the right clothing to wear to the photo shoot": "Die passende Kleidung für das Shooting finden",
      "Get in your car and drive to the photo studio": "Ins Auto steigen und zum Fotostudio fahren",
      "Pose for your photos and pick your favorite shots": "Für die Fotos posieren und die besten Aufnahmen auswählen",
      "Wait for the photographer to send you the photos": "Warten, bis der Fotograf die Bilder schickt",
      "Update your professional profiles with your new photos": "Deine beruflichen Profile mit den neuen Fotos aktualisieren",
      "Create your AI headshots within 2 hours": "Erstelle deine KI-Bewerbungsfotos in 2 Stunden",
      "I updated my Linkedin profile image with this, cheaper than a studio pro with quality level better than home pro.": "Ich habe mein LinkedIn-Profilbild damit aktualisiert - günstiger als ein Profi-Studio und bessere Qualität als selbstgemacht.",
      "Same day results!": "Ergebnisse am selben Tag!"
    }
  }
</i18n>
