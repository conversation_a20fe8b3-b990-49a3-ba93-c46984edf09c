<template>
  <section class="py-12 bg-white sm:py-16 lg:py-20 xl:py-24">
    <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8 2xl:px-0">
      <div v-if="blocks && blocks.length > 0" class="space-y-12 xl:px-20 sm:space-y-16 lg:space-y-20 xl:space-y-24">
        <div v-for="(block, index) in blocks" :key="block.title" class="gap-12 lg:items-start lg:flex">
          <div class="w-full lg:max-w-md" :style="(index % 2 === 0) ? '' : 'order: 2'">
            <img class="object-cover w-full h-48 rounded-lg lg:h-full sm:h-56 md:h-64" :src="examples[index]" alt="">
          </div>

          <div class="flex-1 min-w-0 mt-4 lg:mt-0 flex justify-center flex-col" :style="(index % 2 === 0) ? '' : 'order: 1'">
            <h2 class="text-2xl font-bold sm:text-3xl lg:text-4xl text-primary-500 tracking-[-1.05px]">
              {{ block.title }}
            </h2>
            <div class="space-y-4 mt-5">
              <p v-for="(paragraph, i) in block.content" :key="`paragraph-${i}`" class="text-base leading-7 text-[#474368]">
                {{ paragraph }}
              </p>
              <div v-if="reviews.length > 0 && reviews[index]?.review" class="flex items-center gap-4 mt-4 lg:mt-10">
                <div class="size-14 overflow-hidden rounded-full shrink-0 relative">
                  <img class="absolute w-full h-full object-cover object-center" :src="reviews[index].image" alt="">
                </div>
                <div class="flex-1 min-w-0 space-y-1.5">
                  <!-- <img class="w-auto h-3" :src="reviews[index].review.image" alt=""> -->
                  <p class="text-xs italic leading-4 font-medium tracking-[-0.2px] text-paragraph">
                    “{{ reviews[index].review.quote }}”
                  </p>
                  <p class="text-xs font-bold leading-4 tracking-[-0.2px] text-paragraph capitalize">
                    {{ reviews[index].title }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  props: {
    blocks: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      reviews: [],
      usedReviews: [],
      examples: []
    }
  },
  computed: {
  },
  mounted () {
    this.getReviews()
  },
  methods: {
    async getReviews () {
      if (!this.$store.state.reviews || this.$store.state.reviews.length === 0) {
        const { data } = await this.$axios.$get('/reviews/all')
        this.$store.commit('SET_REVIEWS', data)
      }
      const approvedReviews = this.$store.state.reviews.filter(review => review?.review?.quote && review.createdAt && this.isCreatedAfterAugust21(review.createdAt))
      // Get reviews with content
      this.blocks.forEach((_) => {
        const notSelectedReviews = approvedReviews.filter(review => !this.usedReviews.includes(review._id))
        const randomReview = notSelectedReviews[Math.floor(Math.random() * notSelectedReviews.length)]
        this.reviews.push(randomReview)
        this.usedReviews.push(randomReview._id)
      })
      // Get example photos
      const exampleReviews = this.$store.state.reviews.filter(review => review?.image && review.createdAt && this.isCreatedAfterAugust21(review.createdAt))
      this.blocks.forEach((_) => {
        const notSelectedReviews = exampleReviews.filter(review => !this.usedReviews.includes(review._id))
        const randomReview = notSelectedReviews[Math.floor(Math.random() * notSelectedReviews.length)]
        if (randomReview?.thumbnail && randomReview?._id) {
          this.examples.push(randomReview.thumbnail)
          this.usedReviews.push(randomReview._id)
        }
      })
    },

    isCreatedAfterAugust21 (createdAt) {
      const date = new Date(createdAt)
      const august21 = new Date('2024-08-19')
      return date > august21
    }
  }

}
</script>

<style>

</style>
