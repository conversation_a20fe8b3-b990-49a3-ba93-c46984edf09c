<template>
  <section class="py-12 bg-white sm:py-16 lg:py-20 xl:py-24">
    <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8 2xl:px-0">
      <div class="text-left md:text-center">
        <h2 class="text-2xl font-bold tracking-[-1.05px] sm:text-3xl lg:text-4xl text-primary-500">
          Also Using HeadshotPro
        </h2>
        <p class="mt-3 text-base font-medium text-[#474368] sm:text-lg md:mx-auto md:max-w-2xl">
          Save hundreds of dollars and hours of time by using HeadshotPro to generate your new favorite professional
          headshots.
        </p>
      </div>

      <div v-if="blocks && blocks.length > 0" class="grid max-w-6xl grid-cols-1 gap-4 mx-auto mt-8 md:gap-8 md:grid-cols-3 sm:mt-12">
        <div

          v-for="(block, index) in blocks"
          :key="block.title"
          class="p-6 bg-white border rounded-lg border-primary-500/15 shadow-[0_0px_75px_0px_rgba(0,0,0,0.07)] md:p-0 md:rounded-none md:border-none md:shadow-none"
        >
          <img v-if="examples[index]" class="object-cover w-full rounded-lg" :src="examples[index]" :alt="block.title">

          <div class="mt-5 space-y-2  flex flex-col justify-between">
            <div class="flex flex-col space-y-2">
              <p class="tracking-[-0.3px] font-bold text-primary-500 text-lg leading-none">
                {{ block.title }}
              </p>
              <p class="text-base mt-2 font-normal text-paragraph tracking-[-0.3px]">
                {{ block.paragraph }}
              </p>
            </div>

            <nuxt-link
              :to="`/headshot-types/${block.slug}`"
              title=""
              class="text-base flex items-center justify-center w-full font-medium text-[#240D0D] border border-[#E6E6E6] hover:opacity-80 transition-all duration-150 bg-[#EEEEEE] rounded-lg px-4 pt-2 pb-2.5 h-10"
              role="button"
            >
              Learn more
            </nuxt-link>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  props: {
    blocks: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      examples: [],
      usedReviews: []
    }
  },
  computed: {
  },
  created () {
    this.getReviews()
  },
  methods: {
    getReviews () {
      const exampleReviews = this.$store.state.reviews.filter(review => review?.image && review.createdAt && this.isCreatedAfterAugust21(review.createdAt))
      this.blocks.forEach((_) => {
        const notSelectedReviews = exampleReviews.filter(review => !this.usedReviews.includes(review._id))
        const randomReview = notSelectedReviews[Math.floor(Math.random() * notSelectedReviews.length)]
        this.examples.push(randomReview.thumbnail)
        this.usedReviews.push(randomReview._id)
      })
    },

    isCreatedAfterAugust21 (createdAt) {
      const date = new Date(createdAt)
      const august21 = new Date('2024-08-19')
      return date > august21
    }
  }

}
</script>

<style>

</style>
