<template>
  <div class="relative flex inline-flex group opacity-50 hover:opacity-100 font-normal">
    <div class="rounded-full flex items-center justify-center cursor-pointer">
      <slot name="icon" />
      <IconSmallQuestionMark v-if="!$slots.icon || !$slots.icon.length" class="w-4 h-4 text-black/60" />
    </div>
    <div
      class="hidden z-[100] text-sm text-gray-700 group-hover:flex w-[250px] px-2 p-2 border border-black/10 leading-tight bg-white shadow-2xl rounded-md"
      :class="{
        'absolute bottom-full mb-2 left-4': position === 'right',
        'absolute bottom-full mb-2 right-4': position === 'left',
        'absolute bottom-full mb-0 left-1/2 -translate-x-1/2 text-center': position === 'center'
      }"
    >
      <Paragraph v-if="info" size="xs">
        <span v-html="info" />
      </Paragraph>
      <slot />
    </div>
  </div>
</template>

<script>
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
export default {
  components: {
    Paragraph
  },
  props: {
    info: {
      type: String,
      default: ''
    },
    position: {
      type: String,
      default: 'center',
      validator: value => ['right', 'left', 'center'].includes(value)
    }
  }

}
</script>

<style>

</style>
