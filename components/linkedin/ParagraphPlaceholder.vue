<template>
  <div
    v-if="loading"
    :class="`rounded-md bg-gray-100 animate-pulse ${placeholderClass} ${height}`"
  />
  <p
    v-else-if="!fake"
    :class="`${textSize}`"
  >
    <slot />
  </p>
  <div v-else />
</template>

<script>
export default {
  props: {
    loading: {
      type: Boolean,
      required: true
    },
    size: {
      type: String,
      default: 'base'
    },
    placeholderClass: {
      type: String,
      default: ''
    },
    fake: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    height () {
      return {
        xs: 'h-3',
        sm: 'h-4',
        base: 'h-6',
        lg: 'h-6',
        xl: 'h-8',
        '2xl': 'h-8'
      }[this.size] || ''
    },
    textSize () {
      return {
        xs: 'text-xs',
        sm: 'text-sm',
        base: 'text-base',
        lg: 'text-lg',
        xl: 'text-xl',
        '2xl': 'text-2xl'
      }[this.size] || ''
    }
  }
}
</script>
