<template>
  <section id="styles" class="bg-white sm:pb-16 lg:pb-20 xl:pb-24">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="mt-8 grid grid-cols-2 gap-4 sm:mt-12 sm:gap-5 lg:mt-16 lg:grid-cols-3">
        <template v-for="style in styles">
          <NuxtLink :key="style._id" :to="`/styles/${convertToSlug(style.title)}`" class="group overflow-hidden rounded-md border border-gray-200 hover:shadow">
            <div class="flex w-full transition duration-300 group-hover:scale-[1.02]">
              <ImageDns :src="style?.image?.maleImage || ''" class="w-1/2" />
              <ImageDns :src="style?.image?.femaleImage || ''" class="w-1/2" />
            </div>
            <div class="p-4">
              <h3 class="my-1 text-xl font-medium leading-6 text-gray-900 group-hover:font-medium">
                {{ style.title || "" }}
              </h3>
              <p class="text-gray-500 group-hover:text-gray-600">
                {{ style.description && style.description !== "null" ? style.description : "Let nature be your backdrop with a refreshing park setting, enhancing your headshot's authenticity and charm." }}
              </p>
              <div class="flex w-full items-center justify-end pt-2 text-gray-500 group-hover:text-teal-500">
                <div class="flex items-center space-x-2">
                  <span class="hidden transition duration-300 group-hover:block">Learn more</span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="h-6 w-6"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12h15m0 0l-6.75-6.75M19.5 12l-6.75 6.75" />
                  </svg>
                </div>
              </div>
            </div>
          </NuxtLink>
        </template>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  props: {
    styles: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style></style>
