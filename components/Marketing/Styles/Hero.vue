<template>
  <section class="relative py-12 sm:py-16 lg:pt-20 xl:pb-0">
    <div class="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="mx-auto max-w-3xl text-center">
        <h1 class="text-3xl font-bold tracking-tight text-primary-500 sm:text-4xl sm:leading-[3rem] lg:text-[46px]">
          Choose Your Perfect Professional Headshot Styles
        </h1>
        <p class="mt-6 text-lg font-normal leading-[1.8rem] text-gray-600">
          <!-- Upload a few selfies, choose your style, and let our AI create hundreds of professional headshots to elevate <strong>you or your team's</strong> headshots. -->
          Our AI-generated headshots come in various styles, allowing you to select the one that best reflects your personality and professional image. Browse our extensive list of styles and find the perfect backdrop for your headshot.
        </p>

        <div class="group relative mt-10 inline-flex space-x-4">
          <NuxtLink to="/app/admin/team/new">
            <ButtonPrimary>Create your team</ButtonPrimary>
          </NuxtLink>
          <NuxtLink to="/app/add">
            <ButtonWhite>Single person</ButtonWhite>
          </NuxtLink>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  data () {
    return {
      companyName: null,
      teamSize: null
    }
  },
  methods: {
    createTeam () {
      const url = '/app/admin/team/new'
      if (this.companyName && this.teamSize) {
        this.$router.push({ path: url, query: { companyName: this.companyName, teamSize: this.teamSize } })
      } else {
        this.$router.push({ path: url })
      }
    }
  }
}
</script>

<style></style>
