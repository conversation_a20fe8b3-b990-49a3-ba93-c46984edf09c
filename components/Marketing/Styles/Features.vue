<template>
  <section v-if="styles?.seo?.sections" class="bg-gray-50 py-10 sm:py-16 lg:py-24">
    <template v-for="(section, index) in styles?.seo?.sections">
      <div :key="section.type + index" class="md:py-18 mx-auto max-w-7xl px-4 py-8 sm:px-6 sm:py-12 lg:px-8">
        <div class="grid items-stretch gap-y-10 md:grid-cols-3 md:gap-x-20">
          <div :class="index % 2 === 0 ? '' : 'md:order-2'" class="relative mt-10 md:mt-0">
            <div class="lg:pl-8">
              <div class="relative flex w-full">
                <ImageDns :src="styles?.image?.femaleImage || ''" class="rounded-r-md" />
              </div>
            </div>
          </div>

          <div class="flex flex-col items-start md:col-span-2">
            <h2 class="mt-6 text-3xl font-bold tracking-tight text-primary-500 sm:text-4xl">
              {{ section.title }}
            </h2>
            <p class="mt-4 text-lg font-normal leading-relaxed text-gray-600">
              {{ section.description }}
            </p>
          </div>
        </div>
      </div>
    </template>
  </section>
</template>

<script>
export default {
  props: {
    styles: {
      type: Object,
      required: true
    }
  }
}
</script>

<style></style>
