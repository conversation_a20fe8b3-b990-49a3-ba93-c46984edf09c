<template>
  <div class="flex justify-start items-center space-x-2">
    <div class="flex items-center justify-center space-x-1">
      <div v-for="i in [1, 2, 3, 4, 5]" :key="i" :class="`${size} bg-[#DCDCE5] relative`">
        <div class="absolute left-0 top-0 h-full bg-[#1db47b]" :style="{ width: `${score >= i ? 100 : (1 - (i - score)) * 100}%` }" />
        <img src="@/assets/img/trustpilot-star.svg" class="absolute left-0 top-0 w-full h-full scale-[0.8]">
      </div>
    </div>
    <span class="text-sm font-medium tracking-tight text-gray-500">{{ score }} Rating</span>
  </div>
</template>

<script>
export default {
  props: {
    size: {
      type: String,
      default: 'w-8 h-8'
    }
  },
  computed: {
    score () {
      return this.$store.state.stats.trustpilotScore
    }
  }
}
</script>
