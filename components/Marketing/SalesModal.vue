<template>
  <Popup size="lg" @closeModal="$emit('close')">
    <div class="flex flex-col gap-2">
      <template v-if="!isSubmitted">
        <div class="gap-2 flex flex-col">
          <h2 class="text-lg font-semibold">
            {{ $t('Contact our sales team') }}
          </h2>
          <p class="text-sm text-paragraph">
            {{ $t('Have a question, special request, or just want to chat? Get in touch with me or my team and we\'ll be happy to help. I monitor all emails daily and would be happy to hop on a call to discuss your needs.') }}
          </p>
          <p class="text-sm text-paragraph">
            {{ $t('Kind regards,') }}
          </p>
          <div class="flex space-x-2 items-center justify-start">
            <div class="w-12 h-12 rounded-full bg-gradient-to-br from-teal-500/20 to-teal-600/20 flex items-center justify-center">
              <img src="@/assets/img/avatar-tam.png" class="w-12 h-12 rounded-full">
            </div>
            <div class="flex flex-col text-sm items-start justify-center ">
              <span class="font-bold text-gray-700">Tam <PERSON></span>
              <span class="text-paragraph">{{ $t('Enterprise General Manager') }}</span>
            </div>
          </div>
        </div>
        <hr class="border-gray-200 my-3">
        <LoadingSpinner v-if="isSubmitting" :title="$t('Sending message...')" />
        <template v-else>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input v-model="url" :label="$t('Business URL')" :placeholder="$t('I.e. headshotpro.com')" />
            <Input v-model="email" :label="$t('Email')" :placeholder="$t('Your email address')" />
          </div>
          <InputTextArea v-model="message" :label="$t('Message')" :rows="5" :placeholder="$t('Let us know how we can help you.')" />
          <ButtonPrimary @click="submit">
            {{ $t('Submit') }}
          </ButtonPrimary>
        </template>
        <span class="text-xs text-paragraph">{{ $t('Rather email us directly? You can reach us at') }} <a href="mailto:<EMAIL>" class="text-sky-500 underline"><EMAIL>.</a></span>
      </template>
      <template v-else>
        <div class="flex flex-col gap-2">
          <h2 class="text-lg font-semibold">
            {{ $t('Thank you for your message!') }}
          </h2>
          <p class="text-sm text-paragraph">
            {{ $t('We will get back to you as soon as possible.') }}
          </p>
        </div>
      </template>
    </div>
  </Popup>
</template>

<script>
export default {
  props: {
    formMessage: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      message: null,
      url: null,
      email: null,
      isSubmitting: false,
      isSubmitted: false
    }
  },
  mounted () {
    if (this.isLoggedIn) {
      this.email = this.$store.state.user.email
    }

    if (this.formMessage) {
      this.message = this.formMessage
    }

    if (this.$store.state.organization.organization.website) {
      this.url = this.$store.state.organization.organization.website
    }
  },
  methods: {
    submit () {
      if (!this.message || !this.url || !this.email) {
        return this.$toast.error('Please fill in all fields')
      }

      // Check for valid email using regex pattern
      const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
      if (!emailPattern.test(this.email)) {
        return this.$toast.error('Please enter a valid email address')
      }

      if (this.message.length < 10) {
        return this.$toast.error('Please enter a message')
      }

      this.isSubmitting = true
      this.$axios.$post('/sales/submit-contact-form', {
        url: this.url,
        email: this.email,
        message: this.message
      }).then(() => {
        this.$toast.success('Your message has been sent. We will get back to you as soon as possible.')
        this.isSubmitted = true
        this.$posthog.capture('$sales:submit_contact_form')
        try {
          this.$gtm.push({
            event: 'form_submit',
            email: this.email || null,
            // eslint-disable-next-line camelcase
            form_name: 'sales_form',
            // eslint-disable-next-line camelcase
            user_id: this.$store.state.user?.uid || null
          })
        } catch (err) {
          console.error(err)
        }
      }).catch(() => {
        this.$toast.error('Something went wrong. Please try again.')
      }).finally(() => {
        this.isSubmitting = false
      })
    }
  }
}
</script>

<i18n>
  {
    "en": {
      "Contact our sales team": "Contact our sales team",
      "Have a question, special request, or just want to chat? Get in touch with me or my team and we'll be happy to help. I monitor all emails daily and would be happy to hop on a call to discuss your needs.": "Have a question, special request, or just want to chat? Get in touch with me or my team and we'll be happy to help. I monitor all emails daily and would be happy to hop on a call to discuss your needs.",
      "Kind regards,": "Kind regards,",
      "Founder and CEO of HeadshotPro": "Founder and CEO of HeadshotPro",
      "Sending message...": "Sending message...",
      "Business URL": "Business URL",
      "I.e. headshotpro.com": "I.e. headshotpro.com",
      "Email": "Email",
      "Your email address": "Your email address",
      "Message": "Message",
      "Let us know how we can help you.": "Let us know how we can help you.",
      "Submit": "Submit",
      "Rather email us directly? You can reach us at": "Rather email us directly? You can reach us at",
      "Thank you for your message!": "Thank you for your message!",
      "We will get back to you as soon as possible.": "We will get back to you as soon as possible."
    },
    "es": {
      "Contact our sales team": "Contacta a nuestro equipo de ventas",
      "Have a question, special request, or just want to chat? Get in touch with me or my team and we'll be happy to help. I monitor all emails daily and would be happy to hop on a call to discuss your needs.": "¿Tienes una pregunta, solicitud especial, o simplemente quieres conversar? Ponte en contacto conmigo o mi equipo y estaremos felices de ayudar. Monitoreo todos los emails diariamente y estaría feliz de hacer una llamada para discutir tus necesidades.",
      "Kind regards,": "Saludos cordiales,",
      "Founder and CEO of HeadshotPro": "Fundador y CEO de HeadshotPro",
      "Sending message...": "Enviando mensaje...",
      "Business URL": "URL del negocio",
      "I.e. headshotpro.com": "P.ej. headshotpro.com",
      "Email": "Email",
      "Your email address": "Tu dirección de email",
      "Message": "Mensaje",
      "Let us know how we can help you.": "Déjanos saber cómo podemos ayudarte.",
      "Submit": "Enviar",
      "Rather email us directly? You can reach us at": "¿Prefieres escribirnos directamente? Puedes contactarnos en",
      "Thank you for your message!": "¡Gracias por tu mensaje!",
      "We will get back to you as soon as possible.": "Te responderemos lo antes posible."
    },
    "de": {
      "Contact our sales team": "Kontaktiere unser Vertriebsteam",
      "Have a question, special request, or just want to chat? Get in touch with me or my team and we'll be happy to help. I monitor all emails daily and would be happy to hop on a call to discuss your needs.": "Hast du eine Frage, eine spezielle Anfrage oder möchtest einfach nur reden? Nimm Kontakt mit mir oder meinem Team auf und wir helfen gerne. Ich überwache alle E-Mails täglich und würde gerne ein Gespräch führen, um deine Bedürfnisse zu besprechen.",
      "Kind regards,": "Mit freundlichen Grüßen,",
      "Founder and CEO of HeadshotPro": "Founder und CEO von HeadshotPro",
      "Sending message...": "Nachricht wird gesendet...",
      "Business URL": "Unternehmens-URL",
      "I.e. headshotpro.com": "Z.B. headshotpro.com",
      "Email": "E-Mail",
      "Your email address": "Deine E-Mail-Adresse",
      "Message": "Nachricht",
      "Let us know how we can help you.": "Lass uns wissen, wie wir dir helfen können.",
      "Submit": "Absenden",
      "Rather email us directly? You can reach us at": "Lieber direkt eine E-Mail schreiben? Du erreichst uns unter",
      "Thank you for your message!": "Vielen Dank für deine Nachricht!",
      "We will get back to you as soon as possible.": "Wir melden uns so schnell wie möglich bei dir."
    }
  }
</i18n>

<style>

</style>
