<template>
  <div class="w-full bg-brand-800 p-2 flex items-center justify-center space-x-2 relative overflow-hidden shine-hope-anim">
    <p class="hidden md:inline-flex space-x-2">
      <span class="gradient-text text-sm"><strong>Black Friday Offer:</strong> </span>
      <span class="text-white text-sm">Get up to 120 extra photos until this Monday</span>
      <span class="text-white/70 text-sm"><CountdownText date="2023-11-28T04:00:00" /></span>
    </p>
    <p class="inline-flex md:hidden space-x-1 ">
      <span class="gradient-text text-xs"><strong>Black Friday:</strong> </span>
      <span class="text-white text-xs">Get up to 120 extra photos.</span>
      <span class="text-white/70 text-xs"><CountdownText size="sm" date="2023-11-28T04:00:00" /></span>
    </p>
  </div>
</template>

<script>
export default {
  computed: {
    name () {
      return this.data
    }
  }

}
</script>

<style scoped>
.gradient-text{
    background: -webkit-linear-gradient(60deg,#f79533,#f37055,#ef4e7b,#a166ab,#5073b8,#1098ad,#07b39b,#6fba82);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
.shine-hope-anim:after{
  content: '';
  position: absolute;
  display: block;
  top: 0;
  left: -100%;
  width: 100%;
  height: 30px;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,.4), transparent);
  animation: ShineAnim 5s ease infinite;
  animation-delay: 2s;
}
@keyframes ShineAnim{
  0%{left: -100%}
  10%{left: 100%}
  100%{left: 100%}
}
</style>
