<template>
  <div class="relative bg-gray-50 w-full">
    <!-- START CTA -->
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="relative overflow-hidden rounded-xl bg-teal-500 shadow-lg">
        <div class="absolute bottom-0 right-0 -mr-10">
          <img class="w-full" src="@/assets/img/ring-pattern.svg" alt="">
        </div>

        <div class="relative flex flex-col items-center lg:flex-row lg:items-center">
          <div class="px-6 py-12 text-center sm:p-12 lg:py-16 lg:text-left xl:p-20">
            <h5 class="text-3xl font-bold text-white sm:text-3xl lg:text-3xl">
              {{ $t('Headshots without a headache') }}.
            </h5>
            <p class="mt-5 text-base font-normal text-white">
              {{ $t('Dreading the idea of scheduling an appointment across town with a local photographer? Our AI headshot generator will have your headshots completed in about 2 hours after you upload your selfies, from the comfort of your own home. Awkward small talk with the photographer not included.') }}
            </p>

            <div class="mt-8 flex flex-col items-center gap-4 sm:flex-row sm:justify-center sm:gap-6 lg:justify-start">
              <a href="/app/admin/team/new" title="" class="inline-flex h-12 w-full items-center justify-center gap-2 rounded-lg border border-transparent bg-primary-500 px-4 py-2 text-base font-medium leading-6 text-white shadow-sm transition-all duration-150 hover:bg-teal-800 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-500 sm:w-auto" role="button">
                {{ $t('Create your team') }}
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </a>

              <span class="text-sm font-bold uppercase tracking-wide text-white/40">{{ $t('Or') }}</span>

              <a :href="localePath('/app/add')" title="" class="text-lg font-medium text-white underline transition-all duration-150 hover:opacity-90">{{ $t('Single person') }}</a>
            </div>
          </div>

          <div class="mt-4 shrink-0 lg:mt-0 lg:px-8 xl:px-16">
            <ImageDns width="422" height="444" class="w-full max-w-sm origin-bottom scale-110 lg:translate-y-16" :src="require('@/assets/img/cta-image.png')" alt="" />
          </div>
        </div>
      </div>
    </div>
    <!-- END CTA -->
  </div>
</template>

<script>
export default {}
</script>

<i18n>
  {
    "es": {
      "Headshots without a headache": "Fotos sin dolor de cabeza",
      "Dreading the idea of scheduling an appointment across town with a local photographer? Our AI headshot generator will have your headshots completed in about 2 hours after you upload your selfies, from the comfort of your own home. Awkward small talk with the photographer not included.": "¿Te da dolor de cabeza la idea de programar una cita en la ciudad con un fotógrafo local? Nuestro generador de fotos de perfil con IA completará tus fotos en aproximadamente 2 horas después de subir tus selfies, desde la comodidad de tu hogar. La charla incómoda con el fotógrafo no está incluida.",
      "Create your team": "Crea tu equipo",
      "Or": "O",
      "Single person": "Persona individual"
    }
  }
</i18n>
