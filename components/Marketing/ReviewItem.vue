<template>
  <div
    class="bg-white border rounded-lg shadow-[0_0px_75px_0px_rgba(0,0,0,0.07)] border-primary-500/15 p-3"
  >
    <div class="space-y-2">
      <div class="flex items-center gap-3">
        <ImageDns class="object-cover rounded-full shrink-0 size-8" :src="item?.thumbnail || item.image" @load="$emit('load')" />
        <div>
          <p class="text-[#111111] text-base font-bold leading-[18px] tracking-[-0.056px]">
            {{ item.title.split(' ')[0] }}
          </p>
          <p class="text-[#555555] text-xs mt-1">
            HeadshotPro customer
          </p>
        </div>
      </div>

      <img
        v-if="item?.review?.trustpilotScore == 4"
        class="w-auto h-6"
        src="@/assets/img/ratings-4.png"
        alt=""
      >
      <img
        v-if="item?.review?.trustpilotScore == 5"
        class="w-auto h-6"
        src="@/assets/img/ratings-5.png"
        alt=""
      >

      <p v-if="item?.review?.title" class="text-base font-bold leading-5 text-[#111111] tracking-[-0.056px]">
        {{ item.review.title }}
      </p>

      <p v-if="item?.review?.quote" class="text-sm font-normal text-[#111111] tracking-[-0.056px]">
        {{ item?.review?.quote }}
      </p>

      <div class="relative">
        <div class="relative group cursor-pointer rounded-lg overflow-hidden" @click="showEnlargedImage(item?.thumbnail || item.image)">
          <ImageDns class="object-cover w-full rounded-lg" :src="item?.thumbnail || item.image" />
          <div class="hidden group-hover:flex absolute inset-0 bg-black bg-opacity-50 items-center justify-center z-50">
            <span class="text-white text-sm">Click to enlarge</span>
          </div>
        </div>
        <span
          v-if="badge"
          class="bg-teal-600/70 backdrop-blur-md text-white text-[9px] font-medium px-1.5 py-0.5 rounded-md absolute top-2 left-2 z-10 opacity-70 uppercase"
        >
          {{ badge }}
        </span>
      </div>
    </div>
    <portal to="modal">
      <div
        v-if="enlargedImage"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 cursor-pointer"
        @click="enlargedImage = null"
      >
        <div class="max-h-[90vh] max-w-[768px] object-contain relative">
          <img :src="enlargedImage" class="max-h-[90vh] max-w-[768px] object-contain">
          <span class="absolute top-2 right-2 text-white text-sm cursor-pointer" @click="enlargedImage = null">
            Close
          </span>
          <div class="absolute text-xs font-medium top-2 left-2 text-white  bg-teal-600/70 backdrop-blur-md px-1.5 py-0.5 rounded-md">
            100% AI generated
          </div>
        </div>
      </div>
    </portal>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true
    },
    badge: {
      type: String,
      required: false,
      default: null
    }
  },
  data () {
    return {
      enlargedImage: null
    }
  },
  methods: {
    showEnlargedImage (imageUrl) {
      this.enlargedImage = imageUrl
    },
    formatReviewDate (date) {
      const now = new Date()
      const reviewDate = new Date(date)
      const timeDiff = now - reviewDate
      const daysAgo = Math.floor(timeDiff / (1000 * 60 * 60 * 24))
      return daysAgo === 0 ? 'Today' : (daysAgo === 1) ? '1 day ago' : `${daysAgo} days ago`
    }
  }
}
</script>
