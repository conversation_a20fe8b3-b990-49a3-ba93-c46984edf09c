<template>
  <div class="bg-black text-white py-3 px-2 sm:px-4 flex flex-col sm:flex-row items-center justify-center font-sans space-y-2 sm:space-y-0 sm:space-x-4">
    <div class="flex flex-row items-center space-x-4">
      <span class="text-sm font-bold text-center hidden sm:block">Cyber Monday Sale: 20% OFF</span>
      <span class="text-sm font-bold text-center block sm:hidden">Cyber Monday: 20% OFF</span>
      <span class="bg-green-600 px-3 py-1 rounded-md font-bold text-sm hidden sm:block">Use code: CYBER20</span>
      <span class="bg-green-600 px-3 py-1 rounded-md font-bold text-sm block sm:hidden">Code: CYBER20</span>
    </div>
    <div class=" items-center space-x-2 flex">
      <span class="text-sm">Ends in:</span>
      <div class="flex space-x-1">
        <CountdownUnit :value="countdown.hours" unit="h" />
        <CountdownUnit :value="countdown.minutes" unit="m" />
        <CountdownUnit :value="countdown.seconds" unit="s" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      countdownDate: new Date('2024-12-02T23:59:59-05:00').getTime(),
      now: Math.trunc((new Date()).getTime() / 1000) * 1000,
      timer: null
    }
  },
  computed: {
    countdown () {
      const difference = this.countdownDate - this.now
      if (difference < 0) {
        return { days: 0, hours: 0, minutes: 0, seconds: 0 }
      }

      const hours = Math.floor(difference / (1000 * 60 * 60))
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((difference % (1000 * 60)) / 1000)

      return { hours, minutes, seconds }
    }
  },
  mounted () {
    this.timer = setInterval(() => {
      this.now = Math.trunc((new Date()).getTime() / 1000) * 1000
    }, 1000)
  },
  beforeDestroy () {
    clearInterval(this.timer)
  }
}
</script>
