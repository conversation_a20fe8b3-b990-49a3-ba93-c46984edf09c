<template>
  <section id="styles" class="py-12 bg-gray-50 sm:py-16 lg:py-20 xl:py-24">
    <div class="px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl">
      <div class="max-w-3xl mx-auto text-center">
        <h2 class="text-md md:text-lg font-bold text-teal-500">
          {{ $t('Backdrops & clothing') }}
        </h2>
        <h2 class="mt-6 text-2xl font-bold tracking-tight sm:text-4xl lg:text-5xl text-primary-500">
          {{ $t('Pick from a wide variety of backdrops and clothing') }}
        </h2>
        <p class="mt-4 text-base font-normal text-gray-600">
          {{ $t('Our AI headshots capture your most photogenic qualities and reproduce them on a backdrop of your choosing, with perfect facial lighting that would take hours for a real photographer to set up under even the most perfect conditions. AI headshots tease out the most photogenic version of yourself.') }}
        </p>
      </div>

      <div class="grid grid-cols-3 gap-3 mt-8 sm:mt-12 lg:grid-cols-5 sm:gap-5 lg:mt-16">
        <template v-for="style in styles">
          <div :key="style._id" class="relative rounded-md overflow-hidden">
            <div class="flex w-full">
              <ImageDns alt="Headshot style male" :src="style?.image?.maleImage || ''" class="w-1/2 rounded-l-md aspect-[2/3]" width="150" height="225" />
              <ImageDns alt="Headshot style female" :src="style?.image?.femaleImage || ''" class="w-1/2 rounded-r-md aspect-[2/3]" width="150" height="225" />
            </div>
            <div class="w-full flex justify-center items-end absolute px-2 pb-4 bg-gradient-to-b from-transparent to-black/70 bottom-0 left-0 h-[100px] text-center">
              <h3 class="text-xs md:text-sm leading-6 font-normal text-white/90">
                {{ style.title || "" }}
              </h3>
            </div>
          </div>
        </template>
      </div>
      <div class="hidden md:block my-8">
        <h3 class="font-medium text-center w-full text-lg my-12 opacity-60">
          {{ $t('With a large variety of clothing options') }}
        </h3>
        <div class="grid grid-cols-3 md:grid-cols-5 gap-4">
          <template v-for="item in clothing">
            <div :key="item.type">
              <p class="text-sm text-center">
                {{ item.type.replace(/^a |^an /i, '') }}
              </p>
            </div>
          </template>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  // props: {
  //   styles: {
  //     type: Array,
  //     default: () => []
  //   },
  //   clothing: {
  //     type: Array,
  //     default: () => []
  //   }
  // }
  computed: {
    styles () {
      return this.$store.state.styles
    },
    clothing () {
      return this.$store.state.clothing
    }
  }

}
</script>

<i18n>
  {
    "es": {
      "Backdrops & clothing": "Fondos y ropa",
      "Pick from a wide variety of backdrops and clothing": "Elija entre una amplia variedad de fondos y ropa",
      "Our AI headshots capture your most photogenic qualities and reproduce them on a backdrop of your choosing, with perfect facial lighting that would take hours for a real photographer to set up under even the most perfect conditions. AI headshots tease out the most photogenic version of yourself.": "Nuestros retratos de IA capturan sus cualidades más fotogénicas y las reproducen en un fondo de su elección, con una iluminación facial perfecta que llevaría horas para que un fotógrafo real la configure incluso en las condiciones más perfectas. Los retratos de IA sacan a relucir tu versión más fotogénica.",
      "With a large variety of clothing options": "Con una gran variedad de opciones de ropa"
    }
  }
</i18n>
