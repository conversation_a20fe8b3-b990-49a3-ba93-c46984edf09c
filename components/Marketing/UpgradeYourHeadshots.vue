<template>
  <section class="py-12 bg-white sm:py-16 lg:py-20 xl:py-24">
    <div class="max-w-screen-xl px-4 mx-auto sm:px-6 2xl:px-0 lg:px-8">
      <div class="relative text-left md:text-center">
        <div v-if="trustpilot" class="flex items-center justify-start md:justify-center gap-x-3">
          <img class="w-auto h-4" src="@/assets/img/trustpilot-stars-4.5.svg" alt="" loading="lazy">
          <img class="w-auto h-4" src="@/assets/img/logo-trustpilot.png" alt="" loading="lazy">
        </div>
        <slot name="pre-title" />
        <h2 class="mt-3 text-2xl font-bold tracking-[-1.05px] sm:text-3xl lg:text-4xl text-primary-500 max-w-3xl mx-auto">
          {{ title }}
        </h2>
        <p class="mt-3 text-base font-medium text-[#474368] sm:text-lg md:mx-auto md:max-w-xl">
          Save hundreds of dollars and hours of time by using HeadshotPro to generate your new favorite professional
          headshots.
        </p>

        <div v-if="trustpilot" class="flex items-center justify-start mt-3 md:justify-center gap-x-3">
          <div class="flex -space-x-2 overflow-hidden">
            <img class="inline-block rounded-full size-6 ring-2 ring-white" src="@/assets/img/avatar-1.jpg" alt="" loading="lazy">
            <img class="inline-block rounded-full size-6 ring-2 ring-white" src="@/assets/img/avatar-2.jpg" alt="" loading="lazy">
            <img class="inline-block rounded-full size-6 ring-2 ring-white" src="@/assets/img/avatar-3.jpg" alt="" loading="lazy">
          </div>
          <p class="text-sm sm:text-base lg:text-lg -mt-0.5 font-medium tracking-tighter text-paragraph">
            Trusted by <span class="font-bold text-[#00B67A]">{{ $store.state.stats.users }}+</span> happy customers
          </p>
        </div>

        <div v-if="trustpilot" class="absolute flex flex-col items-center hidden -top-32 left-64 lg:block">
          <p
            class="text-base leading-4 rotate-[8deg] py-4 text-center font-cursive text-paragraph tracking-[-0.056px]"
          >
            Start now and get<br>
            your headshots by today
          </p>
          <svg
            class="w-auto h-16 text-paragraph"
            viewBox="0 0 38 64"
            fill="currentColor"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M28.303 61.2675C25.5873 61.4634 22.9526 61.6967 19.9573 61.9085C20.54 63.2885 21.8211 63.8314 23.1018 63.8427C26.9437 63.8768 30.8372 63.8379 34.7229 63.5057C36.9455 63.3237 37.7316 61.7542 36.6115 60.2228C34.7838 57.6403 32.7723 55.1662 30.8122 52.6191C30.7017 52.4715 30.5764 52.2688 30.4291 52.2491C29.8917 52.0977 29.3176 52.0743 28.7287 51.9958C28.6335 52.4349 28.2733 52.9449 28.4281 53.2579C28.937 54.3623 29.5635 55.3761 30.1238 56.4075C30.4187 56.9784 30.7651 57.4764 30.6703 58.4471C29.7131 58.0537 28.6898 57.678 27.7326 57.2846C8.45592 48.3884 -0.1283 29.5965 6.16177 10.423C6.9912 7.95569 8.12984 5.58275 9.10649 3.13504C9.44427 2.27648 9.71583 1.43566 10.0536 0.577093C9.87685 0.447212 9.64864 0.390198 9.47188 0.260316C8.92017 0.58539 8.14025 0.85345 7.8463 1.34578C6.78054 2.93114 5.71478 4.5165 4.89201 6.214C-3.3653 23.0787 1.2669 42.2206 16.6011 53.8865C19.5398 56.1122 22.9859 57.8475 26.1966 59.764C26.8741 60.1733 27.5883 60.4546 28.3172 60.791C28.413 60.8835 28.3911 61.0666 28.303 61.2675Z"
            />
          </svg>
        </div>
      </div>

      <div class="gap-6 mt-8 md:flex md:justify-center sm:mt-12">
        <div
          class="w-full p-8 bg-white border rounded-lg shadow-[0px_0px_75px_0px_rgba(0,0,0,0.07)] border-primary-500/15 lg:max-w-lg"
        >
          <img class="w-full rounded-lg ring-1 ring-gray-200" src="@/assets/img/with-headshotpro.png" alt="A calendar showing HeadshotPro only takes 10 minutes to generate AI headshots">

          <div class="flex items-center gap-2 mt-6">
            <p class="text-xl font-bold text-primary-500">
              With
            </p>
            <img class="w-auto h-6 -mb-0.5" src="@/assets/img/logo.svg" alt="" loading="lazy">
          </div>

          <ul class="mt-6 space-y-8">
            <li class="flex items-start gap-4">
              <img class="hidden shrink-0 size-8 sm:block" src="@/assets/img/icon-profile.svg" alt="" loading="lazy">
              <div class="flex-1 min-w-0">
                <p class="text-lg font-bold leading-none tracking-tighter text-primary-500">
                  1. Upload your photos <span class="font-normal">(5 minutes)</span>
                </p>
                <p class="mt-1 text-base font-normal tracking-[-0.056px] text-paragraph">
                  Use your best existing photos or take fresh selfies on the spot.
                </p>
              </div>
            </li>

            <li class="flex items-start gap-4">
              <img class="hidden shrink-0 size-8 sm:block" src="@/assets/img/icon-magic.svg" alt="" loading="lazy">
              <div class="flex-1 min-w-0">
                <p class="text-lg leading-none font-bold tracking-[-0.056px] text-primary-500">
                  2. Let our AI work its magic <span class="font-normal">(1-2 hours)</span>
                </p>
                <p class="mt-1 text-base font-normal tracking-[-0.056px] text-paragraph">
                  Our AI will pull your most photogenic qualities from the photos you uploaded.
                </p>
              </div>
            </li>

            <li class="flex items-start gap-4">
              <img class="hidden shrink-0 size-8 sm:block" src="@/assets/img/icon-user.svg" alt="" loading="lazy">
              <div class="flex-1 min-w-0">
                <p class="text-lg leading-none font-bold tracking-[-0.056px] text-primary-500">
                  3. Download your favorites <span class="font-normal">(5 minutes)</span>
                </p>
                <p class="mt-1 text-base font-normal tracking-[-0.056px] text-paragraph">
                  That was easy! Download your keepers and enjoy your new professional photos.
                </p>
              </div>
            </li>
          </ul>
        </div>

        <div class="relative hidden w-px min-h-full bg-[#92A0B5] bg-opacity-45 md:block shrink-0">
          <span
            class="absolute left-1/2 -translate-x-1/2 text-sm font-medium tracking-[-0.3px] text-slate-400 -translate-y-1/2 top-1/2 py-2.5 bg-[#F5F5F5] text-center w-5"
          >
            vs
          </span>
        </div>

        <div
          class="w-full p-8 mt-4 bg-white border rounded-lg shadow-[0px_0px_75px_0px_rgba(0,0,0,0.07)] border-primary-500/15 lg:max-w-lg md:mt-0"
        >
          <img class="w-full rounded-lg ring-1 ring-gray-200" src="@/assets/img/physical-photoshoot.png" alt="A calendar showing a physical photoshoot can take days before you get your headshots back">

          <p class="mt-5 text-xl font-bold text-primary-500">
            Physical photoshoot
          </p>

          <ul class="mt-4 space-y-3 text-base font-normal tracking-[-0.056px] text-paragraph">
            <li class="flex items-center gap-2.5">
              <svg
                class="-mb-0.5 text-paragraph size-5 shrink-0"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z"
                />
              </svg>
              Find a photographer you like
            </li>

            <li class="flex items-center gap-2.5">
              <svg
                class="-mb-0.5 text-paragraph size-5 shrink-0"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z"
                />
              </svg>
              Contact them and wait for a reply
            </li>

            <li class="flex items-center gap-2.5">
              <svg
                class="-mb-0.5 text-paragraph size-5 shrink-0"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z"
                />
              </svg>
              Decide on a date and time you’re both available
            </li>

            <li class="flex items-center gap-2.5">
              <svg
                class="-mb-0.5 text-paragraph size-5 shrink-0"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z"
                />
              </svg>
              Find the right clothing to wear to the photo shoot
            </li>

            <li class="flex items-center gap-2.5">
              <svg
                class="-mb-0.5 text-paragraph size-5 shrink-0"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z"
                />
              </svg>
              Get in your car and drive to the photo studio
            </li>

            <li class="flex items-center gap-2.5">
              <svg
                class="-mb-0.5 text-paragraph size-5 shrink-0"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z"
                />
              </svg>
              Pose for your photos and pick your favorite shots
            </li>

            <li class="flex items-center gap-2.5">
              <svg
                class="-mb-0.5 text-paragraph size-5 shrink-0"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z"
                />
              </svg>
              Wait for the photographer to send you the finished photos
            </li>

            <li class="flex items-center gap-2.5">
              <svg
                class="-mb-0.5 text-paragraph size-5 shrink-0"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z"
                />
              </svg>
              Update your professional profiles with your new photos
            </li>
          </ul>
        </div>
      </div>

      <div class="relative">
        <div class="gap-6 mt-8 sm:flex sm:items-center sm:justify-center sm:mt-12">
          <nuxt-link
            to="/"
            class="text-lg w-full leading-6 sm:w-auto inline-flex font-bold text-white rounded-lg bg-primary-500 px-6 pt-2.5 pb-3.5 h-12 border border-primary-600 transition-all duration-150 hover:bg-opacity-90 shadow-[0_0px_24px_0px_rgba(0,0,0,0.25)] gap-1.5 items-center justify-center disabled:bg-opacity-20"
            role="button"
          >
            Create your AI headshots within 2 hours
          </nuxt-link>

          <nuxt-link
            v-if="testimonials"
            to="/reviews"
            class="text-base w-full mt-4 sm:mt-0 sm:w-auto h-12 inline-flex font-semibold text-gray-600 rounded-lg shadow-sm bg-gray-200 px-4 pt-2.5 pb-3.5 border border-transparent transition-all duration-150 hover:bg-opacity-90 gap-1.5 items-center justify-center disabled:bg-opacity-20"
            role="button"
          >
            See 100+ real examples
          </nuxt-link>
        </div>

        <slot name="after" />
      </div>

      <template v-if="testimonials">
        <div class="mt-8 text-left md:text-center sm:mt-12">
          <p class="text-sm font-bold tracking-tighter sm:text-base lg:text-lg text-primary-500">
            Trusted by {{ $store.state.stats.users }}+ happy customers and counting.
          </p>
        </div>

        <div class="mt-8 md:px-20 lg:px-0">
          <MarketingTestimonialGrid />
        </div>
      </template>
    </div>
  </section>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      required: false,
      default: 'Upgrade Your Headshots In 2 Hours'
    },
    testimonials: {
      type: Boolean,
      default: true
    },
    trustpilot: {
      type: Boolean,
      default: true
    }
  }
}
</script>
