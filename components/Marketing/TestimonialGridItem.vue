<template>
  <div class="w-full shrink-0 snap-start">
		<slot />
		<div class="relative overflow-hidden rounded-lg">
			<img class="object-cover w-full h-full" :src="testimonial.imageUrl" alt="">

			<div class="absolute inset-x-0 bottom-0 h-1/2 bg-gradient-to-t from-black to-transparent" />

			<div class="absolute inset-x-0 bottom-0 p-6">
				<svg
					aria-hidden="true"
					class="w-auto h-2.5 text-white"
					viewBox="0 0 13 10"
					fill="currentColor"
					xmlns="http://www.w3.org/2000/svg"
				>
					<path
						d="M6.496 9.152L6.88 6.4C7.11467 4.69333 7.72267 3.28533 8.704 2.176C9.68533 1.06667 10.9973 0.341333 12.64 0L12.352 2.048C11.6053 2.21867 10.976 2.53867 10.464 3.008C9.97333 3.47733 9.62133 4.032 9.408 4.672H11.36L10.72 9.152H6.496ZM0 9.152L0.384 6.4C0.618667 4.69333 1.22667 3.28533 2.208 2.176C3.18933 1.06667 4.50133 0.341333 6.144 0L5.888 2.048C5.14133 2.21867 4.512 2.53867 4 3.008C3.488 3.47733 3.12533 4.032 2.912 4.672H4.864L4.256 9.152H0Z"
					/>
				</svg>

				<blockquote class="mt-3">
					<p class="text-sm italic font-medium tracking-tight text-white">
						{{ testimonial.quote }}
					</p>

					<div class="flex items-center gap-4 mt-3">
						<div class="flex-1 min-w-0">
							<p class="text-base font-bold tracking-tight text-white">
								{{ testimonial.name }}
							</p>
							<p v-show="testimonial.position" class="text-sm font-normal tracking-tight text-white">
								{{ testimonial.position }}
							</p>
						</div>
					</div>
				</blockquote>
			</div>

			<div class="absolute top-2.5 left-2.5">
				<span
					class="px-3 pt-px pb-0.5 text-xs font-bold tracking-normal text-white uppercase rounded-full bg-gray-900/60"
				>
					100% AI GENERATED
				</span>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		testimonial: {
			type: Object,
			required: true
		}
	}
}
</script>
