<template>
  <div>
    <section class="bg-white relative overflow-hidden flex flex-col w-full max-w-full h-screen">
      <div class="hero-mobile h-[calc(100vh-510px)]" />
      <div class="p-4 h-[380px] flex flex-col  justify-center space-y-4">
        <div class="space-y-2">
          <h1 class="font-bold text-2xl leading-[1.7rem] text-primary-500">
            {{ title }}
          </h1>

          <p id="convert-3-v1-p" class="text-sm leading-[1.4rem] md:text-lg font-normal md:leading-[1.8rem] text-gray-600">
            Get professional business headshots in minutes with our AI-photographer. Upload photos, pick style & receive 120+ generated headshots.
          </p>
        </div>
        <div class="flex flex-col space-y-1 md:hidden">
          <div class="flex items-center justify-start">
            <IconCheck class="w-3 h-3 text-green-500 mr-1.5" />
            <span class="text-xs text-gray-400">Done in 2 hours, fast turn-around</span>
          </div>
          <div class="flex items-center">
            <IconCheck class="w-3 h-3 text-green-500 mr-1.5" />
            <p class=" text-xs font-normal text-gray-400">
              Used by {{ $store.state.stats.users }} happy customers
            </p>
            <div class="flex items-center space-x-px ml-2">
              <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path
                  d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                />
              </svg>
              <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path
                  d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                />
              </svg>
              <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path
                  d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                />
              </svg>
              <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path
                  d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                />
              </svg>
              <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path
                  d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                />
              </svg>
            </div>
          </div>
          <div class="flex items-center justify-start">
            <IconCheck class="w-3 h-3 text-green-500 mr-1.5" />
            <span class="text-xs text-gray-400">{{ $store.state.stats.photos }}+ professional headshots already created</span>
          </div>
        </div>
        <div class="flex gap-2 items-center content-center w-full">
          <template v-if="!isLoggedIn">
            <nuxt-link to="/auth/login?redirect=%2Fapp" class="w-full">
              <button title="" class="inline-flex h-12 w-full items-center justify-center gap-2 rounded-lg border border-transparent bg-orange-500 px-4 py-2 text-base font-medium leading-6 text-white shadow-sm transition-all duration-150 hover:bg-gray-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-500 sm:w-auto" role="button">
                <span class="flex-shrink-0">Get your headshots</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </button>
            </nuxt-link>
          </template>
          <template v-else>
            <nuxt-link to="/app">
              <ButtonPrimary class="w-full md:auto">
                Access your photos
              </ButtonPrimary>
            </nuxt-link>
          </template>
        </div>
        <div class="flex items-center justify-center gap-3 lg:justify-start">
          <div class="flex items-center space-x-2">
            <span class="text-sm text-gray-600 hidden md:flex">As seen on: </span>
            <img src="@/assets/img/logo-1.png" class="w-[40px] md:w-[60px]">
            <img src="@/assets/img/logo-2.png" class="w-[40px] md:w-[60px]">
            <img src="@/assets/img/logo-3.png" class="w-1/5 md:w-[100px]">
            <img src="@/assets/img/logo-4.png" class="w-1/5 md:w-[100px]">
            <img src="@/assets/img/logo-5.png" class="w-1/5 md:w-[100px]">
          </div>
        </div>
        <!-- <img src="@/assets/img/steps-mobile.png" class="w-full"> -->
      </div>
      <div class="w-full bg-teal-100 p-2">
        <div class="grid grid-cols-3 divide-x divide-teal-200">
          <div class="flex flex-col items-center">
            <div class="flex items-center gap-2">
              <p class="text-3xl font-bold text-primary-500">
                2
              </p>
              <p class="text-xs font-bold uppercase leading-none text-primary-500">
                hours
                <br>
                done
              </p>
            </div>
            <p class="text-xs font-normal text-primary-500">
              fast turn around
            </p>
          </div>
          <div class="flex flex-col items-center">
            <div class="flex items-center">
              <p class="text-sm font-bold uppercase leading-none text-primary-500">
                $
              </p>
              <p class="text-2xl font-bold text-primary-500">
                29
              </p>
            </div>
            <p class="text-xs font-normal text-primary-500">
              price/person
            </p>
          </div>
          <div class="flex flex-col items-center">
            <div class="flex items-center">
              <p class="text-2xl font-bold text-primary-500">
                {{ photoPerStyle * 3 }}
              </p>
            </div>
            <p class="text-xs font-normal text-primary-500">
              headshots/shoot
            </p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  data () {
    return {
      companyName: null,
      teamSize: null
    }
  },
  computed: {
    title () {
      const { utm_campaign: utmCampaign } = this.$route.query
      if (utmCampaign === 'ai-headshots') { return 'AI generated headshots, without physical photo shoot' }
      if (utmCampaign === 'profile-picture') { return 'AI generated profile pictures, without physical photo shoot' }
      return 'Professional corporate headshots, without physical photo shoot'
    }
  },
  methods: {
    createTeam () {
      const url = '/app/admin/team/new'
      if (this.companyName && this.teamSize) {
        this.$router.push({ path: url, query: { companyName: this.companyName, teamSize: this.teamSize } })
      } else {
        this.$router.push({ path: url })
      }
    }
  }
}
</script>

<style>

</style>
