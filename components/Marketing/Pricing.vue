<template>
  <section id="pricing" class=" py-12 sm:py-16 lg:py-20 xl:py-24">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="mx-auto max-w-3xl text-center">
        <h2 class="text-base md:text-lg font-bold text-teal-500">
          {{ $t('Pricing') }}
        </h2>
        <p class="mt-6 text-2xl font-bold tracking-tight text-primary-500 sm:text-4xl lg:text-5xl">
          {{ $t('Professional headshots for 10x less than a physical photo shoot') }}
        </p>
        <p class="text-sm italic mt-6 text-black/60">
          {{ $t('According to our 2023 research, see the results over') }}
          <nuxt-link to="/blog/how-much-does-a-headshot-cost#headshot-pricing-in-united-states-ranked-by-state" target="_blank" class="underline">
            {{ $t('here') }}
          </nuxt-link>.
        </p>
        <p class="mt-4 text-base font-normal text-gray-600">
          {{ $t('Get high-quality professional corporate business headshots for you or your team without breaking the bank. Our pricing is an affordable option for small and large teams alike.') }}
        </p>

        <!-- <ExperimentWrapper id="7-profile-worthy-refund">
          <template #variant>
            <div class="w-auto mx-auto bg-green-500/10 rounded-md py-2 px-4 inline-block  mt-8">
              <div class="flex items-center justify-center">
                <IconSolidCheckBadge class="text-[#17A400] w-5 h-5 mr-1.5 hidden md:inline-flex" />
                <p class="font-medium text-center md:text-left text-green-900">
                  Try risk free now with our <nuxt-link to="/refund" target="_blank" class="underline">
                    ‘Profile-Worthy’ Money Back Guarantee
                  </nuxt-link>
                </p>
              </div>
            </div>
          </template>
        </ExperimentWrapper> -->
        <!-- <ExperimentWrapper id="7-profile-worthy-refund"> -->
        <!-- <div class="w-auto mx-auto bg-green-500/10 rounded-md py-2 px-4 inline-block  mt-8">
          <div class="flex items-center justify-center">
            <IconSolidCheckBadge class="text-[#17A400] w-5 h-5 mr-1.5 hidden md:inline-flex" />
            <p class="font-medium text-center md:text-left text-green-900">
              <strong>Black Friday Offer:</strong> Get up to 120 extra photos until this Saturday
              <span class="text-black/70 text-sm"><CountdownText date="2023-11-28T04:00:00" /></span>
            </p>
          </div>
        </div> -->
        <!-- </ExperimentWrapper> -->
        <div class="mt-8 flex justify-center">
          <fieldset class="grid grid-cols-2 gap-x-1 rounded-full p-1 text-center text-xs font-medium leading-5 ring-1 ring-inset ring-gray-200">
            <legend class="sr-only">
              {{ $t('For teams or individuals') }}
            </legend>

            <label :class="type === 'team' ? 'bg-brand-500 text-white' : 'text-gray-500'" class="cursor-pointer rounded-full py-1 px-2.5">
              <input v-model="type" type="radio" name="type" value="team" class="sr-only">
              <span>{{ $t('Team') }}</span>
            </label>

            <label :class="type === 'individual' ? 'bg-brand-500 text-white' : 'text-gray-500'" class="cursor-pointer rounded-full py-1 px-2.5">
              <input v-model="type" type="radio" name="type" value="individual" class="sr-only">
              <span>{{ $t('Individual') }}</span>
            </label>
          </fieldset>
        </div>
      </div>

      <div v-show="type === 'team'" class="mx-auto mt-4 max-w-2xl rounded-lg border border-gray-200 bg-white shadow-sm sm:mt-6 lg:mt-8">
        <div class="p-8">
          <div class="flex flex-col justify-center gap-8 sm:flex-row sm:items-center sm:gap-12 lg:gap-12">
            <div class="text-center">
              <p class="text-7xl font-bold text-teal-500">
                $39
              </p>
              <p class="mt-1 text-base font-normal text-primary-500">
                {{ $t('per person') }}
              </p>

              <p class="mt-2 text-xs font-normal text-gray-500">
                {{ $t('5+ people: 20% off') }} <br>
                {{ $t('10+ people: 30% off') }} <br>
                {{ $t('50+ people: 50% off') }}
              </p>

              <div class="mt-4">
                <a href="/app/admin/team/new" title="" class="inline-flex h-12 w-full items-center justify-center gap-2 rounded-lg border border-transparent bg-primary-500 px-4 py-2 text-base font-medium leading-6 text-white shadow-sm transition-all duration-150 hover:bg-gray-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-500 sm:w-auto" role="button">
                  {{ $t('Create your team') }}
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </a>
              </div>

              <div class="mt-4">
                <a href="/app/add" title="" class="text-sm font-normal text-gray-500 underline transition-all duration-150 hover:text-gray-900">Or create just for yourself</a>
              </div>
            </div>

            <div class="h-px w-full bg-gray-300 sm:h-40 sm:w-px" />

            <div>
              <h3 class="text-lg font-bold text-primary-500">
                {{ $t('All photoshoots include') }}
              </h3>

              <ul class="mt-4 space-y-3 text-primary-500">
                <li class="flex items-center gap-2">
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  {{ photoPerStyle * 10 }} {{ $t('headshots/person') }}
                </li>

                <li class="flex items-center gap-2">
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  {{ $t('10 backgrounds & clothing combos') }}
                </li>

                <li class="flex items-center gap-2">
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  {{ $t('2 hours turnaround time') }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <!--:style="`${screenWidth > 768 && $store.state?.user?.countryCode === 'US' ? 'grid-template-columns: repeat(2, minmax(0, 1fr)); max-width:768px' : ''}`"-->
      <div v-show="type === 'individual'" class="lg:mt-8 mx-auto mt-4 space-y-4 md:space-y-0 grid grid-cols-1 gap-0 sm:mt-6 sm:grid-cols-3 md:max-w-5xl">
        <template v-for="price in packages">
          <div v-if="price?.visible" :key="price.packageId" :class="price.tag === 'popular' ? 'border-brand-200' : 'border-gray-200'" class="mx-auto max-w-2xl rounded-lg border bg-white shadow-sm">
            <div class="p-8">
              <div class="flex flex-col gap-6 sm:gap-8 lg:gap-4">
                <div class="">
                  <p class="text-3xl md:text-4xl font-bold text-teal-500">
                    {{ getLocalizedPrice(price.id, true, 0, false) }}
                    <span class="text-base font-normal text-primary-500">/ {{ $t('shoot') }}</span>
                  </p>
                </div>

                <div>
                  <h3 class="text-md md:text-lg font-bold text-primary-500">
                    {{ price.title }} {{ $t('shoot includes') }}
                  </h3>

                  <ul class="mt-4 space-y-1 md:space-y-3 text-primary-500">
                    <li class="flex items-center gap-2">
                      <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                      <strike v-if="price.regularStyles" class="text-red-500">
                        {{ price.meta.regularStyles * photoPerStyle }}
                      </strike>
                      <b>
                        {{ (price.meta.styles + price.meta.additional) * photoPerStyle }}
                      </b>
                      {{ $t('headshots') }}
                    </li>
                    <!-- <li class="flex items-center gap-2">
                      <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                      <strong>{{ price.regenerationCredits }}</strong> {{ $t('edit credits') }}
                      <Tooltip info="Create your own custom headshots by editing exciting photos. Swap face, background, pose and more to get your ideal photo." />
                    </li> -->

                    <li class="flex items-center gap-2">
                      <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                      <strike v-if="price.meta.regularStyles" class="text-red-500">
                        {{ price.meta.regularStyles }}
                      </strike>
                      <b>
                        {{ (price.meta.styles + price.meta.additional) }}
                      </b>
                      {{ $t('unique backgrounds') }}
                    </li>
                    <li class="flex items-center gap-2">
                      <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                      <strike v-if="price.meta.regularStyles" class="text-red-500">
                        {{ price.meta.regularStyles }}
                      </strike>
                      <b>
                        {{ (price.meta.styles + price.meta.additional) }}
                      </b>
                      {{ $t('clothing styles') }}
                    </li>

                    <li class="flex items-center gap-2">
                      <svg class="h-5 w-5 text-gray-900" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                      <span>{{ $t('Done in') }} <strong>{{ (price?.meta?.turnAroundTime ?? '2 hours').replace('hour', $t('hour')) }}</strong></span>
                    </li>
                  </ul>
                </div>
                <div class="mt-4">
                  <a :href="localePath('/app/add')" title="" :class="price?.meta?.tag === 'popular' ? 'border border-transparent bg-brand-500 text-white' : 'border border-brand-500 bg-white text-brand-500 hover:bg-brand-500 hover:text-white'" class="inline-flex h-12 w-full items-center justify-center gap-2 rounded-lg px-4 py-2 text-base font-medium leading-6 shadow-sm transition-all duration-150 hover:bg-gray-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-500 sm:w-auto" role="button">
                    {{ $t('Create your headshots') }}
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>

      <div class="relative mx-auto mt-8 max-w-lg space-y-4 text-center">
        <div class="absolute -top-4 left-14 hidden sm:block">
          <img class="h-12 w-auto" src="@/assets/img/arrow-up.svg" alt="">
        </div>

        <div class="flex items-center justify-center gap-3">
          <div class="flex -space-x-3 overflow-hidden">
            <ImageDns class="inline-block h-10 w-10 rounded-full ring-[3px] ring-white" :src="require('@/assets/img/avatar-01.png')" alt="" />
            <ImageDns class="inline-block h-10 w-10 rounded-full ring-[3px] ring-white" :src="require('@/assets/img/avatar-02.png')" alt="" />
            <ImageDns class="inline-block h-10 w-10 rounded-full ring-[3px] ring-white" :src="require('@/assets/img/avatar-03.png')" alt="" />
            <ImageDns class="inline-block h-10 w-10 rounded-full ring-[3px] ring-white" :src="require('@/assets/img/avatar-04.png')" alt="" />
            <ImageDns class="inline-block h-10 w-10 rounded-full ring-[3px] ring-white" :src="require('@/assets/img/avatar-05.png')" alt="" />
          </div>

          <div class="flex">
            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
          </div>
        </div>

        <p class="text-sm text-gray-500">
          {{ $t('Tens of thousands of people have created their professional photos with our tools. Get matching team photos today.') }}
        </p>
      </div>
      <MarketingLogoCloud class="w-full max-w-2xl mx-auto mt-8" />
    </div>
  </section>
</template>

<script>
export default {
  data () {
    return {
      type: 'individual'
    }
  },
  computed: {
    packages () {
      const packages = this.$store.state.packages
      // if (this.$store.state?.user?.countryCode === 'US') {
      // delete packages.small
      // return packages
      // } else {
      return packages
      // }
      // return this.$store.state.packages
    }
  },
  mounted () {

  }
}
</script>

<i18n>
  {
    "es": {
      "Pricing": "Precios",
      "Professional headshots for 10x less than a physical photo shoot": "Retratos profesionales por 10 veces menos que una sesión de fotos física",
      "According to our 2023 research, see the results over": "Según nuestra investigación de 2023, ve los resultados",
      "here": "aquí",
      "Get high-quality professional corporate business headshots for you or your team without breaking the bank. Our pricing is an affordable option for small and large teams alike.": "Obtenga retratos corporativos de alta calidad para usted o su equipo sin gastar una fortuna. Nuestros precios son una opción asequible tanto para equipos pequeños como grandes.",
      "For teams or individuals": "Para equipos o individuos",
      "Team": "Equipo",
      "Individual": "Individual",
      "per person": "por persona",
      "Create your team": "Crea tu equipo",
      "5+ people: 20% off": "5+ personas: 20% de descuento",
      "10+ people: 30% off": "10+ personas: 30% de descuento",
      "50+ people: 50% off": "50+ personas: 50% de descuento",
      "All photoshoots include": "Todas las sesiones de fotos incluyen",
      "headshots/person": "retratos/persona",
      "10 backgrounds & clothing combos": "10 fondos y combinaciones de ropa",
      "2 hours turnaround time": "Tiempo de entrega de 2 horas",
      "shoot": "sesión",
      "shoot includes": "sesión incluye",
      "headshots": "retratos",
      "edit credits": "créditos de edición",
      "unique backgrounds": "fondos únicos",
      "clothing styles": "estilos de ropa",
      "Done in": "Hecho en",
      "hour": "hora",
      "Tens of thousands of people have created their professional photos with our tools. Get matching team photos today.": "Decenas de miles de personas han creado sus fotos profesionales con nuestras herramientas. Obtén fotos de equipo a juego hoy."
    }
  }
</i18n>
