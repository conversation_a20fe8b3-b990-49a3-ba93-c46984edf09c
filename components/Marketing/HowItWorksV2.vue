<template>
  <section class="max-w-7xl mx-auto flex flex-col pb-16 px-4 md:px-0">
    <div id="step-1" class="max-w-4xl mx-auto">
      <div class="max-w-2xl mx-auto text-center">
        <h2 class="text-md md:text-lg font-bold text-teal-500">
          Get your headshots in three simple steps
        </h2>
        <div class="mx-auto relative h-[80px] my-6">
          <div class="w-[40px] h-[40px] rounded-full bg-teal-500 flex items-center justify-center absolute top-0 left-1/2 transform -translate-x-1/2  z-20">
            <span class="text-white text-[23px] font-bold">1</span>
          </div>
          <div class="w-[4px] bg-teal-500 absolute top-[40px] left-1/2 transform -translate-x-1/2 h-[40px] z-10" />
          <div class="bg-gradient-to-b from-transparent to-white w-[40px] h-[40px] top-[40px] absolute left-1/2 transform -translate-x-1/2  z-10" />
        </div>

        <h2 class="mt-6 text-2xl font-bold tracking-tight sm:text-4xl lg:text-5xl text-primary-500">
          Upload a few selfies
        </h2>
        <p class="mt-4 text-lg font-medium text-gray-600">
          We'll use these to create your headshots.
        </p>
      </div>
      <div class="w-full max-w-4xl mx-auto mt-8 relative">
        <div class="w-full bg-gradient-to-r from-transparent via-white to-transparent h-full absolute top-0 left-0" />
        <div class="hidden md:grid grid-cols-6 gap-4 py-8">
          <template v-for="index in 18">
            <div :key="index" class="w-full h-full md:w-[122px]  md:h-[122px] rounded-md bg-gray-100 overflow-hidden">
              <img :src="require('@/assets/img/selfies/selfie-' + index + '.png')" class="w-full h-full object-cover">
            </div>
          </template>
        </div>
        <div class="grid md:hidden grid-cols-5 gap-4 py-8">
          <template v-for="index in 15">
            <div :key="index" class="w-full h-full md:w-[122px]  md:h-[122px] rounded-md bg-gray-100 overflow-hidden">
              <img :src="require('@/assets/img/selfies/selfie-' + index + '.png')" class="w-full h-full object-cover">
            </div>
          </template>
        </div>
        <div class="left-1/2 transform -translate-x-1/2 top-0 absolute z-10 aspect-[9.2/19] h-full shrink-0 overflow-hidden rounded-[35px] border-[10px] border-black bg-white shadow-xl md:w-[227px]" style="box-shadow: 0px 4px 20px 3px rgb(var(--cl-black) / 0.2);">
          <video
            muted
            loop
            controls="false"
            autoplay
            class="w-full h-full"
          >
            <source
              src="https://storage.googleapis.com/headshotpro-public-content/headshotpro/website/selfie-demo.mp4"
              type="video/mp4"
            >
            Sorry, your browser doesn't support embedded videos.
          </video>
        </div>
      </div>
    </div>
    <div id="step-2" class="max-w-4xl mx-auto">
      <div class="max-w-2xl mx-auto text-center">
        <div class="mx-auto relative h-[80px] my-6 mt-16">
          <div class="w-[40px] h-[40px] rounded-full bg-teal-500 flex items-center justify-center absolute top-0 left-1/2 transform -translate-x-1/2  z-20">
            <span class="text-white text-[23px] font-bold">2</span>
          </div>
          <div class="w-[4px] bg-teal-500 absolute top-[40px] left-1/2 transform -translate-x-1/2 h-[40px] z-10" />
          <div class="bg-gradient-to-b from-transparent to-white w-[40px] h-[40px] top-[40px] absolute left-1/2 transform -translate-x-1/2  z-10" />
          <div class="w-[4px] bg-teal-500 absolute top-[-40px] left-1/2 transform -translate-x-1/2 h-[40px] z-10" />
          <div class="bg-gradient-to-b from-white to-transparent w-[40px] h-[40px] top-[-40px] absolute left-1/2 transform -translate-x-1/2  z-10" />
        </div>

        <h2 class="mt-6 text-2xl font-bold tracking-tight sm:text-4xl lg:text-5xl text-primary-500">
          Our AI goes to work
        </h2>
        <p class="mt-4 text-lg font-medium text-gray-600">
          This is where the magic happens.
        </p>
      </div>
      <div class="mx-auto mt-10 h-[200px] relative max-w-xl ">
        <img src="@/assets/img/learning-1.png" class="w-[200px]  rounded-full animation animate-[pulse_2s_infinite_100ms]  z-20 left-1/2 transform -translate-x-1/2 absolute">
        <img src="@/assets/img/learning-2.png" class="w-[200px] rounded-full animation animate-[pulse_4s_infinite_200ms]  z-10 left-1/2 transform -translate-x-1/2 absolute">
        <img src="@/assets/img/learning-3.png" class="w-[200px] rounded-full  z-0 left-1/2 transform -translate-x-1/2 absolute">
        <div class="ai left-1/2 transform -translate-x-1/2 absolute top-[-55px]">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="310"
            height="310"
            viewBox="0 0 391 391"
            fill="none"
          >
            <g id="ai">

              <g id="g" filter="url(#filter1_i)">
                <path
                  d="M258.925 321.952C217.208 345.095 172.592 342.312 125.078 313.602C77.5637 284.893 55.7755 241.764 59.7134 184.217C63.6513 126.669 92.3392 89.9343 145.777 74.0127C199.215 58.0912 240.138 64.8972 288.549 94.4308C336.959 123.964 352.887 163.482 336.333 212.984C319.778 262.486 300.643 298.809 258.925 321.952Z"
                  fill="white"
                  fill-opacity="0.01"
                />
              </g>
              <g id="f" filter="url(#filter2_i)">
                <path
                  d="M326.448 156.236C346.632 212.15 332.809 259.232 284.98 291.481C237.151 323.731 192.504 332.917 149.039 323.041C105.574 313.164 89.3852 282.81 62.4734 237.98C35.5616 193.149 44.0955 149.683 88.0752 113.583C132.055 77.4825 162.066 60.7542 216.109 63.398C270.151 66.0419 306.264 100.321 326.448 156.236Z"
                  fill="white"
                  fill-opacity="0.01"
                />
              </g>
              <g id="e" filter="url(#filter3_i)">
                <path
                  d="M308.736 272.285C277.494 310.837 241.385 331.695 202.457 330.853C163.529 330.012 131.724 311.363 90.3165 273.652C48.909 235.941 42.6591 180.041 78.2399 130.975C113.821 81.9087 148.433 66.384 203.478 59.8779C267.756 59.878 301.626 79.7019 321.834 139.983C342.042 200.265 339.977 233.733 308.736 272.285Z"
                  fill="white"
                  fill-opacity="0.01"
                />
              </g>
              <g id="d" filter="url(#filter4_i)">
                <path
                  d="M310.393 271.778C277.096 320.326 237.637 341.218 192.017 334.454C146.397 327.691 108.445 303.417 78.163 261.632C47.8806 219.848 47.2907 176.786 76.3933 132.447C105.496 88.1073 144.037 62.6309 192.017 56.0176C239.997 49.4042 280.045 71.5739 312.163 122.527C344.281 173.48 343.691 223.23 310.393 271.778Z"
                  fill="white"
                  fill-opacity="0.01"
                />
              </g>
              <g id="c" filter="url(#filter5_i)">
                <path
                  d="M307.832 268.624C269.508 314.707 224.746 336.931 177.547 333.296C130.347 329.662 95.4519 306.621 72.8607 264.173C50.2695 221.725 51.3869 179.861 70.2129 130.581C89.0389 81.3006 124.741 56.7332 177.319 56.8786C229.898 57.0239 268.542 71.6641 305.253 120.799C341.964 169.934 346.157 222.542 307.832 268.624Z"
                  fill="white"
                  fill-opacity="0.01"
                />
              </g>
              <g id="b" filter="url(#filter6_i)">
                <path
                  d="M331.624 168.687C347.668 221.613 330.95 272.44 279.471 301.168C227.991 329.896 187.577 329.032 145.905 318.035C105.707 305.685 76.9748 280.125 58.6244 235.997C45.9255 192.116 49.6617 164.211 87.3237 111.256C124.986 58.3013 191.752 38.1339 243.897 64.3134C296.041 90.4928 315.581 115.761 331.624 168.687Z"
                  fill="white"
                  fill-opacity="0.01"
                />
              </g>
              <g id="a" filter="url(#filter7_i)">
                <path
                  d="M326.506 247.112C315.692 300.334 286.086 320.463 219.776 328.226C162.35 330.151 125.891 317.84 89.3915 279.281C52.8915 240.723 45.0065 196.243 65.7364 145.84C86.4663 95.437 130.158 67.4141 184.915 60.2048C239.672 52.9955 281.677 69.9023 310.931 110.925C340.185 151.948 337.32 193.89 326.506 247.112Z"
                  fill="white"
                  fill-opacity="0.01"
                />
              </g>
            </g>
            <defs>
              <filter
                id="filter0_i"
                x="58"
                y="65.8779"
                width="274"
                height="260"
                filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB"
              >
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                <feColorMatrix
                  in="SourceAlpha"
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                  result="hardAlpha"
                />
                <feMorphology radius="21" operator="erode" in="SourceAlpha" result="effect1_innerShadow" />
                <feOffset />
                <feGaussianBlur stdDeviation="11" />
                <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
                <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.06 0" />
                <feBlend mode="normal" in2="shape" result="effect1_innerShadow" />
              </filter>
              <filter
                id="filter1_i"
                x="2.5"
                y="2.87793"
                width="386.053"
                height="385.637"
                filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB"
              >
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                <feColorMatrix
                  in="SourceAlpha"
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                  result="hardAlpha"
                />
                <feOffset />
                <feGaussianBlur stdDeviation="10" />
                <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
                <feColorMatrix type="matrix" values="0 0 0 0 0.418229 0 0 0 0 0.448185 0 0 0 0 0.9125 0 0 0 0.77 0" />
                <feBlend mode="normal" in2="shape" result="effect1_innerShadow" />
              </filter>
              <filter
                id="filter2_i"
                x="2"
                y="1.93799"
                width="385.898"
                height="386.199"
                filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB"
              >
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                <feColorMatrix
                  in="SourceAlpha"
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                  result="hardAlpha"
                />
                <feOffset />
                <feGaussianBlur stdDeviation="10" />
                <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
                <feColorMatrix type="matrix" values="0 0 0 0 0.418229 0 0 0 0 0.448185 0 0 0 0 0.9125 0 0 0 0.77 0" />
                <feBlend mode="normal" in2="shape" result="effect1_innerShadow" />
              </filter>
              <filter
                id="filter3_i"
                x="55"
                y="59.8779"
                width="280"
                height="271"
                filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB"
              >
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                <feColorMatrix
                  in="SourceAlpha"
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                  result="hardAlpha"
                />
                <feOffset />
                <feGaussianBlur stdDeviation="10" />
                <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
                <feColorMatrix type="matrix" values="0 0 0 0 0.418229 0 0 0 0 0.448185 0 0 0 0 0.9125 0 0 0 0.77 0" />
                <feBlend mode="normal" in2="shape" result="effect1_innerShadow" />
              </filter>
              <filter
                id="filter4_i"
                x="55"
                y="54.8779"
                width="280.817"
                height="280.817"
                filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB"
              >
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                <feColorMatrix
                  in="SourceAlpha"
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                  result="hardAlpha"
                />
                <feOffset />
                <feGaussianBlur stdDeviation="10" />
                <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
                <feColorMatrix type="matrix" values="0 0 0 0 0.418229 0 0 0 0 0.448185 0 0 0 0 0.9125 0 0 0 0.77 0" />
                <feBlend mode="normal" in2="shape" result="effect1_innerShadow" />
              </filter>
              <filter
                id="filter5_i"
                x="56"
                y="56.8779"
                width="278.738"
                height="276.802"
                filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB"
              >
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                <feColorMatrix
                  in="SourceAlpha"
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                  result="hardAlpha"
                />
                <feOffset />
                <feGaussianBlur stdDeviation="10" />
                <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
                <feColorMatrix type="matrix" values="0 0 0 0 0.418229 0 0 0 0 0.448185 0 0 0 0 0.9125 0 0 0 0.77 0" />
                <feBlend mode="normal" in2="shape" result="effect1_innerShadow" />
              </filter>
              <filter
                id="filter6_i"
                x="0"
                y="0"
                width="390.838"
                height="390.84"
                filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB"
              >
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                <feColorMatrix
                  in="SourceAlpha"
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                  result="hardAlpha"
                />
                <feOffset />
                <feGaussianBlur stdDeviation="10" />
                <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
                <feColorMatrix type="matrix" values="0 0 0 0 0.418229 0 0 0 0 0.448185 0 0 0 0 0.9125 0 0 0 0.77 0" />
                <feBlend mode="normal" in2="shape" result="effect1_innerShadow" />
              </filter>
              <filter
                id="filter7_i"
                x="35"
                y="39.8555"
                width="320.27"
                height="311.235"
                filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB"
              >
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                <feColorMatrix
                  in="SourceAlpha"
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                  result="hardAlpha"
                />
                <feOffset />
                <feGaussianBlur stdDeviation="10" />
                <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
                <feColorMatrix type="matrix" values="0 0 0 0 0.418229 0 0 0 0 0.448185 0 0 0 0 0.9125 0 0 0 0.77 0" />
                <feBlend mode="normal" in2="shape" result="effect1_innerShadow" />
              </filter>
            </defs>
          </svg>
        </div>
      </div>
    </div>
    <div id="step-3" class="max-w-6xl mx-auto">
      <div class="max-w-2xl mx-auto text-center">
        <div class="mx-auto relative h-[80px] my-6 mt-16">
          <div class="w-[40px] h-[40px] rounded-full bg-teal-500 flex items-center justify-center absolute top-0 left-1/2 transform -translate-x-1/2  z-20">
            <span class="text-white text-[23px] font-bold">3</span>
          </div>
          <div class="w-[4px] bg-teal-500 absolute top-[-40px] left-1/2 transform -translate-x-1/2 h-[40px] z-10" />
          <div class="bg-gradient-to-b from-white to-transparent w-[40px] h-[40px] top-[-40px] absolute left-1/2 transform -translate-x-1/2  z-10" />
        </div>

        <h2 class="mt-6 text-2xl font-bold tracking-tight sm:text-4xl lg:text-5xl text-primary-500">
          Get hundreds of headshots
        </h2>
        <p class="mt-4 text-xl font-medium text-gray-600">
          Indistinguishable from real photos. No physical shoot needed.
        </p>
      </div>
      <div class="mt-8">
        <div class="hidden md:flex items-center justify-center w-full space-x-2 text-sm animate-pulse mt-4">
          <IconChevron class="w-4 h-4 text-black/50 transform rotate-90" />
          <span>These photos are not real. All of them were created with our AI headshot generator</span>
          <IconChevron class="w-4 h-4 text-black/50 transform rotate-90" />
        </div>
        <div class="block relative">
          <div class="hidden md:grid grid-cols-4 md:grid-cols-8 gap-1 mt-4">
            <ImageDns v-for="index of 16" :key="index" :src="require(`@/assets/img/examples/${index + 29}.jpg`)" />
          </div>
          <div class="grid md:hidden grid-cols-4 md:grid-cols-8 gap-1 mt-4">
            <ImageDns v-for="index of 8" :key="index" :src="require(`@/assets/img/examples/${index + 29}.jpg`)" />
          </div>
          <div class="w-full h-full bg-gradient-to-r from-white via-transparent to-white absolute top-0 left-0" />
        </div>
      </div>
    </div>
    <div id="step-4" class="max-w-2xl mx-auto flex flex-col items-center">
      <div class=" bg-yellow-500/10 rounded-xl my-8 p-6 w-full">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <nuxt-link to="/auth/login?redirect=%2Fapp" class="w-full">
            <ButtonPrimary class="w-full">
              Get your headshots now
            </ButtonPrimary>
          </nuxt-link>
          <nuxt-link to="/reviews" class="w-full">
            <ButtonWhite class="w-full">
              <IconSolidEye class="w-4 h-4 text-black/70 mr-1.5" />
              <span>View all reviews</span>
            </ButtonWhite>
          </nuxt-link>
        </div>
      </div>
      <div class="flex items-center space-x-4">
        <div class="flex -space-x-3 overflow-hidden">
          <img class="inline-block h-10 w-10 rounded-full border-[3px] border-white" src="@/assets/img/avatar-01.png" alt="">
          <img class="inline-block h-10 w-10 rounded-full border-[3px] border-white" src="@/assets/img/avatar-02.png" alt="">
          <img class="inline-block h-10 w-10 rounded-full border-[3px] border-white" src="@/assets/img/avatar-03.png" alt="">
          <img class="hidden md:inline-block h-10 w-10 rounded-full border-[3px] border-white" src="@/assets/img/avatar-04.png" alt="">
          <img class="hidden md:inline-block h-10 w-10 rounded-full border-[3px] border-white" src="@/assets/img/avatar-05.png" alt="">
        </div>
        <p class="mx-auto max-w-lg text-sm text-gray-500 lg:mx-0">
          <strong>3,439,182</strong> AI headshots already created
        </p>
        <div class="hidden md:flex">
          <IconSolidStar v-for="index in 5" :key="index" class="w-4 h-4 text-yellow-400" />
        </div>
      </div>
      <div class="flex items-center justify-center gap-3 lg:justify-start mt-4">
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-600 hidden md:flex">As seen on: </span>
          <img src="@/assets/img/logo-1.png" class="w-[40px] md:w-[60px] grayscale">
          <img src="@/assets/img/logo-2.png" class="w-[40px] md:w-[60px] grayscale">
          <img src="@/assets/img/logo-3.png" class="w-1/5 md:w-[100px] grayscale">
          <img src="@/assets/img/logo-4.png" class="w-1/5 md:w-[100px] grayscale">
          <img src="@/assets/img/logo-5.png" class="w-1/5 md:w-[100px] grayscale">
        </div>
      </div>
    </div>
  </section>
</template>

<script>
// import { CollapseTransition } from '@ivanv/vue-collapse-transition'
export default {
  // components: {
  //   CollapseTransition
  // },
  data () {
    return {
      type: 'individual',
      active: 0,
      hiwTeam: [
        { question: 'Create your team', answer: 'To get started, create a team on our platform. You can easily add your colleagues and team members by entering their names and email addresses.' },
        { question: 'Select a style', answer: "Next, choose the style of headshot that best represents your brand. Whether you prefer a traditional, formal look or something more creative and modern, we've got you covered. Our AI-photographer will generate multiple options for you to choose from." },
        { question: 'Invite your collegues', answer: 'Invite your colleagues to join your team and upload their selfies. You can easily send them an invitation email or share a link.' },
        { question: 'Upload their selfies', answer: 'Once your colleagues have accepted the invitation, they can upload their selfies to the platform. We recommend taking the selfies against a plain background, with good lighting and a natural smile.' },
        { question: 'AI-photographer generates headshots', answer: 'Our state-of-the-art AI-photographer will then generate over 120+ headshots for each team member. The AI will use advanced algorithms to optimize each photo for lighting, focus, and color, resulting in stunning, professional headshots.' },
        { question: 'Download your favorites', answer: 'Finally, you and your team can browse through the generated headshots and select your favorites. You can download the selected headshots in high resolution and use them on your website, LinkedIn profiles, business cards, and other marketing materials.' }
      ],
      hiwIndividual: [
        { question: 'Select a style', answer: "Choose the style of headshot that best represents you. Whether you prefer a traditional, formal look or something more creative and modern, we've got you covered. Our AI-photographer will generate multiple options for you to choose from." },
        { question: 'Upload your selfies', answer: 'Upload your selfies to the platform. We recommend taking the selfies against a plain background, with good lighting and a natural smile.' },
        { question: 'AI-photographer generates headshots', answer: 'Our state-of-the-art AI-photographer will then generate over 120+ headshots for each team member. The AI will use advanced algorithms to optimize each photo for lighting, focus, and color, resulting in stunning, professional headshots.' },
        { question: 'Download your favorites', answer: 'Finally, you can browse through the generated headshots and select your favorites. You can download the selected headshots in high resolution and use them on your website, LinkedIn profiles, business cards, and other marketing materials.' }
      ]
    }
  },
  computed: {
    hiw () {
      if (this.type === 'team') {
        return this.hiwTeam
      } else {
        return this.hiwIndividual
      }
    }
  },
  methods: {
    createTeam () {
      const url = '/app/admin/team/new'
      if (this.companyName && this.teamSize) {
        this.$router.push({ path: url, query: { companyName: this.companyName, teamSize: this.teamSize } })
      } else {
        this.$router.push({ path: url })
      }
    }
  }
}
</script>

<style scoped>
.smooth-enter-active,
.smooth-leave-active {
  transition: max-height 0.5s;
  overflow: hidden;
}
.smooth-enter,
.smooth-leave-to {
  max-height: 0;
}

svg g {
  transform-origin: center;
  transform-box: fill-box;
}

#a {
  animation: clockwise 8s linear infinite;

}

#b {
  animation: clockwise 10s linear infinite;
}

#d {
  animation: clockwise 12s linear infinite;
}

#f {
  animation: clockwise 9s linear infinite;
}

#c {
  animation: anticlockwise 9s linear infinite;
}

#e {
  animation: anticlockwise 11s linear infinite;
}

/* for 0 to 360deg rotation */
@keyframes clockwise {
  from {
    transform: rotatez(0deg);

  }

  to {
    transform: rotatez(360deg);
  }
}

/* for 0 to -360deg rotation */
@keyframes anticlockwise {
  from {
    transform: rotatez(0deg);

  }

  to {
    transform: rotatez(-360deg);
  }
}

/* for smart-phone */
@media only screen and (max-width: 600px) {
  svg {
    width: fit-content;
    margin: 10px;
  }
}
</style>
