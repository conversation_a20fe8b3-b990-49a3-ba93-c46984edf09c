<template>
  <div>
    <section class="md:bg-white bg-teal-200 relative overflow-hidden w-full max-w-full">
      <div class="hero" />
      <div class="hidden sm:block w-full bg-gradient-to-r from-white/10 via-white/90 to-white/10 absolute top-0 left-0 h-full z-10 backdrop-blur-[1px]" style="background-image: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.1));" />
      <div class="hidden sm:block w-full bg-gradient-to-b from-white/80 to-white/20 absolute top-0 left-0 h-full z-10" style="background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.2));" />
      <div class="sm:hidden w-full bg-gradient-to-b from-white/90 via-white/90 to-white/70 absolute top-0 left-0 h-full z-10" style="background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));" />
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 relative z-20">
        <div class="flex flex-col lg:flex-row lg:items-center sm:justify-center">
          <div class="mx-auto text-left w-full shrink-0 space-y-3 py-12 sm:py-24  md:text-center lg:mx-0 sm:text-center max-w-[620px]">
            <h1 class="text-base font-medium text-gray-800 sm:text-xl">
              The #1 Professional AI Headshot Generator
            </h1>
            <h2 class="text-2xl leading-[1.6rem] font-bold tracking-tight text-primary-500 sm:text-4xl sm:leading-[3rem] lg:text-[38px]">
              {{ headline }}
            </h2>
            <p id="convert-3-v1-p" class="text-sm leading-[1.4rem] md:text-lg font-normal md:leading-[1.8rem] text-gray-600">
              Get professional business headshots in minutes with our AI-photographer. Upload photos, pick your styles & receive 120+ headshots.
            </p>

            <div class="relative space-y-5">
              <div class="space-x-2">
                <div class="flex gap-4 items-center content-center sm:justify-center sm:gap-6">
                  <template v-if="!isLoggedIn">
                    <nuxt-link :to="'/auth/login?redirect=' + encodeURIComponent('/app')">
                      <button title="" class="inline-flex h-12 w-full items-center justify-center gap-2 rounded-lg border border-transparent bg-orange-500 px-4 py-2 text-base font-medium leading-6 text-white shadow-sm transition-all duration-150 hover:bg-gray-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-500 sm:w-auto" role="button">
                        <span class="block md:hidden">Start now</span>
                        <span class="hidden md:block">Get your headshots</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                      </button>
                    </nuxt-link>

                    <span class="text-sm font-bold uppercase tracking-wide text-gray-400">Or</span>

                    <nuxt-link to="/company-headshots">
                      <ButtonWhite title="" class="text-sm font-medium  transition-all duration-150 hover:opacity-90">
                        <span class="block md:hidden">For teams</span>
                        <span class="md:block hidden">For companies & teams</span>
                      </ButtonWhite>
                    </nuxt-link>
                  </template>
                  <template v-else>
                    <nuxt-link to="/app">
                      <ButtonPrimary class="w-full md:auto">
                        Access your photos
                      </ButtonPrimary>
                    </nuxt-link>
                  </template>
                </div>
              </div>
              <div class="flex flex-col space-y-1 md:hidden">
                <div class="flex items-center justify-start">
                  <IconCheck class="w-3 h-3 text-green-500 mr-1.5" />
                  <span class="text-xs text-gray-700">Done in 2 hours, fast turn-around</span>
                </div>
                <div class="flex items-center">
                  <IconCheck class="w-3 h-3 text-green-500 mr-1.5" />
                  <p class=" text-xs font-normal text-gray-700">
                    Used by {{ $store.state.stats.users }} happy customers
                  </p>
                  <div class="flex items-center space-x-px ml-2">
                    <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                      />
                    </svg>
                    <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                      />
                    </svg>
                    <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                      />
                    </svg>
                    <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                      />
                    </svg>
                    <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                      />
                    </svg>
                  </div>
                </div>
                <div class="flex items-center justify-start">
                  <IconCheck class="w-3 h-3 text-green-500 mr-1.5" />
                  <span class="text-xs text-gray-700">{{ $store.state.stats.photos }}+ professional headshots already created</span>
                </div>
              </div>
              <div class="hidden md:flex items-center justify-center space-x-4">
                <div class="flex -space-x-3 overflow-hidden">
                  <ImageDns width="40" height="40" class="aspect-[1/1] inline-block h-10 w-10 rounded-full border-[3px] border-white" :src="require('@/assets/img/avatar-01.png')" alt="" />
                  <ImageDns width="40" height="40" class="aspect-[1/1] inline-block h-10 w-10 rounded-full border-[3px] border-white" :src="require('@/assets/img/avatar-02.png')" alt="" />
                  <ImageDns width="40" height="40" class="aspect-[1/1] inline-block h-10 w-10 rounded-full border-[3px] border-white" :src="require('@/assets/img/avatar-03.png')" alt="" />
                  <ImageDns width="40" height="40" class="aspect-[1/1] inline-block h-10 w-10 rounded-full border-[3px] border-white" :src="require('@/assets/img/avatar-04.png')" alt="" />
                  <ImageDns width="40" height="40" class="aspect-[1/1] inline-block h-10 w-10 rounded-full border-[3px] border-white" :src="require('@/assets/img/avatar-05.png')" alt="" />
                </div>
                <p class="mx-auto max-w-lg text-sm text-gray-500 lg:mx-0">
                  <strong>{{ $store.state.stats.photos }}+</strong> AI headshots already created
                </p>
                <div class="flex convert-13-v1">
                  <IconSolidStar v-for="index in 5" :key="index" class="w-4 h-4 text-yellow-400" />
                </div>
              </div>
              <div class="hidden md:flex items-center justify-center gap-3">
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-gray-600 hidden md:flex">As seen on: </span>
                  <ImageDns width="60" height="30" :src="require('@/assets/img/logo-1.png')" class="w-[40px] md:w-[60px] grayscale" />
                  <ImageDns width="60" height="30" :src="require('@/assets/img/logo-2.png')" class="w-[40px] md:w-[60px] grayscale" />
                  <ImageDns width="100" height="32" :src="require('@/assets/img/logo-3.png')" class="w-1/5 md:w-[100px] grayscale" />
                  <ImageDns width="100" height="32" :src="require('@/assets/img/logo-4.png')" class="w-1/5 md:w-[100px] grayscale" />
                  <ImageDns width="100" height="32" :src="require('@/assets/img/logo-5.png')" class="w-1/5 md:w-[100px] grayscale" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <div class="relative hidden md:block">
      <div class="hidden sm:grid absolute inset-0" aria-hidden="true">
        <div class="bg-teal-100" />
      </div>

      <div class=" mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="isolate mx-auto max-w-5xl rounded-lg border-2 border-primary-500 bg-white px-6 py-5 shadow-lg sm:px-8 top-[-30px] relative z-20">
          <div class="flex flex-col gap-6 lg:flex-row lg:items-end">
            <ImageDns width="956" height="48" :src="require('@/assets/img/steps-hero.png')" />
          </div>
        </div>
      </div>
    </div>
    <div class="md:hidden">
      <ImageDns :src="require('@/assets/img/hero-mobile-bg.jpg')" class="w-full" />
      <!-- <div class="grid grid-cols-5">
        <ImageDns v-for="item of reviews.slice(0, 10)" :key="item._id" :src="item.image" />
      </div> -->
    </div>
  </div>
</template>

<script>
export default {
  props: {
    headline: {
      type: String,
      default: 'Professional business headshots, without a physical photo shoot'
    }
  },
  data () {
    return {
      companyName: null,
      teamSize: null
    }
  },
  computed: {
    reviews () {
      return this.$store.state.reviews
    },
    title () {
      const { utm_campaign: utmCampaign } = this.$route.query
      if (utmCampaign === 'ai-headshots') { return 'AI generated headshots, without physical photo shoot' }
      if (utmCampaign === 'profile-picture') { return 'AI generated profile pictures, without physical photo shoot' }
      return 'Professional business headshots, without a physical photo shoot'
    }
  },
  methods: {
    createTeam () {
      const url = '/app/admin/team/new'
      if (this.companyName && this.teamSize) {
        this.$router.push({ path: url, query: { companyName: this.companyName, teamSize: this.teamSize } })
      } else {
        this.$router.push({ path: url })
      }
    }
  }
}
</script>

<style>
@media all and (min-width:768px){
  .hero{
    background-image:url('@/assets/img/hero-examples-moving.jpg');
    background-size:100%;
    background-position: 0 10%;
    /* background-size:cover; */
    position: absolute;
    top: 0;
    left: 0;
    width: 200%; /* Double the width to cover the full container */
    height: 100%;
    animation: moveBackground 100s linear infinite; /* Adjust the animation duration as needed */

  }
    @keyframes moveBackground {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-50%); /* Move the image to the left by half of its width */
    }
  }
}

</style>
