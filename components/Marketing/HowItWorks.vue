<template>
  <section id="how-it-works" class="bg-gray-50 py-12 sm:py-16 lg:py-20 xl:py-24">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="max-w-3xl">
        <h2 class=" text-md md:text-lg font-bold text-teal-500">
          {{ $t('How it works') }}
        </h2>
        <h2 class="mt-6 text-2xl font-bold tracking-tight text-primary-500 sm:text-4xl">
          {{ $t('The fastest and easiest way to get you or your team professional corporate headshots') }}
        </h2>
      </div>

      <div class="mt-8 flex justify-start">
        <fieldset class="grid grid-cols-2 gap-x-1 rounded-full p-1 text-center text-xs font-medium leading-5 ring-1 ring-inset ring-gray-200">
          <legend class="sr-only">
            {{ $t('For teams or individuals') }}
          </legend>

          <label :class="type === 'team' ? 'bg-brand-500 text-white' : 'text-gray-500 bg-white'" class="cursor-pointer rounded-full py-1 px-2.5">
            <input v-model="type" type="radio" name="type" value="team" class="sr-only">
            <span>{{ $t('Team') }}</span>
          </label>

          <label :class="type === 'individual' ? 'bg-brand-500 text-white' : 'text-gray-500 bg-white'" class="cursor-pointer rounded-full py-1 px-2.5">
            <input v-model="type" type="radio" name="type" value="individual" class="sr-only">
            <span>{{ $t('Individual') }}</span>
          </label>
        </fieldset>
      </div>

      <div class="mt-12 grid grid-cols-1 items-stretch gap-8 sm:mt-10 sm:gap-12 lg:grid-cols-2 lg:gap-16">
        <div x-data="{ active: 1 }" class="space-y-5">
          <div v-for="(item, index) in hiw" :key="`hiw-${index}`" role="region" class="border-b border-gray-200 pb-5">
            <h4>
              <button class="flex w-full items-center justify-between text-left text-base font-bold text-gray-900" @click="active = index">
                <span>{{ index + 1 }}. {{ item.question }}</span>
                <span v-if="active === index" aria-hidden="true" class="ml-4">
                  <svg class="h-5 w-5 text-gray-900" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </span>
                <span v-else aria-hidden="true" class="ml-4">
                  <svg class="h-5 w-5 text-gray-900" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                  </svg>
                </span>
              </button>
            </h4>

            <collapse-transition>
              <div v-if="active === index">
                <div class="space-y-5 pt-4">
                  <p class="text-sm md:text-base font-normal text-gray-900">
                    {{ item.answer }}
                  </p>
                </div>
              </div>
            </collapse-transition>
          </div>
        </div>

        <div class="hidden md:flex">
          <template v-if="type === 'team'">
            <ImageDns class="h-full w-full rounded-lg object-cover border border-gray-100" :src="require(`@/assets/img/how-it-works-${active}.png`)" alt="" />
          </template>
          <template v-else>
            <ImageDns v-if="active === 0" alt="How it works" class="h-full w-full rounded-lg object-cover border border-gray-100" :src="require(`@/assets/img/how-it-works-1.png`)" />
            <ImageDns v-if="active === 1" alt="How it works" class="h-full w-full rounded-lg object-cover border border-gray-100" :src="require(`@/assets/img/how-it-works-3.png`)" />
            <ImageDns v-if="active === 2" alt="How it works" class="h-full w-full rounded-lg object-cover border border-gray-100" :src="require(`@/assets/img/how-it-works-4.png`)" />
            <ImageDns v-if="active === 3" alt="How it works" class="h-full w-full rounded-lg object-cover border border-gray-100" :src="require(`@/assets/img/how-it-works-5.png`)" />
          </template>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import { CollapseTransition } from '@ivanv/vue-collapse-transition'
export default {
  components: {
    CollapseTransition
  },
  data () {
    return {
      type: 'individual',
      active: 0,
      hiwTeam: [
        {
          question: this.$t('Create your team'),
          answer: this.$t('To get started, create a team on our platform. You can easily add your colleagues and team members by entering their names and email addresses.')
        },
        {
          question: this.$t('Select a style'),
          answer: this.$t("Next, choose the style of headshot that best represents your brand. Whether you prefer a traditional, formal look or something more creative and modern, we've got you covered. Our AI-photographer will generate multiple options for you to choose from.")
        },
        {
          question: this.$t('Invite your collegues'),
          answer: this.$t('Invite your colleagues to join your team and upload their selfies. You can easily send them an invitation email or share a link.')
        },
        {
          question: this.$t('Upload their selfies'),
          answer: this.$t('Once your colleagues have accepted the invitation, they can upload their selfies to the platform. We recommend taking the selfies against a plain background, with good lighting and a natural smile.')
        },
        {
          question: this.$t('AI-photographer generates headshots'),
          answer: this.$t('Our state-of-the-art AI-photographer will then generate over 120+ headshots for each team member. The AI will use advanced algorithms to optimize each photo for lighting, focus, and color, resulting in stunning, professional headshots.')
        },
        {
          question: this.$t('Download your favorites'),
          answer: this.$t('Finally, you and your team can browse through the generated headshots and select your favorites. You can download the selected headshots in high resolution and use them on your website, LinkedIn profiles, business cards, and other marketing materials.')
        }
      ],
      hiwIndividual: [
        {
          question: this.$t('Select a style'),
          answer: this.$t("Choose the style of headshot that best represents you. Whether you prefer a traditional, formal look or something more creative and modern, we've got you covered. Our AI-photographer will generate multiple options for you to choose from.")
        },
        {
          question: this.$t('Upload your selfies'),
          answer: this.$t('Upload your selfies to the platform. We recommend taking the selfies against a plain background, with good lighting and a natural smile.')
        },
        {
          question: this.$t('AI-photographer generates headshots'),
          answer: this.$t('Our state-of-the-art AI-photographer will then generate over 120+ headshots for each team member. The AI will use advanced algorithms to optimize each photo for lighting, focus, and color, resulting in stunning, professional headshots.')
        },
        {
          question: this.$t('Download your favorites'),
          answer: this.$t('Finally, you can browse through the generated headshots and select your favorites. You can download the selected headshots in high resolution and use them on your website, LinkedIn profiles, business cards, and other marketing materials.')
        }
      ]
    }
  },
  computed: {
    hiw () {
      if (this.type === 'team') {
        return this.hiwTeam
      } else {
        return this.hiwIndividual
      }
    }
  }
}
</script>

<style scoped>
.smooth-enter-active,
.smooth-leave-active {
  transition: max-height 0.5s;
  overflow: hidden;
}
.smooth-enter,
.smooth-leave-to {
  max-height: 0;
}
</style>

<i18n>
  {
    "es": {
      "How it works": "Cómo funciona",
      "The fastest and easiest way to get you or your team professional corporate headshots": "La forma más rápida y sencilla de obtener retratos corporativos profesionales para ti o tu equipo",
      "For teams or individuals": "Para equipos o individuos",
      "Team": "Equipo",
      "Individual": "Individual",
      "Create your team": "Crea tu equipo",
      "Select a style": "Selecciona un estilo",
      "Invite your collegues": "Invita a tus colegas",
      "Upload their selfies": "Sube sus selfies",
      "AI-photographer generates headshots": "El fotógrafo de IA genera retratos",
      "Download your favorites": "Descarga tus favoritas",
      "Upload your selfies": "Sube tus selfies",
      "AI-photographer generates headshots": "Nuestra IA genera tus fotos",
      "To get started, create a team on our platform. You can easily add your colleagues and team members by entering their names and email addresses.": "Para empezar, crea un equipo en nuestra plataforma. Puedes añadir fácilmente a tus colegas y miembros del equipo introduciendo sus nombres y direcciones de correo electrónico.",
      "Next, choose the style of headshot that best represents your brand. Whether you prefer a traditional, formal look or something more creative and modern, we've got you covered. Our AI-photographer will generate multiple options for you to choose from.": "A continuación, elige el estilo de retrato que mejor represente tu marca. Ya sea que prefieras un look tradicional y formal o algo más creativo y moderno, nosotros te cubrimos. Nuestro fotógrafo de IA generará múltiples opciones para que elijas.",
      "Invite your colleagues to join your team and upload their selfies. You can easily send them an invitation email or share a link.": "Invita a tus colegas a unirse a tu equipo y subir sus selfies. Puedes enviarles fácilmente un correo electrónico de invitación o compartir un enlace.",
      "Once your colleagues have accepted the invitation, they can upload their selfies to the platform. We recommend taking the selfies against a plain background, with good lighting and a natural smile.": "Una vez que tus colegas hayan aceptado la invitación, pueden subir sus selfies a la plataforma. Recomendamos tomar las selfies contra un fondo liso, con buena iluminación y una sonrisa natural.",
      "Our state-of-the-art AI-photographer will then generate over 120+ headshots for each team member. The AI will use advanced algorithms to optimize each photo for lighting, focus, and color, resulting in stunning, professional headshots.": "Nuestro fotógrafo de IA de última generación generará más de 120 retratos para cada miembro del equipo. La IA utilizará algoritmos avanzados para optimizar cada foto en cuanto a iluminación, enfoque y color, lo que resultará en impresionantes retratos profesionales.",
      "Finally, you and your team can browse through the generated headshots and select your favorites. You can download the selected headshots in high resolution and use them on your website, LinkedIn profiles, business cards, and other marketing materials.": "Por último, tú y tu equipo pueden navegar por los retratos generados y seleccionar tus favoritos. Puedes descargar los retratos seleccionados en alta resolución y utilizarlos en tu sitio web, perfiles de LinkedIn, tarjetas de presentación y otros materiales de marketing.",
      "Choose the style of headshot that best represents you. Whether you prefer a traditional, formal look or something more creative and modern, we've got you covered. Our AI-photographer will generate multiple options for you to choose from.": "Elige el estilo de retrato que mejor te represente. Ya sea que prefieras un look tradicional y formal o algo más creativo y moderno, nosotros te cubrimos. Nuestro fotógrafo de IA generará múltiples opciones para que elijas.",
      "Upload your selfies to the platform. We recommend taking the selfies against a plain background, with good lighting and a natural smile.": "Sube tus selfies a la plataforma. Recomendamos tomar las selfies contra un fondo liso, con buena iluminación y una sonrisa natural.",
      "Our state-of-the-art AI-photographer will then generate over 120+ headshots for each team member. The AI will use advanced algorithms to optimize each photo for lighting, focus, and color, resulting in stunning, professional headshots.": "Nuestro fotógrafo de IA de última generación generará más de 120 retratos para cada miembro del equipo. La IA utilizará algoritmos avanzados para optimizar cada foto en cuanto a iluminación, enfoque y color, lo que resultará en impresionantes retratos profesionales.",
      "Finally, you can browse through the generated headshots and select your favorites. You can download the selected headshots in high resolution and use them on your website, LinkedIn profiles, business cards, and other marketing materials.": "Por último, puedes navegar por los retratos generados y seleccionar tus favoritos. Puedes descargar los retratos seleccionados en alta resolución y utilizarlos en tu sitio web, perfiles de LinkedIn, tarjetas de presentación y otros materiales de marketing."
    }
  }
</i18n>
