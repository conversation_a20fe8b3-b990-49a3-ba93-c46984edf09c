<template>
  <section class="hidden sm:block bg-teal-100 pb-12">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="flex flex-col items-center justify-center gap-8 md:flex-row lg:gap-16">
        <div>
          <h3 class="text-md lg:text-lg font-bold text-primary-500">
            All photoshoots include
          </h3>

          <ul class="mt-4 text-sm xl:text-base space-y-1 md:space-y-3 text-primary-500">
            <li class="flex items-center gap-2">
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              {{ (photoPerStyle*8) }} headshots per person
            </li>

            <li class="flex items-center gap-2">
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              8 unique locations per shoot
            </li>

            <li class="flex items-center gap-2">
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              High quality photo size
            </li>
          </ul>
        </div>

        <div class="h-px w-full bg-teal-300 md:h-28 md:w-px" />
        <!-- 13 V2 -->
        <div class="space-y-6 convert-13-v2 hidden">
          <div class="flex flex-col justify-center">
            <div class="flex mx-auto items-center justify-center space-x-px">
              <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path
                  d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                />
              </svg>
              <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path
                  d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                />
              </svg>
              <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path
                  d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                />
              </svg>
              <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path
                  d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                />
              </svg>
              <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path
                  d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                />
              </svg>
            </div>
            <p class="mt-4 text-brand-500">
              Used by <span class=" font-bold"> {{ $store.state.stats.users }}</span> happy customers!
            </p>
          </div>
          <div class="flex items-center justify-center gap-6 md:flex-col md:gap-2 lg:flex-row lg:gap-6">
            <div class="flex items-center gap-2">
              <p class="text-5xl font-bold text-primary-500">
                2
              </p>
              <p class="text-sm font-bold uppercase leading-none text-primary-500">
                hours
                <br>
                done
              </p>
            </div>
            <p class="text-base font-normal text-primary-500">
              fast turn around
            </p>
          </div>
        </div>
        <!-- END -->
        <!-- V13 Original -->
        <div class="space-y-6 convert-13-v1">
          <div class="flex items-center gap-6 md:flex-col md:gap-2 lg:flex-row lg:gap-6">
            <p class="text-5xl font-bold text-primary-500">
              <template v-if="!$route.fullPath.includes('company-headshots')">
                {{ price }}
              </template>
              <template v-else>
                $19
              </template>
            </p>
            <p class="text-base font-normal text-primary-500">
              starting at <br>
              per person
            </p>
          </div> <div class="flex items-center gap-6 md:flex-col md:gap-2 lg:flex-row lg:gap-6">
            <div class="flex items-center gap-2">
              <p class="text-5xl font-bold text-primary-500">
                2
              </p> <p class="text-sm font-bold uppercase leading-none text-primary-500">
                hours
                <br>
                done
              </p>
            </div> <p class="text-base font-normal text-primary-500">
              fast turn around
            </p>
          </div>
        </div>
        <!-- END -->

        <div class="hidden md:flex h-px w-full bg-teal-300 md:h-28 md:w-px" />

        <div class="hidden sm:block">
          <h3 class="text-md lg:text-lg font-bold text-primary-500">
            Why choose AI headshots?
          </h3>

          <ul class="mt-4 text-sm xl:text-base space-y-1 md:space-y-3 text-primary-500">
            <li class="flex items-center gap-2">
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              Indistinguishable from real photos
            </li>

            <li class="flex items-center gap-2">
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              No need for any physical shoot
            </li>

            <li class="flex items-center gap-2">
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              Matching photos, no matter where
            </li>
          </ul>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  computed: {
    price () {
      try {
        const priceId = this.$store.state.packages.small.id
        return this.getLocalizedPrice(priceId, true, 0, true)
      } catch (err) {
        return 29
      }
    }
  }
}
</script>

<style></style>
