<template>
  <div
    :class="{
      'relative w-full h-full bg-black': !asCard,
      'relative': asCard,
    }"
  >
    <transition v-for="(testimonial, index) in $store.state.testimonials" :key="testimonial.id" name="fade" :mode="asCard ? 'in-out' : 'out-in'">
      <div
        v-show="currentSlide === index"
        class="transition-opacity duration-500"
        :class="{
          'absolute w-full h-full': !asCard
        }"
      >
        <img v-if="!asCard" :src="testimonial.imageUrl" class="absolute left-0 top-0 w-full h-full object-cover">
        <div
          :class="{
            'bg-gradient-to-t from-black to-transparent absolute left-0 top-0 w-full h-full flex items-end justify-start p-8': !asCard,
            'border border-gray-100 shadow rounded-xl bg-white p-8': asCard
          }"
        >
          <div
            :class="{
              'space-y-7 text-white': !asCard,
              'space-y-4 text-slate-600': asCard
            }"
          >
            <template v-if="!asCard">
              <img v-if="testimonial.logoUrl" :alt="testimonial.logoAlt" :src="testimonial.logoUrl" class="block w-auto h-10">
              <component :is="testimonial.logoComponent" v-else-if="testimonial.logoComponent" class="block w-auto h-10" />
            </template>
            <img v-if="asCard" :src="testimonial.imageUrl" class="h-[60px] w-[60px] rounded-full object-top object-cover">
            <blockquote
              :class="{
                'space-y-4': asCard
              }"
            >
              <p
                :class="{
                  'text-[22px] leading-snug': !asCard,
                  'text-base': asCard
                }"
              >
                "{{ testimonial.quote }}"
              </p>
              <footer
                v-if="testimonial.footer"
                :class="{
                  'text-lg': !asCard,
                  'text-slate-500 text-sm': asCard
                }"
              >
                {{ testimonial.footer }}
              </footer>
            </blockquote>
            <template v-if="asCard">
              <div class="flex justify-between">
                <img v-if="testimonial.logoUrl" :alt="testimonial.logoAlt" :src="testimonial.logoUrl" class="block w-auto h-[26px]">
                <component :is="testimonial.logoComponent" v-else-if="testimonial.logoComponent" class="block w-auto h-[26px] text-blue-500" />
                <div v-else />
                <svg width="43" height="36" viewBox="0 0 43 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M42.8957 35.0649H26.6557C25.069 29.9316 24.2757 24.6116 24.2757 19.1049C24.2757 13.5049 25.7224 9.07156 28.6157 5.80489C31.6024 2.44489 35.989 0.764893 41.7757 0.764893V8.60489C37.109 8.60489 34.7757 11.4516 34.7757 17.1449V19.8049H42.8957V35.0649ZM19.2357 35.0649H2.99569C1.40903 29.9316 0.615692 24.6116 0.615692 19.1049C0.615692 13.5049 2.06236 9.07156 4.95569 5.80489C7.94236 2.44489 12.329 0.764893 18.1157 0.764893V8.60489C13.449 8.60489 11.1157 11.4516 11.1157 17.1449V19.8049H19.2357L19.2357 35.0649Z" fill="#F1F5F9" />
                </svg>
              </div>
            </template>
            <template v-if="!asCard">
              <nav class="space-x-2.5">
                <button
                  v-for="(testimonial, navIndex) in $store.state.testimonials"
                  :key="testimonial.id"
                  :class="{
                    'bg-white': navIndex === currentSlide,
                    'hover:bg-white hover:bg-opacity-50': navIndex !== currentSlide,
                  }"
                  class="w-3 h-3 border border-white rounded-full cursor-pointer"
                  :aria-label="'Go to slide ' + (navIndex + 1)"
                  @click="setSlide(navIndex)"
                />
              </nav>
            </template>
          </div>
        </div>
      </div>
    </transition>
    <template v-if="asCard">
      <nav class="space-x-2.5 flex justify-center mt-2.5">
        <button
          v-for="(testimonial, navIndex) in $store.state.testimonials"
          :key="testimonial.id"
          :class="{
            'bg-slate-600 border-slate-600': navIndex === currentSlide,
            'border-slate-400 hover:bg-slate-400': navIndex !== currentSlide,
          }"
          class="w-3 h-3 border rounded-full cursor-pointer"
          :aria-label="'Go to slide ' + (navIndex + 1)"
          @click="setSlide(navIndex)"
        />
      </nav>
    </template>
  </div>
</template>

<script>
// import IconLogoBox from '../icon/logo/Box.vue'
import Block from '../landingpage/solution/Block.vue'

export default {
  components: { Block },
  props: {
    asCard: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      currentSlide: 0,
      interval: null
    }
  },
  mounted () {
    this.autoSlide()
  },
  methods: {
    setSlide (index) {
      this.currentSlide = index
      this.autoSlide()
    },
    autoSlide () {
      if (this.interval) {
        clearInterval(this.interval)
      }

      this.interval = setInterval(() => {
        this.currentSlide = this.currentSlide === this.$store.state.testimonials.length - 1 ? 0 : this.currentSlide + 1
      }, 3000)
    }
  }
}
</script>

<style>
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}
.fade-enter-to, .fade-leave-from {
  opacity: 1;
}
</style>

<i18n>
  {
    "es": {
      "In a jam to send a prof headshot to my company for a presentation and this worked amazingly.": "En un apuro para enviar una foto profesional a mi empresa para una presentación y esto funcionó increíblemente.",
      "I quickly generated a professional headshot for a conference speaker profile using this tool, avoiding the expense and time constraints of traditional photography.": "Rápidamente generé una foto profesional para un perfil de conferencista utilizando esta herramienta, evitando los gastos y limitaciones de tiempo de la fotografía tradicional.",
      "Fast and fresh looks I would not have thought of myself": "Looks rápidos y frescos que no se me habrían ocurrido a mí misma",
      "I was looking for professional work photos for my LinkedIn so this is perfect": "Estaba buscando fotos profesionales para mi LinkedIn, así que esto es perfecto"
    }
  }
</i18n>
