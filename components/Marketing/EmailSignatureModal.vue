<template>
  <div class="p-4 md:p-8 md:px-16 max-w-4xl">
    <template v-if="formSubmitted">
      <div class="mx-auto text-center">
        <IconCircledCheck class="mx-auto w-24 h-24 text-green-500 mb-6" />
        <h2 class="font-medium tracking-tight text-2xl md:text-3xl text-primary-500">
          Nice! Check your email<br>to create your email signature
        </h2>
        <p class="mt-6 text-sm md:text-lg text-gray-600 max-w-xl mx-auto">
          We've sent you an email to confirm your email address. Check your inbox and click the link to create your email signature.
        </p>
      </div>
    </template>
    <template v-else>
      <div class="mx-auto text-center">
        <h2 class="font-medium tracking-tight text-2xl md:text-3xl text-primary-500">
          Want a matching email signature?
        </h2>
        <div class="overflow-auto mt-6">
          <div class="w-[512px] mx-auto p-4 border-slate-200 border rounded-md bg-slate-50">
            <GalleryPreviewPartialsEmailSignature
              :photo="photo"
              name="Your name"
              email="<EMAIL>"
              website="www.example.com"
            />
          </div>
        </div>
        <p class="mt-6 text-sm md:text-lg text-gray-600 max-w-xl mx-auto">
          Create a professional email signature with exclusive templates using the free headshot you just downloaded.
        </p>
      </div>
      <LoadingWrapper :is-loading="isLoading">
        <form @submit.prevent="send">
          <div class="flex flex-col items-center justify-center mt-8 mx-auto max-w-lg">
            <Input :value="email" class="w-full" placeholder="Your email" @input="updateEmail($event)" />
            <p class="flex justify-center items-center text-center text-gray-500 text-sm mt-2">
              <IconSolidLock class="w-4 h-4 mr-2 text-gray-400" />
              <span>100% privacy-friendly. Your email will never be sold.</span>
            </p>
            <div class="mt-4 flex flex-col gap-4 md:flex-row justify-center items-center">
              <ButtonWhite type="button" class="order-last md:order-first" @click="$emit('close')">
                No thanks
              </ButtonWhite>
              <ButtonPrimary type="submit">
                Generate Email Signature
              </ButtonPrimary>
            </div>
          </div>
        </form>
      </LoadingWrapper>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    photo: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      email: '',
      formSubmitted: false,
      isLoading: false
    }
  },
  methods: {
    send () {
      const isEmailValid = this.email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)
      if (!isEmailValid) {
        alert('Please enter a valid email address')
        return
      }

      this.isLoading = true
      window.bento.identify(this.email)
      window.bento.track('double-opt-in')
      setTimeout(() => {
        this.isLoading = false
        this.formSubmitted = true
      }, 500)
    },
    updateEmail (event) {
      this.email = event
    }
  }
}
</script>
