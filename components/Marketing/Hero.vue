<template>
  <div>
    <section class="bg-white">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col lg:flex-row lg:items-center">
          <div class="mx-auto text-left max-w-lg shrink-0 space-y-3 py-8 md:text-center lg:mx-0 lg:text-left xl:max-w-[620px]">
            <h2 id="convert-3-v1" class="text-2xl leading-[1.6rem] font-bold tracking-tight text-primary-500 sm:text-4xl sm:leading-[3rem] lg:text-[38px]">
              {{ headline }}
            </h2>
            <h1 class=" text-md font-medium text-gray-800 sm:text-xl">
              The #1 Professional AI Headshot Generator
              <!-- Get professional headshots in minutes with our AI-photographer -->
            </h1>
            <p id="convert-3-v1-p" class="text-sm leading-[1.4rem] md:text-lg font-normal md:leading-[1.8rem] text-gray-600">
              Get professional business headshots in minutes with our AI-photographer. Upload photos, pick style & receive 120+ generated headshots.
            </p>

            <div class="relative space-y-5">
              <div class="hidden sm:flex items-center justify-start flex-col md:flex-row">
                <div class="flex">
                  <IconSolidStar v-for="index in 5" :key="index" class="w-4 h-4 text-yellow-400" />
                </div>
                <span class="text-gray-600 text-sm ml-1.5">Used by {{ $store.state.stats.users }} happy indiviuals and remote teams.</span>
              </div>
              <div class="flex flex-col space-y-1 md:hidden">
                <div class="flex items-center justify-start">
                  <IconCheck class="w-3 h-3 text-green-500 mr-1.5" />
                  <span class="text-xs text-gray-400">Done in 2 hours, fast turn-around</span>
                </div>
                <div class="flex items-center">
                  <IconCheck class="w-3 h-3 text-green-500 mr-1.5" />
                  <p class=" text-xs font-normal text-gray-400">
                    Used by {{ $store.state.stats.users }} happy customers
                  </p>
                  <div class="flex items-center space-x-px ml-2">
                    <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                      />
                    </svg>
                    <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                      />
                    </svg>
                    <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                      />
                    </svg>
                    <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                      />
                    </svg>
                    <svg class="w-3 h-3 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                      />
                    </svg>
                  </div>
                </div>
                <div class="flex items-center justify-start">
                  <IconCheck class="w-3 h-3 text-green-500 mr-1.5" />
                  <span class="text-xs text-gray-400">{{ $store.state.stats.photos }}+ professional headshots already created</span>
                </div>
              </div>
              <div class=" absolute -bottom-4 rotate-12 transform hidden sm:right-16 sm:block lg:right-44 xl:right-8">
                <img class="h-8 w-auto" src="@/assets/img/arrow-down.svg" alt="">
              </div>

              <div class="flex items-center justify-center gap-3 lg:justify-start">
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-gray-600 hidden md:flex">As seen on: </span>
                  <ImageDns width="60" height="30" :src="require('@/assets/img/logo-1.png')" class="w-[40px] md:w-[60px] grayscale" />
                  <ImageDns width="60" height="30" :src="require('@/assets/img/logo-2.png')" class="w-[40px] md:w-[60px] grayscale" />
                  <ImageDns width="100" height="32" :src="require('@/assets/img/logo-3.png')" class="w-1/5 md:w-[100px] grayscale" />
                  <ImageDns width="100" height="32" :src="require('@/assets/img/logo-4.png')" class="w-1/5 md:w-[100px] grayscale" />
                  <ImageDns width="100" height="32" :src="require('@/assets/img/logo-5.png')" class="w-1/5 md:w-[100px] grayscale" />
                </div>
              </div>
              <div class="flex sm:hidden space-x-2">
                <div class="w-full">
                  <template v-if="!isLoggedIn">
                    <nuxt-link :to="'/auth/login?redirect=' + encodeURIComponent('/app')" class="w-full">
                      <button title="" class="inline-flex h-12 w-full items-center justify-center gap-2 rounded-lg border border-transparent bg-orange-500 px-4 py-2 text-base font-medium leading-6 text-white shadow-sm transition-all duration-150 hover:bg-gray-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-500 sm:w-auto" role="button">
                        Get your headshots now
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                      </button>
                    </nuxt-link>

                    <!-- <span class="text-sm font-bold uppercase tracking-wide text-gray-400">Or</span>

                    <button title="" class="text-sm font-medium text-primary-500 underline transition-all duration-150 hover:opacity-90" @click="createTeam">
                      Signup as team
                    </button> -->
                  </template>
                  <template v-else>
                    <nuxt-link to="/app">
                      <ButtonPrimary class="w-full md:auto">
                        Access your photos
                      </ButtonPrimary>
                    </nuxt-link>
                  </template>
                </div>
              </div>

              <!-- <p class="mx-auto max-w-lg text-sm text-gray-500 lg:mx-0">
                Tens of thousands of people have created their professional photos with our headshot photography tools. Get your business headshots today.
              </p> -->
            </div>
          </div>

          <div class="lg:pl-8">
            <img
              fetchpriority="high"
              width="343"
              height="274"
              class="relative  mt-4 md:mt-0 md:bottom-[-60px] w-full"
              :src="require('@/assets/img/hero-headshots.jpg')"
              alt="Headshot Examples"
            >
          </div>
        </div>
      </div>
    </section>
    <div class="relative">
      <div class="absolute inset-0 grid" aria-hidden="true">
        <div class="bg-transparent" />
        <div class="bg-teal-100" />
      </div>

      <div class="hidden md:block mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="isolate mx-auto max-w-5xl rounded-lg border-2 border-primary-500 bg-white px-6 py-5 shadow-lg sm:px-8">
          <template v-if="!isLoggedIn">
            <div class="flex flex-col gap-6 lg:flex-row lg:items-end">
              <div class="flex-1">
                <label for="companyName" class="block text-sm font-medium leading-6 text-gray-900">Company name</label>
                <div class="mt-1">
                  <input
                    id="companyName"
                    v-model="companyName"
                    type="text"
                    name="companyName"
                    class="block h-12 w-full rounded-lg border-0 px-4 py-2 text-gray-900 placeholder-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-teal-500"
                    placeholder="Acme Corp"
                  >
                </div>
              </div>

              <div class="flex-1">
                <label for="teamSize" class="block text-sm font-medium leading-6 text-gray-900">Team size</label>
                <div class="mt-1">
                  <input
                    id="teamSize"
                    v-model="teamSize"
                    type="number"
                    name="teamSize"
                    class="block h-12 w-full rounded-lg border-0 px-4 py-2 text-gray-900 placeholder-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-teal-500"
                    placeholder="5"
                    min="1"
                    max="100"
                  >
                </div>
              </div>

              <div class="flex flex-col items-center gap-4 sm:flex-row sm:gap-6">
                <button title="" class="inline-flex h-12 w-full items-center justify-center gap-2 rounded-lg border border-transparent bg-primary-500 px-4 py-2 text-base font-medium leading-6 text-white shadow-sm transition-all duration-150 hover:bg-gray-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-500 sm:w-auto" role="button" @click="createTeam">
                  Create your team
                  <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </button>

                <span class="text-sm font-bold uppercase tracking-wide text-gray-400">Or</span>

                <nuxt-link to="/app/add" title="" class="text-lg font-medium text-primary-500 underline transition-all duration-150 hover:opacity-90">
                  Single person
                </nuxt-link>
              </div>
            </div>
          </template>
          <template v-else>
            <nuxt-link to="/app">
              <ButtonPrimary class="w-full md:auto">
                To dashboard
              </ButtonPrimary>
            </nuxt-link>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    headline: {
      type: String,
      default: 'Professional business headshots, without a physical photo shoot'
    }
  },
  data () {
    return {
      companyName: null,
      teamSize: null
    }
  },
  computed: {
    title () {
      const { utm_campaign: utmCampaign } = this.$route.query
      if (utmCampaign === 'ai-headshots') { return 'AI generated headshots, without physical photo shoot' }
      if (utmCampaign === 'profile-picture') { return 'AI generated profile pictures, without physical photo shoot' }
      return 'Professional business headshots, without physical photo shoot'
    }
  },
  methods: {
    createTeam () {
      const url = '/app/admin/team/new'
      if (this.companyName && this.teamSize) {
        this.$router.push({ path: url, query: { companyName: this.companyName, teamSize: this.teamSize } })
      } else {
        this.$router.push({ path: url })
      }
    }
  }
}
</script>

<style></style>
