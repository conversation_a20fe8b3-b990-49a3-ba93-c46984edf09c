<template>
  <div class="flex w-full gap-6 mx-auto overflow-x-auto xl:max-w-6xl lg:grid lg:grid-cols-3 snap-x">
    <MarketingTestimonialGridItem v-for="testimonial in testimonials" :key="testimonial.id" :testimonial="testimonial" />
  </div>
</template>

<script>
export default {
  computed: {
    testimonials () {
      return this.$store.state.testimonials.filter(t => !!t.name)
    }
  }
}
</script>
