<template>
  <div>
    <LoadingWrapper :is-loading="isLoading" title="Uploading...">
      <EditRemixSelector
        label="Hairstyle"
        :selected-item="hairstyle"
        :items="hairstyles"
        :gender="item?.trigger"
        class="mb-4"
        @reset="hairstyle = null"
        @selectItem="selectHairstyle"
      />
      <EditRemixSelector
        label="Emotion"
        :selected-item="emotion"
        :items="emotions"
        :gender="item?.trigger"
        @reset="emotion = null"
        @selectItem="selectEmotion"
      />
    </LoadingWrapper>
  </div>
</template>

<script>
import ModelPersonalizationMixin from '../../mixins/ModelPersonalizationMixin'

export default {
  mixins: [ModelPersonalizationMixin],
  props: {
    photo: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      isLoading: false,
      results: null,
      hairstyle: null,
      emotion: null,
      selecting: null
    }
  },
  computed: {
    hairstyles () {
      return (this.$store.state.hairstyles)
        .filter(hairstyle => hairstyle?.gender?.includes(this.item?.trigger))
        // .filter(hairstyle => this.hairstyleImage(hairstyle))
    },
    emotions () {
      return (this.$store.state.emotions)
        .filter(emotion => emotion?.gender?.includes(this.item?.trigger))
        .filter(emotion => this.emotionImage(emotion))
    },
    item () {
      return this.$store.state.results.item
    },
    currentHairstyleImage () {
      return this.hairstyleImage(this.hairstyle)
    },
    currentEmotionImage () {
      return this.emotionImage(this.emotion)
    }
  },
  watch: {
    hairstyles () {
      this.hairstyle = this.hairstyleFromPrompt(this.metaVariable('hairstyle', null))
    },
    emotions () {
      this.emotion = this.emotionFromPrompt(this.metaVariable('emotion', null))
    }
  },
  mounted () {
    if (!this.hairstyles || this.hairstyles.length === 0) {
      this.$store.dispatch('getHairstyles')
    } else {
      this.hairstyle = this.hairstyleFromPrompt(this.metaVariable('hairstyle', null))
    }

    if (!this.emotions || this.emotions.length === 0) {
      this.$store.dispatch('getEmotions')
    } else {
      this.emotion = this.emotionFromPrompt(this.metaVariable('emotion', null))
    }
  },
  methods: {
    hairstyleImage (hairstyle) {
      if (!hairstyle?.images) { return null }
      if (this.item?.trigger === 'male' && hairstyle?.images?.male) {
        return hairstyle?.images?.male
      }

      if (this.item?.trigger === 'female' && hairstyle?.images?.female) {
        return hairstyle?.images?.female
      }

      return hairstyle?.images?.female || hairstyle?.images?.male
    },
    emotionImage (emotion) {
      if (this.item?.trigger === 'male' && emotion?.images?.male) {
        return emotion?.images?.male
      }

      if (this.item?.trigger === 'female' && emotion?.images?.female) {
        return emotion?.images?.female
      }

      return emotion?.images?.female || emotion?.images?.male
    },
    selectHairstyle (hairstyle) {
      this.hairstyle = hairstyle
      this.confirm()
      this.selecting = null
    },
    selectEmotion (emotion) {
      this.emotion = emotion
      this.confirm()
      this.selecting = null
    },
    confirm () {
      this.$emit('submit', {
        hairstyle: this.hairstyle?._id,
        emotion: this.emotion?._id
      })
    }
  }
}
</script>

<style>

</style>
