<template>
  <div>
    <form class="mb-4 flex items-center justify-center" @submit.prevent="search">
      <Input v-model="query" class="w-full" placeholder="Search" />
      <ButtonPrimary size="sm" class="ml-4" @click="search">
        <IconSearch class="h-4 w-4" />
      </ButtonPrimary>
    </form>
    <LoadingWrapper :is-loading="isLoading" title="Working...">
      <template v-if="results?.results">
        <ul class="grid grid-cols-2 md:grid-cols-3 gap-4">
          <li v-for="item in results?.results" :key="item.url">
            <div class="aspect-[2/3] relative">
              <button type="button" class="group" @click="$emit('backgroundSelected', item.url)">
                <img :src="item.url" :alt="item.alt" class="absolute left-0 top-0 w-full h-full rounded object-cover">
                <div class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded opacity-0 transition-opacity group-hover:opacity-100">
                  <IconSolidCheck class="w-8 h-8 text-white" />
                </div>
              </button>
            </div>
          </li>
        </ul>
        <p class="text-xs text-gray-600 mt-2">
          Photos powered by <a class="underline" href="https://www.pexels.com" target="_blanl">Pexels.com</a>
        </p>
      </template>
      <template v-else>
        <p class="p-4 rounded-md bg-blue-50 text-center text-blue-600 text-sm">
          Use the search bar above to find the perfect background or <label class="underline">click here to upload your own<input type="file" accept="image/png, image/jpeg, image/heic, image/heif, image/jpg" class="sr-only" @change="uploadCustomBackground"></label>
        </p>
      </template>
    </LoadingWrapper>
  </div>
</template>

<script>
export default {
  data () {
    return {
      isLoading: false,
      query: '',
      results: null
    }
  },
  methods: {
    search () {
      if (this.isLoading || this.query.trim().length === 0) {
        return
      }

      this.isLoading = true
      this.$axios.get('/backgrounds/search', {
        params: {
          query: this.query
        }
      }).then((response) => {
        this.results = response.data
        this.isLoading = false
      }).catch((err) => {
        console.error(err)
        this.results = null
        this.isLoading = false
      })
    },
    async uploadCustomBackground (event) {
      if (this.isLoading || (event.target.files?.length || 0) === 0) {
        return
      }

      this.isLoading = true
      try {
        const imageUrl = await this.uploadImageToServer(event.target.files[0])
        this.$emit('backgroundSelected', imageUrl)
      } catch (e) {
        console.error(e)
      } finally {
        this.isLoading = false
      }
    },
    uploadImageToServer (file) {
      const formData = new FormData()
      formData.append('files', file)
      formData.append('folder', 'background')
      formData.append('requiresFullUrl', '1')

      return new Promise((resolve, reject) => {
        this.$axios.post('/image/upload', formData)
          .then((response) => {
            if (response.data.success) {
              if (!response.data.data?.url) {
                return reject(new Error('Unknown error'))
              }

              return resolve(response.data.data.url)
            }

            if (response.data.errorMessage) {
              return reject(new Error(response.data.errorMessage))
            }

            reject(new Error('Unknown error'))
          })
          .catch((err) => {
            console.log(err)
            reject(new Error('Unknown error'))
          })
      })
    }
  }

}
</script>

  <style>

  </style>
