<template>
  <div>
    <h2 class="text-lg font-medium text-gray-800">
      Use photo
    </h2>
    <p class="text-sm text-gray-500">
      Do you like your photo? Now you can configure an email signature or turn it into a profile picture.
    </p>
    <LoadingWrapper :is-loading="loading">
      <div class="space-y-8 bg-gray-100 p-4 rounded-md mt-4">
        <button type="button" class="w-full px-4 py-5 sm:px-6 flex items-center justify-start text-left overflow-hidden rounded-lg bg-white shadow group" @click="goToEmailSignature">
          <span class="pr-4 whitespace-nowrap text-sm font-bold">Create email signature</span>
          <IconSolidEnveloppe class="w-6 h-6 text-gray-500 ml-auto transition group-hover:text-amber-400 group-hover:rotate-6" />
        </button>
        <button type="button" class="w-full px-4 py-5 sm:px-6 flex items-center justify-start text-left overflow-hidden rounded-lg bg-white shadow group" @click="goToCreateProfilePicture">
          <span class="pr-4 whitespace-nowrap text-sm font-bold">Create profile picture</span>
          <IconSolidSmiley class="w-6 h-6 text-gray-500 ml-auto transition group-hover:text-amber-400 group-hover:rotate-6" />
        </button>
      </div>
    </LoadingWrapper>
  </div>
</template>

<script>
import EmailSignatureMixin from '../../mixins/EmailSignatureMixin'

export default {
  mixins: [EmailSignatureMixin],
  layout: 'protected',
  props: {
    photo: {
      type: Object,
      required: true
    },
    modelId: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      loading: false
    }
  },
  computed: {
    item () {
      return this.$store.state.results.item
    },
    user () {
      return this.$fire.auth.currentUser
    },
    imageUrl () {
      return (this.watermark) ? this.photo.thumbnail : this.photo.image
    }
  },
  methods: {
    goToCreateProfilePicture () {
      this.$store.state.editor.pfp.baseSrc = this.photo?.image || this.photo?.upscaled || this.photo?.thumbnail
      this.$store.state.editor.pfp.src = this.photo?.image || this.photo?.upscaled || this.photo?.thumbnail
      // const imageUrl = this.photo?.image || this.photo?.upscaled || this.photo?.thumbnail
      this.$posthog.capture('$model:edit:create_pfp')
      // window.open('/tools/free-headshot-generator?photo=' + encodeURIComponent(imageUrl), '_blank')
      this.$router.push('/tools/free-headshot-generator')
      // window.open('/tools/free-headshot-generator')
    },
    goToEmailSignature () {
      this.loading = true
      this.uploadPictureToPublicBucket(this.imageUrl)
        .then((url) => {
          this.$router.push('/tools/free-email-signature-generator?photo=' + encodeURIComponent(url) + '&name=' + encodeURIComponent(this.item.title) + '&email=' + encodeURIComponent(this.user.email))
        })
        .catch((err) => {
          console.error(err)
          this.$toast.error('Failed to upload image')
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>
