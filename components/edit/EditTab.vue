<template>
  <div>
    <h2 class="text-lg font-medium text-gray-800">
      Edit photo
    </h2>
    <p class="text-sm text-gray-500">
      Now that you have the perfect photo, you can make a few more adjustments like replacing or removing the background, or removing blemishes.
    </p>
    <template v-if="availableRegenerations > 0">
      <LoadingWrapper :is-loading="loading">
        <div class="space-y-8 bg-gray-100 p-4 rounded-md mt-4">
          <button type="button" class="w-full px-4 py-5 sm:px-6 flex items-center justify-start text-left overflow-hidden rounded-lg bg-white shadow group" @click="createTransparentBackground">
            <span class="pr-4 whitespace-nowrap text-sm font-bold">Remove background (make transparent)</span>
            <IconSolidWrenchAndScrewdriver class="w-6 h-6 text-gray-500 ml-auto transition group-hover:text-amber-400 group-hover:rotate-6" />
          </button>
          <EditPanel title="Replace background">
            <EditBackgroundSelection @backgroundSelected="backgroundSelected" />
          </EditPanel>
          <button v-if="!blemishRemoved" type="button" class="w-full px-4 py-5 sm:px-6 flex items-center justify-start text-left overflow-hidden rounded-lg bg-white shadow group" @click="removeBlemish">
            <span class="pr-4 whitespace-nowrap text-sm font-bold">Remove blemish</span>
            <IconSolidSparkles class="w-6 h-6 text-gray-500 ml-auto transition group-hover:text-amber-400 group-hover:rotate-6" />
          </button>
          <p class="text-xs text-gray-500 text-center">
            You have <strong>{{ availableRegenerations }}</strong> credits left. Each edit will use <strong>one</strong> credit.
          </p>
        </div>
      </LoadingWrapper>
    </template>
    <template v-else>
      <div class="flex flex-col items-center justify-center mt-12">
        <IconFaceFrown class="w-24 h-24 text-primary-500" />
        <p class="text-sm text-gray-500 mt-2 text-center">
          You've already used all your credits.
        </p>
        <LoadingWrapper v-if="shouldShowUpsell" :is-loading="loading" class="mt-4">
          <ButtonPrimary size="sm" @click="addMoreCredits">
            Buy 10 credits for $3
          </ButtonPrimary>
        </LoadingWrapper>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    photo: {
      type: Object,
      required: true
    },
    modelId: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      newBackgroundUrl: null,
      showPhotoDetails: false,
      blemishRemoved: false,
      loading: false
    }
  },
  computed: {
    item () {
      return this.$store.state.results.item
    },
    user () {
      return this.$fire.auth.currentUser
    },
    imageUrl () {
      return (this.watermark) ? this.photo.thumbnail : this.photo.image
    },
    availableRegenerations () {
      return this.item?.regenerationCredits - this.item?.usedRegenerations || 0
    }
  },
  mounted () {
    this.blemishRemoved = this.photo?.actions?.blemishRemoved
  },
  methods: {
    backgroundSelected (url) {
      this.newBackgroundUrl = url
      this.replaceBackground()
    },
    replaceBackground () {
      if (this.loading) {
        return
      }

      this.loading = true
      let newBackgroundUrl = this.newBackgroundUrl
      if (newBackgroundUrl.includes('&h') && newBackgroundUrl.includes('&w')) {
        newBackgroundUrl = newBackgroundUrl.replace(/(&h=\d+)|(&w=\d+)/g, '') + '&h=1536'
      }

      this.$axios.$post('/model/edit/remove-background', {
        modelId: this.modelId,
        photoId: this.photo._id,
        newBackgroundUrl
      }).then((response) => {
        if (response?.success) {
          this.$modal.open({
            time: 10000,
            title: 'Photo editing started',
            description: "We've started replacing the background of your photo. Please come back later or refresh this page."
          })
          this.$posthog.capture('$model:edit:replace_background')
          if (response.photo) {
            this.$store.commit('results/ADD_SINGLE_PHOTO', response.photo)
          }
          this.loading = false
          this.$emit('closeModal')
        } else {
          this.$toast.error(response.message || 'Failed to replace background, try again later.')
          this.loading = false
        }
      }).catch((error) => {
        console.error(error)
        this.$toast.error('Failed to replace background, try again later.')
        this.loading = false
      })
    },
    removeBlemish () {
      if (this.blemishRemoved || this.loading) {
        return
      }

      this.loading = true
      this.$axios.$post('/model/edit/remove-blemish', {
        modelId: this.modelId,
        photoId: this.photo._id
      }).then((response) => {
        this.$modal.open({
          time: 10000,
          title: 'Photo editing started',
          description: "We've started removing blemish, freckles, wrinkles, eye-bags and more from your photo. This can take up to 5 minutes. Please come back later or refresh this page."
        })
        this.$posthog.capture('$model:edit:remove_blemish')
        this.loading = false
        this.blemishRemoved = true

        // optimistically increment used regenerations
        this.$store.commit('results/INCREMENT_USED_REGENERATIONS')
        this.$store.commit('results/SET_PHOTO_STATUS', {
          _id: this.photo._id,
          status: 'editing'
        })
        this.$emit('closeModal')
      }).catch((error) => {
        console.error(error)
        this.loading = false
        this.$toast.error('Failed to remove blemish, try again later.')
      })
    },
    createTransparentBackground () {
      this.loading = true
      this.$axios.$post('/model/edit/remove-background', {
        modelId: this.modelId,
        photoId: this.photo._id
      }).then((response) => {
        if (response && response.success) {
          this.$modal.open({
            time: 10000,
            title: 'Photo editing started',
            description: "We've started to remove your background. This can take up to 5 minutes. Please come back later or refresh this page."
          })
          if (response.photo) {
            this.$store.commit('results/ADD_SINGLE_PHOTO', response.photo)
          }
          this.$emit('closeModal')
        } else {
          this.$toast.error(response.message || 'Failed to remove background, try again later.')
        }
      }).catch((err) => {
        console.error(err)
        this.$toast.error('Failed to remove background, try again later.')
      }).finally(() => {
        this.loading = false
      })
    },
    addMoreCredits () {
      if (this.loading) {
        return
      }

      this.loading = true

      this.$store.dispatch('upsell/addRemixCredits', { amount: 10, modelId: this.modelId })
        .then(() => {
          this.$router.push(`/app/results/${this.modelId}/unlock-more`)
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>
