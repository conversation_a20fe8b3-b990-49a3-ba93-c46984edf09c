<template>
  <div class="space-y-4">
    <div class="flex flex-col space-y-1">
      <h2 class="text-base font-semibold leading-[16px] tracking-[-0.41px] text-gray-900">
        Remix your photo
      </h2>
      <p class="text-[14px] font-medium leading-[20px] tracking-[-0.41px] text-paragraph">
        Set the options below to your liking and click 'Remix photo' to generate an entirely new image.
      </p>
    </div>
    <template v-if="availableRegenerations > 0 && photo.canEdit">
      <LoadingWrapper :is-loading="loading">
        <form class="space-y-4" @submit.prevent="editImage">
          <EditStyleSelection
            :photo="photo"
            @submit="updateParameters($event)"
          />
          <EditHairEmotionSelection :photo="photo" @submit="updateParameters($event)" />
          <button type="button" class="w-full text-gray-500 flex items-center justify-between text-left" @click="showAdvancedEditing = !showAdvancedEditing">
            <span class="pr-4 text-xs underline">Advanced editing</span>
            <IconChevronDown class="w-4 h-4 text-gray-500 ml-auto transition group-hover:rotate-180" />
          </button>
          <template v-if="showAdvancedEditing">
            <div class="relative">
              <InputSelect v-model="personalization.bodyType" :options="bodyTypeList" :label="$t('Body type')" />
              <div class="absolute top-0 right-0 z-10">
                <Tooltip position="left">
                  <img src="@/assets/img/body-types.jpg" alt="" class="w-full h-auto">
                </Tooltip>
              </div>
            </div>

            <div>
              <InputSelect v-model="personalization.glasses" :options="glassesEditList" :label="$t('Glasses')" />
            </div>

            <div>
              <InputSelect v-model="personalization.age" :options="ageList" :label="$t('Age')" />
            </div>

            <div>
              <InputSelect v-model="personalization.height" :options="heightList" :label="$t('Height')" />
            </div>

            <div>
              <InputSelect v-model="personalization.weight" :options="weightList" :label="$t('Weight')" />
            </div>

            <div>
              <InputSelect v-model="personalization.ethnicity" :options="ethnicityList" :label="$t('Ethnicity')" />
            </div>

            <div>
              <p
                class="text-sm font-medium text-primary-500"
              >
                {{ $t('Eye color') }}
              </p>

              <div class="flex items-center flex-wrap mt-2 gap-2.5">
                <PostcheckoutEyeColorButton
                  v-for="eyeColor in eyeColors"
                  :key="eyeColor.id"
                  v-model="personalization.eyeColor"
                  :identifier="eyeColor.id"
                  :label="eyeColor.label"
                  :background-color="eyeColor.color"
                />
              </div>
            </div>
          </template>

          <div>
            <ButtonPrimary type="submit" size="sm" class="w-full" :is-disabled="!didMakeChanges">
              Remix photo
            </ButtonPrimary>
            <p class="text-xs text-gray-500 mt-2 text-center">
              You have <strong>{{ availableRegenerations }}</strong> credits left. This remix will use <strong>one</strong> credit.
            </p>
          </div>
        </form>
      </LoadingWrapper>
    </template>
    <template v-else-if="availableRegenerations <= 0">
      <div class="flex flex-col items-center justify-center mt-12">
        <IconFaceFrown class="w-24 h-24 text-primary-500" />
        <p class="text-sm text-gray-500 mt-2 text-center">
          You've already used all your credits.
        </p>
        <LoadingWrapper v-if="shouldShowUpsell" :is-loading="loading" class="mt-4">
          <ButtonPrimary size="sm" @click="addMoreCredits">
            Buy 10 credits for $3
          </ButtonPrimary>
        </LoadingWrapper>
      </div>
    </template>
    <template v-else>
      <div class="flex flex-col items-center justify-center mt-12">
        <IconFaceFrown class="w-24 h-24 text-primary-500" />
        <p class="text-sm text-gray-500 mt-2 text-center">
          This model doesn't support remixing, but you can still edit certain aspects of your photo from the "Edit" tab.
        </p>
      </div>
    </template>
  </div>
</template>

<script>
import ModelPersonalizationMixin from '../../mixins/ModelPersonalizationMixin'

export default {
  mixins: [ModelPersonalizationMixin],
  props: {
    photo: {
      type: Object,
      required: true
    },
    modelId: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      loading: false,
      showAdvancedEditing: false,
      personalization: {
        age: null,
        height: null,
        weight: null,
        ethnicity: null,
        style: null,
        clothing: null,
        hairstyle: null,
        emotion: null,
        glasses: null,
        bodyType: null,
        eyeColor: null
      }
    }
  },
  computed: {
    item () {
      return this.$store.state.results.item
    },
    availableRegenerations () {
      return this.item?.regenerationCredits - this.item?.usedRegenerations || 0
    },
    didMakeChanges () {
      let hasChanges = false
      hasChanges = hasChanges || this.personalization.age !== null
      hasChanges = hasChanges || this.personalization.height !== null
      hasChanges = hasChanges || this.personalization.weight !== null
      hasChanges = hasChanges || this.personalization.ethnicity !== null
      hasChanges = hasChanges || this.personalization.style !== null
      hasChanges = hasChanges || this.personalization.clothing !== null
      hasChanges = hasChanges || this.personalization.hairstyle !== null
      hasChanges = hasChanges || this.personalization.emotion !== null
      hasChanges = hasChanges || this.personalization.glasses !== null
      hasChanges = hasChanges || this.personalization.bodyType !== null
      hasChanges = hasChanges || this.personalization.eyeColor !== null

      return hasChanges
    }
  },
  mounted () {
    if (this.photo.meta?.variables) {
      this.personalization.age = this.ageFromId(this.metaVariable('age', null))?.value || null
      this.personalization.height = this.heightFromId(this.metaVariable('height', null))?.value || null
      this.personalization.weight = this.weightFromId(this.metaVariable('weight', null))?.value || null
      this.personalization.ethnicity = this.ethnicityFromId(this.metaVariable('ethnicity', null))?.value || null
      this.personalization.glasses = this.metaVariable('glasses', null) ? 'yes' : 'no'
      this.personalization.bodyType = this.bodyTypeFromId(this.metaVariable('bodyType', null))?.value || null
      this.personalization.eyeColor = this.eyeColorFromId(this.metaVariable('eyeColor', null))?.id || null
    }
  },
  methods: {
    updateParameters (params) {
      if (params.hairstyle) {
        this.personalization.hairstyle = params.hairstyle
      }
      if (params.emotion) {
        this.personalization.emotion = params.emotion
      }
      if (params.style) {
        this.personalization.style = params.style
      }
      if (params.clothing) {
        this.personalization.clothing = params.clothing
      }
    },
    editImage () {
      if (this.loading) {
        return
      }

      this.loading = true
      this.$axios.post('/model/edit/remix', {
        modelId: this.modelId,
        photoId: this.photo._id,
        personalization: this.personalization
      }).then((response) => {
        this.$modal.open({
          time: 10000,
          title: 'Photo editing started',
          description: 'We\'ve started regenerating your photo. This usually takes up a few minutes, although in some cases it might take up to 4 hours. Please come back later or refresh this page.'
        })
        this.$posthog.capture('$model:edit:remix')
        this.loading = false
        if (response.data.photo) {
          this.$store.commit('results/ADD_SINGLE_PHOTO', response.data.photo)
        }
        this.$emit('closeModal')
      }).catch((error) => {
        console.error(error)
        this.$toast.error('Failed to remix photo, try again later.')
        this.loading = false
      })
    },
    addMoreCredits () {
      if (this.loading) {
        return
      }

      this.loading = true

      this.$store.dispatch('upsell/addRemixCredits', { amount: 10, modelId: this.modelId })
        .then(() => {
          this.$router.push(`/app/results/${this.modelId}/unlock-more`)
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>
