<template>
  <portal to="modal">
    <Popup size="4xl" :additional-classes="`max-h-[calc(100vh-100px)] !bg-gray-100`" @closeModal="$emit('closeModal')">
      <div class="flex flex-row space-x-4 items-center justify-between">
        <h2 class="text-base font-semibold leading-[16px] tracking-[-0.41px] text-gray-900">
          Edit photo
        </h2>
        <NavigationBoxTabs
          :initial-active="tab"
          :items="tabs"
          @selected="tab = $event"
        />
      </div>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-4 w-full">
        <div class="flex flex-col space-y-4">
          <ImageDns :src="imageUrl" class="w-full h-auto rounded-md" />
          <div class="hidden md:grid grid-cols-2 gap-y-2 gap-x-8 mt-4 w-full bg-white/80 p-4 rounded-md">
            <EditPhotoMetadataItem label="Gender" :value="genderFromId(item?.trigger)" />
            <EditPhotoMetadataItem label="Age" :value="ageFromId(metaVariable('age', item?.age))?.title || 'N/A'" />
            <EditPhotoMetadataItem label="Eye Color" :value="eyeColorFromId(metaVariable('eyeColor', item?.eyeColor))?.label || 'N/A'" />
            <EditPhotoMetadataItem v-if="emotionFromPrompt(metaVariable('emotion', null))" label="Emotion" :value="emotionFromPrompt(metaVariable('emotion', null))?.type" />
            <EditPhotoMetadataItem class="col-span-2" label="Ethnicity" :value="ethnicityFromId(metaVariable('ethnicity', item?.ethnicity))?.title || 'N/A'" />
            <EditPhotoMetadataItem label="Height" :value="heightFromId(metaVariable('height', item?.appearance?.height))?.title || 'N/A'" />
            <EditPhotoMetadataItem label="Glasses" :value="metaVariable('glasses', null) ? 'Yes' : 'No'" />
            <EditPhotoMetadataItem v-if="styleFromPrompt(metaVariable('style', null))" label="Background" :value="styleFromPrompt(metaVariable('style', null))?.title" />
            <EditPhotoMetadataItem v-if="clothingFromPrompt(metaVariable('clothing', null))" class="col-span-2" label="Clothing" :value="clothingFromPrompt(metaVariable('clothing', null))?.type" />
            <EditPhotoMetadataItem class="col-span-2" label="Hairstyle" :value="hairstyleFromPrompt(metaVariable('hairstyle', null))?.type || 'Automatic'" />
            <EditPhotoMetadataItem v-if="bodyTypeFromId(metaVariable('bodyType', item?.appearance?.bodyType))" class="col-span-2" label="Body Type" :value="bodyTypeFromId(metaVariable('bodyType', item?.appearance?.bodyType))?.title || 'N/A'" />
            <EditPhotoMetadataItem class="col-span-2" label="Created at" :value="formatDateAndTime(photo.finishedAt || photo.createdAt)" />
          </div>
        </div>
        <!-- Editor -->
        <div class="flex flex-col gap-4">
          <div class="card">
            <EditRemixTab v-show="tab === 'remix'" :photo="photo" :model-id="modelId" @closeModal="$emit('closeModal')" />
            <EditTab v-show="tab === 'edit'" :photo="photo" :model-id="modelId" class="w-full lg:overflow-y-auto" @closeModal="$emit('closeModal')" />
            <EditUseTab v-show="tab === 'use'" :photo="photo" :model-id="modelId" class="w-full lg:overflow-y-auto" />
          </div>
        </div>
      </div>
    </Popup>
  </portal>
</template>

<script>
import ModelPersonalizationMixin from '../../mixins/ModelPersonalizationMixin'

export default {
  mixins: [ModelPersonalizationMixin],
  layout: 'protected',
  props: {
    photo: {
      type: Object,
      required: true
    },
    modelId: {
      type: String,
      required: true
    },
    watermark: {
      type: Boolean,
      required: false,
      default: () => true
    }
  },
  data () {
    const canHaveRemix = !!this.photo.canEdit
    const tabs = [{ label: 'Remix', value: 'remix' }, { label: 'Edit', value: 'edit' }, { label: 'Use', value: 'use' }]
    if (!canHaveRemix) {
      tabs.splice(0, 1)
    }

    return {
      newBackgroundUrl: null,
      showPhotoDetails: false,
      blemishRemoved: false,
      loading: false,
      tab: canHaveRemix ? 'remix' : 'edit',
      canHaveRemix,
      tabs
    }
  },
  computed: {
    item () {
      return this.$store.state.results.item
    },
    user () {
      return this.$fire.auth.currentUser
    },
    imageUrl () {
      return (this.watermark) ? this.photo.thumbnail : this.photo.image
    }
  }
}
</script>
