<template>
  <div class="space-y-2">
    <div class="flex items-center justify-between">
      <label
        v-if="label"
        for="email"
        class="block text-sm font-medium leading-5 text-primary-500"
      >
        {{ label }}
      </label>
      <button class="underline text-xs text-red-500" @click="reset">
        Clear
      </button>
    </div>
    <div class="input-wrapper hover:bg-gray-50 transition-all duration-300 hover:!border-gray-300 relative">
      <div class="flex items-center justify-between w-full  cursor-pointer" @click="isSelecting = !isSelecting">
        <template v-if="selectedItem">
          <div class="flex items-center gap-2">
            <ImageDns v-if="genderImage(selectedItem)" :src="genderImage(selectedItem)" class="w-8 h-8 rounded-md" />
            <span class="text-sm text-gray-700 capitalize">{{ selectedItem?.title || selectedItem?.name || selectedItem?.type }}</span>
          </div>
        </template>
        <p v-else class="text-sm text-gray-500">
          Keep the same
        </p>
        <IconSmallChevronDown />
      </div>
      <div v-if="isSelecting" class="z-50 top-9 w-full h-[200px] absolute  left-0 border border-black/10 shadow-box bg-white rounded-md p-2 overflow-y-auto">
        <template v-for="item in items">
          <div :key="item.id" class="flex items-center group justify-between gap-2 hover:bg-gray-50 transition-all duration-300 rounded-md p-2 cursor-pointer" @click="selectItem(item)">
            <div class="flex items-center gap-2">
              <ImageDns v-if="genderImage(item)" :src="genderImage(item)" class="w-10 h-10 rounded-md" />
              <!-- <span class="text-sm text-gray-700 capitalize">{{ selectedItem?.title || selectedItem?.name || selectedItem?.type }}</span> -->
              <span class="text-sm text-gray-700">{{ item?.title || item?.name || item?.type }}</span>
            </div>
            <div class="hidden group-hover:flex">
              <span class="text-xs text-gray-500">
                Select
              </span>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    label: {
      type: String,
      required: false
    },
    selectedItem: {
      type: Object,
      default: () => null
    },
    currentStyleImage: {
      type: Object,
      default: () => null
    },
    items: {
      type: Array,
      default: () => []
    },
    gender: {
      type: String,
      default: () => 'male'
    }
  },
  data () {
    return {
      isSelecting: false
    }
  },
  methods: {
    genderImage (item) {
      if (!item) { return null }
      if (!item.images && !item.image) { return null }
      if (this.gender === 'male' && (item?.image?.maleImage || item?.images?.male)) {
        return item?.image?.maleImage ?? item?.images?.male
      } else if (this.gender === 'female' && (item?.image?.femaleImage || item?.images?.female)) {
        return item?.image?.femaleImage ?? item?.images?.female
      }
    },
    selectItem (item) {
      this.$emit('selectItem', item)
      this.isSelecting = false
    },
    reset (event) {
      event.preventDefault()
      this.isSelecting = false
      this.$emit('reset')
    }
  }

}
</script>

<style>
.input-wrapper {
    display: flex;
    width:100%;
    padding: var(--spacing-2, 8px) var(--spacing-3, 12px);
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    border-radius: var(--radius-rounded-md, 6px);
    border: 1px solid var(--border-border-input, #E4E4E7);
    background: var(--background-bg-background, #FFF);
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
}
</style>
