<template>
  <div>
    <LoadingWrapper :is-loading="isLoading" title="Uploading...">
      <EditRemixSelector
        label="Background"
        :selected-item="style"
        :items="styles"
        :gender="item?.trigger"
        class="mb-4"
        @reset="style = null"
        @selectItem="selectStyle"
      />
      <EditRemixSelector
        label="Clothing"
        :selected-item="clothing"
        :items="clothingItems"
        :gender="item?.trigger"
        @reset="clothing = null"
        @selectItem="selectClothing"
      />
    </LoadingWrapper>
  </div>
</template>

<script>
import ModelPersonalizationMixin from '../../mixins/ModelPersonalizationMixin'

export default {
  mixins: [ModelPersonalizationMixin],
  props: {
    photo: {
      type: Object,
      required: true
    },
    initialClothing: {
      type: Object,
      default: () => null
    },
    initialStyle: {
      type: Object,
      default: () => null
    }
  },
  data () {
    return {
      isLoading: false,
      results: null,
      style: null,
      clothing: null,
      selecting: null
    }
  },
  computed: {
    styles () {
      return (this.$store.state.styles).filter(style => this.styleImage(style))
    },
    clothingItems () {
      return (this.$store.state.clothing || [])
        .filter(clothing => this.clothingImage(clothing))
        .filter(clothing => clothing.gender.includes(this.item?.trigger))
    },
    item () {
      return this.$store.state.results.item
    },
    currentStyleImage () {
      return this.styleImage(this.style)
    },
    currentClothingImage () {
      return this.clothingImage(this.clothing)
    }
  },
  watch: {
    styles () {
      this.style = this.styleFromPrompt(this.metaVariable('style', null))
    },
    clothingItems () {
      this.clothing = this.clothingFromPrompt(this.metaVariable('clothing', null))
    }
  },
  mounted () {
    if (!this.styles || this.styles.length === 0) {
      this.$store.dispatch('getStyles')
    } else {
      this.style = this.styleFromPrompt(this.metaVariable('style', null))
    }

    if (!this.clothingItems || this.clothingItems.length === 0) {
      this.$store.dispatch('getClothing')
    } else {
      this.clothing = this.clothingFromPrompt(this.metaVariable('clothing', null))
    }
  },
  methods: {
    styleImage (style) {
      if (this.item?.trigger === 'male' && style?.image?.maleImage) {
        return style?.image?.maleImage
      }

      if (this.item?.trigger === 'female' && style?.image?.femaleImage) {
        return style?.image?.femaleImage
      }

      return style?.image?.femaleImage || style?.image?.maleImage
    },
    clothingImage (clothing) {
      if (this.item?.trigger === 'male' && clothing?.images?.male) {
        return clothing?.images?.male
      }

      if (this.item?.trigger === 'female' && clothing?.images?.female) {
        return clothing?.images?.female
      }

      return clothing?.images?.female || clothing?.images?.male
    },
    selectStyle (style) {
      this.style = style
      this.confirm()
      this.selecting = null
    },
    selectClothing (clothing) {
      this.clothing = clothing
      this.confirm()
      this.selecting = null
    },
    confirm () {
      this.$emit('submit', {
        style: this.style?._id,
        clothing: this.clothing?._id
      })
    }
  }
}
</script>

<style>

</style>
