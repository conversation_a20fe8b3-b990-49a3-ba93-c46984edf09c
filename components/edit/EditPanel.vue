<template>
  <div class="divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow">
    <button type="button" class="w-full px-4 py-5 sm:px-6 flex items-center justify-start text-left" @click="isOpen = !isOpen">
      <span class="pr-4 whitespace-nowrap text-sm font-bold">{{ title }}</span>
      <IconChevronDown class="w-6 h-6 text-gray-500 ml-auto transition-transform" :class="{'rotate-180': isOpen}" />
    </button>
    <div v-if="isOpen" class="px-4 py-5 sm:p-6">
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      isOpen: false
    }
  }
}
</script>

<style>

</style>
