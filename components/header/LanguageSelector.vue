<template>
  <client-only>
    <div ref="dropdownContainer" class="relative">
      <!-- Button -->
      <button
        class="flex items-center space-x-1 rounded-md border border-gray-300 bg-white px-2 py-2 text-xs font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-150"
        aria-haspopup="true"
        :aria-expanded="isDropdownOpen"
        @click="toggleDropdown"
      >
        <span class="hidden md:block uppercase">{{ $i18n.locale }} / {{ currentCurrency }}</span>
        <IconOutlineGlobe class="w-4 h-4 text-gray-600 transition-transform duration-200 md:hidden" />
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="w-3 h-3 text-gray-600 transition-transform duration-200"
          :class="{'rotate-180': isDropdownOpen}"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <polyline points="6 9 12 15 18 9" />
        </svg>
      </button>

      <!-- Dropdown -->
      <transition
        enter-active-class="transition ease-out duration-200"
        enter-from-class="transform opacity-0 scale-95"
        enter-to-class="transform opacity-100 scale-100"
        leave-active-class="transition ease-in duration-150"
        leave-from-class="transform opacity-100 scale-100"
        leave-to-class="transform opacity-0 scale-95"
      >
        <div
          v-show="isDropdownOpen"
          class="absolute right-0 mt-2 bg-white shadow-lg rounded-md w-[200px] z-10 overflow-hidden border border-gray-100"
        >
          <!-- Language Section -->
          <div class="text-xs font-semibold text-gray-700 p-3 bg-gray-50 border-b border-gray-100">
            Switch language:
          </div>
          <ul class="py-1">
            <li
              v-for="locale in localeForDropdown"
              :key="locale.code"
              class="border-b border-gray-50 last:border-b-0"
            >
              <nuxt-link
                :to="switchLocalePath(locale.value)"
                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150"
                :class="{'font-medium text-primary-600 bg-gray-50': locale.value === $i18n.locale}"
                @click.native="closeDropdown"
              >
                <span>{{ locale.title }}</span>
                <svg
                  v-if="locale.value === $i18n.locale"
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4 ml-auto text-primary-600"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <polyline points="20 6 9 17 4 12" />
                </svg>
              </nuxt-link>
            </li>
          </ul>

          <!-- Currency Section -->
          <div class="text-xs font-semibold text-gray-700 p-3 bg-gray-50 border-y border-gray-100">
            Switch currency:
          </div>
          <ul class="py-1">
            <li
              v-for="currency in availableCurrencies"
              :key="currency.code"
              class="last:border-b-0"
            >
              <button
                class="w-full text-left flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150"
                :class="{'font-medium text-primary-600 bg-gray-50': currency.code === currentCurrency.toLowerCase()}"
                @click="setCurrency(currency.code)"
              >
                <span>{{ currency.title }} ({{ currency.symbol }})</span>
                <svg
                  v-if="currency.code === currentCurrency.toLowerCase()"
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4 ml-auto text-primary-600"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <polyline points="20 6 9 17 4 12" />
                </svg>
              </button>
            </li>
          </ul>
        </div>
      </transition>
    </div>
  </client-only>
</template>

<script>

export default {
  data () {
    return {
      isDropdownOpen: false,
      currencyUpdateTrigger: 0
    }
  },
  computed: {
    availableLocales () {
      return this.$i18n.locales
    },
    localeForDropdown () {
      const titlePerLocal = {
        en: 'English',
        es: 'Español',
        de: 'Deutsch'
      }
      return this.$i18n.locales.map((locale) => {
        return {
          title: titlePerLocal[locale.code],
          value: locale.code
        }
      })
    },
    currentSelectedLanguage () {
      return this.localeForDropdown.find(locale => locale.value === this.$i18n.locale)?.title
    },
    availableCurrencies () {
      const allowed = this.$store.state.allowedCurrencies
      // Ensure 'allowed' is an array before attempting to filter
      if (!Array.isArray(allowed)) {
        return [] // Return empty array if not an array or undefined
      }
      return allowed.filter((currency) => {
        // Basic check for valid currency object and code
        if (!currency || typeof currency.code === 'undefined') {
          return false
        }
        if (!currency.hidden) { return true }
        // Ensure user and countryCode exist before comparison
        return currency.locale === (this.$store.state.user && this.$store.state.user.countryCode)
      })
    },
    allowedCurrencyCodes () {
      // This will be an array of lowercase currency codes, e.g., ['usd', 'eur']
      return this.availableCurrencies.map(currency => currency.code.toLowerCase())
    },
    currentCurrency () {
      this.currencyUpdateTrigger // eslint-disable-line no-unused-expressions

      // Priority 1: User-specific currency from Vuex store (e.g., this.$store.state.user.currency)
      const userStoreCurrency = this.$store.state.user && this.$store.state.user.currency
        ? String(this.$store.state.user.currency).toLowerCase()
        : null

      if (userStoreCurrency && this.allowedCurrencyCodes.includes(userStoreCurrency)) {
        return userStoreCurrency.toUpperCase()
      }

      // Priority 2: Currency from localStorage
      if (process.client) {
        const storedPreferredCurrency = localStorage.getItem('preferedCurrency')
        if (storedPreferredCurrency) {
          const storedLower = String(storedPreferredCurrency).toLowerCase()
          if (this.allowedCurrencyCodes.includes(storedLower)) {
            return storedLower.toUpperCase()
          }
        }
      }

      // Priority 3: Fallback logic
      // Default to 'USD' if it's an allowed currency
      if (this.allowedCurrencyCodes.includes('usd')) {
        return 'USD'
      }
      // Otherwise, use the first available currency from the allowed list
      if (this.availableCurrencies.length > 0 && this.availableCurrencies[0].code) {
        return this.availableCurrencies[0].code.toUpperCase()
      }
      // Absolute fallback if no currencies are available or 'usd' is not allowed
      return 'USD'
    }
  },
  mounted () {
    // Add event listener for clicks outside when component is mounted
    this.$nextTick(() => {
      document.addEventListener('click', this.handleClickOutside)
    })

    // Listen for currency changes to update the reactive trigger
    this.$root.$on('currency-changed', this.handleCurrencyChange)
  },
  beforeDestroy () {
    // Remove event listener when component is destroyed
    document.removeEventListener('click', this.handleClickOutside)

    // Remove currency change listener
    this.$root.$off('currency-changed', this.handleCurrencyChange)
  },
  methods: {
    toggleDropdown () {
      this.isDropdownOpen = !this.isDropdownOpen
    },
    closeDropdown () {
      this.isDropdownOpen = false
    },
    handleCurrencyChange () {
      // Update the reactive trigger to force computed properties to re-evaluate
      this.currencyUpdateTrigger += 1
    },
    async setCurrency (currencyCode) {
      this.closeDropdown() // Close dropdown first
      if (process.client) {
        // Show loading state
        this.$loading.show({ title: 'Switching currency...', description: 'Updating prices and content...' })

        try {
          // Update localStorage with new currency
          localStorage.setItem('preferedCurrency', currencyCode.toLowerCase())

          // Update the reactive trigger to force computed properties to re-evaluate
          this.currencyUpdateTrigger += 1

          // Force Vue to re-compute currency-dependent values
          // Since userCurrency is a computed property that depends on localStorage,
          // we need to trigger reactivity by forcing the component to update
          this.$forceUpdate()

          // Also force parent components and other components to update
          // by emitting a global event that other components can listen to
          this.$root.$emit('currency-changed', currencyCode.toLowerCase())

          // Give a small delay to ensure all computed properties and UI updates complete
          await this.$nextTick()

          // Additional small delay for smooth UX and to show the loading state briefly
          await new Promise(resolve => setTimeout(resolve, 500))
        } catch (error) {
          console.error('Error switching currency:', error)
        } finally {
          this.$loading.hide()
        }
      }
    },
    handleClickOutside (event) {
      // Check if dropdown is open and click is outside the dropdown container
      if (process.client && this.isDropdownOpen && this.$refs.dropdownContainer && !this.$refs.dropdownContainer.contains(event.target)) {
        this.closeDropdown()
      }
    }
  }
}
</script>

<style scoped>
/* Add custom styles if needed */
.bg-primary-50 {
  background-color: #eff6ff;
}
.focus\:ring-primary-500:focus {
  --tw-ring-color: #3b82f6;
}
</style>
