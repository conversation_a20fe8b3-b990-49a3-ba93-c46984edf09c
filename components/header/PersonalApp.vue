<template>
  <nav class="border-b border-gray-200 bg-white">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="flex h-16 justify-between">
        <div class="flex">
          <div class="flex flex-shrink-0 items-center">
            <NuxtLink to="/app">
              <Logo class="w-[140px]" />
            </NuxtLink>
          </div>
        </div>
        <div class="hidden sm:ml-6 sm:flex sm:items-center space-x-2">
          <!-- <NuxtLink v-if="!isTeamMember" :to="`/app/styles/${$route.params.id}`">
            <ButtonWhite size="sm">
              Get extra headshots
            </ButtonWhite>
          </NuxtLink> -->
          <!-- Profile dropdown -->
          <div class="relative ml-3">
            <div>
              <button
                id="user-menu-button"
                type="button"
                class="flex max-w-xs items-center rounded-full bg-white text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                aria-expanded="false"
                aria-haspopup="true"
                @click="showDropdown = !showDropdown"
              >
                <span class="sr-only">Open user menu</span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="h-6 w-6 text-gray-500"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 12.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 18.75a.75.75 0 110-1.5.75.75 0 010 1.5z" />
                </svg>
              </button>
            </div>

            <div
              v-if="showDropdown"
              class="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
              role="menu"
              aria-orientation="vertical"
              aria-labelledby="user-menu-button"
              tabindex="-1"
            >
              <!-- Active: "bg-gray-100", Not Active: "" -->

              <button
                id="user-menu-item-2"
                class="z-50 block px-4 py-2 text-sm text-gray-700"
                role="menuitem"
                tabindex="-1"
                @click="logout()"
              >
                Sign out
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile menu, show/hide based on menu state. -->
  </nav>
</template>

<script>
export default {
  name: 'AppHeader',
  data () {
    return {
      showDropdown: false,
      navigationItemsAll: [
        {
          title: 'Dashboard',
          to: '/app/admin'
        },
        {
          title: 'Results',
          to: '/app/admin/results'
        },
        {
          title: 'Team',
          to: '/app/admin/team'
        },
        {
          title: 'Credits',
          to: '/app/admin/credits'
        },

        {
          title: 'Style',
          to: '/app/admin/style'
        }
      ]
    }
  },
  computed: {
    navigationItems () {
      const hasOrganization = this.$store.state.organization && this.$store.state.organization.organization
      if (hasOrganization) {
        return [...this.navigationItemsAll]
      } else {
        return [this.navigationItemsAll[0]]
      }
    }
  }
}
</script>

<style></style>
