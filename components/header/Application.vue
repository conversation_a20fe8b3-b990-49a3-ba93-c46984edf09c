<template>
  <div class="w-full bg-white border-b border-gray-100 z-10">
    <div class="relative max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8 py-4">
      <div class="grid grid-cols-3 gap-2">
        <div class="col-span-1 w-full flex justify-start space-x-4 content-center items-center">
          <nuxt-link v-if="hasInvites && !isTeamMember" to="/app/invites" class="hidden text-sm font-medium text-gray-500 underline transition-all duration-150 md:inline-flex hover:text-gray-700">
            <span class="underline-none mr-1 flex h-4 w-4 animate-pulse items-center justify-center rounded-full bg-green-500 text-center text-xs text-white">!</span>
            <p class="text-sm text-gray-500 underline">
              Invites
            </p>
          </nuxt-link>
          <nuxt-link v-if="isTeamLead" to="/app/admin" class="hidden text-sm font-medium text-gray-500 underline transition-all duration-150 md:inline-flex hover:text-gray-700">
            Manage team
          </nuxt-link>
          <nuxt-link v-else-if="$store.state?.user?.role === 'User'" to="/app/admin/team/new" class="hidden text-sm font-medium text-gray-500 underline transition-all duration-150 md:inline-flex hover:text-gray-700">
            Create a team
          </nuxt-link>
          <template v-if="role !== 'TeamMember'">
            <nuxt-link
              v-if="$store?.state?.user?.stripeId || $store?.state?.user?.paypalId"
              to="/profile/invoices"
              class="hidden text-sm font-medium text-gray-500 underline transition-all duration-150 md:inline-flex hover:text-gray-700"
            >
              Invoices
            </nuxt-link>

            <button
              type="button"
              class="hidden text-sm font-medium text-gray-500 underline transition-all duration-150 md:inline-flex hover:text-gray-700"
              @click="$store.commit('SET_MODAL', { name: 'support', value: true })"
            >
              Support
            </button>
          </template>
        </div>

        <div class="col-span-1 w-full flex justify-center content-center items-center">
          <nuxt-link to="/app">
            <Logo class="w-auto h-6" />
          </nuxt-link>
        </div>
        <div class="col-span-1 w-full flex justify-end space-x-4 content-center items-center">
          <HeaderLanguageSelector />
          <nuxt-link to="/profile" class="hidden text-sm font-medium text-gray-500 underline transition-all duration-150 md:inline-flex hover:text-gray-700">
            Profile
          </nuxt-link>
          <button
            type="button"
            class="hidden text-sm font-medium text-gray-500 underline transition-all duration-150 md:inline-flex hover:text-gray-700"
            @click="logout"
          >
            Logout
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  computed: {
    hasInvites () {
      return this.$store.state?.user?.invites || null
    },
    isTeamLead () {
      return this.$store.state?.user?.role === 'TeamLead' || null
    },
    role () {
      return this.$store.state?.user?.role || null
    }
  }
}
</script>

<style>

</style>
