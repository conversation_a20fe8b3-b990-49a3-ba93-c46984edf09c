<template>
  <div class="sticky z-50 top-0 inset-x-0">
    <div class="pt-2 hidden lg:block pb-2.5 border-b border-gray-200 bg-gray-50">
      <div class="max-w-screen-xl px-4 mx-auto 2xl:px-0 sm:px-6 lg:px-8">
        <div class="flex items-center gap-6">
          <p class="flex items-center gap-1 text-xs font-medium text-paragraph">
            <IconCheck class="size-3 text-green-500" />
            <span>{{ $t('Founded in Holland.') }} {{ $t('We respect your privacy.') }}</span>
          </p>
          <div class="flex items-center gap-1 text-xs font-medium text-paragraph">
            <div class="flex items-center">
              <IconSolidStar class="size-3 text-green-500" />
              <IconSolidStar class="size-3 text-green-500" />
              <IconSolidStar class="size-3 text-green-500" />
              <IconSolidStar class="size-3 text-green-500" />
              <IconSolidStar class="size-3 text-green-500" />
            </div>
            <span>{{ $t('usedByHappyCustomers', { users: $store.state.stats.users }) }}</span>
          </div>

          <p class="flex items-center gap-1 text-xs font-medium text-paragraph">
            <IconCheck class="size-3 text-green-500" />
            <span>{{ $store.state.stats.photos }}+ {{ $t('professional headshots created') }}</span>
          </p>
        </div>
      </div>
    </div>

    <header class="sticky inset-x-0 top-0 z-50 py-3 border-b border-gray-100 backdrop-blur-lg bg-white/80">
      <div class="max-w-screen-xl px-4 mx-auto 2xl:px-0 sm:px-6 md:px-8">
        <div class="flex items-center justify-between gap-6 md:gap-8">
          <div class="flex items-center gap-1">
            <nuxt-link :to="localePath('/')" class="flex relative">
              <img width="181" height="28" class="h-6 w-auto" src="@/assets/img/logo.svg" alt="HeadshotPro logo">
            </nuxt-link>
            <!-- <LandingpageCommonPill class="hover:scale-105 transition-all duration-150 hidden md:block !px-1.5 !py-1 text-[9px] leading-[9px] cursor-pointer" @click="$router.push(localePath('/jobs'))">
              Hiring
            </LandingpageCommonPill> -->
          </div>
          <div class="items-center justify-start flex-1 hidden min-w-0 gap-6 md:flex lg:gap-8">
            <nuxt-link
              v-for="nav in mainNav.left"
              :key="nav.url + '_' + nav.title"
              :to="nav.url"
              class="text-base font-semibold transition-all duration-150 text-primary-500 hover:text-opacity-80"
            >
              {{ nav.title }}
            </nuxt-link>
          </div>

          <div class="items-center justify-end hidden gap-4 xl:flex xl:gap-6">
            <div v-for="nav in mainNav.right" :key="nav.url + '_' + nav.title">
              <nuxt-link
                v-if="!nav.children || nav.children.length === 0"
                :key="nav.url + '_' + nav.title"
                :to="nav.url"
                class="text-sm hover:font-bold leading-6 text-gray-600 transition-all duration-150 hover:text-gray-900"
              >
                {{ nav.title }}
              </nuxt-link>
              <div v-else class="group">
                <div class="flex items-center justify-start gap-0.5">
                  <component
                    :is="nav.url ? 'nuxt-link' : 'span'"
                    :to="nav.url || undefined"
                    class="text-sm hover:font-bold leading-6 text-gray-600 transition-all duration-150 hover:text-gray-900"
                  >
                    <!-- <span class="text-sm hover:font-bold leading-6 text-gray-600 transition-all duration-150 hover:text-gray-900"> -->
                    {{ nav.title }}
                    <!-- </span> -->
                  </component>
                  <IconChevronDown class="size-3 text-paragraph" />
                </div>
                <ul class="hidden group-hover:flex absolute top-[40px]  w-[250px] flex-col bg-white px-4 py-2.5 border-t-2 border-t-brand-500 rounded-b-md shadow-xl space-y-2 border border-black/10 z-50">
                  <li v-for="(child, index) in nav.children" :key="child.url + '_child_' + index">
                    <nuxt-link :to="child.url" :title="child.title" class="text-sm hover:font-bold leading-6 text-gray-600 transition-all duration-150 hover:text-gray-900">
                      {{ child.title }}
                    </nuxt-link>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div class="items-center justify-end flex gap-3">
            <div class="hidden lg:flex lg:items-center space-x-2">
              <client-only>
                <HeaderLanguageSelector />
                <nuxt-link v-if="!isLoggedIn" :to="localePath('/auth/login')" title="" class="text-sm inline-flex font-semibold text-gray-500 rounded-lg bg-gray-200 pt-1.5 pb-2 px-3.5 border border-transparent transition-all duration-150 hover:bg-opacity-90 gap-1.5 items-center justify-center disabled:bg-opacity-20" role="button">
                  {{ $t('Login') }}
                </nuxt-link>
                <nuxt-link v-if="!isLoggedIn" :to="localePath('/app/add')" title="" class="text-sm inline-flex font-semibold text-white rounded-lg shadow-sm bg-primary-500 pt-1.5 pb-2 px-3.5 border border-transparent transition-all duration-150 hover:bg-opacity-90 gap-1.5 items-center justify-center disabled:bg-opacity-20" role="button">
                  <span>{{ $t('Get your headshots') }}</span>
                </nuxt-link>
                <nuxt-link v-else :to="localePath('/app')" title="" class="text-sm inline-flex font-semibold text-white rounded-lg shadow-sm bg-primary-500 pt-1.5 pb-2 px-3.5 border border-transparent transition-all duration-150 hover:bg-opacity-90 gap-1.5 items-center justify-center disabled:bg-opacity-20" role="button">
                  <span>{{ $t('To dashboard') }}</span>
                </nuxt-link>
              </client-only>
            </div>

            <div class="flex lg:hidden space-x-2">
              <client-only>
                <HeaderLanguageSelector />
              </client-only>
              <!-- <nuxt-link
                v-if="!isLoggedIn"
                :to="'/auth/login?redirect=' + encodeURIComponent('/app')"
              >
                <ButtonPrimary size="sm">
                  Create
                </ButtonPrimary>
              </nuxt-link> -->
              <ButtonWhite size="sm" @click="showMobileMenu = !showMobileMenu">
                <span v-if="!showMobileMenu"><IconBars class="w-4 h-4 text-gray-700" /></span>
                <span v-else><IconSolidCross class="w-4 h-4 text-gray-700" /></span>
              </ButtonWhite>
            </div>
          </div>
        </div>
      </div>
    </header>
    <!-- Mobile Menu -->
    <div v-if="showMobileMenu" class="absolute top-full left-0 w-full bg-white shadow-lg border-t border-gray-200 lg:hidden z-40 max-h-[calc(100vh-80px)] overflow-y-auto">
      <!-- Top Section: Auth Links -->
      <client-only>
        <div class="px-4 py-2 border-b border-gray-100  flex gap-2">
          <template v-if="isLoggedIn">
            <nuxt-link :to="localePath('/app')" @click.native="showMobileMenu = false">
              <ButtonPrimary class="w-full">
                {{ $t('Dashboard') }}
              </ButtonPrimary>
            </nuxt-link>
          </template>
          <template v-else>
            <nuxt-link :to="localePath('/auth/login') + '?redirect=' + encodeURIComponent(localePath('/app'))" @click.native="showMobileMenu = false">
              <ButtonWhite class="w-full">
                {{ $t('Login') }}
              </ButtonWhite>
            </nuxt-link>
            <nuxt-link :to="localePath('/app/add')" @click.native="showMobileMenu = false">
              <ButtonPrimary class="w-full">
                {{ $t('Start creating') }}
              </ButtonPrimary>
            </nuxt-link>
          </template>
        </div>
      </client-only>

      <!-- Navigation Links -->
      <div class="py-2 px-2 space-y-1">
        <div v-for="nav in [...mainNav.left, ...mainNav.right]" :key="nav.url || nav.title">
          <template v-if="!nav.children || nav.children.length === 0">
            <nuxt-link
              :to="nav.url"
              :title="nav.title"
              class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50"
              @click.native="showMobileMenu = false"
            >
              {{ nav.title }}
            </nuxt-link>
          </template>
          <template v-else>
            <!-- Parent item (not clickable) -->
            <div class="px-3 py-2 text-base font-medium text-gray-500">
              {{ nav.title }}
            </div>
            <!-- Child items -->
            <div class="pl-4 space-y-1">
              <nuxt-link
                v-for="(child, index) in nav.children"
                :key="child.url + '_child_' + index"
                :to="child.url"
                :title="child.title"
                class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50"
                @click.native="showMobileMenu = false"
              >
                {{ child.title }}
              </nuxt-link>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      expanded: false,
      showMobileMenu: false,
      subNav: []
    }
  },
  computed: {
    mainNav () {
      return {
        left: this.parseNavItems(this.$store.state.navigation?.left || []),
        right: this.parseNavItems(this.$store.state.navigation?.right || [])
      }
    }
  },
  watch: {
    showMobileMenu (newValue, oldValue) {
      if (newValue) {
        document.body.style.overflow = 'hidden'
      } else {
        document.body.style.overflow = 'auto'
      }
    }
  },
  methods: {
    parseNavItems (navItems) {
      return navItems.map((item) => {
        const newItem = { ...item }

        // Handle i18n for URLs (skip hash links and external URLs)
        if (newItem.url && !newItem.url.startsWith('#') && !newItem.url.startsWith('http')) {
          newItem.url = this.localePath(newItem.url)
        }

        if (newItem.title === 'Headshots') {
          newItem.children = [...newItem.children, ...this.$store.state?.navigation?.headshotTypes] || []
          // Handle i18n for children URLs
          newItem.children = newItem.children.map(child => ({
            ...child,
            url: child.url && !child.url.startsWith('#') && !child.url.startsWith('http')
              ? this.localePath(child.url)
              : child.url
          }))
        } else if (newItem.title === 'Pricing' && this.$route.path === '/company-headshots') {
          newItem.url = '#pricing'
        }

        // Handle i18n for children if they exist
        if (newItem.children && newItem.children.length > 0) {
          newItem.children = newItem.children.map(child => ({
            ...child,
            url: child.url && !child.url.startsWith('#') && !child.url.startsWith('http')
              ? this.localePath(child.url)
              : child.url
          }))
        }

        return newItem
      })
    }
  }
}
</script>

<style></style>

<i18n>
  {
    "en": {
      "Founded in Holland.": "Founded in Holland.",
      "We respect your privacy.": "We respect your privacy.",
      "professional headshots created": "professional headshots created",
      "Get your headshots": "Get your headshots",
      "To dashboard": "To dashboard",
      "Create your headshots": "Create your headshots",
      "Hiring": "Hiring",
      "Login": "Login"
    },
    "es": {
      "Founded in Holland.": "Fundada en Holanda.",
      "We respect your privacy.": "Respetamos tu privacidad.",
      "professional headshots created": "fotos profesionales creadas",
      "Get your headshots": "Obtén tus fotos",
      "To dashboard": "Dashboard",
      "Create your headshots": "Crea tus fotos",
      "Hiring": "Contratando",
      "Login": "Iniciar sesión"
    },
    "de": {
      "Founded in Holland.": "Gegründet in Holland.",
      "We respect your privacy.": "Wir respektieren deine Privatsphäre.",
      "professional headshots created": "professionelle Bewerbungsfotos erstellt",
      "Get your headshots": "Bewerbungsfotos erstellen",
      "To dashboard": "Zum Dashboard",
      "Create your headshots": "Bewerbungsfotos erstellen",
      "Hiring": "Stellenausschreibungen",
      "Login": "Anmelden"
    }
  }
</i18n>
