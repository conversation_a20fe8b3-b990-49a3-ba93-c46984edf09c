<template>
  <header class="fixed top-0 left-0 w-full z-50 flex-shrink-0 shadow-sm bg-white border-[#E4E4E7] p-4 border-b lg:shadow-none lg:relative lg:border-b-0 lg:border-r lg:p-6 lg:flex lg:flex-col lg:w-[240px] lg:h-screen" @click.stop>
    <div class="flex items-center justify-between">
      <NuxtLink to="/app">
        <Logo class="w-[160px]" />
      </NuxtLink>
      <ButtonWhite size="sm" class="lg:hidden" @click="showMobile = !showMobile">
        <span v-if="!showMobile"><IconBars class="w-4 h-4 text-gray-700" /></span>
        <span v-else><IconSolidCross class="w-4 h-4 text-gray-700" /></span>
      </ButtonWhite>
    </div>
    <div class="mt-4 w-full lg:block md:pb-8 lg:flex-1 lg:flex lg:flex-col lg:min-h-0" :class="{ 'hidden': !showMobile }">
      <div class="flex flex-col items-start justify-start gap-2 flex-shrink-0">
        <ButtonDropdown
          button-class="!justify-start !text-left"
          dropdown-class="!w-64"
          theme="v1"
          size="sm"
          xalign="left"
          icon-position="before"
          show-caret
          full-width
          :title="selectedView.title"
          :icon="selectedView.icon"
          :items="viewOptions"
          @select="updateView($event)"
        />
        <div class="w-full flex items-center gap-2">
          <ButtonDropdown
            button-class="!justify-start !text-left"
            dropdown-class="!w-64"
            theme="v1"
            size="sm"
            xalign="left"
            icon-position="before"
            show-caret
            full-width
            :title="selectedTeam.title"
            :icon="selectedTeam.icon"
            :items="teamOptions"
            @select="updateTeam($event)"
          />
          <ButtonWhite
            v-if="selectedTeam?.value"
            size="sm"
            @click="editTeam(selectedTeam)"
          >
            <IconPencilSquare class="size-4" />
          </ButtonWhite>
        </div>
      </div>
      <nav class="mt-4 flex-1 overflow-y-auto min-h-0">
        <div class="flex flex-col gap-1">
          <template v-for="item in navigationItems">
            <a
              v-if="item.external"
              :key="`a-${item.title}`"
              :href="item.to"
              target="_blank"
              rel="noopener noreferrer"
              class="inline-flex items-center px-4 h-[36px] rounded-lg text-[15px] hover:bg-gray-100 bg-transparent text-paragraph font-medium"
              @click="handleMobileClick"
            >
              <component :is="item.icon" class="size-4 mr-3 text-paragraph" />
              <span>{{ item.title }}</span>
              <span v-if="item.badge" class="ml-auto bg-teal-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                {{ item.badge }}
              </span>
            </a>
            <NuxtLink
              v-else
              :key="`nuxt-${item.title}`"
              :to="item.to"
              class="inline-flex items-center px-4 h-[36px] rounded-lg text-[15px] hover:bg-gray-100"
              :class="$route.fullPath === item.to ? 'bg-[#1B145D1A] text-primary-500 font-bold' : 'bg-transparent text-paragraph font-medium'"
              @click.native="handleMobileClick"
            >
              <component :is="item.icon" class="size-4 mr-3 text-paragraph" />
              <span>{{ item.title }}</span>
              <span v-if="item.badge" class="ml-auto bg-teal-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                {{ item.badge }}
              </span>
            </NuxtLink>
          </template>
          <button
            class="inline-flex items-center px-4 h-[36px] rounded-lg text-[15px] bg-transparent text-paragraph font-medium hover:bg-gray-100"
            @click="logout()"
          >
            <IconArrowLeftSquare class="size-4 mr-3 text-paragraph" />
            <span>Logout</span>
          </button>
        </div>
      </nav>
    </div>
    <div v-if="showLiveChat" class="mt-auto cursor-pointer group hidden lg:block" @click="openLiveChat">
      <IconLifebuoy class="w-6 h-6 text-paragraph group-hover:text-teal-500 group-hover:rotate-45 transition" />
      <p class="text-sm font-medium text-paragraph group-hover:underline">
        Having trouble?
      </p>
      <p class="text-sm font-medium mt-1 text-paragraph opacity-[0.78]">
        We'll get you sorted out. Click here to submit an email to our customer support team.
      </p>
    </div>
    <portal to="modal">
      <Modal v-if="showUpsertTeamModal" @close="showUpsertTeamModal = false">
        <TeamUpsertTeamModal :key="`edit-team-${editingTeam?.value ?? 'new'}`" :team="editingTeam" @closeModal="showUpsertTeamModal = false" />
      </Modal>
    </portal>
  </header>
</template>

<script>
export default {
  name: 'AppHeader',
  data () {
    return {
      showMobile: false,
      navigationItemsAll: [
        {
          title: 'Dashboard',
          to: '/app/admin',
          icon: 'IconUsers'
        },
        {
          title: 'Clothing',
          to: '/app/admin/clothing',
          icon: 'IconScissors'
        },
        {
          title: 'Backdrops',
          to: '/app/admin/style',
          icon: 'IconSwatch'
        },
        {
          title: 'Billing',
          to: '/app/admin/credits',
          icon: 'IconCurrencyDollar'
        },
        {
          title: 'Invoices',
          to: '/app/admin/invoices',
          icon: 'IconDocument'
        },
        {
          title: 'Settings',
          to: '/app/admin/settings',
          icon: 'IconCog6Tooth'
        },
        {
          title: 'Help & FAQs',
          to: '/app/admin/help-faq',
          icon: 'IconLifebuoy'
        },
        {
          title: 'Changelog',
          to: '/app/admin/changelog',
          icon: 'IconDocumentText'
        },
        {
          title: 'API',
          to: '/app/admin/api',
          icon: 'IconCodeBracket'
        },
        {
          title: 'Zapier',
          to: 'https://zapier.com/apps/headshotpro/integrations',
          icon: 'IconBolt',
          external: true
        },
        {
          title: 'Bulk Upload',
          to: '/app/admin/bulk-upload',
          icon: 'IconCloudArrowUp'
        },
        {
          title: 'Personal view',
          to: '/app?lead=true',
          icon: 'IconMiniArrowLeftRight'
        }
      ],
      showUpsertTeamModal: false,
      editingTeam: null
    }
  },
  computed: {
    showLiveChat () {
      if (!this.screenHeight) { return false }
      return this.screenHeight > 768
    },
    navigationItems () {
      const hasOrganization = this.$store.state.organization && this.$store.state.organization.organization
      let items = []

      if (hasOrganization) {
        items = [...this.navigationItemsAll]

        // Filter out bulk upload if the feature is not enabled for this organization
        const organization = this.$store.state.organization.organization
        const hasBulkUploadFeature = organization?.features?.bulkUpload === true

        if (!hasBulkUploadFeature) {
          items = items.filter(item => item.to !== '/app/admin/bulk-upload')
        }
      } else {
        items = [this.navigationItemsAll[0]]
      }

      // Only show changelog if there are entries available
      const changelogs = this.$store.state.changelogs
      const hasChangelogs = changelogs && Array.isArray(changelogs) && changelogs.length > 0

      if (!hasChangelogs) {
        // Remove changelog from navigation if no entries
        items = items.filter(item => item.to !== '/app/admin/changelog')
      } else {
        // Add badge to changelog item if there are unseen entries
        const changelogItemIndex = items.findIndex(item => item.to === '/app/admin/changelog')
        if (changelogItemIndex !== -1 && this.unseenChangelogCount > 0) {
          items[changelogItemIndex] = {
            ...items[changelogItemIndex],
            badge: this.unseenChangelogCount
          }
        }
      }

      return items
    },
    viewOptions () {
      const options = [
        { title: 'Switch to personal view', value: 'personal', classes: 'font-bold flex-row-reverse !justify-between gap-4 *:!normal-case', icon: 'IconUserCircle' }
      ]

      if (this.$store.state.organization && this.$store.state.organization.organization) {
        options.push({ title: this.$store.state.organization.organization.name, value: 'org', icon: 'IconBuildingOffice', classes: 'flex-row-reverse !justify-between gap-4 *:!normal-case' })
      }

      return options
    },
    selectedView () {
      return this.viewOptions.find(option => option.value === 'org')
    },
    teamOptions () {
      const organization = this.$store.state.organization?.organization
      if (!organization) {
        return []
      }

      const teams = (organization.teams || []).map(team => ({
        title: team.name,
        value: team.id,
        icon: team.icon || 'IconUsers',
        classes: 'flex-row-reverse !justify-between gap-4 *:!normal-case'
      }))

      return [
        {
          title: 'All members',
          value: null,
          icon: 'IconUsers',
          classes: 'flex-row-reverse !justify-between gap-4 *:!normal-case'
        },
        ...teams,
        {
          title: 'Create new team',
          value: 'new',
          icon: 'IconPlus',
          classes: 'font-bold flex-row-reverse !justify-between gap-4 *:!normal-case'
        }
      ]
    },
    selectedTeam () {
      const team = this.teamOptions.find(option => option.value === this.$store.state.organization.selectedTeamId)
      return team || this.teamOptions[0]
    },
    unseenChangelogCount () {
      // Force reactivity by accessing the store state directly
      const changelogs = this.$store.state.changelogs
      if (!changelogs || !Array.isArray(changelogs)) {
        return 0
      }
      return changelogs.filter(changelog => !changelog.isViewed).length
    }
  },
  watch: {
    '$store.state.changelogs': {
      handler () {
        // Force reactivity update when changelogs change
        this.$forceUpdate()
      },
      deep: true
    }
  },
  mounted () {
    document.addEventListener('click', this.handleClickOutside)
    this.fetchChangelogs()

    // Listen for changelog updates
    this.$nuxt.$on('changelog-updated', () => {
      this.fetchChangelogs()
    })
  },
  beforeDestroy () {
    document.removeEventListener('click', this.handleClickOutside)
    this.$nuxt.$off('changelog-updated')
  },
  methods: {
    updateView (value) {
      if (value === 'personal') {
        this.$router.push('/app?lead=true')
      }
    },
    handleMobileClick () {
      this.showMobile = false
    },
    handleClickOutside (event) {
      if (!this.$el.contains(event.target)) {
        this.showMobile = false
      }
    },
    updateTeam (value) {
      if (value === 'new') {
        this.createTeam()
        return
      }

      // this condition is to handle the case where the team is a new team, which doesn't have a value and the selector returns the whole object
      this.$store.commit('organization/SET_SELECTED_TEAM_ID', value?.title ? value.value : value)
    },
    createTeam () {
      this.editingTeam = null
      this.showUpsertTeamModal = true
    },
    editTeam (team) {
      this.editingTeam = team
      this.showUpsertTeamModal = true
    },
    async fetchChangelogs () {
      try {
        const response = await this.$axios.$get('/changelog/public')
        console.log(response)
        // Create a deep copy to ensure we don't have references that could cause mutations
        const changelogsCopy = JSON.parse(JSON.stringify(response.data))
        this.$store.commit('SET_CHANGELOGS', changelogsCopy)
      } catch (error) {
        console.error('Error fetching changelogs:', error)
      }
    },
    async markChangelogAsSeen (changelogId) {
      try {
        await this.$axios.$post('/changelog/mark-viewed', { changelogId })
        // Refresh changelog data to update view status
        await this.fetchChangelogs()
      } catch (error) {
        console.error('Error marking changelog as viewed:', error)
      }
    }
  }
}
</script>
