<template>
  <div class="w-full relative">
    <div class="flex flex-col sm:flex-row justify-between items-center mb-6">
      <img src="@/assets/img/logo.svg" class="w-40 h-auto">
      <p v-if="isQuote" class="font-bold">
        Quote #{{ charge.id || charge._id }}
      </p>
      <p v-else class="font-bold">
        Invoice #{{ charge.id || charge._id }}
      </p>
    </div>
    <header class="grid grid-cols-2 gap-4">
      <div class="text-left text-sm">
        <p v-if="isBeforeMarch2025">
          Headshot Pro Photography Pte. Ltd.
          <br>
          320 SERANGOON ROAD #11-06
          <br>
          CENTRIUM SQUARE
          <br>
          SINGAPORE (218108)
          <br>
          Company Reg. No.: 202119430N
          <br>
        </p>
        <p v-else>
          Headshot Pro Photography Pte. Ltd.<br>
          7 Temasek Boulevard<br>
          #12-07, Suntec Tower One<br>
          Singapore 038987, Singapore<br>
          Company Reg. No.: 202450176D
        </p>
        <p class="font-bold mt-2">
          Bill to:
        </p>
        <p style="white-space: pre;" class="overflow-x-auto" v-html="billingInformation" />
      </div>
      <div class="text-right text-sm">
        <p class="font-bold">
          Date of issue:
        </p>
        <p>{{ formatDate(charge.created || charge.createdOn) }}</p>
        <template v-if="!isQuote">
          <p class="font-bold mt-2">
            Date paid:
          </p>
          <p>{{ formatDate(charge.updatedAt || charge.updatedAt) }}</p>
        </template>
      </div>
    </header>

    <!-- !! New PaymentIntent.items format !! -->
    <Table v-if="charge?.items?.length" :head="['Description', quantityHeader, 'Unit price', 'Amount']" class="mt-8 w-full overflow-x-auto" :sticky="false">
      <TableRow v-for="item in charge.items" :key="item.id">
        <TableItem>{{ item.name }}</TableItem>
        <TableItem>{{ item.quantity }}</TableItem>
        <TableItem>{{ formatPrice(item.amount / 100, charge.currency,2,false) }}</TableItem>
        <TableItem class="text-right">
          {{ formatPrice(item.amount * item.quantity / 100, charge.currency,2,false) }}
        </TableItem>
      </TableRow>
      <TableRow v-if="charge.items.length > 1">
        <TableItem />
        <TableItem />
        <TableItem>Subtotal</TableItem>
        <TableItem class="text-right">
          {{ formatPrice(totalCost / 100, charge.currency,2,false) }}
        </TableItem>
      </TableRow>
      <TableRow v-if="totalDiscount > 0">
        <TableItem />
        <TableItem />
        <TableItem>Discount</TableItem>
        <TableItem class="text-green-600 text-right">
          -{{ formatPrice(totalDiscount / 100, charge.currency,2,false) }}
        </TableItem>
      </TableRow>
      <TableRow v-if="charge.refunded">
        <TableItem />
        <TableItem />
        <TableItem>Refund</TableItem>
        <TableItem class="text-right">
          <template v-if="charge.amountRefunded > 0">
            -{{ formatPrice(charge.amountRefunded / 100, charge.currency,2,false) }}
          </template>
          <template v-else>
            -{{ formatPrice(amountCaptured / 100, charge.currency,2,false) }}
          </template>
        </TableItem>
      </TableRow>
      <TableRow>
        <TableItem />
        <TableItem />
        <template v-if="!isQuote">
          <TableItem class="font-bold text-primary-500">
            Total
          </TableItem>
          <TableItem class="font-bold text-primary-500 text-right">
            {{ formatPrice(subtotal / 100, charge.currency, 2, false) }}
          </TableItem>
        </template>
        <template v-else>
          <TableItem class="font-bold text-primary-500">
            Amount quoted
          </TableItem>
          <TableItem class="font-bold text-primary-500 text-right">
            {{ formatPrice(amountCaptured / 100, charge.currency, 2, false) }}
          </TableItem>
        </template>
      </TableRow>
    </Table>
    <!-- !! Old format without .items array !! -->
    <Table v-else :head="['Description', 'Qty', 'Unit price', 'Amount']" class="mt-8 w-full overflow-x-auto" :sticky="false">
      <TableRow>
        <TableItem>
          <span v-if="isOrganizationInvoice">HeadshotPro Credits</span>
          <span v-else>Purchase HeadshotPro</span>
        </TableItem>
        <TableItem>{{ charge.quantity || 1 }}</TableItem>
        <TableItem>
          {{ formatPrice((amountCaptured / (charge?.quantity || 1)) / 100, charge.currency,2,false) }}
          <span v-if="charge?.price?.discount" class="text-xs text-green-500">
            ({{ charge?.price?.discount }}% off)
          </span>
        </TableItem>
        <TableItem class="text-right">
          {{ formatPrice((amountCaptured) / 100, charge.currency,2,false) }}
        </TableItem>
      </TableRow>
      <TableRow v-if="charge.refunded">
        <TableItem>Refund</TableItem>
        <TableItem>1</TableItem>
        <TableItem>
          <template v-if="charge.amountRefunded > 0">
            -{{ formatPrice(charge.amountRefunded / 100, charge.currency,2,false) }}
          </template>
          <template v-else>
            -{{ formatPrice(amountCaptured / 100, charge.currency,2,false) }}
          </template>
        </TableItem>
        <TableItem class="text-right">
          <template v-if="charge.amountRefunded > 0">
            -{{ formatPrice(charge.amountRefunded / 100, charge.currency,2,false) }}
          </template>
          <template v-else>
            -{{ formatPrice(amountCaptured / 100, charge.currency,2,false) }}
          </template>
        </TableItem>
      </TableRow>
      <TableRow>
        <TableItem />
        <TableItem />
        <TableItem>Subtotal</TableItem>
        <TableItem v-if="!isQuote" class="text-right">
          {{ formatPrice(subtotal / 100, charge.currency,2,false) }}
        </TableItem>
        <TableItem v-else class="text-right">
          {{ formatPrice(amountCaptured / 100, charge.currency,2,false) }}
        </TableItem>
      </TableRow>
      <TableRow>
        <TableItem />
        <TableItem />
        <template v-if="!isQuote">
          <TableItem class="font-bold text-primary-500">
            Amount paid
          </TableItem>
          <TableItem class="font-bold text-primary-500 text-right">
            {{ formatPrice(subtotal / 100, charge.currency, 2, false) }}
          </TableItem>
        </template>
        <template v-else>
          <TableItem class="font-bold text-primary-500">
            Amount quoted
          </TableItem>
          <TableItem class="font-bold text-primary-500 text-right">
            {{ formatPrice(amountCaptured / 100, charge.currency, 2, false) }}
          </TableItem>
        </template>
      </TableRow>
    </Table>
    <p class="print:hidden mt-12 text-sm text-center text-paragraph">
      Press CMD/CTRL+P to save. Questions? Contact <NAME_EMAIL>
    </p>
    <div v-if="isOrganizationInvoice" class="text-left mt-12 text-xs text-paragraph space-y-2">
      <p v-if="paymentUrl" class="text-left mb-6">
        <a :href="paymentUrl" class="text-blue-500 underline">Click here</a> to pay this invoice using either credit card or bank transfer.
      </p>
    </div>

    <div v-if="charge.status === 'void'" class="absolute flex items-center justify-center left-0 top-0 w-full h-full text-5xl sm:text-[100px] md:text-[150px] font-bold text-center text-black/20">
      VOID
    </div>
  </div>
</template>

<script>
export default {
  props: {
    paymentUrl: {
      type: String,
      required: false,
      default: null
    },
    isOrganizationInvoice: {
      type: Boolean,
      default: false
    },
    charge: {
      type: Object,
      required: true
    },
    billingInformation: {
      type: String,
      required: false,
      default: null
    },
    quantityHeader: {
      type: String,
      required: false,
      default: 'Qty'
    }
  },
  computed: {
    totalDiscount () {
      if (!this.charge?.discount) { return 0 }
      if (this.charge?.discount?.type === 'percentage') {
        if (this.isQuote) {
          return this.totalCost * (this.charge.discount.amount / 100)
        }

        return Math.max(this.totalCost * ((this.charge?.discount?.amount || 0) / 100), this.amountCaptured * ((this.charge?.discount?.amount || 0) / 100))
      } else if (this.charge?.discount?.type === 'fixed' || this.charge?.discount?.type === 'amount') {
        return this.charge.discount.amount
      }
      return 0
    },
    totalCost () {
      let totalCost = 0
      this.charge.items.forEach((item) => {
        totalCost += item.amount * item.quantity
      })
      return totalCost
    },
    subtotal () {
      if (this.charge.refunded) {
        if (this.charge.amountRefunded > 0) {
          return Math.max(0, this.amountCaptured - this.charge.amountRefunded)
        } else {
          return 0
        }
      }

      return this.amountCaptured
    },
    amountCaptured () {
      return this.charge?.amountCaptured || this.charge.amountTotal
    },
    isBeforeMarch2025 () {
      const date = new Date(this.charge.created)
      return date < new Date('2025-03-01')
    },
    isQuote () {
      return ['invoiced', 'void', 'quoted'].includes(this.charge.status)
    }
  }
}
</script>
