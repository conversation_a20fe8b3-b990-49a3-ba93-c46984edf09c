<template>
  <span
    class="px-2 py-0.5 rounded-full text-xs font-medium inline-flex items-center justify-center gap-[6px]"
    :class="{
      'bg-[#ECFDF3] text-[#027A48]': isPaid(invoice),
      'bg-[#F2F4F7] text-[#344054]': isRefunded(invoice),
      'bg-[#FEE2E2] text-[#B42318]': isVoid(invoice),
      'bg-[#FFF7DF] text-[#CF9C08]': !isPaid(invoice) && !isRefunded(invoice) && !isVoid(invoice) || isInvoiced(invoice),
    }"
  >
    <span v-if="isPaid(invoice)" class="w-[6px] h-[6px] rounded-full bg-[#12B76A]" />
    <span v-else-if="isInvoiced(invoice)" class="w-[6px] h-[6px] rounded-full bg-[#CF9C08]" />
    <span v-else-if="isVoid(invoice)" class="w-[6px] h-[6px] rounded-full bg-[#B42318]" />
    <IconCornerLeft v-else-if="isRefunded(invoice)" class="w-3 h-3 text-[#667085]" />
    <span>{{ badgeText(invoice) }}</span>
  </span>
</template>

<script>
export default {
  props: {
    invoice: {
      type: Object,
      required: true
    }
  },
  methods: {
    isPaid (invoice) {
      return invoice.status === 'complete'
    },
    isRefunded (invoice) {
      return invoice.status === 'refunded'
    },
    isInvoiced (invoice) {
      return invoice.status === 'invoiced'
    },
    isVoid (invoice) {
      return invoice.status === 'void'
    },
    badgeText (invoice) {
      if (this.isPaid(invoice)) {
        return 'Paid'
      }

      if (this.isRefunded(invoice)) {
        return 'Refunded'
      }

      return this.slugToTitle(invoice.status)
    }
  }
}
</script>
