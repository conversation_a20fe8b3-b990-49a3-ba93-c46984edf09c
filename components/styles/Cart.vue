<template>
  <div class="mx-auto mt-4 flex w-full max-w-4xl flex-col items-center justify-center space-y-2 rounded-lg border border-gray-200/10 bg-white p-4 shadow backdrop-blur-md md:flex-row md:space-y-0">
    <LoadingWrapper :is-loading="isLoading" title="Processing payment..." class="flex flex-col items-center justify-between md:flex-row">
      <p>
        Purchase
        <strong>{{ selectedStyles.length * numberOfItems }} photos</strong>
        in
        <strong>{{ selectedStyles.length }} styles</strong>

        for {{ formatPrice((cartTotal) / 100) }}
      </p>
      <button aria-label="Primary Button" type="button" class="gradient-bg ml-4 flex content-center items-center justify-center rounded-md border border-transparent px-4 py-2 text-center text-sm font-bold leading-4 text-white shadow-sm hover:border-black hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-brand-200 focus:ring-offset-2" style="flex-shrink: 0" @click="$emit('purchase')">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="mr-1.5 h-4 w-4 text-white"
          data-v-fac1f164=""
        ><path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m6-6H6" /></svg>
        <span>Unlock photos</span>
      </button>
    </LoadingWrapper>
  </div>
</template>

<script>
export default {
  props: {
    selectedStyles: {
      type: Array,
      default: () => []
    },
    numberOfItems: {
      type: Number,
      default: 10
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      price: 299
    }
  },
  computed: {
    purchasedUpsell () {
      return this.$store.state.user.purchasedUpsell
    },
    cartTotal () {
      const { purchasedUpsell, selectedStyles, price } = this
      const totalPrice = selectedStyles.length * price
      if (!purchasedUpsell) { return totalPrice - 199 }
      return totalPrice
    }
  }
}
</script>

<style></style>
