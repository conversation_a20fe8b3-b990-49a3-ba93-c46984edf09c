<template>
  <component :is="type">
    <!-- div, span, etc -->
    <template v-if="variantId === 'control'">
      <slot name="control" />
    </template>
    <template v-else>
      <slot :name="variantId" />
    </template>
  </component>
</template>

<script>
import PosthogMixin from '~/mixins/PosthogMixin.js'

export default {
  mixins: [PosthogMixin],
  props: {
    id: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: 'div'
    }
  },
  computed: {
    variantId () {
      if (this.featureFlagExists(this.id)) {
        return this.getFeatureFlagVariant(this.id)
      }
      return 'control'
    }
  }
}
</script>

<style>

</style>
