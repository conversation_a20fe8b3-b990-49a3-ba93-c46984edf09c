<template>
  <div>
    <template v-if="shoots?.length > 0">
      <div class="space-y-4">
        <template v-for="shoot in shoots">
          <div :key="shoot._id" class="flex items-center justify-between gap-3">
            <ShootDeletedThumbnail v-if="isDeleted(shoot)" />
            <ShootOnboardingThumbnail v-else-if="isOnboarding(shoot)" />
            <ShootLoadingThumbnail v-else-if="isLoading(shoot)" />
            <div
              v-else
              class="inline-flex items-center justify-center overflow-hidden rounded-full ring-1 ring-transparent size-8 sm:size-12 md:size-14 shrink-0 bg-gray-50"
            >
              <img v-if="shoot?.thumbnail" class="object-cover w-full h-full" :src="shoot.thumbnail" alt="">
            </div>

            <div class="flex-1 min-w-0">
              <p class="text-sm font-bold tracking-tight sm:text-base text-primary-500">
                {{ stripLength(shoot.title, 20) }}
              </p>
              <p v-if="isOnboarding(shoot)" class="mt-px text-xs font-medium text-gray-500 sm:text-sm">
                You still haven't finished setting up this model.
              </p>
              <p v-else-if="isLoading(shoot)" class="mt-px text-xs font-medium text-gray-500 sm:text-sm">
                Generating headshots. Takes up to 2 hours.
              </p>
              <p v-else-if="isActive(shoot)" class="mt-px text-xs font-medium text-gray-500 sm:text-sm">
                Created on {{ formatDate(shoot.createdAt) }}
              </p>
              <p v-if="isPendingRefund(shoot)" class="mt-px text-xs font-medium text-gray-500 sm:text-sm">
                Pending refund. We will get back to you shortly.
              </p>
              <p v-else-if="isRefunded(shoot)" class="mt-px text-xs font-medium text-gray-500 sm:text-sm">
                You've refunded this shoot
              </p>
              <p v-else-if="isDeleted(shoot)" class="mt-px text-xs font-medium text-gray-500 sm:text-sm">
                <template v-if="createdMoreThan30DaysAgo(shoot)">
                  Fully deleted after 30 days for privacy reasons
                </template>
                <template v-else>
                  You've deleted this shoot
                </template>
              </p>
            </div>

            <div v-if="isOnboarding(shoot)" class="flex items-center justify-end gap-2 ml-auto">
              <nuxt-link :to="localePath('/app/upload')">
                <button
                  type="button"
                  class="text-sm font-medium text-primary-500 gap-1.5 rounded-lg shadow-sm bg-white border border-gray-200 transition-all duration-150 hover:bg-gray-50 px-3 pt-1 pb-1.5 inline-flex items-center justify-center"
                >
                  <span>Continue</span>
                </button>
              </nuxt-link>
            </div>
            <div v-else-if="isActive(shoot) || isGeneratingButHasHeadshots(shoot)" class="flex items-center justify-end gap-2 ml-auto">
              <nuxt-link :to="localePath('/app/results/' + shoot._id)">
                <button
                  type="button"
                  class="text-sm font-medium text-primary-500 gap-1.5 rounded-lg shadow-sm bg-white border border-gray-200 transition-all duration-150 hover:bg-gray-50 px-3 pt-1 pb-1.5 inline-flex items-center justify-center"
                >
                  <template v-if="isActive(shoot)">
                    <span class="hidden sm:inline-block">View</span> Results
                  </template>
                  <template v-else>
                    <span>Preview</span>
                  </template>
                </button>
              </nuxt-link>
              <ButtonDropdown
                v-if="!isTeamMember"
                class="hidden md:flex"
                :items="photoActions"
                title="Actions"
                theme="v2"
                size="xs"
                @select="handleAction($event, shoot)"
              />
            </div>
          </div>
          <hr :key="'hr_' + shoot._id" class="bg-gray-200">
        </template>

        <div v-if="$store.state.user.role === 'User' || (isTeamMember && hasRetry) || (isTeamLead && hasRetry)">
          <button
            type="button"
            class="text-base inline-flex w-full font-medium text-white rounded-lg shadow-sm bg-primary-500 gap-1.5 pt-2 pb-2.5 px-3.5 border border-transparent transition-all duration-150 hover:bg-opacity-90 items-center justify-center disabled:bg-opacity-20"
            @click="$router.push(localePath('/app/add'))"
          >
            Create another headshot
          </button>
        </div>
        <div v-if="isTeamLead" class="mt-4 space-y-1 text-center text-sm text-gray-300">
          <NuxtLink to="/app/admin">
            <button
              type="button"
              class="text-base inline-flex w-full font-medium text-white rounded-lg shadow-sm bg-primary-500 gap-1.5 pt-2 pb-2.5 px-3.5 border border-transparent transition-all duration-150 hover:bg-opacity-90 items-center justify-center disabled:bg-opacity-20"
            >
              To Team Dashboard
            </button>
          </NuxtLink>
        </div>
        <div v-if="$store.state.user.role === 'TeamLead' && $store.state.organization.organization.credits > 0">
          <p class="text-xs italic text-gray-400 text-left pt-2">
            Don't want to let your team members upload their own headshots? You can upload headshots for them instead <nuxt-link class="underline" to="/app/add">
              here
            </nuxt-link>.
          </p>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="space-y-2">
        <div>
          <h2 class="text-xl font-bold">
            Welcome to HeadshotPro
          </h2>
          <p class="pb-2 text-base text-gray-700">
            Create your own professional headshots, without the need for a photographer. Start creating your own headshots, or create a team and let your team members create their own headshots.
          </p>
        </div>
        <div class="mt-4 space-y-1 text-center text-sm text-gray-300">
          <NuxtLink to="/app/add">
            <ButtonPrimary class="w-full">
              {{ isTeamMember || isTeamLead ? `Create your headshot for ${organization}` : "Create your own headshot" }}
            </ButtonPrimary>
          </NuxtLink>
          <div v-if="!isTeamMember" class="pb-1">
            OR
          </div>
          <NuxtLink v-if="!isTeamMember" :to="localePath(isTeamLead ? '/app/admin' : '/app/admin/team/new')">
            <ButtonWhite class="w-full">
              {{ isTeamLead ? "Go to admin dashboard" : "Create a new team" }}
            </ButtonWhite>
          </NuxtLink>
          <p class="text-xs italic text-gray-400 text-left pt-2">
            Expected to see your results here? Try refreshing this page or a different browser. If you still don't see your results, please contact us.
          </p>
        </div>
      </div>
    </template>
    <ModelDeleteModal v-if="showDeleteModal" :tune="selectedTune" @closeModal="showDeleteModal = false; selectedTune = {}" @success="handleDeleteSuccess" />
  </div>
</template>

<script>
export default {
  props: {
    shoots: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      photoActions: [
        { title: 'Delete', value: 'delete-tune' }
      ],
      showDeleteModal: false
    }
  },
  computed: {
    hasRetry () {
      return this.$store.state?.user?.hasRetry || false
    },
    isTeamLead () {
      return this.$store?.state?.user?.role === 'TeamLead'
    },
    isTeamMember () {
      return this.$store?.state?.user?.role === 'TeamMember'
    },
    organization () {
      return this.$store?.state?.organization?.organization?.name || 'your organization'
    }
  },
  methods: {
    createdMoreThan30DaysAgo (shoot) {
      const shootDate = new Date(shoot.createdAt)
      const today = new Date()
      const diffTime = Math.abs(today - shootDate)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      return diffDays > 30
    },
    handleAction (value, data) {
      if (value === 'delete-tune') {
        this.setupDeleteTune(data)
      }
      if (value === 'refund') {
        this.$router.push('/profile/refund')
      }
    },
    setupDeleteTune (data) {
      this.showDeleteModal = true
      this.selectedTune = data
    },
    handleDeleteSuccess () {
      this.showDeleteModal = false
      this.selectedTune = {}
      this.$emit('refresh')
    },
    isDeleted (shoot) {
      return shoot.status === 'deleted'
    },
    isOnboarding (shoot) {
      return shoot.status === 'onboarding'
    },
    isLoading (shoot) {
      return ['pending', 'generatingHeadshots', 'waiting'].includes(shoot.status)
    },
    isActive (shoot) {
      return shoot.status === 'active'
    },
    isPendingRefund (shoot) {
      return shoot.status === 'pending-refund'
    },
    isRefunded (shoot) {
      return shoot.status === 'refunded'
    },
    isGeneratingButHasHeadshots (shoot) {
      return shoot.status === 'generatingHeadshots' && shoot?.imageCount > 0
    }
  }
}
</script>

  <style></style>
