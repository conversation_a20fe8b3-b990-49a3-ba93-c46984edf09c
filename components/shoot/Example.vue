<template>
  <div>
    <div class="flex flex-col w-full" :class="{'justify-start items-start': index % 2 == 0, 'justify-end items-end': index % 2 != 0}">
      <h3 class="mb-2">
        <strong class="font-bold text-teal-500 mr-1.5">{{ item.name }}</strong><span class="text-normal text-gray-600" v-html="item.title" />
      </h3>
      <div class="flex space-x-4 items-center max-w-full overflow-hidden">
        <ImageDns
          v-if="index % 2 !== 0"
          alt="Arrow"
          :src="require('@/assets/img/shoot/arrow-left.png')"
          class="w-[24px h-[24px] md:w-[50px] md:h-[50px]"
          width="50"
          height="50"
        />
        <ImageDns :alt="item.alt || 'Avatar'" :src="require(`@/assets/img/shoot/${item.value}/input.png`)" class="w-[80%] max-w-full md:w-[350px]" width="350" height="60" />
        <ImageDns
          v-if="index % 2 === 0"
          alt="Arrow"
          :src="require('@/assets/img/shoot/arrow-right.png')"
          class="w-[24px h-[24px] md:w-[50px] md:h-[50px]"
          width="50"
          height="50"
        />
      </div>
    </div>
    <div :class="`grid grid-cols-2 gap-4 mt-4  sm:gap-2`" :style="`grid-template-columns: repeat(${cols},minmax(0,1fr))`">
      <div v-for="index of examples" :key="item.value + index" class="relative aspect-[5/4] rounded-md object-cover w-full h-full">
        <div class="bg-teal-600/70 backdrop-blur-md text-white text-[9px] font-medium px-1.5 py-0.5 rounded-md absolute top-2 left-2 z-10 opacity-70 uppercase">
          AI GENERATED
        </div>
        <ImageDns

          width="192"
          height="288"
          class="aspect-[5/4] rounded-md object-cover w-full h-full"
          :src="require(`@/assets/img/shoot/${item.value}/${index}.png`)"
          :alt="item.examplesAlt?.[index - 1] || 'Headshot Example'"
        />
      </div>
    </div>
    <div class=" lg:grids-cols-5 hidden" />
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      default: 0
    },
    examples: {
      type: Number,
      default: 10
    },
    cols: {
      type: Number,
      default: 2
    }
  }

}
</script>

<style>

</style>
