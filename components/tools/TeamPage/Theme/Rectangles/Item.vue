<template>
  <div class="relative group">
    <ToolsTeamPagePartialsFavoritePicture
      v-if="favoritePictures?.length > 0"
      :initial-value="styleImage"
      image-class="w-full aspect-[4/3] object-cover rounded-xl overflow-hidden"
      :is-editable="editable"
      :favorite-pictures="favoritePictures"
      @update="updatePicture"
    />
    <ToolsTeamPagePartialsPicture
      v-else
      :initial-value="styleImage"
      image-class="w-full aspect-[4/3] object-cover rounded-xl overflow-hidden"
      :is-editable="editable"
      @update="updatePicture"
    />
    <ToolsTeamPagePartialsText
      :initial-value="member.name"
      placeholder="Member name"
      :is-editable="editable"
      text-class="text-lg font-bold text-black placeholder-black text-left mt-4"
      @update="updateName"
    />
    <ToolsTeamPagePartialsText
      :initial-value="member.role"
      placeholder="Member role"
      :is-editable="editable"
      text-class="text-gray-600 placeholder-gray-600 text-left"
      @update="updateRole"
    />
    <ToolsTeamPagePartialsDeleteButton
      v-if="editable && member.id"
      class="absolute right-0 top-0 p-2 text-white bg-black bg-opacity-50 rounded-md rounded-tr-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200"
      :team-id="teamId"
      :member-id="member.id"
      @deleted-member="deletedMember"
    />
  </div>
</template>

<script>
import TeamPageTeamItemMixin from '../../../../../mixins/tools/TeamPage/TeamPageTeamItemMixin'
export default {
  mixins: [TeamPageTeamItemMixin]
}
</script>
