<template>
  <div class="mx-auto max-w-7xl px-4">
    <div class="relative overflow-hidden rounded-xl bg-teal-500 shadow-lg">
      <div v-if="saved" class="relative flex flex-col items-center lg:flex-row lg:items-center">
        <div class="px-6 py-12 text-center sm:p-12 lg:py-16 lg:text-left xl:p-20">
          <h5 class="text-3xl font-bold text-white sm:text-3xl lg:text-3xl">
            Your team page was saved!
          </h5>
          <p class="mt-5 text-base font-normal text-white">
            Check your email address. You should have gotten an email with a link to your team page that you can use for editing your team page. Enjoy!
          </p>
        </div>
      </div>
      <div v-else class="relative flex flex-col items-center lg:flex-row lg:items-center">
        <div class="px-6 py-12 text-center sm:p-12 lg:py-16 lg:text-left xl:p-20">
          <h5 class="text-3xl font-bold text-white sm:text-3xl lg:text-3xl">
            Save your team page
          </h5>
          <p class="mt-5 text-base font-normal text-white">
            Add here your email address and we will send you an email with a link to your team page, so you can access and edit it later.
          </p>
        </div>

        <LoadingWrapper :is-loading="isLoading" class="mt-8 p-6 sm:p-12 lg:p-20 flex-shrink-0 w-full lg:w-[30rem]">
          <form class="flex flex-col items-center justify-center" @submit.prevent="saveTeam">
            <Input v-model="email" class="w-full" placeholder="<EMAIL>" />
            <ButtonPrimary type="submit" size="sm" class="mt-4 w-full">
              Save team
            </ButtonPrimary>
          </form>
        </LoadingWrapper>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    teamId: {
      type: String,
      required: true
    },
    alreadySaved: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      email: '',
      isLoading: false,
      saved: this.alreadySaved
    }
  },
  methods: {
    saveTeam () {
      this.isLoading = true
      this.$axios.post(`tools/team-page/page/${this.teamId}/magic-link`, {
        email: this.email
      }).then((res) => {
        this.saved = true
      }).catch((err) => {
        if (err.response?.data?.data?.alreadyGenerated) {
          this.saved = true
        }

        this.$toast.error(err.response?.data?.errorMessage || 'Failed to save team. Please, try again later.')
      }).finally(() => {
        this.isLoading = false
      })
    }
  }
}
</script>
