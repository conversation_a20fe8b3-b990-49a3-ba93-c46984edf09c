<template>
  <LoadingWrapper :is-loading="isLoading">
    <div v-if="team">
      <div v-if="!isIframe" class="mx-auto max-w-7xl px-4 mb-8">
        <div class="flex justify-start items-center">
          <h3 class="font-bold text-sm text-left flex-shrink-0 w-20">
            Theme
          </h3>
          <div class="flex justify-start gap-2 flex-wrap">
            <component :is="themeComponentFor('default')" size="xs" @click="setTheme('default')">
              Grid
            </component>
            <component :is="themeComponentFor('circleGrid')" size="xs" @click="setTheme('circleGrid')">
              Circle Grid
            </component>
            <component :is="themeComponentFor('rectangles')" size="xs" @click="setTheme('rectangles')">
              Rectangles
            </component>
          </div>
        </div>

        <div v-if="hasStylizer" class="mt-4 flex justify-start items-center">
          <h3 class="font-bold text-sm text-left flex-shrink-0 w-20">
            Style
          </h3>
          <div class="flex justify-start gap-2 flex-wrap">
            <component :is="styleComponentFor(null)" size="xs" @click="changeStyle(null)">
              Default
            </component>
            <component :is="styleComponentFor('anime')" size="xs" @click="changeStyle('anime')">
              Anime
            </component>
            <component :is="styleComponentFor('cartoon')" size="xs" @click="changeStyle('cartoon')">
              Cartoon
            </component>
            <component :is="styleComponentFor('warhol')" size="xs" @click="changeStyle('warhol')">
              Warhol
            </component>
            <component :is="styleComponentFor('popart')" size="xs" @click="changeStyle('popart')">
              Popart
            </component>
            <component :is="styleComponentFor('vangogh')" size="xs" @click="changeStyle('vangogh')">
              Van Gogh
            </component>
          </div>
        </div>
      </div>

      <component :is="pageLayout" ref="pageLayout">
        <component
          :is="pageItem"
          v-for="(member) in team.members"
          :key="member.id"
          :member="member"
          :team-id="teamId"
          :selected-style="style"
          :favorite-pictures="favoritePicturesFor(member)"
          @deleted-member="deleteMember"
          @updated-member="getTeam(true)"
        />
        <component
          :is="pageItem"
          v-if="canAdd && canAddMore && !isIframe"
          :key="'new-member-'+team.members.length"
          :team-id="teamId"
          :member="{
            name: '',
            role: '',
            basePictureUrl: '',
          }"
          @added-member="addMember"
        />
      </component>
    </div>
    <div v-if="canDownload && !isIframe" class="flex justify-center flex-wrap gap-4 mt-8">
      <ButtonWhite v-if="hasStylizer" :is-disabled="isStylizing" @click="downloadAsZip">
        Download images as ZIP
      </ButtonWhite>
      <ButtonPrimary @click="publishChanges">
        {{ hasStylizer ? 'Publish changes' : 'Copy embed code' }}
      </ButtonPrimary>
    </div>
    <div v-if="showEmbedInstructions" class="mt-4 rounded-md bg-teal-50 text-primary-500 p-4 max-w-3xl mx-auto">
      <p>Follow these steps to add this team page to your website:</p>
      <ol>
        <li class="flex justify-start items-center space-x-4 mt-4">
          <span class="flex-shrink-0 h-8 w-8 bg-primary-500 rounded-full flex items-center justify-center text-white">1.</span>
          <p>Create a &lt;div&gt; tag where you want to put your team page, and set it an id:</p>
        </li>
        <li class="relative group">
          <pre class="overflow-auto w-full bg-primary-500 p-4 rounded-lg mt-4 cursor-pointer" @click="copyWrapperCode"><code class="w-full block text-white">{{ wrapperCode }}</code></pre>
          <IconCopy class="h-4 w-4 absolute top-1 right-1 opacity-50 text-white transition group-hover:opacity-80" />
        </li>
        <li class="flex justify-start items-center space-x-4 mt-8">
          <span class="flex-shrink-0 h-8 w-8 bg-primary-500 rounded-full flex items-center justify-center text-white">2.</span>
          <p>Add the script tag with the embed code before your &lt;/body&gt; tag</p>
        </li>
        <li class="relative group">
          <pre class="overflow-auto w-full bg-primary-500 p-4 rounded-lg mt-4 cursor-pointer" @click="copyEmbedCode"><code class="w-full block text-white">{{ embedCode }}</code></pre>
          <IconCopy class="h-4 w-4 absolute top-1 right-1 opacity-50 text-white transition group-hover:opacity-80" />
        </li>
      </ol>
    </div>
  </LoadingWrapper>
</template>

<script>
import copy from 'copy-to-clipboard'

export default {
  props: {
    teamPage: {
      type: Object,
      required: false
    },
    members: {
      type: Array,
      required: false
    },
    teamId: {
      type: String,
      required: false
    },
    hasStylizer: {
      type: Boolean,
      default: false
    },
    maxMembers: {
      type: Number,
      default: 10
    },
    canAdd: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      isLoading: false,
      team: this.teamPage || null,
      theme: 'default',
      style: null,
      interval: null,
      showEmbedInstructions: false,
      prodId: null
    }
  },
  computed: {
    pageLayout () {
      return {
        default: 'ToolsTeamPageThemeDefaultLayout',
        circleGrid: 'ToolsTeamPageThemeCircleGridLayout',
        rectangles: 'ToolsTeamPageThemeRectanglesLayout'
      }[this.theme]
    },
    pageItem () {
      return {
        default: 'ToolsTeamPageThemeDefaultItem',
        circleGrid: 'ToolsTeamPageThemeCircleGridItem',
        rectangles: 'ToolsTeamPageThemeRectanglesItem'
      }[this.theme]
    },
    isStylizing () {
      if (!this.style || !this.hasStylizer) {
        return false
      }

      return this.team.members.filter(member => ['completed', 'error'].includes(member.styles?.[this.style]?.status)).length !== this.team.members.length
    },
    canAddMore () {
      return !this.isStylizing && !this.style && this.team.members.length < this.maxMembers
    },
    canDownload () {
      return this.team?.members?.length > 0
    },
    embedCode () {
      // getting a compile error if I write the tag directly so had to split it
      let embedCode = `<script src="${process.env.BASE_URL}/scripts/team-page.js" data-team-id="${this.prodId || this.teamId}" data-id="my-team"`
      if (this.style) {
        embedCode += ` data-style="${this.style}"`
      }
      if (process.env.NODE_ENV === 'development') {
        embedCode += ' data-dev'
      }
      embedCode += '></' + 'script' + '>'
      return embedCode
    },
    wrapperCode () {
      return '<div id="my-team"></div>'
    }
  },
  mounted () {
    if (!this.team) {
      this.getTeam()
    } else {
      this.theme = this.team.style || 'default'
      if (this.$route.query.style) {
        this.style = this.$route.query.style
      }
    }

    this.interval = setInterval(() => {
      if (this.isStylizing) {
        this.getTeam(true)
      }
    }, 5000)

    this.$nextTick(() => {
      this.postIframeSize()
    })

    window.addEventListener('resize', this.postIframeSize)
  },
  beforeDestroy () {
    clearInterval(this.interval)
    window.removeEventListener('resize', this.postIframeSize)
  },
  methods: {
    postIframeSize () {
      if (!this.$refs.pageLayout) {
        return
      }

      window.parent.postMessage({
        type: 'GENERATOR_EVENT',
        payload: {
          height: this.$refs.pageLayout.$el.scrollHeight
        }
      }, '*')
    },
    getTeam (silent = false) {
      if (!silent) {
        this.isLoading = true
      }

      const query = this.isIframe ? '?prod=1' : ''
      this.$axios.get('/tools/team-page/page/' + this.teamId + query)
        .then((res) => {
          this.team = res.data.data
          this.theme = this.team.style || 'default'
          if (this.$route.query.style) {
            this.style = this.$route.query.style
          }
        })
        .catch(() => {
          this.$toast.error('Failed to fetch team data')
        })
        .finally(() => {
          if (!silent) {
            this.isLoading = false

            this.$nextTick(() => {
              setTimeout(() => {
                this.postIframeSize()
              }, 100)
            })
          }
        })
    },
    changeStyle (style) {
      if (!this.canChangeStyle()) {
        this.$toast.error('All members must have a picture to change the style')
        return
      }

      if (this.isStylizing && style) {
        this.$toast.error('Please wait for the current style change to finish')
        return
      }

      if (!style || !this.hasStylizer) {
        this.style = null
        this.$router.push({ query: { style: null } })
        return
      }

      if (this.team.members.filter(member => !member.styles?.[style]).length === 0) {
        this.style = style
        this.$router.push({ query: { style } })
        return
      }

      this.isLoading = true
      this.$axios.post('/tools/team-page/page/' + this.teamId + '/stylize', {
        style
      })
        .then((res) => {
          this.team = res.data.data
          this.$toast.success('Style change is being processed. Results will be automatically refreshed')
          this.style = style
          this.$router.push({ query: { style } })
          this.$emit('updated-team')
        })
        .catch(() => {
          this.$toast.error('Failed to change style')
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    addMember (member) {
      this.team.members.push(member)
      this.$emit('updated-team')
    },
    deleteMember (memberId) {
      this.team.members = this.team.members.filter(m => m.id !== memberId)
      this.$emit('updated-team')
    },
    setTheme (theme) {
      this.isLoading = true
      this.$axios.post('/tools/team-page/page/' + this.teamId + '/theme', {
        theme
      })
        .then(() => {
          this.$toast.success('Theme changed')
          this.theme = theme
          this.$emit('updated-team')
        })
        .catch(() => {
          this.$toast.error('Failed to change theme')
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    styleComponentFor (style) {
      return style === this.style ? 'ButtonPrimary' : 'ButtonWhite'
    },
    themeComponentFor (theme) {
      return theme === this.theme ? 'ButtonPrimary' : 'ButtonWhite'
    },
    async downloadAsZip () {
      if (!this.team || !this.team.members) {
        return
      }

      this.isLoading = true
      try {
        const response = await this.$axios.get(`/tools/team-page/page/${this.teamId}/download-zip`, {
          params: {
            style: this.style
          },
          responseType: 'blob'
        })

        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', 'team-images.zip')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        this.$toast.success('Download started')
      } catch (error) {
        this.$toast.error('Failed to download images')
      } finally {
        this.isLoading = false
      }
    },
    copyEmbedCode () {
      copy(this.embedCode)
      this.$toast.success('Embed code copied to clipboard!')
    },
    copyWrapperCode () {
      copy(this.wrapperCode)
      this.$toast.success('Code copied to clipboard!')
    },
    restart () {
      this.$emit('restart')
    },
    favoritePicturesFor (member) {
      return this.members?.find(m => m._id === member.id)?.pictures || null
    },
    canChangeStyle () {
      return this.team.members.filter(member => !member.basePictureUrl).length === 0
    },
    publishChanges () {
      if (this.isLoading || this.isStylizing) {
        return
      }

      if (!this.canChangeStyle()) {
        this.$toast.error('All members must have a picture to publish changes')
        return
      }

      if (!this.hasStylizer) {
        this.showEmbedInstructions = true
        return
      }

      this.isLoading = true

      this.$axios.post('/tools/team-page/organization/publish')
        .then((res) => {
          this.prodId = res.data.data._id
          this.$toast.success('Changes published')
          this.showEmbedInstructions = true
        })
        .catch(() => {
          this.$toast.error('Failed to publish changes')
        })
        .finally(() => {
          this.isLoading = false
        })
    }
  }
}
</script>
