<template>
  <div v-if="isEditable && !isIframe">
    <input v-model="value" :class="`appearance-none bg-transparent w-full ${textClass} focus:outline-none focus:shadow-none`" :placeholder="placeholder">
  </div>
  <div v-else>
    <span :class="`block w-full ${textClass}`">{{ value || placeholder }}</span>
  </div>
</template>

<script>
export default {
  props: {
    initialValue: {
      type: String,
      required: true
    },
    placeholder: {
      type: String,
      required: true
    },
    isEditable: {
      type: Boolean,
      required: false,
      default: true
    },
    textClass: {
      type: String,
      required: false,
      default: ''
    }
  },
  data () {
    return {
      value: this.initialValue
    }
  },
  watch: {
    initialValue (newVal) {
      this.value = newVal
    },
    value (newVal) {
      this.$emit('update', newVal)
    }
  }
}
</script>
