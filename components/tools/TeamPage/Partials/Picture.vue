<template>
  <div v-if="isEditable && !isIframe" key="picture-editable">
    <label :class="`relative overflow-hidden flex items-center justify-center bg-gray-100 cursor-pointer ${imageClass}`">
      <input type="file" class="sr-only" @change="updatePicture">
      <img v-if="value" :src="value" class="w-full h-full object-cover object-center absolute left-0 top-0">
      <IconUpload v-else class="w-12 h-12 text-primary-500" />
    </label>
  </div>
  <div v-else key="picture-not-editable">
    <img :src="value" :class="`${imageClass}`">
  </div>
</template>

<script>
export default {
  props: {
    initialValue: {
      type: String,
      required: true
    },
    isEditable: {
      type: Boolean,
      required: false,
      default: true
    },
    imageClass: {
      type: String,
      required: false,
      default: ''
    }
  },
  data () {
    return {
      uploadedUrl: null
    }
  },
  computed: {
    value () {
      return this.uploadedUrl || this.initialValue
    }
  },
  methods: {
    updatePicture (event) {
      const file = event.target.files[0]
      const formData = new FormData()
      formData.append('files', file)
      formData.append('folder', 'team')
      formData.append('public', 1)
      formData.append('requiresFullUrl', 1)
      formData.append('maxWidth', 600)
      this.$axios.post('/image/upload', formData)
        .then((res) => {
          this.uploadedUrl = res.data.data.url
          this.$emit('update', this.uploadedUrl)
        })
        .catch((err) => {
          console.error(err)
          this.$toast.error('Failed to upload image')
        })
    }
  }
}
</script>
