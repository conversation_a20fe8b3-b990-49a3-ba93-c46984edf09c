<template>
  <div v-if="isEditable && !isIframe">
    <div :class="`relative overflow-hidden flex items-center justify-center bg-gray-100 ${imageClass}`">
      <img :src="currentPicture" alt="" class="w-full h-full object-cover object-center absolute left-0 top-0">
      <div v-if="!styleIsSelected" class="absolute top-0 h-full left-0 flex items-center justify-center">
        <button class="p-2 bg-black bg-opacity-50" @click="prevPicture">
          <IconChevron class="w-6 h-6 text-white rotate-180" />
        </button>
      </div>
      <div v-if="!styleIsSelected" class="absolute top-0 h-full right-0 flex items-center justify-center">
        <button class="p-2 bg-black bg-opacity-50" @click="nextPicture">
          <IconChevron class="w-6 h-6 text-white" />
        </button>
      </div>
      <label v-if="!styleIsSelected" class="absolute p-2 top-0 left-0 flex items-center justify-center bg-black bg-opacity-75 cursor-pointer">
        <input type="file" class="sr-only" @change="updatePicture">
        <IconUpload class="w-4 h-4 text-white" />
      </label>
    </div>
  </div>
  <div v-else>
    <img :src="value" :class="`${imageClass}`">
  </div>
</template>

<script>
export default {
  props: {
    initialValue: {
      type: String,
      required: true
    },
    isEditable: {
      type: Boolean,
      required: false,
      default: true
    },
    favoritePictures: {
      type: Array,
      required: true
    },
    imageClass: {
      type: String,
      required: false,
      default: ''
    }
  },
  data () {
    return {
      uploadedUrl: null,
      selectedPicture: 0
    }
  },
  computed: {
    value () {
      return this.uploadedUrl || this.initialValue
    },
    slider () {
      return [...new Set([this.value, ...this.favoritePictures])]
    },
    currentPicture () {
      return this.slider[this.selectedPicture]
    },
    styleIsSelected () {
      return (this.$route.query.style || 'default') !== 'default'
    }
  },
  methods: {
    updatePicture (event) {
      const file = event.target.files[0]
      const formData = new FormData()
      formData.append('files', file)
      formData.append('folder', 'team')
      formData.append('public', 1)
      formData.append('requiresFullUrl', 1)
      formData.append('maxWidth', 600)
      this.$axios.post('/image/upload', formData)
        .then((res) => {
          this.uploadedUrl = res.data.data.url
          this.selectedPicture = 0
          this.$emit('update', this.uploadedUrl)
        })
        .catch((err) => {
          console.error(err)
          this.$toast.error('Failed to upload image')
        })
    },
    nextPicture () {
      this.selectedPicture = (this.selectedPicture + 1) % this.slider.length
      this.$emit('update', this.currentPicture)
    },
    prevPicture () {
      this.selectedPicture = (this.selectedPicture - 1 + this.slider.length) % this.slider.length
      this.$emit('update', this.currentPicture)
    }
  }
}
</script>
