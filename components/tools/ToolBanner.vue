<template>
  <section class="bg-[#F8FCFF] py-12 sm:py-16">
    <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8 2xl:px-0">
      <div class="max-w-3xl mx-auto text-left md:text-center">
        <p class="text-base font-normal text-paragraph">
          {{ pretitle }}
        </p>
        <h1
          v-if="title"
          class="mt-3 text-2xl font-bold tracking-[-1.05px] sm:text-3xl lg:text-[42px] text-primary-500 lg:leading-[48px]"
        >
          {{ title }}
        </h1>
        <slot name="raw-title" />
        <p class="text-base font-medium sm:text-lg lg:text-xl text-[#474368] mt-5">
          {{ subtitle }}
        </p>

        <p class="mt-5 text-base font-normal sm:text-lg text-paragraph">
          <slot name="description" />
        </p>
      </div>

      <slot />
    </div>
  </section>
</template>

<script>
export default {
  props: {
    pretitle: {
      type: String,
      required: true
    },
    title: {
      type: String,
      required: false
    },
    subtitle: {
      type: String,
      required: true
    }
  }
}
</script>
