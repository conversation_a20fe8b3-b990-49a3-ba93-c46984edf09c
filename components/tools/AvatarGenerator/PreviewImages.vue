<template>
  <div class="mx-auto mt-8 max-w-7xl">
    <h2 class="text-center text-gray-500">
      Some portraits of real people 👇
    </h2>
    <div class="mt-6 grid grid-cols-3 gap-6 sm:grid-cols-4 lg:grid-cols-5">
      <img
        v-for="(image, n) in images"
        :key="image"
        :src="image"
        class="rounded border-8 border-white shadow-lg"
        :class="n % 2 ? 'rotate-1 md:rotate-2' : '-rotate-1 md:-rotate-2'"
        loading="lazy"
      >
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      images: [
        '/avatar-results/danny-1.webp',
        '/avatar-results/danny-2.webp',
        '/avatar-results/danny-3.webp',
        '/avatar-results/danny-4.webp',
        '/avatar-results/random-1.webp',
        '/avatar-results/fayaz-3.webp',
        '/avatar-results/fayaz-2.webp',
        '/avatar-results/fayaz-4.webp',
        '/avatar-results/naz-8.webp',
        '/avatar-results/naz-4.webp',
        '/avatar-results/random-2.webp',
        '/avatar-results/naz-2.webp',
        '/avatar-results/naz-1.webp',
        '/avatar-results/naz-3.webp',
        '/avatar-results/naz-5.webp'
      ]
    }
  }
}
</script>
