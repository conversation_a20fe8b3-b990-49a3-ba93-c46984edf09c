<template>
  <div class="mx-auto max-w-4xl py-8">
    <div v-if="!portrait" class="text-center flex flex-col items-center gap-3">
      <!-- <div v-if="rateLimit > 5 && rateLimit < 50" class="text-black/60 rounded-full px-3 text-sm py-1">
        {{ rateLimit }}/50 avatars generated this minute
      </div> -->
      <!-- <div v-if="rateLimit > 50" class="bg-orange-200 text-orange-700/90 rounded-full px-3 text-sm py-1">
        <span>We've hit our limit. Come back in a few minutes</span>
      </div> -->
      <!-- <div class="bg-orange-100 text-orange-700/70 rounded-full px-3 text-sm py-1">
        <span>
          Switched to LLaMA due to rate limits. Return tomorrow for better quality.
        </span>
      </div> -->
      <h1 class="text-2xl md:text-3xl text-brand-500 font-bold">
        Free Profile Picture Generator
      </h1>
      <p class="text-gray-600 text-center max-w-2xl mx-auto">
        Create a cute avatar from your photo. Powered by DALL-E 3. We'll analyse your photo, describe it in words, and generate a new image from scratch.
      </p>
      <input
        ref="fileInput"
        type="file"
        accept="image/*"
        style="display: none"
        @change="$emit('file-selected', $event.target.files[0])"
      >
      <ButtonPrimary
        v-if="!portrait && !loading"
        type="button"
        class="gradient-bg"
        @click="$emit('open-file-picker', $refs.fileInput)"
      >
        <span>Upload one photo</span>
        <IconUpload class="ml-1.5" />
      </ButtonPrimary>
      <p class="text-sm text-center font-medium text-orange-700">
        Maximum 1 try per day due to high demand.
        <button class="text-sm text-center font-medium text-gray-700 underline" @click="$emit('showUpsell')">
          <span>Want a professional headshot?</span>
          <IconArrowRight class="ml-1.5" />
        </button>
      </p>
    </div>
    <div v-else class="text-center">
      <template v-if="$route?.query?.payment !== 'success'">
        <p class="text-sm md:text-lg font-bold text-teal-500">
          Looking for a professional headshot?
        </p>
        <h3 class="mt-3 font-medium tracking-tight text-3xl text-primary-500">
          Get your professional headshot, <br>without a physical photo shoot
        </h3>
        <p class="text-base text-gray-700 max-w-lg mx-auto my-2">
          <strong>3,439,182</strong> AI headshots already created for <strong>20,691</strong> happy customers!
        </p>
        <div class="flex items-center justify-center py-4">
          <a target="_blank" title="HeadshotPro" href="https://www.headshotpro.com?ref=avatar-generator">
            <ButtonPrimary>
              Get your headshots now
            </ButtonPrimary>
          </a>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    portrait: {
      type: String,
      default: null
    },
    rateLimit: {
      type: Number,
      default: 0
    },
    loading: {
      type: Boolean,
      default: false
    }
  }
}
</script>
