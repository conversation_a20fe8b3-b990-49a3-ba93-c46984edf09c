<template>
  <Card class="mt-4 rounded-lg shadow w-full max-w-2xl min-w-lg">
    <div class="p-4 md:p-8">
      <h2 class="font-medium text-xl">
        Select a headshot type
      </h2>
      <p class="text-sm text-gray-700 mb-4">
        Get any profile pictures in 3 easy steps. Select the package you want, upload your images and select your styles. We'll take care of the rest!
      </p>
      <div class="bg-gray-50 p-4 rounded-md border border-gray-100 space-y-2">
        <span class="block text-sm font-medium leading-5 text-gray-900 mb-2">Who's photos are you uploading?</span>
        <div class="grid grid-cols-3 md:grid-cols-5 gap-2">
          <button v-for="sex in sexOptions" :key="sex.value" class="md:p-2 p-1 px-2 border text-sm rounded-md cursor-pointer" :class="selectedSex === sex.value ? 'border-transparent shadow-sm text-white bg-brand-500 hover:bg-brand-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-200' : 'border-gray-300 shadow-sm text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500'" @click="selectedSex = sex.value">
            {{ sex.title }}
          </button>
        </div>
        <span class="block text-sm font-medium leading-5 text-gray-900 mb-2">What kind of shoot are you looking for?</span>
        <div class="grid grid-cols-3 gap-4">
          <template v-for="style in selectedStyles">
            <div :key="style._id">
              <ImageDns :src="style.image" />
              <span>{{ style.title }}</span>
            </div>
          </template>
        </div>
      </div>
      <OrderButtons />
    </div>
  </Card>
</template>

<script>
// Seed: 11020
export default {
  data () {
    return {
      styles: [],
      sexOptions: [
        { title: 'Male', value: 'male' },
        { title: 'Female', value: 'female' }
      ]
    }
  },
  computed: {
    selectedSex: {
      get () {
        return this.$store.state.onboarding.selectedSex
      },
      set (value) {
        this.$store.commit('onboarding/SET_SELECTED_SEX', value)
      }
    },
    selectedStyles: {
      get () {
        return this.$store.state.onboarding.selectedStyles
      },
      set (value) {
        this.$store.commit('onboarding/SET_SELECTED_STYLES', value)
      }
    }
  },
  watch: {
    selectedSex (newValue, oldValue) {
      this.selectedStyles = this.$store.state.onboarding.allStyles.filter(
        style => style.trigger === newValue
      )
    }
  }

}
</script>

<style>

</style>
