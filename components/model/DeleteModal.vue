<template>
  <Popup size="xl" @closeModal="$emit('closeModal')">
    <div class="flex mb-0.5">
      <img class="h-6 w-6 rounded-full" :src="tune.thumbnail" alt="">
      <div class="ml-2">
        <p class="text-base font-bold text-gray-900">
          Delete data for {{ tune.title }}
        </p>
      </div>
    </div>
    <p class="text-sm text-gray-700  mb-3">
      Deleting all data is permanent and cannot be undone.
    </p>
    <LoadingWrapper :is-loading="isLoading" title="Deleting...">
      <div class="flex flex-col rounded-xl border border-gray-200 overflow-hidden mt-2">
        <div class="w-full flex justify-between p-4 items-center border-b border-gray-200">
          <div class="flex flex-col">
            <h2 class="font-medium text-base">
              Delete all data
            </h2>
            <p class="text-sm text-gray-700">
              Delete the AI model + all photos from our servers.
            </p>
          </div>
          <div class="">
            <ButtonDelete size="sm" @click="deleteTune('all')">
              Delete all
            </ButtonDelete>
          </div>
        </div>
      </div>
    </LoadingWrapper>
  </Popup>
</template>

<script>
export default {
  props: {
    tune: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      isLoading: false
    }
  },
  methods: {
    deleteTune () {
      this.isLoading = true
      this.$axios.$delete('/model/' + this.tune._id)
        .then(() => {
          this.isLoading = false
          this.$toast.success('Model deleted succesfully')
          this.$emit('success')
        })
        .catch((err) => {
          this.isLoading = false
          this.$toast.error('Something went wrong. Try again or contact us.')
          console.log(err)
        })
    }
  }

}
</script>

<style>

</style>
