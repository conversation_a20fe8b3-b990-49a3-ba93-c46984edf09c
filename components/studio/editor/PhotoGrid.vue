<template>
  <div id="content" class="p-4 bg-muted-50 w-full">
    <div v-masonry="containerId" transition-duration="0.3s" item-selector=".item" class="w-full">
      <div v-for="photo in photos" :key="photo._id" v-masonry-tile class="w-1/2 lg:w-1/3 xl:w-1/4 3xl:w-1/4 item p-2">
        <PhotoItem
          :photo="photo"
          @click="openPhoto(photo)"
        />
      </div>
    </div>
    <PaginationInfinity v-if="canLoadMore" @fetch="$store.dispatch('studio/fetchPhotos')" />
    <Popup v-if="selectedPhoto" size="4xl" :show="!!selectedPhoto" @close="selectedPhoto = null">
      <AlertWarningV2 v-if="!selectedPhoto?.images?.hd" text="This is a low quality photo. Make sure to enhance it to get the best results." class="mb-2" />
      <ImageDns :src="selectedPhoto?.images?.hd || selectedPhoto?.images?.full || selectedPhoto?.images?.thumbnail" class="w-full h-full object-cover" />
    </Popup>
  </div>
</template>

<script>
import PhotoItem from './PhotoItem.vue'
export default {
  components: {
    PhotoItem
  },
  data () {
    return {
      selectedPhoto: null,
      containerId: ''
    }
  },
  computed: {
    canLoadMore () {
      return this.$store.state.studio.canLoadMore
    },
    photos () {
      return this.$store.state.studio.photos
    }
  },
  methods: {
    openPhoto (photo) {
      this.selectedPhoto = photo
    }
  }
}
</script>

<style>

</style>
