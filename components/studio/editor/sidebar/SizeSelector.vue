<template>
  <div>
    <Selector label="Size" @click="showPicker = true">
      <template v-if="settings?.size">
        <span>{{ sizeOptions.find(size => size.value === settings?.size)?.label }}</span>
      </template>
      <template v-else>
        No size selected
      </template>
    </Selector>
    <Modal v-if="showPicker" size="4xl" :show="showPicker" @close="showPicker = false">
      <div class="p-4 space-y-2">
        <div class="flex flex-col gap-0.5">
          <Heading>Select size</Heading>
          <Paragraph size="sm">
            Select the size of the photo you want to create.
          </Paragraph>
        </div>
        <div class="grid grid-cols-4 gap-4">
          <div v-for="size in sizeOptions" :key="size.value" class="w-full h-full cursor-pointer hover:scale-105 transition-all duration-200 bg-white rounded-md border border-gray-200 px-2.5 py-1.5" @click="selectValue(size.value)">
            <span>{{ size.label }}</span>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
import Selector from '../../common/Selector.vue'
import Heading from '@/components/landingpage/common/H5.vue'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
export default {
  components: {
    Selector,
    Heading,
    Paragraph
  },
  data () {
    return {
      showPicker: false
    }
  },
  computed: {
    settings () {
      return this.$store.state.studio.settings
    },
    sizeOptions () {
      return [
        {
          label: 'Landscape',
          value: '1024x831'
        },
        {
          label: '1:1',
          value: '1024x1024'
        },
        {
          label: 'Portrait',
          value: '831x1024'
        },
        {
          label: '4:5',
          value: '1024x1280'
        },
        {
          label: '5:4',
          value: '1280x1024'
        },
        {
          label: '16:9',
          value: '1280x720'
        },
        {
          label: '9:16',
          value: '720x1280'
        },
        {
          label: '4:3',
          value: '1024x768'
        },
        {
          label: '5:7',
          value: '1024x1433'
        }
      ]
    }
  },
  methods: {
    selectValue (value) {
      this.showPicker = false
      this.$store.commit('studio/UPDATE_SETTINGS', { key: 'size', value })
    }
  }
}
</script>

<style>

</style>
