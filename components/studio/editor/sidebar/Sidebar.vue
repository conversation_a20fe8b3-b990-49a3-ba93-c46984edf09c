<template>
  <div class="w-full max-w-[300px] h-full bg-white border-r border-gray-200 flex flex-col overflow-y-auto">
    <!-- <div class="w-full bg-slate-100 flex justify-start">
      <button v-for="tab in tabs" :key="tab" class="w-auto text-sm text-gray-500 px-2 py-1" :class="activeTab === tab ? 'bg-white font-bold text-primary-500 border-r border-l border-gray-200' : ''" @click="activeTab = tab">
        <span class="capitalize">{{ tab }}</span>
      </button>
    </div> -->
    <div class="gap-4 p-3 flex flex-col">
      <TemplateSelector />
      <SizeSelector />
      <OutfitSelector />
      <GlassSelector v-if="model?.appearance?.glasses" />
      <HairstyleSelector v-if="model?.appearance?.hairstyle" />
      <!--<ValueSelector label="Hairstyle" settings-name="hairstyle" :default-value="model?.appearance?.hairstyle" /> -->
      <EmotionSelector />
      <QualitySelector />
      <template v-if="settings?.photoTemplateId">
        <ButtonGradient class="!border !border-black/50" @click="createPhoto">
          <span>Create photo</span>
          <div class="ml-2 flex items-center content-center justify-center p-1 px-2 text-xs font-normal text-white bg-black/20 border-black/30 rounded-full shadow-inner">
            <span>Cost: {{ (settings.quality === 'sd' ? 1 : 2) }}</span>
            <IconArrowPathRoundedSquare class="w-3 h-3 ml-1 text-gray-200" />
          </div>
        </ButtonGradient>
      </template>
      <ButtonGray v-else size="sm" :disabled="true">
        Select a photo template first
      </ButtonGray>
      <div class="flex items-center justify-start gap-2">
        <Paragraph size="xs">
          Credits left:
        </Paragraph>
        <StudioCreditTotal />
      </div>
      <hr>
      <Paragraph size="xs" class="text-gray-500">
        This tool is in beta. Things may break. Please <a href="mailto:<EMAIL>" class="underline" target="_blank">report</a> and issues or feedback to me personally.
      </Paragraph>
    </div>
  </div>
</template>

<script>
import TemplateSelector from './PhotoTemplateSelector.vue'
import SizeSelector from './SizeSelector.vue'
import OutfitSelector from './OutfitSelector.vue'
import EmotionSelector from './EmotionSelector.vue'
import GlassSelector from './GlassSelector.vue'
import QualitySelector from './QualitySelector.vue'
import HairstyleSelector from './HairstyleSelector.vue'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'

export default {
  components: {
    TemplateSelector,
    SizeSelector,
    OutfitSelector,
    EmotionSelector,
    GlassSelector,
    QualitySelector,
    Paragraph,
    HairstyleSelector
  },
  data () {
    return {
      tabs: ['guided', 'pro'],
      activeTab: 'guided'
    }
  },
  computed: {
    model () {
      return this.$store.state.studio.model
    },
    settings () {
      return this.$store.state.studio.settings
    }
  },
  methods: {
    async createPhoto () {
      try {
        this.$loading.show()
        await this.$store.dispatch('studio/createPhoto')
      } catch (err) {
        if (err.message === 'Not enough credits') {
          this.$store.commit('studio/SET_POPUP_STATUS', { key: 'creditModal', value: true })
          this.$toast.error('Not enough credits')
        }
      } finally {
        this.$loading.hide()
      }
    }
  }
}
</script>

<style>

</style>
