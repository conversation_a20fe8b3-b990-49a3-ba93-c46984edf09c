<template>
  <div>
    <Selector label="Glasses" @click="showTemplateSelector = !showTemplateSelector">
      {{ displayValue }}
      <template #label>
        <Tooltip info="Depending on your training data, the model may or may not generate glasses. This option will force the model to generate glasses regardless of your training data." />
      </template>
    </Selector>
    <SelectorDropdown v-if="showTemplateSelector" :options="options" :value="value" @select="value = $event" @close="showTemplateSelector = false" />
  </div>
</template>

<script>
import Selector from '@/components/studio/common/Selector.vue'
import SelectorDropdown from '@/components/studio/common/SelectorDropdown.vue'

export default {
  components: {
    Selector,
    SelectorDropdown
  },
  data () {
    return {
      options: [
        { label: 'Force glasses', value: true },
        { label: 'No glasses', value: false }
      ],
      showTemplateSelector: false
    }
  },
  computed: {
    value: {
      get () {
        return this.$store.state.studio.settings.glasses
      },
      set (value) {
        this.$store.commit('studio/UPDATE_SETTINGS', { key: 'glasses', value })
      }
    },
    displayValue () {
      return this.options.find(option => option.value === this.value)?.label
    }
  }
}
</script>

  <style>

  </style>
