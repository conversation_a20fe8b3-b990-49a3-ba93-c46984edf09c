<template>
  <div>
    <Selector label="Emotion" :clearable="settings?.emotionPrompt !== null" @click="showPicker = true" @clear="clearValue">
      <div v-if="settings?.emotionPrompt">
        <span>
          {{ selectorDisplayValue }}
        </span>
      </div>
      <template v-else>
        Use default emotion
      </template>
    </Selector>
    <Popup v-if="showPicker" :size="selectedTab === 'catalog' ? '4xl' : 'xl'" :show="showPicker" @close="showPicker = false">
      <div class="space-y-2">
        <div class="flex flex-col gap-0.5">
          <Heading>Select emotion</Heading>
          <Paragraph size="sm">
            Select the emotion you want to use for the photo.
          </Paragraph>
        </div>
        <div class="flex flex-col">
          <!--<Tab class="rounded-b-none">
            <TabItem v-for="tab in tabs" :key="tab" :active="selectedTab === tab" @click="selectedTab = tab">
              {{ tab }}
            </TabItem>
          </Tab> -->
          <div class="flex flex-row">
            <button v-for="tab in tabs" :key="tab" :class="selectedTab === tab ? 'active' : ''" class="example-button bg-white rounded-t text-md px-4 py-1.5 text-black/60" @click="selectedTab = tab">
              <span class="capitalize">{{ tab }}</span>
            </button>
          </div>
          <div class="bg-[#E9E9EC] p-4 rounded-b-lg rounded-tr-lg">
            <div v-if="selectedTab === 'catalog'" class="grid grid-cols-5 gap-4 max-h-[calc(50vh)] overflow-y-scroll">
              <div v-for="item in galleryItems" :key="item._id" class="w-full h-full cursor-pointer hover:scale-105 transition-all duration-200 bg-white rounded-md border border-gray-200 px-2.5 py-1.5" @click="selectValue(item.prompt)">
                <div class="flex flex-col items-center justify-center">
                  <ImageDns :src="item.images[gender]" class="w-full h-full object-cover block" />
                  <Paragraph size="xs" class="w-full text-center capitalize">
                    {{ item.type }}
                  </Paragraph>
                </div>
              </div>
            </div>
            <div v-if="selectedTab === 'manual'">
              <!-- <Input :value="manualOutfit" placeholder="Enter outfit prompt" label="Describe your outfit" @input="updateOutfitPrompt" /> -->
              <Input :value="manualValue" placeholder="Enter outfit prompt" label="Describe your outfit" @input="updateValuePrompt" />
            </div>
          </div>
        </div>
      </div>
    </Popup>
  </div>
</template>

<script>
import Selector from '../../common/Selector.vue'
import Heading from '@/components/landingpage/common/H5.vue'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
export default {
  components: {
    Selector,
    Heading,
    Paragraph
  },
  data () {
    return {
      showPicker: false,
      tabs: ['catalog', 'manual'],
      selectedTab: 'catalog',
      manualValue: ''
    }
  },
  computed: {
    settings () {
      return this.$store.state.studio.settings
    },
    gender () {
      return this.$store.state.studio.settings.gender || this.$store.state.studio.model.gender
    },
    selectorDisplayValue () {
      if (this.galleryItems.find(item => item.prompt === this.settings?.emotionPrompt)?.type) {
        return this.galleryItems.find(item => item.prompt === this.settings?.emotionPrompt)?.type
      }
      if (this.settings?.emotionPrompt) {
        return this.settings?.emotionPrompt
      }
      return 'Use default emotion'
    },
    galleryItems () {
      return this.$store.state.studio.emotions
    },
    emotionPrompt: {
      get () {
        return this.$store.state.studio.settings.emotion
      },
      set (value) {
        this.$store.commit('studio/UPDATE_SETTINGS', { key: 'emotionPrompt', value })
      }
    }
  },
  methods: {
    selectValue (value) {
      console.log(value)
      this.showPicker = false
      this.$store.commit('studio/UPDATE_SETTINGS', { key: 'emotionPrompt', value })
    },
    clearValue () {
      this.$store.commit('studio/UPDATE_SETTINGS', { key: 'emotionPrompt', value: null })
    },
    updateValuePrompt (value) {
      this.$store.commit('studio/UPDATE_SETTINGS', { key: 'emotionPrompt', value })
    }
  }
}
</script>

<style scoped>
/deep/.example-button.active {
  @apply text-primary-500 font-bold;
  background-color: #E9E9EC;
}
/deep/.example-button:not(.active) {
  @apply text-black/60;
  background-color: #fff;
}
/deep/.example-button:not(.active):hover {
  @apply scale-105 transition-all duration-300 bg-gray-100;
}

/* Fade transition for images */
/deep/.fade-enter-active,
/deep/.fade-leave-active {
  transition: opacity 0.3s ease;
}

/deep/.fade-enter,
  /* .fade-enter-from for Vue 3 */
  /deep/.fade-leave-to
/* .fade-leave-active in <2.1.8 */
{
  opacity: 0;
}
</style>
