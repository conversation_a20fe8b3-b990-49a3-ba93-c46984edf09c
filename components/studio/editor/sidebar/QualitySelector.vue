<template>
  <div>
    <Selector label="Quality" @click="showTemplateSelector = !showTemplateSelector">
      {{ displayValue }}
      <template #label>
        <Tooltip info="SD is low quality, but fast (~8s) while HD is high quality, but slow (~40s). You can always enhance the photo later." />
      </template>
    </Selector>
    <SelectorDropdown v-if="showTemplateSelector" :options="options" :value="value" @select="value = $event" @close="showTemplateSelector = false" />
  </div>
</template>

<script>
import Selector from '@/components/studio/common/Selector.vue'
import SelectorDropdown from '@/components/studio/common/SelectorDropdown.vue'

export default {
  components: {
    Selector,
    SelectorDropdown
  },
  data () {
    return {
      options: [
        { label: 'SD (1 credit)', value: 'sd' },
        { label: 'HD (2 credits)', value: 'hd' }
      ],
      showTemplateSelector: false
    }
  },
  computed: {
    value: {
      get () {
        return this.$store.state.studio.settings.quality
      },
      set (value) {
        this.$store.commit('studio/UPDATE_SETTINGS', { key: 'quality', value })
      }
    },
    displayValue () {
      return this.options.find(option => option.value === this.value)?.label
    }
  }
}
</script>

<style>

</style>
