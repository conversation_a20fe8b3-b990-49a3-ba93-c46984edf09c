<template>
  <div>
    <Selector label="Hairstyle" @click="showTemplateSelector = !showTemplateSelector">
      {{ displayValue }}
      <template #label>
        <Tooltip info="Depending on your training data, we can force the model to generate a specific hairstyle. Select no hairstyle to let the model choose a hairstyle that fits the photo." />
      </template>
    </Selector>
    <SelectorDropdown v-if="showTemplateSelector" :options="options" :value="value" @select="value = $event" @close="showTemplateSelector = false" />
  </div>
</template>

<script>
import Selector from '@/components/studio/common/Selector.vue'
import SelectorDropdown from '@/components/studio/common/SelectorDropdown.vue'

export default {
  components: {
    Selector,
    SelectorDropdown
  },
  data () {
    return {
      options: [
        { label: 'Force hairstyle', value: true },
        { label: 'Default hairstyle', value: false }
      ],
      showTemplateSelector: false
    }
  },
  computed: {
    value: {
      get () {
        return this.$store.state.studio.settings.hairstyle
      },
      set (value) {
        this.$store.commit('studio/UPDATE_SETTINGS', { key: 'hairstyle', value })
      }
    },
    displayValue () {
      return this.options.find(option => option.value === this.value)?.label
    }
  }
}
</script>

  <style>

  </style>
