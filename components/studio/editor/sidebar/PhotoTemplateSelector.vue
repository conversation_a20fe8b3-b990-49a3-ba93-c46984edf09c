<template>
  <div>
    <Selector label="Photo Template" @click="showTemplateSelector = true">
      <template v-if="settings?.photoTemplateId && selectedPhotoTemplate">
        <div class="h-8 w-8 rounded-md overflow-hidden">
          <img :src="selectedPhotoTemplate.url" alt="Photo Template" class="w-full h-full object-cover">
        </div>
        <span>{{ selectedPhotoTemplate.name }}</span>
      </template>
      <template v-else>
        No photo template selected
      </template>
    </Selector>
    <Popup v-show="showTemplateSelector" size="4xl" :show="showTemplateSelector" @close="showTemplateSelector = false">
      <div class="space-y-2">
        <div class="flex flex-col gap-0.5">
          <Heading>Select template</Heading>
          <Paragraph size="sm">
            Start a photo shoot based on a template photo. We will generate a photo of you in this style.
          </Paragraph>
        </div>
      </div>
      <div class="grid grid-cols-4 gap-4 p-4 bg-slate-100 rounded-lg mt-4 max-h-[calc(50vh)] overflow-y-scroll">
        <div v-for="photoTemplate in photoTemplates" :key="photoTemplate._id" class="w-full h-full cursor-pointer hover:scale-105 transition-all duration-200" @click="selectPhotoTemplate(photoTemplate)">
          <ImageDns :src="photoTemplate.url" alt="Photo Template" class="w-full h-full object-cover" />
        </div>
      </div>
    </Popup>
  </div>
</template>

<script>
import Selector from '../../common/Selector.vue'
import Heading from '@/components/landingpage/common/H5.vue'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
export default {
  components: {
    Selector,
    Heading,
    Paragraph
  },
  data () {
    return {
      showTemplateSelector: false
    }
  },
  computed: {
    settings () {
      return this.$store.state.studio.settings
    },
    gender () {
      return this.$store.state.studio.settings.gender
    },
    selectedPhotoTemplate () {
      return this.$store.state.studio.photoTemplates.find(photoTemplate => photoTemplate._id === this.settings?.photoTemplateId)
    },
    photoTemplates () {
      return this.$store.state.studio.photoTemplates.filter(template => template.gender === this.gender)
    }
  },
  watch: {
    photoTemplates (newValue, oldValue) {
      if (!this.selectedPhotoTemplate && newValue.length > 0) {
        this.$store.commit('studio/UPDATE_SETTINGS', { key: 'photoTemplateId', value: newValue[0]._id })
      }
    }
  },
  methods: {
    selectPhotoTemplate (photoTemplate) {
      this.$store.commit('studio/UPDATE_SETTINGS', { key: 'photoTemplateId', value: photoTemplate._id })
      this.showTemplateSelector = false
    }
  }
}
</script>

<style>

</style>
