<template>
  <div v-if="typeof settings?.[settingsName] !== 'undefined'">
    <Selector :label="label" :clearable="clearable" @click="showPicker = true" @clear="clearValue">
      <div v-if="settings?.[settingsName]" class="flex items-center justify-start gap-2">
        <span>
          {{ settings?.[settingsName] }}
        </span>
      </div>
      <template v-else>
        Use default
      </template>
    </Selector>
    <Popup v-if="showPicker" size="xl" :show="showPicker" @close="showPicker = false">
      <div class="space-y-2">
        <div class="flex flex-col gap-0.5">
          <Heading>Define {{ label }}</Heading>
          <Paragraph size="xs">
            Define the {{ label }} you want to use for the photo. Leave empty to use nothing.
          </Paragraph>
        </div>
        <Input v-model="value" />
        <div class="flex items-center justify-start gap-2">
          <ButtonPrimary class="w-auto" size="sm" @click="showPicker = false">
            Close
          </ButtonPrimary>
          <ButtonWhite v-if="defaultValue" size="sm" @click="resetDefault">
            Reset to default
          </ButtonWhite>
        </div>
      </div>
    </Popup>
  </div>
</template>

<script>
import Selector from '../../common/Selector.vue'
import Heading from '@/components/landingpage/common/H5.vue'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
export default {
  components: {
    Selector,
    Heading,
    Paragraph
  },
  props: {
    label: {
      type: String,
      default: null
    },
    settingsName: {
      type: String,
      required: true
    },
    defaultValue: {
      type: String,
      default: null
    }
  },
  data () {
    return {
      showPicker: false
    }
  },
  computed: {
    value: {
      get () {
        return this.$store.state.studio.settings[this.settingsName]
      },
      set (value) {
        this.$store.commit('studio/UPDATE_SETTINGS', { key: this.settingsName, value })
      }
    },
    clearable () {
      return this.value !== null
    },
    settings () {
      return this.$store.state.studio.settings
    },
    outfits () {
      return this.$store.state.studio.outfits.filter(outfit => outfit.gender.includes(this.settings.gender)).map((outfit) => {
        return {
          ...outfit,
          image: outfit.images[this.settings.gender]
        }
      })
    }
  },
  methods: {
    selectValue (value) {
      this.showPicker = false
      this.$store.commit('studio/UPDATE_SETTINGS', { key: 'outfitId', value })
    },
    clearValue () {
      console.log('clearValue')
      this.$store.commit('studio/UPDATE_SETTINGS', { key: this.settingsName, value: null })
    },
    resetDefault () {
      this.$store.commit('studio/UPDATE_SETTINGS', { key: this.settingsName, value: this.defaultValue })
    }
  }
}
</script>
