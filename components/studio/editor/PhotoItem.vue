<template>
  <div v-if="photo && photo._id" class="w-full h-full min-h-[100px] bg-white" :data-photo-id="photo._id">
    <template v-if="photo?.status === 'pending'">
      <div class="w-full h-full min-h-[200px] bg-black/70 animate-pulse flex items-center justify-center gap-2">
        <LoadingSpinnerWhite />
        <span class="text-white text-xs">
          Generating ({{ getTimeSinceCreationInSeconds(photo) }}s)
        </span>
      </div>
    </template>
    <template v-else-if="photo?.status === 'failed'">
      <div class="w-full h-full min-h-[100px] bg-black/70 flex flex-col items-center justify-center gap-1">
        <span class="text-white text-xs font-bold">
          Failed photo
        </span>
        <span class="text-white text-xs">
          We've refunded your credit.
        </span>
      </div>
    </template>
    <template v-else>
      <div class="cursor-pointer relative group">
        <div v-if="photo?.status === 'enhancing' || photo?.status === 'saving'" class="absolute w-full h-full z-30 bg-black/50 flex items-center justify-center gap-2">
          <LoadingSpinnerWhite />
          <span class="text-white text-xs">
            <template v-if="photo?.status === 'enhancing'">Enhancing ({{ getTimeSinceCreationInSeconds(photo) }}s)</template>
            <template v-else-if="photo?.status === 'saving'">Saving photo...</template>
            <span v-else>Unknown status</span>
          </span>
        </div>
        <div class="absolute top-0 right-1 z-10">
          <span class="text-[9px] bg-black/70 text-white font-medium rounded-full px-2 py-0.5" :class="qualityType === 'SD' ? 'bg-black/70' : 'bg-purple-500/70'">{{ qualityType }}</span>
        </div>
        <div class="absolute top-1 left-1 z-10">
          <IconSolidHeart v-if="photo.liked" class="w-4 h-4 text-red-500" />
        </div>
        <ImageDns v-if="photo?.images?.thumbnail" :src="photo.images.thumbnail" class="w-full h-full object-cover relative z-0" @load="refreshMasonry" />
        <div v-if="photo?.status === 'completed'" class="hidden group-hover:flex absolute h-full w-full bg-black/50 items-center justify-center z-20 top-0 left-0 flex-col gap-2 mx-auto">
          <div class="w-1/2 mx-auto gap-2 flex flex-col">
            <ButtonWhite size="sm" class="w-full" :loading="isDownloading" @click="downloadPhoto">
              Download
            </ButtonWhite>
            <ButtonWhite v-if="qualityType === 'SD'" size="sm" class="w-full" @click="enhancePhoto">
              <div class="flex items-center">
                <span>Enhance </span>
                <div class="ml-2 flex items-center content-center font-medium justify-center p-1 px-2 text-xs font-normal text-black bg-black/20 border-black/30 rounded-full shadow-inner">
                  <span>1</span>
                  <IconArrowPathRoundedSquare class="w-3 h-3 ml-1 text-black" />
                </div>
              </div>
            </ButtonWhite>
            <ButtonWhite size="sm" class="w-full" @click="$emit('click')">
              <span>Preview</span>
            </ButtonWhite>
            <div class="grid grid-cols-2 gap-2">
              <ButtonWhite v-if="!photo.liked" size="sm" class="w-full" :loading="isLoading.like" @click="likePhoto">
                <span>Like</span>
              </ButtonWhite>
              <ButtonWhite v-else size="sm" class="w-full" :loading="isLoading.dislike" @click="dislikePhoto">
                <span>Dislike</span>
              </ButtonWhite>
              <ButtonDelete size="sm" class="w-full" :loading="isLoading.delete" @click="deletePhoto">
                <span>Delete</span>
              </ButtonDelete>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    photo: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      isDownloading: false,
      isLoading: {
        like: false,
        dislike: false,
        delete: false
      }
    }
  },
  computed: {
    qualityType () {
      const { images } = this.photo
      if (images?.['4k']) { return '4K' }
      if (images?.hd) { return 'HD' }
      if (images?.full) { return 'SD' }
      return 'SD'
    }
  },
  methods: {
    getTimeSinceCreationInSeconds (photo) {
      if (!photo.updatedAt) { return 0 }
      const now = new Date()
      const creationDate = new Date(photo.updatedAt)
      const diffTime = Math.abs(now - creationDate)
      const diffSeconds = Math.ceil(diffTime / 1000)
      return diffSeconds
    },
    async enhancePhoto () {
      this.$loading.show({
        text: 'Enhancing photo...'
      })
      await this.$store.dispatch('studio/enhancePhoto', this.photo._id)
      this.$loading.hide()
    },
    downloadPhoto () {
      const { images } = this.photo
      if (!images) {
        this.$toast.error('No image found.')
        return
      }
      this.isDownloading = true
      const url = images?.['4k'] || images?.hd || images?.full || images?.thumbnail
      this.$toast.success('Your image will download shortly. Make sure to accept the download prompt.')
      fetch(url)
        .then(response => response.blob())
        .then((blob) => {
          const url = window.URL.createObjectURL(new Blob([blob]))
          const link = document.createElement('a')
          link.href = url
          const fileType = blob.type.split('/')[1]
          const fileName = `${this.photo._id}-HeadshotPro.${fileType}`
          link.setAttribute('download', fileName)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        })
        .finally(() => {
          this.isDownloading = false
        })
    },
    async likePhoto () {
      this.isLoading.like = true
      await this.$store.dispatch('studio/likePhoto', this.photo._id)
      this.isLoading.like = false
    },
    async dislikePhoto () {
      this.isLoading.dislike = true
      await this.$store.dispatch('studio/dislikePhoto', this.photo._id)
      this.isLoading.dislike = false
    },
    async deletePhoto () {
      this.isLoading.delete = true
      await this.$store.dispatch('studio/deletePhoto', this.photo._id)
      this.isLoading.delete = false
    }
  }

}
</script>

<style>

</style>
