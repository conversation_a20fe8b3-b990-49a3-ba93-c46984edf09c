<template>
  <div class="grid grid-cols-5 gap-4">
    <div
      v-for="photoTemplate in photoTemplates"
      :key="photoTemplate._id"
      relative@click="$emit(s)>
      <div class="
      group"
    >
      <ImageDns v-if="photoTemplate.url" :src="photoTemplate.url" />
    </div>
  </div>
  </div>
</template>

<script>
export default {
  props: {
    photoTemplates: {
      type: Array,
      required: true
    }
  }
}
</script>

<style>

</style>
