<template>
  <div>
    <button class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium leading-4text-gray-500 border cursor-pointer bg-yellow-100 border-yellow-200 hover:border-yellow-400 gap-1" @click="$store.commit('studio/SET_POPUP_STATUS', { key: 'creditModal', value: true })">
      <IconArrowPathRoundedSquare class="w-3 h-3  text-yellow-500" />
      <strong class="text-yellow-600 ">
        {{ credits }}
      </strong>
    </button>
  </div>
</template>

<script>
export default {
  computed: {
    credits () {
      return this.$store.state.user.studio.credits
    }
  }
}
</script>

<style>

</style>
