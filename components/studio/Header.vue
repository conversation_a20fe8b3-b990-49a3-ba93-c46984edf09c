<template>
  <div class="w-full bg-white px-4 py-3 border-b border-gray-200">
    <div class="grid grid-cols-3 items-center w-full">
      <div class="flex items-center justify-start">
        <ButtonWhite size="sm" @click="goBack">
          Back to results
        </ButtonWhite>
      </div>
      <div class="flex items-center gap-2 justify-center">
        <img width="181" height="28" class="h-6 w-auto" src="@/assets/img/logo.svg" alt="HeadshotPro logo">
        <div class="bg-teal-400 py-1.5 px-1.5 rounded-md flex items-center justify-center">
          <span class="text-[11px] text-white font-bold tracking-tighter uppercase leading-none">Studio</span>
        </div>
      </div>
      <div />
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    goBack () {
      this.$router.push(`/app/results/${this.$route.params.id}`)
    }
  }
}
</script>

<style>

</style>
