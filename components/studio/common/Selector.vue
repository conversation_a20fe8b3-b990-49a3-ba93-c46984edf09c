<template>
  <div class="flex flex-col gap-0.5">
    <div class="flex justify-between items-center">
      <label v-if="label" class="block text-sm font-semibold leading-5 text-slate-600" :class="{ 'text-primary-500': !disabled, 'text-gray-400': disabled,}">
        {{ label }}
      </label>
      <slot name="label" />
      <button v-if="clearable" class="text-xs font-normal underline leading-5 text-gray-500 cursor-pointer" @click="$emit('clear')">
        Clear
      </button>
    </div>
    <div class="selector overflow-hidden block w-full pl-2 pr-4 pt-2 pb-2 bg-white border rounded-lg border-gray-300 placeholder-gray-400 focus:outline-none focus:border-teal-500 focus:ring-teal-500 sm:text-sm focus:ring-1 hover:border-gray-400 hover:bg-muted-50 cursor-pointer transition-all duration-200" @click="$emit('click')">
      <div class="flex items-center gap-2 justify-between">
        <div class="flex items-center gap-2 inner-content overflow-hidden">
          <slot />
        </div>
        <IconChevronDown class="w-4 h-4" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    label: {
      type: String,
      default: ''
    },
    clearable: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  }

}
</script>

<style scoped>
  /deep/.inner-content span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: all 0.3s ease;
  }

  /deep/.selector:hover span {
    overflow-x: hidden;
    text-overflow: initial;
    animation: scrollText 8s linear infinite;
  }

  @keyframes scrollText {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-100%);
    }
  }
</style>
