<template>
  <div>
    <div class="selector overflow-hidden bg-white border-gray-300 border rounded-lg shadow-lg relative z-50">
      <ul class="flex flex-col">
        <li v-for="option in options" :key="option.value" class="p-2 hover:bg-gray-100 cursor-pointer" @click="updateValue(option.value)">
          <Paragraph size="xs">
            {{ option.label }}
          </Paragraph>
        </li>
      </ul>
    </div>
    <div class="w-full h-screen fixed top-0 left-0 bg-transparent" @click="$emit('close')" />
  </div>
</template>

<script>
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
export default {
  components: {
    Paragraph
  },
  props: {
    options: {
      type: Array,
      required: true
    }
  },
  methods: {
    updateValue (value) {
      this.$emit('select', value)
      this.$emit('close')
    }
  }
}
</script>

<style>

</style>
