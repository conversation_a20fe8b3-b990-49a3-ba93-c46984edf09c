<template>
  <div v-if="showInHolidayMode" class="bg-teal-50 rounded-md border border-black/10 w-full">
    <div class="p-4 px-6 flex flex-col justify-start items-start">
      <h3 class="font-bold">
        👉 Get free headshots for your feedback
      </h3>
      <p class="mb-3 mt-1.5 font-normal text-sm text-gray-700">
        <strong>Limited time:</strong> Get 10 extra headshots in any style for leaving your feedback. Only takes 2 minutes and helps us improve our service.
      </p>
      <div class="w-full flex flex-row items-start justify-start space-x-2">
        <nuxt-link :to="`/app/feedback`">
          <ButtonPrimary class="w-[fit-content]" size="sm">
            Get your headshots now
          </ButtonPrimary>
        </nuxt-link>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    id: {
      type: String,
      required: true
    }
  },
  computed: {
    showInHolidayMode () {
      if (!this.isHolidayMode) {
        return true
      }

      const random = Math.random()
      return random <= 0.1
    }
  }
}
</script>
