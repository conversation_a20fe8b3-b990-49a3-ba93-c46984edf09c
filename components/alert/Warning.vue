<template>
  <div class="text-left border border-yellow-300 rounded-lg bg-yellow-50">
    <div class="p-3">
      <div class="flex items-start justify-start">
        <svg
          class="flex-shrink-0 w-5 h-5 text-yellow-500"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          stroke-width="2"
        >
          <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <div class="ml-3">
          <p class="text-sm font-bold text-yellow-900">
            {{ title }}
          </p>
          <p class="text-xs mt-0.5 font-medium text-yellow-900">
            {{ description }}
          </p>
          <slot />
        </div>

        <!-- <div class="pl-3 ml-auto">
          <button type="button" class="inline-flex bg-yellow-50 rounded-lg -m1.5 p-1.5 text-yellow-500 hover:bg-yellow-100 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-yellow-50 focus:ring-yellow-500">
            <svg
              class="w-4 h-4"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              stroke-width="2"
            >
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: { type: String },
    description: { type: String }
  }

}
</script>

<style>

</style>
