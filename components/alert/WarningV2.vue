<template>
  <div class="px-4 py-3 text-left rounded-lg bg-[#FFFAED] border border-solid border-black/10">
    <div class="flex items-start justify-start">
      <svg
        class="flex-shrink-0 mr-[10px] translate-y-[7px] hidden md:block"
        width="14"
        height="12"
        viewBox="0 0 14 12"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path fill-rule="evenodd" clip-rule="evenodd" d="M5.39796 0.75C5.97496 -0.25 7.41796 -0.25 7.99596 0.75L13.192 9.75C13.3236 9.97803 13.3929 10.2367 13.3929 10.5C13.3929 10.7633 13.3236 11.022 13.192 11.25C13.0603 11.478 12.871 11.6674 12.6429 11.799C12.4149 11.9307 12.1563 12 11.893 12H1.50096C1.23758 12.0002 0.978788 11.931 0.750625 11.7994C0.522462 11.6678 0.332969 11.4785 0.201202 11.2504C0.0694359 11.0224 4.22052e-05 10.7636 1.92458e-08 10.5002C-4.21667e-05 10.2369 0.0692686 9.9781 0.200962 9.75L5.39796 0.75ZM6.69696 2.5C6.89587 2.5 7.08664 2.57902 7.22729 2.71967C7.36794 2.86032 7.44696 3.05109 7.44696 3.25V6.25C7.44696 6.44891 7.36794 6.63968 7.22729 6.78033C7.08664 6.92098 6.89587 7 6.69696 7C6.49805 7 6.30728 6.92098 6.16663 6.78033C6.02598 6.63968 5.94696 6.44891 5.94696 6.25V3.25C5.94696 3.05109 6.02598 2.86032 6.16663 2.71967C6.30728 2.57902 6.49805 2.5 6.69696 2.5ZM6.69696 10.5C6.96218 10.5 7.21653 10.3946 7.40407 10.2071C7.5916 10.0196 7.69696 9.76522 7.69696 9.5C7.69696 9.23478 7.5916 8.98043 7.40407 8.79289C7.21653 8.60536 6.96218 8.5 6.69696 8.5C6.43175 8.5 6.17739 8.60536 5.98986 8.79289C5.80232 8.98043 5.69696 9.23478 5.69696 9.5C5.69696 9.76522 5.80232 10.0196 5.98986 10.2071C6.17739 10.3946 6.43175 10.5 6.69696 10.5Z" fill="#FBBC04" />
      </svg>

      <div>
        <p class="text-base font-medium text-[#624C0E]">
          {{ text }}
        </p>
        <slot />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    text: { type: String }
  }

}
</script>

<style>

</style>
