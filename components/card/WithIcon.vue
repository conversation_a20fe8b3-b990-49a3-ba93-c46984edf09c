<template>
  <Card class="relative overflow-hidden" inner-class="flex justify-between items-center xs:block">
    <component :is="icon" v-if="icon" class="w-32 xs:w-40 h-auto absolute top-0 right-0 left-0 mx-auto text-paragraph opacity-25 translate-x-1/4 xs:-translate-y-1/4 xs:left-auto" />
    <p class="text-3xl xs:text-6xl font-bold text-brand-500">
      {{ value }}
    </p>
    <p class="text-sm text-paragraph order-first xs:mt-4 xs:order-none">
      {{ title }}
    </p>
  </Card>
</template>

<script>
export default {
  props: {
    icon: {
      type: String,
      required: false,
      default: null
    },
    title: {
      type: String,
      required: true
    },
    value: {
      type: String,
      required: true
    }
  }
}
</script>
