<template>
  <nav class="flex space-x-4">
    <button
      v-for="item in items"
      :key="item.value"
      :class="[
        'px-3 py-1.5 rounded-full text-sm font-medium transition-colors duration-200',
        activeItem === item.value
          ? 'bg-sky-200 text-sky-900'
          : 'text-gray-700 hover:text-gray-900'
      ]"
      @click="handleClick(item.value)"
    >
      {{ item.label }}
    </button>
  </nav>
</template>

<script>
export default {
  props: {
    items: {
      type: Array,
      required: true,
      validator: value => value.every(item => 'label' in item && 'value' in item)
    },
    initialActive: {
      type: String,
      default: null
    }
  },
  data () {
    return {
      activeItem: this.initialActive
    }
  },
  methods: {
    handleClick (value) {
      this.activeItem = value
      this.$emit('click', value)
    }
  }
}
</script>
