<!-- This example requires Tailwind CSS v2.0+ -->
<template>
  <button v-if="size === 'xs'" aria-label="Secondary button" type="button" class="text-brand-700 bg-brand-200 hover:bg-brand-300 focus:ring-brand-200 flex content-center items-center justify-center rounded border border-transparent px-2.5 py-1.5 text-center text-xs font-medium shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2" @click="$emit('click')">
    <slot />
  </button>
  <button v-else-if="size === 'sm'" aria-label="Secondary button" type="button" class="text-brand-700 bg-brand-200 hover:bg-brand-300 focus:ring-brand-200 flex content-center items-center justify-center rounded-md border border-transparent px-3 py-2 text-center text-sm font-medium leading-4 shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2" @click="$emit('click')">
    <slot />
  </button>
  <button v-else-if="size === 'base'" aria-label="Secondary button" type="button" class="text-brand-700 bg-brand-200 hover:bg-brand-300 focus:ring-brand-200 flex content-center items-center justify-center rounded-md border border-transparent px-4 py-2 text-center text-sm font-medium shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2" @click="$emit('click')">
    <slot />
  </button>
  <button v-else-if="size === 'lg'" aria-label="Secondary button" type="button" class="text-brand-700 bg-brand-200 hover:bg-brand-300 focus:ring-brand-200 flex content-center items-center justify-center rounded-md border border-transparent px-4 py-2 text-center text-base font-medium shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2" @click="$emit('click')">
    <slot />
  </button>
  <button v-else-if="size === 'xl'" aria-label="Secondary button" type="button" class="text-brand-700 bg-brand-200 hover:bg-brand-300 focus:ring-brand-200 flex content-center items-center justify-center rounded-md border border-transparent px-6 py-3 text-center text-base font-medium shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2" @click="$emit('click')">
    <slot />
  </button>
</template>

<script>
export default {
  props: {
    size: {
      type: String,
      default: 'base'
    }
  }
}
</script>

<style></style>
