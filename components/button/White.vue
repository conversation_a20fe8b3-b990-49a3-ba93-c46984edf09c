<!-- This example requires Tailwind CSS v2.0+ -->
<template>
  <button v-if="size === 'xs'" aria-label="White button" type="button" class="flex content-center items-center justify-center rounded-md border border-[#e4e4e7] bg-white px-2.5 py-1.5 text-center text-xs font-medium text-slate-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2" @click="$emit('click')">
    <LoadingSpinner v-if="loading" class="w-4 h-4" />
    <slot v-else />
  </button>
  <button v-else-if="size === 'sm'" aria-label="White button" type="button" class="flex content-center items-center justify-center rounded-md border border-[#e4e4e7] bg-white px-4 py-2 text-center text-sm font-medium leading-4 text-slate-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2" @click="$emit('click')">
    <LoadingSpinner v-if="loading" class="w-4 h-4" />
    <slot v-else />
  </button>
  <button v-else-if="size === 'base'" aria-label="White button" type="button" class="flex content-center items-center justify-center rounded-md border border-[#e4e4e7] bg-white px-6 py-3 text-center text-base font-medium text-slate-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2" @click="$emit('click')">
    <LoadingSpinner v-if="loading" class="w-4 h-4" />
    <slot v-else />
  </button>
  <button v-else-if="size === 'lg'" aria-label="White button" type="button" class="flex content-center items-center justify-center rounded-md border border-[#e4e4e7] bg-white px-8 py-4 text-center text-lg font-medium text-slate-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2" @click="$emit('click')">
    <LoadingSpinner v-if="loading" class="w-4 h-4" />
    <slot v-else />
  </button>
  <button v-else-if="size === 'xl'" aria-label="White button" type="button" class="flex content-center items-center justify-center rounded-md border border-[#e4e4e7] bg-white px-10 py-5 text-center text-xl font-medium text-slate-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2" @click="$emit('click')">
    <LoadingSpinner v-if="loading" class="w-4 h-4" />
    <slot v-else />
  </button>
</template>

<script>
export default {
  props: {
    size: {
      type: String,
      default: 'base'
    },
    loading: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style></style>
