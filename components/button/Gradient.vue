<!-- This example requires Tailwind CSS v2.0+ -->
<template>
  <button
    v-if="size === 'xs'"
    style="flex-shrink: 0"
    aria-label="Primary Button"
    type="button"
    class="gradient-bg focus:ring-brand-200 flex content-center items-center justify-center rounded border border-transparent px-2.5 py-1.5 text-center text-xs font-bold text-white shadow-sm hover:border-black hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2"
    @click="$emit('click')"
  >
    <slot />
  </button>
  <button
    v-else-if="size === 'sm'"
    style="flex-shrink: 0"
    aria-label="Primary Button"
    type="button"
    class="gradient-bg focus:ring-brand-200 flex content-center items-center justify-center rounded-md border border-transparent px-4 py-2 text-center text-sm font-bold leading-4 text-white shadow-sm hover:border-black hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2"
    @click="$emit('click')"
  >
    <slot />
  </button>
  <button
    v-else-if="size === 'base'"
    style="flex-shrink: 0"
    aria-label="Primary Button"
    type="button"
    class="gradient-bg focus:ring-brand-200 flex content-center items-center justify-center rounded-md border border-transparent px-6 py-3 text-center text-base font-bold text-white shadow-sm hover:border-black hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2"
    @click="$emit('click')"
  >
    <slot />
  </button>
  <button
    v-else-if="size === 'lg'"
    style="flex-shrink: 0"
    aria-label="Primary Button"
    type="button"
    class="gradient-bg focus:ring-brand-200 flex content-center items-center justify-center rounded-md border border-transparent px-8 py-4 text-center text-lg font-bold text-white shadow-sm hover:border-black hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2"
    @click="$emit('click')"
  >
    <slot />
  </button>
  <button
    v-else-if="size === 'xl'"
    style="flex-shrink: 0"
    aria-label="Primary Button"
    type="button"
    class="gradient-bg focus:ring-brand-200 flex content-center items-center justify-center rounded-md border border-transparent px-10 py-5 text-center text-xl font-bold text-white shadow-sm hover:border-black hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2"
    @click="$emit('click')"
  >
    <slot />
  </button>
</template>

<script>
export default {
  props: {
    size: {
      type: String,
      default: 'base'
    }
  }
}
</script>

<style scoped>
.gradient-bg {
  content: "";
  background: linear-gradient(60deg, #f79533, #f37055, #ef4e7b, #a166ab, #5073b8, #1098ad, #07b39b, #6fba82);
  -webkit-animation: animatedgradient 3s ease infinite alternate;
  animation: animatedgradient 3s ease infinite alternate;
  background-size: 300% 300%;
}

@keyframes animatedgradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
</style>
