<template>
  <button v-if="!showConfirm" aria-label="White button" type="button" class="flex content-center items-center justify-center rounded-md border border-white/10 bg-red-500 px-4 py-2 text-center text-sm font-medium leading-4 text-white shadow-sm hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2" @click="toggleConfirmation">
    <slot />
  </button>
  <button v-else aria-label="White button" type="button" class="flex content-center items-center justify-center rounded-md border border-white/10 bg-red-500 px-4 py-2 text-center text-sm font-medium leading-4 text-white shadow-sm hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2" @click="proceed">
    <span>Are you sure?</span>
  </button>
</template>

<script>
export default {
  data () {
    return {
      showConfirm: false
    }
  },
  methods: {
    toggleConfirmation () {
      this.showConfirm = !this.showConfirm
      if (this.showConfirm) {
        setTimeout(() => {
          this.cancel()
        }, 2000)
      }
    },
    proceed () {
      this.$emit('click')
      this.toggleConfirmation()
    },
    cancel () {
      this.toggleConfirmation()
      this.showModal = false
    }
  }
}
</script>

<style></style>
