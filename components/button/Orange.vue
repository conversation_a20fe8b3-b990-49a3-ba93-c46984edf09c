<template>
  <button
    v-if="size === 'xs'"
    aria-label="Primary Button"
    type="button"
    :disabled="isDisabled"
    :class="{ 'cursor-not-allowed': isDisabled }"
    class="hover:bg-brand-800 focus:ring-brand-200 flex flex-shrink-0 content-center items-center justify-center rounded border border-transparent bg-[#ff6600] px-2.5 py-1.5 text-center text-xs font-medium text-white shadow-sm transition duration-100 focus:outline-none focus:ring-2 focus:ring-offset-2"
    @click="$emit('click')"
  >
    <slot />
  </button>
  <button
    v-else-if="size === 'sm'"
    aria-label="Primary Button"
    type="button"
    :disabled="isDisabled"
    :class="{ 'cursor-not-allowed': isDisabled }"
    class="hover:bg-brand-800 focus:ring-brand-200 flex flex-shrink-0 content-center items-center justify-center rounded-md border border-transparent bg-[#ff6600] px-4 py-2 text-center text-sm font-medium leading-4 text-white shadow-sm transition duration-100 focus:outline-none focus:ring-2 focus:ring-offset-2"
    @click="$emit('click')"
  >
    <slot />
  </button>
  <button
    v-else-if="size === 'base'"
    aria-label="Primary Button"
    type="button"
    :disabled="isDisabled"
    :class="{ 'cursor-not-allowed': isDisabled }"
    class="hover:bg-brand-800 focus:ring-brand-200 flex flex-shrink-0 content-center items-center justify-center rounded-md border border-transparent bg-[#ff6600] px-6 py-3 text-center text-base font-medium text-white shadow-sm transition duration-100 focus:outline-none focus:ring-2 focus:ring-offset-2"
    @click="$emit('click')"
  >
    <slot />
  </button>
  <button
    v-else-if="size === 'lg'"
    aria-label="Primary Button"
    type="button"
    :disabled="isDisabled"
    :class="{ 'cursor-not-allowed': isDisabled }"
    class="hover:bg-brand-800 focus:ring-brand-200 flex flex-shrink-0 content-center items-center justify-center rounded-md border border-transparent bg-[#ff6600] px-8 py-4 text-center text-lg font-medium text-white shadow-sm transition duration-100 focus:outline-none focus:ring-2 focus:ring-offset-2"
    @click="$emit('click')"
  >
    <slot />
  </button>
  <button
    v-else-if="size === 'xl'"
    aria-label="Primary Button"
    type="button"
    :disabled="isDisabled"
    :class="{ 'cursor-not-allowed': isDisabled }"
    class="hover:bg-brand-800 focus:ring-brand-200 flex flex-shrink-0 content-center items-center justify-center rounded-md border border-transparent bg-[#ff6600] px-10 py-5 text-center text-xl font-medium text-white shadow-sm transition duration-100 focus:outline-none focus:ring-2 focus:ring-offset-2"
    @click="$emit('click')"
  >
    <slot />
  </button>
</template>

<script>
export default {
  props: {
    size: {
      type: String,
      default: 'base'
    },
    isDisabled: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style></style>
