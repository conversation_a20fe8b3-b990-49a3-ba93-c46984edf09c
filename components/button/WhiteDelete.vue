<template>
  <button v-if="size === 'xs'" type="button" class="flex content-center items-center justify-center rounded-md border border-[#e4e4e7] bg-white px-2.5 py-1.5 text-center text-xs font-medium text-[#EA4335] hover:bg-[#EA4335] hover:text-white" @click="showConfirm ? proceed() : toggleConfirmation()">
    <span v-if="!showConfirm">
      <slot />
    </span>
    <span v-else>
      Are you sure?
    </span>
  </button>
  <button v-else-if="size === 'sm'" type="button" class="flex content-center items-center justify-center rounded-md border border-[#e4e4e7] bg-white px-4 py-2 text-center text-sm font-medium leading-4 text-[#EA4335] hover:bg-[#EA4335] hover:text-white" @click="showConfirm ? proceed() : toggleConfirmation()">
    <span v-if="!showConfirm">
      <slot />
    </span>
    <span v-else>
      Are you sure?
    </span>
  </button>
  <button v-else-if="size === 'base'" type="button" class="flex content-center items-center justify-center rounded-md border border-[#e4e4e7] bg-white px-6 py-3 text-center text-base font-medium text-[#EA4335] hover:bg-[#EA4335] hover:text-white" @click="showConfirm ? proceed() : toggleConfirmation()">
    <span v-if="!showConfirm">
      <slot />
    </span>
    <span v-else>
      Are you sure?
    </span>
  </button>
  <button v-else-if="size === 'lg'" type="button" class="flex content-center items-center justify-center rounded-md border border-[#e4e4e7] bg-white px-8 py-4 text-center text-lg font-medium text-[#EA4335] hover:bg-[#EA4335] hover:text-white" @click="showConfirm ? proceed() : toggleConfirmation()">
    <span v-if="!showConfirm">
      <slot />
    </span>
    <span v-else>
      Are you sure?
    </span>
  </button>
  <button v-else-if="size === 'xl'" type="button" class="flex content-center items-center justify-center rounded-md border border-[#e4e4e7] bg-white px-10 py-5 text-center text-xl font-medium text-[#EA4335] hover:bg-[#EA4335] hover:text-white" @click="showConfirm ? proceed() : toggleConfirmation()">
    <span v-if="!showConfirm">
      <slot />
    </span>
    <span v-else>
      Are you sure?
    </span>
  </button>
</template>

<script>
export default {
  props: {
    size: {
      type: String,
      default: 'base'
    }
  },
  data () {
    return {
      showConfirm: false
    }
  },
  methods: {
    toggleConfirmation () {
      this.showConfirm = !this.showConfirm
      if (this.showConfirm) {
        setTimeout(() => {
          this.cancel()
        }, 2000)
      }
    },
    proceed () {
      this.$emit('click')
      this.toggleConfirmation()
    },
    cancel () {
      this.toggleConfirmation()
    }
  }
}
</script>

  <style></style>
