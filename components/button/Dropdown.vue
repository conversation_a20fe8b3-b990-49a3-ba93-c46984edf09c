<template>
  <div :class="{ 'w-full': fullWidth }">
    <div v-if="showMenu" class="w-full h-screen bg-transparent absolute top-0 left-0 z-10" @click="showMenu = false" />
    <div
      class="relative text-left"
      :class="{
        'inline-block': theme === 'v1',
        'inline-flex': theme === 'v2',
        'w-full': fullWidth
      }"
    >
      <div class="flex" :class="{ 'w-full': fullWidth }">
        <div v-if="theme === 'v2'" class="flex">
          <button :aria-label="title" type="button" class="text-sm font-medium text-primary-500 rounded-lg shadow-sm bg-white border border-gray-200 transition-all duration-150 hover:bg-gray-50 px-3 py-1.5 inline-flex items-center justify-center flex-col gap-[2px]" :class="buttonClass" @click="showMenu = !showMenu">
            <div class="h-1 w-1 rounded-full bg-gray-500" />
            <div class="h-1 w-1 rounded-full bg-gray-500" />
            <div class="h-1 w-1 rounded-full bg-gray-500" />
          </button>
        </div>
        <ButtonWhite
          v-else
          class="relative z-0 gap-1"
          :class="{ 'w-full': fullWidth, [buttonClass]: !!buttonClass }"
          theme="v2"
          :size="size"
          @click="showMenu = !showMenu"
        >
          <span v-if="iconPosition === 'after'" class="line-clamp-1">{{ title }}</span>
          <svg
            v-if="!icon"
            class="h-4 w-4 flex-shrink-0"
            :class="{ '-mr-1': iconPosition === 'after', '-ml-1': iconPosition === 'before' }"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="#807D96"
            aria-hidden="true"
          >
            <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
          </svg>
          <component :is="icon" v-else class="w-5 h-5 text-[#807D96] flex-shrink-0" :class="{ '-mr-1': iconPosition === 'after', '-ml-1': iconPosition === 'before' }" />
          <span v-if="iconPosition === 'before'" class="line-clamp-1">{{ title }}</span>
          <div v-if="showCaret" class="absolute right-4 top-0 bottom-0 flex items-center justify-center flex-shrink-0">
            <svg width="9" height="6" viewBox="0 0 9 6" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M0.220011 1.07437C0.360637 0.93392 0.551261 0.855031 0.750011 0.855031C0.948762 0.855031 1.13939 0.93392 1.28001 1.07437L4.00001 3.79437L6.72001 1.07437C6.78867 1.00068 6.87147 0.941582 6.96347 0.90059C7.05547 0.859598 7.15479 0.837556 7.25549 0.83578C7.35619 0.834003 7.45622 0.852527 7.54961 0.890248C7.643 0.927969 7.72783 0.984114 7.79905 1.05533C7.87027 1.12655 7.92641 1.21139 7.96413 1.30477C8.00185 1.39816 8.02038 1.49819 8.0186 1.59889C8.01683 1.6996 7.99478 1.79891 7.95379 1.89091C7.9128 1.98291 7.8537 2.06571 7.78001 2.13437L4.53001 5.38437C4.38939 5.52482 4.19876 5.60371 4.00001 5.60371C3.80126 5.60371 3.61064 5.52482 3.47001 5.38437L0.220011 2.13437C0.0795608 1.99375 0.000671387 1.80312 0.000671387 1.60437C0.000671387 1.40562 0.0795608 1.215 0.220011 1.07437Z" fill="#807D96" />
            </svg>
          </div>
        </ButtonWhite>
      </div>

      <div
        v-if="showMenu"
        class="absolute z-[900] w-56 rounded-md overflow-hidden bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
        :class="{
          // horizontal alignment
          'right-0': xalign === 'right', // right side aligned with the button
          'left-0': xalign === 'left', // left side aligned with the button
          'left-1/2 -translate-x-1/2': xalign === 'center', // horizontally centered with the button
          'left-0 xs:left-full': xalign === 'right-full', // left side aligned with the right part of the button
          'right-0 xs:right-full': xalign === 'left-full', // right side aligned with the left part of the button
          // vertical alignment
          'top-full': valign === 'bottom', // bottom side aligned with the top of the button
          'bottom-full': valign === 'top', // top side aligned with the bottom of the button
          'top-1/2 -translate-y-1/2': valign === 'center', // vertically centered with the button
          // margin
          'mb-2': valign === 'top',
          'mt-2': valign === 'bottom',
          'mr-2': valign === 'center' && (xalign === 'left' || xalign === 'left-full'),
          'ml-2': valign === 'center' && (xalign === 'right' || xalign === 'right-full'),
          // origin
          'origin-center': valign === 'center', // vertically and center center aligned with the button
          'origin-top-center': valign === 'top' && xalign === 'center',
          'origin-bottom-center': valign === 'bottom' && xalign === 'center',
          'origin-top-left': valign === 'top' && (xalign === 'left' || xalign === 'left-full'),
          'origin-top-right': valign === 'top' && (xalign === 'right' || xalign === 'right-full'),
          'origin-bottom-left': valign === 'bottom' && (xalign === 'left' || xalign === 'left-full'),
          'origin-bottom-right': valign === 'bottom' && (xalign === 'right' || xalign === 'right-full'),
          [dropdownClass]: !!dropdownClass
        }"
        role="menu"
        aria-orientation="vertical"
        aria-labelledby="menu-button"
        tabindex="-1"
      >
        <div role="none">
          <ul class="flex flex-col divide-y divide-gray-100">
            <li
              v-for="item in items"
              :key="item.value || item"
              class="cursor-pointer flex items-center justify-start px-4 py-2 text-primary-500 text-sm hover:bg-gray-100"
              :class="item.classes"
              :style="item.color ? `color:${item.color}` : ''"
              @click="handleClick(item.value || item)"
            >
              <component :is="item.icon" v-if="item.icon" class="w-5 h-5 mr-2 flex-shrink-0 text-paragraph" />
              <span class="capitalize line-clamp-1">{{ item.title || item }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    xalign: {
      type: String,
      default: 'right' // left, right, center, left-full, right-full
    },
    valign: {
      type: String,
      default: 'bottom' // top, bottom, center
    },
    items: {
      type: Array,
      required: true
    },
    size: {
      type: String,
      default: 'base'
    },
    title: {
      type: String,
      required: true
    },
    theme: {
      type: String,
      default: 'v1'
    },
    fullWidth: {
      type: Boolean,
      default: false
    },
    icon: {
      type: String,
      default: null
    },
    iconPosition: {
      type: String,
      default: 'after' // before or after
    },
    showCaret: {
      type: Boolean,
      default: false
    },
    buttonClass: {
      type: String,
      default: ''
    },
    dropdownClass: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      showMenu: false
    }
  },
  mounted () {
    document.addEventListener('click', this.handleClickOutside)
  },
  beforeDestroy () {
    document.removeEventListener('click', this.handleClickOutside)
  },
  methods: {
    handleClick (value) {
      this.$emit('select', value)
      this.showMenu = false
    },
    toggleMenu () {
      this.showMenu = !this.showMenu
    },
    handleClickOutside (e) {
      const dropdown = this.$el
      if (!dropdown.contains(e.target)) {
        this.showMenu = false
      }
    }
  }

}
</script>

<style>

</style>
