<!-- This example requires Tailwind CSS v2.0+ -->
<template>
  <button v-if="size === 'xs'" aria-label="Disables button" type="button" class="flex content-center items-center justify-center text-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-gray-700 bg-gray-200 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-200" @click="$emit('click')">
    <slot />
  </button>
  <button v-else-if="size === 'sm'" aria-label="Disables button" type="button" class="flex content-center items-center justify-center text-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-gray-700 bg-gray-200 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-200" @click="$emit('click')">
    <slot />
  </button>
  <button v-else-if="size === 'base'" aria-label="Disables button" type="button" class="flex content-center items-center justify-center text-center px-6 py-3 border border-transparent  text-base font-medium rounded-md shadow-sm text-gray-700 bg-gray-200 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-200" @click="$emit('click')">
    <slot />
  </button>
  <button v-else-if="size === 'lg'" aria-label="Disables button" type="button" class="flex content-center items-center justify-center text-center px-4 py-2 border border-transparent text-base font-medium rounded-md shadow-sm text-gray-700 bg-gray-200 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-200" @click="$emit('click')">
    <slot />
  </button>
  <button v-else-if="size === 'xl'" aria-label="Disables button" type="button" class="flex content-center items-center justify-center text-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-gray-700 bg-gray-200 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-200" @click="$emit('click')">
    <slot />
  </button>
</template>

<script>
export default {
  props: {
    size: {
      type: String,
      default: 'base'
    }
  }

}
</script>

<style>

</style>
