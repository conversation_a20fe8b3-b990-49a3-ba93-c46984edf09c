<template>
  <!-- <button
    v-if="size === 'xs'"
    style="flex-shrink:0;"
    aria-label="Secondary button"
    type="button"
    class="flex content-center transition items-center justify-center text-center px-2.5 py-1.5 text-xs font-medium rounded shadow-sm text-white font-bold bg-brand-500 hover:bg-brand-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-200"
    @click="$emit('click')"
  >
    <slot />
  </button>
  <button
    v-else-if="size === 'sm'"
    style="flex-shrink:0;"
    aria-label="Secondary button"
    type="button"
    class="flex content-center transition items-center justify-center text-center px-3.5 py-2 text-sm leading-4 font-medium rounded-md shadow-sm text-white font-bold bg-brand-500 hover:bg-brand-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-200"
    @click="$emit('click')"
  >
    <slot />
  </button>
  <button
    v-else-if="size === 'base'"
    style="flex-shrink:0;"
    aria-label="Secondary button"
    type="button"
    class="flex content-center transition items-center justify-center text-center px-4 py-2.5 text-base font-medium rounded-md shadow-sm text-white font-bold bg-brand-500 hover:bg-brand-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-200"
    @click="$emit('click')"
  >
    <slot />
  </button>
  <button
    v-else-if="size === 'lg'"
    style="flex-shrink:0;"
    aria-label="Secondary button"
    type="button"
    class="flex content-center transition items-center justify-center text-center px-5 py-3 text-lg font-medium rounded-md shadow-sm text-white font-bold bg-brand-500 hover:bg-brand-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-200"
    @click="$emit('click')"
  >
    <slot />
  </button>
  <button
    v-else-if="size === 'xl'"
    style="flex-shrink:0;"
    aria-label="Secondary button"
    type="button"
    class="flex content-center transition items-center justify-center text-center px-6 py-5  text-xl font-medium rounded-md shadow-sm text-white font-bold bg-brand-500 hover:bg-brand-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-200"
    @click="$emit('click')"
  >
    <slot />
  </button> -->
  <button
    v-if="size === 'xs'"
    aria-label="Primary Button"
    type="button"
    :disabled="isDisabled"
    :class="{ 'cursor-not-allowed opacity-40': isDisabled }"
    class="hover:bg-brand-800 focus:ring-brand-200 flex flex-shrink-0 content-center items-center justify-center rounded-md border border-transparent bg-brand-500 px-2.5 py-1.5 text-center text-xs font-medium text-white shadow-sm transition duration-100 focus:outline-none focus:ring-2 focus:ring-offset-2"
    @click="$emit('click')"
  >
    <slot />
  </button>
  <button
    v-else-if="size === 'sm'"
    aria-label="Primary Button"
    type="button"
    :disabled="isDisabled"
    :class="{ 'cursor-not-allowed opacity-40': isDisabled }"
    class="hover:bg-brand-800 focus:ring-brand-200 flex flex-shrink-0 content-center items-center justify-center rounded-md border border-transparent bg-brand-500 px-4 py-2 text-center text-sm font-medium leading-4 text-white shadow-sm transition duration-100 focus:outline-none focus:ring-2 focus:ring-offset-2"
    @click="$emit('click')"
  >
    <slot />
  </button>
  <button
    v-else-if="size === 'base'"
    aria-label="Primary Button"
    type="button"
    :disabled="isDisabled"
    :class="{ 'cursor-not-allowed opacity-40': isDisabled }"
    class="hover:bg-brand-800 focus:ring-brand-200 flex flex-shrink-0 content-center items-center justify-center rounded-md border border-transparent bg-brand-500 px-6 py-3 text-center text-base font-medium text-white shadow-sm transition duration-100 focus:outline-none focus:ring-2 focus:ring-offset-2"
    @click="$emit('click')"
  >
    <slot />
  </button>
  <button
    v-else-if="size === 'lg'"
    aria-label="Primary Button"
    type="button"
    :disabled="isDisabled"
    :class="{ 'cursor-not-allowed opacity-40': isDisabled }"
    class="hover:bg-brand-800 focus:ring-brand-200 flex flex-shrink-0 content-center items-center justify-center rounded-md border border-transparent bg-brand-500 px-8 py-4 text-center text-lg font-medium text-white shadow-sm transition duration-100 focus:outline-none focus:ring-2 focus:ring-offset-2"
    @click="$emit('click')"
  >
    <slot />
  </button>
  <button
    v-else-if="size === 'xl'"
    aria-label="Primary Button"
    type="button"
    :disabled="isDisabled"
    :class="{ 'cursor-not-allowed opacity-40': isDisabled }"
    class="hover:bg-brand-800 focus:ring-brand-200 flex flex-shrink-0 content-center items-center justify-center rounded-md border border-transparent bg-brand-500 px-10 py-5 text-center text-xl font-medium text-white shadow-sm transition duration-100 focus:outline-none focus:ring-2 focus:ring-offset-2"
    @click="$emit('click')"
  >
    <slot />
  </button>
</template>

<script>
export default {
  props: {
    size: {
      type: String,
      default: 'base'
    },
    isDisabled: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style></style>
