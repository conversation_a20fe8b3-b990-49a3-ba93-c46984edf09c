<!-- This example requires Tailwind CSS v2.0+ -->
<template>
  <button v-if="size === 'xs'" aria-label="White button" type="button" class="py-1-300 flex content-center items-center justify-center rounded border-white/40 bg-white/20 hover:bg-white/30 px-2.5 text-center text-xs font-medium text-white/80 shadow-sm backdrop-blur-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2" @click="$emit('click')">
    <slot />
  </button>
  <button v-else-if="size === 'sm'" aria-label="White button" type="button" class="flex content-center items-center justify-center rounded-md border-white/40 bg-white/20 hover:bg-white/30 px-4 py-2 text-center text-sm font-medium leading-4 text-white/80 shadow-sm backdrop-blur-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2" @click="$emit('click')">
    <slot />
  </button>
  <button v-else-if="size === 'base'" aria-label="White button" type="button" class="flex content-center items-center justify-center rounded-md border-white/40 bg-white/20 hover:bg-white/30 px-6 py-3 text-center text-base font-medium text-white/80 shadow-sm backdrop-blur-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2" @click="$emit('click')">
    <slot />
  </button>
  <button v-else-if="size === 'lg'" aria-label="White button" type="button" class="flex content-center items-center justify-center rounded-md border-white/40 bg-white/20 hover:bg-white/30 px-8 py-4 text-center text-lg font-medium text-white/80 shadow-sm backdrop-blur-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2" @click="$emit('click')">
    <slot />
  </button>
  <button v-else-if="size === 'xl'" aria-label="White button" type="button" class="flex content-center items-center justify-center rounded-md border-white/40 bg-white/20 hover:bg-white/30 px-10 py-5 text-center text-xl font-medium text-white/80 shadow-sm backdrop-blur-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2" @click="$emit('click')">
    <slot />
  </button>
</template>

<script>
export default {
  props: {
    size: {
      type: String,
      default: 'base'
    }
  }
}
</script>

<style></style>
