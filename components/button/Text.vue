<!-- This example requires Tailwind CSS v2.0+ -->
<template>
  <button class="text-primary-500 underline leading-[20px] text-[16px] font-medium tracking-[-0.4px] opacity-70 hover:opacity-100 flex items-center gap-2 transition-all duration-300">
    <slot />
    <IconArrowRight class="w-4 h-4 text-primary-500" />
  </button>
</template>

<script>
export default {
  props: {
    size: {
      type: String,
      default: 'base'
    }
  }
}
</script>

<style></style>
