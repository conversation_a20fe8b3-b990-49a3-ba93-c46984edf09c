<template>
  <div class="fixed inset-0 z-50 overflow-hidden text-black">
    <div class="absolute inset-0 overflow-hidden">
      <!--
      Background overlay, show/hide based on slide-over state.

      Entering: "ease-in-out duration-500"
        From: "opacity-0"
        To: "opacity-100"
      Leaving: "ease-in-out duration-500"
        From: "opacity-100"
        To: "opacity-0"
    -->
      <div class="absolute inset-0 transition-opacity bg-gray-500 " :class="{'bg-opacity-25':!background, 'bg-opacity-75':background}" @click="$emit('closeModal')" />

      <section :class="`absolute inset-y-0 ${position}-0 max-w-full flex`">
        <!--
        Slide-over panel, show/hide based on slide-over state.

        Entering: "transform transition ease-in-out duration-500 sm:duration-700"
          From: "translate-x-full"
          To: "translate-x-0"
        Leaving: "transform transition ease-in-out duration-500 sm:duration-700"
          From: "translate-x-0"
          To: "translate-x-full"
      -->
        <div :class="`w-screen`" :style="`max-width:${maxWidth}`">
          <div class="flex flex-col h-full py-6 space-y-4 overflow-y-auto bg-white shadow-2xl overflow-overlay">
            <header class="px-4 sm:px-6 border-b border-gray-200 pb-4">
              <div class="flex items-start justify-between space-x-3">
                <h2 class="text-base font-medium leading-7 text-gray-800" style="flex-shrink:0;">
                  {{ title }}
                </h2>
                <div class="flex items-center h-7 space-x-2">
                  <slot name="header" />
                  <button aria-label="Close panel" class="text-gray-300 transition duration-150 ease-in-out hover:text-gray-500" @click="$emit('closeModal')">
                    <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
              <p v-if="description" class="mt-2 text-sm text-gray-700">
                {{ description }}
              </p>
            </header>
            <div class="relative flex-1 px-4 sm:px-6 ">
              <slot />
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: 'title'
    },
    description: {
      type: String,
      required: false
    },
    size: {
      type: String,
      required: false,
      default: 'md'
    },
    position: {
      type: String,
      required: false,
      default: 'right'
    },
    background: {
      type: Boolean,
      required: false,
      default: true
    }
  },
  computed: {
    windowWidth () {
      return window.innerWidth
    },
    maxWidth () {
      if (this.windowWidth < 768) { return '100%' }

      const { size } = this
      if (size === 'xs') { return '20rem' } else if (size === 'sm') { return '24rem' } else if (size === 'md') { return '28rem' } else if (size === 'lg') { return '32rem' } else if (size === 'xl') { return '36rem' } else if (size === '2xl') { return '42rem' } else if (size === '3xl') { return '48rem' } else if (size === '4xl') { return '56rem' } else if (size === '5xl') { return '64rem' } else if (size === '6xl') { return '72rem' } else if (size === '7xl') { return '80rem' }
      return '100%'
    }
  }

}
</script>

<style>

</style>
