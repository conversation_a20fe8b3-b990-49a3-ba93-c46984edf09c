<template>
  <div class="fixed inset-0 z-40 overflow-y-auto">
    <div class="flex items-center justify-start min-h-screen px-4 pt-4 pb-20 text-center md:items-end md:justify-center sm:block sm:p-0">
      <div class="fixed inset-0 transition-opacity" @click="$emit('closeModal')">
        <div class="absolute inset-0 bg-gray-500 opacity-75">
          <slot name="overlay" />
        </div>
      </div>

      <!-- This element is to trick the browser into centering the modal contents. -->
      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" />
      &#8203;

      <div :class="`modal overflow-y-scroll relative inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle w-full sm:p-6`" :style="`max-width:${maxWidth}`" role="dialog" aria-modal="true" aria-labelledby="modal-headline">
        <div class="absolute top-0 right-0 flex items-center mt-2 mr-2 h-7">
          <button aria-label="Close panel" class="text-gray-400 transition duration-150 ease-in-out hover:text-gray-500" @click="$emit('closeModal')">
            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div>
          <slot />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    size: {
      type: String,
      required: false,
      default: 'sm'
    }
  },
  computed: {
    windowWidth () {
      return window.innerWidth
    },
    maxWidth () {
      if (this.windowWidth < 768) {
        return '100%'
      }

      const { size } = this
      if (size === 'xs') {
        return '20rem'
      } else if (size === 'sm') {
        return '24rem'
      } else if (size === 'md') {
        return '28rem'
      } else if (size === 'lg') {
        return '32rem'
      } else if (size === 'xl') {
        return '36rem'
      } else if (size === '2xl') {
        return '42rem'
      } else if (size === '3xl') {
        return '48rem'
      } else if (size === '4xl') {
        return '56rem'
      } else if (size === '5xl') {
        return '64rem'
      } else if (size === '6xl') {
        return '72rem'
      } else if (size === '7xl') {
        return '80rem'
      }
      return '100%'
    }
  }
}
</script>

<style scoped>
.modal {
  max-height: calc(100vh - 72px);
  max-width: calc(100vw - 72px);
}
</style>
