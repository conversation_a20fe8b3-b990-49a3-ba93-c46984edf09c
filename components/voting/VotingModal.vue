<template>
  <Modal max-width="sm:max-w-3xl" @close="$emit('close')">
    <div class="w-full bg-white p-4 md:p-8 lg:w-[800px]">
      <h2 class="text-2xl font-bold">
        Not sure which headshot is your favorite?
      </h2>
      <p class="text-base text-paragraph mt-2">
        Not sure which ones you like the most? Share the link below with your friends to let them vote on your headshots!
      </p>

      <h3 class="text-lg font-bold mt-8">
        How does it work?
      </h3>
      <ul class="text-base text-paragraph space-y-2 mt-2">
        <li class="flex justify-start items-center gap-2">
          <IconSolidCheck class="w-6 h-6 text-green-500" />
          <p>
            Share the link with your friends
          </p>
        </li>
        <li class="flex justify-start items-center gap-2">
          <IconSolidCheck class="w-6 h-6 text-green-500" />
          <p>
            Your friends will be able to choose 3 headshots for you
          </p>
        </li>
        <li class="flex justify-start items-center gap-2">
          <IconSolidCheck class="w-6 h-6 text-green-500" />
          <p>
            You can see the results in the voting results page
          </p>
        </li>
      </ul>
      <LoadingWrapper :is-loading="shareToken === null || shareToken === undefined || shareToken === ''" title="Generating link..." class="mt-8 space-y-4">
        <div class="mt-8 bg-gray-100 p-4 rounded-md text-base text-paragraph cursor-pointer text-center" @click="copyLink">
          {{ link }}
        </div>

        <div class="flex flex-col sm:flex-row sm:items-center justify-center gap-4 mt-8">
          <ButtonWhite class="gap-2 w-full sm:w-auto" @click="copyLink">
            <IconCopy class="w-4 h-4" />
            <span>Copy link</span>
          </ButtonWhite>
          <ButtonGradient class="gap-2 w-full sm:w-auto" @click="seeResults">
            <IconShare class="w-4 h-4" />
            <span>See results</span>
          </ButtonGradient>
        </div>
      </LoadingWrapper>
    </div>
  </Modal>
</template>

<script>
import copy from 'copy-to-clipboard'
export default {
  props: {
    shareToken: {
      type: String,
      required: false
    },
    modelId: {
      type: String,
      required: true
    }
  },
  computed: {
    link () {
      return `${process.env.BASE_URL}/vote/${this.shareToken}`
    }
  },
  mounted () {
    this.copyLink()
  },
  methods: {
    copyLink () {
      if (this.link && this.shareToken) {
        copy(this.link)
        this.$toast.success('Link copied!')
      }
    },
    seeResults () {
      this.$store.commit('results/SET_SHOW_VOTING_MODAL', false)
      this.$router.push(`/app/results/${this.modelId}/voting-results`)
    }
  }
}
</script>
