<template>
  <div class="p-2 space-y-4">
    <h3 class="font-bold text-xl">
      Clothing
    </h3>
    <div class="flex flex-col space-y-4">
      <!-- Male Clothing -->
      <div>
        <h4>Male</h4>
        <div class="flex flex-col space-y-1">
          <div v-for="clothing in maleClothing" :key="clothing._id">
            <div class="flex items-center space-x-2">
              <img v-if="clothing?.images?.male" :src="clothing.images.male" alt="Male Clothing" class="w-10 h-10">
              <p>{{ clothing.type }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Female Clothing -->
      <div>
        <h4>Female</h4>
        <div class="flex flex-col space-y-1">
          <div v-for="clothing in femaleClothing" :key="clothing._id">
            <div class="flex items-center space-x-2">
              <img v-if="clothing?.images?.female" :src="clothing.images.female" alt="Female Clothing" class="w-10 h-10">
              <p>{{ clothing.type }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <h3 class="font-bold text-xl">
      Backdrops
    </h3>
    <div class="flex flex-col space-y-4">
      <div v-for="backdrop in selectedBackdrops" :key="backdrop._id">
        <div class="flex items-center space-x-2">
          <img v-if="backdrop.image.maleImage" :src="backdrop.image.maleImage" alt="Backdrop" class="w-10 h-10">
          <img v-if="backdrop.image.femaleImage" :src="backdrop.image.femaleImage" alt="Backdrop" class="w-10 h-10">
          <p>{{ backdrop.title }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    organization: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    maleClothing () {
      const ids = this.organization.allowedOptions.clothing.male
      return this.$store.state.clothing.filter(c => ids.includes(c._id))
    },
    femaleClothing () {
      const ids = this.organization.allowedOptions.clothing.female
      return this.$store.state.clothing.filter(c => ids.includes(c._id))
    },
    selectedBackdrops () {
      const ids = this.organization.allowedOptions.styles
      return this.$store.state.styles.filter(s => ids.includes(s._id))
    }
  }
}
</script>

<style>

</style>
