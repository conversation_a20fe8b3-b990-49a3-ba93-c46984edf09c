<template>
  <div class="h-[calc(100vh-50px)] overflow-hidden">
    <div class="h-[calc(100vh-100px)] overflow-y-scroll p-4 ">
      <img v-if="post?.thumbnail?.large" :src="`https://storage.googleapis.com/headshotpro-public-content/${post.thumbnail.large}`" class="w-full object-cover mb-8">
      <h1 v-if="post.title" class="text-2xl font-bold">
        {{ post.title }}
      </h1>
      <div class="prose h-full" v-html="compiledMarkdown" />
    </div>
  </div>
</template>

<script>
import { marked } from 'marked'
export default {
  computed: {
    post () {
      return this.$store.state.admin.post
    },
    compiledMarkdown () {
      return marked(this.post.content, { sanitize: true })
    }
  }

}
</script>

<style>

</style>
