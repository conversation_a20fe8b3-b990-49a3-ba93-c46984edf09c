<template>
  <div class="h-[200px] overflow-hidden overflow-y-scroll">
    <Table>
      <TableRow v-for="item in markdownTips" :key="item.title">
        <TableItem>{{ item.title }}</TableItem>
        <TableItem>
          <button class="hover:underline" @click="$emit('click', item.example)">
            {{ item.example }}
          </button>
        </TableItem>
      </TableRow>
    </Table>
  </div>
</template>

<script>
export default {
  data () {
    return {
      markdownTips: [
        { title: 'Headings', example: '#Title, ## Title' },
        { title: 'Bold', example: '**bold**' },
        { title: 'Italic', example: '*italic*' },
        { title: 'Link', example: '[link](https://google.com)' },
        { title: 'Image', example: '![alt text](https://google.com)' },
        { title: 'List', example: '- list item' },
        { title: 'Code', example: '`code`' },
        { title: 'Quote', example: '> quote' },
        { title: 'Horizontal Rule', example: '---' },
        { title: 'Table', example: '| Header | Header |' },
        { title: 'Table', example: '| --- | --- |' },
        { title: 'Table', example: '| Cell | Cell |' }
      ]
    }
  }

}
</script>

<style>

</style>
