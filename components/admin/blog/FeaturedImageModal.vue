<template>
  <Popup size="xl" title="Upload image" @closeModal="$emit('closeModal')">
    <LoadingWrapper :is-loading="isLoading" title="Uploading...">
      <div class="p-2 rounded bg-gray-50 flex items-center justify-center">
        <input
          id="file"
          ref="file"
          name="file"
          type="file"
          class="w-full "
          accept="image/png, image/jpeg, image/heic, image/heif, image/jpg"
          @change="addFiles"
        >
        <ButtonPrimary v-if="files.length > 0" size="sm" @click="upload">
          Upload
        </ButtonPrimary>
      </div>
    </LoadingWrapper>
  </Popup>
</template>

<script>
export default {
  data () {
    return {
      files: [],
      isLoading: false
    }
  },
  methods: {
    addFiles (e) {
      const files = e.target.files
      if (files.length > 0) {
        this.files = files
      }
    },
    upload () {
      const formData = new FormData()
      formData.append('files', this.files[0])
      this.isLoading = true
      this.$axios.$post('/admin/blog/featured-image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
        .then((res) => {
          const { data } = res
          this.$emit('upload', data)
          this.$emit('closeModal')
        })
        .catch((err) => {
          console.log(err)
        })
    }
  }

}
</script>

<style>

</style>
