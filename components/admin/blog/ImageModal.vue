<template>
  <Popup size="xl" title="Upload image" @closeModal="$emit('closeModal')">
    <LoadingWrapper :is-loading="isLoading" title="Uploading...">
      <div class="p-2 rounded bg-gray-50 flex items-center justify-center">
        <input
          id="file"
          ref="file"
          name="file"
          type="file"
          class="w-full "
          accept="image/png, image/jpeg, image/heic, image/heif, image/jpg"
          @change="addFiles"
        >
        <ButtonPrimary v-if="files.length > 0" size="sm" @click="upload">
          Upload
        </ButtonPrimary>
      </div>
    </LoadingWrapper>
    <p v-if="imageMarkdownString" class="mt-2 p-1 bg-gray-100 rounded">
      {{ imageMarkdownString }}
    </p>
  </Popup>
</template>

<script>
export default {
  data () {
    return {
      files: [],
      isLoading: false,
      imageMarkdownString: ''
    }
  },
  methods: {
    addFiles (e) {
      const files = e.target.files
      if (files.length > 0) {
        this.files = files
      }
    },
    upload () {
      const formData = new FormData()
      formData.append('files', this.files[0])
      this.isLoading = true
      this.$axios.$post('/admin/blog/image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
        .then((res) => {
          const { data } = res
          const path = 'https://storage.googleapis.com/headshotpro-public-content/' + data
          this.imageMarkdownString = `![alt](${path})`
          this.$emit('upload', path)
          // this.$emit('closeModal')
        })
        .catch((err) => {
          console.log(err)
        })
    }
  }

}
</script>

<style>

</style>
