<template>
  <div class="h-[50px] bg-white flex items-center justify-between space-x-2 px-4 border-b border-gray-50">
    <div class="flex items-center justify-start space-x-2">
      <ButtonWhite size="sm" @click="showImageModal = true">
        Upload image
      </ButtonWhite>
    </div>
    <div class="flex items-center justify-end space-x-2">
      <ButtonWhite size="sm" @click="showFeaturedImageModal = true">
        Add featured image
      </ButtonWhite>
      <InputSelect :value="post.status" :options="['draft', 'published']" @input="updateStatus($event)" />
      <template v-if="!$route.params.id">
        <ButtonPrimary v-if="post.status === 'draft'" size="sm" @click="savePost(false)">
          Save
        </ButtonPrimary>
        <ButtonPrimary v-if="post.status === 'published'" size="sm" @click="savePost(true)">
          Schedule
        </ButtonPrimary>
      </template>

      <ButtonPrimary v-else size="sm" @click="editPost">
        Edit
      </ButtonPrimary>
    </div>
    <AdminBlogImageModal v-if="showImageModal" @closeModal="showImageModal = false" @upload="copyString(`![alt](${$event})`)" /> <!--addToEditor(`\n ![alt](${$event})`)" />-->
    <AdminBlogFeaturedImageModal v-if="showFeaturedImageModal" @closeModal="showFeaturedImageModal = false" @upload="updateThumbnail($event)" />
  </div>
</template>

<script>
export default {
  data () {
    return {
      showImageModal: false,
      showFeaturedImageModal: false
    }
  },
  computed: {
    post () {
      return this.$store.state.admin.post
    }
  },
  methods: {
    updateStatus (event) {
      this.$store.commit('admin/SET_POST_STATUS', event)
    },
    updateThumbnail (event) {
      this.$store.commit('admin/SET_POST_THUMBNAIL', event)
    },
    addToEditor (value) {
      const content = this.post.content + value
      this.$store.commit('admin/SET_POST_CONTENT', content)
    },
    savePost (isScheduled) {
      const { title, excerpt, content, status } = this.post

      if (!title || !excerpt || !content || !status) {
        return alert('Please fill all the fields')
      }

      this.$axios.post('/admin/blog', { ...this.post, isScheduled })
        .then((data) => {
          if (data.status && data.status === 200) {
            this.$router.push('/admin/blog')
          }
        })
    },
    editPost () {
      const { title, excerpt, content, status } = this.post

      if (!title || !excerpt || !content || !status) {
        return alert('Please fill all the fields')
      }

      this.$axios.put(`/admin/blog/single/${this.$route.params.id}`, this.post)
        .then((data) => {
          if (data.status && data.status === 200) {
            this.$router.push('/admin/blog')
          }
        })
    }
  }

}
</script>

<style>

</style>
