<template>
  <div class="flex flex-col space-y-2 bg-gray-50 p-2 rounded-md border border-black/50">
    <div v-if="approvedPhotos.length > 0" class="grid grid-cols-4 gap-2">
      <div class="flex items-center justify-start space-x-0.5 col-span-4">
        <IconCheck class="h-4 w-4 text-green-500" />
        <span class="text-sm font-medium">Passed check</span>
      </div>
      <template v-for="src in approvedPhotos">
        <img :key="src" :src="src" class="rounded-md" @click="removeFile(index)">
      </template>
    </div>
    <div v-if="declinedPhotos.length > 0" class="grid grid-cols-6 gap-2">
      <div class="flex items-center justify-start space-x-0.5 col-span-6">
        <IconCross class="h-4 w-4 text-red-500" />
        <span class="text-sm font-medium">Declined photos</span>
      </div>
      <template v-for="src in declinedPhotos">
        <img :key="src" :src="src" class="rounded-md">
      </template>
    </div>
    <LoadingWrapper :is-loading="addingFiles">
      <input
        id="file"
        ref="file"
        name="file"
        type="file"
        class="w-full"
        accept="image/png, image/jpeg, image/jpg"
        multiple
        @change="addFiles"
      >
    </LoadingWrapper>
  </div>
</template>

<script>
import SparkMD5 from 'spark-md5'
export default {
  data () {
    return {
      addingFiles: false,
      uploadedFileHashes: [],
      source: null,
      declinedPhotos: [],
      approvedPhotoPaths: [],
      approvedPhotos: []
    }
  },
  computed: {
    trainingImagePaths: {
      get () {
        return this.$store.state.admin.trainingImagePaths
      },
      set (value) {
        this.$store.commit('admin/SET_TRAINING_IMAGE_PATHS', value)
      }
    }
  },
  methods: {
    async addFiles (event) {
      try {
        this.addingFiles = true
        const files = event.target.files

        const source = this.$axios.CancelToken.source()
        this.source = source

        for (let i = 0; i < files.length; i++) {
          try {
            this.addingFiles = true
            let fileData = files[i]
            const fileName = fileData.name || 'trainingImage' + new Date().getTime().toString()

            // const fileHash = await this.calculateMD5(fileData)
            // if (this.uploadedFileHashes.has(fileHash) && this.env !== 'development') {
            //   this.$toast.warning('You already uploaded this photo.')
            //   continue
            // }
            fileData = await this.resizeImage(fileData)
            const blob = new File([fileData], fileName, { type: fileData.type })

            const formData = new FormData()
            formData.append('files', blob)

            const { success, data, errorMessage } = await this.$axios.$post(
              '/admin/wardrobe/upload',
              formData,
              { cancelToken: source.token },
              {
                headers: {
                  'Content-Type': 'multipart/form-data'
                }
              }
            )

            // photos.push(response)
            if (!success) {
              if (data?.url === 'failed') {
                this.declinedPhotos = [...this.declinedPhotos, this.createObjectUrl(fileData)]
                this.$toast.error(errorMessage)
              }
            } else {
              console.log(data.path)
              this.trainingImagePaths = [...this.trainingImagePaths, data.path]
              this.approvedPhotos = [...this.approvedPhotos, data.url]
            //   this.uploadedFileHashes.add(fileHash)
            }
          } catch (err) {
            if (err.message === 'Canceled') {
              return
            }
            this.addingFiles = false
            this.$toast.warning(err.message || err || 'Something went wrong. Please try another photo.')
          }
        }
        this.addingFiles = false
      } catch (err) {
        console.log(err)
        if (err.message === 'Canceled') {
          return
        }
        this.$toast.warning(err.message || 'Something went wrong. Please try another photo.')
        this.addingFiles = false
      }
    },
    calculateMD5 (file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        const spark = new SparkMD5.ArrayBuffer()

        reader.onload = (event) => {
          spark.append(event.target.result)
          const hash = spark.end()
          resolve(hash)
        }

        reader.onerror = (error) => {
          reject(error)
        }

        reader.readAsArrayBuffer(file)
      })
    },
    resizeImage (blob) {
      try {
        // Resize image from blob and return resized blob with canvas
        return new Promise((resolve, reject) => {
          const reader = new FileReader()
          reader.readAsDataURL(blob)
          reader.onload = (event) => {
            const img = new Image()
            img.src = event.target.result
            img.onload = () => {
              // if (img.width < 512 || img.height < 512) {
              //   // this.photos.declined.push(this.createObjectUrl(blob))
              //   this.declinedPhotos = [...this.declinedPhotos, this.createObjectUrl(blob)]
              //   reject(Error('Image is too small'))
              // }
              const elem = document.createElement('canvas')
              const scaleFactor = img.width / 1024
              elem.width = 1024
              elem.height = img.height / scaleFactor
              const ctx = elem.getContext('2d')
              ctx.drawImage(img, 0, 0, elem.width, elem.height)
              ctx.canvas.toBlob(
                (blob) => {
                  resolve(blob)
                },
                'image/png',
                1
              )
            }
            img.onerror = (error) => {
              reject(error)
            }
          }
        })
      } catch (err) {
        console.log(err)
      }
    },
    removeFile (index) {
      // this.handleFileDeletion({
      //   trainingId: this.trainingId,
      //   image: this.approvedPhotos[index]
      // })
      // this.files.splice(index, 1)
      // this.photos.approved.splice(index, 1)
      this.approvedPhotos = this.approvedPhotos.filter((item, i) => i !== index)
      this.trainingImagePaths = this.trainingImagePaths.filter((item, i) => i !== index)
      this.inputFiles = [...this.trainingImagePaths]
    },
    createObjectUrl (file) {
      return URL.createObjectURL(file)
    }
  }

}
</script>

<style>

</style>
