<template>
  <PopupSlideIn title="Edit style" @closeModal="$emit('closeModal')">
    <div class="flex flex-col space-y-2">
      <LoadingWrapper :is-loading="isSaving" title="Updating style...">
        <Input v-model="form.title" label="Title" />
        <label for="email" class="block text-sm font-medium leading-5 text-gray-700">Thumnail</label>
        <input
          id="fileextra"
          ref="fileextra"
          name="fileextra"
          type="file"
          accept="image/png, image/jpeg, image/jpg"
          enctype="multipart/form-data"
          @change="onFileSelected"
        >
        <Input v-model="form.location" label="Location" placeholder="standing in front of a build" />
        <InputSelect v-model="form.status" :options="['active', 'offline']" label="Status" />
        <ButtonPrimary @click="editStyle">
          Edit style
        </ButtonPrimary>
      </LoadingWrapper>
    </div>
  </PopupSlideIn>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      isSaving: false,
      form: {
        title: null,
        description: null,
        prompt: null,
        images: {
          maleImage: null,
          femaleImage: null
        },
        status: null,
        location: null,
        background: null
      }
    }
  },
  created () {
    this.form.title = this.item.title
    this.form.description = this.item.description
    this.form.prompt = this.item.prompt
    this.form.status = this.item.status
    this.form.location = this.item?.location || null
    this.form.background = this.item?.background || null
  },
  methods: {
    async generateDescriptionGPT4 () {
      try {
        const { title } = this.form
        if (!title) {
          return this.$toast.error('Please enter a title first')
        }

        const prompt = [{ role: 'user', content: `Please act like an SEO copywriter. \nI will give you a scenario for a location where a professional headshot can be taken. \nYou will return two things. Three short titles of max 4 words for that scenario, and three descriptions of max 20 words of that scenario. \n The location is ${this.form.title}` }]
        const { success, data, errorMessage } = await this.$axios.$post('/admin/gpt3/chat', {
          prompt
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.form.description = data
      } catch (err) {
        console.log(err)
        this.$toast.error(err)
      }
    },
    async generateDescription () {
      try {
        const { title } = this.form
        if (!title) {
          return this.$toast.error('Please enter a title first')
        }

        const prompt = `Task: Write 20 words description of a professional headshot style based on a provided title.
  Title: Business & Corporate
  Description: Business headshots typically used on LinkedIn profiles, press releases, websites, advertisements, business cards, and presentations. Ideal for executives, entrepreneurs, startups, or anyone else in a business environment.
  """
  Task: Write 20 words description of a professional headshot style based on a provided title.
  Title: Professional Acting
  Description: Aspiring and professional acting professionals need great headshots to get called back for new roles. This type is perfect for theatrical, commercial, voice, or dramatic headshots.
  """
  Task: Write 20 words description of a professional headshot style based on a provided title.
  Title: Artistic Creative
  Description: Stylized headshots for websites, social media profiles, press releases, and book covers. In these photos, the worst thing you can be is boring. Ideal for artists, designers, musicians, creative writers, and thought leaders.
  """
  Task: Write 20 words description of a professional headshot style based on a provided title.
  Title: ${title}
  Description:`
        const { success, data, errorMessage } = await this.$axios.$post('/admin/gpt3', {
          prompt
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.form.description = data
      } catch (err) {
        console.log(err)
        this.$toast.error(err)
      }
    },
    async editStyle () {
      try {
        this.isSaving = true

        const { title, prompt, description, status, images, location, background, thumbnail } = this.form
        if (!this.item?._id) {
          throw new Error('No style id found')
        }
        if (!prompt.includes('{trigger}') || !prompt.includes('{clothing}')) {
          return this.$toast.warning('Please make sure prompt contains {trigger} and {clothing}')
        }

        const formData = new FormData()
        formData.append('_id', this.item?._id)
        formData.append('title', title || null)
        formData.append('location', location || null)
        formData.append('description', description || null)
        formData.append('prompt', prompt || null)
        formData.append('status', status || null)
        if (images?.maleImage) {
          formData.append('maleImage', images.maleImage[0])
        }
        if (images?.femaleImage) {
          formData.append('femaleImage', images.femaleImage[0])
        }
        if (background) {
          formData.append('background', background[0])
        }
        if (thumbnail) {
          formData.append('thumbnail', thumbnail[0])
        }

        const { success, errorMessage } = await this.$axios.$put('/admin/style', formData)
        if (!success) {
          throw new Error(errorMessage)
        }

        this.$toast.success('Style updated successfully')

        this.form = {
          title: null,
          location: null,
          description: null,
          prompt: null,
          images: {
            maleImage: null,
            femaleImage: null
          },
          background: null
        }
        this.$emit('closeModal')
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isSaving = false
      }
    },

    onFileSelectedMale (event) {
      this.form.images.maleImage = event.target.files
    },
    onFileSelected (event) {
      this.form.thumbnail = event.target.files
    },
    onFileSelectedFemale (event) {
      this.form.images.femaleImage = event.target.files
    },
    onFileSelectedBackground (event) {
      this.form.background = event.target.files
    }
  }
}
</script>

<style></style>
