<template>
  <div class="flex w-full items-center justify-between rounded-md border border-gray-200 bg-white px-4 py-2">
    <div class="flex items-center space-x-4">
      <h1 class="text-2xl font-medium text-gray-900">
        {{ title || "" }}
      </h1>
      <span class="flex items-center text-sm text-gray-500">
        <div class="mr-1 h-2 w-2 rounded-full" :class="live ? 'bg-green-500' : 'bg-orange-500'" />
        {{ live ? "Live" : "Draft" }}
      </span>
    </div>
    <div class="flex items-center space-x-2">
      <ButtonDelete v-if="live" size="sm">
        Unpublish
      </ButtonDelete>
      <ButtonPrimary size="sm" @click="$emit('publish')">
        Publish
      </ButtonPrimary>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    live: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style></style>
