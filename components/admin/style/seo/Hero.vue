<template>
  <div class="w-full space-y-4 rounded-md border border-gray-200 bg-white p-4">
    <h1 class="text-xl font-medium text-gray-900">
      Hero
    </h1>
    <div>
      <LoadingWrapper :is-loading="isLoading.length > 0 && isLoading.includes('headline')" title="Generating headline...">
        <div class="flex w-full items-center justify-between">
          <h2>Headline</h2>
          <ButtonWhite size="xs" @click="generateHeadline">
            {{ headline ? "Regenerate" : "Generate" }}
          </ButtonWhite>
        </div>
        <InputTextArea
          :value="headline"
          :rows="3"
          @input="
            $emit('change', {
              field: 'headline',
              value: $event,
            })
          "
        />
      </LoadingWrapper>
    </div>
    <div v-if="headline && headline.length > 0">
      <LoadingWrapper :is-loading="isLoading.length > 0 && isLoading.includes('subHeadline')" title="Generating sub headline...">
        <div class="flex w-full items-center justify-between">
          <h2>Sub headline</h2>
          <ButtonWhite size="xs" @click="generateSubHeadline">
            {{ subHeadline ? "Regenerate" : "Generate" }}
          </ButtonWhite>
        </div>
        <InputTextArea
          :value="subHeadline"
          :rows="5"
          @input="
            $emit('change', {
              field: 'subHeadline',
              value: $event,
            })
          "
        />
      </LoadingWrapper>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    styles: {
      type: Object,
      default: () => {}
    },
    prompts: {
      type: Object,
      default: () => {}
    },
    headline: {
      type: String,
      default: ''
    },
    subHeadline: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      isLoading: []
    }
  },
  methods: {
    async getText (prompt) {
      try {
        if (!prompt) {
          throw new Error('Prompt is required')
        }
        const { success, data, errorMessage } = await this.$axios.$post('/admin/gpt3/chat', {
          prompt
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        return data.replace(/"/g, '').trim()
      } catch (err) {
        console.log(err)
        return ''
      }
    },
    async generateHeadline () {
      try {
        this.isLoading.push('headline')
        const prompt = [this.prompts.headlinePrompt]

        const text = await this.getText(prompt)
        if (!text || text.length === 0 || text === '') {
          throw new Error('No text returned')
        }
        this.$emit('change', {
          field: 'headline',
          value: text
        })
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = this.isLoading.filter(item => item !== 'headline')
      }
    },
    async generateSubHeadline () {
      try {
        this.isLoading.push('subHeadline')
        const prompt = [this.prompts.headlinePrompt, { role: 'system', content: this.headline }, this.prompts.subHeadlinePrompt]

        const text = await this.getText(prompt)
        if (!text || text.length === 0 || text === '') {
          throw new Error('No text returned')
        }
        this.$emit('change', {
          field: 'subHeadline',
          value: text
        })
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = this.isLoading.filter(item => item !== 'subHeadline')
      }
    }
  }
}
</script>

<style></style>
