<template>
  <div class="w-full space-y-4 rounded-md border border-gray-200 bg-white p-4">
    <div class="flex w-full items-center justify-between">
      <h1 class="text-xl font-medium text-gray-900">
        Features
      </h1>
      <ButtonPrimary size="sm" @click="generateAll">
        Generate All
      </ButtonPrimary>
    </div>
    <div class="space-y-8">
      <template v-for="(section, index) in sections">
        <LoadingWrapper :key="section.title + index" :is-loading="isLoading.length > 0 && isLoading.includes(section.type)" title="Generating paragraph...">
          <div class="flex w-full items-center justify-between">
            <h2 class="font-medium">
              {{ section.title }}
            </h2>
            <ButtonWhite size="xs" @click="generateSection(section.type)">
              {{ section.description ? "Regenerate" : "Generate" }}
            </ButtonWhite>
          </div>

          <InputTextArea
            :rows="5"
            label="Description"
            class="mt-2"
            :value="section.description"
            @input="
              $emit('changeSection', {
                index,
                field: `description`,
                value: $event,
              })
            "
          />
          <div class="mb-1">
            <img v-if="previews[section.type] || section.image" :src="previews[section.type] || section.image" class="mt-4 h-16 w-16">
            <label for="email" class="block text-sm font-medium leading-5 text-gray-700">Image</label>
            <input
              :id="`file${section.type}`"
              ref="fileextra"
              name="fileextra"
              type="file"
              accept="image/png, image/jpeg, image/jpg"
              enctype="multipart/form-data"
              @change="addFile($event, section.type, index)"
            >
          </div>
        </LoadingWrapper>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    styles: {
      type: Object,
      default: () => {}
    },
    prompts: {
      type: Object,
      default: () => {}
    },
    sections: {
      type: Array,
      default: () => []
    },
    headline: {
      type: String,
      default: ''
    },
    subHeadline: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      isLoading: [],
      previews: {
        what: null,
        when: null,
        who: null
      }
    }
  },
  methods: {
    previewImage (type, file) {
      const reader = new FileReader()
      reader.onload = (event) => {
        this.previews[type] = event.target.result
      }
      reader.readAsDataURL(file)
    },
    addFile (event, type, index) {
      const file = event.target.files[0]
      this.$emit('changeSection', {
        index,
        field: 'image',
        value: file
      })
      this.previewImage(type, file)
    },
    async generateAll () {
      this.isLoading.push('what')
      this.isLoading.push('when')
      this.isLoading.push('who')

      await this.generateWhat()
      await this.generateWhen()
      await this.generateWho()
    },
    generateSection (section) {
      switch (section) {
        case 'what':
          this.generateWhat()
          break
        case 'when':
          this.generateWhen()
          break
        case 'who':
          this.generateWho()
          break
      }
    },
    async generateWhat () {
      await this.generate('what', 0, [this.prompts.headlinePrompt, this.headline, this.prompts.subHeadlinePrompt, this.subHeadline, this.prompts.whatPrompt])
    },
    async generateWhen () {
      await this.generate('when', 1, [this.prompts.headlinePrompt, this.headline, this.prompts.subHeadlinePrompt, this.subHeadline, this.prompts.whatPrompt, this.sections[0].description, this.prompts.whenPrompt])
    },
    async generateWho () {
      await this.generate('who', 2, [this.prompts.headlinePrompt, this.headline, this.prompts.subHeadlinePrompt, this.subHeadline, this.prompts.whatPrompt, this.sections[0].description, this.prompts.whenPrompt, this.sections[1].description, this.prompts.whoPrompt])
    },
    async generate (type, index, promptItems) {
      try {
        this.isLoading.push(type)
        const prompt = promptItems.map((item, idx) => {
          if (idx % 2 === 0) {
            return item
          }
          return { role: 'system', content: item }
        })

        const text = await this.getText(prompt)
        if (!text || text.length === 0 || text === '') {
          throw new Error('No text returned')
        }
        this.$emit('changeSection', {
          index,
          field: 'description',
          value: text
        })
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = this.isLoading.filter(item => item !== type)
      }
    },
    async getText (prompt) {
      try {
        if (!prompt) {
          throw new Error('Prompt is required')
        }
        const { success, data, errorMessage } = await this.$axios.$post('/admin/gpt3/chat', {
          prompt
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        return data.replace(/"/g, '').trim()
      } catch (err) {
        console.log(err)
        return ''
      }
    }
  }
}
</script>

<style></style>
