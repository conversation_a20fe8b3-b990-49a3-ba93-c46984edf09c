<template>
  <div class="flex flex-col space-y-0.5 text-sm px-2">
    <div class="font-bold text-xs text-gray-700">
      {{ title }}
    </div>
    <div class="flex gap-1">
      <template v-if="!isEditing">
        <span class="flex-1">{{ finalValue }}</span>
        <ButtonWhite size="xs" type="button" @click="isEditing = true">
          <IconSolidEdit class="h-3 w-3 text-gray-500" />
        </ButtonWhite>
      </template>
      <template v-else>
        <Input v-model="newValue" placeholder="New value" class="flex-1" />
        <ButtonWhite size="xs" type="button" @click="cancel">
          <IconCross class="h-6 w-6 text-gray-500" />
        </ButtonWhite>
        <ButtonPrimary size="xs" type="button" @click="save">
          <IconCheck class="h-6 w-6 text-white" />
        </ButtonPrimary>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      required: true
    },
    value: {
      type: String,
      required: true
    },
    modelId: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      isEditing: false,
      finalValue: this.value,
      newValue: this.value
    }
  },
  methods: {
    cancel () {
      this.newValue = this.finalValue
      this.isEditing = false
    },
    save () {
      if (!confirm(`Are you sure you want to update the ${this.title} of this model?`)) {
        return
      }

      this.$axios.$post(`/admin/model/${this.modelId}/appearance`, {
        type: this.title,
        value: this.newValue
      }).then((res) => {
        this.finalValue = this.newValue
        this.isEditing = false
        this.$toast.success('Appearance updated!')
      }).catch((err) => {
        console.log(err)
        this.$toast.error('There was an error updating the appearance of this model')
      })
    }
  }
}
</script>

<style>

</style>
