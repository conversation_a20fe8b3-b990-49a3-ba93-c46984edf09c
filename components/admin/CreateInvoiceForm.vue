<template>
  <div class="w-full bg-white p-8 lg:w-[800px]">
    <div class="space-y-4">
      <Input
        theme="v2"
        label="Select an organization"
        :value="searchTerm"
        class="w-full"
        list="organizations"
        placeholder="Organization name"
        @input="handleSearchInput($event)"
      />
      <datalist id="organizations">
        <option v-for="organization in organizations" :key="organization._id" :value="organization._id">
          {{ organization.name }} ({{ organization.owner?.email || 'No email' }})
        </option>
      </datalist>
      <Input
        theme="v2"
        type="date"
        :value="invoice.date"
        class="w-full"
        label="Date"
        placeholder="Date"
        @input="updateDate($event)"
      />
      <div class="grid grid-cols-3 gap-4">
        <Input
          theme="v2"
          type="number"
          :min="1"
          :value="invoice.quantity"
          class="w-full"
          label="Quantity"
          placeholder="Quantity"
          @input="updateQuantity($event)"
        />
        <Input
          theme="v2"
          type="number"
          :min="0"
          :max="100"
          :value="invoice.discount"
          class="w-full"
          label="Discount %"
          placeholder="Discount %"
          @input="updateDiscount($event)"
        />
        <InputSelect
          theme="v2"
          :value="invoice.currency"
          :options="currencyOptions"
          class="w-full"
          label="Currency"
          @input="updateCurrency($event)"
        />
      </div>
      <div class="flex items-start gap-2 flex-col">
        <InputCheckbox id="dontApplyCredits" v-model="invoice.dontApplyCredits" label="Don't apply credits on purchase" />
        <span class="text-sm text-gray-500">
          <template v-if="invoice.dontApplyCredits">
            When paid, the organization will need to be handled manually.
          </template>
          <template v-else>
            When paid, the organization will be credited for the purchase.
          </template>
        </span>
      </div>
      <InputTextArea
        theme="v2"
        label="Billing details"
        :value="invoice.billingDetails"
        class="w-full"
        placeholder="Billing details"
        @input="updateBillingDetails($event)"
      />
      <div class="space-y-2">
        <H5>Billing information</H5>
        <Paragraph size="sm">
          By default, the billing information will be the organization's information. Add below to overwrite this.
        </Paragraph>
        <div class="grid grid-cols-2 gap-4">
          <Input
            v-for="field in ['firstName', 'lastName', 'companyName', 'email']"
            :key="field"
            theme="v2"
            :label="field"
            :value="invoice.meta[field]"
            class="w-full"
            @input="updateMeta(field, $event)"
          />
        </div>
      </div>

      <InputCheckbox id="sendQuoteToEmail" v-model="invoice.sendQuoteToEmail" label="Send quote to email" />

      <div class="bg-gray-100 rounded-lg p-4 text-center font-bold text-lg">
        Total: {{ formattedPrice }}
      </div>
      <ButtonPrimary @click="createInvoice">
        Create invoice
      </ButtonPrimary>
    </div>
  </div>
</template>

<script>
import H5 from '@/components/landingpage/common/H5.vue'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'

export default {
  components: {
    H5, Paragraph
  },
  data () {
    return {
      searchTerm: '',
      searchTimeout: null,
      invoice: {
        organizationId: null,
        quantity: 1,
        billingDetails: '',
        date: new Date().toISOString().split('T')[0],
        discount: 0,
        currency: 'USD',
        dontApplyCredits: false,
        meta: {
          firstName: null,
          lastName: null,
          companyName: null,
          email: null
        },
        sendQuoteToEmail: false
      },
      unitPrice: 39,
      organizations: [],
      currencyOptions: [
        { title: 'USD ($)', value: 'USD' },
        { title: 'EUR (€)', value: 'EUR' },
        { title: 'GBP (£)', value: 'GBP' },
        { title: 'CAD (C$)', value: 'CAD' },
        { title: 'AUD (A$)', value: 'AUD' },
        { title: 'JPY (¥)', value: 'JPY' },
        { title: 'CHF (CHF)', value: 'CHF' },
        { title: 'SEK (kr)', value: 'SEK' },
        { title: 'NOK (kr)', value: 'NOK' },
        { title: 'DKK (kr)', value: 'DKK' },
        { title: 'CNY (¥)', value: 'CNY' }
      ]
    }
  },
  computed: {
    totalPrice () {
      const quantity = this.invoice.quantity || 1
      return this.unitPrice * quantity
    },
    price () {
      if (this.invoice.discount > 0) {
        return this.totalPrice * (1 - this.invoice.discount / 100)
      }
      return this.totalPrice
    },
    currentDiscountTier () {
      const selectedOrg = this.organizations.find(org => org._id === this.invoice.organizationId)
      const quantity = this.invoice.quantity || 1
      const activeBulkDiscount = this.getTeamDiscountOptions(selectedOrg).find(item => quantity >= item.from && quantity <= item.to)
      return activeBulkDiscount
    },
    formattedPrice () {
      return this.formatPrice(this.price, this.invoice.currency, 2, false)
    }
  },
  watch: {
    currentDiscountTier () {
      this.invoice.discount = this.currentDiscountTier?.discount || 0
    }
  },
  mounted () {
    this.fetchData()
  },
  methods: {
    updateMeta (field, value) {
      this.invoice.meta[field] = value
    },
    async fetchData () {
      this.$loading.show({
        title: 'Loading organizations...'
      })
      try {
        const { success, data, errorMessage } = await this.$axios.$get('/admin/organization/search?search=')
        if (!success) {
          throw new Error(errorMessage)
        }

        data.sort((a, b) => a.name.localeCompare(b.name))
        this.organizations = data
      } catch (err) {
        this.handleError(err)
      } finally {
        this.$loading.hide()
      }
    },
    handleSearchInput (value) {
      this.searchTerm = value

      // First check if the value matches an existing org ID in current list
      const selectedOrgById = this.organizations.find(org => org._id === value)

      // Also check if the value matches an organization name in current list
      const selectedOrgByName = this.organizations.find(org => org.name.toLowerCase() === value.toLowerCase())

      if (selectedOrgById) {
        this.invoice.organizationId = value
        clearTimeout(this.searchTimeout)
      } else if (selectedOrgByName) {
        this.invoice.organizationId = selectedOrgByName._id
        // Update search term to show the org ID (as per current behavior)
        this.searchTerm = selectedOrgByName._id
        clearTimeout(this.searchTimeout)
      } else {
        this.invoice.organizationId = null
        clearTimeout(this.searchTimeout)
        this.searchTimeout = setTimeout(() => {
          this.fetchOrganizationsBySearch()
        }, 1000)
      }
    },
    async fetchOrganizationsBySearch () {
      if (this.invoice.organizationId) { return }

      if (!this.searchTerm.trim()) {
        await this.fetchData()
        return
      }

      this.$loading.show({ title: 'Searching organizations...' })
      try {
        const encodedSearchTerm = encodeURIComponent(this.searchTerm)

        // Check if the search term looks like a MongoDB ObjectId (24 hex characters)
        const isLikelyObjectId = /^[0-9a-fA-F]{24}$/.test(this.searchTerm.trim())

        // The backend automatically searches by ID if the search term is a valid ObjectId
        const { success, data, errorMessage } = await this.$axios.$get(`/admin/organization/search?search=${encodedSearchTerm}`)
        if (!success) { throw new Error(errorMessage) }

        data.sort((a, b) => a.name.localeCompare(b.name))
        this.organizations = data

        // If we found an exact match by ID, automatically select it
        if (isLikelyObjectId && data.length === 1) {
          this.invoice.organizationId = data[0]._id
          this.searchTerm = data[0]._id
        }
      } catch (err) {
        this.handleError(err)
      } finally {
        this.$loading.hide()
      }
    },
    updateQuantity (value) {
      this.invoice.quantity = value
    },
    updateBillingDetails (value) {
      this.invoice.billingDetails = value
    },
    updateDate (value) {
      this.invoice.date = value
    },
    updateDiscount (value) {
      this.invoice.discount = value
    },
    updateCurrency (value) {
      this.invoice.currency = value
    },
    async createInvoice () {
      this.$loading.show({
        title: 'Creating invoice...'
      })
      try {
        // Validate input
        if (!this.invoice.organizationId) {
          this.$toast.error('Please select an organization')
          return
        }
        if (!this.invoice.quantity || this.invoice.quantity < 1) {
          this.$toast.error('Please enter a valid quantity')
          return
        }
        const { success, errorMessage } = await this.$axios.$post('/admin/invoices', this.invoice)
        if (success) {
          this.$toast.success('Invoice created successfully')
          this.$emit('success')
        } else {
          this.$toast.error(errorMessage)
        }
      } catch (err) {
        this.handleError(err)
      } finally {
        this.$loading.hide()
      }
    }
  }
}
</script>
