<template>
  <div class="relative group">
    <div v-if="photo?.type === 'regeneration'" class="group-hover:hidden top-2 right-8 absolute z-20 text-xs text-white font-normal px-2 py-1 rounded-md bg-teal-500">
      Remix
    </div>
    <div v-if="photo?.resemblanceScore && showResemblanceScore" class="absolute bg-black/70 rounded-md text-white text-xs p-1 top-2 left-2">
      {{ photo?.resemblanceScore?.toFixed(3) }}
    </div>
    <div v-if="photo?.pfp" class="w-[38px] h-[38px] rounded-full bg-white absolute bottom-2 left-2 overflow-hidden">
      <ImageDns :src="photo.pfp" />
    </div>
    <ImageDns :src="photo.thumbnail" class="rounded-md overflow-hidden" @click="$emit('select', photo)" />
    <div v-if="photo?.actions?.blemishRemoved" class="absolute bottom-6 right-2 bg-pink-500 rounded-full w-4 h-4 border-2 border-white" />
    <div v-if="photo.isDownloaded" class="absolute bottom-2 right-2 bg-green-500 rounded-full w-4 h-4 border-2 border-white" />
    <div v-if="photo.approvedForReviewPage" class="absolute bottom-2 right-6 bg-blue-500 rounded-full w-4 h-4 border-2 border-white" />
    <div v-if="photo.upscaled" class="absolute bottom-2 right-8 bg-yellow-500 rounded-full w-4 h-4 border-2 border-white" />
    <div v-if="photo.nsfw" class="absolute top-2 right-2 bg-red-500 rounded-full w-4 h-4 border-2 border-white" />
    <div class="w-full h-full hidden group-hover:flex flex-col bg-black/40 text-white top-0 left-0 absolute p-4 z-10 cursor-pointer rounded-md" @click="$emit('select', photo)">
      <!-- <ul class="space-y-1">
        <li class="text-sm">
          {{ photo._id }}
        </li>
        <li v-for="(value, key) in photo.meta" :key="key" class="flex flex-col text-sm">
          <template v-if="key !== 'pose'">
            <div class="font-bold">
              {{ key }}
            </div>
            <div class="">
              {{ value }}
            </div>
          </template>
        </li>
      </ul> -->
    </div>
    <ButtonDropdown
      theme="v1"
      title="Actions"
      :items="actions"
      size="sm"
      icon="IconEllipsisVertical"
      icon-position="before"
      class="absolute bottom-2 right-2 hidden group-hover:block z-30"
      @select="handleAction"
    />
  </div>
</template>

<script>
export default {
  props: {
    photo: {
      type: Object,
      required: true
    },
    showResemblanceScore: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    actions () {
      const actions = [
        { title: 'Download', value: 'download', icon: 'IconCloudDownload' }
      ]

      if (this.photo.approvedForReviewPage) {
        actions.push({ title: 'Remove review', value: 'remove_review', icon: 'IconThumbDown' })
      } else {
        actions.push({ title: 'Approve review', value: 'approve_review', icon: 'IconThumbUp' })
      }

      if (this.photo.nsfw) {
        actions.push({ title: 'Set as SFW', value: 'set_as_sfw', icon: 'IconCheck' })
      } else {
        actions.push({ title: 'Set as NSFW', value: 'set_as_nsfw', icon: 'IconCross' })
      }

      if (this.photo.upscaled) {
        actions.push({ title: 'Download upscaled', value: 'download_upscaled', icon: 'IconCloudDownload' })
      } else {
        actions.push({ title: 'Upscale', value: 'upscale', icon: 'IconPhoto' })
      }

      actions.push({ title: 'Delete', value: 'delete', icon: 'IconTrash' })

      return actions
    }
  },
  methods: {
    handleAction (action) {
      switch (action) {
        case 'download':
          this.$emit('download', this.photo.image)
          break
        case 'download_upscaled':
          this.$emit('download', this.photo.upscaled)
          break
        case 'approve_review':
          this.$emit('approve-review', this.photo._id)
          break
        case 'remove_review':
          this.$emit('remove-review', this.photo._id)
          break
        case 'set_as_sfw':
          this.$emit('set-as-nsfw', { id: this.photo._id, nsfw: false })
          break
        case 'set_as_nsfw':
          this.$emit('set-as-nsfw', { id: this.photo._id, nsfw: true })
          break
        case 'upscale':
          this.$emit('upscale', this.photo._id)
          break
        case 'delete':
          this.$emit('delete', this.photo._id)
          break
      }
    }
  }
}
</script>
