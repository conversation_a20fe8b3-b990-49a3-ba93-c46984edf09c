<template>
  <nav class="flex">
    <ol role="list" class="flex flex-wrap items-center gap-y-5 gap-x-1.5 px-2 md:gap-y-0">
      <template v-for="(step, index) in steps">
        <li v-if="step.length > 0" :key="`step-${index}`">
          <div class="flex items-center">
            <svg
              v-if="index !== 0"
              class="h-3 w-3 flex-shrink-0 text-gray-300"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              stroke="currentColor"
              viewBox="0 0 20 20"
              aria-hidden="true"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <div class="-m-1">
              <div title="" :class="{ 'font-bold text-gray-700': index === activeStep, 'text-gray-400': index !== activeStep }" class="ml-1.5 rounded-md p-1 text-xs font-medium focus:outline-none">
                {{ step }}
              </div>
            </div>
          </div>
        </li>
      </template>
    </ol>
  </nav>
</template>

<script>
export default {
  props: {
    steps: {
      type: Array,
      required: true
    },
    activeStep: {
      type: Number,
      default: 0
    }
  }
}
</script>

<style></style>
