<template>
  <div
    class="space-y-2"
  >
    <div class="relative overflow-hidden rounded-lg cursor-pointer hover:scale-105 transition-all duration-150" @click="!selected && openModal()">
      <ImageDns class="object-cover w-full h-full" :src="image" alt="" />

      <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 via-gray-900/10 to-transparent" />

      <span
        v-if="item.mostPopular && !selected && !isTeamMember"
        class="absolute text-[8px] px-2 py-0.5 font-extrabold tracking-wide text-white uppercase bg-rose-500 rounded-full top-2 right-2"
      >
        {{ $t('Favorite') }}
      </span>
      <span
        v-if="selected"
        class="absolute text-[10px] px-2 py-0.5 font-extrabold tracking-wide text-white uppercase bg-teal-500 rounded-full top-2 right-2"
      >
        {{ $t('Selected') }}
      </span>

      <div class="absolute inset-x-0 bottom-0 flex items-center justify-center p-4">
        <p class="text-base font-medium tracking-tight text-white">
          {{ item.title }}
        </p>
      </div>
    </div>

    <button
      type="button"
      class="text-sm w-full font-medium text-primary-500 rounded-lg shadow-sm bg-white border border-gray-200 transition-all duration-150 hover:bg-gray-50 pt-1.5 pb-2 px-2.5 inline-flex items-center gap-1.5 justify-center"
      @click="openModal"
    >
      <span v-if="!selected">
        {{ $t('Select') }}
      </span>
      <span v-else>
        {{ $t('Select again') }}
      </span>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 20 20"
        fill="currentColor"
        class="size-5 -mb-0.5"
      >
        <path
          d="M10.75 4.75a.75.75 0 0 0-1.5 0v4.5h-4.5a.75.75 0 0 0 0 1.5h4.5v4.5a.75.75 0 0 0 1.5 0v-4.5h4.5a.75.75 0 0 0 0-1.5h-4.5v-4.5Z"
        />
      </svg>
    </button>
    <portal to="modal">
      <PostcheckoutClothingSelectorModal
        v-if="showModal"
        data-testid="clothing-selector-modal"
        :style-item="item"
        @closeModal="showModal = false"
        @select="selectedClothing"
      />
    </portal>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true
    },
    selectedStyles: {
      type: Array,
      required: true
    },
    popular: {
      type: Boolean
    }
  },
  data () {
    return {
      showModal: false
    }
  },
  computed: {
    image () {
      const gender = this.$store.state.onboarding.selectedSex || this.$store.state.results.item.trigger
      if (gender === 'male' && this.item.image?.maleImage) {
        return this.item.image.maleImage
      }

      if (gender === 'female' && this.item.image?.femaleImage) {
        return this.item.image.femaleImage
      }

      return this.item.image?.maleImage || this.item.image?.femaleImage || ''
    },
    selected () {
      if (this.isTeamMember) {
        return false
      }
      return this.selectedStyles.find(item => item.style === this.item._id)
    }
  },
  methods: {
    openModal () {
      this.showModal = true
    },
    selectedClothing (clothing) {
      this.$emit('select', clothing)
    }
  }
}
</script>
