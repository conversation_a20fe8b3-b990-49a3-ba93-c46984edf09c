<template>
  <span
    :class="`relative px-2 pt-0.5 pb-1 overflow-hidden text-xs font-medium text-white ${bg} rounded-full`"
  >
    <span class="absolute inset-y-0 left-0 bg-green-500 transition" :style="{ width: (current / total * 100) + '%' }" />
    <span class="relative">
      {{ current }}/{{ total }}
    </span>
  </span>
</template>

<script>
export default {
  props: {
    current: {
      type: Number,
      required: true
    },
    total: {
      type: Number,
      required: true
    },
    bg: {
      type: String,
      default: 'bg-green-700'
    }
  }
}
</script>
