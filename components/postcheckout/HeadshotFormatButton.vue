<template>
  <button class="relative block" type="button" @click="updateValue">
    <div
      class="relative px-2.5 pt-1.5 pb-2 cursor-pointer bg-white rounded-md ring-1 transition"
      :class="{
        'text-teal-500 ring-teal-500': value === identifier,
        'text-gray-500 ring-gray-200': value !== identifier
      }"
    >
      <div class="flex items-center gap-1.5">
        <div class="flex items-center">
          <p class="text-sm font-medium">
            {{ label }}
          </p>
          <span v-if="identifier === '3:2'" class="ml-1.5 text-[9px] font-bold uppercase gradient-bg text-white px-1.5 py-0.5 rounded-lg">
            {{ $t('Recommended') }}
          </span>
        </div>
      </div>
    </div>
  </button>
</template>

<script>
export default {
  props: {
    identifier: {
      type: String,
      required: true
    },
    label: {
      type: String,
      required: true
    },
    value: {
      type: String,
      required: true
    }
  },
  methods: {
    updateValue () {
      this.$emit('input', this.identifier)
    }
  }
}
</script>
