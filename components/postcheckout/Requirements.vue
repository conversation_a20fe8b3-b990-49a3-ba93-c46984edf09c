<template>
  <div class="px-4 py-8 border-l border-gray-100 bg-gray-50 lg:px-8 lg:py-12 xl:pl-12">
    <p class="text-base font-medium text-gray-500">
      {{ $t('Photo requirements') }}
    </p>
    <p class="mt-1 text-lg font-bold tracking-tight text-primary-500">
      {{ $t('How to help our AI help you') }}
    </p>

    <div class="flex flex-col justify-start items-start gap-4 mt-4 md:flex-row">
      <div class="flex items-center gap-1 shrink-0">
        <img class="object-cover w-16 shrink-0" src="@/assets/img/post-checkout/image-1.jpg" alt="">
        <svg
          aria-hidden="true"
          class="text-gray-300 size-5 shrink-0"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path stroke="none" d="M0 0h24v24H0z" fill="none" />
          <path d="M5 12l14 0" />
          <path d="M15 16l4 -4" />
          <path d="M15 8l4 4" />
        </svg>
        <img class="object-cover w-16 shrink-0" src="@/assets/img/post-checkout/image-2.jpg" alt="">
      </div>

      <p class="flex-1 text-sm font-medium text-gray-500">
        {{ $t('accuracyText') }}
      </p>
    </div>

    <div class="mt-8">
      <div class="inline-flex items-center gap-1.5">
        <p class="text-lg font-bold tracking-tight text-red-500">
          {{ $t('Don\'t') }}
        </p>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
          class="-mb-1 text-red-500 size-5"
        >
          <path
            fill-rule="evenodd"
            d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z"
            clip-rule="evenodd"
          />
        </svg>
      </div>

      <div class="flex flex-col mt-3 text-base font-medium tracking-tight text-gray-500 gap-1.5 md:gap-x-4 md:gap-y-1.5 md:flex-row">
        <div class="space-y-1.5">
          <PostcheckoutDont :text="$t('Don\'t wear sunglasses')" />
          <PostcheckoutDont :text="$t('Group photos')" />
          <PostcheckoutDont :text="$t('Face too far/too close')" />
        </div>

        <div class="space-y-1.5">
          <PostcheckoutDont :text="$t('Not looking at the camera')" />
          <PostcheckoutDont :text="$t('Funny/dumb expressions')" />
          <PostcheckoutDont :text="$t('Blurry and pixelated photo')" />
        </div>
      </div>

      <div class="grid grid-cols-5 gap-4 mt-6">
        <img class="object-cover w-full h-full rounded-lg" src="@/assets/img/post-checkout/image-3.jpg" alt="">
        <img class="object-cover w-full h-full rounded-lg" src="@/assets/img/post-checkout/image-4.jpg" alt="">
        <img class="object-cover w-full h-full rounded-lg" src="@/assets/img/post-checkout/image-5.jpg" alt="">
        <img class="object-cover w-full h-full rounded-lg" src="@/assets/img/post-checkout/image-6.jpg" alt="">
        <img class="object-cover w-full h-full rounded-lg" src="@/assets/img/post-checkout/image-7.jpg" alt="">
      </div>
    </div>

    <div class="mt-8">
      <div class="inline-flex items-center gap-1.5">
        <p class="text-lg font-bold tracking-tight text-green-500">
          {{ $t('Do') }}
        </p>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
          class="-mb-1 text-green-500 size-5"
        >
          <path
            fill-rule="evenodd"
            d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z"
            clip-rule="evenodd"
          />
        </svg>
      </div>

      <div class="flex flex-col mt-3 text-base font-medium tracking-tight text-gray-500 gap-1.5 md:gap-x-4 md:gap-y-1.5 md:flex-row">
        <div class="space-y-1.5">
          <PostcheckoutDo :text="$t('Look at the camera')" />
          <PostcheckoutDo :text="$t('Face is majority of the image')" />
          <PostcheckoutDo :text="$t('Professional expression')" />
        </div>

        <div class="space-y-1.5">
          <PostcheckoutDo :text="$t('One person, clean background')" />
          <PostcheckoutDo :text="$t('Good angle/clear face')" />
          <PostcheckoutDo :text="$t('Clear, in focus images')" />
        </div>
      </div>

      <div class="grid grid-cols-5 gap-4 mt-6">
        <img class="object-cover w-full h-full rounded-lg" src="@/assets/img/post-checkout/image-8.jpg" alt="">
        <img class="object-cover w-full h-full rounded-lg" src="@/assets/img/post-checkout/image-9.jpg" alt="">
        <img class="object-cover w-full h-full rounded-lg" src="@/assets/img/post-checkout/image-10.jpg" alt="">
        <img class="object-cover w-full h-full rounded-lg" src="@/assets/img/post-checkout/image-11.jpg" alt="">
        <img class="object-cover w-full h-full rounded-lg" src="@/assets/img/post-checkout/image-12.jpg" alt="">
      </div>
    </div>
  </div>
</template>

<i18n>
  {
    "en": {
      "Photo requirements": "Photo requirements",
      "How to help our AI help you": "How to help our AI help you",
      "accuracyText": "The accuracy of your likeness, facial expressions, and physical qualities depend on the photos you upload here. Please follow the instructions carefully.",
      "Don't": "Don't",
      "Don't wear sunglasses": "Don't wear sunglasses",
      "Group photos": "Group photos",
      "Face too far/too close": "Face too far/too close",
      "Not looking at the camera": "Not looking at the camera",
      "Funny/dumb expressions": "Funny/dumb expressions",
      "Blurry and pixelated photo": "Blurry and pixelated photo",
      "Do": "Do",
      "Look at the camera": "Look at the camera",
      "Face is majority of the image": "Face is majority of the image",
      "Professional expression": "Professional expression",
      "One person, clean background": "One person, clean background",
      "Good angle/clear face": "Good angle/clear face",
      "Clear, in focus images": "Clear, in focus images"
    },
    "es": {
      "Photo requirements": "Requisitos de las fotos",
      "How to help our AI help you": "Cómo ayudar a nuestra IA a ayudarte",
      "accuracyText": "La precisión de tu parecido, expresiones faciales y cualidades físicas dependen de las fotos que subas aquí. Por favor, sigue las instrucciones cuidadosamente.",
      "Don't": "No",
      "Don't wear sunglasses": "No uses gafas de sol",
      "Group photos": "Fotos de grupo",
      "Face too far/too close": "Cara demasiado lejos/demasiado cerca",
      "Not looking at the camera": "No mirar a la cámara",
      "Funny/dumb expressions": "Expresiones graciosas/tontas",
      "Blurry and pixelated photo": "Foto borrosa y pixelada",
      "Do": "Sí",
      "Look at the camera": "Mira a la cámara",
      "Face is majority of the image": "La cara es la mayor parte de la imagen",
      "Professional expression": "Expresión profesional",
      "One person, clean background": "Una persona, fondo limpio",
      "Good angle/clear face": "Buen ángulo/cara clara",
      "Clear, in focus images": "Imágenes nítidas y enfocadas"
    },
    "de": {
      "Photo requirements": "Foto-Anforderungen",
      "How to help our AI help you": "Wie du unserer KI hilfst, dir zu helfen",
      "accuracyText": "Die Genauigkeit deiner Ähnlichkeit, Gesichtsausdrücke und körperlichen Eigenschaften hängt von den Fotos ab, die du hier hochlädst. Bitte befolge die Anweisungen sorgfältig.",
      "Don't": "Nicht",
      "Don't wear sunglasses": "Keine Sonnenbrille tragen",
      "Group photos": "Gruppenfotos",
      "Face too far/too close": "Gesicht zu weit/zu nah",
      "Not looking at the camera": "Nicht in die Kamera schauen",
      "Funny/dumb expressions": "Lustige/alberne Ausdrücke",
      "Blurry and pixelated photo": "Verschwommenes und pixeliges Foto",
      "Do": "Tu",
      "Look at the camera": "In die Kamera schauen",
      "Face is majority of the image": "Gesicht ist die größte Teil der Bild",
      "Professional expression": "Professioneller Ausdruck",
      "One person, clean background": "Eine Person, sauberer Hintergrund",
      "Good angle/clear face": "Guter Winkel/klares Gesicht",
      "Clear, in focus images": "Scharfe Bilder"
    }
  }
</i18n>
