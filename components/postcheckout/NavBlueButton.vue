<template>
  <button
    type="button"
    class="text-sm hidden md:inline-flex font-medium text-white rounded-lg shadow-sm bg-primary-500 pt-1.5 pb-2 px-2.5 border border-transparent transition-all duration-150 hover:bg-opacity-90 gap-1.5 items-center justify-center disabled:bg-opacity-20"
    :disabled="disabled"
    @click="$emit('click')"
  >
    <slot />
  </button>
</template>

<script>
export default {
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  }
}
</script>
