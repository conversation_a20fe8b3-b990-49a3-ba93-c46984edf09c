<template>
  <div>
    <input
      ref="fileInput"
      type="file"
      accept="image/jpeg, image/png"
      multiple
      class="sr-only"
      tabindex="-1"
      @change="onFileChange"
    >
    <div
      ref="dropzone"
      class="hidden p-8 space-y-4 text-center transition-all duration-200 border-2 border-dashed rounded-lg cursor-pointer lg:block bg-gray-50/30 hover:bg-sky-50/70 hover:border-sky-500"
      @click="$refs.fileInput.click()"
      @dragover.prevent="dragOverHandler"
      @dragleave.prevent="dragLeaveHandler"
      @drop.prevent="dropHandler"
    >
      <svg
        aria-hidden="true"
        class="mx-auto text-gray-300 size-10"
        viewBox="0 0 41 41"
        fill="currentColor"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M13.0284 32.5803H9.86834C5.02959 32.2347 2.85718 28.5151 2.85718 25.207C2.85718 21.8989 5.02961 18.1629 9.78607 17.8337C10.4609 17.7679 11.0533 18.2945 11.1027 18.9858C11.1521 19.6606 10.642 20.2531 9.95072 20.3024C6.7578 20.5329 5.32593 22.9358 5.32593 25.2235C5.32593 27.5112 6.7578 29.9141 9.95072 30.1445H13.0284C13.7032 30.1445 14.2628 30.7041 14.2628 31.3789C14.2628 32.0537 13.7032 32.5803 13.0284 32.5803Z"
        />
        <path
          d="M28.1859 32.5804C28.1529 32.5804 28.1365 32.5804 28.1036 32.5804C27.4288 32.5804 26.8034 32.0209 26.8034 31.3461C26.8034 30.6384 27.3301 30.1117 28.0213 30.1117C30.0457 30.1117 31.8561 29.404 33.2715 28.1367C35.839 25.8984 36.0036 22.6725 35.3123 20.4013C34.6211 18.1465 32.6955 15.5625 29.338 15.1511C28.7948 15.0852 28.3669 14.6738 28.2681 14.1307C27.6098 10.1807 25.4867 7.44857 22.2609 6.46107C18.9363 5.42419 15.0521 6.44461 12.6327 8.97919C10.2792 11.4315 9.73605 14.8713 11.1021 18.6567C11.3325 19.2986 11.0034 20.0063 10.3615 20.2367C9.71967 20.4671 9.01193 20.1379 8.78151 19.4961C7.11922 14.8548 7.87632 10.4111 10.8553 7.28398C13.9001 4.09107 18.7882 2.82376 22.9851 4.10751C26.8363 5.29251 29.5519 8.46897 30.5394 12.8798C33.8969 13.6369 36.5961 16.1879 37.6659 19.71C38.8344 23.5448 37.7811 27.4948 34.9009 29.9965C33.074 31.6423 30.6875 32.5804 28.1859 32.5804Z"
        />
        <path
          d="M20.4999 37.4193C17.1918 37.4193 14.0977 35.6582 12.4024 32.8109C12.2214 32.5311 12.0404 32.202 11.8922 31.8399C11.3327 30.6714 11.0364 29.3382 11.0364 27.9557C11.0364 22.7384 15.2826 18.4922 20.4999 18.4922C25.7172 18.4922 29.9635 22.7384 29.9635 27.9557C29.9635 29.3547 29.6672 30.6714 29.0747 31.8893C28.9431 32.202 28.762 32.5311 28.5645 32.8439C26.9023 35.6582 23.808 37.4193 20.4999 37.4193ZM20.4999 20.9609C16.6487 20.9609 13.5051 24.1045 13.5051 27.9557C13.5051 28.9761 13.7191 29.9307 14.1306 30.803C14.2622 31.0828 14.3774 31.3132 14.5091 31.5272C15.7599 33.6503 18.0476 34.9505 20.4834 34.9505C22.9193 34.9505 25.207 33.6503 26.4414 31.5601C26.5895 31.3132 26.7212 31.0828 26.82 30.8524C27.2643 29.9472 27.4782 28.9926 27.4782 27.9722C27.4947 24.1045 24.3512 20.9609 20.4999 20.9609Z"
        />
        <path
          d="M19.5618 30.8192C19.2491 30.8192 18.9365 30.704 18.6896 30.4571L17.0601 28.8278C16.5828 28.3505 16.5828 27.5605 17.0601 27.0832C17.5374 26.6059 18.3274 26.6059 18.8047 27.0832L19.5948 27.8732L22.2281 25.4373C22.7383 24.9765 23.5118 25.0094 23.9726 25.5032C24.4335 25.9969 24.4006 26.7869 23.9068 27.2478L20.4012 30.4901C20.1543 30.704 19.8581 30.8192 19.5618 30.8192Z"
        />
      </svg>

      <div>
        <p class="text-sm font-medium tracking-tight text-primary-500">
          {{ $t('Choose an image or drag & drop it here') }}
        </p>
        <p class="mt-1 text-sm font-medium text-gray-500">
          {{ $t('JPEG and PNG formats, up to 50MB') }}
        </p>
      </div>

      <span
        class="text-sm font-medium text-primary-500 rounded-lg shadow-sm bg-white border border-gray-200 transition-all duration-150 hover:bg-gray-50 pt-2 pb-2.5 px-12 inline-flex items-center gap-1.5 justify-center"
      >
        {{ $t('Browse file') }}
      </span>
    </div>

    <PostcheckoutUploadImagesButton
      :text="$t('Upload from my gallery')"
      :mobile-text="$t('Upload from my gallery')"
      class="lg:hidden"
      @click="$refs.fileInput.click()"
    >
      <svg
        aria-hidden="true"
        class="text-gray-400 size-6"
        viewBox="0 0 27 28"
        fill="currentColor"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M21.375 6.125C22.5612 6.125 23.5331 7.04286 23.619 8.20708L23.6252 8.375V21.875C23.6252 23.0612 22.7072 24.0329 21.5429 24.1188L21.375 24.125H5.625C4.43884 24.125 3.46706 23.2072 3.38117 22.0429L3.375 21.875V8.375C3.375 7.18884 4.29286 6.21706 5.45708 6.13117L5.625 6.125H21.375ZM21.375 8.375H5.625V21.875H6.67018L16.0172 12.5279C16.5664 11.9788 17.4567 11.9788 18.006 12.5279L21.375 15.897V8.375ZM17.0116 14.7156L9.85216 21.875H21.375V19.0789L17.0116 14.7156ZM9.5625 10.625C10.4945 10.625 11.25 11.3805 11.25 12.3125C11.25 13.2444 10.4945 14 9.5625 14C8.63052 14 7.875 13.2444 7.875 12.3125C7.875 11.3805 8.63052 10.625 9.5625 10.625ZM20.25 2.75C20.8713 2.75 21.375 3.25368 21.375 3.875C21.375 4.49632 20.8713 5 20.25 5H6.75C6.12868 5 5.625 4.49632 5.625 3.875C5.625 3.25368 6.12868 2.75 6.75 2.75H20.25Z"
        />
      </svg>
    </PostcheckoutUploadImagesButton>
  </div>
</template>

<script>
export default {
  methods: {
    dragOverHandler () {
      this.$refs.dropzone.classList.add('dropzone-active')
    },
    dragLeaveHandler () {
      this.$refs.dropzone.classList.remove('dropzone-active')
    },
    async dropHandler (e) {
      this.$refs.dropzone.classList.remove('dropzone-active')

      if (e.dataTransfer.files.length === 0) {
        return
      }

      await this._uploadFiles(e.dataTransfer.files)
    },
    async onFileChange (e) {
      if (!e.target.files[0]) {
        return
      }

      await this._uploadFiles(e.target.files)
    },
    async _uploadFiles (files) {
      const fileArray = Array.from(files)
      const allowedMimes = ['image/png', 'image/jpeg']
      const chunkSize = 2 // Can't make this too high otherwise the full body check doesn't work properly
      const chunks = []
      for (let i = 0; i < fileArray.length; i += chunkSize) {
        chunks.push(fileArray.slice(i, i + chunkSize))
      }

      for (const chunk of chunks) {
        await Promise.allSettled(chunk.map(async (file) => {
          if (allowedMimes.includes(file.type)) {
            try {
              await this.$store.dispatch('onboarding/addPhoto', {
                file
              })
            } catch (error) {
              this.$toast.warning(error.message || this.$t('Failed to upload image'))
            }
          } else {
            this.$toast.warning(this.$t('Only JPEG and PNG formats are supported'))
          }
        }))
      }
    }
  }
}
</script>

<style scoped>
  .dropzone-active {
    @apply border-sky-500 bg-sky-50/70;
  }
</style>

<i18n>
  {
    "en": {
      "Choose an image or drag & drop it here": "Choose an image or drag & drop it here",
      "JPEG and PNG formats, up to 50MB": "JPEG and PNG formats, up to 50MB",
      "Browse file": "Browse file",
      "Upload from my gallery": "Upload from my gallery",
      "Failed to upload image": "Failed to upload image",
      "Only JPEG and PNG formats are supported": "Only JPEG and PNG formats are supported"
    },
    "es": {
      "Choose an image or drag & drop it here": "Elige una imagen o arrástrala aquí",
      "JPEG and PNG formats, up to 50MB": "Formatos JPEG y PNG, hasta 50 MB",
      "Browse file": "Explorar archivos",
      "Upload from my gallery": "Subir desde mi galería",
      "Failed to upload image": "Error al subir la imagen",
      "Only JPEG and PNG formats are supported": "Solo se admiten formatos JPEG y PNG"
    },
    "de": {
      "Choose an image or drag & drop it here": "Bild auswählen oder hierher ziehen",
      "JPEG and PNG formats, up to 50MB": "JPEG und PNG Formate, bis zu 50MB",
      "Browse file": "Datei durchsuchen",
      "Upload from my gallery": "Aus Galerie hochladen",
      "Failed to upload image": "Bild konnte nicht hochgeladen werden",
      "Only JPEG and PNG formats are supported": "Nur JPEG und PNG Formate werden unterstützt"
    }
  }
</i18n>
