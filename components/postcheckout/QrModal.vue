<template>
  <div v-if="isIOS || isAndroid">
    <div class="">
      <p class="text-lg font-bold tracking-tight text-primary-500">
        {{ goodPhotos.length === 0 ? $t('Capture selfies') : $t('takenNSelfies', { n: goodPhotos.length, min: minimumPhotos }) }}
      </p>
      <p class="mt-1 text-base text-gray-500">
        {{ goodPhotos.length === 0 ? $t('Easily capture selfies with our phone onboarding process. Tap the button below to get started.') : $t('Click on Continue when you finish taking all your pictures.') }}
      </p>
      <div v-if="goodPhotos.length === 0" class="px-4 py-3 mt-4 border border-yellow-200 rounded-lg bg-yellow-50">
        <div class="flex items-center gap-4">
          <div class="inline-flex items-center justify-center bg-yellow-200 rounded-full size-8 shrink-0">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              class="text-yellow-400 size-6"
            >
              <path
                fill-rule="evenodd"
                d="M18 10a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-8-5a.75.75 0 0 1 .75.75v4.5a.75.75 0 0 1-1.5 0v-4.5A.75.75 0 0 1 10 5Zm0 10a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z"
                clip-rule="evenodd"
              />
            </svg>
          </div>

          <div class="flex-1 min-w-0">
            <p class="text-base font-medium tracking-tight text-yellow-800">
              {{ $t('Keep this window open') }}
            </p>
            <p class="text-sm text-yellow-800 mt-0.5">
              {{ $t('Do not close this window until you have uploaded all your photos on your phone. Come back here when you finish!') }}
            </p>
          </div>
        </div>
      </div>
      <ButtonWhite v-if="goodPhotos.length > 0" type="button" class="mt-4 w-full" @click="closeModal">
        {{ $t('Continue') }}
      </ButtonWhite>
      <ButtonPrimary type="button" class="mt-4 w-full" @click="openLinkInNewWindow">
        {{ goodPhotos.length > 0 ? $t('Capture more selfies') : $t('Start capturing selfies') }}
      </ButtonPrimary>
    </div>
  </div>
  <div v-else>
    <div class="">
      <p class="text-lg font-bold tracking-tight text-primary-500">
        {{ $t('Scan QR code with phone camera') }}
      </p>
      <p class="mt-1 text-base text-gray-500">
        {{ $t('Easily capture selfies with our phone onboarding process. Scan the QR code below with your phone to get started.') }}
      </p>
    </div>

    <PostcheckoutPhoneConnected v-if="phoneConnected" class="mt-4" />

    <div class="px-4 py-3 mt-4 border border-yellow-200 rounded-lg bg-yellow-50">
      <div class="flex items-center gap-4">
        <div class="inline-flex items-center justify-center bg-yellow-200 rounded-full size-8 shrink-0">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
            class="text-yellow-400 size-6"
          >
            <path
              fill-rule="evenodd"
              d="M18 10a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-8-5a.75.75 0 0 1 .75.75v4.5a.75.75 0 0 1-1.5 0v-4.5A.75.75 0 0 1 10 5Zm0 10a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z"
              clip-rule="evenodd"
            />
          </svg>
        </div>

        <div class="flex-1 min-w-0">
          <p class="text-base font-medium tracking-tight text-yellow-800">
            {{ $t('Keep this screen open') }}
          </p>
          <p class="text-sm text-yellow-800 mt-0.5">
            {{ $t('Do not close this screen until you have uploaded all your photos on your phone.') }}
          </p>
          <p class="text-sm text-yellow-700 mt-1">
            {{ $t('Your photos will appear automatically on this desktop page when uploaded.') }}
          </p>
        </div>
      </div>
    </div>

    <div class="flex items-center justify-center p-6 mt-4 bg-gray-100 rounded-lg">
      <div class="border border-gray-200 size-40">
        <qrcode
          v-if="phoneRoomLinkQrCode"
          :value="phoneRoomLinkQrCode"
          class="rounded-md ring-1 ring-gray-200 !w-full !h-full"
        />
      </div>
    </div>

    <div class="relative mt-2.5">
      <div class="absolute inset-0 flex items-center" aria-hidden="true">
        <div class="w-full border-t border-gray-200" />
      </div>
      <div class="relative flex justify-center">
        <span class="bg-white px-2.5 text-sm font-medium  text-gray-400">
          {{ $t('Or use the link below') }}
        </span>
      </div>
    </div>

    <div v-if="showPhoneLink" class="mt-2.5">
      <p class="text-sm font-medium text-gray-500">
        {{ phoneRoomLinkQrCode }}
      </p>
    </div>
  </div>
</template>

<script>
import VueQrcode from '@chenfengyuan/vue-qrcode'
import PostcheckoutMixin from '../../mixins/PostcheckoutMixin'

export default {
  components: {
    qrcode: VueQrcode
  },
  mixins: [PostcheckoutMixin],
  data () {
    return {
      showPhoneLink: false,
      phoneRoomLink: null,
      webcamModal: false,
      phoneConnected: false,
      showPhoneUpload: true
    }
  },
  computed: {
    phoneRoomLinkQrCode () {
      if (this.goodPhotos.length > 0) {
        return `${this.phoneRoomLink}?hasPicturesApproved=true`
      }
      return this.phoneRoomLink
    },
    isAndroid () {
      return navigator.userAgent.toLowerCase().includes('android')
    },
    isIOS () {
      return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream
    }
  },
  watch: {
    '$store.state.onboarding.photos' (newVal, oldVal) {
      if (newVal.length === this.minimumPhotos) {
        this.closeModal()
      }
    }
  },
  mounted () {
    this.createRoom()
  },
  methods: {
    receiveImage (arrayBuffer, message = 'Received image') {
      this.$toast.success(message)
      this.showPhoneLink = false
      const blob = new Blob([arrayBuffer], { type: 'image/png' })
      this.$store.dispatch('onboarding/addPhoto', {
        file: blob
      }).catch((error) => {
        this.$toast.warning(error.message || this.$t('Failed to upload image'))
      })
      this.addingFiles = false
    },
    createRoom () {
      try {
        this.createAndStoreNewLink()

        this.showPhoneLink = true
      } catch (error) {
        this.$toast.warning(
          error?.response?.data?.message || this.$t('Something went wrong')
        )
      }
    },
    createAndStoreNewLink () {
      const link = process.env.BASE_URL + '/photo-upload/' + this.$store.state.onboarding.modelId
      this.phoneRoomLink = link
    },
    get30MinTimestamp () {
      const now = new Date()
      now.setMinutes(now.getMinutes() + 30)
      let hours = now.getHours()
      let minutes = now.getMinutes()
      const ampm = hours >= 12 ? 'PM' : 'AM'
      hours = hours % 12
      hours = hours || 12
      minutes = minutes < 10 ? '0' + minutes : minutes
      return hours + ':' + minutes + ' ' + ampm
    },
    disconnectAndHideQRModal () {
      this.showPhoneLink = false
      this.showPhoneUpload = false
    },
    startPhoneUploadFlow () {
      this.createRoom()
    },
    openLinkInNewWindow () {
      if (this.phoneRoomLinkQrCode) {
        window.open(this.phoneRoomLinkQrCode + '?phoneFlow=1', '_blank')
      }
    },
    closeModal () {
      this.$emit('closeModal')
    }
  }
}
</script>

<i18n>
  {
    "en": {
      "Capture selfies": "Capture selfies",
      "Easily capture selfies with our phone onboarding process. Tap the button below to get started.": "Easily capture selfies with our phone onboarding process. Tap the button below to get started.",
      "Keep this window open": "Keep this window open",
      "Do not close this window until you have uploaded all your photos on your phone. Come back here when you finish!": "Do not close this window until you have uploaded all your photos on your phone. Come back here when you finish!",
      "Start capturing selfies": "Start capturing selfies",
      "Failed to upload image": "Failed to upload image",
      "Scan QR code with phone camera": "Scan QR code with phone camera",
      "Easily capture selfies with our phone onboarding process. Scan the QR code below with your phone to get started.": "Easily capture selfies with our phone onboarding process. Scan the QR code below with your phone to get started.",
      "Keep this screen open": "Keep this screen open",
      "Do not close this screen until you have uploaded all your photos on your phone.": "Do not close this screen until you have uploaded all your photos on your phone.",
      "Or use the link below": "Or use the link below",
      "Something went wrong": "Something went wrong",
      "Uploading image from your phone": "Uploading image from your phone",
      "Connection established with server": "Connection established with server",
      "Connection lost with server, trying to reconnect": "Connection lost with server, trying to reconnect",
      "Reconnect attempt": "Reconnect attempt",
      "Reconnection failed, please try uploading directly from your computer": "Reconnection failed, please try uploading directly from your computer",
      "Phone is connected": "Phone is connected",
      "Phone is disconnected": "Phone is disconnected",
      "Connection encountered error:": "Connection encountered error:",
      "Capture more selfies": "Capture more selfies",
      "takenNSelfies": "You've taken {n}/{min} shots!",
      "Click on Continue when you finish taking all your pictures.": "Click on Continue when you finish taking all your pictures.",
      "Continue": "Continue",
      "Your photos will appear automatically on this desktop page when uploaded.": "Your photos will appear automatically on this desktop page when uploaded."
    },
    "es": {
      "Capture selfies": "Sácate selfies",
      "Easily capture selfies with our phone onboarding process. Tap the button below to get started.": "Sácate selfies fácilmente con nuestro proceso a través del móvil. Pulsa el botón a continuación para comenzar.",
      "Keep this window open": "Mantén esta ventana abierta",
      "Do not close this window until you have uploaded all your photos on your phone. Come back here when you finish!": "No cierres esta ventana hasta que hayas subido todas sus fotos desde tu móvil. ¡Vuelve aquí cuando termines!",
      "Start capturing selfies": "Empieza a sacarte selfies",
      "Failed to upload image": "Error al cargar la imagen",
      "Scan QR code with phone camera": "Escanee el código QR con la cámara del teléfono",
      "Easily capture selfies with our phone onboarding process. Scan the QR code below with your phone to get started.": "Sácate selfies fácilmente con nuestro proceso a través del móvil. Escanea el código QR a continuación con tu móvil para empezar.",
      "Keep this screen open": "Mantén esta pantalla abierta",
      "Do not close this screen until you have uploaded all your photos on your phone.": "No cierres esta pantalla hasta que haya subido todas las fotos en tu móvil.",
      "Or use the link below": "O usa el enlace a continuación",
      "Something went wrong": "Algo salió mal",
      "Uploading image from your phone": "Cargando imagen desde tu móvil",
      "Connection established with server": "Conexión establecida con el servidor",
      "Connection lost with server, trying to reconnect": "Conexión perdida con el servidor, intentando reconectar",
      "Reconnect attempt": "Intento de reconexión",
      "Reconnection failed, please try uploading directly from your computer": "La reconexión falló, intente cargar directamente desde tu ordenador",
      "Phone is connected": "El móvil está conectado",
      "Phone is disconnected": "El móvil está desconectado",
      "Connection encountered error:": "Se ha producido un error de conexión:",
      "Capture more selfies": "Sacarme más selfies",
      "takenNSelfies": "¡Te has sacado {n}/{min} selfies!",
      "Click on Continue when you finish taking all your pictures.": "Haz click en Continuar cuando hayas terminado.",
      "Continue": "Continuar",
      "Your photos will appear automatically on this desktop page when uploaded.": "Tus fotos aparecerán automáticamente en esta página de escritorio cuando se suban."
    },
    "de": {
      "Capture selfies": "Selfies aufnehmen",
      "Easily capture selfies with our phone onboarding process. Tap the button below to get started.": "Nimm einfach Selfies mit unserem Handy-Einrichtungsprozess auf. Tippe unten auf den Button, um zu starten.",
      "Keep this window open": "Dieses Fenster offen lassen",
      "Do not close this window until you have uploaded all your photos on your phone. Come back here when you finish!": "Schließe dieses Fenster nicht, bis du alle Fotos auf deinem Handy hochgeladen hast. Komme zurück, wenn du fertig bist!",
      "Start capturing selfies": "Selfies aufnehmen",
      "Failed to upload image": "Bild konnte nicht hochgeladen werden",
      "Scan QR code with phone camera": "QR-Code mit Handy-Kamera scannen",
      "Easily capture selfies with our phone onboarding process. Scan the QR code below with your phone to get started.": "Nimm einfach Selfies mit unserem Handy-Einrichtungsprozess auf. Scanne den QR-Code unten mit deinem Handy, um zu starten.",
      "Keep this screen open": "Diesen Bildschirm offen lassen",
      "Do not close this screen until you have uploaded all your photos on your phone.": "Schließe diesen Bildschirm nicht, bis du alle Fotos auf deinem Handy hochgeladen hast.",
      "Or use the link below": "Oder verwende den Link unten",
      "Something went wrong": "Etwas ist schiefgelaufen",
      "Uploading image from your phone": "Bild wird vom Handy hochgeladen",
      "Connection established with server": "Verbindung zum Server hergestellt",
      "Connection lost with server, trying to reconnect": "Verbindung zum Server verloren, versuche neu zu verbinden",
      "Reconnect attempt": "Verbindungsversuch",
      "Reconnection failed, please try uploading directly from your computer": "Neuverbindung fehlgeschlagen, bitte versuche direkt vom Computer hochzuladen",
      "Phone is connected": "Handy ist verbunden",
      "Phone is disconnected": "Handy ist getrennt",
      "Connection encountered error:": "Verbindungsfehler aufgetreten:",
      "Capture more selfies": "Mehr Selfies aufnehmen",
      "takenNSelfies": "Du hast {n}/{min} Aufnahmen gemacht!",
      "Click on Continue when you finish taking all your pictures.": "Klicke auf Weiter, wenn du alle Bilder aufgenommen hast.",
      "Continue": "Weiter",
      "Your photos will appear automatically on this desktop page when uploaded.": "Deine Fotos erscheinen automatisch auf dieser Desktop-Seite, wenn sie hochgeladen werden."
    }
  }
</i18n>
