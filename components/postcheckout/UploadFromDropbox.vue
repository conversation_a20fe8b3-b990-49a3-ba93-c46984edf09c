<template>
  <PostcheckoutUploadImagesButton
    :text="$t('Upload photos from Dropbox')"
    :mobile-text="$t('Upload photos from Dropbox')"
    @click="openDropboxFilePicker"
  >
    <img class="size-6" src="@/assets/img/dropbox-icon.svg" alt="">
  </PostcheckoutUploadImagesButton>
</template>

<script>
export default {
  data () {
    return {
      dropboxOptions: {
        success: (files) => {
          const blobsPromises = files.map((file) => {
            return fetch(file.link, {
              method: 'GET'
            }).then(res => res.blob())
          })
          Promise.all(blobsPromises)
            .then((blobs) => {
              blobs.forEach((blob) => {
                this.$store.dispatch('onboarding/addPhoto', {
                  file: blob
                }).catch((error) => {
                  this.$toast.warning(error.message || this.$t('Failed to upload image'))
                })
              })
            })
            .catch((error) => {
              this.$toast.error(this.$t('Error fetching files:'), error)
            })
        },
        linkType: 'direct',
        extensions: ['.jpg', '.png', '.jpeg'],
        multiselect: true
      }
    }
  },
  head () {
    return {
      script: [
        {
          src: 'https://www.dropbox.com/static/api/2/dropins.js',
          'data-app-key': '2wszg6s6puctlkz',
          hid: 'dropbox-picker',
          id: 'dropboxjs'
        }
      ]
    }
  },
  methods: {
    openDropboxFilePicker () {
      if (!window.Dropbox) {
        this.$toast.error(this.$t('Dropbox is not available at the moment. Please try again later.'))
      } else {
        window.Dropbox.choose(this.dropboxOptions)
      }
    }
  }
}
</script>

<i18n>
  {
    "en": {
      "Upload photos from Dropbox": "Upload photos from Dropbox",
      "Failed to upload image": "Failed to upload image",
      "Error fetching files:": "Error fetching files:",
      "Dropbox is not available at the moment. Please try again later.": "Dropbox is not available at the moment. Please try again later."
    },
    "es": {
      "Upload photos from Dropbox": "Subir fotos desde Dropbox",
      "Failed to upload image": "Error al subir la imagen",
      "Error fetching files:": "Error al obtener los archivos:",
      "Dropbox is not available at the moment. Please try again later.": "Dropbox no está disponible en este momento. Por favor, inténtelo de nuevo más tarde."
    },
    "de": {
      "Upload photos from Dropbox": "Fotos von Dropbox hochladen",
      "Failed to upload image": "Bild konnte nicht hochgeladen werden",
      "Error fetching files:": "Fehler beim Abrufen der Dateien:",
      "Dropbox is not available at the moment. Please try again later.": "Dropbox ist derzeit nicht verfügbar. Bitte versuche es später erneut."
    }
  }
</i18n>
