<template>
  <section class="relative pt-8 pb-32 sm:pt-12 md:pb-12">
    <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8">
      <PostcheckoutStepper :step="2" classes="md:justify-center" />

      <div class="mx-auto mt-4 text-left sm:mt-6 md:max-w-xl md:text-center">
        <h1 class="text-xl font-bold tracking-tight sm:text-2xl lg:text-3xl text-primary-500">
          That's enough photos!
        </h1>
        <p class="mt-2 text-base font-medium text-gray-500 md:max-w-md md:mx-auto">
          You're ready to move on to the next step.
        </p>
      </div>

      <div class="px-4 py-5 mx-auto mt-5 border border-green-200 rounded-lg md:max-w-lg bg-green-50">
        <div class="flex items-center justify-between">
          <p class="text-sm font-medium text-green-800">
            Good photos
          </p>

          <span
            class="relative hidden md:block px-2 pt-0.5 pb-1 overflow-hidden text-xs font-medium text-white bg-green-700 rounded-full"
          >
            <span class="absolute inset-y-0 left-0 bg-green-500 w-[100%]" />
            <span class="relative">
              {{ goodPhotos.length }}/{{ Math.max(goodPhotos.length, minimumPhotos) }}
            </span>
          </span>
        </div>

        <hr class="mt-4 border-green-200">

        <div class="grid grid-cols-5 md:grid-cols-6 gap-2.5 mt-4 xl:pr-20">
          <PostcheckoutUploadedPhoto v-for="photo in goodPhotos" :key="photo.md5 + '_' + photo.status" :photo="photo" />
        </div>
      </div>

      <div class="hidden mx-auto mt-5 md:max-w-lg md:block">
        <ButtonPrimary class="w-full" data-testid="continue-to-next-step" @click="goToNextStep">
          Continue to next step
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-5 -mb-0.5">
            <path
              fill-rule="evenodd"
              d="M3 10a.75.75 0 0 1 .75-.75h10.638L10.23 5.29a.75.75 0 1 1 1.04-1.08l5.5 5.25a.75.75 0 0 1 0 1.08l-5.5 5.25a.75.75 0 1 1-1.04-1.08l4.158-3.96H3.75A.75.75 0 0 1 3 10Z"
              clip-rule="evenodd"
            />
          </svg>
        </ButtonPrimary>
      </div>
    </div>

    <div class="fixed inset-x-0 bottom-0 py-4 bg-white border-t border-gray-100 shadow-sm md:hidden">
      <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8">
        <ButtonPrimary class="w-full" @click="goToNextStep">
          Continue to next step
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-5 -mb-0.5">
            <path
              fill-rule="evenodd"
              d="M3 10a.75.75 0 0 1 .75-.75h10.638L10.23 5.29a.75.75 0 1 1 1.04-1.08l5.5 5.25a.75.75 0 0 1 0 1.08l-5.5 5.25a.75.75 0 1 1-1.04-1.08l4.158-3.96H3.75A.75.75 0 0 1 3 10Z"
              clip-rule="evenodd"
            />
          </svg>
        </ButtonPrimary>
      </div>
    </div>
  </section>
</template>

<script>
import PostcheckoutMixin from '../../mixins/PostcheckoutMixin'

export default {
  mixins: [PostcheckoutMixin],
  methods: {
    goToNextStep () {
      this.$router.push({ path: this.localePath('/app/upload/personal-info') })
    }
  }
}
</script>

<i18n>
  {
    "en": {
      "That's enough photos!": "That's enough photos!",
      "You're ready to move on to the next step.": "You're ready to move on to the next step.",
      "Good photos": "Good photos",
      "Continue to next step": "Continue to next step"
    },
    "es": {
      "That's enough photos!": "¡Suficientes fotos!",
      "You're ready to move on to the next step.": "Estás listo para ir al siguiente paso.",
      "Good photos": "Buenas fotos",
      "Continue to next step": "Continuar al siguiente paso"
    },
    "de": {
      "That's enough photos!": "Das sind genug Fotos!",
      "You're ready to move on to the next step.": "Du kannst zum nächsten Schritt übergehen.",
      "Good photos": "Gute Fotos",
      "Continue to next step": "Weiter zum nächsten Schritt"
    }
  }
</i18n>
