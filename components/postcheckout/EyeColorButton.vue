<template>
  <button type="button" class="relative block" @click="updateValue">
    <div
      class="relative px-2.5 pt-1.5 pb-2 cursor-pointer rounded-md ring-1 transition"
      :class="{
        'text-primary-500 ring-gray-200 bg-gray-50': active,
        'text-gray-500 ring-transparent bg-white': !active
      }"
    >
      <div class="flex items-center gap-1.5">
        <div class="rounded-full size-5 -mb-0.5 text-sm font-medium" :style="{ backgroundColor }" />
        <span>{{ label }}</span>
      </div>
    </div>
  </button>
</template>

<script>
export default {
  props: {
    backgroundColor: {
      type: String,
      required: true
    },
    label: {
      type: String,
      required: true
    },
    value: {
      type: String,
      required: true
    },
    identifier: {
      type: String,
      required: true
    }
  },
  computed: {
    active () {
      return this.value === this.identifier
    }
  },
  methods: {
    updateValue () {
      this.$emit('input', this.identifier)
    }
  }
}
</script>
