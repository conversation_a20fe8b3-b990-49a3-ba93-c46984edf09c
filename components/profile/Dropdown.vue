<template>
  <div class="relative z-40">
    <template v-if="isLoggedIn">
      <div>
        <button type="button" class="flex h-8 w-8 max-w-xs items-center justify-center rounded-full bg-white/20 text-sm hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2" aria-expanded="false" aria-haspopup="true" @click="showDropdown = !showDropdown">
          <span class="sr-only">Open user menu</span>
          <img v-if="$store.state.user.avatar" :src="$store.state.user.avatar" class="h-8 w-8 rounded-full" alt="Avatar">
          <IconSolidUser v-else class="h-4 w-4 text-white/50" />
        </button>
      </div>
      <div
        v-if="showDropdown"
        class="absolute right-0 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
        role="menu"
        aria-orientation="vertical"
        aria-labelledby="user-menu-button"
        tabindex="-1"
      >
        <!-- Active: "bg-gray-100", Not Active: "" -->
        <nuxt-link
          v-for="(item, index) in menuItems"
          id="user-menu-item-0"
          :key="index"
          :to="item.url"
          class="block px-4 py-2 text-sm text-gray-700"
          role="menuitem"
          tabindex="-1"
        >
          {{ item.title }}
        </nuxt-link>
        <button class="block px-4 py-2 text-sm text-gray-700" @click="logout">
          Sign out
        </button>
      </div>
    </template>
    <template v-else>
      <nuxt-link :to="`/auth/login?redirect=${$route.path}`">
        <ButtonDark size="sm">
          Login
        </ButtonDark>
      </nuxt-link>
    </template>
  </div>
</template>

<script>
export default {
  data () {
    return {
      showDropdown: false,
      menuItems: [
        { title: 'Profile', url: '/profile' },
        { title: 'Invoices', url: '/profile/invoices' }
      ]
    }
  }
}
</script>

<style></style>
