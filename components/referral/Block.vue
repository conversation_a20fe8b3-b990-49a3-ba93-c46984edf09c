<template>
  <Card :inner-class="`flex justify-between gap-4 flex-col ${orientation === 'vertical' ? '' : 'lg:items-center lg:items-start lg:flex-row'}`">
    <div class="space-y-2">
      <div class="flex items-center justify-start gap-2">
        <IconGift class="w-5 h-5 text-paragraph" />
        <p class="text-lg font-bold text-primary-500">
          Treat a friend to $9 off AI headshots
        </p>
      </div>
      <p class="text-sm text-paragraph">
        Refer a friend and they'll save $9 on their order. Plus, you'll get 20 bonus headshots.
      </p>
    </div>
    <div class="flex-shrink-0 w-full" :class="{ 'lg:w-auto': orientation !== 'vertical' }">
      <ButtonGradient size="sm" class="w-full" :class="{ 'lg:w-auto': orientation !== 'vertical' }" @click="handleClick">
        Get Extra Headshots
      </ButtonGradient>
    </div>
  </Card>
</template>

<script>
export default {
  props: {
    modelId: {
      type: String,
      required: true
    },
    orientation: {
      type: String,
      default: 'horizontal'
    }
  },
  methods: {
    handleClick () {
      this.$router.push(`/app/results/${this.modelId}?tab=referral`)
      this.$emit('click')
    }
  }
}
</script>
