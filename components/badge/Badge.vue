<template>
  <span class="badge" :class="colorClass"><slot /></span>
</template>

<script>
export default {
  props: {
    color: {
      type: String,
      default: 'teal'
    }
  },
  computed: {
    colorClass () {
      // Return the specific class based on the color prop
      return `badge--${this.color}`
    }
  }
}
</script>

<style scoped>
.badge {
  display: inline-flex;
  align-items: center;
  border-radius: 9999px; /* rounded-full */
  padding-left: 0.625rem; /* px-2.5 */
  padding-right: 0.625rem; /* px-2.5 */
  padding-top: 0.125rem; /* py-0.5 */
  padding-bottom: 0.125rem; /* py-0.5 */
  font-size: 0.75rem; /* text-xs */
  line-height: 1rem; /* text-xs */
  flex-shrink: 0; /* flex-shrink-0 */
}

/* Default/Teal */
.badge--teal {
  background-color: #ccfbf1; /* bg-teal-100 */
  color: #115e59; /* text-teal-800 */
}

.badge--green {
  background-color: #dcfce7; /* bg-green-100 */
  color: #166534; /* text-green-800 */
}

.badge--red {
  background-color: #fee2e2; /* bg-red-100 */
  color: #991b1b; /* text-red-800 */
}

.badge--gray {
  background-color: #f3f4f6; /* bg-gray-100 */
  color: #374151; /* text-gray-800 */
}

/* Add other color variations here if needed, following the pattern: */
/*
.badge--{color-name} {
  background-color: ...;
  color: ...;
}
*/
</style>
