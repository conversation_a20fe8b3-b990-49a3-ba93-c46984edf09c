<template>
  <section class="py-4 sm:py-12">
    <div class="max-w-screen-xl px-2 mx-auto sm:px-6 lg:px-8 space-y-8">
      <div class="max-w-2xl mx-auto text-left md:text-center space-y-1">
        <!-- <div class="flex items-center justify-start gap-x-3 md:justify-center pb-6">
          <img class="h-4 w-auto" src="@/assets/img/trustpilot-stars-4.5.svg" alt="">
          <img class="h-4 w-auto" src="@/assets/img/landing-page/logo-trustpilot.png" alt="">
        </div> -->
        <PrecheckoutStepCounter :active="3" :total="4" />
        <h1 class="text-xl font-bold tracking-tight sm:text-2xl lg:text-3xl text-primary-500 pt-4">
          {{ $t('Select a package') }}
        </h1>
        <p class="mt-2 text-sm sm:text-base font-medium text-gray-600 md:max-w-lg md:mx-auto">
          {{ $t('Pay once, no subscriptions or hidden fees. We offer no trial due to high costs, but we will refund you if you\'re unsatisfied') }}
        </p>
      </div>
      <Invite v-if="hasInvites && hasInvites?.length > 0 && showInviteWarning" class="max-w-xl" />
      <div class=" sm:bg-white sm:border sm:border-gray-200 rounded-lg  sm:p-8 max-w-xl mx-auto flex-col space-y-8">
        <div class="flex flex-col space-y-2">
          <div class="border border-gray-200 rounded-md bg-white">
            <template v-for="price in prices">
              <div v-if="price?.visible" :key="price.packageId" class="grid md:flex w-full grid-cols-5 md:space-x-4 items-center border-b border-gray-200 p-4 gap-4 last:border-none">
                <div class="col-span-5 mb-2 flex flex-col md:col-span-4 md:mb-0 pr-2">
                  <div class="flex items-center justify-start space-x-2">
                    <h2 class="text-base text-gray-800 flex items-center space-x-1">
                      <span class="font-bold text-black">{{ getLocalizedPrice(price.id, true, 0, false) }}</span>
                      <span class="text-gray-700">∙</span>
                      <span class="text-sm font-medium">{{ price.title }}</span>
                    </h2>
                    <div v-if="price.tag === 'popular'" class="gradient-bg rounded-lg px-1.5 py-0.5 text-[9px] font-bold uppercase text-white">
                      <!-- X% PICK THIS PLAN -->
                      {{ $t('percentPickThisPlan', { percent: 78 }) }}
                    </div>
                    <!-- <div v-if="price.tag === 'value'" class="rounded-lg bg-teal-500 px-1.5 py-0.5 text-[9px] font-bold uppercase text-white">
                        BEST VALUE
                      </div> -->
                  </div>

                  <p class="text-sm text-gray-500 tracking-[-0.4px]">
                    <span v-if="$i18n.locale === 'es'" class="font-medium">
                      Consigue <strike v-if="price.meta.regularStyles" class="text-red-500"> {{ price.meta.regularStyles * photoPerStyle }}</strike> <strong>{{ (price.meta.styles + price.meta.additional) * photoPerStyle }}</strong>
                      fotos con <strike v-if="price.meta.regularStyles" class="text-red-500"> {{ price.meta.regularStyles }}</strike>
                      <template v-if="(price.meta.styles + price.meta.additional) === 1">fondo y ropa</template>
                      <template v-else>fondos y ropas</template>.
                      <strong>{{ (price.meta.styles + price.meta.additional) }}</strong> únicos.
                      <span>Hecho en <strong>{{ (price?.meta?.turnAroundTime ?? '2 hours').replace('hour', 'hora') }}</strong></span>
                    </span>
                    <span v-else class="font-medium">
                      Get <strike v-if="price.meta.regularStyles" class="text-red-500"> {{ price.meta.regularStyles * photoPerStyle }}</strike> <strong>{{ (price.meta.styles + price.meta.additional) * photoPerStyle }}</strong>
                      headshots with <strike v-if="price.meta.regularStyles" class="text-red-500"> {{ price.meta.regularStyles }}</strike>
                      <strong>{{ (price.meta.styles + price.meta.additional) }}</strong> unique
                      <template v-if="(price.meta.styles + price.meta.additional) === 1">background and outfit</template>
                      <template v-else>backgrounds and outfits</template>
                      + <strong>{{ price?.meta?.regenerationCredits }}</strong> edit credits.
                      <span>Done in <strong>{{ price?.meta?.turnAroundTime ?? '2 hours' }}.</strong></span>
                    </span>
                  </p>
                </div>
                <div class="col-span-5 flex w-full justify-start md:col-span-1 md:w-auto md:justify-end">
                  <nuxt-link :to="localePath(`/checkout/?priceId=${price.id}${couponQueryString}`)" class="block w-full md:w-auto">
                    <ButtonPrimary v-if="price.tag === 'popular'" size="sm" class="w-full md:w-auto">
                      <span class="font-bold">{{ $t('Select') }}</span>
                      <IconSmallArrow class="w-4 h-4 text-white ml-0.5" />
                    </ButtonPrimary>
                    <ButtonWhite v-else size="sm" class="w-full md:w-auto">
                      <span>{{ $t('Select') }}</span>
                      <IconSmallArrow class="w-4 h-4 text-black ml-0.5" />
                    </ButtonWhite>
                  </nuxt-link>
                </div>
              </div>
            </template>
          </div>
          <div class="flex items-center justify-start space-x-2">
            <img src="@/assets/img/5-star-gray.png" class="w-[60px]">
            <span class="text-paragraph text-[13px] tracking-[-0.4%]">{{ $t('usedByNHappyCustomers', { n: $store.state.stats.users }) }} <a href="/reviews" target="_blank" class="underline">{{ $t('Check reviews') }}</a></span>
          </div>
        </div>
      </div>
      <!-- <div v-if="reviewsWithContent.length" class="hidden md:flex w-auto">
        <div id="review" class="transition duration-300 relative rounded-md overflow-hidden aspect-w-5 aspect-h-4 w-[300px] mx-auto">
          <div class="bg-black/50 backdrop-blur-md text-white uppercase rounded-full font-bold text-[9px] py-[2px] px-[12px] absolute top-2 left-2">
            100% {{ $t('AI generated') }}
          </div>
          <ImageDns :src="reviewsWithContent[selectedReview].image" class="w-full" @load="increaseOpacity()" />
          <div class="absolute bg-gradient-to-t h-1/2 flex justify-end flex-col from-black/80 to-transparent text-white p-3 bottom-0 left-0 w-full text-sm  space-y-1">
            <p class="font-bold">
              {{ reviewsWithContent[selectedReview].title }}
            </p>
            <p class="italic text-white/80 text-xs">
              "{{ reviewsWithContent[selectedReview].review.quote }}"
            </p>
          </div>
        </div>
      </div> -->
    </div>
  </section>
</template>

<script>
import PosthogMixin from '@/mixins/PosthogMixin'
export default {
  mixins: [PosthogMixin],
  data () {
    return {
      selectedReview: 0
    }
  },
  computed: {
    happyCustomers () {
      return Math.round(this.$store.state.stats.users.replace(/,/g, '') / 1000) + 'K'
    },
    prices () {
      const packages = this.$store.state.packages
      return packages
    },
    couponQueryString () {
      if (this.$route.query.coupon) {
        return `&coupon=${this.$route.query.coupon}`
      }

      return ''
    },
    reviewsWithContent () {
      // Create a copy of the reviews array to avoid side effects
      // const reviewsCopy = [...this.$store.state.reviews]
      // if (!reviewsCopy.length) {
      //   return []
      // }
      // // Sort review.review.quote to the front
      // return reviewsCopy.sort((a, b) => {
      //   if (a.review.quote && !b.review.quote) { return -1 }
      //   if (!a.review.quote && b.review.quote) { return 1 }
      //   return 0
      // })
      return this.$store.state.reviews.filter(review => this.isCreatedAfterAugust21(review.createdAt))
    }
  },
  mounted () {
    // Create an interval of 5000 milliseconds a  nd increment selectedReview
    // On change, change the opacity from 1 to 0 and back to 1
    if (this.reviewsWithContent.length) {
      setInterval(() => {
        this.selectedReview = (this.selectedReview + 1) % this.reviewsWithContent.length
        const $review = document.getElementById('review')
        if ($review) {
          $review.style.opacity = 0.1
        }
      }, 8000)
    }

    if (!this.$store.state.packages || this.$store.state.packages.length === 0) {
      this.$store.dispatch('getPackages')
    }

    this.$posthog.capture('$funnel:package_selection')
  },
  methods: {
    isCreatedAfterAugust21 (createdAt) {
      const date = new Date(createdAt)
      const august21 = new Date('2024-08-19')
      return date > august21
    },
    priceIsPopular (price) {
      return price.tag === 'popular'
    },
    increaseOpacity () {
      document.getElementById('review').style.opacity = 1
    }
  }
}
</script>

<i18n>
  {
    "en": {
      "Select a package": "Select a package",
      "Pay once, no subscriptions or hidden fees. We offer no trial due to high costs, but we will refund you if you're unsatisfied": "Pay once, no subscriptions or hidden fees. We offer no trial due to high costs, but we will refund you if you're unsatisfied",
      "percentPickThisPlan": "{percent}% PICK THIS PLAN",
      "Select": "Select",
      "Check reviews": "Check reviews",
      "usedByNHappyCustomers": "Used by {n}+ happy customers.",
      "AI generated": "AI generated",
      "14-day money back guarantee": "14-day money back guarantee"
    },
    "es": {
      "Select a package": "Selecciona un paquete",
      "Pay once, no subscriptions or hidden fees. We offer no trial due to high costs, but we will refund you if you're unsatisfied": "Paga una vez, sin suscripciones ni tarifas ocultas. No ofrecemos prueba debido a los altos costos, pero te reembolsaremos si no estás satisfecho",
      "percentPickThisPlan": "{percent}% ELIGE ESTE PLAN",
      "Select": "Seleccionar",
      "Check reviews": "Ver reseñas",
      "usedByNHappyCustomers": "Usado por {n}+ clientes felices.",
      "AI generated": "Generado por IA",
      "14-day money back guarantee": "Garantía de devolución de 14 días"
    },
    "de": {
      "Select a package": "Paket auswählen",
      "Pay once, no subscriptions or hidden fees. We offer no trial due to high costs, but we will refund you if you're unsatisfied": "Einmalige Zahlung, keine Abos oder versteckten Kosten. Aufgrund hoher Kosten bieten wir keine Testversion an, erstatten aber bei Unzufriedenheit",
      "percentPickThisPlan": "{percent}% WÄHLEN DIESES PAKET",
      "Select": "Auswählen",
      "Check reviews": "Bewertungen ansehen",
      "usedByNHappyCustomers": "Bereits von über {n} zufriedenen Kunden genutzt",
      "AI generated": "KI-generiert",
      "14-day money back guarantee": "14-Tage Geld-zurück-Garantie"
    }
  }
</i18n>
