<template>
  <div>
    <InviteWarningBar v-if="hasInvites && showInviteWarning" />
    <section class="py-4 sm:py-12">
      <div class="max-w-screen-xl px-2 mx-auto sm:px-6 lg:px-8">
        <div class="max-w-2xl mx-auto text-left md:text-center space-y-2">
          <PrecheckoutStepCounter :active="1" :total="4" />
          <h1 class="text-xl font-bold tracking-tight sm:text-2xl lg:text-3xl text-primary-500">
            {{ $t('How it works') }}
          </h1>
          <p class="mt-2 text-sm sm:text-base font-medium text-gray-600 md:max-w-lg md:mx-auto">
            {{ $t('Creating your professional headshots is easy. All you need to do is upload some photos of yourself. We\'ll take care of the rest!') }}
          </p>
        </div>

        <div class="p-6 space-y-3 bg-white border border-gray-200 rounded-lg sm:space-y-4 sm:p-8 max-w-lg mt-8">
          <ButtonPrimary class="mb-4 w-full" @click="$emit('next')">
            {{ $t('Click here to start!') }}
          </ButtonPrimary>
          <img :src="require(`@/assets/img/expectations-${isForOrganization ? 'organization' : 'individual'}.png`)">
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  computed: {
    isTeamLead () {
      return this.$store?.state?.user?.role === 'TeamLead'
    },
    isTeamMember () {
      return this.$store?.state?.user?.role === 'TeamMember'
    },
    organization () {
      return this.$store?.state?.organization?.organization?.name || 'your organization'
    },
    hasInvites () {
      return this.$store.state?.user?.invites || null
    },
    showInviteWarning () {
      return this.$store.state?.showInviteWarning || null
    },
    isForOrganization () {
      if (this.$store.state.user.role === 'TeamLead' || this.$store.state.user.role === 'TeamMember') {
        return true
      } else {
        return false
      }
    }
  },
  mounted () {
    this.$posthog.capture('$funnel:intro')
  }
}
</script>

<i18n>
  {
    "en": {
      "How it works": "How it works",
      "Creating your professional headshots is easy. All you need to do is upload some photos of yourself. We'll take care of the rest!": "Creating your professional headshots is easy. All you need to do is upload some photos of yourself. We'll take care of the rest!",
      "Click here to start!": "Click here to start!"
    },
    "es": {
      "How it works": "Cómo funciona",
      "Creating your professional headshots is easy. All you need to do is upload some photos of yourself. We'll take care of the rest!": "Crear tus fotos profesionales es fácil. Todo lo que necesitas hacer es subir algunas fotos tuyas. ¡Nosotros nos encargaremos del resto!",
      "Click here to start!": "¡Haz click aquí para empezar!"
    },
    "de": {
      "How it works": "So funktioniert's",
      "Creating your professional headshots is easy. All you need to do is upload some photos of yourself. We'll take care of the rest!": "Professionelle Bewerbungsfotos erstellen ist ganz einfach. Du musst nur ein paar Fotos von dir hochladen. Um den Rest kümmern wir uns!",
      "Click here to start!": "Hier klicken und loslegen!"
    }
  }
</i18n>
