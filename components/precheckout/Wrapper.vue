<template>
  <div class="flex flex-col min-h-screen bg-gray-50">
    <!-- <MarketingSalesBar /> -->
    <InviteWarningBar v-if="hasInvites && hasInvites?.length > 0 && showInviteWarning" />
    <header class="py-4 bg-white border-b border-gray-100 w-full">
      <div class="max-w-screen-xl grid grid-cols-3 items-center justify-between px-4 mx-auto sm:px-6 lg:px-8">
        <div class="items-center justify-start flex">
          <nuxt-link v-if="backUrl" :to="backUrl" class="inline-flex items-center gap-1 text-sm font-medium text-gray-700">
            <svg
              aria-hidden="true"
              class="text-gray-500 size-5"
              viewBox="0 0 18 18"
              fill="none"
              stroke="currentColor"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M10.5 4.5L6 9L10.5 13.5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            {{ $t('Back') }}
          </nuxt-link>
        </div>
        <div class="w-full items-center justify-center flex">
          <nuxt-link to="/" class="block mx-auto">
            <Logo class="w-auto h-6 text-black" />
          </nuxt-link>
        </div>
        <div class="flex justify-end items-center space-x-2">
          <HeaderLanguageSelector />
        </div>
      </div>
    </header>
    <div
      class="flex w-full flex-col items-center justify-start p-4 md:p-8 antialiased flex-1 "
      :class="{
        'md:items-center md:justify-center': alignment === 'center',
        'md:items-center md:justify-start': alignment === 'top',
      }"
    >
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    backUrl: {
      type: String,
      default: null
    },
    alignment: {
      type: String,
      default: 'center'
    }
  },
  computed: {
    availableLocales () {
      return this.$i18n.locales
    }
  }
}
</script>
