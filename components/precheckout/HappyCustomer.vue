<template>
  <div class="relative overflow-hidden rounded-md">
    <img class="object-cover w-full h-full" :src="resolvedSrc" alt="">

    <div class="absolute top-2 left-2">
      <span
        class="px-1.5 block py-[3px] rounded-md text-[9px] text-white font-medium tracking-tight bg-gray-900/50 uppercase"
      >
        {{ $t('AI generated') }}
      </span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    src: {
      type: String,
      required: true
    }
  },
  computed: {
    resolvedSrc () {
      return require(`@/assets/img/happy-customers/${this.src}`)
    }
  }
}
</script>

<i18n>
  {
    "en": {
      "AI generated": "AI generated"
    },
    "es": {
      "AI generated": "Generado por IA"
    },
    "de": {
      "AI generated": "KI generiert"
    }
  }
</i18n>
