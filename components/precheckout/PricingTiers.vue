<template>
  <div class="mx-auto  grid max-w-5xl grid-cols-1 items-start gap-4  md:grid-cols-3">
    <template v-for="price in prices">
      <div
        v-if="price?.meta?.visible"
        :key="`price-${price.id}`"
        class="relative bg-white shadow-[0_0px_75px_0px_rgba(0,0,0,0.07)] rounded-lg"
        :class="{'border border-[#21B8BA] bg-white shadow-lg': price?.meta?.tag === 'popular', '  border border-primary-500/15': price?.meta?.tag !== 'popular'}"
      >
        <div v-if="price?.meta?.tag === 'popular'" class="absolute inset-x-0 top-0 translate-y-px transform">
          <div class="flex -translate-y-1/2 transform justify-center">
            <span class="rounded-full bg-[#21B8BA] px-3 py-1 text-xs font-extrabold uppercase tracking-wide text-white">
              {{ $t(recommended) }}
            </span>
          </div>
        </div>
        <div class="px-5 pb-4 pt-5">
          <h2 class="text-sm font-medium leading-6 tracking-[-0.2px] text-gray-600">
            {{ price.title }}
          </h2>

          <p class="mt-4 text-[40px] font-bold leading-6 tracking-[-0.2px] text-primary-500">
            <!-- {{ getLocalizedPrice(price.id, true, 0, false) }} -->
            <span v-if="getFormattedPriceParts(getLocalizedPrice(price.id, true, 0, false)).smallTextPrefix" class="text-2xl font-normal">
              {{ getFormattedPriceParts(getLocalizedPrice(price.id, true, 0, false)).smallTextPrefix }}
            </span>
            <span v-if="getFormattedPriceParts(getLocalizedPrice(price.id, true, 0, false)).largePart">
              {{ getFormattedPriceParts(getLocalizedPrice(price.id, true, 0, false)).largePart }}
            </span>
          </p>
          <p class="mt-6 text-sm font-medium leading-5 tracking-[-0.3px] text-paragraph">
            {{ $t('Get') }} {{ (price.meta.styles + price.meta.additional) * photoPerStyle }} {{ $t('headshots') }} {{ $t('with') }} {{ price.meta.styles + price.meta.additional }} {{ $t('unique') }} {{ $t('backdrops') }} {{ $t('and') }} {{ $t('outfits') }}.
          </p>
        </div>

        <div class="space-y-4 px-5 pb-5 pt-4">
          <ul class="space-y-2.5 text-sm font-medium leading-4 text-paragraph">
            <li class="flex items-center gap-1">
              <component :is="getIconForPrice(price, 1)" v-if="getIconForPrice(price, 1)" class="size-5 shrink-0" :class="getIconColor(getIconForPrice(price, 1), price)" />
              {{ (price.meta.styles + price.meta.additional) * photoPerStyle }} {{ $t('headshots') }}
            </li>

            <li class="flex items-center gap-1">
              <component :is="getIconForPrice(price, 2)" v-if="getIconForPrice(price, 2)" class="size-5 shrink-0" :class="getIconColor(getIconForPrice(price, 2), price)" />
              {{ (price.meta.styles + price.meta.additional) }} {{ $t('backdrop') }} {{ $t('and') }} {{ $t('outfit') }} {{ $t('combos') }} <Tooltip info="Select from a wide range of backdrop options as location for your headshot photo shoot and a huge wardrobe of clothing options to wear in combination with your backdrop. You will get 10 photos for every outfit and backdrop combination." />
            </li>

            <li class="flex items-center gap-1">
              <component :is="getIconForPrice(price, 3)" v-if="getIconForPrice(price, 3)" class="size-5 shrink-0" :class="getIconColor(getIconForPrice(price, 3), price)" />
              {{ $t('Hundreds of styles to choose from') }}
            </li>

            <li class="flex items-center gap-1">
              <component :is="getIconForPrice(price, 0)" v-if="getIconForPrice(price, 0)" class="size-5 shrink-0" :class="getIconColor(getIconForPrice(price, 0), price)" />
              {{ price?.meta?.turnAroundTime ?? '2 hours' }} {{ $t('turn-around time') }}
            </li>
            <li v-if="price?.meta?.regenerationCredits > 0" class="flex items-center gap-1">
              <component :is="getIconForPrice(price, 4)" v-if="getIconForPrice(price, 4)" class="size-5 shrink-0" :class="getIconColor(getIconForPrice(price, 4), price)" />
              {{ price?.meta?.regenerationCredits }} {{ $t('edit credits') }} <Tooltip info="You can use edit credits to customize every aspect of your headshots, creating the exact look you want." />
            </li>
            <li class="flex items-center gap-1">
              <component :is="getIconForPrice(price, 6)" v-if="getIconForPrice(price, 6)" class="size-5 shrink-0" :class="getIconColor(getIconForPrice(price, 6), price)" />
              <span v-if="price?.meta?.resolution === '4k'">
                {{ $t('Print ready 4K resolution') }}
              </span>
              <span v-else>
                {{ $t('Premium resolution') }}
              </span>
              <Tooltip v-if="price?.meta?.resolution === '4k'" info="Your final results will be at least 4096px, ultra-sharp and ready for print." />
              <Tooltip v-else info="Your final results will be at least 2048px, high quality and perfect for social media." />
            </li>
            <li v-if="price?.meta?.portraitSizesPicker" class="flex items-center gap-1">
              <component :is="getIconForPrice(price, 7)" v-if="getIconForPrice(price, 7)" class="size-5 shrink-0" :class="getIconColor(getIconForPrice(price, 7), price)" />
              <template v-if="price?.meta?.portraitSizesPicker">
                {{ $t('Portrait or landscape format') }}
              </template>
              <template v-else>
                {{ $t('Fixed format') }}
              </template>
              <Tooltip info="You can choose between portrait (4:5) and landscape (3:2) format for your headshots." />
            </li>
          </ul>
          <div>
            <nuxt-link :to="localePath(`/checkout/?productId=${price.product}&priceId=${price.id}`)" class="block w-full md:w-auto">
              <button
                type="button"
                class="flex w-full items-center justify-center rounded-lg px-4 py-2 text-sm leading-6 text-primary-500 shadow-sm ring-1 ring-gray-200 transition-all duration-150 lg:h-10 whitespace-normal break-words text-center"
                :class="{'bg-[#ff6600] text-white font-bold hover:bg-[#ff6600]/80': price?.meta?.tag === 'popular', ' font-normal bg-white hover:bg-gray-50  text-primary-500': price?.meta?.tag !== 'popular'}"
              >
                {{ $t('Get') }} {{ (price.meta.styles + price.meta.additional) * photoPerStyle }} {{ $t('headshots') }} {{ $t('in') }} {{ price?.meta?.turnAroundTime ?? '2 hours' }}
              </button>
            </nuxt-link>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>

export default {

  props: {
    recommended: {
      type: String,
      required: false,
      default: '100% Money Back Guarantee'
    }
  },
  computed: {

    icons () {
      return {
        tiny: ['IconCheck', 'IconCheck', 'IconCheck', 'IconCheck', 'IconCheck', 'IconCheck', 'IconCheck', 'IconMiniMinus'],
        small: ['IconCheck', 'IconCheck', 'IconCheck', 'IconCheck', 'IconCheck', 'IconCheck', 'IconCheck', 'IconMiniMinus'],
        medium: ['IconCheck', 'IconCheck', 'IconCheck', 'IconCheck', 'IconCheck', 'IconCheck', 'IconCheck', 'IconMiniMinus'],
        large: ['IconCheck', 'IconCheck', 'IconCheck', 'IconCheck', 'IconCheck', 'IconCheck', 'IconPlus', 'IconPlus']
      }
    },
    prices () {
      const packages = this.$store?.state?.packages || []
      return packages
    }
  },
  mounted () {
    if (!this.$store.state.packages || !this.$store.state.packages?.medium) {
      this.$store.dispatch('getPackages')
    }
  },
  methods: {
    getIconColor (icon, price) {
      if (price?.id === 'large' || price?.id === 'small') {
        return 'text-paragraph'
      }
      if (icon === 'IconMiniMinus') {
        return 'text-paragraph'
      }
      if (icon === 'IconCheck') {
        return 'text-green-500'
      }
      if (icon === 'IconMiniXMark') {
        return 'text-red-500'
      }
      return 'text-paragraph'
    },
    getIconForPrice (price, index) {
      return this.icons[price?.id]?.[index]
    }
  }
}
</script>

<i18n>
  {
    "es": {
      "Get 40 headshots with 4 unique backdrops and outfits.": "Obtén 40 fotos con 4 fondos y atuendos únicos.",
      "hours turnaround time": "horas para entrega",
      "headshots": "fotos",
      "unique styles": "estilos únicos",
      "backdrops": "fondos",
      "outfits": "atuendos",
      "Choice of": "Escoge de",
      "edit credits": "créditos de edición",
      "Get 40 headshots in 3 hours": "Obtén 40 fotos en 3 horas",
      "100% Money Back Guarantee": "Garantía de devolución del 100%",
      "Get 100 headshots with 10 unique backdrops and outfits.": "Obtén 100 fotos con 10 fondos y atuendos únicos.",
      "unique clothing options": "estilos de ropa únicos",
      "Get 100 headshots in 2 hours!": "¡Obtén 100 fotos en 2 horas!",
      "Get 200 headshots with 20 unique backdrops and outfits.": "Obtén 200 fotos con 20 fondos y atuendos únicos.",
      "hour turnaround time": "hora para entrega",
      "Get 200 headshots in 1 hour": "Obtén 200 fotos en 1 hora",
      "Pick from 80+ outfits and backdrops": "Elige de 80+ atuendos y fondos",
      "Choose": "Elige",
      "turn-around time": "tiempo de entrega",
      "Pre-selected outfits": "Atuendos preseleccionados",
      "Pre-selected backdrops": "Fondos preseleccionados",
      "No edit credits": "Sin créditos de edición",
      "Pre-selected combinations": "Combinaciones preseleccionadas",
      "with": "con",
      "unique": "único"
    },
    "de": {
      "headshots": "Headshots",
      "backdrop": "Hintergrund",
      "outfit": "Outfit",
      "and": "und",
      "Hundreds of styles to choose from": "Hunderte Stile zur Auswahl",
      "edit credits": "Bearbeitungsguthaben",
      "turn-around time": "Bearbeitungszeit",
      "Get": "Erhalte",
      "hours turnaround time": "Stunden Bearbeitungszeit",
      "hour turnaround time": "Stunde Bearbeitungszeit",
      "in": "in",
      "combos": "Kombinationen",
      "with": "mit",
      "unique": "einzigartige",
      "Get 40 headshots with 4 unique backdrops and outfits.": "Erhalte 40 Headshots mit 4 einzigartigen Hintergründen und Outfits.",
      "unique styles": "einzigartige Styles",
      "backdrops": "Hintergründe",
      "outfits": "Outfits",
      "Choice of": "Auswahl von",
      "Get 40 headshots in 3 hours": "Erhalte 40 Headshots in 3 Stunden",
      "100% Money Back Guarantee": "100 % Geld-zurück-Garantie",
      "Get 100 headshots with 10 unique backdrops and outfits.": "Erhalte 100 Headshots mit 10 einzigartigen Hintergründen und Outfits.",
      "unique clothing options": "einzigartige Kleideroptionen",
      "Get 100 headshots in 2 hours!": "Erhalte 100 Headshots in 2 Stunden!",
      "Get 200 headshots with 20 unique backdrops and outfits.": "Erhalte 200 Headshots mit 20 einzigartigen Hintergründen und Outfits.",
      "Get 200 headshots in 1 hour": "Erhalte 200 Headshots in 1 Stunde",
      "Pick from 80+ outfits and backdrops": "Wähle aus über 80 Outfits und Hintergründen",
      "Choose": "Wähle",
      "Pre-selected outfits": "Vorausgewählte Outfits",
      "Pre-selected backdrops": "Vorausgewählte Hintergründe",
      "No edit credits": "Keine Bearbeitungsguthaben",
      "Pre-selected combinations": "Vorausgewählte Kombinationen"
    }
  }
</i18n>
