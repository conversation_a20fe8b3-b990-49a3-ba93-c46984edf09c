<template>
  <div>
    <InviteWarningBar v-if="hasInvites && showInviteWarning" />
    <section class="py-4 sm:py-12">
      <div class="max-w-screen-xl px-2 mx-auto sm:px-6 lg:px-8">
        <div class="max-w-2xl mx-auto text-left md:text-center space-y-2">
          <PrecheckoutStepCounter :active="2" :total="4" />
          <h1 class="text-xl font-bold tracking-tight sm:text-2xl lg:text-3xl text-primary-500">
            {{ $t('Who do you want to create headshots for?') }}
          </h1>
          <p class="mt-2 text-sm sm:text-base font-medium text-gray-600 md:max-w-md md:mx-auto">
            {{ $t('Start creating your own headshots, or create a team and let your team members create their own headshots.') }}
          </p>
        </div>

        <div class="grid max-w-4xl grid-cols-1 gap-6 mx-auto mt-6 sm:mt-8 lg:mt-10 md:gap-10 md:grid-cols-2">
          <div class="p-6 space-y-3 bg-white border border-gray-200 rounded-lg sm:space-y-4 sm:p-8">
            <svg
              aria-hidden="true"
              class="size-8 sm:size-12 text-primary-500/60"
              viewBox="0 0 50 50"
              fill="currentColor"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g opacity="0.3" clip-path="url(#clip0_316_4565)">
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M33.3335 28.125C39.6618 28.125 44.7918 33.255 44.7918 39.5833V42.7083C44.7918 44.4342 43.3927 45.8333 41.6668 45.8333C39.941 45.8333 38.5418 44.4342 38.5418 42.7083V39.5833C38.5418 36.7069 36.21 34.375 33.3335 34.375H16.6668C13.7904 34.375 11.4585 36.7069 11.4585 39.5833V42.7083C11.4585 44.4342 10.0594 45.8333 8.3335 45.8333C6.6076 45.8333 5.2085 44.4342 5.2085 42.7083V39.5833C5.2085 33.255 10.3386 28.125 16.6668 28.125H33.3335ZM25.0002 3.125C31.3285 3.125 36.4585 8.25506 36.4585 14.5833C36.4585 20.9117 31.3285 26.0417 25.0002 26.0417C18.6719 26.0417 13.5418 20.9117 13.5418 14.5833C13.5418 8.25506 18.6719 3.125 25.0002 3.125ZM25.0002 9.375C22.1237 9.375 19.7918 11.7069 19.7918 14.5833C19.7918 17.4598 22.1237 19.7917 25.0002 19.7917C27.8766 19.7917 30.2085 17.4598 30.2085 14.5833C30.2085 11.7069 27.8766 9.375 25.0002 9.375Z"
                />
              </g>
            </svg>

            <div>
              <div class="flex items-center gap-2.5">
                <h2 class="text-xl font-bold tracking-tight text-primary-500">
                  {{ $t('Just for myself') }}
                </h2>
                <span
                  class="hidden px-3 py-1 text-center text-xs font-extrabold tracking-wide text-white uppercase bg-teal-500 rounded-full sm:inline"
                >
                  {{ $t('Most Popular') }}
                </span>
              </div>

              <p class="mt-2.5 text-sm sm:text-base font-medium tracking-tight text-gray-500">
                {{ $t('The fastest way to get professional headshots you can use anywhere.') }}
              </p>
            </div>

            <ul class="hidden sm:block space-y-0.5 sm:space-y-2.5 text-xs sm:text-base font-normal text-gray-500 tracking-tight">
              <li class="flex items-center gap-1">
                <svg
                  aria-hidden="true"
                  class="text-gray-500 size-6 shrink-0"
                  viewBox="0 0 26 26"
                  fill="currentColor"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g clip-path="url(#clip0_316_4574)">
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M15.0947 6.25C15.3557 6.25 15.6074 6.34075 15.8077 6.50477L15.8902 6.57951L17.0606 7.75H19C19.7908 7.75 20.4386 8.36191 20.4959 9.13805L20.5 9.25V18.25C20.5 19.0408 19.8881 19.6886 19.1119 19.7459L19 19.75H7C6.20923 19.75 5.56138 19.1381 5.50411 18.3619L5.5 18.25V9.25C5.5 8.45923 6.11191 7.81138 6.88805 7.75411L7 7.75H8.93934L10.1098 6.57951C10.2944 6.3949 10.5366 6.28108 10.7941 6.2555L10.9053 6.25H15.0947ZM14.9394 7.75H11.0607L9.89016 8.9205C9.70556 9.1051 9.4634 9.21892 9.20586 9.24449L9.09467 9.25H7V18.25H19V9.25H16.9053C16.6443 9.25 16.3926 9.15926 16.1923 8.99523L16.1098 8.9205L14.9394 7.75ZM13 9.625C15.071 9.625 16.75 11.3039 16.75 13.375C16.75 15.446 15.071 17.125 13 17.125C10.9289 17.125 9.25 15.446 9.25 13.375C9.25 11.3039 10.9289 9.625 13 9.625ZM13 11.125C11.7573 11.125 10.75 12.1323 10.75 13.375C10.75 14.6177 11.7573 15.625 13 15.625C14.2427 15.625 15.25 14.6177 15.25 13.375C15.25 12.1323 14.2427 11.125 13 11.125Z"
                    />
                  </g>
                </svg>
                {{ $t('No need to dress up and drive to a studio') }}
              </li>

              <li class="flex items-center gap-1">
                <svg
                  aria-hidden="true"
                  class="text-gray-500 size-6 shrink-0"
                  viewBox="0 0 26 26"
                  fill="currentColor"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g clip-path="url(#clip0_316_4578)">
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M13.9209 6.01617C13.3793 5.59487 12.6208 5.59487 12.0792 6.01617L5.79131 10.9067C5.22704 11.3456 5.53742 12.2501 6.25223 12.2501H7.00004V18.2501C7.00004 19.0785 7.67161 19.7501 8.50004 19.7501H13.0001H17.5001C18.3284 19.7501 19.0001 19.0785 19.0001 18.2501V12.2501H19.7478C20.4619 12.2501 20.7736 11.346 20.2088 10.9067L13.9209 6.01617ZM13.7501 18.2501V13.7501C13.7501 13.3358 13.4142 13.0001 13.0001 13.0001C12.5859 13.0001 12.2501 13.3358 12.2501 13.7501V18.2501H8.50004V11.5076C8.50004 11.2698 8.39049 11.0576 8.2191 10.9187L13.0001 7.2002L17.7809 10.9187C17.6096 11.0576 17.5001 11.2698 17.5001 11.5076V18.2501H13.7501Z"
                    />
                  </g>
                </svg>
                {{ $t('Get it done right from your home or office') }}
              </li>

              <li class="flex items-center gap-1">
                <svg
                  aria-hidden="true"
                  class="text-gray-500 size-6 shrink-0"
                  viewBox="0 0 26 26"
                  fill="currentColor"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g clip-path="url(#clip0_316_4586)">
                    <path
                      d="M17.8151 6.09736L18.1042 6.13941C18.5541 6.21075 19.0581 6.32984 19.3683 6.64006C19.6785 6.95029 19.7976 7.45423 19.8689 7.90409L19.911 8.19327C20.0054 8.87586 20.03 9.77029 19.869 10.7632C19.5511 12.7229 18.5078 15.0612 15.8877 16.8717C15.8746 17.0044 15.873 17.1391 15.8755 17.2745L15.8884 17.682C15.8957 17.9538 15.8908 18.2243 15.8159 18.4844C15.6732 18.9797 15.1661 19.3057 14.6916 19.5398L14.459 19.6496L14.159 19.782L13.9535 19.8651C13.4235 20.0676 12.7843 20.2086 12.3653 19.7896C12.1752 19.5995 12.0875 19.3415 12.017 19.0835L11.9828 18.9548C11.8634 18.4997 11.7269 18.0479 11.5574 17.6089C11.511 17.6653 11.4615 17.7199 11.4088 17.7726C11.0006 18.1808 10.4462 18.3864 9.89151 18.5457L9.52324 18.6466L9.15555 18.7446L8.79781 18.8336L8.4738 18.9082L8.08604 18.9909L7.84366 19.0384C7.62738 19.0787 7.3661 19.0262 7.16966 18.8297C6.97322 18.6333 6.92069 18.372 6.96099 18.1557L7.04398 17.7427L7.14048 17.3091L7.23338 16.9273L7.40219 16.2926C7.56978 15.6782 7.77322 15.0442 8.22682 14.5906L8.30151 14.5192L7.72926 14.3133L7.15417 14.1158C6.81778 14.0047 6.4633 13.8876 6.21874 13.643C5.77176 13.1961 5.96202 12.4987 6.18442 11.9504L6.30892 11.6603L6.41185 11.4344C6.64952 10.9293 6.98737 10.347 7.52393 10.1925C7.78407 10.1176 8.05454 10.1127 8.32632 10.12L8.73386 10.1329C8.86929 10.1354 9.00392 10.1337 9.13663 10.1207C10.9471 7.50049 13.2854 6.45727 15.2452 6.13937C16.238 5.97832 17.1325 6.00296 17.8151 6.09736ZM15.4853 7.62002C13.8365 7.88746 11.8336 8.77063 10.2703 11.1215C10.0809 11.4062 9.76054 11.5428 9.4339 11.5944C9.24959 11.6235 9.06365 11.6314 8.87712 11.6317L8.31681 11.6235C8.22348 11.6228 8.13027 11.6236 8.0373 11.6276C7.80937 11.9309 7.65016 12.291 7.5306 12.6489L8.65567 13.0572L9.11857 13.2321C9.96416 13.5637 10.7831 13.9648 11.4133 14.595C12.4747 15.6563 13.0142 17.0446 13.4042 18.4625C13.7459 18.3429 14.0898 18.1897 14.3807 17.9711L14.3848 17.6914L14.3764 17.1322C14.3767 16.9459 14.3847 16.7599 14.414 16.5744C14.4655 16.2478 14.6021 15.9274 14.8869 15.738C17.2377 14.1747 18.1209 12.1718 18.3884 10.523C18.5234 9.69061 18.5009 8.94702 18.4251 8.39874C18.384 8.10156 18.3269 7.86728 18.2808 7.72756C18.1411 7.68149 17.9068 7.62431 17.6096 7.58321C17.0613 7.50739 16.3178 7.48501 15.4853 7.62002ZM9.28747 15.6512C9.02509 15.9679 8.90234 16.3845 8.80399 16.7972L8.72297 17.1476C8.70957 17.205 8.69599 17.2618 8.68191 17.3175L9.20231 17.1954C9.61516 17.0971 10.032 16.9743 10.3485 16.7116C10.6414 16.4187 10.641 15.9442 10.3481 15.6512C10.0552 15.3584 9.58036 15.3584 9.28747 15.6512ZM13.5302 10.348C14.1159 9.76222 15.0657 9.76222 15.6515 10.348C16.2373 10.9338 16.2373 11.8835 15.6515 12.4693C15.0657 13.0551 14.1159 13.0551 13.5302 12.4693C12.9443 11.8835 12.9443 10.9338 13.5302 10.348Z"
                    />
                  </g>
                </svg>
                {{ $t('8x cheaper than a physical photoshoot') }}
              </li>
            </ul>

            <div>
              <ButtonPrimary class="font-bold w-full" @click="$emit('next')">
                <span class="text-sm sm:text-base font-medium">{{ $t('Create headshots for myself') }}</span>
              </ButtonPrimary>
            </div>
          </div>

          <div class="p-6 space-y-3 bg-white border border-gray-200 rounded-lg sm:space-y-4 sm:p-8">
            <svg
              aria-hidden="true"
              class="size-8 sm:size-12 text-primary-500/60"
              viewBox="0 0 50 51"
              fill="currentColor"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g opacity="0.3" clip-path="url(#clip0_316_4592)">
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M27.0832 27.5833C31.6855 27.5833 35.4165 31.3144 35.4165 35.9167V40.0833C35.4165 41.234 34.4838 42.1667 33.3332 42.1667C32.1825 42.1667 31.2498 41.234 31.2498 40.0833V35.9167C31.2498 33.6154 29.3844 31.75 27.0832 31.75H12.4998C10.1986 31.75 8.33317 33.6154 8.33317 35.9167V40.0833C8.33317 41.234 7.40042 42.1667 6.24984 42.1667C5.09925 42.1667 4.1665 41.234 4.1665 40.0833V35.9167C4.1665 31.3144 7.89746 27.5833 12.4998 27.5833H27.0832ZM39.583 27.5838C43.0348 27.5838 45.833 30.3821 45.833 33.8338V38.0004C45.833 39.151 44.9003 40.0838 43.7496 40.0838C42.5992 40.0838 41.6663 39.151 41.6663 38.0004V33.8338C41.6663 32.6833 40.7336 31.7504 39.583 31.7504H36.6334C35.9086 30.0917 34.7644 28.6581 33.3348 27.5838H39.583ZM19.7915 6.75C24.9692 6.75 29.1665 10.9473 29.1665 16.125C29.1665 21.3027 24.9692 25.5 19.7915 25.5C14.6138 25.5 10.4165 21.3027 10.4165 16.125C10.4165 10.9473 14.6138 6.75 19.7915 6.75ZM37.4998 13C40.9517 13 43.7498 15.7982 43.7498 19.25C43.7498 22.7019 40.9517 25.5 37.4998 25.5C34.048 25.5 31.2498 22.7019 31.2498 19.25C31.2498 15.7982 34.048 13 37.4998 13ZM19.7915 10.9167C16.915 10.9167 14.5831 13.2485 14.5831 16.125C14.5831 19.0015 16.915 21.3333 19.7915 21.3333C22.668 21.3333 24.9998 19.0015 24.9998 16.125C24.9998 13.2485 22.668 10.9167 19.7915 10.9167ZM37.4998 17.1667C36.3492 17.1667 35.4165 18.0994 35.4165 19.25C35.4165 20.4006 36.3492 21.3333 37.4998 21.3333C38.6505 21.3333 39.5832 20.4006 39.5832 19.25C39.5832 18.0994 38.6505 17.1667 37.4998 17.1667Z"
                />
              </g>
            </svg>

            <div>
              <h2 class="text-xl font-bold tracking-tight text-primary-500">
                {{ $t('For my team and me') }}
              </h2>

              <p class="mt-2.5 text-sm sm:text-base font-medium tracking-tight text-gray-500">
                {{ $t('Save hundreds of dollars on corporate headshots without ever having to leave the office.') }}
              </p>
            </div>

            <ul class="hidden sm:block space-y-0.5 sm:space-y-2.5 text-xs sm:text-base font-normal text-gray-500 tracking-tight">
              <li class="flex items-center gap-1">
                <svg
                  aria-hidden="true"
                  class="text-gray-500 size-6 shrink-0"
                  viewBox="0 0 26 26"
                  fill="currentColor"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g clip-path="url(#clip0_316_4598)">
                    <path
                      d="M19 7.5C19.7908 7.5 20.4386 8.11191 20.4959 8.88805L20.5 9V18C20.5 18.7908 19.8881 19.4386 19.1119 19.4959L19 19.5H7C6.20923 19.5 5.56138 18.8881 5.50411 18.1119L5.5 18V17.25H7V18H19V10.0608L13.7955 15.2652C13.3837 15.6771 12.7319 15.7028 12.29 15.3424L12.2045 15.2652L7 10.0607V10.5H5.5V9C5.5 8.20923 6.11191 7.56138 6.88805 7.50411L7 7.5H19ZM8.5 14.25C8.91421 14.25 9.25 14.5858 9.25 15C9.25 15.4142 8.91421 15.75 8.5 15.75H4.75C4.33579 15.75 4 15.4142 4 15C4 14.5858 4.33579 14.25 4.75 14.25H8.5ZM17.9394 9H8.06066L13.0001 13.9394L17.9394 9ZM7.75 12C8.16421 12 8.5 12.3358 8.5 12.75C8.5 13.1346 8.21047 13.4516 7.83747 13.495L7.75 13.5H5.5C5.08579 13.5 4.75 13.1642 4.75 12.75C4.75 12.3654 5.03953 12.0484 5.41253 12.005L5.5 12H7.75Z"
                    />
                  </g>
                </svg>
                {{ $t('Easily invite your team members') }}
              </li>

              <li class="flex items-center gap-1">
                <svg
                  aria-hidden="true"
                  class="text-gray-500 size-6 shrink-0"
                  viewBox="0 0 26 26"
                  fill="currentColor"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g clip-path="url(#clip0_316_4602)">
                    <path
                      d="M13 6C17.1421 6 20.5 9.35786 20.5 13.5C20.5 17.6421 17.1421 21 13 21C8.85786 21 5.5 17.6421 5.5 13.5C5.5 9.35786 8.85786 6 13 6ZM13 7.5C9.68629 7.5 7 10.1863 7 13.5C7 16.8137 9.68629 19.5 13 19.5C16.3137 19.5 19 16.8137 19 13.5C19 10.1863 16.3137 7.5 13 7.5ZM13 9C13.3846 9 13.7016 9.28953 13.745 9.66253L13.75 9.75V13.1894L15.7803 15.2197C16.0732 15.5126 16.0732 15.9874 15.7803 16.2803C15.51 16.5507 15.0845 16.5715 14.7903 16.3427L14.7197 16.2803L12.4697 14.0303C12.3525 13.9131 12.2781 13.7611 12.2565 13.5986L12.25 13.5V9.75C12.25 9.33579 12.5858 9 13 9Z"
                    />
                  </g>
                </svg>
                {{ $t('Save up to 10X the cost') }}
              </li>

              <li class="flex items-center gap-1">
                <svg
                  aria-hidden="true"
                  class="text-gray-500 size-6 shrink-0"
                  viewBox="0 0 26 26"
                  fill="currentColor"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g clip-path="url(#clip0_316_4606)">
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M5.5 8.25C5.5 7.42157 6.17157 6.75 7 6.75H19C19.8285 6.75 20.5 7.42157 20.5 8.25V18.75C20.5 19.5785 19.8285 20.25 19 20.25H7C6.17157 20.25 5.5 19.5785 5.5 18.75V8.25ZM19 8.25H18.25L17.5 10.5H19V8.25ZM19 12H7V18.75H19V12ZM7 10.5H8.5L9.25 8.25H7V10.5ZM10 10.5H12.25L13 8.25H10.75L10 10.5ZM13.75 10.5H16L16.75 8.25H14.5L13.75 10.5Z"
                    />
                  </g>
                </svg>
                {{ $t('Coordinate from one simple app') }}
              </li>
            </ul>

            <NuxtLink to="/app/admin/team/new/intro" class="block">
              <ButtonWhite class="w-full font-medium text-sm sm:text-base">
                {{ $t('Create headshots for my team') }}
              </ButtonWhite>
            </NuxtLink>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  computed: {
    isTeamLead () {
      return this.$store?.state?.user?.role === 'TeamLead'
    },
    isTeamMember () {
      return this.$store?.state?.user?.role === 'TeamMember'
    },
    organization () {
      return this.$store?.state?.organization?.organization?.name || 'your organization'
    },
    hasInvites () {
      return this.$store.state?.user?.invites || null
    },
    showInviteWarning () {
      return this.$store.state?.showInviteWarning || null
    }
  },
  mounted () {
    this.$posthog.capture('$funnel:individual_or_team')
  }
}
</script>

<i18n>
  {
    "en": {
      "Who do you want to create headshots for?": "Who do you want to create headshots for?",
      "Start creating your own headshots, or create a team and let your team members create their own headshots.": "Start creating your own headshots, or create a team and let your team members create their own headshots.",
      "Just for myself": "Just for myself",
      "Most Popular": "Most Popular",
      "The fastest way to get professional headshots you can use anywhere.": "The fastest way to get professional headshots you can use anywhere.",
      "No need to dress up and drive to a studio": "No need to dress up and drive to a studio",
      "Get it done right from your home or office": "Get it done right from your home or office",
      "8x cheaper than a physical photoshoot": "8x cheaper than a physical photoshoot",
      "Create headshots for myself": "Create headshots for myself",
      "For my team and me": "For my team and me",
      "Save hundreds of dollars on corporate headshots without ever having to leave the office.": "Save hundreds of dollars on corporate headshots without ever having to leave the office.",
      "Easily invite your team members": "Easily invite your team members",
      "Save up to 10X the cost": "Save up to 10X the cost",
      "Coordinate from one simple app": "Coordinate from one simple app",
      "Create headshots for my team": "Create headshots for my team"
    },
    "es": {
      "Who do you want to create headshots for?": "¿Para quién quieres crear fotos profesionales?",
      "Start creating your own headshots, or create a team and let your team members create their own headshots.": "Comienza a crear tus propias fotos profesionales, o crea un equipo y permite que los miembros de tu equipo creen sus propias fotos profesionales.",
      "Just for myself": "Solo para mí",
      "Most Popular": "Más popular",
      "The fastest way to get professional headshots you can use anywhere.": "La forma más rápida de obtener fotos profesionales profesionales que puedes usar en cualquier lugar.",
      "No need to dress up and drive to a studio": "No necesitas vestirte y conducir a un estudio",
      "Get it done right from your home or office": "Hazlo bien desde tu casa u oficina",
      "8x cheaper than a physical photoshoot": "8x más barato que una sesión de fotos física",
      "Create headshots for myself": "Crear fotos profesionales para mí",
      "For my team and me": "Para mi equipo y para mí",
      "Save hundreds of dollars on corporate headshots without ever having to leave the office.": "Ahorra cientos de dólares en fotos profesionales corporativas sin tener que salir de la oficina.",
      "Easily invite your team members": "Invita fácilmente a los miembros de tu equipo",
      "Save up to 10X the cost": "Ahorra hasta 10 veces el costo",
      "Coordinate from one simple app": "Coordina desde una sola aplicación",
      "Create headshots for my team": "Crear fotos profesionales para mi equipo"
    },
    "de": {
      "Who do you want to create headshots for?": "Für wen sollen Bewerbungsfotos erstellt werden?",
      "Start creating your own headshots, or create a team and let your team members create their own headshots.": "Erstelle deine eigenen Fotos oder gründe ein Team für deine Mitarbeiter.",
      "Just for myself": "Nur für mich",
      "Most Popular": "Beliebteste Wahl",
      "The fastest way to get professional headshots you can use anywhere.": "Der schnellste Weg zu professionellen Fotos für jeden Einsatz.",
      "No need to dress up and drive to a studio": "Kein Styling und keine Fahrt ins Studio nötig",
      "Get it done right from your home or office": "Direkt von zu Hause oder vom Büro aus",
      "8x cheaper than a physical photoshoot": "8x günstiger als klassische Fotoshootings",
      "Create headshots for myself": "Bewerbungsfotos für mich erstellen",
      "For my team and me": "Für mein Team und mich",
      "Save hundreds of dollars on corporate headshots without ever having to leave the office.": "Spare hunderte Euro bei Firmenfotos, ohne das Büro zu verlassen.",
      "Easily invite your team members": "Teammitglieder einfach einladen",
      "Save up to 10X the cost": "Bis zu 10x günstiger",
      "Coordinate from one simple app": "Alles über eine einfache App koordinieren",
      "Create headshots for my team": "Team-Fotos erstellen"
    }
  }
</i18n>
