<template>
  <div class="flex items-center justify-start sm:justify-center space-x-2 mb-4">
    <div v-for="i in active" :key="`${i}-active`" class="bg-[#34C759] rounded-full w-[40px] h-[7px]" />
    <div v-for="i in (total-active)" :key="`${i}-deactive`" class="bg-[#EAE8FF] rounded-full w-[40px] h-[7px]" />
  </div>
</template>

<script>
export default {
  props: {
    active: {
      type: Number,
      default: 1
    },
    total: {
      type: Number,
      default: 4
    }
  }

}
</script>

<style>

</style>
