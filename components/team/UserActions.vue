<template>
  <div class="relative ml-3">
    <ButtonDropdown
      :items="items"
      theme="v1"
      size="xs"
      title="Actions"
      xalign="left-full"
      valign="center"
      @select="handleAction"
    />

    <portal to="modal">
      <Modal v-if="showAssignTeamModal" @close="showAssignTeamModal = false">
        <TeamAssignToTeamModal :user="user" @closeModal="showAssignTeamModal = false" />
      </Modal>
    </portal>
  </div>
</template>

<script>
export default {
  props: {
    user: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      showDropdown: false,
      showAssignTeamModal: false
    }
  },
  computed: {
    modelStatus () {
      return this.user?.modelStatus
    },
    items () {
      const items = []
      if (this.modelStatus && (this.modelStatus === 'active' || this.modelStatus === 'deleted')) {
        items.push({
          title: 'Retry shoot',
          value: 'retry',
          icon: 'IconArrowPathRoundedSquare'
        })
      }

      if (!this.modelStatus || this.modelStatus === 'pending-invite' || this.modelStatus === 'waiting-for-upload' || this.modelStatus === 'onboarding') {
        items.push({
          title: 'Remind User',
          value: 'remind',
          icon: 'IconBellAlert'
        })
      }

      if (this.user.role !== 'TeamLead' && this.modelStatus !== 'active') {
        items.push({
          title: 'Remove User',
          value: 'remove',
          icon: 'IconTrash'
        })
      }

      if (this.teams.length >= 1) {
        items.push({
          title: 'Assign team',
          value: 'assign-team',
          icon: 'IconUserGroup'
        })
      }

      return items
    },
    teams () {
      return this.$store.state.organization.organization?.teams || []
    }
  },
  methods: {
    handleAction (action) {
      if (action === 'retry') {
        this.$emit('retry', this.user._id)
      } else if (action === 'remind') {
        this.remindUser()
      } else if (action === 'remove') {
        this.removeUser()
      } else if (action === 'assign-team') {
        this.openAssignTeamModal()
      }
    },
    async remindUser () {
      try {
        const { success, errorMessage } = await this.$axios.$post('/organization/remind-user', {
          email: this.user.email
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.$emit('reminded')
        this.$toast.success('User has been reminded')
      } catch (err) {
        this.handleError(err)
      } finally {
        this.showDropdown = false
      }
    },
    async removeUser () {
      try {
        if (this.user.role === 'TeamLead') {
          throw new Error("Cannot remove team lead's account")
        }
        const { success, errorMessage } = await this.$axios.$post('/organization/remove-user', {
          userId: this.user._id
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.$store.commit('organization/REMOVE_TEAM_MEMBER', this.user._id)
        this.$toast.success('User has been removed')
      } catch (err) {
        this.handleError(err)
      }
    },
    openAssignTeamModal () {
      this.showAssignTeamModal = true
    }
  }
}
</script>

<style></style>
