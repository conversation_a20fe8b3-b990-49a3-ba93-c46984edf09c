<template>
  <TableRow :key="invite._id" class="last:rounded-b-lg text-sm">
    <TableItem class="text-xs max-w-[240px] overflow-hidden">
      {{ invite?.email || "" }}
    </TableItem>
    <TableItem class="text-xs">
      <template v-if="invite.createdAt">
        {{ formatDate(invite.createdAt) }}
      </template>
      <span v-else />
    </TableItem>
    <TableItem class="text-xs">
      {{ lastRemindedAt ? formatDateAndTime(lastRemindedAt) : '-' }}
    </TableItem>
    <TableItem class="text-xs">
      {{ timesReminded }}
    </TableItem>
    <TableItem class="text-xs flex items-center gap-2">
      <component :is="team?.icon || 'IconUsers'" v-if="team" class="w-4 h-4" />
      {{ team?.name || '-' }}
    </TableItem>
    <TableItem class="text-xs">
      <AppInvitesActions
        v-if="invite?.invite?._id"
        :invite="invite.invite"
        @reloadInvites="$emit('reload')"
        @reminded="handleReminded"
      />
    </TableItem>
  </TableRow>
</template>

<script>
export default {
  props: {
    invite: {
      type: Object,
      required: true
    }
  },
  computed: {
    username () {
      return this.user.email.split('@')[0].replace(/[^a-zA-Z0-9]/g, ' ') || ''
    },
    organizationReminders () {
      const reminders = this.invite?.invite?.reminders || []
      const organizationReminders = reminders.filter(reminder => reminder.organization === this.$store.state.organization.organization?._id) || []
      return organizationReminders
    },
    lastRemindedAt () {
      return this.organizationReminders.at(-1)?.remindedAt || null
    },
    timesReminded () {
      return this.organizationReminders.length || 0
    },
    teams () {
      return this.$store.state.organization.organization?.teams || []
    },
    team () {
      const teamId = this.invite?.invite?.teamId || this.invite?.teamId
      return this.teams.find(team => team.id === teamId) || null
    }
  },
  methods: {
    handleReminded () {
      this.$store.commit('organization/ADD_INVITE_REMINDER', this.invite.invite)
    }
  }
}
</script>

<style>

</style>
