<template>
  <div class="flex flex-col gap-4 p-8">
    <div class="flex flex-col space-y-0.5">
      <h2 class="text-lg font-bold text-primary-500">
        {{ team ? 'Edit team' : 'Create new team' }}
      </h2>
      <p class="text-paragraph text-sm">
        {{ team ? 'Modify the team name or its members' : 'Create a new team to organize your team members' }}. You can use teams to organize your team members.
      </p>
    </div>
    <div class="flex flex-col gap-3">
      <div class="flex flex-col gap-2 md:flex-row">
        <Input v-model="teamName" class="w-full" label="Team name" placeholder="Team name" />
        <div class="md:w-[150px] flex-shrink-0">
          <label class="block text-sm font-semibold leading-5 text-primary-500 mb-1">
            Icon
          </label>
          <ButtonDropdown
            theme="v1"
            size="sm"
            xalign="left-full"
            valign="center"
            full-width
            dropdown-class="!w-64 max-h-[150px] overflow-y-auto"
            :title="selectedIcon?.title || 'User Group'"
            :icon="teamIcon || 'IconUserGroup'"
            :items="icons"
            placeholder="Select an icon"
            @select="updateTeamIcon"
          />
        </div>
      </div>
      <div
        class="w-full flex"
        :class="{ 'justify-between': team, 'justify-end': !team }"
      >
        <ButtonWhiteDelete
          v-if="team"
          size="sm"
          @click="deleteTeam"
        >
          Delete team
        </ButtonWhiteDelete>
        <ButtonPrimary
          size="sm"
          @click="saveTeam"
        >
          <span>{{ team ? 'Update team' : 'Create team' }}</span>
        </ButtonPrimary>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    team: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      teamName: this.team?.title || '',
      icons: [],
      teamIcon: this.team?.icon || ''
    }
  },
  computed: {
    selectedIcon () {
      return this.icons.find(icon => icon.value === (this.teamIcon || 'IconUserGroup'))
    }
  },
  created () {
    // Use require.context to get all icon components
    const iconContext = require.context('@/components/icon', false, /\.vue$/)
    this.icons = iconContext.keys().map((key) => {
      const name = key.replace('./', '').replace('.vue', '')
      return {
        title: this.formatIconName(name),
        value: 'Icon' + name,
        icon: 'Icon' + name
      }
    })
  },
  methods: {
    updateTeamName (event) {
      this.teamName = event.target.value
    },
    formatIconName (name) {
      // Remove any special characters and split by capital letters
      return name
        // Split by capital letters and join with spaces
        .replace(/([A-Z])/g, ' $1')
        // Remove any remaining special characters
        .replace(/[^a-zA-Z0-9\s]/g, '')
        // Trim and capitalize first letter of each word
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(' ')
        .trim()
    },
    async saveTeam () {
      try {
        this.$loading.show({
          title: 'Saving team...'
        })
        let response = null
        if (this.team) {
          response = await this.$axios.$put(`/organization/teams/${this.team.value}`, {
            name: this.teamName,
            icon: this.teamIcon
          })
        } else {
          response = await this.$axios.$post('/organization/teams', {
            name: this.teamName,
            icon: this.teamIcon
          })
        }

        if (response.data?.organization?.teams) {
          this.$store.commit('organization/SET_ORGANIZATION_TEAMS', response.data.organization.teams)
        }

        this.$emit('closeModal')
        this.$toast.success('Team saved')
      } catch (error) {
        this.handleError(error)
      } finally {
        this.$loading.hide()
      }
    },
    async deleteTeam () {
      try {
        this.$loading.show({
          title: 'Deleting team...'
        })

        const response = await this.$axios.$delete(`/organization/teams/${this.team.value}`)

        if (response.data?.organization) {
          this.$store.commit('organization/SET_ORGANIZATION_TEAMS', response.data.organization.teams || [])
        }

        if (this.$store.state.organization.selectedTeamId === this.team.value) {
          this.$store.commit('organization/SET_SELECTED_TEAM_ID', null)
        }

        this.$emit('closeModal')
        this.$toast.success('Team deleted')
      } catch (error) {
        this.handleError(error)
      } finally {
        this.$loading.hide()
      }
    },
    updateTeamIcon (event) {
      this.teamIcon = event
    }
  }
}
</script>
