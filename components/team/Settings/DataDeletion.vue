<template>
  <Card>
    <h2 class="text-base font-bold text-primary-500">
      Data management
    </h2>
    <div class="mt-6">
      <div class="space-y-6">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-sm font-medium text-gray-900">
              Delete all user data after 30 days
            </h3>
            <p class="text-sm text-gray-500">
              ⚠️ Warning: if enabled, all team member results will be deleted 30 days after their photo shoot is completed.
            </p>
          </div>
          <InputToggle :active="settings.shouldDeleteModels" @change="toggleSetting('shouldDeleteModels')">
            <span class="text-sm font-medium" :class="settings.shouldDeleteModels ? 'text-gray-900' : 'text-gray-500'">
              {{ settings.shouldDeleteModels ? 'On' : 'Off' }}
            </span>
          </InputToggle>
        </div>
      </div>

      <!-- Save button - only shown when changes are made -->
      <div class="mt-8 flex justify-end">
        <ButtonPrimary size="sm" :is-disabled="!hasChanges" @click="saveSettings">
          Save changes
        </ButtonPrimary>
      </div>
    </div>
  </Card>
</template>

<script>
export default {
  data () {
    return {
      // Current settings
      settings: {
        shouldDeleteModels: false
      },
      // Initial settings (to track changes)
      initialSettings: {
        shouldDeleteModels: false
      }
    }
  },
  computed: {
    hasChanges () {
      // Compare current settings with initial settings
      return Object.keys(this.settings).some(key =>
        this.settings[key] !== this.initialSettings[key]
      )
    }
  },
  async mounted () {
    try {
      this.$loading.show()
      const response = await this.$axios.$get('/organization/settings/data-deletion')
      this.settings = response
      this.initialSettings = { ...response }
      this.$loading.hide()
    } catch (error) {
      console.error('Failed to load settings:', error)
      this.$loading.hide()
    }
  },
  methods: {
    toggleSetting (key) {
      // Toggle setting by key
      this.settings[key] = !this.settings[key]
    },
    async saveSettings () {
      if (this.settings.shouldDeleteModels && !confirm('Are you sure you want to save this settings? All the data, including the photos, will be deleted after 30 days since the photo shoot is completed.')) {
        return
      }

      try {
        this.$loading.show({
          title: 'Updating settings...'
        })
        // Send updated settings to backend
        await this.$axios.$post('/organization/settings/data-deletion', this.settings)

        // Update initial settings to match current
        this.initialSettings = { ...this.settings }

        // Show success notification
        this.$toast.success('Settings saved successfully')
      } catch (error) {
        console.error('Failed to save settings:', error)
        this.$toast.error('Failed to save settings. Please try again.')
      } finally {
        this.$loading.hide()
      }
    }
  }
}
</script>

<style scoped>

</style>
