<template>
  <Card>
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-base font-bold text-primary-500">
        Admin management
      </h2>
      <ButtonWhite size="sm" @click="openAddAdminModal">
        Add new
        <IconPlus class="!w-4 !h-4 ml-1 scale-125" />
      </ButtonWhite>
    </div>

    <LoadingWrapper :is-loading="isLoadingAdmins">
      <div v-if="admins.length > 0">
        <div class="max-h-[300px] overflow-y-auto">
          <Table :head="['Email', 'Role']">
            <template v-for="(admin, index) in admins">
              <TableRow :key="admin._id">
                <TableItem>
                  <div class="text-primary-500 text-sm font-medium">
                    {{ admin.displayName || admin.email }}
                  </div>
                  <div v-if="admin.displayName" class="text-paragraph text-[13px]">
                    {{ admin.email }}
                  </div>
                </TableItem>
                <TableItem>
                  <div class="flex justify-end">
                    <ButtonDropdown
                      v-if="getRoleDropdownItems(admin).length > 0"
                      :title="getRoleTitle(admin)"
                      :items="getRoleDropdownItems(admin)"
                      size="sm"
                      theme="v1"
                      :valign="index === admins.length - 1 ? 'top' : 'bottom'"
                      @select="handleRoleAction($event, admin)"
                    />
                    <div v-else class="flex content-center items-center justify-center rounded-md border border-[#e4e4e7] bg-white px-4 py-2 text-center text-sm font-medium leading-4 text-slate-700 cursor-not-allowed">
                      <span>{{ getRoleTitle(admin) }}</span>
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="#807D96" aria-hidden="true" class="h-4 w-4 flex-shrink-0 -mr-1"><path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" /></svg>
                    </div>
                  </div>
                </TableItem>
              </TableRow>
            </template>
          </Table>
        </div>
      </div>
      <div v-else class="text-center text-gray-500 py-4">
        No admins found.
      </div>
    </LoadingWrapper>

    <Modal v-if="showAddAdminModal" max-width="w-full sm:max-w-md" @close="closeAddAdminModal">
      <div class="p-6">
        <h3 class="text-base font-bold text-primary-500 mb-1">
          Add new admin
        </h3>
        <p class="text-paragraph text-sm mb-4">
          Add a new admin to your organization. This person will have full access to your organization's data and settings.
        </p>
        <Input
          v-model="newAdminEmail"
          label="Email address"
          placeholder="Admin email address"
          type="email"
          class="mb-4"
          @keyup.enter="addAdmin"
        />
        <AlertWarningV2 v-if="addingError" :text="addingError" class="mb-4" />
        <div class="flex justify-end space-x-2">
          <ButtonWhite size="sm" @click="closeAddAdminModal">
            Cancel
          </ButtonWhite>
          <ButtonPrimary size="sm" :is-disabled="!newAdminEmail || isAddingAdmin" @click="addAdmin">
            <LoadingSpinner v-if="isAddingAdmin" class="w-4 h-4 mr-1" />
            Add admin
          </ButtonPrimary>
        </div>
      </div>
    </Modal>
  </Card>
</template>

<script>
export default {
  data () {
    return {
      admins: [],
      isLoadingAdmins: true,
      showAddAdminModal: false,
      newAdminEmail: '',
      isAddingAdmin: false,
      addingError: null
    }
  },
  computed: {
    organizationOwnerId () {
      return this.$store.state.organization?.organization?.owner || null
    }
  },
  async mounted () {
    await this.fetchAdmins()
  },
  methods: {
    async fetchAdmins () {
      this.isLoadingAdmins = true
      try {
        const response = await this.$axios.$get('/organization/roles')
        this.admins = response.users || []
      } catch (error) {
        console.error('Failed to load admins:', error)
        this.$toast.error('Failed to load admins. Please try again.')
        this.admins = [] // Ensure admins is an array even on error
      } finally {
        this.isLoadingAdmins = false
      }
    },
    getRoleTitle (admin) {
      if (admin._id === this.organizationOwnerId) {
        return 'Owner'
      }

      return 'Admin'
    },
    getRoleDropdownItems (admin) {
      if (admin._id === this.organizationOwnerId || admin.email === this.$store.state.user.email) {
        return []
      }

      return [
        { title: 'Remove as admin', value: 'remove', classes: 'text-red-600 hover:bg-red-50' }
      ]
    },
    async handleRoleAction (action, admin) {
      if (action === 'remove') {
        await this.removeAdmin(admin.email)
      }
    },
    openAddAdminModal () {
      this.newAdminEmail = ''
      this.isAddingAdmin = false
      this.addingError = null
      this.showAddAdminModal = true
    },
    closeAddAdminModal () {
      this.showAddAdminModal = false
      this.addingError = null
    },
    async addAdmin () {
      if (!this.newAdminEmail || !this.newAdminEmail.includes('@') || this.isAddingAdmin) {
        if (this.newAdminEmail && !this.newAdminEmail.includes('@')) {
          this.$toast.error('Please enter a valid email address.')
        }
        return
      }

      this.addingError = null
      this.isAddingAdmin = true
      try {
        this.$loading.show({ title: 'Adding admin...' })
        await this.$axios.$post('/organization/roles/add-admin', { email: this.newAdminEmail })
        this.$toast.success(`${this.newAdminEmail} has been added as an admin.`)
        this.closeAddAdminModal()
        await this.fetchAdmins()
      } catch (error) {
        const message = error.response.data?.errorMessage || error.message || error.errorMessage
        if (message) {
          this.addingError = message
        } else {
          this.handleError(error)
        }
      } finally {
        this.isAddingAdmin = false
        this.$loading.hide()
      }
    },
    async removeAdmin (email) {
      if (!email) { return }
      if (!confirm('Are you sure you want to remove this admin?')) { return }

      try {
        this.$loading.show({ title: 'Removing admin...' })
        await this.$axios.$post('/organization/roles/remove-admin', { email })
        this.$toast.success(`${email} has been removed as admin.`)
        await this.fetchAdmins()
      } catch (error) {
        console.error('Failed to remove admin:', error)
        this.handleError(error)
      } finally {
        this.$loading.hide()
      }
    }
  }
}
</script>

<style scoped>
/* Add any specific styles if needed */
</style>
