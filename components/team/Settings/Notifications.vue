<template>
  <Card>
    <h2 class="text-base font-bold text-primary-500">
      Email notifications
    </h2>
    <div class="mt-6">
      <div class="space-y-6">
        <div
          v-for="notification in emailNotifications"
          :key="notification.key"
          class="flex items-center justify-between"
        >
          <div>
            <h3 class="text-sm font-medium text-gray-900">
              {{ notification.title }}
            </h3>
            <p class="text-sm text-gray-500">
              {{ notification.description }}
            </p>
          </div>
          <InputToggle :active="settings[notification.key]" @change="toggleSetting(notification.key)">
            <span class="text-sm font-medium" :class="settings[notification.key] ? 'text-gray-900' : 'text-gray-500'">
              {{ settings[notification.key] ? 'On' : 'Off' }}
            </span>
          </InputToggle>
        </div>
      </div>

      <!-- Save button - only shown when changes are made -->
      <div class="mt-8 flex justify-end">
        <ButtonPrimary size="sm" :is-disabled="!hasChanges" @click="saveSettings">
          Save changes
        </ButtonPrimary>
      </div>
    </div>
  </Card>
</template>

<script>
export default {
  data () {
    return {
      // Current settings
      settings: {
        notifyLowCredits: true,
        notifyResultsReady: true,
        automaticallyRemindTeamUsersToCompleteHeadshot: true
      },
      // Initial settings (to track changes)
      initialSettings: {
        notifyLowCredits: true,
        notifyResultsReady: true,
        automaticallyRemindTeamUsersToCompleteHeadshot: true
      },
      // Email notification configurations
      emailNotifications: [
        {
          key: 'notifyLowCredits',
          title: 'Credits',
          description: 'Get notified when your account goes under 3 credits'
        },
        {
          key: 'notifyResultsReady',
          title: 'Results ready',
          description: 'Get notified when someone\'s results are ready'
        },
        {
          key: 'automaticallyRemindTeamUsersToCompleteHeadshot',
          title: 'Automatic reminders',
          description: 'Automatically remind team members to complete headshots'
        }
      ]
    }
  },
  computed: {
    hasChanges () {
      // Compare current settings with initial settings for all notification types
      return Object.keys(this.settings).some(key =>
        this.settings[key] !== this.initialSettings[key]
      )
    }
  },
  async mounted () {
    try {
      this.$loading.show()
      const response = await this.$axios.$get('/organization/settings/email-notifications')
      this.settings = response
      this.initialSettings = { ...response }
      this.$loading.hide()
    } catch (error) {
      console.error('Failed to load settings:', error)
    }
  },
  methods: {
    toggleSetting (key) {
      // Toggle setting by key
      this.settings[key] = !this.settings[key]
    },
    async saveSettings () {
      try {
        this.$loading.show({
          title: 'Updating settings...'
        })
        // Send updated settings to backend
        await this.$axios.$post('/organization/settings/email-notifications', this.settings)

        // Update initial settings to match current
        this.initialSettings = { ...this.settings }

        // Show success notification
        this.$toast.success('Settings saved successfully')
      } catch (error) {
        console.error('Failed to save settings:', error)
        this.$toast.error('Failed to save settings. Please try again.')
      } finally {
        this.$loading.hide()
      }
    }
  }
}
</script>

<style scoped>

</style>
