<template>
  <TableRow :key="user._id" class="last:rounded-b-lg">
    <TableItem class="text-xs">
      <div class="flex flex-col items-left gap-0.5">
        <span class="text-xs font-medium">{{ user?.email || "" }}</span>
        <span v-if="user?.title" class="text-xs font-normal text-slate-500">{{ user?.title || "" }}</span>
      </div>
    </TableItem>
    <TableItem>
      <span class="inline-flex items-center text-xs font-medium h-[22px] px-2 rounded-full whitespace-nowrap" :style="{ backgroundColor: modelStatus.background, color: modelStatus.color }">
        {{ modelStatus.title }}
      </span>
    </TableItem>
    <TableItem class="text-xs">
      <template v-if="user.createdAt">
        {{ formatDate(user.createdAt) }}
      </template>
      <span v-else />
    </TableItem>
    <TableItem class="text-xs">
      {{ lastRemindedAt ? formatDateAndTime(lastRemindedAt) : '-' }}
    </TableItem>
    <TableItem class="text-xs">
      {{ timesReminded }}
    </TableItem>
    <TableItem class="text-xs flex items-center gap-2">
      <component :is="team?.icon || 'IconUsers'" v-if="team" class="w-4 h-4 flex-shrink-0" />
      {{ team?.name || '-' }}
    </TableItem>
    <TableItem>
      <div class="flex items-center justify-end">
        <TeamUserActions
          v-if="!user?.invite?._id"
          :user="user"
          @retry="$emit('retry', $event)"
          @success="$emit('reload')"
          @reminded="handleReminded"
        />
        <AppInvitesActions
          v-if="user?.invite?._id"
          :invite="user.invite"
          @reloadInvites="$emit('reload')"
          @reminded="handleReminded"
        />
      </div>
    </TableItem>
  </TableRow>
</template>

<script>
export default {
  props: {
    user: {
      type: Object,
      required: true
    }
  },
  computed: {
    username () {
      return this.user.email.split('@')[0].replace(/[^a-zA-Z0-9]/g, ' ') || ''
    },
    modelStatus () {
      const modelStatus = this.user?.modelStatus
      if (!modelStatus || modelStatus.length === 0) {
        return null
      }

      if (modelStatus === 'active') {
        return { title: 'Finished', background: '#ECFDF3', color: '#027A48' }
      }

      if (modelStatus === 'waiting-for-upload' || modelStatus === 'onboarding') {
        return { title: 'Hasn\'t uploaded their photos', background: '#FFF6ED', color: '#C4320A' }
      }

      if (['pending', 'generatingHeadshots', 'waiting'].includes(modelStatus)) {
        return { title: 'Photos being generated', background: '#ECFDF3', color: '#027A48' }
      }

      return { title: this.slugToTitle(modelStatus, false), background: '#FFF6ED', color: '#C4320A' }
    },
    organizationReminders () {
      if (this.modelStatus?.title === 'Finished') {
        return []
      }

      const reminders = (this.isInvite ? this.user?.invite?.reminders : this.user?.reminders) || []
      const organizationReminders = reminders.filter(reminder => reminder.organization === this.$store.state.organization.organization?._id) || []
      return organizationReminders
    },
    lastRemindedAt () {
      return this.organizationReminders.at(-1)?.remindedAt || null
    },
    timesReminded () {
      return this.organizationReminders.length || 0
    },
    isInvite () {
      return !!this.user?.invite?._id
    },
    teams () {
      return this.$store.state.organization.organization?.teams || []
    },
    team () {
      const teamId = this.user?.teamId
      return this.teams.find(team => team.id === teamId) || null
    }
  },
  methods: {
    handleReminded () {
      if (this.isInvite) {
        this.$store.commit('organization/ADD_INVITE_REMINDER', this.user.invite)
      } else {
        this.$store.commit('organization/ADD_USER_REMINDER', this.user)
      }
    }
  }
}
</script>

<style>

</style>
