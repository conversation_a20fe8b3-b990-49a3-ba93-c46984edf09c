<template>
  <div class="bg-white rounded-lg border border-black/10 p-3 grid grid-cols-6 gap-3 relative group">
    <template v-if="team">
      <div class="absolute right-0 top-0 p-1 bg-teal-50 rounded-tr-lg rounded-bl-lg text-teal-500 group-hover:opacity-0 opacity-100 transition-opacity duration-300">
        <component :is="team?.icon || 'IconUsers'" class="w-4 h-4" />
      </div>
      <div class="absolute right-0 top-0 p-1 bg-teal-50 rounded-tr-lg rounded-bl-lg text-teal-500 group-hover:opacity-100 opacity-0 transition-opacity duration-300 flex items-center gap-1 whitespace-nowrap max-w-[100px] line-clamp-1 text-xs">
        <component :is="team?.icon || 'IconUsers'" class="w-4 h-4 flex-shrink-0" />
        {{ team?.name }}
      </div>
    </template>
    <div class="relative w-full aspect-square rounded-full overflow-hidden mx-auto col-span-2">
      <div v-if="user.favoritePhoto?.pfp && $store.state.organization?.organization?.branding" class="absolute inset-0 w-full h-full">
        <GalleryProfilePicture
          ref="galleryProfilePicture"
          class="!w-full !h-full absolute inset-0"
          :pfp-url="user.favoritePhoto?.pfp"
          :photo-id="user.favoritePhoto?._id"
          :branding="$store.state.organization?.organization?.branding"
        />
      </div>
      <ImageDns v-else-if="user.favoritePhoto?.thumbnail" :src="user.favoritePhoto?.thumbnail" class="absolute inset-0 w-full h-full object-cover object-top" />
      <div v-else class="absolute inset-0 w-full h-full bg-[#D3D3D3] flex items-center justify-center text-center text-[9px] text-paragraph">
        No favorite selected
      </div>
    </div>
    <div class="flex flex-col gap-2 col-span-4 items-start justify-center">
      <div class="overflow-hidden w-full overflow-x-scroll">
        <span v-if="user?.title" class="text-[13px] font-medium text-primary-500 text-left">{{ user.title?.slice(0, 20) }}</span>
        <p class="text-[13px] font-medium text-paragraph text-left">
          {{ user.email }}
        </p>
      </div>
      <div class="flex items-center justify-start gap-2">
        <ButtonWhite size="xs" class="block md:hidden lg:hidden xl:block" @click="handleAction('view-results')">
          View
        </ButtonWhite>
        <ButtonDropdown
          full-width
          theme="v1"
          size="xs"
          class=""
          dropdown-class="!w-64"
          :items="options"
          title="Actions"
          xalign="right"
          @select="handleAction"
        />
      </div>
    </div>
    <portal to="modal">
      <Modal v-if="showAssignTeamModal" @close="showAssignTeamModal = false">
        <TeamAssignToTeamModal :user="user" @closeModal="showAssignTeamModal = false" />
      </Modal>
    </portal>
  </div>
</template>

<script>
export default {
  props: {
    user: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      showAssignTeamModal: false
    }
  },
  computed: {
    options () {
      const options = []
      options.push({
        title: 'View results',
        value: 'view-results',
        icon: 'IconEye',
        classes: '*:!normal-case'
      })

      options.push({
        title: 'Give retry',
        value: 'give-retry',
        icon: 'IconArrowPathRoundedSquare',
        classes: '*:!normal-case'
      })

      if (this.user.favoritePhoto?.thumbnail) {
        options.push({
          title: 'Download PFP',
          value: 'download-pfp',
          icon: 'IconSolidUserCircle',
          classes: '*:!normal-case'
        })
      }

      if (this.teams.length >= 1) {
        options.push({
          title: 'Assign team',
          value: 'assign-team',
          icon: 'IconUserGroup',
          classes: '*:!normal-case'
        })
      }

      return options
    },
    teams () {
      return this.$store.state.organization.organization?.teams || []
    },
    team () {
      const teamId = this.user?.teamId
      return this.teams.find(team => team.id === teamId) || null
    }
  },
  methods: {
    handleAction (value) {
      switch (value) {
        case 'view-results':
          this.$router.push(`/app/admin/results/${this.user.modelId}`)
          break
        case 'download-pfp':
          this.downloadPfp()
          break
        case 'give-retry':
          this.$emit('retry', this.user._id)
          break
        case 'assign-team':
          this.openAssignTeamModal()
          break
      }
    },
    downloadPfp () {
      if (this.$refs.galleryProfilePicture) {
        this.$refs.galleryProfilePicture.downloadPfp()
        return
      }

      if (!this.user.favoritePhoto?.thumbnail) {
        this.$toast.error('No favorite photo selected')
        return
      }

      this.$loading.show({
        title: 'Downloading PFP...'
      })

      const url = this.user.favoritePhoto?.thumbnail
      this.$toast.success('Your image will download shortly. Make sure to accept the download prompt.')
      fetch(url)
        .then(response => response.blob())
        .then((blob) => {
          const url = window.URL.createObjectURL(new Blob([blob]))
          const link = document.createElement('a')
          link.href = url
          const fileName = `${this.user.modelId}-HeadshotPro.jpg`
          link.setAttribute('download', fileName)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        })
        .finally(() => {
          this.$loading.hide()
        })
    },
    openAssignTeamModal () {
      this.showAssignTeamModal = true
    }
  }
}
</script>
