<template>
  <div>
    <AppTitle title="Finish your setup" sub-title="You've got a few outstanding steps to complete!" :with-mobile-actions="completedAllMandatorySteps">
      <ButtonPrimary size="sm" :is-disabled="!completedAllMandatorySteps" @click="inviteMember">
        <span>Invite member</span>
        <IconPlus class="!size-4 scale-125 ml-1.5" />
      </ButtonPrimary>
      <template #mobile>
        <ButtonPrimary size="sm" class="w-full" @click="inviteMember">
          <span>Invite member</span>
          <IconPlus class="!size-4 scale-125 ml-1.5" />
        </ButtonPrimary>
      </template>
    </AppTitle>
    <div class="flex flex-col xl:flex-row gap-4 mt-6 ">
      <Card class="max-w-2xl">
        <div>
          <div v-for="step in onboardingSteps" :key="step.title" class="py-4 flex flex-col xs:justify-between xs:items-center xs:flex-row gap-4">
            <div class="flex items-start justify-start gap-[14px]">
              <div
                class="w-[26px] h-[26px] rounded-full flex items-center justify-center flex-shrink-0"
                :class="{
                  'bg-[#0EB567]': step.completed,
                  'bg-[#E4E4E7]': !step.completed
                }"
              >
                <IconCheck class="size-4 text-white" />
              </div>
              <div>
                <h3
                  class="text-sm font-medium"
                  :class="{
                    'text-paragraph': step.completed,
                    'text-primary-500': !step.completed
                  }"
                >
                  {{ step.title }}
                  <span v-if="step.required && !step.completed" class="text-paragraph"> (required)</span>
                </h3>
                <p class="text-sm text-paragraph">
                  {{ step.subtitle }}
                </p>
              </div>
            </div>
            <div v-if="!step.completed">
              <ButtonPrimary v-if="step.required" class="w-full xs:w-auto" size="sm" :is-disabled="!step.canComplete" @click="step.complete">
                Finalise this step
              </ButtonPrimary>
              <ButtonWhite v-else class="w-full xs:w-auto" size="sm" :disabled="!step.canComplete" @click="step.complete">
                Finalise this step
              </ButtonWhite>
            </div>
          </div>
        </div>
      </Card>
      <Card class="max-w-2xl">
        <h2 class="text-lg font-semibold text-primary-500">
          Watch our founder explain the product
        </h2>
        <Paragraph size="md" class="mb-4">
          See how the full process works, from creating your team to the experience of your employees.
        </Paragraph>
        <VideoPlayer
          class="w-full max-w-[550px]"
          src="https://cdn2.headshotpror2.com/demo-b2b-compressed.mp4"
          :placeholder="require('@/assets/img/b2b/app-placeholder.jpg')"
          :autoplay="true"
          :markers="[{ time: 15, label: 'Onboarding' }, { time: 236, label: 'Dashboard' }, { time: 538, label: 'For your team' }]"
        />
      </Card>
    </div>
  </div>
</template>

<script>
import OrganizationMixin from '@/mixins/OrganizationMixin'
import VideoPlayer from '@/components/landingpage/common/VideoPlayer.vue'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
export default {
  components: {
    VideoPlayer,
    Paragraph
  },
  mixins: [OrganizationMixin],
  computed: {
    onboardingSteps () {
      const hasOrganization = !!this.$store.state.organization?.organization
      const hasOrganizationDetails = this.completionData.hasDetails
      return [
        {
          title: 'Start creating a team',
          subtitle: 'Start creating your team',
          required: true,
          completed: hasOrganization,
          canComplete: !hasOrganization,
          complete: () => this.$router.push('/app/admin/team/new/intro')
        },
        {
          title: 'Company details',
          subtitle: 'Tell us a bit about your company',
          required: true,
          completed: this.completionData.hasDetails,
          canComplete: hasOrganization && !this.completionData.hasDetails,
          complete: () => this.$router.push('/app/admin/team/new/intro')
        },
        {
          title: 'Branding',
          subtitle: 'Make your headshots brand-proof',
          required: false,
          completed: this.completionData.hasBranding,
          canComplete: hasOrganizationDetails && !this.completionData.hasBranding,
          complete: () => this.$router.push('/app/admin/team/new/branding')
        },
        {
          title: 'Backdrops',
          subtitle: 'Pick your backdrops',
          required: true,
          completed: this.completionData.hasStyles,
          canComplete: hasOrganizationDetails && !this.completionData.hasStyles,
          complete: () => this.$router.push('/app/admin/team/new/select-style')
        },
        {
          title: 'Clothing',
          subtitle: 'What clothing to wear',
          required: true,
          completed: this.completionData.hasClothing,
          canComplete: hasOrganizationDetails && !this.completionData.hasClothing,
          complete: () => this.$router.push('/app/admin/team/new/select-clothing')
        },
        {
          title: 'Payment',
          subtitle: 'Many ways to pay',
          required: true,
          completed: this.completionData.hasBilling,
          canComplete: hasOrganizationDetails && !this.completionData.hasBilling,
          complete: () => this.$router.push('/app/admin/team/new/payment')
        },
        {
          title: 'Invite',
          subtitle: 'Get your team onboard',
          required: true,
          completed: this.completionData.hasInvitedTeam,
          canComplete: hasOrganizationDetails && this.completionData.hasBilling, // Can't do this until payment is completed
          complete: () => this.$router.push('/app/admin/team/new/invite-team')
        }
      ]
    }
  },
  methods: {
    inviteMember () {
      this.$emit('invite')
    }
  }
}
</script>
