<template>
  <div>
    <AppTitle title="Dashboard" sub-title="Everything you need to know about your organisation in a single glance." :with-mobile-actions="true">
      <ButtonWhite size="sm" @click="startGuidedShoot">
        <span class="flex-shrink-0">Managed upload</span>
      </ButtonWhite>
      <ButtonPrimary size="sm" @click="$emit('invite')">
        <span>Invite member</span>
        <IconPlus class="!size-4 scale-125 ml-1.5" />
      </ButtonPrimary>

      <template #mobile>
        <ButtonPrimary size="sm" class="w-full" @click="$emit('invite')">
          <span>Invite member</span>
          <IconPlus class="!size-4 scale-125 ml-1.5" />
        </ButtonPrimary>
      </template>
    </AppTitle>
    <main class="mt-6">
      <div class="space-y-4">
        <div class="flex flex-col sm:flex-row gap-3 mb-4">
          <div class="flex">
            <Segment :key="filterStatus" class="hidden sm:block" :items="filterStatusOptions" :initial-active="filterStatus" @selected="filterStatus = $event; query = ''" />
            <InputSelect class="w-full sm:hidden" :options="filterStatusOptions.map((option) => ({ title: option.label, value: option.value }))" :value="filterStatus" @input="filterStatus = $event" />
          </div>
          <div class="flex-1">
            <Input
              v-model="query"
              placeholder="Search for a member"
              class="w-full"
              input-class="pl-10"
              @input="debouncedSearch"
            >
              <template #affix>
                <IconSearch class="!size-4 scale-125 text-paragraph absolute left-3 top-1/2 -translate-y-1/2" />
              </template>
            </Input>
          </div>
        </div>
        <TeamDashboardZapierIntegrationBlock />
        <template v-if="query.length >= 2">
          <Card>
            <div class="flex items-center justify-between">
              <div>
                <h2 class="font-semibold text-lg text-primary-500">
                  Results for "{{ query }}"
                </h2>
                <p class="font-medium text-slate-500">
                  Here's who we could find for your search query.
                </p>
              </div>
              <div v-if="finishedSearchResults.length > 0" class="flex-shrink-0">
                <ButtonWhite size="xs" @click="toggleSearchSort">
                  <span class="text-xs">
                    Switch to {{ sortBy === 'date' ? 'Alphabetical' : 'Date' }}
                  </span>
                </ButtonWhite>
              </div>
            </div>
            <p v-if="searchResults.length === 0" class="font-medium text-slate-500 italic">
              No members were found for "{{ query }}".
            </p>
            <template v-else>
              <div v-if="finishedSearchResults.length > 0" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-4">
                <template v-for="user in finishedSearchResults">
                  <TeamModelCard
                    :key="user._id + '-finished'"
                    :user="user"
                    @retry="$emit('retry', $event)"
                    @reload="loadAll()"
                  />
                </template>
              </div>
              <Table v-if="pendingSearchResults.length > 0" :head="['Email', 'Status', 'Invited on' ,'Actions']" class="w-full mt-4 overflow-x-auto" :sticky="false">
                <template v-for="user in pendingSearchResults">
                  <TeamRowItem
                    :key="user._id + '-pending'"
                    :user="user"
                    @retry="$emit('retry', $event)"
                    @reload="loadAll()"
                  />
                </template>
              </Table>
            </template>
          </Card>
        </template>
        <template v-else>
          <Card v-if="(finishedModels.length > 0 && filterStatus === 'all') || filterStatus === 'active'">
            <div class="flex items-center justify-between">
              <div>
                <h2 class="font-semibold text-lg text-primary-500">
                  Finished photo shoots <span v-if="finishedModels.length > 0">({{ $store.state.organization.dashboardMetadata.finishedModels.total }})</span>
                </h2>
                <p v-if="finishedModels.length > 0" class="font-medium text-slate-500">
                  All team members who've selected their favourite headshot.
                </p>
                <p v-else class="font-medium text-slate-500">
                  No finished photo shoots yet.
                </p>
              </div>
              <div v-if="finishedModels.length > 0" class="flex-shrink-0">
                <ButtonWhite size="xs" @click="toggleFinishedSort">
                  <span class="text-xs">
                    Switch to {{ sortBy === 'date' ? 'Alphabetical' : 'Date' }}
                  </span>
                </ButtonWhite>
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4 mt-4">
              <template v-for="user in finishedModels">
                <TeamModelCard
                  :key="user._id + '-' + user.modelId"
                  :user="user"
                  @retry="$emit('retry', $event)"
                  @reload="loadAll()"
                />
              </template>
            </div>
            <div v-if="!$store.state.organization.dashboardMetadata.finishedModels.reachedEnd" class="mt-4 w-full">
              <ButtonWhite size="sm" @click="getFinishedModels(true)">
                Load more users
              </ButtonWhite>
            </div>
          </Card>
          <Card v-if="(pendingTeamMembers.length > 0 && filterStatus === 'all') || filterStatus === 'waiting-for-upload'">
            <h2 class="font-semibold text-lg text-primary-500">
              Have not completed their photo shoots <span v-if="pendingTeamMembers.length > 0">({{ $store.state.organization.dashboardMetadata.teamMembers.total }})</span>
            </h2>
            <p v-if="pendingTeamMembers.length > 0" class="font-medium text-slate-500">
              All team members who haven't finished their shoot.
            </p>
            <p v-else class="font-medium text-slate-500">
              All team members have completed their photo shoots.
            </p>
            <Table :head="['Email', 'Status', 'Invited on', 'Last reminded on', 'Times reminded', 'Team', 'Actions']" class="w-full mt-4 overflow-x-auto" :sticky="false">
              <template v-for="user in pendingTeamMembers">
                <TeamRowItem
                  :key="user._id"
                  :user="user"
                  @retry="$emit('retry', $event)"
                  @reload="getAcceptedModels()"
                />
              </template>
            </Table>
            <div v-if="!$store.state.organization.dashboardMetadata.teamMembers.reachedEnd" class="mt-4 w-full">
              <ButtonWhite size="sm" @click="getAcceptedModels(true)">
                Load more users
              </ButtonWhite>
            </div>
          </Card>
          <Card v-if="(pendingInvites.length > 0 && filterStatus === 'all') || filterStatus === 'pending-invite'">
            <h2 class="font-semibold text-lg text-primary-500">
              Have not accepted invite <span v-if="pendingInvites.length > 0">({{ $store.state.organization.dashboardMetadata.pendingInvites.total }})</span>
            </h2>
            <p v-if="pendingInvites.length > 0" class="font-medium text-slate-500">
              Team members who you've invited, but haven't accepted yet.
            </p>
            <p v-else class="font-medium text-slate-500">
              All team members have accepted their invite.
            </p>
            <Table :head="['Email', 'Invited on', 'Last reminded on', 'Times reminded', 'Team', 'Actions']" class="w-full mt-4 overflow-x-auto" :sticky="false">
              <template v-for="invite in pendingInvites">
                <TeamInviteRowItem
                  :key="invite._id"
                  :invite="invite"
                  @reload="getPendingInvites(); $emit('removed-invites')"
                />
              </template>
            </Table>
            <div v-if="!$store.state.organization.dashboardMetadata.pendingInvites.reachedEnd" class="mt-4 w-full">
              <ButtonWhite size="sm" @click="getPendingInvites(true)">
                Load more invites
              </ButtonWhite>
            </div>
          </Card>
        </template>
      </div>
    </main>
  </div>
</template>

<script>
export default {
  data () {
    return {
      query: '',
      searchResults: [],
      filterStatus: 'all',
      debounceTimeout: null,
      sortBy: 'date',
      sortByOptions: [
        { label: 'Date', value: 'date' },
        { label: 'Alphabetical', value: 'alphabetical' }
      ]
    }
  },
  computed: {
    filteredTeamMembers () {
      if (this.filterStatus === 'all') {
        return this.teamMembers
      }

      if (this.filterStatus === 'waiting-for-upload') {
        return this.pendingTeamMembers
      }

      return this.teamMembers.filter((member) => {
        return member.modelStatus === this.filterStatus
      })
    },
    pendingSearchResults () {
      return this.searchResults.filter((user) => {
        return !['deleted', 'active'].includes(user.modelStatus)
      })
    },
    finishedSearchResults () {
      return this.searchResults.filter((user) => {
        return user.modelStatus === 'active'
      })
    },
    pendingTeamMembers () {
      return this.teamMembers.filter((member) => {
        return member.modelStatus !== 'pending-invite'
      })
    },
    organization () {
      return this.$store.state.organization.organization
    },
    teamMembers () {
      return this.$store.state.organization.teamMembers.filter((member) => {
        return member.modelStatus !== 'deleted'
      })
    },
    finishedModels () {
      return this.$store.state.organization.finishedModels
    },

    pendingInvites () {
      return this.$store.state.organization.pendingInvites
    },
    filterStatusOptions () {
      const finished = this.$store.state.organization.dashboardMetadata?.finishedModels?.total || 0
      const joined = this.$store.state.organization.dashboardMetadata?.teamMembers?.total || 0
      const invited = this.$store.state.organization.dashboardMetadata?.pendingInvites?.total || 0

      return [
        { label: `All (${finished + joined + invited})`, value: 'all' },
        { label: `Finished (${finished})`, value: 'active' },
        { label: `Joined (${joined})`, value: 'waiting-for-upload' },
        { label: `Invited (${invited})`, value: 'pending-invite' }
      ]
    }
  },
  watch: {
    '$store.state.organization.selectedTeamId': {
      handler () {
        this.loadAll(true)
      },
      immediate: true
    }
  },
  async mounted () {
    await this.loadAll(false)
  },
  methods: {
    toggleFinishedSort () {
      this.sortBy = this.sortBy === 'date' ? 'alphabetical' : 'date'
      this.getFinishedModels()
    },
    toggleSearchSort () {
      this.sortBy = this.sortBy === 'date' ? 'alphabetical' : 'date'
      this.search()
    },
    startGuidedShoot () {
      // Check credits
      if (this.$store.state.user.credits < 1) {
        this.$router.push('/app/admin/credits')
        return
      }
      this.$router.push('/app/upload/managed-upload')
    },
    async loadAll (refresh = true) {
      const promises = []
      if (this.finishedModels.length === 0 || refresh) {
        promises.push(this.getFinishedModels())
      }
      if (this.pendingTeamMembers.length === 0 || refresh) {
        promises.push(this.getAcceptedModels())
      }
      if (this.pendingInvites.length === 0 || refresh) {
        promises.push(this.getPendingInvites())
      }

      if (refresh) {
        this.query = ''
        this.searchResults = []
      }

      await Promise.allSettled(promises)
    },
    async getPendingInvites (increasePage = false) {
      try {
        this.$loading.show({
          title: 'Loading...'
        })
        const currentPage = increasePage ? this.$store.state.organization.dashboardMetadata.pendingInvites.pages + 1 : 1
        this.$store.commit('organization/SET_DASHBOARD_METADATA', {
          ...this.$store.state.organization.dashboardMetadata,
          pendingInvites: {
            ...this.$store.state.organization.dashboardMetadata.pendingInvites,
            pages: currentPage
          }
        })
        const { success, data, errorMessage } = await this.$axios.$get('/organization/team/pending', {
          params: {
            page: currentPage,
            teamId: this.$store.state.organization.selectedTeamId
          }
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        if (increasePage) {
          this.$store.commit('organization/ADD_PENDING_INVITES', data.users)
        } else {
          this.$store.commit('organization/SET_PENDING_INVITES', data.users)
        }

        this.$store.commit('organization/SET_DASHBOARD_METADATA', {
          ...this.$store.state.organization.dashboardMetadata,
          pendingInvites: {
            ...this.$store.state.organization.dashboardMetadata.pendingInvites,
            reachedEnd: !data.hasMorePages,
            total: data.total
          }
        })
        this.$loading.hide()
      } catch (err) {
        this.$loading.hide()
        console.log(err)
      }
    },
    async getAcceptedModels (increasePage = false) {
      try {
        this.$loading.show({
          title: 'Loading...'
        })
        const currentPage = increasePage ? this.$store.state.organization.dashboardMetadata.teamMembers.pages + 1 : 1
        this.$store.commit('organization/SET_DASHBOARD_METADATA', {
          ...this.$store.state.organization.dashboardMetadata,
          teamMembers: {
            ...this.$store.state.organization.dashboardMetadata.teamMembers,
            pages: currentPage
          }
        })
        const { success, data, errorMessage } = await this.$axios.$get('/organization/team/accepted', {
          params: {
            page: currentPage,
            teamId: this.$store.state.organization.selectedTeamId
          }
        })

        if (!success) {
          throw new Error(errorMessage)
        }
        this.$store.commit('organization/SET_DASHBOARD_METADATA', {
          ...this.$store.state.organization.dashboardMetadata,
          teamMembers: {
            ...this.$store.state.organization.dashboardMetadata.teamMembers,
            reachedEnd: !data.hasMorePages,
            total: data.total
          }
        })
        if (increasePage) {
          this.$store.commit('organization/ADD_TEAM_MEMBERS', data.users)
        } else {
          this.$store.commit('organization/SET_TEAM_MEMBERS', data.users)
        }
        this.$loading.hide()
      } catch (err) {
        this.$loading.hide()
        console.log(err)
      }
    },
    async getFinishedModels (increasePage = false) {
      try {
        this.$loading.show({
          title: 'Loading...'
        })
        const currentPage = increasePage ? this.$store.state.organization.dashboardMetadata.finishedModels.pages + 1 : 1
        this.$store.commit('organization/SET_DASHBOARD_METADATA', {
          ...this.$store.state.organization.dashboardMetadata,
          finishedModels: {
            ...this.$store.state.organization.dashboardMetadata.finishedModels,
            pages: currentPage
          }
        })
        const { success, data, errorMessage } = await this.$axios.$get('/organization/team/finished', {
          params: {
            page: currentPage,
            teamId: this.$store.state.organization.selectedTeamId,
            sortBy: this.sortBy
          }
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.$store.commit('organization/SET_DASHBOARD_METADATA', {
          ...this.$store.state.organization.dashboardMetadata,
          finishedModels: {
            ...this.$store.state.organization.dashboardMetadata.finishedModels,
            reachedEnd: !data.hasMorePages,
            total: data.total
          }
        })
        console.log(data)
        if (increasePage) {
          this.$store.commit('organization/ADD_FINISHED_MODELS', data.users)
        } else {
          this.$store.commit('organization/SET_FINISHED_MODELS', data.users)
        }
        this.$loading.hide()
      } catch (err) {
        this.$loading.hide()
        console.log(err)
      }
    },
    getInviteStatus (invite) {
      if (invite?.used) {
        return 'Used'
      } else if (invite?.createdAt < Date.now() - 1000 * 60 * 60 * 24 * 7) {
        return 'Expired'
      } else {
        return 'Pending'
      }
    },
    async search () {
      this.filterStatus = 'all'
      this.searchResults = []

      if (this.query.length < 2) {
        this.$loading.hide()
        return
      }

      this.$loading.show({
        title: 'Searching...'
      })

      try {
        const { success, data, errorMessage } = await this.$axios.$get('/organization/team/search', {
          params: {
            search: this.query,
            teamId: this.$store.state.organization.selectedTeamId,
            sortBy: this.sortBy
          }
        })

        if (!success) {
          throw new Error(errorMessage)
        }

        if (data.query === this.query) {
          this.searchResults = data.users.filter(user => user.modelStatus !== 'deleted')
        }
      } catch (err) {
        console.log(err)
        this.$toast.error('An error occurred while searching for users. Please try again later.')
      } finally {
        this.$loading.hide()
      }
    },
    debouncedSearch (event) {
      clearTimeout(this.debounceTimeout)
      this.debounceTimeout = setTimeout(() => {
        this.search()
      }, 300)
    }
  }
}
</script>
