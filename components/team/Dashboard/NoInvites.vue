<template>
  <div>
    <AppTitle title="Dashboard" sub-title="Everything you need to organize your team's AI photo shoot." with-mobile-actions>
      <!-- <ButtonWhite size="sm" class="shadow-sm" @click="manageInviteLinks">
        <IconLink class="w-4 h-4 mr-1.5" />
        <span>Manage invite links</span>
      </ButtonWhite> -->
      <ButtonPrimary size="sm" @click="$emit('invite')">
        <span>Invite member</span>
        <IconPlus class="!size-4 scale-125 ml-1.5" />
      </ButtonPrimary>
      <template #mobile>
        <ButtonPrimary size="sm" class="w-full" @click="$emit('invite')">
          <span>Invite member</span>
          <IconPlus class="!size-4 scale-125 ml-1.5" />
        </ButtonPrimary>
      </template>
    </AppTitle>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
      <div>
        <div class="space-y-4">
          <Card>
            <span class="bg-[#21B8BA] text-white text-[10px] font-bold uppercase px-[11px] h-5 inline-flex items-center justify-center rounded-full">Recommended</span>
            <div class="mt-6">
              <h3 class="text-lg font-semibold text-primary-500">
                Create your own headshots
              </h3>
              <p class="text-slate-500 font-medium">
                See the full experience your team members will go through by creating your own headshots first.
              </p>
              <ButtonPrimary size="sm" class="mt-4" @click="$router.push('/app/upload/intro')">
                <span>Start my photo shoot</span>
                <IconChevron class="w-4 h-4 ml-1.5" />
              </ButtonPrimary>
            </div>
          </Card>
          <!-- <Card>
            <h3 class="text-lg font-semibold text-primary-500">
              Here's how it works
            </h3>
            <p class="text-slate-500 font-medium">
              Want to see how HeadshotPro for Teams works? Watch our CEO explain in the video below.
            </p>
            <video src="https://cdn.coverr.co/videos/coverr-temp-uuela1926cb29078fd7c6e77cc521e3284b6smoothedmp4-2653/720p.mp4" class="w-full aspect-video rounded-lg mt-6" controls />
          </Card> -->
        </div>
      </div>
      <div>
        <div class="space-y-4">
          <Card>
            <span class="bg-[#21B8BA] text-white text-[10px] font-bold uppercase px-[11px] h-5 inline-flex items-center justify-center rounded-full">Recommended</span>
            <div class="mt-6">
              <h3 class="text-lg font-semibold text-primary-500">
                Invite team members via email
              </h3>
              <p class="text-slate-500 font-medium">
                We'll email each team member and invite them to start their AI photo shoot.
              </p>
              <div class="mt-6 flex items-end justify-start gap-2.5">
                <Input
                  class="w-full"
                  label="Email address"
                  placeholder="Add member by email"
                  :value="inviteEmail"
                  @input="inviteEmail = $event"
                />
                <ButtonWhite size="sm" class="h-[40px] shadow-sm" @click="inviteMember">
                  Invite
                </ButtonWhite>
              </div>
              <div class="relative flex justify-center items-center my-6">
                <div class="absolute left-0 right-0 top-1/2 -translate-y-1/2 bg-[#E4E4E7CC] h-px" />
                <span class="text-sm text-slate-400 font-medium px-[10px] bg-white relative">OR</span>
              </div>
              <h3 class="text-lg font-semibold text-primary-500">
                Invite team members via URL
              </h3>
              <p class="text-slate-500 font-medium">
                Create a bulk invite link you can send to the whole organisation. You can always easily invalidate this link later on for security.
              </p>
              <ButtonWhite size="sm" class="shadow-sm w-full mt-6" @click="$emit('invite-link')">
                Create invite link
              </ButtonWhite>
            </div>
          </Card>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import OrganizationMixin from '@/mixins/OrganizationMixin'

export default {
  mixins: [OrganizationMixin],
  data () {
    return {
      inviteEmail: '',
      isInviting: false
    }
  },
  methods: {
    manageInviteLinks () {
      // TODO: Implement
    },
    async inviteMember () {
      try {
        this.isInviting = true
        this.$loading.show({
          title: 'Inviting team member...'
        })

        if (!this.inviteEmail) {
          this.$toast.error('Please enter an email')
          return
        }

        if (!this.isValidEmail(this.inviteEmail)) {
          this.$toast.error(`${this.inviteEmail}` + ' is not a valid email')
          return
        }

        const { success, errorMessage } = await this.$axios.$post('/organization/invite', {
          emails: [this.inviteEmail]
        })

        if (!success) {
          this.$toast.open({
            message: errorMessage,
            type: 'error',
            duration: 5000
          })
          throw new Error(errorMessage)
        }

        this.$store.commit('organization/ADD_MANUAL_INVITES', { emails: [this.inviteEmail] })
        this.$toast.success('Invitations sent')
        this.inviteEmail = ''
        this.$emit('invited')
      } catch (err) {
        this.handleError(err)
      } finally {
        this.$loading.hide()
        this.isInviting = false
      }
    }
  }
}
</script>
