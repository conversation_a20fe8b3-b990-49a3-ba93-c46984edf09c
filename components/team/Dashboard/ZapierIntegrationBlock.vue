<template>
  <Card v-if="!isHidden" class="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200 relative space-y-4 md:space-y-0">
    <div class="flex items-start gap-4">
      <div class="w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-600 rounded-lg flex items-center justify-center flex-shrink-0">
        <IconBolt class="w-6 h-6 text-white" />
      </div>
      <div class="flex-1">
        <div class="flex items-start justify-between mb-2">
          <div>
            <h3 class="font-semibold text-lg text-slate-900">
              {{ $t('Automate with Zapier') }}
            </h3>
            <p class="text-sm text-slate-600">
              {{ $t('Connect HeadshotPro to your HR systems and automate team onboarding') }}
            </p>
          </div>
          <div class="flex gap-2 ml-4 hidden md:flex">
            <ButtonWhite size="xs" @click="openApiDocs">
              {{ $t('API docs') }}
            </ButtonWhite>
            <ButtonPrimary size="xs" @click="openZapierSetup">
              {{ $t('Setup') }}
            </ButtonPrimary>
          </div>
        </div>
        <div class="hidden md:grid grid-cols-1 sm:grid-cols-2 gap-3 mt-4">
          <div v-for="workflow in workflows" :key="workflow.title" class="flex items-center gap-2 text-sm text-slate-700 bg-white/60 rounded-md p-2">
            <component :is="workflow.icon" class="w-4 h-4 text-orange-600 flex-shrink-0" />
            <span class="font-medium">{{ workflow.title }}</span>
          </div>
        </div>
        <!-- <div class="mt-3 text-center">
          <button
            class="text-xs text-slate-500 hover:text-slate-700 underline underline-offset-2 transition-colors"
            @click="hideBlock"
          >
            {{ $t('Not interested') }}
          </button>
        </div> -->
        <div class="absolute top-[-6px] right-[-6px]">
          <button class="w-6 h-6 bg-black rounded-full flex items-center justify-center hover:scale-110 transition-transform cursor-pointer hover:bg-red-500" @click="hideBlock">
            <IconMiniXMark class="w-4 h-4 text-white" />
          </button>
        </div>
      </div>
    </div>
    <div class="flex gap-2 md:hidden w-full">
      <ButtonWhite class="w-1/2" size="xs" @click="openApiDocs">
        {{ $t('API docs') }}
      </ButtonWhite>
      <ButtonPrimary class="w-1/2" size="xs" @click="openZapierSetup">
        {{ $t('Setup') }}
      </ButtonPrimary>
    </div>
  </Card>
</template>

<script>
export default {
  data () {
    return {
      isHidden: false,
      workflows: [
        {
          title: this.$t('Auto-invite new hires'),
          icon: 'IconStar'
        },
        {
          title: this.$t('Sync with HRIS'),
          icon: 'IconArrowPathRoundedSquare'
        },
        {
          title: this.$t('Workday integration'),
          icon: 'IconBuildingOffice'
        },
        {
          title: this.$t('Update directories'),
          icon: 'IconUsers'
        }
      ]
    }
  },
  mounted () {
    // Check if user has previously hidden the Zapier block
    this.isHidden = localStorage.getItem('zapier-block-hidden') === 'true'
  },
  methods: {
    openApiDocs () {
      this.$router.push('/app/admin/api')
    },
    openZapierSetup () {
      // TODO: Put the right link here
      window.open('https://zapier.com/apps/headshotpro/integrations', '_blank')
    },
    hideBlock () {
      this.isHidden = true
      localStorage.setItem('zapier-block-hidden', 'true')
    }
  }
}
</script>

<i18n>
  {
    "en": {
      "Automate with Zapier": "Automate with Zapier",
      "Connect HeadshotPro to your HR systems and automate team onboarding": "Connect HeadshotPro to your HR systems and automate team onboarding",
      "API docs": "API docs",
      "Setup": "Setup",
      "Auto-invite new hires": "Auto-invite new hires",
      "Sync with HRIS": "Sync with HRIS",
      "Workday integration": "Workday integration",
      "Update directories": "Update directories",
      "Not interested": "Not interested"
    },
    "es": {
      "Automate with Zapier": "Automatizar con Zapier",
      "Connect HeadshotPro to your HR systems and automate team onboarding": "Conecta HeadshotPro a tus sistemas de RRHH y automatiza la incorporación del equipo",
      "API docs": "Docs API",
      "Setup": "Configurar",
      "Auto-invite new hires": "Auto-invitar nuevos empleados",
      "Sync with HRIS": "Sincronizar con HRIS",
      "Workday integration": "Integración Workday",
      "Update directories": "Actualizar directorios",
      "Not interested": "No me interesa"
    },
    "de": {
      "Automate with Zapier": "Mit Zapier automatisieren",
      "Connect HeadshotPro to your HR systems and automate team onboarding": "Verbinde HeadshotPro mit deinen HR-Systemen und automatisiere das Team-Onboarding",
      "API docs": "API-Docs",
      "Setup": "Einrichten",
      "Auto-invite new hires": "Neue Mitarbeiter automatisch einladen",
      "Sync with HRIS": "Mit HRIS synchronisieren",
      "Workday integration": "Workday-Integration",
      "Update directories": "Verzeichnisse aktualisieren",
      "Not interested": "Nicht interessiert"
    }
  }
</i18n>

<style>

</style>
