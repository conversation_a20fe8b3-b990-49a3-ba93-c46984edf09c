<template>
  <Card class="p-6">
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-lg font-semibold text-primary-500">
          Automatically top-up credits
        </h2>
        <p class="text-sm text-slate-500 mt-1">
          Purchase more credits when your available credits are running low.
        </p>
      </div>
      <InputToggle
        :active="automaticTopupSettings.automaticTopup"
        @change="toggleAutomaticTopup"
      >
        <span class="text-sm font-medium text-gray-700">
          {{ automaticTopupSettings.automaticTopup ? 'On' : 'Off' }}
        </span>
      </InputToggle>
    </div>

    <div v-if="automaticTopupSettings.automaticTopup" class="mt-6 space-y-4">
      <div class="grid grid-cols-1 gap-2">
        <div>
          <Input
            v-model.number="automaticTopupSettings.topupThreshold"
            class="md:flex md:items-center md:justify-between md:gap-2"
            input-class="md:w-16"
            label="Purchase credits when my available credits go below:"
            type="number"
            :min="1"
            :max="100"
            @input="updateTopupSettings"
          />
        </div>
        <div>
          <Input
            v-model.number="automaticTopupSettings.topupAmount"
            class="md:flex md:items-center md:justify-between md:gap-2"
            input-class="md:w-16"
            label="Additional credits to purchase:"
            type="number"
            :min="1"
            :max="1000"
            @input="updateTopupSettings"
          />
        </div>
      </div>

      <div>
        <p class="text-sm text-paragraph">
          You will <strong>purchase {{ automaticTopupSettings.topupAmount }} credits</strong> when your available credits go <strong>below {{ automaticTopupSettings.topupThreshold }} credits</strong>.
          At your current bulk-discount of {{ bulkDiscountPercent }}%, <strong>one credit costs {{ oneCreditPrice }}</strong>.
        </p>
      </div>

      <div class="!mt-6 border-t border-gray-200 pt-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-base font-semibold text-primary-500 mb-4">
            Your cards
          </h3>
          <ButtonWhite size="sm" @click="redirectToPaymentPortal">
            Manage
          </ButtonWhite>
        </div>

        <template v-if="paymentMethods.length > 0">
          <div v-if="isLoadingPaymentMethods" class="flex items-center justify-center py-8">
            <LoadingSpinnerGray class="!w-6 !h-6" />
          </div>

          <div v-else-if="paymentMethods.length === 0" class="text-center py-8">
            <p class="text-sm text-paragraph mb-4">
              No payment methods found
            </p>
            <ButtonWhite size="sm" class="w-full" @click="redirectToPaymentPortal">
              Add a new card
            </ButtonWhite>
          </div>

          <div v-else class="space-y-3">
            <div
              v-for="card in paymentMethods"
              :key="card.id"
              class="border rounded-lg p-4 relative"
              :class="{
                'border-teal-500 bg-teal-50': card.isDefault && !card.hasIssue,
                'border-red-400 bg-red-50': card.hasIssue,
                'border-gray-200': !card.isDefault && !card.hasIssue
              }"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-start space-x-3">
                  <div class="flex-shrink-0">
                    <div v-if="card.brand === 'visa'">
                      <img src="@/assets/img/payment-methods/visa.svg" class="w-auto h-6">
                    </div>
                    <div v-else-if="card.brand === 'mastercard'">
                      <img src="@/assets/img/payment-methods/mastercard.svg" class="w-auto h-6">
                    </div>
                    <div v-else class="w-9 h-6 bg-gray-400 rounded text-white text-xs flex items-center justify-center font-bold">
                      {{ card.brand.toUpperCase().slice(0, 2) }}
                    </div>
                  </div>

                  <div>
                    <p
                      class="text-sm font-medium"
                      :class="{
                        'text-primary-500': card.isDefault && !card.hasIssue,
                        'text-red-500': card.hasIssue,
                        'text-gray-700': !card.isDefault && !card.hasIssue
                      }"
                    >
                      {{ card.brand.charAt(0).toUpperCase() + card.brand.slice(1) }} ending in {{ card.last4 }}
                    </p>
                    <p class="text-xs text-gray-500">
                      Expiry {{ String(card.expMonth).padStart(2, '0') }}/{{ card.expYear }}
                    </p>
                    <div class="flex gap-2 text-xs font-medium mt-2">
                      <button v-if="!card.isDefault && !card.hasIssue" type="button" class="text-gray-500 hover:underline" @click="setDefaultCard(card.id)">
                        Set as default
                      </button>
                      <button v-if="card.hasIssue" type="button" class="text-gray-500 hover:underline" @click="redirectToPaymentPortal">
                        Resolve issue
                      </button>
                      <button type="button" class="text-red-500 hover:underline" @click="redirectToPaymentPortal">
                        Remove
                      </button>
                    </div>
                  </div>

                  <IconCircledCheck v-if="card.isDefault" class="w-5 h-5 text-teal-500 absolute top-2 right-2" color="currentColor" />
                </div>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <AlertWarningV2>
            You don't have any payment methods added. Please add a payment method or we will not be able to automatically top-up your credits.
          </AlertWarningV2>
        </template>

        <ButtonWhite size="sm" class="w-full mt-4" @click="redirectToPaymentPortal">
          Add a new card
        </ButtonWhite>
      </div>

      <div class="flex items-center justify-start mt-4">
        <span class="text-xs text-gray-500">Securely hosted by</span>
        <img src="@/assets/img/stripe.svg" class="h-8 w-auto ml-1">
      </div>
    </div>
    </div>
  </Card>
</template>

<script>
export default {
  data () {
    return {
      isLoadingPaymentMethods: false,
      invitesPending: 0,
      paymentMethods: [],
      automaticTopupSettings: {
        automaticTopup: false,
        topupThreshold: 1,
        topupAmount: 1,
        topupFailedAttempts: 0
      },
      updateTimeout: null,
      priceDetails: null
    }
  },
  computed: {
    bulkDiscountPercent () {
      const { totalCreditsPurchased } = this.$store.state.organization.organization
      const discountOptions = this.teamDiscountOptions
      const discount = discountOptions.find(option => totalCreditsPurchased >= option.from && totalCreditsPurchased <= option.to)
      return discount.discount
    },
    oneCreditPrice () {
      console.log(this.price, this.bulkDiscountPercent)
      return this.formatPrice(this.price * (100 - this.bulkDiscountPercent) / 10000, this.userCurrency, 2, false)
    },
    priceId () {
      return 'medium'
    },
    price () {
      try {
        return this.priceDetails?.currency[this.userCurrency] ?? this.priceDetails?.price
      } catch (err) {
        return this.priceDetails?.price
      }
    }
  },
  mounted () {
    this.getPackage()
    this.loadPaymentMethods()
  },
  methods: {
    async getPackage (id = null) {
      try {
        this.$loading.show({
          title: 'Loading price...'
        })
        const { success, data, errorMessage } = await this.$axios.$get(`/checkout/price?productId=package&priceId=${this.priceId}`)

        if (!success) {
          throw new Error(errorMessage)
        }

        if (!data?.product) {
          throw new Error('Product not found')
        }

        if (!data?.price) {
          throw new Error('Price not found')
        }

        const { product, price } = data

        this.priceDetails = price

        if (product) {
          const { description, images, name } = product
          this.product = { description, name, image: images[0] }
        }
      } catch (err) {
        this.handleError(err)
      } finally {
        this.$loading.hide()
      }
    },
    async loadPaymentMethods () {
      try {
        this.isLoadingPaymentMethods = true
        const { success, data, errorMessage } = await this.$axios.$get('/organization/payment-methods')
        if (!success) {
          throw new Error(errorMessage)
        }

        this.paymentMethods = data.cards || []
        this.automaticTopupSettings = {
          automaticTopup: data.automaticTopup || false,
          topupThreshold: data.topupThreshold || 5,
          topupAmount: data.topupAmount || 10,
          topupFailedAttempts: data.topupFailedAttempts || 0
        }
      } catch (err) {
        console.error('Error loading payment methods:', err)
        this.handleError(err)
      } finally {
        this.isLoadingPaymentMethods = false
      }
    },

    async toggleAutomaticTopup () {
      const newValue = !this.automaticTopupSettings.automaticTopup
      this.automaticTopupSettings.automaticTopup = newValue

      try {
        const { success, errorMessage } = await this.$axios.$post('/organization/payment-methods/automatic-topup', {
          automaticTopup: newValue,
          topupThreshold: this.automaticTopupSettings.topupThreshold,
          topupAmount: this.automaticTopupSettings.topupAmount
        })

        if (!success) {
          throw new Error(errorMessage)
        }
      } catch (err) {
        // Revert on error
        this.automaticTopupSettings.automaticTopup = !newValue
        console.error('Error updating automatic top-up:', err)
        this.handleError(err)
      }
    },

    updateTopupSettings () {
      // Debounce the API call to avoid too many requests
      if (this.updateTimeout) {
        clearTimeout(this.updateTimeout)
      }

      this.updateTimeout = setTimeout(async () => {
        try {
          const { success, errorMessage } = await this.$axios.$post('/organization/payment-methods/automatic-topup', {
            automaticTopup: this.automaticTopupSettings.automaticTopup,
            topupThreshold: this.automaticTopupSettings.topupThreshold,
            topupAmount: this.automaticTopupSettings.topupAmount
          })

          if (!success) {
            throw new Error(errorMessage)
          }
        } catch (err) {
          console.error('Error updating top-up settings:', err)
          this.handleError(err)
        }
      }, 1000)
    },

    async setDefaultCard (paymentMethodId) {
      try {
        this.$loading.show({ title: 'Setting default payment method...' })

        const { success, errorMessage } = await this.$axios.$post('/organization/payment-methods/set-default', {
          paymentMethodId
        })

        if (!success) {
          throw new Error(errorMessage)
        }

        // Update local state
        this.paymentMethods = this.paymentMethods.map(card => ({
          ...card,
          isDefault: card.id === paymentMethodId
        }))
      } catch (err) {
        console.error('Error setting default payment method:', err)
        this.handleError(err)
      } finally {
        this.$loading.hide()
      }
    },

    async redirectToPaymentPortal () {
      try {
        this.$loading.show({ title: 'Redirecting to payment portal...' })

        const { success, data, errorMessage } = await this.$axios.$post('/organization/payment-methods/customer-portal')

        if (!success) {
          throw new Error(errorMessage)
        }

        // Redirect to Stripe customer portal
        window.location.href = data.url
      } catch (err) {
        console.error('Error opening customer portal:', err)
        this.handleError(err)
      } finally {
        this.$loading.hide()
      }
    }
  }
}
</script>
