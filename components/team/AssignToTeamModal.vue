<template>
  <div class="flex flex-col gap-4 p-8">
    <div class="flex flex-col space-y-0.5">
      <h2 class="text-lg font-bold text-primary-500">
        Assign to team
      </h2>
      <p class="text-paragraph text-sm">
        Assign the user to a team and make it easier to manage them.
      </p>
    </div>

    <div class="flex flex-col gap-3">
      <InputSelect :value="selectedTeam" :options="teams" placeholder="Select team" @input="selectedTeam = $event" />
      <ButtonPrimary size="sm" :is-disabled="!selectedTeam" @click="assignTeam">
        Assign to team
      </ButtonPrimary>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    user: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      selectedTeam: null
    }
  },
  computed: {
    teams () {
      return (this.$store.state.organization.organization?.teams || []).map(team => ({
        title: team.name,
        value: team.id
      }))
    }
  },
  methods: {
    async assignTeam () {
      try {
        this.$loading.show({
          title: 'Assigning user to team...'
        })

        const response = await this.$axios.$post(`/organization/teams/${this.selectedTeam}/add-members`, {
          userIds: [this.user._id]
        })

        if (response.success) {
          this.$store.commit('organization/UPDATE_MEMBER_TEAM', {
            email: this.user.email,
            uid: this.user.uid,
            teamId: this.selectedTeam
          })

          this.$emit('closeModal')
          this.$toast.success('User assigned to team')
        } else {
          this.$toast.error('Failed to assign user to team. Please, try again later.')
        }
      } catch (error) {
        this.handleError(error)
      } finally {
        this.$loading.hide()
      }
    }
  }
}
</script>
