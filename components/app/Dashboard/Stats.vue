<template>
  <div class="px-4 sm:px-0">
    <dl class="mt-2 grid grid-cols-1 gap-5 sm:grid-cols-3">
      <div class="group relative overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
        <dt class="truncate text-sm font-medium text-gray-500">
          Headshots Selected
        </dt>
        <LoadingWrapper :is-loading="selectedImages.isLoading">
          <dd class="text-5xl font-medium tracking-tight text-gray-900">
            {{ selectedImages.total || 0 }}
          </dd>
        </LoadingWrapper>
        <!-- <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="absolute top-4 right-4 h-4 w-4 text-gray-400 transition-all group-hover:translate-x-1"
        >
          <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12h15m0 0l-6.75-6.75M19.5 12l-6.75 6.75" />
        </svg> -->
      </div>

      <div class="group relative overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
        <dt class="truncate text-sm font-medium text-gray-500">
          Total Team Members
        </dt>
        <LoadingWrapper :is-loading="teamMembers.isLoading">
          <dd class="text-5xl font-medium tracking-tight text-gray-900">
            {{ teamMembers.total || 0 }}
          </dd>
        </LoadingWrapper>
        <!-- <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="absolute top-4 right-4 h-4 w-4 text-gray-400 transition-all group-hover:translate-x-1"
        >
          <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12h15m0 0l-6.75-6.75M19.5 12l-6.75 6.75" />
        </svg> -->
      </div>

      <NuxtLink to="/app/admin/credits" class="group relative overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6">
        <dt class="truncate text-sm font-medium text-gray-500">
          Credits Left
        </dt>
        <LoadingWrapper :is-loading="creditsLefts.isLoading">
          <dd class="text-5xl font-medium tracking-tight text-gray-900">
            {{ creditsLefts.total || 0 }}
          </dd>
        </LoadingWrapper>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="absolute top-4 right-4 h-4 w-4 text-gray-400 transition-all group-hover:translate-x-1"
        >
          <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12h15m0 0l-6.75-6.75M19.5 12l-6.75 6.75" />
        </svg>
      </NuxtLink>
    </dl>
  </div>
</template>

<script>
export default {
  computed: {
    selectedImages () {
      const images = this.$store.state.organization?.selectedImages
      if (!images) {
        return {
          total: 0,
          isLoading: true
        }
      }
      return {
        total: images.length,
        isLoading: false
      }
    },
    teamMembers () {
      const total = this.$store.state.organization?.totalTeamSize
      if (!total) {
        return {
          total: 0,
          isLoading: true
        }
      }
      return {
        total: this.$store.state.organization.totalTeamSize,
        isLoading: false
      }
    },
    creditsLefts () {
      const creditsLeft = this.$store.state?.organization?.creditsLeft
      if (!creditsLeft && creditsLeft !== 0) {
        return {
          total: 0,
          isLoading: true
        }
      }
      return {
        total: creditsLeft,
        isLoading: false
      }
    }
  }
}
</script>

<style></style>
