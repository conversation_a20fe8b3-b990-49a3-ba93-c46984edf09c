<template>
  <header>
    <div class="flex flex-col items-start md:items-center justify-between md:flex-row">
      <div>
        <h1 v-if="title" class="text-2xl font-bold leading-tight tracking-tight text-primary-500">
          {{ title }}
        </h1>
        <p v-if="subTitle" class="text-base font-medium text-slate-500">
          {{ subTitle }}
        </p>
        <slot name="description" />
      </div>
      <div class="hidden md:flex items-center gap-3">
        <slot />
      </div>
      <div v-if="withMobileActions" class="flex flex-col items-center justify-center gap-3 fixed bottom-0 left-0 w-full z-50 bg-white p-4 border-t border-gray-200 shadow-sm md:hidden">
        <slot name="mobile" />
      </div>
    </div>
  </header>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: null
    },
    subTitle: {
      type: String,
      default: null
    },
    withMobileActions: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style></style>
