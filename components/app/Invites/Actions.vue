<template>
  <div class="relative ml-3">
    <template v-if="inviteStatus === 'Pending' || inviteStatus === 'Expired'">
      <div class="flex gap-3 justify-end">
        <template v-if="inviteStatus === 'Expired'">
          <ButtonWhite size="xs" @click="resendInvite()">
            Resend
          </ButtonWhite>
        </template>
        <template v-if="inviteStatus === 'Pending'">
          <ButtonWhite size="xs" @click="sendInviteReminder()">
            Send reminder
          </ButtonWhite>
        </template>
        <template v-if="inviteStatus === 'Pending'">
          <ButtonWhiteDelete size="xs" @click="revokeInvite()">
            Revoke
          </ButtonWhiteDelete>
        </template>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    invite: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      showDropdown: false
    }
  },
  computed: {
    inviteStatus () {
      const { invite } = this
      if (invite?.used) {
        return 'Used'
      } else if (invite?.createdAt < Date.now() - 1000 * 60 * 60 * 24 * 7) {
        return 'Expired'
      } else {
        return 'Pending'
      }
    }
  },
  methods: {
    async revokeInvite () {
      try {
        const { success, errorMessage } = await this.$axios.$post('/organization/invite/revoke', {
          _id: this.invite._id
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.$toast.success('Invite revoked')
        this.$emit('reloadInvites')
        this.showDropdown = false
      } catch (err) {
        this.handleError(err)
      }
    },
    async resendInvite () {
      try {
        const { success, errorMessage } = await this.$axios.$post('/organization/invite', {
          emails: [this.invite.email]
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.$toast.success('Invite resent')
        this.$emit('reloadInvites')
        this.showDropdown = false
      } catch (err) {
        this.handleError(err)
      }
    },
    async sendInviteReminder () {
      try {
        if (!this.invite.email) {
          return this.$toast.error('No email provided')
        }
        const { success, errorMessage } = await this.$axios.$post('/organization/remind-user', {
          email: this.invite.email
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.$emit('reminded')
        this.$toast.success('User has been reminded')
      } catch (err) {
        this.handleError(err)
      } finally {
        this.showDropdown = false
      }
    },
    copyInviteLink () {
      const el = document.createElement('textarea')
      el.value = `${this.$config.BASE_URL}/auth/signup?invite=${this.invite.uid}`
      document.body.appendChild(el)
      el.select()
      document.execCommand('copy')
      document.body.removeChild(el)
      this.$toast.success('Invite link copied')
      this.showDropdown = false
    }
  }
}
</script>

<style></style>
