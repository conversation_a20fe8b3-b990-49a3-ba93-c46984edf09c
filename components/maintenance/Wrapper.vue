<template>
  <div>
    <div v-if="isMaintenanceMode" class="min-h-screen w-full flex items-center justify-center bg-gray-50">
      <div class="text-center px-6">
        <div class="mb-8">
          <Logo class="w-[150px] text-black mx-auto" />
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">
          We'll be back soon!
        </h2>
        <p class="text-gray-600 mb-6">
          We're currently performing scheduled maintenance to improve our service.<br>
          Please check back in a little while.
        </p>
        <div class="text-sm text-gray-500">
          Expected duration: {{ maintenanceEndTime || 'A few hours' }}
        </div>
      </div>
    </div>
    <slot v-else />
  </div>
</template>

<script>
export default {
  data () {
    return {
      maintenanceEndTime: null // This can be set from your backend or environment
    }
  }
}
</script>
