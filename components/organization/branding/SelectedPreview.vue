<template>
  <div class="flex space-x-2.5 items-center justify-start">
    <div
      class="w-[52px] h-[52px] bg-gray-200 rounded-lg border border-gray-100"
      :style="backgroundStyle"
    />
    <span class="text-black text-2xl font-thin">
      +
    </span>
    <ImageDns :src="require('@/assets/img/branding/thumbnail.png')" class="h-[52px] rounded-lg" />
    <span class="text-black text-2xl font-thin">
      =
    </span>
    <div
      class="w-[64px] h-[64px] bg-gray-200 rounded-full flex items-center justify-center overflow-hidden"
      :style="backgroundStyle"
    >
      <ImageDns :src="require('@/assets/img/branding/thumbnail-transparent.png')" class="w-[64px] rounded-lg" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'BrandingPreview',
  props: {
    backgroundColor: {
      type: String,
      default: null
    },
    backgroundImage: {
      type: String,
      default: null
    }
  },
  computed: {
    backgroundStyle () {
      if (this.backgroundColor) {
        return {
          backgroundColor: this.backgroundColor,
          backgroundSize: 'cover'
        }
      }
      if (this.backgroundImage) {
        return {
          backgroundImage: `url(${this.backgroundImage})`,
          backgroundPosition: 'center center',
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover'
        }
      }
      return {}
    }
  }
}
</script>
