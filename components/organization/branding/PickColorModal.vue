<template>
  <Popup v-if="showModal" size="xl" @closeModal="showModal = false">
    <div class="flex flex-col gap-4 p-2">
      <div class="flex flex-col space-y-0.5">
        <h2 class="text-lg font-medium">
          Pick a background color
        </h2>
        <p class="text-gray-500 text-sm">
          Select the background you want to use for your teams profile pictures.
        </p>
      </div>
      <InputColorPicker :start-color="startColor" @select="handleColorSelect" />
      <div class="flex flex-col gap-2">
        <div class="flex flex-col">
          <h3 class="text-md font-medium">
            Preview
          </h3>
          <p class="text-gray-500 text-sm">
            This is how your branded profile picture will look like.
          </p>
        </div>
        <OrganizationBrandingSelectedPreview :background-color="selectedColor" />
        <div class="flex space-x-2.5 items-center justify-between w-full pt-4">
          <ButtonWhite size="sm" @click="showModal = false">
            Cancel
          </ButtonWhite>
          <ButtonPrimary size="sm" @click="saveColor">
            <span>Select color</span>
            <IconChevron class="ml-1.5 h-4 w-4" />
          </ButtonPrimary>
        </div>
      </div>
    </div>
  </Popup>
</template>

<script>

export default {
  name: 'PickColorModal',
  props: {
    startColor: {
      type: String,
      default: '#00CEF3'
    }
  },
  data () {
    return {
      showModal: false,
      selectedColor: this.startColor
    }
  },
  methods: {
    open () {
      this.showModal = true
    },
    close () {
      this.showModal = false
    },
    handleColorSelect (color) {
      this.selectedColor = color
    },
    saveColor () {
      // Check if color is valid
      if (!this.selectedColor) {
        this.$toast.error('Please select a color')
        return
      }

      // Validate hex code format
      const hexCode = this.selectedColor.replace('#', '')
      if (!/^[0-9A-Fa-f]{6}$/.test(hexCode)) {
        this.$toast.error('Please enter a valid hex color code')
        return
      }

      this.$emit('save', this.selectedColor)
    }
  }
}
</script>
