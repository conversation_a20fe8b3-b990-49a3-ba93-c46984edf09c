<template>
  <Popup v-if="showModal" size="xl" @closeModal="showModal = false">
    <div class="flex flex-col gap-4 p-2">
      <div class="flex flex-col space-y-0.5">
        <h2 class="text-lg font-bold text-primary-500">
          Upload custom background
        </h2>
        <p class="text-paragraph text-sm">
          Select the background you want to use for your team's profile pictures.
        </p>
      </div>

      <div
        class="bg-[#F5F5F5] rounded-lg p-8 cursor-pointer group"
        :class="{ 'border-2 border-dashed border-primary bg-primary/5': isDragging }"
        @click="$refs.fileInput.click()"
        @dragenter.prevent="isDragging = true"
        @dragover.prevent="isDragging = true"
        @dragleave.prevent="isDragging = false"
        @drop.prevent="handleFileDrop"
      >
        <div class="flex flex-col items-center justify-center">
          <div class="flex flex-col items-center justify-center text-center">
            <div class="mb-4">
              <IconCustomCloudUpload :circle-class="isDragging ? 'animate-bounce' : 'group-hover:animate-bounce'" />
            </div>
            <h3 class="text-base font-medium" :class="{ 'text-primary': isDragging, 'text-[#101828]': !isDragging }">
              {{ isDragging ? 'Drop your file here' : 'Start by uploading a file' }}
            </h3>
            <p class="mt-1 text-sm" :class="isDragging ? 'text-primary/80' : 'text-gray-500'">
              Minimum size: 512x512px<br>
              PNG, JPG, or WebP
            </p>
          </div>
        </div>
      </div>

      <div class="flex justify-start gap-3">
        <input
          ref="fileInput"
          type="file"
          class="hidden"
          accept="image/*"
          :disabled="isUploading"
          @change="handleFileUpload"
        >
        <ButtonWhite size="sm" @click="showModal = false">
          Cancel
        </ButtonWhite>
        <ButtonPrimary
          :disabled="isUploading"
          class="cursor-pointer flex-1"
          size="sm"
          @click="$refs.fileInput.click()"
        >
          <IconUpload class="mr-2 -ml-1 h-4 w-4" />
          <span>{{ isUploading ? 'Uploading...' : 'Select file' }}</span>
        </ButtonPrimary>
      </div>
    </div>
  </Popup>
</template>

<script>
export default {
  name: 'UploadImageModal',
  props: {
    isUploading: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      showModal: false,
      isDragging: false
    }
  },
  methods: {
    open () {
      this.showModal = true
    },
    close () {
      this.showModal = false
    },
    async handleFileUpload (event) {
      const file = event.target.files[0]
      if (!file) { return }

      // Validate file type
      if (!file.type.startsWith('image/')) {
        this.$toast.error('Please upload an image file')
        event.target.value = ''
        return
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        this.$toast.error('File size should be less than 5MB')
        event.target.value = ''
        return
      }

      try {
        // Check image dimensions using FileReader
        const dimensions = await new Promise((resolve, reject) => {
          const reader = new FileReader()
          reader.onload = (e) => {
            const img = new Image()
            img.onload = () => {
              resolve({ width: img.width, height: img.height })
            }
            img.onerror = () => reject(new Error('Failed to load image'))
            img.src = e.target.result
          }
          reader.onerror = () => reject(new Error('Failed to read file'))
          reader.readAsDataURL(file)
        })

        if (dimensions.width < 512 || dimensions.height < 512) {
          this.$toast.error('Image must be at least 512px in both width and height')
          event.target.value = ''
          return
        }
      } catch (error) {
        this.$toast.error('Failed to validate image dimensions')
        event.target.value = ''
        return
      }

      this.$emit('upload', file)
      event.target.value = ''
    },
    handleFileDrop (event) {
      this.isDragging = false
      const file = event.dataTransfer?.files?.[0]
      if (!file) { return }

      // Validate file type
      if (!file.type.startsWith('image/')) {
        this.$toast.error('Please upload an image file')
        return
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        this.$toast.error('File size should be less than 5MB')
        return
      }

      // Create a fake change event to reuse existing upload logic
      const fakeEvent = { target: { files: [file] } }
      this.handleFileUpload(fakeEvent)
    }
  }
}
</script>
