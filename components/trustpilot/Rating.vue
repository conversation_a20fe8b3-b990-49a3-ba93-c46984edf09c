<template>
  <div>
    <div class="hidden md:flex items-center gap-2">
      <img class="h-6 w-auto" src="@/assets/img/trustpilot-stars-5.svg" alt="" loading="lazy">
      <p class="tracking-tight">
        <span class="text-gray-700">{{ $t('Rated') }} <strong class="text-black">{{ $store.state.trustpilot.stars }}</strong> {{ $t('out of 5 with') }}</span>
        <span class="text-gray-700"><strong class="text-black">{{ $store.state.trustpilot.totalReviews.toLocaleString() }}</strong> {{ $t('reviews on') }}</span>
      </p>
      <img class="h-6 w-auto" src="@/assets/img/landing-page/logo-trustpilot.png" alt="" loading="lazy">
    </div>
    <div class=" md:hidden items-center gap-1 flex">
      <div class="flex items-center gap-2 justify-center">
        <img class="h-4 w-auto" src="@/assets/img/trustpilot-stars-4.5.svg" alt="" loading="lazy">
        <span class="text-xs text-gray-800"><strong class="text-black">{{ $store.state.trustpilot.stars }}</strong>{{ $t('/5 of') }}</span>
      </div>
      <div class="flex items-center gap-1 justify-center">
        <span class="text-xs  text-gray-800"><strong class="text-black">{{ $store.state.trustpilot.totalReviews.toLocaleString() }}</strong> {{ $t('reviews') }}</span>
        <img class="h-4 md:h-5 w-auto" src="@/assets/img/landing-page/logo-trustpilot.png" alt="" loading="lazy">
      </div>
    </div>
  </div>
</template>

<script>
export default {

}
</script>

<i18n>
  {
    "en": {
      "Rated": "Rated",
      "out of 5 with": "out of 5 with",
      "reviews on": "reviews on",
      "/5 of": "/5 of",
      "reviews": "reviews"
    },
    "es": {
      "Rated": "Valorado",
      "out of 5 with": "de 5 con",
      "reviews on": "reseñas en",
      "/5 of": "/5 de",
      "reviews": "reseñas"
    },
    "de": {
      "Rated": "Bewertet mit",
      "out of 5 with": "von 5 mit",
      "reviews on": "Bewertungen auf",
      "/5 of": "/5 von",
      "reviews": "Bewertungen"
    }
  }
</i18n>

<style>

</style>
