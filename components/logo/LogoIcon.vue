<template>
  <svg xmlns="http://www.w3.org/2000/svg" width="93" height="92" viewBox="0 0 93 92" fill="none">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M42.6076 2.86208C42.6234 2.45541 42.5489 2.05032 42.3894 1.6759C42.2298 1.30147 41.9893 0.967017 41.6851 0.69656C41.3809 0.4261 41.0208 0.226353 40.6301 0.111673C40.2396 -0.00300746 39.8285 -0.0297771 39.4263 0.0332882C27.9406 1.82709 17.5366 7.83874 10.2483 16.8932C9.9958 17.2105 9.8162 17.5793 9.72195 17.9736C9.62763 18.368 9.62106 18.7782 9.70282 19.1753C9.78452 19.5724 9.95242 19.9468 10.1948 20.2719C10.4371 20.597 10.7479 20.8649 11.1051 21.0568C20.5433 26.0703 29.3572 30.008 35.2884 26.5882C41.2196 23.1684 42.2224 13.4934 42.6076 2.86208ZM82.8366 20.2745C82.5928 20.5995 82.2806 20.8666 81.9213 21.0567C72.5163 26.0436 63.656 30.0146 57.7182 26.5881C51.7803 23.1617 50.7907 13.5598 50.4121 2.88199C50.3986 2.4762 50.4748 2.07241 50.6352 1.69939C50.7956 1.32637 51.0362 0.993266 51.34 0.723799C51.6437 0.454335 52.0032 0.25514 52.3927 0.140373C52.7822 0.0256069 53.1922 -0.00189148 53.5936 0.0598205C65.0802 1.83846 75.4886 7.84179 82.7781 16.8932C83.0332 17.2096 83.2145 17.5783 83.3101 17.9732C83.4058 18.3679 83.4131 18.779 83.3314 19.1768C83.249 19.5746 83.0803 19.9495 82.8366 20.2745ZM85.8135 64.1728C86.1575 64.3883 86.5447 64.5255 86.9472 64.5744C87.3504 64.6234 87.7589 64.5831 88.1441 64.4564C88.53 64.3296 88.8827 64.1194 89.1776 63.8409C89.4731 63.5624 89.703 63.2222 89.8517 62.8447C94.0494 52.008 94.0494 39.9948 89.8517 29.158C89.7036 28.7807 89.4745 28.4407 89.1802 28.1622C88.8853 27.8836 88.5333 27.6734 88.1481 27.5466C87.7629 27.4198 87.355 27.3794 86.9525 27.4284C86.55 27.4774 86.1635 27.6145 85.8201 27.83C76.7473 33.461 68.9231 39.1452 68.9231 45.9715C68.9231 52.7978 76.7937 58.5218 85.8135 64.1728ZM7.18647 27.8565C6.84262 27.641 6.45554 27.5039 6.05268 27.4548C5.64982 27.4058 5.24099 27.4462 4.85548 27.573C4.46997 27.6997 4.11715 27.9099 3.82206 28.1884C3.52697 28.467 3.29689 28.8071 3.14814 29.1846C-1.04938 40.0214 -1.04938 52.0345 3.14814 62.8713C3.29689 63.2488 3.52697 63.5889 3.82206 63.8674C4.11715 64.146 4.46997 64.3561 4.85548 64.4829C5.24099 64.6096 5.64982 64.65 6.05268 64.601C6.45554 64.552 6.84262 64.4149 7.18647 64.1994C16.246 58.5418 24.0635 52.8775 24.0701 46.0313C24.0768 39.185 16.2128 33.4809 7.18647 27.8565ZM11.0785 70.9459C10.7194 71.1359 10.407 71.4035 10.1632 71.7282C9.91955 72.0529 9.75071 72.4281 9.66888 72.8258C9.58699 73.2236 9.59403 73.6346 9.6896 74.0297C9.78518 74.4242 9.96704 74.7934 10.2218 75.1095C17.5209 84.1703 27.945 90.1745 39.4462 91.9429C39.8473 92.0046 40.2571 91.9774 40.6463 91.8625C41.0356 91.7476 41.3946 91.5484 41.6979 91.2788C42.0012 91.0092 42.2411 90.6759 42.4007 90.3027C42.5603 89.9302 42.6354 89.5264 42.6209 89.1207C42.249 78.4429 41.2527 68.8409 35.3148 65.4146C29.377 61.9881 20.4834 65.9591 11.0785 70.9459ZM50.617 90.3266C50.4574 89.9527 50.3829 89.547 50.3989 89.1406C50.7775 78.5093 51.7738 68.8343 57.7117 65.4145C63.6495 61.9947 72.4632 65.9325 81.8947 70.9459C82.2527 71.1372 82.5642 71.4048 82.8073 71.7295C83.0504 72.0549 83.2191 72.4294 83.3008 72.8265C83.3825 73.2243 83.3759 73.6346 83.2809 74.0291C83.1859 74.4242 83.0053 74.7927 82.7515 75.1095C75.4674 84.1656 65.0649 90.1778 53.5803 91.9694C53.1782 92.0292 52.7679 92.0013 52.3779 91.8864C51.9879 91.7715 51.6276 91.5736 51.3221 91.3054C51.0175 91.0351 50.7767 90.7011 50.617 90.3266ZM57.5653 46.0047C57.5653 52.1145 52.6112 57.0676 46.4999 57.0676C40.3886 57.0676 35.4345 52.1145 35.4345 46.0047C35.4345 39.8948 40.3886 34.9418 46.4999 34.9418C52.6112 34.9418 57.5653 39.8948 57.5653 46.0047Z"
      fill="url(#paint0_linear_21_80)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_21_80"
        x1="0"
        y1="0"
        x2="92.9656"
        y2="92.3227"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#3F07F0" />
        <stop offset="0.9999" stop-color="#5A71F2" />
      </linearGradient>
    </defs>
  </svg>
</template>

<script>
export default {
  name: 'LogoIcon'
}
</script>

<style></style>
