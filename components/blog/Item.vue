<template>
  <div
    class="p-6 relative bg-white border rounded-lg border-primary-500/15 shadow-[0px_0px_75px_0px_rgba(0,0,0,0.07)] flex flex-col justify-between"
  >
    <div class="flex-1 space-y-2">
      <nuxt-link :to="url">
        <img class="object-cover w-full" :src="photoUrl" alt="">
      </nuxt-link>

      <p class="text-base font-bold sm:text-lg text-primary-500 tracking-[-0.3px]">
        {{ postTitle }}
        <nuxt-link :to="url" class="">
          <span class="absolute inset-0" aria-hidden="true" />
        </nuxt-link>
      </p>
      <p class="text-sm tracking-[-0.3px] font-medium text-[#474368]">
        {{ formatDate(date) }}
      </p>
      <p class="text-sm font-normal tracking-[-0.3px] text-paragraph">
        {{ postExcerpt }}
      </p>
    </div>

    <div class="mt-3">
      <nuxt-link
        :to="url"
        class="text-base flex items-center justify-center w-full font-medium text-[#240D0D] border border-[#E6E6E6] hover:opacity-80 transition-all duration-150 bg-[#EEEEEE] rounded-lg px-4 pt-2 pb-2.5 h-10"
        role="button"
      >
        Read article
      </nuxt-link>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: false,
      default: null
    },
    href: {
      type: String,
      required: false,
      default: null
    },
    thumbnailUrl: {
      type: String,
      required: false,
      default: null
    },
    title: {
      type: String,
      required: false,
      default: null
    },
    createdAt: {
      type: String,
      required: false,
      default: null
    },
    excerpt: {
      type: String,
      required: false,
      default: null
    }
  },
  computed: {
    url () {
      return this.href || '/blog/' + this.item.slug
    },
    photoUrl () {
      return this.thumbnailUrl || 'https://storage.googleapis.com/headshotpro-public-content/' + this.item.thumbnail.small
    },
    postTitle () {
      return this.title || this.item.title
    },
    date () {
      return this.createdAt || this.item.createdAt
    },
    postExcerpt () {
      return this.excerpt || this.item.excerpt
    }
  }
}
</script>

<style>

</style>
