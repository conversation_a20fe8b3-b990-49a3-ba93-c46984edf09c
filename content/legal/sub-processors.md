---
title: Sub-Processors
description: HeadshotPro uses certain sub-processors (including third parties, as listed below) and content delivery networks to assist it in providing the HeadshotPro Services as described in the Master Subscription and Professional Services Agreement (“MSA”). Defined terms used herein shall have the same meaning as defined in the MSA.
---

## What is a Sub-processor?
A sub-processor is a third-party data processor engaged by HeadshotPro, including entities from within HeadshotPro, who has or potentially will have access to or process Customer Content containing personal information. HeadshotPro engages different types of sub-processors to perform various functions as explained in the tables below.

### Due Diligence & Contractual Safeguards
HeadshotPro evaluates the security, privacy and confidentiality practices of proposed sub-processors that will or may have access to or otherwise process Customer Content and enters into Data Protection Agreements with each such sub-processor.

This advisory does not give Customers any additional rights or remedies and should not be construed as a binding agreement. The information herein is only provided to illustrate HeadshotPro’s engagement process for sub-processors as well as to provide the actual list of third party sub-processors and content delivery networks used by HeadshotPro as of the date of this advisory (which HeadshotPro may use in the delivery and support of its Services).

If you are a HeadshotPro customer and wish to enter into our DPA, please email <NAME_EMAIL>.

### Process to Engage New Sub-processors:
HeadshotPro provides notice via this advisory of updates to the list of sub-processors that are utilized, or which HeadshotPro proposes to utilize to deliver its Services. HeadshotPro undertakes to keep this list updated regularly to enable its Customers to stay informed of the scope of sub-processing associated with the Services. 

The following is an up-to-date list (as of the date of this advisory) of the names and locations of HeadshotPro sub-processors and content delivery networks.

### Infrastructure Sub-processors – Service Data Storage and Processing
HeadshotPro owns or controls access to the infrastructure that HeadshotPro uses to host and process Customer Content, other than as set forth herein. Currently, the HeadshotPro production systems used for hosting Customer Content for the Services are in facilities in the United States and in the infrastructure sub-processors listed below. The following table describes the countries and legal entities engaged by HeadshotPro in the storage of Service Data. HeadshotPro also uses additional services provided by these sub-processors to process Service Data as needed to provide the Services.

| Entity Name | Entity Type | Entity Country |
| ----------- | ----------- | -------------- | 
| Render | Cloud Computing/Data Center Hosting | US |
| Google Cloud Platform | Cloud Computing/Data Center Hosting | EU/US |
| MongoDB, Inc. | Cloud Database/Data Center Hosting | US |
| Replicate Inc. | AI Processing and Training | US |
| Features & Labels (Fal.ai) | AI Processing and Training | US |
| Cloudflare Inc. | Data storage | US |
| OpenAI | AI Processing, Content Generation and Vision | US |


## Service Specific Sub-processors
HeadshotPro works with certain third parties to provide specific functionality within the Services. These providers are the Sub-processors set forth below. In order to provide the relevant functionality these Sub-processors access Customer Content. Their use is limited to the indicated Services.

| Entity Name | Purpose | Entity Country |
| -----       | -----   | -----
| Crisp, IM SAS | Customer support | EU |
| Bento, Backpack Internet Pty. Ltd | Email marketing| Australia |
| Posthog | Analytics | US |
| Stripe | Payment Processing | US |




## Content Delivery Networks
As explained above, HeadshotPro may use content delivery networks (“CDNs”) to provide the Services, for security purposes, and to optimize content delivery. CDNs are commonly used systems of distributed services that deliver content based on the geographic location of the individual accessing the content and the origin of the content provider. Website content served to website visitors and domain name information may be stored with a CDN to expedite transmission, and information transmitted across a CDN may be accessed by that CDN to enable its functions. The following describes use of CDNs by HeadshotPro’s Services.

| CDN Provider | Services Using CDN | Description of CDN Services | CDN Location |
| ------------ | ------------------ | ----------------------------| ------------ | 
| Google Cloud Platform | All HeadshotPro Services | Public website content served to website visitors may be stored with Cloudflare, Inc., and transmitted by Cloudflare, Inc., to website visitors, to expedite transmission. | Global

## Advertising
HeadshotPro uses third-party advertising services to deliver targeted advertisements. These services may collect and use customer data to provide personalized advertising experiences.

| Entity Name | Purpose | Entity Country |
| -----       | -----   | -----
| Facebook | Advertising | US |
| Google LLC | Advertising | US |