**Security Policy**  
**EFFECTIVE DATE: March 12, 2025**

## Overview
At HeadshotPro, protecting customer data is our top priority. This Security Policy outlines the organizational and technical safeguards implemented to prevent unauthorized access, use, alteration, or disclosure of customer data. HeadshotPro utilizes Google Cloud Storage (GCS), Render, and Vercel platforms. We encourage reviewing our Terms of Service and Privacy Policy for additional details.

## Security Team
Our security team includes experienced professionals who have successfully designed, built, and operated secure, internet-facing systems across startups and large corporations.

## Security Best Practices

### Incident Response
- Established formal procedures for security events with comprehensive training for all staff.
- Immediate escalation, team assembly, and response to security incidents.
- Detailed post-mortem analyses following each incident, reviewed internally and distributed to all relevant teams.
- Prompt written notification to affected customers upon verifying any security breach, detailing the breach and status of our investigation.

### Build Automation
- Fully automated deployment processes enabling secure and reliable updates within minutes.
- Multiple daily deployments facilitate rapid deployment of critical security patches.

### Authentication
- Mandatory two-factor authentication (2FA) and strong password policies enforced across GitHub, Render, GCS, and Vercel.

### Infrastructure Security
- Entirely cloud-based operations; HeadshotPro does not operate physical servers or infrastructure hardware.
- Customer data and services hosted and protected by GCS, Render, and Vercel security practices.
- Robust disaster recovery strategies, including comprehensive backups for all customer data.

### Application Monitoring
- Active monitoring using Sentry to rapidly identify and resolve issues.
- Logging of all access to HeadshotPro applications and production consoles.

### Data Management
- Data hosted within GCS facilities in the United States (us-east-1) and through Render (hosted by AWS).
- Customer data stored securely in multi-tenant environments, with strict logical separation enforced by application-level privacy controls.
- Systems handling customer data are securely configured following industry-standard security and system-hardening practices.
- Use of subprocessors (GCS, Render, Vercel) to assist in securely managing customer data.

### Data Transfer
- 100% HTTPS service delivery.
- Encryption of all data transmissions using industry-standard TLS/SSL protocols with 256-bit encryption.
- Sensitive data encrypted using AES-256 encryption standards.

### Payment Processing
- All transactions processed securely through Stripe.

## Customer Responsibilities
- Secure management of user accounts and organizational data.
- Protection of user credentials and access via secure email practices.
- Compliance with the HeadshotPro Terms of Service and applicable laws.
- Immediate notification to HeadshotPro regarding compromised credentials or suspected security incidents.
- Security penetration testing or assessments require explicit advance written authorization from HeadshotPro.

