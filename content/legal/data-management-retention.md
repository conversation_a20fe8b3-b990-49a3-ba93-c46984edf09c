---
title: Data Management and Retention Policy
description: Governs the management and retention of data.
---

### 1. <PERSON><PERSON><PERSON> and <PERSON>ope

This policy details the protocols and measures that HeadshotPro employs to oversee the entire data lifecycle, from the point of creation or acquisition through to retention and eventual deletion.

This policy applies to all employees, contractors, and third parties who have access to our organization's data, regardless of its format or storage location.

### 2. Policy Statements: Our Commitments

The following commitments outline how HeadshotPro will manage data throughout its lifecycle.

### 2.1 Data Classification and Labeling

All data must be classified based on its sensitivity and labeled accordingly to ensure proper handling and retention.

HeadshotPro classifies data into the following categories:

| **Data Classification Level** | **Description**                                                                                       | **Examples**                                                                                        | **Handling Requirements**                                                                                                                                                                                                                                        |
| ----------------------------- | ----------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Public**                    | Information that is intentionally made available to the public. No potential for damage if disclosed. | Press releases, Public website content, Marketing materials, Annual reports                         | No restrictions on distribution, Can be freely shared internally and externally, Should be reviewed for accuracy before publication                                                                                                                              |
| **Internal**                  | Information intended for use within the organization. Limited damage potential if disclosed.          | Internal memos or emails, employee directories, internal policies and procedures, project schedules | Share only within the organization. Encrypt when transmitting externally. Use caution when discussing in public places. Dispose of securely (e.g. shredding)                                                                                                     |
| **Confidential**              | Sensitive information that requires protection. Moderate to high damage potential if disclosed.       | Customer data, Financial records, Contracts, Intellectual Property (IP)                             | Share only on a need-to-know basis. Encrypt during storage and transmission. Use strong access controls. Securely dispose (e.g., secure digital wiping, physical destruction). Set contractual agreements with all third parties (e.g., NDAs, security measures) |
| **Critical**                    | Highly sensitive information. Severe damage potential if disclosed.                                   | Passwords, Encryption keys, Merger and Acquisition plans, Trade secrets.                            | Strictest access controls (e.g. multi-factor authentication). Encrypted at rest and in transit. Audit logging and access monitoring. Prohibited from being stored on mobile device. Dispose only through certified destruction methods.                          |

### **2.2 Inventory Creation and Maintenance**

A data inventory must be created and maintained for all types of data processed by HeadshotPro, covering both structured (databases, CRM systems) and unstructured data (documents, emails).

The inventory should include key details such as:

- Data type (e.g., personal data, financial data)
- Data owner or custodian
- Data location (e.g., servers, cloud platforms, physical storage)
- Data format (e.g., electronic, physical)
- Sensitivity classification (e.g., confidential, internal, public)
- Retention period and disposal requirements

The data inventory must be reviewed and updated regularly to ensure its accuracy and completeness. Any new data sources, data sets, or changes to existing data must be promptly reflected in the inventory.

### **2.3 Data Flow Mapping**

A data flow map must be maintained to document how data moves within the organization and externally (e.g., data transfers to third parties, external storage locations).

This map should be regularly updated to reflect changes in processes, systems, or third-party relationships.

### **2.4 Data Retention Periods**

Data must be retained only for as long as it is needed to fulfill the purposes for which it was collected or as required by legal, regulatory, or contractual obligations.

The default retention period for data types is as follows (adjustable based on business and legal needs):

- **Financial Records**: 7 years
- **Customer Data**: Retained as long as customer relationship exists + at least 30 days to allow the customer to bring their account back into good standing
- **Personal Data (PII)**: Retained as specified by data protection regulations (e.g., GDPR) or until the purpose for its collection has been fulfilled
- **Intellectual Property (IP)**: Retained for the duration of the IP's legal protection or until no longer required by the organization
- **Security Logs**: Minimum of 1 year, maximum of 5 years
- **Backup Data**: Retained for 1 year and then securely deleted or overwritten.

### **2.5 Data Disposal and Deletion**

Data no longer required must be securely deleted or anonymized. This applies to both physical and electronic records. Physical records must be shredded, while electronic data must be permanently erased using secure data destruction methods.

Backup data must be deleted following its retention period or when it is no longer necessary for restoration or recovery purposes according to the Backup Policy.

### **2.6 Legal and Regulatory Compliance**

All data management activities must comply with relevant legal, regulatory, and contractual obligations, such as the General Data Protection Regulation (GDPR), California Consumer Privacy Act (CCPA), or other applicable laws.

Data subject rights (e.g., access, rectification, erasure) must be honored as required by law. A process to handle data subject access requests (DSARs) in compliance with applicable privacy regulations is implemented.

When data is subject to litigation hold or regulatory investigation, retention periods may be extended as needed.

### **2.7 Data Minimization and Accuracy**

Only data that is necessary for the business process should be collected and retained. Data should be accurate, relevant, and kept up-to-date to prevent unnecessary storage of outdated or incorrect information.

### 2.8 Breach Notification Procedures

HeadshotPro has established a clear process for identifying, reporting, and responding to data breaches. Affected individuals and relevant authorities are notified within required timeframes.

### 3. Compliance and Enforcement

Compliance with this policy is mandatory for all employees, contractors, and third parties with access to HeadshotPro's data.

In rare cases, business needs, local laws, or regulations may require exceptions. Management will approve any exceptions and define alternative solutions.

Non-compliance may lead to disciplinary action, including termination, as per HeadshotPro's policies.

### 4. Policy Review and Maintenance

This policy will be reviewed annually or when significant changes occur to maintain its continuing suitability, adequacy, and effectiveness.

Reviews must consider changes in the regulatory landscape.

| Version | Last Review Date | Next Review Date | Reviewed By | Approved By |
| ------- | ---------------- | ---------------- | ----------- | ----------- |
| 1.0     | 12-03-2025    | 01-06-2026    | Danny Postma  | Danny Postma  |
