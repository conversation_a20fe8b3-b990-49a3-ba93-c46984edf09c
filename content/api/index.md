## API Reference

### Authentication

HeadshotPro uses API keys to allow access to the API.

All API requests on HeadshotPro are scoped to an Organization. A unique API key can be generated by Team Owners at www.headshotpro.com/app/admin/api.

HeadshotPro expects the API key to be included in all API requests to the server in a header that looks like the following:

```text
Authorization: Bearer API_KEY
```

All request bodies need to be valid **JSON**

-----

## API Endpoints

All HeadshotPro endpoints start with `https://server.headshotpro.com/api/v1/`

### Get organization details

```text
GET: https://server.headshotpro.com/api/v1/organization
```

**Response:**

```json
{
	"success": true,
	"organization": {
		"uid": "UNIQUE_ID",
		"name": "Organization Name",
		"website": "https://organization.com",
		"teams": [
			{
				"name": "Team Name",
				"id": "TEAM_UNIQUE_ID"
			}
		]
	}
}
```

### Check credits
Returns the amount of available credits your organization has.

```text
GET: https://server.headshotpro.com/api/v1/organization/credits
```

**Response:**

```json

{
	"success": true,
	"credits": 100
}
```


### Invite a team member
Consumes a credit to invite a team member. An email will be send to the `email` with further instructions.

```text
POST: https://server.headshotpro.com/api/v1/organization/invite
```

**Request Body:**
```json
{
  "email": "USER_EMAIL"
}
```

Or, if you want to invite a team member to a specific team:

```json
{
  "email": "USER_EMAIL",
  "teamId": "TEAM_ID"
}
```

**Response:**
```json
{
  "success": true,
  "input": {
    "email": "EMAIL_ADDRESS"
  },
  "message": "Invite sent",
  "link": "https://www.headshotpro.com/auth/signup?invite=[UNIQUE_ID]",
  "id": "[UNIQUE_ID]",
  "teamId": "TEAM_ID"
}
```

*(Credits are only consumed when a a team member uploads their photos)*

### Revoke an invite

```text
POST: https://server.headshotpro.com/api/v1/organization/invite/revoke
```

**Request Body:**
```json
{
  "email": "USER_EMAIL"
}
```

**Response:**

```json
{
	"success": true,
	"message": "Invite revoked",
	"input": {
		"email": "USER_EMAIL"
	}
}
```

### Get all invites
Gets all invites for the organization

```text
GET: https://server.headshotpro.com/api/v1/organization/invites
```

**Response:**
```json
{
  "success": true,
  "invites": [
    {
      "id": "UNIQUE_ID",
      "email": "<EMAIL>",
      "valid": true,
      "teamId": "TEAM_ID",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "used": false,
      "link": "https://www.headshotpro.com/auth/signup?invite=UNIQUE_ID"
    },
    {
      "id": "UNIQUE_ID_2",
      "email": "<EMAIL>",
      "valid": false,
      "teamId": "TEAM_ID",
      "createdAt": "2024-01-10T14:20:00.000Z",
      "used": true,
      "link": "https://www.headshotpro.com/auth/signup?invite=UNIQUE_ID_2"
    }
  ]
}
```

### Get invite
Gets the details of an invite

```text
GET: https://server.headshotpro.com/api/v1/organization/invite/id/[UNIQUE_ID]
```

**Response:**
```json
{
  "success": true,
  "invite": {
    "email": "<EMAIL>",
    "status": "pending-invite",
    "teamId": "TEAM_ID"
  }
}
```

`status` could be either of the following: `'pending-invite', 'revoked', 'waiting-for-upload', 'active', 'generating-headshots', 'deleted'`

If user has used the invite, the returned email in the response will be their **active** email, not the email that you used to invite them.

### Create a team
Creates a new team within your organization.

```text
POST: https://server.headshotpro.com/api/v1/organization/teams
```

**Request Body:**
```json
{
  "name": "Team Name"
}
```

**Response:**
```json
{
  "success": true,
  "organization": {
    "uid": "UNIQUE_ID",
    "name": "Organization Name",
    "description": "Organization Description",
    "website": "https://organization.com",
    "teams": [
      {
        "name": "Team Name",
        "id": "TEAM_UNIQUE_ID"
      }
    ]
  }
}
```

### Update a team
Updates the name of an existing team.

```text
PUT: https://server.headshotpro.com/api/v1/organization/teams/[TEAM_ID]
```

**Request Body:**
```json
{
  "name": "Updated Team Name"
}
```

**Response:**
```json
{
  "success": true,
  "organization": {
    "uid": "UNIQUE_ID",
    "name": "Organization Name",
    "description": "Organization Description",
    "website": "https://organization.com",
    "teams": [
      {
        "name": "Updated Team Name",
        "id": "TEAM_UNIQUE_ID"
      }
    ]
  }
}
```

### Delete a team
Deletes a team and removes all team members from it. **The users will not be removed and will still belong to your organization, but they will have no team assigned to them.**

```text
DELETE: https://server.headshotpro.com/api/v1/organization/teams/[TEAM_ID]
```

**Response:**
```json
{
  "success": true,
  "organization": {
    "uid": "UNIQUE_ID",
    "name": "Organization Name",
    "description": "Organization Description",
    "website": "https://organization.com",
    "teams": [
      {
        "name": "Other Team Name",
        "id": "TEAM_UNIQUE_ID"
      }
    ]
  }
}
```

### Add members to a team
Adds existing organization members to a specific team. You can add multiple members at once, but they must already be part of your organization.

```text
POST: https://server.headshotpro.com/api/v1/organization/teams/[TEAM_ID]/add-members
```

**Request Body:**
```json
{
  "emails": ["<EMAIL>", "<EMAIL>"]
}
```

**Response:**
```json
{
  "success": true,
  "organization": {
    "uid": "UNIQUE_ID",
    "name": "Organization Name",
    "description": "Organization Description",
    "website": "https://organization.com",
    "teams": [
      {
        "name": "Team Name",
        "id": "TEAM_UNIQUE_ID"
      }
    ]
  }
}
```
