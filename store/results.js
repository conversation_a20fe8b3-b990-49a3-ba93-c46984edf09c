export const state = () => ({
  item: {},
  selected: [],
  liked: [],
  shoots: [],
  showVotingModal: false,
  tab: 'keepers'
})

export const mutations = {
  SET_ITEM (state, payload) {
    state.item = payload

    const totalNsfw = state.item.images?.filter(image => image && image.nsfw && image.status === 'active')?.length
    const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    const totalNsfwNew = state.item
      .images
      ?.filter(
        image => image &&
          image.nsfw &&
          image.status === 'active' &&
          image.likedStatus === 'none' &&
          ['edit', 'regeneration', 'additional'].includes(image?.type) &&
          new Date(image?.createdAt) > oneWeekAgo
      )?.length || 0
    const totalPhotos = state.item.images?.filter(image => image && image.status === 'active')?.length

    state.item.images = state.item.images?.filter(image => image && !image.nsfw)?.map((image) => {
      image.isDownloaded = image.isDownloaded || false
      return image
    })

    state.item.totalNsfwNew = totalNsfwNew
    state.item.totalNsfw = totalNsfw
    state.item.totalPhotos = totalPhotos
  },
  SET_SHOOTS (state, payload) {
    state.shoots = payload
  },
  UPDATE_ITEM (state, payload) {
    state.item = { ...state.item, ...payload }
  },
  SET_SHOW_VOTING_MODAL (state, payload) {
    state.showVotingModal = payload
  },
  SET_SELECTED (state, payload) {
    state.selected = payload
  },
  SET_ACTIVE_TAB (state, payload) {
    state.tab = payload
  },
  SET_DOWNLOADED (state, payload) {
    state.item.images = state.item.images?.map((image) => {
      if (payload.includes(image?._id?.toString())) {
        image.isDownloaded = true
      }

      return image
    })
  },
  INCREMENT_USED_REGENERATIONS (state) {
    state.item.usedRegenerations++
  },
  ADD_TRAINING_IMAGE (state, payload) {
    state.item.trainingImages.push(payload)
  },
  SET_LIKED_STATUS (state, payload) {
    state.item.images = state.item.images?.map((image) => {
      if (payload.likedStatus === 'loved' && image.likedStatus === 'loved') {
        image.likedStatus = 'favorite'
      }

      if (image?._id?.toString() === payload.id) {
        image.likedStatus = payload.likedStatus
      }
      return image
    })
  },
  ADD_SINGLE_PHOTO (state, payload) {
    state.item.images.push(payload)
  },
  SET_PHOTO_STATUS (state, payload) {
    for (const image of state.item.images) {
      if (image._id === payload._id) {
        image.status = payload.status
      }
    }
  }
}
