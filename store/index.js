export const state = () => ({
  numberOfHeadshots: 100,
  photoPerStyle: 10,
  modal: {
    support: false
  },
  reachedEnd: false,
  showInviteWarning: true,
  showReviewRequestModal: false,
  styles: [],
  clothing: [],
  hairstyles: [],
  emotions: [],
  packages: null,
  stats: {
    photos: '17,943,292',
    users: '102,207',
    trustpilotScore: 4.7
  },
  trustpilotReviews: [],
  trustpilot: {
    stars: 4.7,
    totalReviews: 1521
  },
  allowedCurrencies: [
    { code: 'usd', title: 'US Dollar', symbol: '$', locale: 'US' },
    { code: 'eur', title: 'Euro', symbol: '€', locale: ['DE', 'FR', 'IT', 'ES', 'PT', 'NL', 'BE', 'AT', 'FI', 'IE', 'LU', 'GR', 'SI', 'CY', 'MT', 'SK', 'EE', 'LV', 'LT', 'HR'] },
    { code: 'cad', title: 'Canadian Dollar', symbol: '$', locale: 'CA' },
    { code: 'aud', title: 'Australian Dollar', symbol: '$', locale: 'AU' },
    { code: 'gbp', title: 'British Pound', symbol: '£', locale: 'GB' },
    { code: 'chf', title: 'Swiss Franc', symbol: 'CHF', locale: 'CH' },
    { code: 'inr', title: 'Indian Rupee', symbol: '₹', hidden: true, locale: 'IN' },
    { code: 'brl', title: 'Brazilian Real', symbol: 'R$', hidden: true, locale: 'BR' },
    { code: 'idr', title: 'Indonesian Rupiah', symbol: 'Rp', hidden: true, locale: 'ID' },
    { code: 'pln', title: 'Polish Zloty', symbol: 'zł', hidden: true, locale: 'PL' },
    { code: 'sgd', title: 'Singapore Dollar', symbol: 'S$', hidden: true, locale: 'SG' },
    { code: 'cny', title: 'Chinese Yuan', symbol: '¥', hidden: true, locale: 'CN' }
  ],
  examples: [
    {
      name: 'Danny Postma',
      title: 'Founder at <a href="https://www.headshotpro.com" target="_blank">HeadshotPro</a>',
      value: 'danny2',
      alt: 'Selfies of a man before they are uploaded to an AI headshot generator',
      examplesAlt: [
        'Closeup AI headshot of a man created by HeadshotPro',
        'Torso-level AI headshot of a man in a park created by HeadshotPro'
      ]
    },
    {
      name: 'Amelia',
      title: 'Professional model',
      value: 'amelia',
      alt: 'Selfies of a woman before they are uploaded to an AI headshot generator',
      examplesAlt: [
        'Torso-level AI headshot of a woman in a park generated by HeadshotPro',
        'Torso-level AI headshot of a woman in an office created by HeadshotPro'
      ]
    },
    // { name: 'Damon Chen', title: 'Founder at <a href="https://testimonial.to/" rel="nofollow" target="_blank">Testimonial</a>', value: 'damon' },
    // { name: 'Dustin Miller', title: 'Co-founder at <a href="https://www.vowelmoney.com/" rel="nofollow" target="_blank">Vowel</a>', value: 'dustin' },
    { name: 'Matthew Russo', title: 'Leading at <a href="https://www.infillion.com" rel="nofollow" target="_blank">Infillion.com</a>', value: 'matthew' }
  ],
  parityDiscountDollarAmount: 0,
  reviews: [],
  maxPercentage: 0,
  maxPixels: 0,
  testimonials: [
    {
      id: 1,
      footer: null,
      quote: 'In a jam to send a prof headshot to my company for a presentation and this worked amazingly.',
      imageUrl: require('@/assets/img/testimonial/testimonial-1.jpeg'),
      name: 'C. Heilman',
      position: 'President at Bankers Life Securities',
      logoUrl: null,
      logoComponent: null,
      logoAlt: null
    },
    {
      id: 2,
      footer: null,
      quote: 'I quickly generated a professional headshot for a conference speaker profile using this tool, avoiding the expense and time constraints of traditional photography.',
      imageUrl: require('@/assets/img/testimonial/testimonial-4.jpeg'),
      logoUrl: null,
      logoComponent: null,
      logoAlt: null
    },
    {
      id: 3,
      footer: null,
      quote: 'Fast and fresh looks I would not have thought of myself',
      name: 'J. Sanderson',
      position: '',
      imageUrl: require('@/assets/img/testimonial/testimonial-3.jpeg'),
      logoUrl: null,
      logoComponent: null,
      logoAlt: null
    },
    {
      id: 4,
      footer: null,
      quote: 'I was looking for professional work photos for my LinkedIn so this is perfect',
      imageUrl: require('@/assets/img/testimonial/testimonial-2.jpeg'),
      avatarUrl: require('@/assets/img/avatar-4.jpg'),
      name: 'D. Burd',
      position: 'Master of Business Administration, MBA at Universidad de la Tercera Edad',
      logoUrl: null,
      logoComponent: null,
      logoAlt: null
    }
  ],
  posthogFeatureFlags: {},
  hasUnsavedChanges: false,
  organizationBulkDiscounts: null,
  changelogs: []
})

export const mutations = {
  SET_MODAL (state, payload) {
    state.modal[payload.name] = payload.value
  },
  SET_REACHED_END (state, payload) {
    state.reachedEnd = payload
  },
  SET_STYLES (state, payload) {
    state.styles = payload
  },
  SET_CLOTHING (state, payload) {
    state.clothing = payload
  },
  SET_HAIRSTYLES (state, payload) {
    state.hairstyles = payload
  },
  SET_EMOTIONS (state, payload) {
    state.emotions = payload
  },
  SET_PACKAGES (state, payload) {
    state.packages = payload
  },
  SET_SHOW_INVITE_WARNING (state, payload) {
    state.showInviteWarning = payload
  },
  SET_PARITY_DISCOUNT_DOLLAR_AMOUNT (state, payload) {
    state.parityDiscountDollarAmount = payload
  },
  SET_MAX_PERCENTAGE (state, payload) {
    state.maxPercentage = payload
  },
  SET_MAX_PIXELS (state, payload) {
    state.maxPixels = payload
  },
  SET_REVIEWS (state, payload) {
    state.reviews = payload
  },
  SET_TRUSTPILOT_REVIEWS (state, payload) {
    state.trustpilotReviews = payload
  },
  SET_TRUSTPILOT_STATS (state, payload) {
    state.trustpilot = payload
  },
  SET_SHOW_REVIEW_REQUEST_MODAL (state, payload) {
    state.showReviewRequestModal = payload
  },
  SET_POSTHOG_FEATURE_FLAGS (state, payload) {
    state.posthogFeatureFlags = payload
  },
  SET_HAS_UNSAVED_CHANGES (state, payload) {
    state.hasUnsavedChanges = payload
  },
  SET_ORGANIZATION_BULK_DISCOUNTS (state, payload) {
    state.organizationBulkDiscounts = payload
  },
  SET_CHANGELOGS (state, payload) {
    state.changelogs = payload
  }
}

export const actions = {
  async nuxtServerInit ({ commit, dispatch }) {
    try {
      await dispatch('getStyles')
      await dispatch('getClothing')
      await dispatch('getHairstyles')
      await dispatch('getEmotions')
      await dispatch('getPackages')
      await dispatch('getNavigationItems')
      await dispatch('getReviewItems')
      await dispatch('getTrustpilotReviews')
      await dispatch('getOrganizationBulkDiscounts')
    } catch (error) {
      console.log(error)
      //   this.handleError(error)
    }
  },
  async getStyles ({ commit, dispatch }) {
    try {
      const { success, data, errorMessage } = await this.$axios.$get('/styles/')
      if (!success) {
        throw new Error(errorMessage)
      }
      commit('SET_STYLES', data.styles)
    } catch (error) {
      console.log(error)
    }
  },
  async getClothing ({ commit, dispatch }) {
    try {
      const { success, data, errorMessage } = await this.$axios.$get('/clothing/')
      if (!success) {
        throw new Error(errorMessage)
      }
      commit('SET_CLOTHING', data)
    } catch (error) {
      console.log(error)
    }
  },
  async getHairstyles ({ commit, dispatch }) {
    try {
      const { success, data, errorMessage } = await this.$axios.$get('/hairstyles/')
      if (!success) {
        throw new Error(errorMessage)
      }
      commit('SET_HAIRSTYLES', data)
    } catch (error) {
      console.log(error)
    }
  },
  async getEmotions ({ commit, dispatch }) {
    try {
      const { success, data, errorMessage } = await this.$axios.$get('/emotions/')
      if (!success) {
        throw new Error(errorMessage)
      }
      commit('SET_EMOTIONS', data)
    } catch (error) {
      console.log(error)
    }
  },
  async getPackages ({ commit, dispatch }) {
    try {
      const { success, data, errorMessage } = await this.$axios.$get('/checkout/package/package')
      if (!success) {
        throw new Error(errorMessage)
      }
      commit('SET_PACKAGES', data.packages)
    } catch (error) {
      console.log(error)
    }
  },
  async getNavigationItems ({ commit, dispatch }) {
    try {
      const slugToTitle = (slug, capitalizeFirstLetter = true) => {
        try {
          const words = slug.split('-')

          for (let i = 0; i < words.length; i++) {
            const word = words[i]
            if (capitalizeFirstLetter) {
              words[i] = word.charAt(0).toUpperCase() + word.slice(1)
            } else if (i === 0) {
              words[i] = word.charAt(0).toUpperCase() + word.slice(1)
            }
          }

          return words.join(' ')
        } catch (err) {
          return slug
        }
      }
      // const { data: headshotTypes } = await this.$axios.$get('/blog/category/headshot-types')
      const headshotTypesContent = await import('@/assets/headshot-types.json')
      // Get the keys of the headshotTypes object
      const headshotTypes = Object.keys(headshotTypesContent).map((key) => {
        // Filter out .default key
        if (key === 'default') { return null }
        return {
          title: `${slugToTitle(key)}`,
          url: `/headshot-types/${key}-headshots`,
          slug: key
        }
      }).filter(item => item !== null)

      if (headshotTypes && headshotTypes.length > 0) {
        headshotTypes.sort((a, b) => a.slug.localeCompare(b.slug))
        commit('navigation/SET_HEADSHOT_TYPES', headshotTypes)
      }
    } catch (error) {
      console.log(error)
    }
  },
  async getReviewItems ({ commit, dispatch }) {
    try {
      const { data } = await this.$axios.$get('/reviews/all')
      commit('SET_REVIEWS', data)
    } catch (error) {
      console.log(error)
    }
  },
  async getTrustpilotReviews ({ commit }) {
    try {
      const { data } = await this.$axios.$get('/reviews/trustpilot')
      const { reviews, stats } = data
      commit('SET_TRUSTPILOT_REVIEWS', reviews)
      commit('SET_TRUSTPILOT_STATS', stats)
    } catch (error) {
      console.log(error)
    }
  },
  async getOrganizationBulkDiscounts ({ commit }) {
    try {
      const { data } = await this.$axios.$get('/checkout/discount/bulk-discount')
      commit('SET_ORGANIZATION_BULK_DISCOUNTS', data)
    } catch (error) {
      console.log(error)
    }
  }
}
//
