export const state = () => ({
  left: [
    { title: 'For teams', url: '/company-headshots', alt: 'Company Headshots' },
    { title: 'Examples', url: '/reviews', alt: 'HeadshotPro Reviews' },
    { title: 'Pricing', url: '/pricing' }
  ],
  right: [
    {
      title: 'Headshots',
      url: '/headshot-types',
      children: [
        { title: 'All backdrops and outfits', url: '/backdrop-and-outfit' }
      ]
    },
    { title: 'Blog', url: '/blog' },
    {
      title: 'Tools',
      url: '#',
      children: [
        { title: 'Free AI Headshot Generator', url: '/tools/free-headshot-generator', alt: 'Free AI Headshot Generator' },
        { title: 'Free LinkedIn Headline Generator', url: '/tools/free-linkedin-headline-generator', alt: 'Free LinkedIn Headline Generator' },
        { title: 'Free LinkedIn Bio Generator', url: '/tools/free-linkedin-bio-generator', alt: 'Free LinkedIn Bio Generator' },
        { title: 'Free LinkedIn Profile Generator', url: '/tools/free-linkedin-profile-generator', alt: 'Free LinkedIn Profile Generator' },
        { title: 'Free Email Signature Generator', url: '/tools/free-email-signature-generator', alt: 'Free Email Signature Generator' },
        { title: 'Free Team Page Generator', url: '/tools/free-team-page-generator', alt: 'Free Team Page Generator' }
      ]
    }
  ],
  headshotTypes: []
})

export const mutations = {
  SET_HEADSHOT_TYPES (state, payload) {
    const headshotTypes = payload.map((item) => {
      return {
        title: `${slugToTitle(item.slug)} Headshots`,
        url: `/headshot-types/${item.slug}-headshots`
      }
    })
    state.headshotTypes = headshotTypes
  }
}

function slugToTitle (slug, capitalizeFirstLetter = true) {
  try {
    const words = slug.split('-')

    for (let i = 0; i < words.length; i++) {
      const word = words[i]
      if (capitalizeFirstLetter) {
        words[i] = word.charAt(0).toUpperCase() + word.slice(1)
      } else if (i === 0) {
        words[i] = word.charAt(0).toUpperCase() + word.slice(1)
      }
    }

    return words.join(' ')
  } catch (err) {
    return slug
  }
}
