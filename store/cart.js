export const state = () => ({
  upsell: {
    selectedStyles: [],
    modelId: null,
    purchasedUpsell: false
  }
})

export const mutations = {
  SET_UPSELL_SELECTED_STYLES (state, payload) {
    state.upsell.selectedStyles = payload
  },
  SET_UPSELL_MODEL_ID (state, payload) {
    state.upsell.modelId = payload
  },
  SET_PURCHASED_UPSELL (state, payload) {
    state.upsell.purchasedUpsell = payload
  }
}
