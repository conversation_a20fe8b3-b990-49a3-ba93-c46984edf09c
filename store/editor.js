export const state = () => ({
  modal: {},
  isRemovingBackground: false,
  pfp: {
    src: null, // 'https://images.unsplash.com/photo-1581382575275-97901c2635b7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=987&q=80',
    baseSrc: null, // 'https://images.unsplash.com/photo-1581382575275-97901c2635b7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=987&q=80',
    transparentSrc: null,
    backgroundSrc: null,
    backgroundColor: '#1B145D',
    grayscale: false,
    borderWidth: 3,
    borderColor: { from: '#f5f5f5', to: '#c1c1c1' },
    borderOffset: 0,
    borderOpacity: 100,
    borderGradientRotation: 45,
    borderText: null,
    textOffset: 28,
    textRotation: -120,
    textSize: 9,
    textLetterSpacing: 0,
    textColor: '#ffffff',
    imageZoom: 100,
    imageOffsetTop: 0,
    imageOffsetLeft: 0,
    imageRotation: 0,
    result: null
  }
})

export const mutations = {
  SET_MODAL (state, payload) { state.modal[payload.name] = payload.value },
  UPDATE_PFP (state, payload) { state.pfp[payload.key] = payload.value },
  SET_PFP (state, payload) { state.pfp = payload },
  SET_IS_REMOVING_BACKGROUND (state, payload) { state.isRemovingBackground = payload }
}

export const actions = {
  nuxtServerInit ({ commit }, { req }) {
  },
  resetSettings ({ commit }) {
    const data = {
      src: null,
      grayscale: false,
      borderWidth: 3,
      borderColor: { from: '#ee0979', to: '#ff6a00' },
      borderOffset: 0,
      borderOpacity: 100,
      borderGradientRotation: 45,
      borderText: null,
      textOffset: 28,
      textRotation: -120,
      textSize: 9,
      textLetterSpacing: 0,
      textColor: '#ffffff',
      imageZoom: 100,
      imageOffsetTop: 0,
      imageOffsetLeft: 0,
      imageRotation: 0
    }

    commit('SET_PFP', data)
  }
}
