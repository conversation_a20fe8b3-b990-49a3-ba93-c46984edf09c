import axios from '../utils/axios'

export const state = () => ({
  selectedStyles: [],
  selectedBodyType: null,
  selectedGlasses: null,
  selectedAge: null,
  selectedHeight: null,
  selectedEthnicity: null,
  basket: null,
  prices: {
    style: 0,
    remix: 0
  },
  freeStyles: 0
})

export const mutations = {
  SET_SELECTED_STYLE (state, payload) {
    state.selectedStyles.push(payload)
  },
  CLEAR_SELECTED_STYLE (state) {
    state.selectedStyles = []
  },
  REMOVE_STYLE (state, index) {
    state.selectedStyles.splice(index, 1)
  },
  SET_ETHNICITY (state, payload) {
    state.selectedEthnicity = payload
  },
  SET_SELECTED_BODY_TYPE (state, payload) {
    state.selectedBodyType = payload
  },
  SET_SELECTED_HEIGHT (state, payload) {
    state.selectedHeight = payload
  },
  SET_SELECTED_AGE (state, payload) {
    state.selectedAge = payload
  },
  SET_SELECTED_GLASSES (state, payload) {
    state.selectedGlasses = payload
  },
  SET_BASKET (state, payload) {
    state.basket = payload
  },
  SET_PRICES (state, payload) {
    state.prices = payload
  },
  SET_FREE_STYLES (state, payload) {
    state.freeStyles = payload
  }

}

export const getters = {
  selectedEthnicity (state) {
    return state.selectedEthnicity
  },
  selectedBodyType (state) {
    return state.selectedBodyType
  },
  selectedHeight (state) {
    return state.selectedHeight
  },
  selectedAge (state) {
    return state.selectedAge
  },
  selectedGlasses (state) {
    return state.selectedGlasses
  },
  selectedStyles (state) {
    return state.selectedStyles
  },
  appearance (state) {
    return {
      ethnicity: state.selectedEthnicity,
      bodyType: state.selectedBodyType,
      height: state.selectedHeight,
      age: state.selectedAge,
      glasses: state.selectedGlasses
    }
  },
  basket (state) {
    return state.basket
  },
  prices (state) {
    return state.prices
  },
  freeStyles (state) {
    return state.freeStyles
  }
}

export const actions = {
  load ({ commit }, payload) {
    return axios.get('/upsell/basket?modelId=' + payload.modelId)
      .then((response) => {
        if (response.data?.success) {
          commit('SET_BASKET', response.data.data.basket)
          commit('SET_PRICES', response.data.data.prices)
          commit('SET_FREE_STYLES', response.data.data.freeStyles)

          commit('CLEAR_SELECTED_STYLE')

          for (const item of response.data.data.basket.items) {
            if (item.type === 'style') {
              commit('SET_SELECTED_STYLE', { style: item.styleId, clothing: item.clothingId })
            }
          }

          if (response.data.data.basket.appearance) {
            commit('SET_ETHNICITY', response.data.data.basket.appearance.ethnicity || null)
            commit('SET_SELECTED_BODY_TYPE', response.data.data.basket.appearance.bodyType || null)
            commit('SET_SELECTED_HEIGHT', response.data.data.basket.appearance.height || null)
            commit('SET_SELECTED_AGE', response.data.data.basket.appearance.age || null)
            commit('SET_SELECTED_GLASSES', response.data.data.basket.appearance.glasses || null)
          }
        }
      })
  },
  updateBasket ({ commit }, payload) {
    if (!Array.isArray(payload.items)) {
      return Promise.reject(new Error('Invalid items'))
    }

    return axios.post('/upsell/basket?modelId=' + payload.modelId, { items: payload.items, appearance: payload.appearance })
      .then((response) => {
        commit('SET_BASKET', response.data.data.basket)
        commit('SET_PRICES', response.data.data.prices)
        commit('SET_FREE_STYLES', response.data.data.freeStyles)
        commit('CLEAR_SELECTED_STYLE')

        for (const item of response.data.data.basket.items) {
          if (item.type === 'style') {
            commit('SET_SELECTED_STYLE', { style: item.styleId, clothing: item.clothingId })
          }
        }

        if (response.data.data.basket.appearance) {
          commit('SET_ETHNICITY', response.data.data.basket.appearance.ethnicity || null)
          commit('SET_SELECTED_BODY_TYPE', response.data.data.basket.appearance.bodyType || null)
          commit('SET_SELECTED_HEIGHT', response.data.data.basket.appearance.height || null)
          commit('SET_SELECTED_AGE', response.data.data.basket.appearance.age || null)
          commit('SET_SELECTED_GLASSES', response.data.data.basket.appearance.glasses || null)
        }

        return response.data.data.basket
      })
  },
  addRemixCredits ({ dispatch, state }, payload) {
    if (!state.basket?.items) {
      return dispatch('load', { modelId: payload.modelId })
        .then(() => {
          return dispatch('addRemixCredits', payload)
        })
    }

    const amount = payload?.amount || 10
    let found = false
    const items = (state.basket?.items || []).map((item) => {
      if (item.type === 'remix') {
        found = true
        return { ...item, amount: parseInt(item.amount) + parseInt(amount) }
      }

      return item
    })

    if (!found) {
      items.push({ type: 'remix', amount })
    }

    return dispatch('updateBasket', { items, modelId: payload.modelId })
  }
}
