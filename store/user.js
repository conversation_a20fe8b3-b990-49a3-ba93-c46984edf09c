export const state = () => ({
  uid: null,
  email: null,
  idToken: null,
  displayName: null,
  stripeId: null,
  paypalId: null,
  avatar: null,
  hasPaidAccount: false,
  role: null,
  hasRetry: false,
  packages: null,
  isAdmin: false,
  invites: null,
  purchasedUpsell: false,
  rewards: {},
  adminRole: null,
  countryCode: null,
  firebaseAuthTokenRefreshedAt: null,
  numberOfModels: 0,
  referredBy: null,
  referralCode: null,
  studio: {
    credits: 0
  }
})

export const mutations = {
  SET_UID (state, payload) {
    state.uid = payload
  },
  SET_DISPLAY_NAME (state, payload) {
    state.displayName = payload
  },
  SET_EMAIL (state, payload) {
    state.email = payload
  },
  SET_AVATAR (state, payload) {
    state.avatar = payload
  },
  SET_IDTOKEN (state, payload) {
    state.idToken = payload
  },
  SET_STRIPE_ID (state, payload) {
    state.stripeId = payload
  },
  SET_PAYPAL_ID (state, payload) {
    state.paypalId = payload
  },
  SET_HAS_PAID_ACCOUNT (state, payload) {
    state.hasPaidAccount = payload
  },
  SET_ROLE (state, payload) {
    state.role = payload
  },
  SET_RETRY (state, payload) {
    state.hasRetry = payload
  },
  SET_USER_TO_NULL (state, payload) {
    state.uid = null
    state.email = null
    state.idToken = null
    state.displayName = null
    state.stripeId = null
    state.avatar = null
    state.tokens = null
    state.hasPaidAccount = false
    state.hasRetry = false
  },
  SET_PACKAGES (state, payload) {
    state.packages = payload
  },
  SET_ADMIN (state, payload) {
    state.isAdmin = payload
  },
  DEDUCT_PACKAGE (state, payload) {
    state.packages.splice(state.packages.indexOf(payload), 1)
  },
  SET_INVITES (state, payload) {
    state.invites = payload
  },
  SET_PURCHASED_UPSELL (state, payload) {
    state.purchasedUpsell = payload
  },
  SET_REWARDS (state, payload) {
    state.rewards = payload
  },
  SET_ADMIN_ROLE (state, payload) {
    state.adminRole = payload
  },
  SET_COUNTRY_CODE (state, payload) {
    state.countryCode = payload
  },
  SET_FIREBASE_AUTH_TOKEN_REFRESHED_AT (state, payload) {
    state.firebaseAuthTokenRefreshedAt = payload
  },
  SET_NUMBER_OF_MODELS (state, payload) {
    state.numberOfModels = payload
  },
  SET_STUDIO_CREDITS (state, payload) {
    state.studio.credits = payload
  },
  REMOVE_STUDIO_CREDITS (state, payload) {
    state.studio.credits -= payload
  },
  ADD_STUDIO_CREDITS (state, payload) {
    state.studio.credits += payload
  },
  SET_REFERRED_BY (state, payload) {
    state.referredBy = payload
  },
  SET_REFERRAL_CODE (state, payload) {
    state.referralCode = payload
  }
}

export const actions = {
  setupUserData ({ commit, dispatch }, payload) {
    if (!payload || !payload.user) {
      return false
    }
    const { user } = payload
    if (user?.stripe?.accountId) {
      commit('SET_STRIPE_ID', user.stripe.accountId)
    }
    if (user?.paypal?.accountId) {
      commit('SET_PAYPAL_ID', user.paypal.accountId)
    }
    if (user?.tokens) {
      commit('SET_TOKENS', user.tokens)
    }
    if (user?.hasPaidAccount) {
      commit('SET_HAS_PAID_ACCOUNT', user.hasPaidAccount)
    }
    if (user?.packages) {
      commit('SET_PACKAGES', user.packages)
    }
    if (user?.organization) {
      commit('organization/SET_ORGANIZATION', user.organization, { root: true })
    }
    if (user?.role) {
      commit('SET_ROLE', user.role)
    }
    if (user?.hasRetry) {
      commit('SET_RETRY', user.hasRetry)
    }
    if (user?.purchasedUpsell) {
      commit('SET_PURCHASED_UPSELL', user.purchasedUpsell)
    }
    if (user?.isAdmin) {
      commit('SET_ADMIN', user.isAdmin)
    }
    if (user?.rewards) {
      commit('SET_REWARDS', user.rewards)
    }
    if (user?.adminRole) {
      commit('SET_ADMIN_ROLE', user.adminRole)
    }
    if (user?.numberOfModels) {
      commit('SET_NUMBER_OF_MODELS', user.numberOfModels)
    }
    if (user?.studio) {
      commit('SET_STUDIO_CREDITS', user.studio.credits)
    }
    if (user?.referredBy) {
      commit('SET_REFERRED_BY', user.referredBy)
    }
    if (user?.referralCode) {
      commit('SET_REFERRAL_CODE', user.referralCode)
    }
  },

  async onAuthStateChangedAction ({ commit, dispatch }, { authUser, claims, token }) {
    if (!authUser) {
      await dispatch('cleanupAction')
      return
    }

    // you can request additional fields if they are optional (e.g. photoURL)
    const { uid, email, displayName } = authUser
    commit('SET_UID', uid)
    commit('SET_EMAIL', email)
    commit('SET_DISPLAY_NAME', displayName)

    if (token) {
      dispatch('getUserDetails', token)
    }
  },

  cleanupAction ({ commit }) {
    commit('SET_UID', null)
  }
}
