import Vue from 'vue'

export const state = () => ({
  organization: null,
  selectedTeamId: null,
  teamMembers: [],
  invites: null,
  totalTeamSize: null,
  creditsLeft: null,
  selectedImages: [],
  pendingInvites: [],
  finishedModels: [],
  preselectedOptions: {
    styles: [],
    clothing: []
  },
  dashboardMetadata: {
    teamMembers: {
      total: 0,
      pages: 1,
      reachedEnd: false
    },
    pendingInvites: {
      total: 0,
      pages: 1,
      reachedEnd: false
    },
    finishedModels: {
      total: 0,
      pages: 1,
      reachedEnd: false
    }
  }
})

export const mutations = {
  SET_ORGANIZATION (state, payload) {
    state.organization = payload
  },
  SET_ORGANIZATION_TEAMS (state, payload) {
    Vue.set(state.organization, 'teams', payload)
  },
  SET_TEAM_MEMBERS (state, payload) {
    state.teamMembers = payload
  },
  ADD_TEAM_MEMBERS (state, payload) {
    if (payload.length === 0) { return }
    state.teamMembers = [...state.teamMembers, ...payload]
  },
  SET_FINISHED_MODELS (state, payload) {
    state.finishedModels = payload
  },
  ADD_FINISHED_MODELS (state, payload) {
    if (payload.length === 0) { return }
    state.finishedModels = [...state.finishedModels, ...payload]
  },
  SET_SELECTED_IMAGE (state, payload) {
    const { userId, imageId } = payload
    const member = state.teamMembers.find(member => member._id === userId)
    const image = member.images.find(image => image.id === imageId)
    Vue.set(member, 'selectedImage', image)
  },
  SET_SELECTED_TEAM_ID (state, payload) {
    state.selectedTeamId = payload
  },
  UPDATE_MEMBER_TEAM (state, payload) {
    const { email, uid, teamId } = payload
    const member = state.teamMembers.find(member => member.email === email)
    if (member) { Vue.set(member, 'teamId', teamId) }

    const finishedModel = state.finishedModels.find(model => model.uid === uid)
    if (finishedModel) { Vue.set(finishedModel, 'teamId', teamId) }

    const pendingInvite = state.pendingInvites.find(invite => invite.email === email)
    if (pendingInvite) { Vue.set(pendingInvite, 'teamId', teamId) }
  },
  REMOVE_TEAM_MEMBER (state, payload) {
    state.teamMembers = state.teamMembers.filter(member => member._id !== payload)
  },
  SET_INVITES (state, payload) {
    state.invites = payload
  },
  SET_STYLE (state, payload) {
    if (!state.organization.style) {
      Vue.set(state.organization, 'style', [])
    }
    state.organization.style.push(payload)
  },
  REMOVE_STYLE (state, index) {
    state.organization.style.splice(index, 1)
  },
  SET_CREDITS (state, payload) {
    if (!state.organization.credits) {
      Vue.set(state.organization, 'credits', 0)
    }
    state.organization.credits = payload
  },
  SET_ORGANIZATION_CREDITS (state, payload) {
    if (!state.organization.credits) {
      Vue.set(state.organization, 'credits', 0)
    }
    state.organization.credits = payload
  },
  SET_TOTAL_TEAM_SIZE (state, payload) {
    state.totalTeamSize = payload
  },
  SET_CREDITS_LEFT (state, payload) {
    state.creditsLeft = payload
  },
  SET_SELECTED_IMAGES (state, payload) {
    state.selectedImages = payload
  },
  SET_ORGANIZATION_ALLOWED_OPTIONS (state, payload) {
    state.organization.allowedOptions = payload
  },
  SET_ORGANIZATION_COMBOS (state, payload) {
    if (!state.organization.allowedOptions) {
      Vue.set(state.organization, 'allowedOptions', {})
    }
    Vue.set(state.organization.allowedOptions, 'combos', payload)
  },
  SET_PENDING_INVITES (state, payload) {
    state.pendingInvites = payload
  },
  ADD_PENDING_INVITES (state, payload) {
    if (payload.length === 0) { return }
    const existingInvites = [...state.pendingInvites]
    for (const invite of payload) {
      const existingInviteIdx = existingInvites.findIndex(i => i.email === invite.email)
      if (existingInviteIdx !== -1) {
        existingInvites[existingInviteIdx] = invite
      } else {
        existingInvites.push(invite)
      }
    }

    state.pendingInvites = existingInvites
  },
  SET_BRANDING_COLOR (state, payload) {
    if (!state.organization.branding) {
      Vue.set(state.organization, 'branding', {})
    }
    Vue.set(state.organization.branding, 'backgroundColor', payload)
  },
  SET_BRANDING (state, payload) {
    Vue.set(state.organization, 'branding', payload)
  },
  SET_BRANDING_BACKGROUND (state, payload) {
    if (!state.organization.branding) {
      Vue.set(state.organization, 'branding', {})
    }
    Vue.set(state.organization.branding, 'backgroundImage', payload)
  },
  SET_PRESELECTED_OPTIONS (state, payload) {
    state.preselectedOptions = payload
  },
  SET_DASHBOARD_METADATA (state, payload) {
    state.dashboardMetadata = {
      ...state.dashboardMetadata,
      ...payload
    }
  },
  ADD_INVITE_REMINDER (state, payload) {
    const invite = state.pendingInvites.find(invite => invite.invite?._id === payload._id)
    if (!invite || !invite.invite) { return }
    if (!invite.invite?.reminders) {
      Vue.set(invite.invite, 'reminders', [])
    }
    invite.invite.reminders.push({
      organization: state.organization._id,
      remindedAt: new Date()
    })
  },
  ADD_USER_REMINDER (state, payload) {
    const user = state.teamMembers.find(user => user._id === payload._id)
    if (!user) { return }
    if (!user.reminders) {
      Vue.set(user, 'reminders', [])
    }
    user.reminders.push({
      organization: state.organization._id,
      remindedAt: new Date()
    })
  },
  ADD_MANUAL_INVITES (state, payload) {
    const { emails, teamId } = payload
    const invites = emails.map(email => ({
      createdAt: new Date().toISOString(),
      email,
      modelStatus: 'pending-invite',
      uid: null,
      invite: null,
      teamId: teamId === 'none' ? null : teamId
    }))

    const existingInvites = [...state.pendingInvites]
    for (const invite of invites) {
      const existingInviteIdx = existingInvites.findIndex(i => i.email === invite.email)
      if (existingInviteIdx === -1) {
        existingInvites.push(invite)
        state.dashboardMetadata.pendingInvites.total++
        state.organization.inviteCount++
      }
    }

    state.pendingInvites = existingInvites
  }
}

export const actions = {
  async getOrganizationInvites ({ commit }) {
    try {
      const { success, data, errorMessage } = await this.$axios.$get('/organization/invites')
      if (!success) {
        throw new Error(errorMessage)
      }
      commit('SET_INVITES', data.invites)
    } catch (err) {
      console.log(err)
    }
  },
  async getOrganizationStats ({ commit }) {
    try {
      const { success, data, errorMessage } = await this.$axios.$get('/organization/stats')
      if (!success) {
        throw new Error(errorMessage)
      }
      const { creditsLeft, totalTeamSize, selectedImages } = data
      commit('SET_CREDITS_LEFT', creditsLeft)
      commit('SET_TOTAL_TEAM_SIZE', totalTeamSize)
      commit('SET_SELECTED_IMAGES', selectedImages)
    } catch (err) {
      console.log(err)
    }
  },
  fetchPreselectedOptions ({ commit }) {
    return this.$axios.$get('/organization/team/preselected-options')
      .then(({ success, data, errorMessage }) => {
        if (!success) {
          throw new Error(errorMessage)
        }

        commit('SET_PRESELECTED_OPTIONS', data)
      })
  }
}
