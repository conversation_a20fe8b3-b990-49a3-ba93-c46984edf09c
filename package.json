{"name": "app", "version": "1.0.0", "private": true, "scripts": {"dev": "nuxt", "build": "nuxt build", "start": "nuxt start", "generate": "nuxt generate"}, "engines": {"node": "18.x"}, "dependencies": {"@chenfengyuan/vue-qrcode": "^1.0.2", "@egjs/flicking": "^4.11.2", "@egjs/flicking-plugins": "^4.7.1", "@egjs/vue-flicking": "^4.11.2", "@ivanv/vue-collapse-transition": "^1.0.2", "@nuxt/content": "^1.15.1", "@nuxtjs/axios": "^5.13.6", "@nuxtjs/firebase": "^8.2.2", "@nuxtjs/gtm": "^2.4.0", "@nuxtjs/i18n": "^7.3.1", "@nuxtjs/recaptcha": "^1.1.2", "@nuxtjs/sitemap": "^2.4.0", "@splitbee/web": "^0.3.0", "@stripe/stripe-js": "^1.54.2", "@vercel/analytics": "^0.1.5", "@vue-stripe/vue-stripe": "^4.3.2", "browser-image-compression": "^2.0.0", "chart.js": "^2.9.4", "cookie-parser": "^1.4.6", "cookie-universal-nuxt": "^2.2.2", "copy-to-clipboard": "^3.3.3", "core-js": "^3.19.3", "dom-to-image": "^2.6.0", "dotenv": "^16.0.1", "file-saver": "^2.0.5", "filesaver": "^0.0.13", "firebase": "^9.14.0", "flag-icons": "^7.2.3", "jszip": "^3.10.1", "marked": "^15.0.11", "md5": "^2.3.0", "nuxt": "^2.15.8", "portal-vue": "^2.1.7", "posthog-js": "^1.194.3", "posthog-node": "^4.0.1", "prism-themes": "^1.9.0", "redirect-ssl": "^3.0.0", "resemblejs": "^4.1.0", "socket.io-client": "^4.7.2", "spark-md5": "^3.0.2", "uuid": "^10.0.0", "v-lazy-image": "^1.4.0", "vue": "^2.6.14", "vue-chartjs": "^3.5.1", "vue-confetti": "^2.3.0", "vue-i18n": "^8.28.2", "vue-image-compare": "^0.6.1", "vue-masonry": "^0.13.0", "vue-server-renderer": "^2.6.14", "vue-stripe-js": "^2.0.2", "vue-template-compiler": "^2.6.14", "vue-toast-notification": "^0.6.2", "webpack": "^4.46.0"}, "devDependencies": {"@egoist/tailwindcss-icons": "^1.4.0", "@iconify-json/heroicons": "^1.1.15", "@nuxtjs/eslint-config": "^6.0.1", "@nuxtjs/eslint-module": "^3.0.2", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.9", "autoprefixer": "^10.4.13", "babel-eslint": "^10.1.0", "eslint": "^7.29.0", "eslint-plugin-nuxt": "^2.0.0", "eslint-plugin-vue": "^7.12.1", "postcss": "^8.4.21", "prettier-plugin-tailwindcss": "^0.2.3", "tailwindcss": "^3.4.3"}}