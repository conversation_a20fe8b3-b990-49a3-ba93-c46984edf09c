@tailwind base;
@tailwind components;
@tailwind utilities;

/* @font-face {
    font-family: "Clarika";
    src: url("https://cdn.headline99.com/fonts/Clarika/ClarikaGeoBold.woff2");
    font-weight: 700;
    font-display: swap;
}
@font-face {
    font-family: "Clarika";
    src: url("https://cdn.headline99.com/fonts/Clarika/ClarikaGeoDemiBold.woff2");
    font-weight: 600;
    font-display: swap;
}
@font-face {
    font-family: "Clarika";
    src: url("https://cdn.headline99.com/fonts/Clarika/ClarikaGeoMedium.woff2");
    font-weight: 500;
    font-display: swap;
}
@font-face {
    font-family: "Clarika";
    src: url("https://cdn.headline99.com/fonts/Clarika/ClarikaGeoRegular.woff2");
    font-weight: 400;
    font-display: swap;
} */

.overflow-y-auto {
    -ms-overflow-style: none; /* for Internet Explorer, Edge */
    scrollbar-width: none; /* for Firefox */
    overflow-y: scroll;
}

div::-webkit-scrollbar {
    display: none; /* for Chrome, Safari, and Opera */
}

@keyframes slide {
  0% {
    transform: translate(0);
  }
  100% {
    transform: translate(-1990px); /* The image width */
  }
}
body {
  @apply  bg-gray-200;
}

body:has(.iframe-site), .iframe-site {
  @apply bg-transparent;
}

.iframe-site .chat-button {
  display: none;
}

html{
  scroll-behavior: smooth;
}


.overflow-y-auto {
    -ms-overflow-style: none; /* for Internet Explorer, Edge */
    scrollbar-width: none; /* for Firefox */
    overflow-y: scroll;
}

div::-webkit-scrollbar {
    display: none; /* for Chrome, Safari, and Opera */
}

.gradient-bg {
    content: "";
    background: linear-gradient(60deg,#f79533,#f37055,#ef4e7b,#a166ab,#5073b8,#1098ad,#07b39b,#6fba82);
    -webkit-animation: animatedgradient 3s ease infinite alternate;
    animation: animatedgradient 3s ease infinite alternate;
    background-size: 300% 300%;
}

@keyframes animatedgradient {
0% {
    background-position: 0% 50%;
}
50% {
    background-position: 100% 50%;
}
100% {
    background-position: 0% 50%;
}
}

.max-h-screen-header{
  @media all and (min-width:1024px){
    max-height:calc(100vh - 58px);
  }
  @media all and (max-width:1024px){
    max-height: calc(100vh - 118px)
  }
  overflow:hidden;
  overflow-y:auto;
}


.infinite-progress {
  animation: infiniteProgress 1s linear infinite;
}

@keyframes infiniteProgress {
  from {
    left: 0;
  }

  to {
    left: 100%;
  }
}

.flicking-pagination {
  position: relative;
  bottom: 0;
  margin-top: 1rem;
  display: flex;
  justify-content: center;
}

.flicking-pagination-bullet {
  width: 0.625rem;
  height: 0.625rem;
  display: block;
  margin: 0 0.25rem;
  border-radius: 9999px;
  border: solid 1px rgba(28, 25, 23, var(--tw-border-opacity));
  background-color: transparent;
}

.flicking-pagination-bullet-active {
  background-color: rgba(28, 25, 23, var(--tw-border-opacity));
}

.zoom-container {
  width: 100%; /* Adjust based on your needs */
  height: 100%; /* Adjust based on your needs */
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  cursor: zoom-in;
}


.card {
  @apply flex flex-col justify-start items-start p-6 gap-6 flex-1 rounded-lg border border-[#E4E4E7] bg-white shadow-sm;
}


.toast-container {
  position: fixed;
  z-index: 9999;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.toast-container.top-right {
  top: 0;
  right: 0;
}

.toast-container.top-left {
  top: 0;
  left: 0;
}

.toast-container.bottom-right {
  bottom: 0;
  right: 0;
}

.toast-container.bottom-left {
  bottom: 0;
  left: 0;
}

.toast-notification {
  min-width: 250px;
  padding: 1rem;
  border-radius: 4px;
  background: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  cursor: pointer;
}

.toast-notification .toast-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toast-notification .close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0 0.5rem;
  margin-left: 0.5rem;
}

.toast-notification.success {
  background: #4caf50;
  color: white;
}

.toast-notification.error {
  background: #f44336;
  color: white;
}

.toast-notification.warning {
  background: #ff9800;
  color: white;
}

.toast-notification.info {
  background: #2196f3;
  color: white;
}

/* Transition animations */
.toast-enter-active, .toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter, .toast-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

/* Loading */


.vue-loading.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;  
}

.vue-loading .loading-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  max-width: 400px;
}

.vue-loading .loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.vue-loading .loading-enter-active, .vue-loading .loading-leave-active {
  transition: opacity 0.3s;
}

.vue-loading .loading-enter, .vue-loading .loading-leave-to {
  opacity: 0;
}

.vue-loading .loading-content {
  display: inline-flex;
  align-items: center;
  gap: 1rem;
}

.vue-loading .loading-title {
  font-size: 1rem;
  font-weight: 500;
}


.vue-loading .loading-description {
  font-size: 1rem;
  font-weight: 400;
  color: #666;
}

/* Popup Styles */
.vue-popup.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.vue-popup .popup-container {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  max-width: 480px;
  width: 90%;
}

.vue-popup .popup-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  text-align: left;
}

.vue-popup .popup-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
}

.vue-popup .popup-description {
  font-size: 1rem;
  color: #666;
  line-height: 1.5;
}

.vue-popup .popup-button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.vue-popup .popup-button:hover {
  background-color: #2980b9;
}

.popup-enter-active, .popup-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.popup-enter, .popup-leave-to {
  opacity: 0;
  transform: scale(0.9);
}
