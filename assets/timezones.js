// 20231009101525
// https://gist.githubusercontent.com/riamaria/d08b352bc671f6615bd9/raw/57200f042af7f464e21f687e2fbb27b872c043ff/timezones-per-country.json

module.exports = {
  AF: [
    'Asia/Kabul'
  ],
  AX: [
    'Europe/Mariehamn'
  ],
  AL: [
    'Europe/Tirane'
  ],
  DZ: [
    'Africa/Algiers'
  ],
  AS: [
    'Pacific/Pago_Pago'
  ],
  AD: [
    'Europe/Andorra'
  ],
  AO: [
    'Africa/Luanda'
  ],
  AI: [
    'America/Anguilla'
  ],
  AQ: [
    'Antarctica/McMurdo',
    'Antarctica/South_Pole',
    'Antarctica/Rothera',
    'Antarctica/Palmer',
    'Antarctica/Mawson',
    'Antarctica/Davis',
    'Antarctica/Casey',
    'Antarctica/Vostok',
    'Antarctica/DumontDUrville',
    'Antarctica/Syowa'
  ],
  AG: [
    'America/Antigua'
  ],
  AR: [
    'America/Argentina/Buenos_Aires',
    'America/Argentina/Cordoba',
    'America/Argentina/Salta',
    'America/Argentina/Jujuy',
    'America/Argentina/Tucuman',
    'America/Argentina/Catamarca',
    'America/Argentina/La_Rioja',
    'America/Argentina/San_Juan',
    'America/Argentina/Mendoza',
    'America/Argentina/San_Luis',
    'America/Argentina/Rio_Gallegos',
    'America/Argentina/Ushuaia'
  ],
  AM: [
    'Asia/Yerevan'
  ],
  AW: [
    'America/Aruba'
  ],
  AU: [
    'Australia/Lord_Howe',
    'Antarctica/Macquarie',
    'Australia/Hobart',
    'Australia/Currie',
    'Australia/Melbourne',
    'Australia/Sydney',
    'Australia/Broken_Hill',
    'Australia/Brisbane',
    'Australia/Lindeman',
    'Australia/Adelaide',
    'Australia/Darwin',
    'Australia/Perth',
    'Australia/Eucla'
  ],
  AT: [
    'Europe/Vienna'
  ],
  AZ: [
    'Asia/Baku'
  ],
  BS: [
    'America/Nassau'
  ],
  BH: [
    'Asia/Bahrain'
  ],
  BD: [
    'Asia/Dhaka'
  ],
  BB: [
    'America/Barbados'
  ],
  BY: [
    'Europe/Minsk'
  ],
  BE: [
    'Europe/Brussels'
  ],
  BZ: [
    'America/Belize'
  ],
  BJ: [
    'Africa/Porto-Novo'
  ],
  BM: [
    'Atlantic/Bermuda'
  ],
  BT: [
    'Asia/Thimphu'
  ],
  BO: [
    'America/La_Paz'
  ],
  BA: [
    'Europe/Sarajevo'
  ],
  BW: [
    'Africa/Gaborone'
  ],
  BV: [
    null
  ],
  BR: [
    'America/Noronha',
    'America/Belem',
    'America/Fortaleza',
    'America/Recife',
    'America/Araguaina',
    'America/Maceio',
    'America/Bahia',
    'America/Sao_Paulo',
    'America/Campo_Grande',
    'America/Cuiaba',
    'America/Santarem',
    'America/Porto_Velho',
    'America/Boa_Vista',
    'America/Manaus',
    'America/Eirunepe',
    'America/Rio_Branco'
  ],
  IO: [
    'Indian/Chagos'
  ],
  BN: [
    'Asia/Brunei'
  ],
  BG: [
    'Europe/Sofia'
  ],
  BF: [
    'Africa/Ouagadougou'
  ],
  BI: [
    'Africa/Bujumbura'
  ],
  KH: [
    'Asia/Phnom_Penh'
  ],
  CM: [
    'Africa/Douala'
  ],
  CA: [
    'America/St_Johns',
    'America/Halifax',
    'America/Glace_Bay',
    'America/Moncton',
    'America/Goose_Bay',
    'America/Blanc-Sablon',
    'America/Montreal',
    'America/Toronto',
    'America/Nipigon',
    'America/Thunder_Bay',
    'America/Iqaluit',
    'America/Pangnirtung',
    'America/Resolute',
    'America/Atikokan',
    'America/Rankin_Inlet',
    'America/Winnipeg',
    'America/Rainy_River',
    'America/Regina',
    'America/Swift_Current',
    'America/Edmonton',
    'America/Cambridge_Bay',
    'America/Yellowknife',
    'America/Inuvik',
    'America/Creston',
    'America/Dawson_Creek',
    'America/Vancouver',
    'America/Whitehorse',
    'America/Dawson'
  ],
  CV: [
    'Atlantic/Cape_Verde'
  ],
  KY: [
    'America/Cayman'
  ],
  CF: [
    'Africa/Bangui'
  ],
  TD: [
    'Africa/Ndjamena'
  ],
  CL: [
    'America/Santiago',
    'Pacific/Easter'
  ],
  CN: [
    'Asia/Shanghai',
    'Asia/Harbin',
    'Asia/Chongqing',
    'Asia/Urumqi',
    'Asia/Kashgar'
  ],
  CX: [
    'Indian/Christmas'
  ],
  CC: [
    'Indian/Cocos'
  ],
  CO: [
    'America/Bogota'
  ],
  KM: [
    'Indian/Comoro'
  ],
  CG: [
    'Africa/Brazzaville'
  ],
  CD: [
    'Africa/Kinshasa',
    'Africa/Lubumbashi'
  ],
  CK: [
    'Pacific/Rarotonga'
  ],
  CR: [
    'America/Costa_Rica'
  ],
  CI: [
    'Africa/Abidjan'
  ],
  HR: [
    'Europe/Zagreb'
  ],
  CU: [
    'America/Havana'
  ],
  CY: [
    'Asia/Nicosia'
  ],
  CZ: [
    'Europe/Prague'
  ],
  DK: [
    'Europe/Copenhagen'
  ],
  DJ: [
    'Africa/Djibouti'
  ],
  DM: [
    'America/Dominica'
  ],
  DO: [
    'America/Santo_Domingo'
  ],
  EC: [
    'America/Guayaquil',
    'Pacific/Galapagos'
  ],
  EG: [
    'Africa/Cairo'
  ],
  SV: [
    'America/El_Salvador'
  ],
  GQ: [
    'Africa/Malabo'
  ],
  ER: [
    'Africa/Asmara'
  ],
  EE: [
    'Europe/Tallinn'
  ],
  ET: [
    'Africa/Addis_Ababa'
  ],
  FK: [
    'Atlantic/Stanley'
  ],
  FO: [
    'Atlantic/Faroe'
  ],
  FJ: [
    'Pacific/Fiji'
  ],
  FI: [
    'Europe/Helsinki'
  ],
  FR: [
    'Europe/Paris'
  ],
  GF: [
    'America/Cayenne'
  ],
  PF: [
    'Pacific/Tahiti',
    'Pacific/Marquesas',
    'Pacific/Gambier'
  ],
  TF: [
    'Indian/Kerguelen'
  ],
  GA: [
    'Africa/Libreville'
  ],
  GM: [
    'Africa/Banjul'
  ],
  GE: [
    'Asia/Tbilisi'
  ],
  DE: [
    'Europe/Berlin',
    'Europe/Busingen'
  ],
  GH: [
    'Africa/Accra'
  ],
  GI: [
    'Europe/Gibraltar'
  ],
  GR: [
    'Europe/Athens'
  ],
  GL: [
    'America/Godthab',
    'America/Danmarkshavn',
    'America/Scoresbysund',
    'America/Thule'
  ],
  GD: [
    'America/Grenada'
  ],
  GP: [
    'America/Guadeloupe'
  ],
  GU: [
    'Pacific/Guam'
  ],
  GT: [
    'America/Guatemala'
  ],
  GG: [
    'Europe/Guernsey'
  ],
  GN: [
    'Africa/Conakry'
  ],
  GW: [
    'Africa/Bissau'
  ],
  GY: [
    'America/Guyana'
  ],
  HT: [
    'America/Port-au-Prince'
  ],
  HM: [
    null
  ],
  VA: [
    'Europe/Vatican'
  ],
  HN: [
    'America/Tegucigalpa'
  ],
  HK: [
    'Asia/Hong_Kong'
  ],
  HU: [
    'Europe/Budapest'
  ],
  IS: [
    'Atlantic/Reykjavik'
  ],
  IN: [
    'Asia/Kolkata'
  ],
  ID: [
    'Asia/Jakarta',
    'Asia/Pontianak',
    'Asia/Makassar',
    'Asia/Jayapura'
  ],
  IR: [
    'Asia/Tehran'
  ],
  IQ: [
    'Asia/Baghdad'
  ],
  IE: [
    'Europe/Dublin'
  ],
  IM: [
    'Europe/Isle_of_Man'
  ],
  IL: [
    'Asia/Jerusalem'
  ],
  IT: [
    'Europe/Rome'
  ],
  JM: [
    'America/Jamaica'
  ],
  JP: [
    'Asia/Tokyo'
  ],
  JE: [
    'Europe/Jersey'
  ],
  JO: [
    'Asia/Amman'
  ],
  KZ: [
    'Asia/Almaty',
    'Asia/Qyzylorda',
    'Asia/Aqtobe',
    'Asia/Aqtau',
    'Asia/Oral'
  ],
  KE: [
    'Africa/Nairobi'
  ],
  KI: [
    'Pacific/Tarawa',
    'Pacific/Enderbury',
    'Pacific/Kiritimati'
  ],
  KP: [
    'Asia/Pyongyang'
  ],
  KR: [
    'Asia/Seoul'
  ],
  KW: [
    'Asia/Kuwait'
  ],
  KG: [
    'Asia/Bishkek'
  ],
  LA: [
    'Asia/Vientiane'
  ],
  LV: [
    'Europe/Riga'
  ],
  LB: [
    'Asia/Beirut'
  ],
  LS: [
    'Africa/Maseru'
  ],
  LR: [
    'Africa/Monrovia'
  ],
  LY: [
    'Africa/Tripoli'
  ],
  LI: [
    'Europe/Vaduz'
  ],
  LT: [
    'Europe/Vilnius'
  ],
  LU: [
    'Europe/Luxembourg'
  ],
  MO: [
    'Asia/Macau'
  ],
  MK: [
    'Europe/Skopje'
  ],
  MG: [
    'Indian/Antananarivo'
  ],
  MW: [
    'Africa/Blantyre'
  ],
  MY: [
    'Asia/Kuala_Lumpur',
    'Asia/Kuching'
  ],
  MV: [
    'Indian/Maldives'
  ],
  ML: [
    'Africa/Bamako'
  ],
  MT: [
    'Europe/Malta'
  ],
  MH: [
    'Pacific/Majuro',
    'Pacific/Kwajalein'
  ],
  MQ: [
    'America/Martinique'
  ],
  MR: [
    'Africa/Nouakchott'
  ],
  MU: [
    'Indian/Mauritius'
  ],
  YT: [
    'Indian/Mayotte'
  ],
  MX: [
    'America/Mexico_City',
    'America/Cancun',
    'America/Merida',
    'America/Monterrey',
    'America/Matamoros',
    'America/Mazatlan',
    'America/Chihuahua',
    'America/Ojinaga',
    'America/Hermosillo',
    'America/Tijuana',
    'America/Santa_Isabel',
    'America/Bahia_Banderas'
  ],
  FM: [
    'Pacific/Chuuk',
    'Pacific/Pohnpei',
    'Pacific/Kosrae'
  ],
  MD: [
    'Europe/Chisinau'
  ],
  MC: [
    'Europe/Monaco'
  ],
  MN: [
    'Asia/Ulaanbaatar',
    'Asia/Hovd',
    'Asia/Choibalsan'
  ],
  ME: [
    'Europe/Podgorica'
  ],
  MS: [
    'America/Montserrat'
  ],
  MA: [
    'Africa/Casablanca'
  ],
  MZ: [
    'Africa/Maputo'
  ],
  MM: [
    'Asia/Rangoon'
  ],
  NA: [
    'Africa/Windhoek'
  ],
  NR: [
    'Pacific/Nauru'
  ],
  NP: [
    'Asia/Kathmandu'
  ],
  NL: [
    'Europe/Amsterdam'
  ],
  AN: [
    null
  ],
  NC: [
    'Pacific/Noumea'
  ],
  NZ: [
    'Pacific/Auckland',
    'Pacific/Chatham'
  ],
  NI: [
    'America/Managua'
  ],
  NE: [
    'Africa/Niamey'
  ],
  NG: [
    'Africa/Lagos'
  ],
  NU: [
    'Pacific/Niue'
  ],
  NF: [
    'Pacific/Norfolk'
  ],
  MP: [
    'Pacific/Saipan'
  ],
  NO: [
    'Europe/Oslo'
  ],
  OM: [
    'Asia/Muscat'
  ],
  PK: [
    'Asia/Karachi'
  ],
  PW: [
    'Pacific/Palau'
  ],
  PS: [
    'Asia/Gaza',
    'Asia/Hebron'
  ],
  PA: [
    'America/Panama'
  ],
  PG: [
    'Pacific/Port_Moresby'
  ],
  PY: [
    'America/Asuncion'
  ],
  PE: [
    'America/Lima'
  ],
  PH: [
    'Asia/Manila'
  ],
  PN: [
    'Pacific/Pitcairn'
  ],
  PL: [
    'Europe/Warsaw'
  ],
  PT: [
    'Europe/Lisbon',
    'Atlantic/Madeira',
    'Atlantic/Azores'
  ],
  PR: [
    'America/Puerto_Rico'
  ],
  QA: [
    'Asia/Qatar'
  ],
  RE: [
    'Indian/Reunion'
  ],
  RO: [
    'Europe/Bucharest'
  ],
  RU: [
    'Europe/Kaliningrad',
    'Europe/Moscow',
    'Europe/Volgograd',
    'Europe/Samara',
    'Asia/Yekaterinburg',
    'Asia/Omsk',
    'Asia/Novosibirsk',
    'Asia/Novokuznetsk',
    'Asia/Krasnoyarsk',
    'Asia/Irkutsk',
    'Asia/Yakutsk',
    'Asia/Khandyga',
    'Asia/Vladivostok',
    'Asia/Sakhalin',
    'Asia/Ust-Nera',
    'Asia/Magadan',
    'Asia/Kamchatka',
    'Asia/Anadyr'
  ],
  RW: [
    'Africa/Kigali'
  ],
  BL: [
    'America/St_Barthelemy'
  ],
  SH: [
    'Atlantic/St_Helena'
  ],
  KN: [
    'America/St_Kitts'
  ],
  LC: [
    'America/St_Lucia'
  ],
  MF: [
    'America/Marigot'
  ],
  PM: [
    'America/Miquelon'
  ],
  VC: [
    'America/St_Vincent'
  ],
  WS: [
    'Pacific/Apia'
  ],
  SM: [
    'Europe/San_Marino'
  ],
  ST: [
    'Africa/Sao_Tome'
  ],
  SA: [
    'Asia/Riyadh'
  ],
  SN: [
    'Africa/Dakar'
  ],
  RS: [
    'Europe/Belgrade'
  ],
  SC: [
    'Indian/Mahe'
  ],
  SL: [
    'Africa/Freetown'
  ],
  SG: [
    'Asia/Singapore'
  ],
  SK: [
    'Europe/Bratislava'
  ],
  SI: [
    'Europe/Ljubljana'
  ],
  SB: [
    'Pacific/Guadalcanal'
  ],
  SO: [
    'Africa/Mogadishu'
  ],
  GS: [
    'Atlantic/South_Georgia'
  ],
  ES: [
    'Europe/Madrid',
    'Africa/Ceuta',
    'Atlantic/Canary'
  ],
  LK: [
    'Asia/Colombo'
  ],
  SD: [
    'Africa/Khartoum'
  ],
  SR: [
    'America/Paramaribo'
  ],
  SJ: [
    'Arctic/Longyearbyen'
  ],
  SZ: [
    'Africa/Mbabane'
  ],
  SE: [
    'Europe/Stockholm'
  ],
  CH: [
    'Europe/Zurich'
  ],
  SY: [
    'Asia/Damascus'
  ],
  TW: [
    'Asia/Taipei'
  ],
  TJ: [
    'Asia/Dushanbe'
  ],
  TZ: [
    'Africa/Dar_es_Salaam'
  ],
  TH: [
    'Asia/Bangkok'
  ],
  TL: [
    'Asia/Dili'
  ],
  TG: [
    'Africa/Lome'
  ],
  TK: [
    'Pacific/Fakaofo'
  ],
  TO: [
    'Pacific/Tongatapu'
  ],
  TT: [
    'America/Port_of_Spain'
  ],
  TN: [
    'Africa/Tunis'
  ],
  TR: [
    'Europe/Istanbul'
  ],
  TM: [
    'Asia/Ashgabat'
  ],
  TC: [
    'America/Grand_Turk'
  ],
  TV: [
    'Pacific/Funafuti'
  ],
  UG: [
    'Africa/Kampala'
  ],
  UA: [
    'Europe/Kiev',
    'Europe/Uzhgorod',
    'Europe/Zaporozhye',
    'Europe/Simferopol'
  ],
  AE: [
    'Asia/Dubai'
  ],
  GB: [
    'Europe/London'
  ],
  US: [
    'America/New_York',
    'America/Detroit',
    'America/Kentucky/Louisville',
    'America/Kentucky/Monticello',
    'America/Indiana/Indianapolis',
    'America/Indiana/Vincennes',
    'America/Indiana/Winamac',
    'America/Indiana/Marengo',
    'America/Indiana/Petersburg',
    'America/Indiana/Vevay',
    'America/Chicago',
    'America/Indiana/Tell_City',
    'America/Indiana/Knox',
    'America/Menominee',
    'America/North_Dakota/Center',
    'America/North_Dakota/New_Salem',
    'America/North_Dakota/Beulah',
    'America/Denver',
    'America/Boise',
    'America/Shiprock',
    'America/Phoenix',
    'America/Los_Angeles',
    'America/Anchorage',
    'America/Juneau',
    'America/Sitka',
    'America/Yakutat',
    'America/Nome',
    'America/Adak',
    'America/Metlakatla',
    'Pacific/Honolulu'
  ],
  UM: [
    'Pacific/Johnston',
    'Pacific/Midway',
    'Pacific/Wake'
  ],
  UY: [
    'America/Montevideo'
  ],
  UZ: [
    'Asia/Samarkand',
    'Asia/Tashkent'
  ],
  VU: [
    'Pacific/Efate'
  ],
  VE: [
    'America/Caracas'
  ],
  VN: [
    'Asia/Ho_Chi_Minh'
  ],
  VG: [
    'America/Tortola'
  ],
  VI: [
    'America/St_Thomas'
  ],
  WF: [
    'Pacific/Wallis'
  ],
  EH: [
    'Africa/El_Aaiun'
  ],
  YE: [
    'Asia/Aden'
  ],
  ZM: [
    'Africa/Lusaka'
  ],
  ZW: [
    'Africa/Harare'
  ],
  ZA: [
    'Africa/Johannesburg'
  ]
}
