require('dotenv').config({})
const redirectSSL = require('redirect-ssl')
const isProd = process.env.NODE_ENV === 'production'
const redirectMiddleware = (req, res, next) => {
  const isAddressLocal = req.connection.remoteAddress !== '127.0.0.1' || ''
  const enabled = isProd && isAddressLocal
  return enabled ? redirectSSL(req, res, next) : next()
}

const serverUrl = isProd
  ? 'https://server.headshotpro.com'
  : 'http://localhost:5000'

const axios = require('axios')

// const metaTitle = "Professional AI Headshot Generator: Done in 2 Hours";
const metaTitle = "The #1 AI Headshot Generator for Professional Headshots";
const metaDescription = "The AI headshot generator used by Fortune 500 companies. Create professional grade AI headshots with HeadshotPro. Best for professional business headshots.";
// const metaDescription = "Profile-worthy professional headshots generated for 60,497+ happy customers. Real AI headshot examples show our AI headshot generator is better.";
// const metaDescription = "Professional headshots by the top rated AI headshot generator. We turn your selfies into studio-quality headshots in just 2 hours. 60,497+ happy customers.";
// const metaTitle = "HeadshotPro: Professional Headshots with AI";
// const metaDescription = "Professional corporate headshots made easy! Upload photos, pick style & receive 120+ generated headshots. Perfect for you or your team.";

export default {
  target: 'static',
  // Global page headers: https://go.nuxtjs.dev/config-head
  head: {
    title: metaTitle,
    htmlAttrs: {
      lang: 'en'
    },
    // script: [
    //   { src: 'https://cdn-4.convertexperiments.com/js/1004935-1004866.js', mode: 'client', },
    // ],
    meta: [
      { charset: 'utf-8' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
      { hid: 'description', name: 'description', content: metaDescription },
      { name: 'format-detection', content: 'telephone=no' },
      {
        name: 'viewport',
        content: 'width=device-width, initial-scale=1, maximum-scale=1'
      },
      {
        hid: 'facebook-domain-verification',
        name: 'facebook-domain-verification',
        content: 'ssavonjxv8xvj10ipq69dnj5kfamqf'
      },
      {
        hid: 'thumbnail',
        name: 'thumbnail',
        content: process.env.BASE_URL + '/thumbnail.png'
      },
      {
        hid: 'twitter:title',
        name: 'twitter:title',
        content: metaTitle
      },
      {
        hid: 'twitter:creator',
        name: 'twitter:creator',
        content: '@dannypostmaa'
      },
      {
        hid: 'twitter:site',
        name: 'twitter:site',
        content: process.env.BASE_URL
      },
      {
        hid: 'twitter:description',
        name: 'twitter:description',
        content: metaDescription
      },
      {
        hid: 'twitter:card',
        name: 'twitter:card',
        content: 'summary_large_image'
      },
      {
        hid: 'twitter:image:src',
        name: 'twitter:image:src',
        content: process.env.BASE_URL + '/og-main.jpg'
      },
      {
        hid: 'twitter:image',
        name: 'twitter:image',
        content: process.env.BASE_URL + '/og-main.jpg'
      },
      {
        hid: 'twitter:image:alt',
        name: 'twitter:image:alt',
        content: metaTitle
      },
      {
        hid: 'og:title',
        property: 'og:title',
        content: metaTitle
      },
      {
        hid: 'og:description',
        property: 'og:description',
        content: metaDescription
      },
      {
        hid: 'og:image',
        property: 'og:image',
        content: process.env.BASE_URL + '/og-main.jpg'
      },
      {
        hid: 'og:image:secure_url',
        property: 'og:image:secure_url',
        content: process.env.BASE_URL + '/og-main.jpg'
      },
      {
        hid: 'og:image:alt',
        property: 'og:image:alt',
        content: metaTitle
      }
    ],
    link: [{ rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }]
  },

  // Global CSS: https://go.nuxtjs.dev/config-css
  css: ['@/node_modules/flag-icons/css/flag-icons.min.css', '@/assets/css/main.css', '@/assets/css/theme-sugar.css'],

  // Plugins to run before rendering page: https://go.nuxtjs.dev/config-plugins
  plugins: [
    '@/plugins/mixins.js',
    '@/plugins/localization.js',
    '@/plugins/parity.js',
    '@/plugins/axios.js',
    '@/plugins/vue-portal.js',
    '@/plugins/modal.js',
    '@/plugins/flicker.js',
    '@/plugins/facebook.js',
    // "@/plugins/vue-masonry.js",
    { src: '~/plugins/vue-masonry.js', mode: 'client' },
    { src: '~/plugins/vue-loading.js', mode: 'client' },
    { src: '~/plugins/router.js', mode: 'client' },
    { src: '~/plugins/posthog.js', mode: 'client' },
    { src: '~/plugins/stripe.js', mode: 'client' },
    { src: '~/plugins/convert.js', mode: 'client', defer: true },
    { src: '~/plugins/gtm.js', mode: 'client', defer: true },
    { src: '~/plugins/vue-toast.js', mode: 'client', defer: true },
    { src: '~/plugins/vue-popup.js', mode: 'client', defer: true },
    { src: '~/plugins/rewardful.js', mode: 'client', defer: true },
    { src: '~/plugins/vue-image-comparison.js', mode: 'client', defer: true },
    { src: '~/plugins/vue-confetti.js', mode: 'client', defer: true },
    ...(process.env.BENTO_SITE_UUID ? [{ src: "~/plugins/bento.js", mode: "client", defer: true }] : []),
    //  ...(process.env.TIKTOK_ID ? [{ src: "~/plugins/tiktok.js", mode: "client", defer: true }] : []),
  ],

  serverMiddleware: [
    redirectMiddleware,
    '~/server-middlewares/redirects.js'
  ],

  env: {
    NODE_ENV: process.env.NODE_ENV,
    SERVER_URL: process.env.SERVER_URL,
    SOCKET_URL: process.env.SOCKET_URL,
    BASE_URL: process.env.BASE_URL,
    STRIPE_PK: process.env.STRIPE_PK,
    // GOOGLE_API_KEY: process.env.GOOGLE_API_KEY,
    // GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
    BENTO_SITE_UUID: process.env.BENTO_SITE_UUID,
    HOLIDAY_MODE: process.env.HOLIDAY_MODE,
    HOLIDAY_END_DATE: process.env.HOLIDAY_END_DATE,
    MAINTENANCE_MODE: process.env.MAINTENANCE_MODE,
    STRIPE_PUBLISHABLE_KEY: process.env.STRIPE_PUBLISHABLE_KEY,
  },
  publicRuntimeConfig: {
    STRIPE_PUBLISHABLE_KEY: process.env.STRIPE_PUBLISHABLE_KEY,
    SOCKET_URL: process.env.SOCKET_URL,
    SERVER_URL: process.env.SERVER_URL,
    NODE_ENV: process.env.NODE_ENV,
    baseURL: process.env.NUXT_BASE_URL,
    STRIPE_PK: process.env.STRIPE_PK,
    BASE_URL: process.env.BASE_URL,
    HOLIDAY_MODE: process.env.HOLIDAY_MODE,
    HOLIDAY_END_DATE: process.env.HOLIDAY_END_DATE,
    MAINTENANCE_MODE: process.env.MAINTENANCE_MODE,
    // GOOGLE_API_KEY: process.env.GOOGLE_API_KEY,
    // GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
  },

  axios: {
    // Workaround to avoid enforcing hard-coded localhost:3000: https://github.com/nuxt-community/axios-module/issues/308
    baseURL: process.env.SERVER_URL
  },

  // Auto import components: https://go.nuxtjs.dev/config-components
  components: true,

  // Modules for dev and build (recommended): https://go.nuxtjs.dev/config-modules
  buildModules: [
    // Doc: https://github.com/nuxt-community/eslint-module
    [
      '@nuxtjs/eslint-module',
      {
        fix: true,
        overrideConfig: {
          parserOptions: {
            ecmaVersion: 2020
          }
        }
      }
    ],
    [
      '@nuxtjs/firebase',
      {
        config: {
          apiKey: process.env.FIREBASE_API_KEY,
          authDomain: process.env.FIREBASE_AUTH_DOMAIN,
          projectId: process.env.FIREBASE_PROJECT_ID,
          storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
          messagingSenderId: process.env.FIREBASE_MESSAGING_ID,
          appId: process.env.FIREBASE_APP_ID
        },
        services: {
          auth: true // Just as example. Can be any other service.
        },
        injectModule: true,
        lazy: false
      }
    ]
  ],
  auth: {
    persistence: 'local', // default
    initialize: {
      onAuthStateChangedMutation: 'ON_AUTH_STATE_CHANGED_MUTATION',
      onAuthStateChangedAction: 'onAuthStateChangedAction',
      subscribeManually: false
    },
    ssr: false // default
  },

  // Modules: https://go.nuxtjs.dev/config-modules
  modules: [
    '@nuxtjs/axios',
    // '@nuxtjs/sitemap',
    '@nuxt/content',
    'cookie-universal-nuxt',
    '@nuxtjs/gtm',
    '@nuxtjs/sitemap',
    [
      '@nuxtjs/recaptcha',
      {
        hideBadge: true,
        siteKey: '6LeV3y4iAAAAAOaPX2tKbINIx2Ff6Te0fIXLPnXu',
        version: 3
      }
    ],
    '@nuxtjs/i18n'
  ],

  // Content module configuration
  content: {
    markdown: {
      prism: {
        theme: 'prism-themes/themes/prism-one-light.css'
      }
    }
  },

  sitemap: {
    hostname: 'https://www.headshotpro.com',
    gzip: true,
    exclude: ['/app/**', '/app', '/styles', '/admin/**', '/lp/**'],
    routes: async () => {
      const { data } = await axios.get(serverUrl + '/sitemap')
      return data
    },
    path: '/sitemap.xml',
    generate: true,
    defaults: {
      changefreq: 'weekly',
      priority: 1,
      lastmod: new Date()
    }
  },

  gtm: {
    // id: 'AW-732004629', //GTM-W4X7FXM // G-SQR4D9VSFN
    pageTracking: true,
    scriptDefer: true,
    enabled: process.env.NODE_ENV !== 'development'
  },
  router: {
    prefetchLinks: false
  },
  render: {
    resourceHints: false
  },

  build: {
    cache: true,
    extractCSS: true,
    postcss: {
      postcssOptions: {
        plugins: {
          tailwindcss: {},
          autoprefixer: {}
        }
      }
    },
    transpile: ['posthog-js', 'marked'],
    extend(config, { isDev, isClient }) {
      // Add babel-loader configuration for posthog-js
      config.module.rules.push({
        test: /\.m?js$/,
        exclude: /node_modules\/(?!posthog-js)/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env']
          }
        }
      })
    }
    // extend(config, ctx) {
    //   if (ctx.isClient) {
    //     config.module.rules.push({
    //       test: /\.m?js$/,
    //       include: /node_modules\/posthog-node/,
    //       use: {
    //         loader: 'babel-loader',
    //         options: {
    //           presets: ['@babel/preset-env'],
    //           plugins: ['@babel/plugin-proposal-optional-chaining']
    //         }
    //       }
    //     });
    //   }
    // },
  },

  i18n: {
    defaultLocale: 'en',
    vueI18n: {
      fallbackLocale: 'en',
      silentTranslationWarn: true
    },
    locales: [
      {
        code: 'en',
        file: 'en.js'
      },
      {
        code: 'es',
        file: 'es.js'
      },
      {
        code: 'de',
        file: 'de.js'
      }
    ],
    lazy: true,
    langDir: 'lang/',
    vueI18nLoader: true,
    baseUrl: process.env.BASE_URL,
  },

  generate: {}
}
