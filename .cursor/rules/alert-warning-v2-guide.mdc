---
description: Guide for the <AlertWarningV2> component (components/alert/WarningV2.vue), a compact warning alert with a primary text prop and a slot for additional content.
globs:
alwaysApply: false
---
# Component Guide: AlertWarningV2

**File Path**: `components/alert/WarningV2.vue`
(Typically used as `<AlertWarningV2>` in templates).

## Overview
The `<AlertWarningV2>` component is another style of warning alert. It features a very light yellow/beige background, a different warning icon, and generally seems to be a more compact version, primarily taking a single `text` prop for its main message and a default slot for additional, richer content.

## Template Structure (Simplified)
```html
<div class="px-4 py-3 text-left rounded-lg bg-[#FFFAED] border border-solid border-black/10">
  <div class="flex items-start justify-start">
    <svg class="flex-shrink-0 mr-[10px] translate-y-[7px] hidden md:block"> <!-- Warning Icon (Yellow fill #FBBC04) --> </svg>
    <div>
      <p class="text-base font-medium text-[#624C0E]">
        {{ text }} <!-- Main warning text -->
      </p>
      <slot /> <!-- For additional details or actions -->
    </div>
  </div>
</div>
```
- The icon is hidden on smaller screens (`hidden md:block`).

## Props

- **`text`**:
    - Type: `String`
    - Required: No
    - Purpose: The primary text message for the warning.

## Slots

- **Default Slot**:
    - Purpose: Allows injecting additional content below the main `text`. This can be used for more detailed explanations, links, or buttons.
    - Example:
      ```html
      <AlertWarningV2 text="Automatic payments failed.">
        <p class="mt-1 text-sm text-[#886F25]">
          Please <nuxt-link to="/billing" class="font-semibold underline">update your payment method</nuxt-link> to avoid service interruption.
        </p>
      </AlertWarningV2>
      ```

## Events
- None.

## Usage Examples

1.  **Simple Warning Text**:
    ```html
    <AlertWarningV2 text="Your current plan does not include this feature. Please upgrade." />
    ```

2.  **Warning with Additional Content in Slot (from Grep results)**:
    ```html
    <!-- File: components/gallery/selection/Page.vue -->
    <AlertWarningV2
      v-if="percentageOfNsfw > 30"
      class="max-w-8xl mx-auto my-4"
      :text="`You\'re missing ${percentageOfNsfw}% of your photos!`"
    >
      <div class="mt-2 text-sm text-[#886F25]">
        <p>Some of your uploaded photos were flagged as Not Safe For Work (NSFW) and could not be processed. Consider uploading different photos to get a full set of results.</p>
      </div>
    </AlertWarningV2>
    ```

3.  **Error Message Display (from Grep results)**:
    ```html
    <!-- File: components/team/Settings/AdminManagement.vue -->
    <AlertWarningV2
      v-if="addingError"
      :text="addingError"
      class="mb-4"
    />
    ```
    *Here, `addingError` is likely a data property holding an error message string.*

## Styling
- Background: `bg-[#FFFAED]` (a very light yellow/beige).
- Border: `border border-solid border-black/10` (subtle black border).
- Text color for main `text` prop: `text-[#624C0E]` (dark brownish-yellow).
- Icon: Filled SVG, color `fill="#FBBC04"` (amber/yellow), hidden on mobile.
- The styling is generally more subdued compared to `AlertWarning`.

## Best Practices
- Use `<AlertWarningV2>` for warnings that might be slightly less critical than those requiring `AlertWarning`, or where a more integrated, less visually loud presentation is desired.
- The `text` prop should be concise.
- Use the default slot for more detailed explanations, mitigation steps, or calls to action.
- Like `AlertWarning`, it's not dismissible by default, so control its visibility using `v-if`.
