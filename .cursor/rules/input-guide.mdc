---
description: Comprehensive guide for the standard <Input> component (components/input/Input.vue), covering its various props (value, label, type, error, etc.), slots, events, and internal validation. Emphasizes preferring it over native <input>.
globs:
alwaysApply: false
---
# Component Guide: Input

**File Path**: `components/input/Input.vue`

## Overview
The `<Input>` component is the standard and preferred way to create various types of input fields in the application (e.g., text, email, number, password). It provides a consistent look and feel, along with built-in features like labels, descriptions, error states, and basic validation helpers.

**Always prefer `<Input />` over the native `<input>` element.**

## Template Structure (Simplified Internal)
```html
<div>
  <!-- Label and Description -->
  <div v-if="label || description">
    <label :for="id">{{ label }}</label>
    <slot name="action" /> <!-- Slot next to label -->
    <span>{{ description }}</span>
  </div>
  <!-- Input field -->
  <div class="relative">
    <input
      :id="id"
      :type="type"
      :value="value"
      :placeholder="placeholder"
      :disabled="disabled"
      :class="{ ... conditional classes for error, darkMode ... }"
      @input="$emit('input', $event.target.value)"
      @keypress="validateInput($event)"
      @keyup="type === 'number' ? validateInput($event) : null"
      :max="max_for_type"
      :min="min_for_type"
      :list="list"
    />
    <slot name="affix" /> <!-- Slot inside input wrapper, e.g., for an icon -->
  </div>
</div>
```

## Props

- **`value`**:
    - Type: `String | Number`
    - Default: `''`
    - Purpose: The current value of the input field. Use with `@input` event for two-way binding, or `v-model`.
- **`label`**:
    - Type: `String`
    - Default: `''`
    - Purpose: Text label displayed above the input field.
- **`description`**:
    - Type: `String`
    - Default: `''`
    - Purpose: Smaller informational text displayed below the label.
- **`placeholder`**:
    - Type: `String`
    - Default: `''`
    - Purpose: Placeholder text for the input field.
- **`id`**:
    - Type: `String`
    - Default: `''`
    - Purpose: Sets the `id` attribute for the input element. Useful for associating with a `<label for="...">` if the label is external, though the component handles its own label display.
- **`type`**:
    - Type: `String`
    - Default: `'text'`
    - Purpose: The type of the input field (e.g., `text`, `email`, `password`, `number`, `url`, `tel`).
- **`disabled`**:
    - Type: `Boolean`
    - Default: `false`
    - Purpose: If `true`, disables the input field and styles it accordingly.
- **`error`**:
    - Type: `Boolean`
    - Default: `false`
    - Purpose: If `true`, applies error styling (e.g., red border) to the input field.
- **`darkMode`**:
    - Type: `Boolean`
    - Default: `false`
    - Purpose: Applies a dark mode theme to the input field (e.g., light text on dark background).
- **`numbersOnly`**:
    - Type: `Boolean`
    - Default: `false`
    - Purpose: If `true`, restricts input to allow only numeric characters. Implemented via the internal `inputNumbersOnly` method (not shown in snippet, but present in actual component).
- **`charRegex`**:
    - Type: `String`
    - Default: `''`
    - Purpose: A string representing a regex character set (e.g., `'a-zA-Z0-9'`) to restrict allowed characters. The component internally creates `new RegExp('([' + this.charRegex + '])')`.
- **`min`**:
    - Type: `Number`
    - Default: `0`
    - Purpose: For `type="number"` or `type="range"`, sets the minimum allowed value.
- **`max`**:
    - Type: `Number`
    - Default: `999999999`
    - Purpose: For `type="number"` or `type="range"`, sets the maximum allowed value. For other types like `text`, it can act as a `maxlength` (though this is handled by preventing default on key events if length exceeds max, not a standard `maxlength` attribute).
- **`list`**:
    - Type: `String`
    - Default: `null`
    - Purpose: Corresponds to the `list` attribute on an `<input>`, used to link to a `<datalist>` element by its ID.
- **`inputClass`**:
    - Type: `String`
    - Default: `''`
    - Purpose: Allows passing additional custom CSS classes directly to the `<input>` element itself.

## Slots

- **`action`**:
    - Purpose: Allows injecting content (like a button or link) to the right of the label.
- **`affix`**:
    - Purpose: Allows injecting content (like an icon) inside the input field's container, typically to the right or left of the actual input area. Styling might be needed for precise positioning.

## Events

- **`input`**:
    - Payload: The new value of the input field (`$event.target.value`).
    - Purpose: Emitted whenever the input value changes. Use this for `v-model` or manual two-way binding.
    - Example: `@input="formData.email = $event"` or `v-model="formData.email"`.

## Internal Validation Logic
- The component has an internal `validateInput` method triggered on `keypress` and `keyup` (for number type).
- If `numbersOnly` is true, it calls `inputNumbersOnly` (not shown, but assumed to filter non-numeric keys).
- If `charRegex` is provided, it calls `charRegexOnly` which allows only characters matching the regex.
- For `type="number"` or `type="range"`, it enforces `min` and `max` values, emitting an input event with the clamped value.
- For other types, if `event.target.value.length > this.max`, it prevents further input (effectively a maxlength).

## Usage Examples

1.  **Basic Text Input with Label and Placeholder**:
    ```html
    <Input
      v-model="form.name"
      label="Full Name"
      placeholder="Enter your full name"
    />
    ```

2.  **Email Input with Error State and Description**:
    ```html
    <Input
      v-model="form.email"
      label="Email Address"
      type="email"
      placeholder="<EMAIL>"
      description="We will never share your email."
      :error="!!emailErrorMEssage"
    />
    ```

3.  **Number Input with Min/Max and `numbersOnly`**:
    ```html
    <Input
      v-model.number="form.age"
      label="Age"
      type="number"
      :min="18"
      :max="120"
      numbersOnly
    />
    ```
    *Note*: `v-model.number` is a Vue modifier for automatic type casting.

4.  **Input with Custom Character Regex (Alphanumeric)**:
    ```html
    <Input
      v-model="form.username"
      label="Username"
      char-regex="a-zA-Z0-9_"
      description="Only letters, numbers, and underscores allowed."
    />
    ```
    *Source Example (similar)*: `pages/app/upload/personal-info.vue` uses `char-regex="a-z\sA-Z\-_.,"` for a name input.

5.  **Search Input (No label, specific styling)**:
    ```html
    <Input
      v-model="searchTerm"
      placeholder="Search items..."
      class="w-full"
    />
    ```
    *Source Example*: `pages/admin/email/index.vue` uses `<Input v-model="search" placeholder="Search" class="w-full" />`.

## Styling
- The component has base styling for a standard input field (border, padding, focus states).
- Conditional classes are applied for `error` and `darkMode` props.
- Use Tailwind utility classes directly on the `<Input>` tag for layout adjustments (e.g., `class="w-full mt-4"`).
- The `inputClass` prop can be used to target the internal `<input>` element specifically if needed.

## Best Practices
- Use `v-model` for simple two-way data binding.
- Always provide a `label` for accessibility, unless the context makes it redundant (e.g., a search bar with only a placeholder).
- Leverage the `error` prop to give visual feedback on validation errors.
- Use `type` appropriate for the data being collected (e.g., `email`, `number`, `password`).
- Utilize `numbersOnly` or `charRegex` for basic client-side input filtering where appropriate, but always ensure robust server-side validation.
