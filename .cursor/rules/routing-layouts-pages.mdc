---
description:
globs:
alwaysApply: true
---
# Routing, Layouts, and Pages Conventions

## Routing (Nuxt File-System Based)
- Routes are automatically generated by <PERSON>uxt based on the file structure in the `pages/` directory.
    - `pages/index.vue` -> `/`
    - `pages/about.vue` -> `/about`
    - `pages/user/profile.vue` -> `/user/profile`
    - `pages/app/index.vue` -> `/app`
- Dynamic routes use an underscore prefix for the file or directory name:
    - `pages/blog/_slug.vue` -> `/blog/:slug` (param accessible via `this.$route.params.slug`)
    - `pages/user/_id/settings.vue` -> `/user/:id/settings`
- Nested routes are created with nested directories.
- Programmatic navigation:
    - Use `<nuxt-link to="/path">Link</nuxt-link>` for declarative navigation.
    - Use `this.$router.push('/path')` or `this.$router.push({ name: 'routeName', params: { id: 123 }})` for imperative navigation.
    - Use `this.localePath('/path')` for i18n-aware navigation (from `@nuxtjs/i18n`).
- Access route parameters: `this.$route.params.paramName`.
- Access query parameters: `this.$route.query.queryName`.

## Layouts
- Layout components are in the `layouts/` directory.
- `layouts/default.vue` is the default layout for all pages unless specified otherwise.
    - It loads global fonts (`Plus Jakarta Sans`, `Architects Daughter`) via `@font-face`.
    - It includes `<portal-target>` for modals and tooltips.
    - It handles global Firebase auth state changes and PostHog scroll tracking.
- `layouts/error.vue` is used by Nuxt to display error pages.
- Custom layouts are defined by creating `.vue` files in `layouts/` (e.g., `protected.vue`, `admin.vue`, `empty.vue`, `tools.vue`).
- Assign a layout to a page or a group of pages by setting the `layout` property in the page component: `export default { layout: 'admin' }`.
- Key Layouts:
    - `protected.vue`: For authenticated user areas. Redirects to login if not authenticated. Includes robust Axios interceptors for auth token refresh and request retries.
    - `admin.vue`: For the super admin panel (`pages/admin/...`).
    - `empty.vue`: A minimal layout.
    - `tools.vue`: For pages in the `pages/tools/` directory.
- The `<Nuxt />` component within a layout is where the page content is rendered.

## Pages
- Page components are `.vue` files located in the `pages/` directory.
- They follow the same Vue component conventions (Options API, template/script/style).
- **`head()` method**: Use the `head()` method in page components to set page-specific `<title>`, `<meta>` tags (for SEO), `link` tags (canonical, alternate for i18n), and other head elements. This is crucial for SEO and social sharing.
    - Example: `head() { return { title: 'My Page Title', meta: [...] } }`.
- **Data Fetching**: `asyncData` or `fetch` can be used for server-side data fetching, but `async mounted()` is common for client-side fetching after initial render, especially with `target: 'static'`.
- Pages often interact with Vuex for state and Axios for data.
- Page-specific translations can be included in an `<i18n>` block.
    - Example: `<i18n>{"en": {"greeting": "Hello"}, "es": {"greeting": "Hola"}}</i18n>`
