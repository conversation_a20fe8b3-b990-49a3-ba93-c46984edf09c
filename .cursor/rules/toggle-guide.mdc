---
description: Guide for the <InputToggle> component (components/input/Toggle.vue), a switch-style boolean toggle. Covers its 'active' prop, 'change' event, and usage with a label in the slot.
globs:
alwaysApply: false
---
# Component Guide: InputToggle (Toggle)

**File Path**: `components/input/Toggle.vue`
(Typically used as `<InputToggle>` in templates).

## Overview
The `<InputToggle>` component provides a switch-style boolean toggle. It's visually represented as a small sliding button.

## Template Structure (Simplified Internal)
```html
<div class="flex items-center">
  <button
    type="button"
    :class="{ 'bg-brand-500': active, 'bg-black/10': !active }"
    class="relative inline-flex h-5 w-9 ..."
    role="switch"
    :aria-checked="active.toString()"
    @click="$emit('change')"
  >
    <span
      aria-hidden="true"
      :class="{ 'translate-x-4': active, 'translate-x-0': !active }"
      class="pointer-events-none inline-block h-4 w-4 ..."
    />
  </button>
  <div class="ml-2 flex items-center justify-start space-x-2" @click="$emit('change')">
    <slot /> <!-- Content next to the toggle, often a label -->
  </div>
</div>
```
- The toggle itself is a `<button>` element.
- The visual state (on/off color, knob position) is controlled by the `active` prop.
- Clicking either the toggle button or the content in the slot will emit the `change` event.

## Props

- **`active`**:
    - Type: `Boolean`
    - Default: `false`
    - Purpose: Determines the state of the toggle. `true` for on/active, `false` for off/inactive.

## Slots

- **Default Slot**:
    - Purpose: Used to place content, typically a label or description, to the right of the toggle switch. Clicking this content also triggers the `change` event.
    - Example:
      ```html
      <InputToggle :active="isEnabled" @change="isEnabled = !isEnabled">
        <span class="text-sm font-medium text-gray-700">Enable Notifications</span>
      </InputToggle>
      ```

## Events

- **`change`**:
    - Payload: None directly from the event. The parent component is expected to update the bound `active` prop.
    - Purpose: Emitted when the toggle button or the slotted content area is clicked, indicating an intention to change the toggle state.
    - Usage:
      ```html
      <InputToggle :active="settings.featureFlag" @change="toggleFeature('featureFlag')" />
      ```
      ```javascript
      // In methods
      toggleFeature(key) {
        this.settings[key] = !this.settings[key];
      }
      ```

## Usage Examples

1.  **Basic Toggle with a Label in the Slot**:
    ```html
    <InputToggle :active="isDarkMode" @change="isDarkMode = !isDarkMode">
      <label for="darkModeToggle" class="text-sm select-none cursor-pointer">Dark Mode</label>
    </InputToggle>
    <!-- Note: The component does not have an input element, so for/id on label is illustrative -->
    ```

2.  **Toggle for a Setting (from Grep result)**:
    ```html
    <!-- File: components/team/Settings/Notifications.vue -->
    <InputToggle :active="settings[notification.key]" @change="toggleSetting(notification.key)">
      <span class="text-sm text-gray-700">{{ notification.label }}</span>
      <BetaBadge v-if="notification.isBeta" />
    </InputToggle>
    ```
    ```javascript
    // In methods (conceptual)
    toggleSetting(key) {
      // This would typically update a Vuex store or a local data property
      // For example, if settings is a local data object:
      this.$set(this.settings, key, !this.settings[key]);
      // Or, if it's a Vuex mutation:
      // this.$store.commit('UPDATE_SETTING', { key, value: !this.settings[key] });
    }
    ```

## Styling
- Inactive state: `bg-black/10` (semi-transparent black) for the track.
- Active state: `bg-brand-500` (brand color) for the track.
- The knob is a white circle (`bg-white`).
- Transitions are applied for color changes and knob movement (`transition-colors duration-200 ease-in-out`).
- Focus state: `focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2` (uses indigo, might be a slight inconsistency if brand color is preferred for focus rings elsewhere).

## Best Practices
- Always pair `<InputToggle>` with descriptive text, usually placed in the default slot, to clarify what the toggle controls.
- The `change` event signals an *intent* to change. The parent component is responsible for actually updating the state that is bound to the `active` prop.
- Use for boolean settings or on/off states.
- Ensure the clickable area (toggle + slot content) provides a good user experience.
