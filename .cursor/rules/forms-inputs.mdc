---
description:
globs:
alwaysApply: true
---
# Forms and Inputs Conventions

## General Approach
- Use the custom `<Input />` component (`components/input/Input.vue`) for all standard text, email, number, password, etc. input fields instead of native `<input>` elements.
- For other form controls like select, checkbox, radio, prefer custom Vue components if available in `components/input/` or `components/common/` (e.g., `<InputSelect />`, `<InputCheckbox />`) over native elements.
- TailwindCSS is used for styling forms, primarily through the `@tailwindcss/forms` plugin (configured with `strategy: 'class'`). This means form elements are styled by applying utility classes.

## `<Input />` Component (`components/input/Input.vue`)
This is the standard component for most input fields. Key features and props:
- `v-model` or `:value` and `@input` for data binding.
- `label`: Text label displayed above the input.
- `description`: Smaller text displayed below the label.
- `placeholder`: Standard input placeholder.
- `type`: Input type (e.g., 'text', 'email', 'password', 'number').
- `disabled`: <PERSON><PERSON>an to disable the input.
- `error`: <PERSON><PERSON>an to indicate an error state (styles the border red).
- `darkMode`: <PERSON><PERSON>an for a dark mode variant.
- `numbersOnly`: Boolean to restrict input to numbers only.
- `charRegex`: String regex pattern to restrict allowed characters.
- `min` / `max`: For number inputs, to define min/max values. For text inputs, `max` can limit length.
- `inputClass`: Custom class to be applied to the input element itself.
- Slots:
    - `action`: For adding an element (e.g., a button) next to the label.
    - `affix`: For adding an element inside the input container (e.g., an icon).

## Form Submission
- Typically handle form submission via a method triggered by `@click` on a submit button or `@submit.prevent` on a `<form>` tag.
- Perform client-side validation before submitting if necessary.
- Use `this.$axios` to send form data to the API.
- Manage loading states during submission (e.g., using `this.$loading.show()` or a component-local `isLoading` flag).

## Validation
- Client-side validation can be done within component methods.
    - The `<Input />` component has built-in `numbersOnly` and `charRegex` props.
    - The global mixin has `isValidEmail(email)` and `isNumber(str)` helper methods.
- Server-side validation errors should be handled and displayed to the user.

## reCAPTCHA
- `@nuxtjs/recaptcha` (v3) is used.
- It's configured in `nuxt.config.js` with `hideBadge: true`.
- Usage typically involves calling `this.$recaptcha.execute('action_name')` to get a token before submitting a form, then sending this token to the backend for verification.

## Example Form Structure (Simplified)
```vue
<template>
  <form @submit.prevent="handleSubmit">
    <Input
      v-model="formData.email"
      label="Email Address"
      type="email"
      placeholder="<EMAIL>"
      :error="!!emailError"
    />
    <span v-if="emailError" class="text-red-500 text-sm">{{ emailError }}</span>

    <Input
      v-model="formData.password"
      label="Password"
      type="password"
      :error="!!passwordError"
    />
    <span v-if="passwordError" class="text-red-500 text-sm">{{ passwordError }}</span>

    <Button type="submit" :is-loading="isSubmitting">Submit</Button>
  </form>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        email: '',
        password: ''
      },
      emailError: null,
      passwordError: null,
      isSubmitting: false
    }
  },
  methods: {
    async handleSubmit() {
      // Basic client-side validation
      if (!this.formData.email) {
        this.emailError = 'Email is required.';
        return;
      }
      // ... more validation ...

      this.isSubmitting = true;
      // const token = await this.$recaptcha.execute('submit_form'); // If reCAPTCHA needed
      try {
        // await this.$axios.$post('/api/submit', { ...this.formData, token });
        // Handle success
      } catch (error) {
        // Handle error
      } finally {
        this.isSubmitting = false;
      }
    }
  }
}
</script>
```
