---
description:
globs:
alwaysApply: true
---
# Styling, TailwindCSS, and Icons Conventions

## TailwindCSS
- TailwindCSS v3.4.3 is the primary styling framework.
- Configuration is in `tailwind.config.js`.
    - **Content Sources**: Scans `components`, `layouts`, `pages`, `plugins`, `nuxt.config.js`.
    - **Custom Fonts**: "Plus Jakarta Sans" (sans-serif), "Georgia" (serif), "Architects Daughter" (cursive). These are loaded via `@font-face` in `layouts/default.vue` from `static/fonts/`.
    - **Custom Screens**: Includes `2xs`, `xs`, `3xl` for fine-grained responsive design.
    - **Extended Theme**: Defines many custom colors (`primary`, `brand`, `paragraph`, `teal`, `error`, etc.), spacing, shadows, animations (`scroll`), and other utilities.
    - **Plugins**: `@tailwindcss/typography`, `@tailwindcss/forms` (class strategy), `@tailwindcss/container-queries`.
- Apply Tailwind classes directly in the `<template>` of Vue components.
- Avoid custom CSS in `<style>` blocks unless absolutely necessary (e.g., for very specific one-off styles not achievable with Tailwind or for third-party library overrides).
- Global custom CSS is in `assets/css/main.css` and `assets/css/theme-sugar.css` (imported in `nuxt.config.js`).
- Follow a mobile-first approach for responsive design (apply base styles, then override for `sm:`, `md:`, `lg:`, etc. breakpoints).

## Icons
Two systems are in place for icons:

1.  **Heroicons via Tailwind CSS (Primary System)**:
    - Uses `@egoist/tailwindcss-icons` with the `heroicons` collection (solid and outline).
    - Icons are applied as Tailwind classes, e.g., `class="i-heroicons-arrow-left"` or `class="i-heroicons-user-solid"`.
    - Refer to Heroicons documentation for available icon names.
    - This is the preferred method for most UI icons due to ease of use and consistency with Tailwind.

2.  **Custom SVG Icon Components**:
    - Located in `components/icon/` as individual `.vue` files (e.g., `IconCamera.vue`, `IconCross.vue`).
    - Subdirectories like `solid/`, `mini/`, `outline/`, `custom/`, `logo/` organize these icons.
    - These are used for:
        - Icons not available in Heroicons.
        - Custom logos or illustrations.
        - When direct manipulation of SVG properties (via props or CSS) is needed.
    - Use them like any other Vue component: `<IconCustomCheckmark class="w-5 h-5 text-green-500" />`.

## Global Styles
- Global CSS files are located in `assets/css/` (e.g., `main.css`, `theme-sugar.css`) and imported in `nuxt.config.js`.
- Font definitions (`@font-face`) are in `layouts/default.vue`.

## Linting & Formatting
- ESLint and Prettier are configured to maintain code style, including for Tailwind class ordering if `prettier-plugin-tailwindcss` is effectively used (it's in `devDependencies`).
