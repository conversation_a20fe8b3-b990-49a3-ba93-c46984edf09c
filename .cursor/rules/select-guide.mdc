---
description: Guide for the <InputSelect> component (components/input/Select.vue), a styled dropdown select menu. Covers props (value, options, label), v-model usage, and styling.
globs:
alwaysApply: false
---
# Component Guide: InputSelect (Select)

**File Path**: `components/input/Select.vue`
(Often used as `<InputSelect>` due to <PERSON><PERSON><PERSON>'s component auto-import naming conventions based on directory structure, but can also be `<Select>` if no prefix is applied).

## Overview
The `<InputSelect>` component provides a styled dropdown select menu, replacing the native `<select>` element with a consistent look and feel that aligns with other custom input components like `<Input />`.

## Template Structure (Simplified Internal)
```html
<div :class="{ dark: darkMode, light: !darkMode }">
  <div class="flex content-center items-center justify-between">
    <label v-if="label" :for="id">{{ label }}</label>
    <slot /> <!-- Default slot next to label -->
  </div>
  <div class="select-wrapper relative">
    <select :id="id" :value="value" @input="$emit('input', $event.target.value)">
      <option value="" disabled selected hidden>{{ placeholder }}</option>
      <template v-for="(option, index) in options">
        <option :value="option.value || option" :selected="value === (option.title || option)">
          {{ option.title || slugToTitle(option) }}
        </option>
      </template>
    </select>
  </div>
  <span v-if="description">{{ description }}</span>
</div>
```
- It includes custom styling to hide the native select arrow and adds its own using an `::after` pseudo-element on `.select-wrapper`.
- Uses the global `slugToTitle` mixin method to format option text if `option.title` is not provided.

## Props

- **`value`**:
    - Type: `Number | String | Boolean`
    - Default: `''`
    - Purpose: The currently selected value of the select input. Use with `@input` event for two-way binding (e.g., `v-model`).
- **`options`**:
    - Type: `Array`
    - Default: `[]`
    - Purpose: An array of items to populate the select dropdown. Each item can be:
        - A simple string/number/boolean: In this case, the value itself is used as the option's value, and `slugToTitle(option)` is used as the display text.
        - An object: Must have a `value` property for the option's value and a `title` property for the display text (e.g., `{ title: 'Option 1', value: 'opt1' }`). If `title` is missing, `slugToTitle(option_object)` might be attempted on the object itself which is likely not intended.
- **`label`**:
    - Type: `String`
    - Default: `''`
    - Purpose: Text label displayed above the select input.
- **`placeholder`**:
    - Type: `String`
    - Default: `'Select your option'` (translatable via `<i18n>` block)
    - Purpose: Text for the initially visible, disabled, and hidden placeholder option.
- **`description`**:
    - Type: `String`
    - Default: `''`
    - Purpose: Smaller informational text displayed below the select input.
- **`id`**:
    - Type: `String`
    - Default: `''`
    - Purpose: Sets the `id` attribute for the `<select>` element.
- **`disabled`**:
    - Type: `Boolean`
    - Default: `false`
    - Purpose: If `true`, disables the select input (though styling for disabled state on the custom arrow might need checking).
- **`darkMode`**:
    - Type: `Boolean`
    - Default: `false`
    - Purpose: Applies a dark mode theme (primarily affecting label and description text color).

## Slots

- **Default Slot**:
    - Purpose: Allows injecting content (like a button or informational icon) to the right of the label.

## Events

- **`input`**:
    - Payload: The new selected value (`$event.target.value`).
    - Purpose: Emitted whenever the selected option changes. Use this for `v-model` or manual two-way binding.

## Internationalization (i18n)
- The default placeholder "Select your option" is translatable via the component's `<i18n>` block.

## Usage Examples

1.  **Basic Select with String Options**:
    ```html
    <InputSelect
      v-model="selectedGender"
      label="Gender"
      :options="['all', 'male', 'female']"
      placeholder="Select Gender"
    />
    ```
    *Source Example*: `pages/admin/clothing.vue` uses a similar pattern.
    *Behavior*: Option text will be "All", "Male", "Female" (due to `slugToTitle`). Option values will be "all", "male", "female".

2.  **Select with Object Options (Title and Value)**:
    ```html
    <InputSelect
      v-model="selectedStatus"
      label="Status"
      :options="[
        { title: 'All Statuses', value: 'all' },
        { title: 'Active Users', value: 'active' },
        { title: 'Pending Approval', value: 'pending' }
      ]"
    />
    ```
    *Source Example*: `pages/admin/invoices/index.vue` uses `[{title: 'All', value: 'all'}, ...]`.

3.  **Select with Dark Mode and Description**:
    ```html
    <InputSelect
      v-model="searchBy"
      :options="['title', 'slug', 'alias']"
      class="w-1/4"
      darkMode
      label="Search By"
      description="Select the field to search on."
    />
    ```
    *Source Example*: `pages/admin/comfy-workflows/index.vue` uses `<InputSelect v-model="searchBy" :options="['title', 'slug', 'alias']" class="w-1/4" />` (darkMode not shown but applicable).

4.  **Using `v-model` and dynamic options from computed property**:
    ```html
    <InputSelect
      v-model="filters.category"
      label="Category"
      :options="categoryOptions"
    />
    ```
    ```javascript
    // In script
    computed: {
      categoryOptions() {
        // Assuming this.$store.state.categories is an array of { name: 'Category Name', id: 'cat_id' }
        return this.$store.state.categories.map(cat => ({ title: cat.name, value: cat.id }));
      }
    }
    ```
    *Source Example*: `pages/admin/api-clients/index.vue` maps organizations to title/value pairs: `:options="organizations.map(o => ({ title: o.name, value: o._id }))"`.

## Styling
- The component has base styling for the select element and a custom dropdown arrow.
- It applies Tailwind classes for focus states, borders, etc.
- `darkMode` prop adjusts text colors for label and description.
- Use Tailwind utility classes directly on the `<InputSelect>` tag for layout adjustments (e.g., `class="w-full mt-4"`).
- The custom arrow is styled using `content: "▼"` on the `.select-wrapper::after` pseudo-element. Its color is hardcoded to `rgba(0, 0, 0, 0.5)` and might not adapt to `darkMode` automatically for the arrow itself.

## Best Practices
- Use `v-model` for straightforward two-way data binding.
- Provide the `options` array with clear `title` and `value` properties for each option when dealing with objects. If providing simple strings, ensure `slugToTitle` produces the desired display text.
- Always include a `label` for accessibility.
- Use the `placeholder` prop for a user-friendly initial state.
