---
description: Guide for the <ButtonWhite> component (components/button/White.vue), a standard secondary button with various sizes and an optional loading state.
globs:
alwaysApply: false
---
# Component Guide: ButtonWhite (White)

**File Path**: `components/button/White.vue`
(Typically used as `<ButtonWhite>` in templates).

## Overview
The `<ButtonWhite>` component is a standard secondary button. It features a white background, a light gray border, and dark text. It's often used for less prominent actions, alternative choices, or cancellations.

## Template Structure (Simplified Internal)
```html
<!-- Example for size = 'base' -->
<button
  aria-label="White button"
  type="button"
  class="flex content-center items-center justify-center rounded-md border border-[#e4e4e7] bg-white ... text-slate-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2"
  @click="$emit('click')"
>
  <LoadingSpinner v-if="loading" class="w-4 h-4" />
  <slot v-else />
</button>
```
- Similar to `<ButtonPrimary>`, it has a series of `<button>` elements, one for each `size` (`xs`, `sm`, `base`, `lg`, `xl`), controlled by `v-if`/`v-else-if`.
- Each button has specific padding and text size classes based on the `size` prop.
- Common classes include `bg-white`, `border border-[#e4e4e7]` (light gray), `text-slate-700`.
- Hover state changes background to `hover:bg-gray-200`.
- Focus state applies `focus:ring-brand-500`.
- Includes an optional loading state.

## Props

- **`size`**:
    - Type: `String`
    - Default: `'base'`
    - Options: `'xs'`, `'sm'`, `'base'`, `'lg'`, `'xl'`.
    - Purpose: Determines the padding and font size of the button.
- **`loading`**:
    - Type: `Boolean`
    - Default: `false`
    - Purpose: If `true`, the button's default slot content is replaced by a `<LoadingSpinner class="w-4 h-4" />`. The button remains clickable.

## Slots

- **Default Slot**:
    - Purpose: The content of the button, typically text. Replaced by a spinner if `loading` is true.
    - Example:
      ```html
      <ButtonWhite>
        Cancel
      </ButtonWhite>

      <ButtonWhite size="sm">
        <IconChevronLeft class="w-4 h-4 mr-1" /> Previous
      </ButtonWhite>
      ```

## Events

- **`click`**:
    - Payload: The native click event object.
    - Purpose: Emitted when the button is clicked.
    - Usage:
      ```html
      <ButtonWhite @click="handleCancel">
        Go Back
      </ButtonWhite>
      ```

## Usage Examples

1.  **Basic White Button (Default Size)**:
    ```html
    <ButtonWhite @click="closeModal">
      Close
    </ButtonWhite>
    ```

2.  **Small White Button with Loading State**:
    ```html
    <ButtonWhite size="sm" :loading="isDataFetching" @click="refreshData">
      Refresh Data
    </ButtonWhite>
    ```
    *Observation*: When `loading` is true, the slot content (e.g., "Refresh Data") is hidden.

3.  **Used as part of another component (e.g., ButtonDropdown)**:
    The `<ButtonDropdown>` component uses `<ButtonWhite>` internally for its `theme='v1'` trigger button.

4.  **Navigation or Secondary Actions**:
    ```html
    <div class="flex justify-end space-x-3">
      <ButtonWhite @click="goBack">
        Back
      </ButtonWhite>
      <ButtonPrimary @click="submitForm">
        Save and Continue
      </ButtonPrimary>
    </div>
    ```
    *Source Example*: `pages/profile/index.vue` uses `<ButtonWhite size="sm" @click="showDeleteAccountModal = false">Cancel</ButtonWhite>` in a modal.

## Styling
- Base style is `bg-white` with a `border border-[#e4e4e7]` and `text-slate-700`.
- Hover state: `hover:bg-gray-200`.
- Focus state: `focus:ring-2 focus:ring-brand-500 focus:ring-offset-2`.
- Padding and font size are determined by the `size` prop.
- The loading spinner is fixed at `w-4 h-4` regardless of button size.
- Additional layout or spacing classes can be added via the `class` attribute.

## Best Practices
- Use `<ButtonWhite>` for secondary actions, cancellations, or alternatives to a primary action.
- Good for actions that don't need the strong visual emphasis of `<ButtonPrimary>`.
- If using the `loading` prop, be aware that the button text/content will be replaced. If you need text alongside the spinner, you'd have to implement that manually in the slot and not use the `loading` prop.
- Ensure there's a clear visual distinction when used alongside `<ButtonPrimary>` to guide the user.
