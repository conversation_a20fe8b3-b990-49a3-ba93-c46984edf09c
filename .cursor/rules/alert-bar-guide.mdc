---
description: Guide for the <AlertBar> component (components/alert/Bar.vue), a simple, full-width notification bar for site-wide announcements.
globs:
alwaysApply: false
---
# Component Guide: AlertBar

**File Path**: `components/alert/Bar.vue`
(Likely used as `<AlertBar>` in templates, though no current usage examples were found via grep search).

## Overview
The `<AlertBar>` component appears to be a simple, full-width notification bar. It has a fixed yellow background and white text, typically used for site-wide announcements or important, non-dismissible messages at the top or bottom of the viewport (due to `z-50`).

## Template Structure (Simplified)
```html
<div class="w-full text-sm z-50 flex p-2 items-center justify-center bg-yellow-600 text-white relative overflow-hidden">
  <slot />
</div>
```

## Props
- None defined in the component script.

## Slots

- **Default Slot**:
    - Purpose: This is where the content of the alert bar is placed (e.g., text, links).
    - Example:
      ```html
      <AlertBar>
        Site maintenance scheduled for Sunday at 2 AM UTC.
        <a href="/status" class="underline font-semibold">More info</a>
      </AlertBar>
      ```

## Events
- None.

## Usage Examples

*No direct usage examples were found in the codebase via grep search. The following is a hypothetical example based on its structure.*

1.  **Displaying a Site-Wide Announcement**:
    ```html
    <AlertBar>
      <p>
        Our annual user conference is now open for registration!
        <nuxt-link to="/conference" class="font-bold hover:underline">Register Today!</nuxt-link>
      </p>
    </AlertBar>
    ```

## Styling
- Full width (`w-full`).
- Yellow background (`bg-yellow-600`).
- White text (`text-white`).
- Small text size (`text-sm`).
- Padding (`p-2`).
- Flexbox for centering content (`flex items-center justify-center`).
- High z-index (`z-50`), suggesting it's meant to overlay other content.
- `relative overflow-hidden`.

## Best Practices
- Use for important, concise announcements that need to be visible across the site or a major section.
- Since it has a fixed style (yellow background, white text), ensure the slotted content is readable against it.
- As it appears to be non-dismissible by default, use it for information that remains relevant for the user's session or a period.
- If dismissibility or different styles (e.g., error, success) are needed, other alert components might be more appropriate.
- Given no current usage found, verify if this component is actively used or if there are preferred alternatives for site-wide bars/notifications.
