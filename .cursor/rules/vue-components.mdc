---
description:
globs:
alwaysApply: true
---
# Vue Component Conventions

## Structure
- Components are located in `components/` and organized into subdirectories by feature or type (e.g., `components/card/`, `components/input/`).
- Use the Vue Options API: `export default { ... }`.
- Template (`<template>`), script (`<script>`), and style (`<style>`) blocks.
    - Style blocks are often empty as TailwindCSS is preferred for styling.
- Component file names are PascalCase (e.g., `UserProfile.vue`).

## Props
- Define props clearly with `type` and `default` values.
- Use camelCase for prop names.

## Slots
- Utilize `<slot />` (default slot) for content injection from parent components.
- Use named slots (`<slot name="header" />`) for more complex component structures.

## Events
- Emit events using `this.$emit('eventName', payload)`.
- Event names should be camelCase or kebab-case.

## Auto-Import
- Nuxt.js auto-imports components from the `components/` directory. You generally DO NOT need to explicitly import them in the `<script>` section of other components or pages.
- Example: To use `components/common/Button.vue`, you can directly use `<CommonButton />` in your templates.

## Common Reusable Components
- **`<Input />`**: (from `components/input/Input.vue`) Use this instead of raw `<input>` elements. It supports labels, descriptions, error states, validation, dark mode, etc.
- **`<Card />`**: (from `components/card/Card.vue`) A general-purpose card container.
- **`<Modal />`**: (from `components/modal/Modal.vue`) For modal dialogs. Content is passed via slots. Emits `close` event.
- **`<Popup />`**: (from `components/popup/Popup.vue`) Similar to Modal, often used for bottom-screen popups or more complex dialogs. Emits `close` / `closeModal`.
- **Icon Components**: Custom SVG icons are available as individual Vue components in `components/icon/` (e.g., `<IconCross />`, `<IconCamera />`). Use these for specific icons not covered by Tailwind Heroicons.
- Check `components/` subdirectories for other reusable UI elements like buttons, alerts, loading indicators specific to a component, etc.

## Accessibility (A11y)
- Ensure interactive components are keyboard accessible.
- Use ARIA attributes where appropriate (e.g., `role="dialog"`, `aria-modal="true"` for modals).

## Logic
- Keep component logic focused on presentation and UI interaction.
- Complex business logic or state management should reside in Vuex stores or mixins/utility functions.
- Use computed properties for derived data to keep templates clean.
- Use `async mounted()` for initial data fetching if component-specific data is needed.
