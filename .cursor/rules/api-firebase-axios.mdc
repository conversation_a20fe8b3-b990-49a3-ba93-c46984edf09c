---
description:
globs:
alwaysApply: true
---
# API, Firebase, and Axios Conventions

## Axios for HTTP Requests
- `@nuxtjs/axios` is used for making HTTP requests.
- Base URL for API calls is configured in `nuxt.config.js` (e.g., `axios: { baseURL: process.env.SERVER_URL }`).
- Access in components/pages:
    - `this.$axios.$get('/endpoint', config)`
    - `this.$axios.$post('/endpoint', data, config)`
    - `this.$axios.$put('/endpoint', data, config)`
    - `this.$axios.$delete('/endpoint', config)`
    - **Note**: These `$method` helpers (e.g., `$get`, `$post`) are a convenience provided by `@nuxtjs/axios`. They automatically return the `data` property of the Axios response object. So, you get the response body directly, without needing to do `response.data`.
    - For more control (e.g., to access headers or status code from the response object itself), you can use the standard Axios methods:
        - `this.$axios.get('/endpoint', config).then(response => { /* use response.data, response.status */ })`
        - `this.$axios.post('/endpoint', data, config).then(response => { /* ... */ })`
- Access in Vuex actions: `this.$axios.$get(...)` (available on `this` within actions).
- A custom Axios instance might also be used from `utils/axios.js` in some parts of the application.

### Axios Interceptors
- **`plugins/axios.js`**: Registers a request interceptor that adds `uid` (from Vuex) and `idToken` (Firebase auth token) to the default headers of *every* outgoing request for both `$axios` and the custom `utils/axios` instance.
- **`layouts/protected.vue`**: This layout (used for authenticated areas) sets up more sophisticated Axios interceptors for `$axios` instances within its child components:
    - **Request Interceptor**: Refreshes the Firebase `idToken` if it's stale (older than 5 minutes) before the request is sent. Updates `idToken` in `$axios.defaults.headers.common` and Vuex store (`user/SET_FIREBASE_AUTH_TOKEN_REFRESHED_AT`).
    - **Response Interceptor**: If an API request fails with a 401 status and an "Incorrect token" message, it attempts to refresh the `idToken` and retries the original request automatically (up to 2 retries). If retries fail, it logs the user out.
- This layered approach ensures API requests are authenticated, with robust token handling in protected areas.

## Firebase Integration
- `@nuxtjs/firebase` module handles Firebase integration.
- Configuration (API keys, project ID, etc.) is in `nuxt.config.js` under the `firebase` key, sourced from environment variables.
- **Authentication**: Firebase Auth is the primary authentication mechanism.
    - `this.$fire.auth` provides access to Firebase Auth methods (e.g., `signInWithEmailAndPassword`, `createUserWithEmailAndPassword`, `signOut`).
    - `onAuthStateChanged` listeners (in `layouts/default.vue`, `layouts/protected.vue`, and Vuex `user` module action `onAuthStateChangedAction`) react to user login/logout, update Vuex store, and handle redirection.
- **Other Firebase Services**: The project might use other Firebase services (e.g., Firestore, Storage). Check `nuxt.config.js` under `firebase.services` and look for usage of `this.$fire.firestore`, `this.$fire.storage`, etc.

## API Endpoints
- API endpoints are relative to the `baseURL` configured for Axios.
- Examples seen:
    - `/model/all` (fetch user shoots)
    - `/checkout/stripe/create-customer-portal-session`
    - `/styles/`, `/clothing/`, `/hairstyles/`, `/emotions/` (fetch global data for store)
    - `/checkout/package/package`
    - `/user/location` (get user country code)
    - `/model/download/track/:id`

## Error Handling
- Use `try...catch` blocks for API calls.
- Set error flags in component `data()` (e.g., `isError: true`) to conditionally render error messages.
- The global mixin provides a `handleError(err)` method which can be used for centralized error logging/reporting, although its specific implementation details weren't fully visible.
- The Axios response interceptor in `protected.vue` handles 401 token errors specifically.
