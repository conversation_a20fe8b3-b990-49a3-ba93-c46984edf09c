---
description:
globs:
alwaysApply: true
---
# Performance and Build Conventions

## Build Configuration (`nuxt.config.js`)
- **Target**: `target: 'static'` for statically generated site. Deployed on Vercel.
- **Chunking Strategy**: Nuxt/Webpack handles code splitting. Specific optimizations are in `nuxt.config.js` under `build.optimization.splitChunks`:
    - **Vendor Chunks**: Separate chunks are created for large vendor libraries like `firebase`, `vue`, `stripe`, `axios`, `tailwind`, `nuxt`, `content`, `i18n`, `chart`, `flicking`, `posthog`.
    - **Layout Chunks**: Chunks are created for different layouts (e.g., `admin`, `default`, `protected`, `empty`).
    - **Common Chunks**: `commons` chunk for shared modules.
    - **Cache Groups**: Detailed configuration for how these chunks are formed based on module paths and usage.
    - `minChunks`, `maxAsyncRequests`, `maxInitialRequests` are configured to optimize splitting.
- **Filenames**: `build.filenames` customizes chunk filenames.
- **Public Path**: `build.publicPath` is set from an environment variable (likely for CDN asset hosting).
- **Transpilation**: `build.transpile` specifies packages to be transpiled by Babel (e.g., `vue-image-compare`, `dom-to-image`).
- **PostCSS**: Configured with `tailwindcss` and `autoprefixer`.

## Dynamic Imports for Components
- Use dynamic imports for non-critical components or components that are heavy and not needed on initial load.
  ```javascript
  // Example of dynamic import for a component
  const HeavyComponent = () => import('@/components/heavy/HeavyComponent.vue');

  export default {
    components: {
      HeavyComponent // Register for use in template
    }
    // ...
  }
  ```
- Nuxt also supports auto-detecting dynamic imports if you use the `() => import(...)` syntax directly in `components` option or when defining async components.

## Image Optimization
- **`<ImageDns>` / `dnsImage` function**: The global mixin (`plugins/mixins.js`) provides a `dnsImage(url)` method. This is likely used to serve images from a CDN or apply other transformations/optimizations. When dealing with images, prefer using this helper if applicable, or look for a component like `<ImageDns>` if it exists (the method suggests its presence or intent).
- **`v-lazy-image`**: This library is a dependency, suggesting lazy loading for images is implemented or can be used.
- **`browser-image-compression`**: Dependency for client-side image compression before uploads.
- **Other image tools**: `vue-image-compare`, `dom-to-image`, `heic2any` are available for specific image manipulation tasks.
- Serve appropriately sized images for different devices/resolutions (responsive images).
- Compress images (e.g., using TinyPNG/ImageOptim) before adding to the project.

## Lazy Loading Plugins
- In `nuxt.config.js`, many plugins are registered with `{ mode: 'client', defer: true }`. This defers their loading and execution, improving initial page load performance.

## Web Vitals (LCP, CLS, FID)
- Strive to optimize for good Web Vitals scores.
- Use browser developer tools (Lighthouse, Performance tab) and online tools (WebPageTest, PageSpeed Insights) to measure and identify bottlenecks.
- Key areas impacting Web Vitals:
    - **LCP (Largest Contentful Paint)**: Optimize image loading (see above), defer non-critical CSS/JS, ensure server responds quickly.
    - **FID (First Input Delay)**: Minimize main thread work, break up long tasks, reduce JS execution time (code splitting, lazy loading helps).
    - **CLS (Cumulative Layout Shift)**: Specify image dimensions, reserve space for ads/embeds, avoid dynamically injecting content above existing content.

## Other Performance Considerations
- **Minimize bundle size**: Code splitting is key. Regularly review bundle analysis (e.g., `nuxt build --analyze`).
- **Tree shaking**: Ensure it's effective by using ES6 modules and avoiding side-effects in imports where possible.
- **Caching**: Leverage browser caching and service workers (if Nuxt PWA module is used, though not explicitly seen).
- **Reduce Re-renders**: Optimize Vue components to avoid unnecessary re-renders (e.g., use `v-once`, proper `key`s in `v-for`, computed properties).
