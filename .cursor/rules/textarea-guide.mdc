---
description: Guide for the <InputTextArea> component (components/input/TextArea.vue), a multi-line text input with labels, character counter, and error states. Emphasizes preferring it over native <textarea>.
globs:
alwaysApply: false
---
# Component Guide: InputTextArea (TextArea)

**File Path**: `components/input/TextArea.vue`
(Typically used as `<InputTextArea>` in templates).

## Overview
The `<InputTextArea>` component provides a multi-line text input field, styled consistently with other input components like `<Input />`. It includes features like labels, descriptions, character counters, and error states.

**Always prefer `<InputTextArea />` over the native `<textarea>` element.**

## Template Structure (Simplified Internal)
```html
<div>
  <!-- Label, Description, and Character Counter -->
  <div class="flex items-center content-center justify-between">
    <label v-if="label" :for="id">{{ label }}</label>
    <span v-if="maxLength && maxLength < 1000">{{ currentLength }} of {{ maxLength }}</span>
  </div>
  <p v-if="description">{{ description }}</p>

  <!-- Textarea field -->
  <div class="relative mt-1">
    <textarea
      :id="id"
      :value="value"
      :rows="rows"
      :placeholder="placeholder"
      :disabled="disabled"
      :maxlength="maxLength"
      :autocomplete="autocomplete"
      :class="{ ... conditional classes for error, darkMode ... }"
      @input="$emit('input', $event.target.value)"
    />
  </div>
</div>
```
- The character counter (`{{ currentLength }} of {{ maxLength }}`) is only shown if `maxLength` is provided and less than 1000.

## Props

- **`value`**:
    - Type: `String`
    - Default: `''`
    - Purpose: The current content of the textarea. Use with `@input` event for two-way binding (e.g., `v-model`).
- **`label`**:
    - Type: `String`
    - Default: `''`
    - Purpose: Text label displayed above the textarea.
- **`description`**:
    - Type: `String`
    - Default: `''`
    - Purpose: Smaller informational text displayed below the label.
- **`placeholder`**:
    - Type: `String`
    - Default: `''`
    - Purpose: Placeholder text for the textarea.
- **`id`**:
    - Type: `String`
    - Default: `''`
    - Purpose: Sets the `id` attribute for the textarea element.
- **`rows`**:
    - Type: `Number`
    - Default: `5`
    - Purpose: The number of visible text lines for the textarea.
- **`maxLength`**:
    - Type: `Number`
    - Default: `9999999` (a large number, effectively no limit by default for the counter)
    - Purpose: The maximum number of characters allowed. Also used by the character counter.
- **`error`**:
    - Type: `Boolean`
    - Default: `false`
    - Purpose: If `true`, applies error styling (e.g., red border) to the textarea.
- **`disabled`**:
    - Type: `Boolean`
    - Default: `false`
    - Purpose: If `true`, disables the textarea and styles it accordingly.
- **`darkMode`**:
    - Type: `Boolean`
    - Default: `false`
    - Purpose: Applies a dark mode theme to the textarea.
- **`autocomplete`**:
    - Type: `String`
    - Default: `'off'`
    - Purpose: Controls the browser's autocomplete behavior for the textarea.
- **`textareaClass`**:
    - Type: `String`
    - Default: `''`
    - Purpose: Allows passing additional custom CSS classes directly to the `<textarea>` element itself.

## Slots
- None.

## Events

- **`input`**:
    - Payload: The new content of the textarea (`$event.target.value`).
    - Purpose: Emitted whenever the textarea content changes. Use this for `v-model` or manual two-way binding.

## Computed Properties

- **`currentLength`**:
    - Calculates the length of the `value` prop. Used for the character counter.

## Usage Examples

1.  **Basic TextArea with Label and Rows**:
    ```html
    <InputTextArea
      v-model="form.message"
      label="Your Message"
      :rows="3"
      placeholder="Enter your feedback here..."
    />
    ```

2.  **TextArea with Character Limit and Description**:
    ```html
    <InputTextArea
      v-model="form.bio"
      label="Biography"
      description="Share a short bio (max 250 characters)."
      :rows="4"
      :maxLength="250"
    />
    ```
    *Observation*: The character counter will display "currentLength of 250".

3.  **TextArea with Error State and Dark Mode**:
    ```html
    <InputTextArea
      v-model="form.comment"
      label="Comment"
      :rows="2"
      :error="hasCommentError"
      darkMode
      textareaClass="custom-scrollbar-class"
    />
    ```
    *Source Example (similar)*: `pages/admin/email/create.vue` uses `textarea-class` for custom height: `<InputTextArea label="Email body" :value="email.text" textarea-class="w-full h-[calc(100vh-400px-100px)]" ... />`

4.  **Required TextArea for a Support Form**:
    ```html
    <InputTextArea
      v-model="supportRequest.details"
      label="How can we help you?*"
      placeholder="Please describe your issue in detail..."
      :rows="6"
      :maxLength="2000"
    />
    ```
    *Source Example*: `components/support/SupportForm.vue` uses a similar setup.

## Styling
- The component has base styling for a standard textarea (border, padding, focus states).
- Tailwind utility classes are used, including from `@tailwindcss/forms` (`form-input`).
- Conditional classes are applied for `error` and `darkMode` props.
- Use Tailwind utility classes directly on the `<InputTextArea>` tag for layout adjustments (e.g., `class="w-full mt-4"`).
- The `textareaClass` prop can be used to target the internal `<textarea>` element specifically, for instance, to apply custom height or scrolling styles.

## Best Practices
- Use `v-model` for simple two-way data binding.
- Always provide a `label` for accessibility.
- Use the `rows` prop to set an appropriate initial height for the expected content.
- Use `maxLength` when there's a practical limit to the input length; the counter provides good UX.
- Leverage the `error` prop for visual feedback on validation errors.
