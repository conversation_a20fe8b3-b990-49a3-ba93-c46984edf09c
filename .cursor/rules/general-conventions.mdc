---
description: 
globs: 
alwaysApply: true
---
# General Project Conventions

## Overview
This project is a Nuxt.js 2 (v2.15.8) and Vue.js 2 (v2.6.14) application. It's a statically generated site (`target: 'static'`) with a strong focus on professional headshot generation using AI.

## Code Style & Structure
- Write concise, maintainable, and technically accurate JavaScript (ES6+).
- **No TypeScript**: Use vanilla JavaScript.
- Use functional and declarative programming patterns; avoid classes unless part of a required library.
- Favor iteration and modularization for DRY principles.
- Use descriptive variable names with auxiliary verbs (e.g., `isLoading`, `hasError`, `isModalVisible`).
- Organize files systematically:
    - Each `.vue` file should contain only its component logic, template, and styles.
    - Helper functions should be in `utils/` or relevant mixins.
    - Static content (like JSON data) is often in `assets/`.
- Pure functions should use the `function` keyword for clarity and hoisting.
- Adhere to ESLint rules defined in `.eslintrc.js` (based on `@nuxtjs` and `plugin:nuxt/recommended`).
    - **Exception**: `camelcase` rule allows `aa_bb` style: `["error", {"allow": ["aa_bb"]}]`.
- Formatting follows `.prettierrc` settings:
    - `printWidth: 300`
    - `singleQuote: true`
    - `semi: false`
    - `trailingComma: "none"`
    - `bracketSpacing: true`
    - `bracketSameLine: true`
    - `htmlWhitespaceSensitivity: "ignore"`

## Naming Conventions
- Directories: lowercase with dashes (kebab-case), e.g., `components/auth-wizard`, `pages/photo-upload`.
- Vue component files: PascalCase (e.g., `UserProfile.vue`, `LoadingSpinner.vue`).
- JavaScript helper/utility files: camelCase (e.g., `imageUtils.js`) or PascalCase if they export a primary class/object.
- Functions and variables: camelCase (e.g., `fetchUserData`, `isLoading`).
- Vuex actions/mutations: UPPER_SNAKE_CASE (e.g., `SET_USER_DATA`, `FETCH_PROFILE`).

## Key Dependencies & Technologies
- Nuxt.js v2.15.8
- Vue.js v2.6.14
- Vue Router (managed by Nuxt)
- Vuex (modular, in `store/`)
- TailwindCSS v3.4.3 (extensive customization in `tailwind.config.js`)
- Firebase (`@nuxtjs/firebase` for auth and other services)
- Axios (`@nuxtjs/axios` for HTTP requests)
- i18n (`@nuxtjs/i18n` for English and Spanish)
- PostHog for analytics and feature flags.
- Stripe for payments.
- Content is managed via `@nuxt/content` (Markdown files).

## Environment Variables
- Extensive use of `process.env` for configuration (API keys, URLs, feature flags like `HOLIDAY_MODE`, `MAINTENANCE_MODE`). Check `nuxt.config.js` for `env` and `publicRuntimeConfig`.
- Sensitive keys are in `.env` files (not committed). See `.env_example`.

## General Workflow
- Mobile-first responsive design.
- Nuxt's auto-import for components is enabled (`components: true` in `nuxt.config.js`). Do not manually import components in `<script>` tags unless necessary (e.g., for dynamic components or third-party libraries not auto-imported).
- Reuse components whenever possible (e.g., use `<Input />` instead of `<input />`).
- Don't ever put :key on a <template> this does not work in Vue2/Nuxt2.
- Don't add async to functions without await in it.
- DO NOT AT ANY MOMENT DELETE CODE. IF NEEDING TO DELETE CODE, ASK FIRST
- AGAIN DO NOT PUT :KEY ON TEMPLATE TAGS IT DOESNT WORK BRO
