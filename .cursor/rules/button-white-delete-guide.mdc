---
description: Guide for the <ButtonWhiteDelete> component (components/button/WhiteDelete.vue), a specialized button for destructive actions with a built-in two-step confirmation.
globs:
alwaysApply: false
---
# Component Guide: ButtonWhiteDelete (WhiteDelete)

**File Path**: `components/button/WhiteDelete.vue`
(Typically used as `<ButtonWhiteDelete>` in templates).

## Overview
The `<ButtonWhiteDelete>` component is a specialized version of a white button, intended for destructive actions like deletion. It features a two-step confirmation process: the first click changes the button text to "Are you sure?", and a second click within a short timeframe (2 seconds) emits the `click` event. If not clicked again, it reverts to its original state.

## Template Structure (Simplified Internal)
```html
<!-- Example for size = 'base' -->
<button
  type="button"
  class="flex content-center items-center justify-center rounded-md border border-[#e4e4e7] bg-white ... text-[#EA4335] hover:bg-[#EA4335] hover:text-white"
  @click="showConfirm ? proceed() : toggleConfirmation()"
>
  <span v-if="!showConfirm">
    <slot /> <!-- Original button content -->
  </span>
  <span v-else>
    Are you sure? <!-- Confirmation text -->
  </span>
</button>
```
- Like other button components, it has a series of `<button>` elements for different `size` props.
- Base style is white background, light gray border, but with red text (`text-[#EA4335]`).
- Hover state changes background to red (`hover:bg-[#EA4335]`) and text to white (`hover:text-white`).
- Manages confirmation state with `showConfirm` data property.

## Props

- **`size`**:
    - Type: `String`
    - Default: `'base'`
    - Options: `'xs'`, `'sm'`, `'base'`, `'lg'`, `'xl'`.
    - Purpose: Determines the padding and font size of the button.

## Slots

- **Default Slot**:
    - Purpose: The initial content/text of the button (e.g., "Delete", "Remove"). This is shown when `showConfirm` is false.
    - Example:
      ```html
      <ButtonWhiteDelete>
        Delete Item
      </ButtonWhiteDelete>
      ```

## Events

- **`click`**:
    - Payload: The native click event object.
    - Purpose: Emitted ONLY after the user has clicked the button twice (confirming the action).
    - Usage:
      ```html
      <ButtonWhiteDelete @click="handleDeleteItem(item.id)">
        Remove
      </ButtonWhiteDelete>
      ```

## Internal Logic

- `showConfirm` (data property, default: `false`): Controls whether the button is in its initial state or confirmation state.
- `toggleConfirmation()`:
    - Flips `showConfirm`.
    - If `showConfirm` becomes true, it starts a 2-second timer (`setTimeout`). If the timer expires before `proceed()` is called, it calls `cancel()` to revert `showConfirm` to false.
- `proceed()`:
    - Emits the `click` event.
    - Calls `toggleConfirmation()` (which will set `showConfirm` to false, effectively resetting the button).
- `cancel()`:
    - Calls `toggleConfirmation()` (which will set `showConfirm` to false if it was true).

## Usage Examples

1.  **Basic Delete Button**:
    ```html
    <ButtonWhiteDelete size="sm" @click="deleteApiKey">
      Delete API Key
    </ButtonWhiteDelete>
    ```
    *Source Example*: `pages/app/admin/api.vue` uses `<ButtonWhiteDelete size="sm" @click="deleteApiKey()">Delete API Key</ButtonWhiteDelete>`.

2.  **Revoke Invite (Extra Small Button)**:
    ```html
    <ButtonWhiteDelete size="xs" @click="revokeUserInvite(invite.id)">
      <IconTrash class="w-4 h-4 mr-1" /> Revoke
    </ButtonWhiteDelete>
    ```
    *Source Example (similar)*: `components/app/Invites/Actions.vue` uses `<ButtonWhiteDelete size="xs" @click="revokeInvite()">Revoke</ButtonWhiteDelete>`.

## Styling
- Initial state: White background (`bg-white`), light gray border (`border-[#e4e4e7]`), red text (`text-[#EA4335]`).
- Hover state (initial): Red background (`hover:bg-[#EA4335]`), white text (`hover:text-white`).
- Confirmation state ("Are you sure?"): Same styling applies.
- Padding and font size are determined by the `size` prop.

## Best Practices
- Use `<ButtonWhiteDelete>` for actions that are destructive and should have a confirmation step to prevent accidental clicks.
- Ensure the slot content clearly indicates the action (e.g., "Delete", "Remove", "Revoke").
- The 2-second timeout for confirmation is fixed. Be mindful of this short window for users to confirm.
- Since the button text changes, ensure the width of the button doesn't drastically change if the original text and "Are you sure?" have very different lengths, which could cause layout shifts. Applying a fixed width or min-width via a class might be necessary in some cases.
