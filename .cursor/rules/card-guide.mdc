---
description: Guide for the <Card> component (components/card/Card.vue), a general-purpose container for content, covering its props, slots, and common usage patterns.
globs:
alwaysApply: false
---
# Component Guide: Card

**File Path**: `components/card/Card.vue`

## Overview
The `<Card>` component is a general-purpose container used throughout the application to group and visually structure content. It typically renders as a white, rounded-lg block that can accept content via its default slot.

## Template Structure (Simplified)
```html
<div class="flex items-start content-start justify-start h-full bg-white rounded-lg card" @click="$emit('click')">
  <div class="w-full h-full" :class="innerClass">
    <slot />
  </div>
</div>
```
- The base styles include `bg-white` and `rounded-lg`.
- It has a root `div` with an outer class `card` (its specific styling might be in global CSS or it might be a marker class).
- It handles a click event on the root element.

## Props

- **`innerClass`**:
    - Type: `String`
    - Default: `''` (empty string)
    - Purpose: Allows you to pass additional Tailwind CSS classes directly to the inner `div` that wraps the `<slot />`. This is useful for controlling padding, overflow, or other internal layout aspects of the card's content area without affecting the card's outer structure or base styling.

## Slots

- **Default Slot**:
    - Purpose: This is the primary way to inject content into the card. Any content placed between the opening and closing `<Card>` tags will be rendered here.
    - Example:
      ```html
      <Card>
        <h2 class="text-xl font-bold">Card Title</h2>
        <p>This is some content inside the card.</p>
      </Card>
      ```

## Events

- **`click`**:
    - Payload: None (the event object itself, if needed by the handler).
    - Purpose: Emitted when the main `div` of the card is clicked.
    - Usage:
      ```html
      <Card @click="handleCardClick">
        <!-- ... content ... -->
      </Card>
      ```

## Usage Examples

1.  **Basic Card with Padding (via Tailwind on Card itself)**:
    ```html
    <Card class="p-6 shadow-md">
      <h3 class="text-lg font-medium">Important Information</h3>
      <p class="mt-2 text-sm text-gray-600">
        Details about the important information go here.
      </p>
    </Card>
    ```
    *Observation*: Most often, padding and other utility classes (`shadow-md`, `max-w-lg`, etc.) are applied directly to the `<Card>` component in the template where it's used, rather than relying solely on `innerClass`.

2.  **Card with Internal Scrolling (using `innerClass`)**:
    ```html
    <Card inner-class="overflow-y-auto max-h-96 p-4">
      <!-- Long content that will scroll -->
      <div v-for="item in veryLongList" :key="item.id">
        {{ item.name }}
      </div>
    </Card>
    ```
    *Source Example*: Similar to `pages/admin/clothing.vue` which uses `inner-class="overflow-x-auto"`.

3.  **Card as a Clickable Element**:
    ```html
    <Card @click="navigateToDetails(item.id)" class="hover:bg-gray-50 cursor-pointer p-4">
      <h4>{{ item.title }}</h4>
    </Card>
    ```

## Styling
- The base `<Card>` component provides a white background and rounded corners.
- Further styling (padding, margins, shadows, borders, width, height, etc.) is typically applied using Tailwind CSS utility classes directly on the `<Card>` element in the parent template.
- The `innerClass` prop can be used for styling the direct content wrapper if needed, especially for things like padding that shouldn't affect box-shadow or borders applied to the outer card element.

## Related Components
- `components/card/Header.vue`: A specific component for card headers, often seen used in conjunction with `<Card>` (e.g., `pages/profile/index.vue`).
- `components/card/WithIcon.vue`: Another specialized card variant.

## Best Practices
- Use `<Card>` as a fundamental building block for UI sections.
- Apply most styling (padding, shadows, etc.) via Tailwind classes on the `<Card>` tag itself for clarity.
- Use the `innerClass` prop sparingly, primarily for internal layout controls like overflow or when direct child styling is needed without an extra wrapper `div` inside the slot.
- Combine with `<CardHeader>` or other sub-components for more structured card content when appropriate.
