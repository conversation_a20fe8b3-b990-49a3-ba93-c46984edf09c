---
description: Guide for the <Tooltip> component (components/tooltip/Tooltip.vue), for displaying informational text on hover. Covers positioning, custom trigger elements via slot, and HTML content via the 'info' prop.
globs:
alwaysApply: false
---
# Component Guide: Tooltip

**File Path**: `components/tooltip/Tooltip.vue`
(Typically used as `<Tooltip>` in templates).

## Overview
The `<Tooltip>` component provides a way to display informational text when a user hovers over an element (typically an icon). It can be positioned to the top, bottom, left, or right of the trigger element.

## Template Structure (Simplified)
```html
<div class="relative flex items-center">
  <!-- Trigger Element (Slot or default IconInfo) -->
  <div @mouseenter="showTooltip = true" @mouseleave="showTooltip = false" @click="showTooltip = !showTooltip" class="cursor-pointer">
    <slot>
      <IconInfo class="w-4 h-4 text-gray-400" />
    </slot>
  </div>

  <!-- Tooltip Content -->
  <div
    v-if="showTooltip"
    :class="[
      'absolute z-50 p-2 text-xs text-white bg-gray-900 rounded-md shadow-lg w-max max-w-xs whitespace-normal break-words',
      tooltipPositionClass
    ]"
    v-html="info"
  >
    <!-- Arrow -->
    <div :class="arrowPositionClass" class="absolute w-3 h-3 bg-gray-900 transform rotate-45"></div>
  </div>
</div>
```

## Props

- **`info`**:
    - Type: `String`
    - Required: `false`
    - Default: `'Tooltip information here.'`
    - Purpose: The HTML content to be displayed inside the tooltip. `v-html` is used, so HTML tags are allowed (e.g., for links like `<a href='...'>...</a>`).

- **`position`**:
    - Type: `String`
    - Default: `'top'`
    - Accepted Values: `'top'`, `'bottom'`, `'left'`, `'right'`
    - Purpose: Determines the placement of the tooltip relative to its trigger element.

## Data

- **`showTooltip`**:
    - Type: `Boolean`
    - Default: `false`
    - Internal state to control the visibility of the tooltip.

## Computed Properties

- **`tooltipPositionClass`**:
    - Returns CSS classes to position the tooltip bubble based on the `position` prop.
    - Examples: `bottom-full -translate-x-1/2 left-1/2 mb-2` for `top`.

- **`arrowPositionClass`**:
    - Returns CSS classes to position the tooltip's arrow based on the `position` prop.
    - Examples: `top-full -translate-x-1/2 left-1/2 -mt-1.5` for `top`.

## Slots

- **Default Slot**:
    - Purpose: Allows providing a custom trigger element for the tooltip. If not provided, it defaults to an `<IconInfo>` component.
    - Example:
      ```html
      <Tooltip info="This is a custom trigger.">
        <ButtonSecondary size="xs">Hover Me</ButtonSecondary>
      </Tooltip>
      ```

## Events
- None directly emitted by the component itself. Interaction is through mouse hover and click on the trigger element.

## Usage Examples

1.  **Default Info Icon Tooltip (Top Position)**:
    ```html
    <label class="mr-2">Setting Name</label>
    <Tooltip info="This setting controls the primary color theme of the application." />
    ```

2.  **Tooltip with Custom Trigger and Left Position**:
    ```vue
    <!-- File: components/edit/RemixTab.vue -->
    <Tooltip position="left">
      <template #default>
        <div class="p-2 bg-blue-100 rounded">
          Custom Element
        </div>
      </template>
      <template #info>
        <!-- Assuming info can also be a slot, but current component uses v-html="info" prop -->
        Detailed explanation for the custom element shown on the left.
      </template>
    </Tooltip>
    ```
    *(Note: The provided `Tooltip.vue` source uses `v-html="info"` for the content, not a named `info` slot. The example above is adjusted for the `info` prop)*

3.  **Tooltip with HTML content in `info` prop (from Grep results)**:
    ```vue
    <!-- File: pages/checkout/v2.vue -->
    <Tooltip
      info="You get to pick how your headshots will look like. Select from a wide range of over 100+ backdrops and outfits. <a href='/backdrop-and-outfit' class='underline text-sky-500'>Click here</a>"
    />
    ```

4.  **Tooltip on a Label (from Grep results)**:
    ```vue
    <!-- File: pages/app/upload/personal-info.vue -->
    <label class="flex items-center">
      Gender
      <Tooltip>
        <!-- This will use the info prop, or default text if info prop is not set -->
      </Tooltip>
    </label>
    ```
    *(Observation: If the `info` prop is not provided, it defaults to "Tooltip information here.")*

## Styling
- Tooltip bubble: `bg-gray-900`, `text-white`, `text-xs`, `p-2`, `rounded-md`, `shadow-lg`.
- Max width for tooltip content: `max-w-xs`.
- Arrow: Styled as a small rotated square using `bg-gray-900`.
- `whitespace-normal break-words` ensures text wraps and breaks correctly within the max width.

## Best Practices
- Use tooltips for providing brief, supplementary information that clarifies a UI element or feature.
- The default slot is useful for attaching a tooltip to interactive elements other than the standard info icon.
- Since the `info` prop accepts HTML via `v-html`, ensure any user-generated content passed to it is properly sanitized if that's a possibility, though typically tooltips display static, developer-defined text.
- Test tooltip positioning, especially `left` and `right`, to ensure they don't get cut off at screen edges. The `w-max max-w-xs` helps, but context matters.
- Keep tooltip content concise. For longer explanations, consider a modal or an expandable section.
