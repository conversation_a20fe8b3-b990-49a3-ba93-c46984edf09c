---
description:
globs:
alwaysApply: true
---
# Vuex Store Conventions

## Structure
- Vuex store files are located in the `store/` directory.
- The store is modular. `store/index.js` contains the root state, mutations, actions, and getters. Other `.js` files in `store/` are treated as namespaced modules (e.g., `store/user.js`, `store/cart.js`).
    - When accessing mutations/actions of a module from another module or globally, use the namespace: `commit('moduleName/MUTATION_NAME', payload)` or `dispatch('moduleName/ACTION_NAME', payload, { root: true })`.
- Each module typically defines its own `state`, `mutations`, `actions`, and `getters`.

## Naming
- State properties: camelCase (e.g., `userData`, `cartItems`).
- Mutations: UPPER_SNAKE_CASE (e.g., `SET_USER_DATA`, `ADD_ITEM_TO_CART`).
- Actions: camelCase for async operations (e.g., `fetchUserProfile`, `submitOrder`) or `onAuthStateChangedAction` style for event-driven actions.

## State
- State should be plain JavaScript objects.
- Initialize state with appropriate default values.

## Mutations
- Mutations must be synchronous.
- They are the *only* way to change state.
- They receive `state` as the first argument and an optional `payload`.

## Actions
- Actions can be asynchronous (e.g., for API calls).
- They commit mutations to change state.
- They receive a `context` object as the first argument (which has `commit`, `dispatch`, `state`, `getters`, `rootState`, `rootGetters`) and an optional `payload`.
- Use `this.$axios` (from Nuxt) or the custom Axios instance for API calls within actions.

## `nuxtServerInit`
- The `nuxtServerInit` action in `store/index.js` is used to pre-populate the store on the server-side before the page is rendered. This is useful for fetching essential global data (e.g., UI configurations, feature flags, user session if cookie-based).

## Accessing Store in Components
- **State**: `this.$store.state.moduleName.propertyName` or `this.$store.state.rootPropertyName`. Use Vuex mappers (`mapState`) for cleaner access, especially for multiple properties.
- **Getters**: `this.$store.getters['moduleName/getterName']`. Use `mapGetters`.
- **Mutations**: `this.$store.commit('moduleName/MUTATION_NAME', payload)` or `this.$store.commit('ROOT_MUTATION_NAME', payload)`. Use `mapMutations`.
- **Actions**: `this.$store.dispatch('moduleName/actionName', payload)` or `this.$store.dispatch('rootActionName', payload)`. Use `mapActions`.

## Key Store Modules (Examples)
- `store/index.js`: Root store, global app state (modals, UI settings, global data like styles, packages).
- `store/user.js`: User authentication data, profile information, roles.
- `store/results.js`: Data related to headshot generation results.
- `store/organization.js`: Team/organization related data.
- `store/admin.js`: State specific to admin functionalities.

## Example Flow
1. Component dispatches an action: `this.$store.dispatch('user/fetchProfile', userId)`.
2. Action (`user/fetchProfile`) makes an API call using `this.$axios`.
3. On success, action commits a mutation: `commit('SET_PROFILE_DATA', response.data)`.
4. Mutation (`SET_PROFILE_DATA`) updates the state: `state.profile = response.data`.
5. Component (if using computed properties mapped to state or getters) reactively updates.
