---
description: Guide for the <ButtonDropdown> component (components/button/Dropdown.vue), which provides a button that reveals a dropdown menu. Covers themes, alignment, item structure, and events.
globs:
alwaysApply: false
---
# Component Guide: ButtonDropdown (Dropdown)

**File Path**: `components/button/Dropdown.vue`
(Typically used as `<ButtonDropdown>` in templates).

## Overview
The `<ButtonDropdown>` component renders a button that, when clicked, reveals a dropdown menu of selectable items. It supports customization of alignment, appearance, and actions.

## Template Structure (Simplified Internal)
```html
<div :class="{ 'w-full': fullWidth }">
  <!-- Click-outside-to-close overlay -->
  <div v-if="showMenu" @click="showMenu = false" />

  <div class="relative text-left">
    <!-- Button part -->
    <div @click="toggleMenu">
      <!-- Theme v2 button (three dots) -->
      <button v-if="theme === 'v2'">
        <!-- ... three dots ... -->
      </button>
      <!-- Theme v1 button (uses ButtonWhite) -->
      <ButtonWhite v-else :size="size">
        <span>{{ title }}</span>
        <svg v-if="!icon"> <!-- Default caret icon --> </svg>
        <component :is="icon" v-else /> <!-- Custom icon -->
        <!-- Optional caret for ButtonWhite -->
      </ButtonWhite>
    </div>

    <!-- Dropdown menu part -->
    <div v-if="showMenu" class="absolute z-[900] ..." :class="{ ...alignment classes... }">
      <ul>
        <li v-for="item in items" @click="handleClick(item.value || item)">
          <component :is="item.icon" v-if="item.icon" />
          <span>{{ item.title || item }}</span>
        </li>
      </ul>
    </div>
  </div>
</div>
```
- Handles showing/hiding the menu (`showMenu` data property).
- Implements click-outside-to-close functionality.
- Supports two visual themes for the button part.

## Props

- **`items`**:
    - Type: `Array`
    - Required: `true`
    - Purpose: An array of objects or strings representing the items in the dropdown menu.
        - If string: The string itself is used as both the `value` and `title` (capitalized).
        - If object:
            - `value`: The value emitted when the item is selected.
            - `title`: The text displayed for the item (capitalized if `value` is used as fallback).
            - `icon`: (Optional) String name of an icon component to display next to the item title.
            - `classes`: (Optional) String of CSS classes to apply to the `<li>` element.
            - `color`: (Optional) String CSS color value to apply as inline style to the `<li>`.
- **`title`**:
    - Type: `String`
    - Required: `true`
    - Purpose: The text displayed on the main button (for `theme='v1'`).
- **`xalign`**:
    - Type: `String`
    - Default: `'right'`
    - Options: `'left'`, `'right'`, `'center'`, `'left-full'`, `'right-full'`.
    - Purpose: Horizontal alignment of the dropdown menu relative to the button.
- **`valign`**:
    - Type: `String`
    - Default: `'bottom'`
    - Options: `'top'`, `'bottom'`, `'center'`.
    - Purpose: Vertical alignment of the dropdown menu relative to the button. Also influences `origin-*` classes for transitions.
- **`size`**:
    - Type: `String`
    - Default: `'base'`
    - Purpose: Size of the button (passed to `<ButtonWhite>` if `theme='v1'`). Affects padding and text size. Options typically are `xs`, `sm`, `base`, `lg`, `xl`.
- **`theme`**:
    - Type: `String`
    - Default: `'v1'`
    - Options: `'v1'`, `'v2'`.
        - `v1`: Uses `<ButtonWhite>` component for the trigger, showing `title` and a caret/icon.
        - `v2`: Shows a button with three vertical dots as the trigger.
- **`fullWidth`**:
    - Type: `Boolean`
    - Default: `false`
    - Purpose: If `true`, the component (and button) will take up the full width of its container.
- **`icon`**:
    - Type: `String` (component name)
    - Default: `null`
    - Purpose: Name of an icon component to display on the button (for `theme='v1'`). If `null`, a default caret icon is shown.
- **`iconPosition`**:
    - Type: `String`
    - Default: `'after'`
    - Options: `'before'`, `'after'`.
    - Purpose: Position of the `icon` relative to the `title` on the button (for `theme='v1'`).
- **`showCaret`**:
    - Type: `Boolean`
    - Default: `false`
    - Purpose: Specifically for `theme='v1'`, if `true`, shows an additional, different caret icon on the far right of the button, separate from the main `icon` prop.
- **`buttonClass`**:
    - Type: `String`
    - Default: `''`
    - Purpose: Additional CSS classes to apply to the trigger button element.
- **`dropdownClass`**:
    - Type: `String`
    - Default: `''`
    - Purpose: Additional CSS classes to apply to the dropdown menu `div`.

## Slots
- None.

## Events

- **`select`**:
    - Payload: The `value` of the selected item from the `items` array.
    - Purpose: Emitted when an item in the dropdown menu is clicked.

## Internal Logic
- `showMenu` (data property): Controls the visibility of the dropdown.
- `handleClick(value)`: Emits the `select` event and closes the menu.
- `toggleMenu()`: Toggles `showMenu`.
- `handleClickOutside(e)`: Attached to the document, closes the menu if a click occurs outside the component.

## Usage Examples

1.  **Basic Dropdown with String Items**:
    ```html
    <ButtonDropdown
      title="Actions"
      :items="['edit', 'delete', 'archive']"
      @select="handleActionSelected"
    />
    ```
    ```javascript
    // In methods
    handleActionSelected(action) {
      // action will be 'edit', 'delete', or 'archive'
      console.log('Selected:', action);
    }
    ```

2.  **Dropdown with Object Items (including icons and custom classes)**:
    ```html
    <ButtonDropdown
      title="User Options"
      :items="[
        { title: 'View Profile', value: 'view_profile', icon: 'IconUserCircle' },
        { title: 'Settings', value: 'settings', icon: 'IconCog' },
        { title: 'Logout', value: 'logout', icon: 'IconLogout', classes: 'text-red-500 hover:bg-red-100', color: '#EA4335' }
      ]"
      @select="handleUserOption"
      xalign="left"
      valign="top"
      size="sm"
    />
    ```
    *Source Example*: Many admin pages like `pages/admin/refund/index.vue` use a pattern of `:items="dropdownItems" title="Actions" @select="handleDropdown($event, item._id)"` where `dropdownItems` is a computed property returning an array of such objects.

3.  **Theme V2 Dropdown (Three Dots)**:
    ```html
    <ButtonDropdown
      theme="v2"
      :items="actionMenuItems"
      @select="performAction"
      xalign="right"
      buttonClass="p-2"
    />
    ```
    *Note*: The `title` prop is not directly visible for `theme='v2'` but is still good for accessibility (aria-label on the button).

4.  **Dropdown with Custom Icon on Button**:
    ```html
    <ButtonDropdown
      title="Share"
      icon="IconShare"
      iconPosition="before"
      :items="shareOptions"
      @select="shareContent"
    />
    ```

## Styling
- The component relies heavily on Tailwind CSS for styling both the button and the dropdown menu.
- `xalign` and `valign` props control positioning using Tailwind classes (e.g., `right-0`, `top-full`, `translate-x-1/2`).
- `buttonClass` and `dropdownClass` allow for further customization.
- The dropdown menu has a `z-index` of `z-[900]`.

## Best Practices
- Provide a clear `title` for the button, especially for `theme='v1'`.
- Structure the `items` array with `title` and `value` for clarity, especially when values are not simple human-readable strings.
- Use icons within menu items (`item.icon`) for better visual cues where appropriate.
- Choose `xalign` and `valign` carefully to ensure the dropdown appears in a sensible position relative to the button and viewport.
- Test responsiveness, as dropdowns near screen edges might require different alignment settings.
