---
description: Compares the <Modal> (components/modal/Modal.vue) and <Popup> (components/popup/Popup.vue) components, detailing their features, props, styling differences, and guidance on when to use each.
globs:
alwaysApply: false
---
# Component Guide: Modal vs. Popup

**File Paths**:
- `components/modal/Modal.vue` (Used as `<Modal>`)
- `components/popup/Popup.vue` (Used as `<Popup>`)

## Overview
Both `<Modal>` and `<Popup>` are used to display content overlaid on the main page, typically for focused tasks, information display, or user interaction. While they serve similar purposes, they have distinct structures, props, and potentially different intended use cases or legacy reasons for coexistence.

## `<Modal>` (`components/modal/Modal.vue`)

- **Purpose**: A general-purpose modal dialog.
- **Key Features & Structure**:
    - Creates a full-screen overlay (`fixed inset-0 ... bg-gray-900 bg-opacity-50 backdrop-blur-sm`) that closes the modal on click.
    - The modal content itself is centered (`flex items-center justify-center`) and has a maximum height (`max-h-[calc(90vh)]`).
    - Content is placed within a `div` with `bg-white`, `rounded-lg`, `shadow`, `border`, and `overflow-y-scroll` (for scrollable content within the modal).
    - Has an optional, small, circular close button (`IconCross`) at the top-right of the content area.

- **Template Structure (Simplified)**:
  ```html
  <div class="fixed inset-0 z-50 flex items-center justify-center ... h-screen w-full">
    <!-- Backdrop -->
    <div class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm" @click="$emit('close')" />
    <!-- Modal Panel -->
    <div :class="`... max-h-[calc(90vh)] ... rounded-lg bg-white overflow-y-scroll ... ${maxWidth} ...`">
      <slot /> <!-- Modal content here -->
      <div v-if="showCloseButton" class="... absolute top-2 right-2 ... cursor-pointer" @click="$emit('close')">
        <IconCross />
      </div>
    </div>
  </div>
  ```

- **Props**:
    - `showCloseButton`:
        - Type: `Boolean`
        - Default: `true`
        - Purpose: Toggles the visibility of the default top-right cross icon button.
    - `maxWidth`:
        - Type: `String`
        - Default: `'sm:max-w-3xl'`
        - Purpose: Allows specifying Tailwind max-width classes for the modal content area (e.g., `'sm:max-w-lg'`, `'sm:max-w-xl'`).

- **Slots**:
    - **Default Slot**: For the main content of the modal.

- **Events**:
    - `close`: Emitted when the backdrop is clicked or the close button (if visible) is clicked.

- **Common Usage (from grep)**:
    - Often used with `v-if` to control visibility.
    - `maxWidth` prop is frequently used to adjust size.
    - `@close` event is used to set the controlling boolean to `false`.
    - Examples: Displaying forms (admin edits), previews (image previews), confirmation dialogs, multi-step flows (onboarding), informational messages.

## `<Popup>` (`components/popup/Popup.vue`)

- **Purpose**: Another modal-like component, possibly with a slightly different styling or historical use.
- **Key Features & Structure**:
    - Also creates a full-screen overlay (`fixed inset-0 ... bg-gray-500 opacity-75`) that closes the popup on click (via `closeModal` method).
    - Uses a trick (`<span class="hidden sm:inline-block sm:align-middle sm:h-screen" />`) for vertical alignment, and the content `div` aligns to `align-bottom` then `sm:align-middle`.
    - Content area (`popup-modal`) is `relative inline-block`, `bg-white`, `rounded-lg`, `shadow-xl`, `transform transition-all`, `overflow-y-scroll`, `max-h-[calc(100vh-64px)]`.
    - Has a similar small, circular close button (`IconCross`) at the top-right.
    - Includes a `title` prop that renders an `<h3>`.

- **Template Structure (Simplified)**:
  ```html
  <div class="fixed inset-0 z-[90] overflow-y-auto">
    <div class="flex items-center justify-start min-h-screen ...">
      <!-- Backdrop -->
      <div class="fixed inset-0 transition-opacity cursor-pointer" @click="closeModal">
        <div class="absolute inset-0 bg-gray-500 opacity-75" />
      </div>
      <!-- Centering trick -->
      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" />
      <!-- Popup Panel -->
      <div :class="`... popup-modal ... max-h-[calc(100vh-64px)] ${additionalClasses}`" :style="`max-width:${maxWidth}; padding:${padding}px;`">
        <div class="... absolute top-2 right-2 ... cursor-pointer" @click="closeModal">
          <IconCross />
        </div>
        <div>
          <h3 v-if="title">{{ title }}</h3>
          <div>
            <slot /> <!-- Popup content here -->
          </div>
        </div>
      </div>
    </div>
  </div>
  ```

- **Props**:
    - `title`:
        - Type: `String`
        - Default: `''`
        - Purpose: An optional title displayed at the top of the popup.
    - `description`: (Unused in the template provided)
        - Type: `String`
        - Required: `false`
    - `padding`:
        - Type: `Number`
        - Default: `24`
        - Purpose: Sets the padding (in pixels) for the content area via inline style.
    - `size`:
        - Type: `String`
        - Default: `'sm'`
        - Purpose: Controls `max-width` via a computed property (`maxWidth`). Supports `xs` to `7xl` and `full`.
    - `additionalClasses`:
        - Type: `String`
        - Default: `''`
        - Purpose: Allows adding extra CSS classes to the main popup panel.

- **Computed**:
    - `maxWidth`: Dynamically calculates `max-width` based on `this.size` and `window.innerWidth` (client-side).

- **Methods**:
    - `closeModal()`: Sets `document.body.style.overflow = 'auto'`, emits `closeModal` and `close` events.

- **Events**:
    - `closeModal`: Emitted by `closeModal()` method.
    - `close`: Also emitted by `closeModal()` method. (Note: `<Modal>` only emits `close`).

- **Common Usage (from grep)**:
    - Widely used, similar contexts to `<Modal>`.
    - The `size` prop is very common for controlling width.
    - `title` prop is often used.
    - Some specific variations like `<PopupSlideIn>` and `<PopupTrustpilot>` exist, suggesting `<Popup>` might be a base for more specialized versions.
    - Examples: Instructions, warnings, forms (creating coupons, API clients), image previews, QR code display.

## Key Differences & When to Use

1.  **Styling & Appearance**:
    - **Backdrop**: `<Modal>` uses `bg-gray-900 bg-opacity-50 backdrop-blur-sm`. `<Popup>` uses `bg-gray-500 opacity-75` (no blur by default).
    - **Max Height**: `<Modal>` uses `max-h-[calc(90vh)]`. `<Popup>` uses `max-h-[calc(100vh-64px)]`.
    - **Width Control**: `<Modal>` uses `maxWidth` prop directly for Tailwind classes. `<Popup>` uses a `size` prop (`xs`-`7xl`, `full`) that translates to `max-width` values in a computed property, and also allows responsive `max-width: 90vw` on small screens.
    - **Padding**: `<Popup>` has a `padding` prop for inline style padding. `<Modal>` relies on padding within the slotted content or its own default padding via classes.
    - **Title**: `<Popup>` has a built-in `title` prop. `<Modal>` requires the title to be part of the slot. (Though a `<ModalTitle>` component was seen, suggesting a companion for `<Modal>`).
    - **Z-index**: `<Modal>` uses `z-50`. `<Popup>` uses `z-[90]`. Both are high, but `Popup` is higher.

2.  **Events**:
    - `<Modal>` emits `close`.
    - `<Popup>` emits `closeModal` *and* `close`.

3.  **Props**: `<Popup>` has more props for direct styling (`padding`, `size`, `additionalClasses`, `title`). `<Modal>` is simpler, relying more on slot content and a single `maxWidth` prop.

4.  **Body Scroll**: `<Popup>` explicitly sets `document.body.style.overflow = 'auto'` on close. `<Modal>` does not explicitly manage body overflow in its own code (might be handled by a global plugin or layout).

**Which to Choose?**
- There isn't a strict rule evident just from the code; both are heavily used. It might be a matter of:
    - **Existing Patterns**: Follow the pattern in the section of the app you're working on.
    - **Specific Styling Needs**: If `<Popup>`'s `size`, `padding`, or built-in `title` are convenient, it might be a quicker choice. If `<Modal>`'s backdrop blur or specific `maxWidth` class control is preferred, use that.
    - **Legacy**: One might be older, and the other introduced later with slight variations. `<Modal>` seems a bit more streamlined in its props, while `<Popup>` offers more direct style control through props.
    - **Specialized Popups**: If you see `<PopupSlideIn>` or other `Popup[Something]` components, `<Popup>` might be the base for these, indicating a family of components.
- For a new, simple modal, `<Modal>` might be slightly preferred due to its cleaner prop interface, assuming its styling is adequate. If more granular control over width (via `size` enum) or a built-in title is desired, `<Popup>` could be better.
- The higher `z-index` of `<Popup>` (`z-[90]` vs `z-50` for `<Modal>`) might be relevant if stacking multiple overlay types, though ideally, only one modal/popup should be active at a time.

Consider checking if there's a team preference or if one is being phased out. Given the similar functionality but different props and minor style differences, they could potentially be consolidated in the future if desired.
