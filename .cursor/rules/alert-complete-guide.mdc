---
description: Guide for the <AlertComplete> component (components/alert/Complete.vue), used to display success or completion messages with an optional action button.
globs:
alwaysApply: false
---
# Component Guide: AlertComplete

**File Path**: `components/alert/Complete.vue`
(Likely used as `<AlertComplete>` in templates).

## Overview
The `<AlertComplete>` component is designed to display a success or completion message. It features a green color scheme, a checkmark icon, a title, a description, and an optional button for further action.

## Template Structure (Simplified)
```html
<div class="rounded-md bg-green-50 p-4">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-5 w-5 text-green-400"> <!-- Checkmark Icon --> </svg>
    </div>
    <div class="ml-3">
      <h3 class="text-sm font-medium text-green-800">{{ title }}</h3>
      <div class="mt-2 text-sm text-green-700">
        <p>{{ description }}</p>
      </div>
      <div class="mt-4" v-if="button && button.url">
        <nuxt-link :to="button.url" class="... text-green-800 hover:bg-green-100 ...">
          {{ button.text }}
        </nuxt-link>
      </div>
    </div>
  </div>
</div>
```

## Props

- **`title`**:
    - Type: `String`
    - Required: `true`
    - Purpose: The main heading for the alert.
- **`description`**:
    - Type: `String`
    - Required: `true`
    - Purpose: A more detailed message below the title.
- **`button`**:
    - Type: `Object`
    - Required: `true` (but the button only renders if `button.url` is present).
    - Purpose: An object to define an action button within the alert.
        - `url` (String): The URL for the `<nuxt-link>`. If not provided, the button is not rendered.
        - `text` (String): The text to display on the button.

## Slots
- None.

## Events
- None directly, but the button uses `<nuxt-link>` for navigation.

## Usage Example

1.  **Alert with Title, Description, and Action Button**:
    ```html
    <AlertComplete
      title="Profile Updated Successfully!"
      description="Your changes have been saved and are now live."
      :button="{ text: 'View Profile', url: '/profile' }"
    />
    ```

2.  **Alert for Phone Connection (from Grep result)**:
    ```html
    <!-- File: components/upload/Add.vue -->
    <AlertComplete
      v-if="phoneConnected"
      title="Phone connected"
      description="You can start clicking pictures now"
      :button="{} // Assuming an empty object or one without a URL won't render the button "
    />
    ```
    *Observation from Grep*: The `button` prop was passed an empty object in the example. Based on the component logic (`v-if="button && button.url"`), this would mean the button part does not render, which is fine if no action is needed.

## Styling
- Uses a light green background (`bg-green-50`) with darker green text for titles (`text-green-800`) and descriptions (`text-green-700`).
- Includes a hardcoded SVG checkmark icon (`text-green-400`).
- The button is styled with green text and a lighter green background on hover.

## Best Practices
- Use `<AlertComplete>` to provide clear positive feedback to the user after a successful operation.
- Ensure the `title` and `description` are concise and informative.
- If an action button is relevant (e.g., navigating to the newly created resource, or closing the alert if it's not auto-dismissing), provide the `button.url` and `button.text`.
- If no button is needed, you can pass an empty object or an object without a `url` property to the `button` prop (e.g., `:button="{}"` or `:button="{ text: 'OK' }"` if only text was considered, but `url` is key).
