---
description:
globs:
alwaysApply: true
---
# Loading Indicators Conventions

Three main types/approaches for loading indicators are used:

## 1. Global Full-Page Loading Indicator (`this.$loading`)
- **Implementation**: Provided by the custom Vue plugin `plugins/vue-loading.js`.
- **Access**: Available globally in Vue components via `this.$loading` (and `context.$loading` in asyncData, plugins, etc.).
- **Usage**:
    - To show: `this.$loading.show(options)`
        - `options` (optional object):
            - `title`: String to display a title with the spinner.
            - `description`: String to display a description below the spinner/title.
        - Example: `this.$loading.show({ title: 'Processing your request...' })`
        - If no options: `this.$loading.show()` (shows default spinner).
    - To hide: `this.$loading.hide()`
- **Behavior**: When shown, it covers the entire page with a semi-transparent overlay and a spinner. It also sets `document.body.style.overflow = 'hidden'` to prevent background scrolling and restores it on hide.
- **When to use**: For page transitions or significant asynchronous operations that block user interaction with the *whole page* (e.g., initial data load for a complex view, submitting a critical form before redirecting, global state changes affecting the entire UI).

## 2. Section/Component Loading with `<LoadingWrapper />`
- **Component**: `<LoadingWrapper>` (from `components/loading/Wrapper.vue`).
- **Purpose**: To show a loading state for a specific section or component while other parts of the page might still be interactive or already loaded.
- **Props**:
    - `isLoading` (Boolean, required): Controls visibility. If `true`, loading state is shown; otherwise, default slot content is shown.
    - `title` (String, optional, default: 'Loading...'): Text passed to the internal `<LoadingSpinner />`.
    - `showSpinner` (Boolean, optional, default: `true`): If `true`, shows `<LoadingSpinner />`. If `false`, renders a named slot `loading` for custom content.
- **Slots**:
    - Default slot: Content to display when `isLoading` is `false`.
    - `loading` (named slot): Custom content for the loading state if `showSpinner` is `false`.
- **Usage**:
  ```vue
  <template>
    <LoadingWrapper :is-loading="isSectionLoading" title="Loading section data...">
      <!-- Content for this section -->
      <p>{{ sectionData }}</p>
    </LoadingWrapper>
  </template>
  <script>
  export default {
    data() { return { isSectionLoading: false, sectionData: null }; },
    async mounted() {
      this.isSectionLoading = true;
      // this.sectionData = await fetchSectionData();
      this.isSectionLoading = false;
    }
  }
  </script>
  ```
- **When to use**: For loading states within a specific part of a page or component (e.g., loading data for a table, a card, a form section) where the rest of the UI remains interactive or has its own loading state.

## 3. Inline/Button Loading Indicators
- **Implementation**: Typically a boolean data property (e.g., `isSubmitting: false`) used to conditionally show a small spinner icon within a button or next to an element, or to change button text (e.g., "Save" -> "Saving...").
- **Spinner Component**: May use `<LoadingSpinner />` (potentially with size props) or a more compact spinner like `<LoadingSpinnerSingle />` or specific icon components.
- **Usage**:
  ```vue
  <ButtonPrimary :is-disabled="isSubmitting" @click="submitForm">
    <LoadingSpinnerSingle v-if="isSubmitting" class="w-5 h-5" />
    <span v-else>Submit</span>
  </ButtonPrimary>
  ```
- **When to use**: For very localized actions, like a button click that triggers an async operation, to give immediate feedback without obscuring other content.

## General Guidance
- Always provide feedback for asynchronous operations that take noticeable time.
- Choose the appropriate loading indicator based on the scope and impact of the loading state:
    - `this.$loading`: Entire page blocked.
    - `<LoadingWrapper>`: Specific section/component blocked, rest of page usable.
    - Inline/Button Spinner: Specific action in progress, minimal UI impact.
- Ensure `hide()` is called for the global `$loading` service, typically in a `finally` block.
- For `<LoadingWrapper>` and inline spinners, ensure the controlling boolean (e.g., `isLoading`) is correctly reset after the operation completes (success or error).
