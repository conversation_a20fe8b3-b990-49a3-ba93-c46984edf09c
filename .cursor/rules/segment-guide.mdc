---
description: Guide for the <Segment> component (components/segment/Segment.vue), a segmented control for selecting one option from a set. Covers props (items, initialActive) and the 'selected' event.
globs:
alwaysApply: false
---
# Component Guide: Segment

**File Path**: `components/segment/Segment.vue`
(Typically used as `<Segment>` in templates).

## Overview
The `<Segment>` component is a segmented control, allowing users to select one option from a set. It renders a row of buttons, with one appearing as "active".

## Template Structure (Simplified)
```html
<div class="flex">
  <button
    v-for="(item, index) in items"
    :key="item.value"
    type="button"
    class="text-sm px-4 py-3 leading-none border border-[#D0D5DD]"
    :class="{
      'text-paragraph font-medium bg-white': activeItem !== item.value,
      'text-primary-500 font-bold bg-[#F9FAFB]': activeItem === item.value,
      'rounded-l-lg': index === 0,
      'rounded-r-lg': index === items.length - 1,
      'border-r-0': index < items.length - 1 /* Avoid double borders */
    }"
    @click="handleClick(item.value)"
  >
    {{ item.label }}
  </button>
</div>
```

## Props

- **`items`**:
    - Type: `Array`
    - Required: `true`
    - Validator: Ensures each item in the array is an object with `label` and `value` properties.
    - Purpose: An array of objects, where each object defines a segment/button.
        - `label` (String): The text displayed on the button.
        - `value` (String): A unique identifier for the item, emitted when selected.
    - Example: `[{ label: 'Option 1', value: 'opt1' }, { label: 'Option 2', value: 'opt2' }]`

- **`initialActive`**:
    - Type: `String`
    - Default: `null`
    - Purpose: The `value` of the item that should be initially selected when the component mounts.

## Data

- **`activeItem`**:
    - Internal state, initialized with `this.initialActive`.
    - Stores the `value` of the currently active segment item.

## Methods

- **`handleClick(value)`**:
    - Called when a segment button is clicked.
    - Sets `this.activeItem` to the clicked item's `value`.
    - Emits the `selected` event with the `value`.

## Events

- **`selected`**:
    - Payload: The `value` (String) of the selected item.
    - Emitted when a segment item is clicked and becomes active.
    - Purpose: Allows the parent component to react to the selection change.

## Usage Examples

1.  **Basic Segment Control**:
    ```vue
    <template>
      <div>
        <Segment
          :items="viewOptions"
          :initial-active="currentView"
          @selected="updateView"
        />
        <p class="mt-4">Selected View: {{ currentView }}</p>
      </div>
    </template>

    <script>
    export default {
      data() {
        return {
          currentView: 'list',
          viewOptions: [
            { label: 'List View', value: 'list' },
            { label: 'Grid View', value: 'grid' },
            { label: 'Map View', value: 'map' }
          ]
        };
      },
      methods: {
        updateView(selectedValue) {
          this.currentView = selectedValue;
        }
      }
    };
    </script>
    ```

2.  **Filter Status (from Grep result)**:
    ```vue
    <!-- File: components/team/Dashboard/Dashboard.vue -->
    <Segment
      :key="filterStatus"
      class="hidden sm:block"
      :items="filterStatusOptions"
      :initial-active="filterStatus"
      @selected="filterStatus = $event; query = ''"
    />
    ```
    *Observation: `:key` is bound to `filterStatus`, which might be used to re-initialize the component if `filterStatus` changes externally. The `@selected` event directly updates `filterStatus` and clears a `query` data property.*

3.  **Tab-like Navigation (from Grep result)**:
    ```vue
    <!-- File: components/gallery/result/Page.vue -->
    <Segment
      :key="tab"
      :items="tabs.map(t => ({ label: t.title, value: t.identifier }))"
      :initial-active="tab"
      @selected="moveToTab($event)"
    />
    ```
    *Observation: The `items` prop is dynamically generated from a `tabs` array. The `@selected` event calls a `moveToTab` method.*

## Styling
- Buttons are styled with borders, padding (`px-4 py-3`), and specific text/background colors for active vs. inactive states.
    - **Active**: `text-primary-500 font-bold bg-[#F9FAFB]`
    - **Inactive**: `text-paragraph font-medium bg-white`
- Outer buttons have rounded corners on one side (`rounded-l-lg`, `rounded-r-lg`).
- `border-r-0` on items not at the end prevents double borders between segments.

## Best Practices
- Use `<Segment>` for a small, fixed set of mutually exclusive options.
- Ensure the `items` prop follows the required structure (array of objects with `label` and `value`).
- Provide an `initialActive` value if a default selection is needed.
- Listen to the `selected` event to respond to user choices.
- If the list of items or the active state needs to be dynamically updated from the parent, consider using a `:key` to force re-rendering if `initialActive` needs to be re-evaluated based on external changes. However, direct v-model style binding is not supported by default; state management is via `initialActive` prop and `selected` event.
