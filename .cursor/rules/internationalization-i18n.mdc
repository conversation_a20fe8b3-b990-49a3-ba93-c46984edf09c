---
description:
globs:
alwaysApply: true
---
# Internationalization (i18n) Conventions

## Overview
- Internationalization is handled by `@nuxtjs/i18n` and `vue-i18n`.
- Supported languages: English (`en`) and Spanish (`es`).
- Default language is English.

## Configuration (`nuxt.config.js`)
- The `i18n` module is configured in `nuxt.config.js` (details may be further down in the file).
- This configuration typically includes:
    - `locales`: Array of locale objects (e.g., `{ code: 'en', iso: 'en-US', file: 'en.js' }`).
    - `defaultLocale`: e.g., 'en'.
    - `vueI18n`: Custom `vue-i18n` options.
    - `strategy`: Routing strategy (e.g., `prefix_except_default` or `prefix`).
    - `lazy`: If true, translation files are lazy-loaded.
    - `langDir`: Directory for translation files (e.g., `lang/`).

## Translation Files
- Translation strings are typically stored in JavaScript or JSON files in the `lang/` directory (e.g., `lang/en.js`, `lang/es.js`).
- Example `lang/en.js`:
  ```javascript
  export default {
    welcomeMessage: 'Hello World',
    userProfile: {
      title: 'User Profile',
      greeting: 'Hello, {name}!'
    }
  }
  ```

## Usage in Components and Pages
- **Accessing translations**: `this.$t('keyName')` or `this.$t('object.nestedKey')`.
- **Interpolation**: `this.$t('userProfile.greeting', { name: 'John' })`.
- **Pluralization**: `this.$tc('itemsCount', count, { count })` (requires ICU message format in translations).
- **Switching locales**: Programmatically via `this.$i18n.setLocale('es')` or via a locale switcher component that uses `<nuxt-link :to="switchLocalePath('es')">Español</nuxt-link>`.
- **Locale-specific routing**: Use `this.localePath('/about')` for generating locale-aware links.
- **`<i18n>` block in SFCs**: For page-specific or component-specific translations that don't need to be in global files:
  ```vue
  <i18n>
  {
    "en": {
      "pageTitle": "My Special Page"
    },
    "es": {
      "pageTitle": "Mi Página Especial"
    }
  }
  </i18n>
  ```
  Access these with `this.$t('pageTitle')`.

## SEO and Headings
- Use the `head()` method in pages to set alternate language links:
  ```javascript
  head() {
    const { path } = this.$route
    const base = 'https://www.headshotpro.com'
    return {
      link: [
        { rel: 'canonical', href: base + this.localePath(path, 'en') },
        { rel: 'alternate', hreflang: 'es', href: base + this.localePath(path, 'es') },
        { rel: 'alternate', hreflang: 'en', href: base + this.localePath(path, 'en') },
        { rel: 'alternate', hreflang: 'x-default', href: base + this.localePath(path, 'en') }
      ]
    }
  }
  ```

## Price and Currency Localization
- Handled by a combination of Vuex state, `plugins/localization.js` (global mixin), and `assets/country-currency.json`.
- `userCurrency` computed property (in global mixin) determines the current currency based on user's country (from API `/user/location`) or `localStorage` preference.
- `getLocalizedPrice(priceId, ...)` method (in global mixin) retrieves package prices from Vuex, applies currency conversion, and can format the price string.
- `formatPrice(...)` method (in global mixin) formats numbers into currency strings.

## Flags
- `flag-icons/css/flag-icons.min.css` is globally imported for displaying flag icons (e.g., in a language switcher).
