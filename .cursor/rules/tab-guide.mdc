---
description: Guide for the <Tab> component (components/tab/Tab.vue), which acts as a container for <TabItem> components to form a tab navigation bar.
globs:
alwaysApply: false
---
# Component Guide: Tab

**File Path**: `components/tab/Tab.vue`
(Likely used as `<Tab>` in templates, though no current usage examples were found via grep search. Expected to be used with `<TabItem>` components as children).

## Overview
The `<Tab>` component acts as a container for a group of tab navigation items (presumably `<TabItem>` components). It provides a styled wrapper for these items.

## Template Structure (Simplified)
```html
<div class="bg-gray-200 rounded-md p-[5px]">
  <nav class="flex flex-wrap gap-1">
    <slot /> <!-- Expected to contain <TabItem> components -->
  </nav>
</div>
```

## Props
- None defined in the component script.

## Slots

- **Default Slot**:
    - Purpose: This is where `<TabItem>` components should be placed to form the tab navigation.
    - Example (Hypothetical, assuming `<TabItem>` usage):
      ```html
      <Tab>
        <TabItem :active="currentTab === 'details'" @click="currentTab = 'details'">Details</TabItem>
        <TabItem :active="currentTab === 'settings'" @click="currentTab = 'settings'">Settings</TabItem>
        <TabItem :active="currentTab === 'billing'" @click="currentTab = 'billing'">Billing</TabItem>
      </Tab>
      ```

## Events
- None directly emitted by `<Tab>` itself. Interaction is handled by child `<TabItem>` components.

## Usage Examples

*No direct usage examples were found in the codebase via grep search. The following is a hypothetical example based on its structure and intended use with `<TabItem>`.*

```vue
<template>
  <div>
    <Tab>
      <TabItem
        :active="activeTab === 'profile'"
        @click="selectTab('profile')"
      >
        Profile
      </TabItem>
      <TabItem
        :active="activeTab === 'account'"
        @click="selectTab('account')"
      >
        Account
      </TabItem>
      <TabItem
        :active="activeTab === 'notifications'"
        @click="selectTab('notifications')"
      >
        Notifications
      </TabItem>
    </Tab>

    <div class="mt-4">
      <div v-if="activeTab === 'profile'">
        <UserProfileContent />
      </div>
      <div v-if="activeTab === 'account'">
        <UserAccountSettings />
      </div>
      <div v-if="activeTab === 'notifications'">
        <NotificationPreferences />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      activeTab: 'profile' // Default active tab
    };
  },
  methods: {
    selectTab(tabName) {
      this.activeTab = tabName;
    }
  }
  // Components like UserProfileContent, etc., would be imported or globally available
};
</script>
```

## Styling
- Outer container has a light gray background (`bg-gray-200`), rounded corners (`rounded-md`), and small padding (`p-[5px]`).
- The inner `<nav>` uses flexbox (`flex flex-wrap`) with a small gap between tab items (`gap-1`).

## Best Practices
- Use `<Tab>` as a wrapper for a set of `<TabItem>` components to create a tabbed navigation interface.
- The parent component controlling the `<Tab>` and its `<TabItem>` children will be responsible for managing the active tab state and handling the content display for each tab.
- Given no current usage found, verify if this component is actively used or if there are preferred alternatives for tabbed interfaces.
