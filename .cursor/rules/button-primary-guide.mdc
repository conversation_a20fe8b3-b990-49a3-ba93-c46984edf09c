---
description: Guide for the <ButtonPrimary> component (components/button/Primary.vue), the standard call-to-action button, covering sizes, disabled state, and usage.
globs:
alwaysApply: false
---
# Component Guide: ButtonPrimary (Primary)

**File Path**: `components/button/Primary.vue`
(Typically used as `<ButtonPrimary>` in templates).

## Overview
The `<ButtonPrimary>` component is the standard call-to-action button used for primary actions across the application. It features a distinct brand color background, white text, and various sizes.

## Template Structure (Simplified Internal)
```html
<!-- Example for size = 'base' -->
<button
  aria-label="Primary Button"
  type="button"
  :disabled="isDisabled"
  :class="{ 'cursor-not-allowed opacity-40': isDisabled }"
  class="hover:bg-brand-800 focus:ring-brand-200 ... bg-brand-500 ... text-white ..."
  @click="$emit('click')"
>
  <slot />
</button>
```
- The component internally has a series of `<button>` elements, one for each `size` (`xs`, `sm`, `base`, `lg`, `xl`), controlled by `v-if`/`v-else-if`.
- Each button has specific padding and text size classes based on the `size` prop.
- Common classes include `bg-brand-500`, `text-white`, hover/focus states, and disabled styling (`cursor-not-allowed opacity-40`).

## Props

- **`size`**:
    - Type: `String`
    - Default: `'base'`
    - Options: `'xs'`, `'sm'`, `'base'`, `'lg'`, `'xl'`.
    - Purpose: Determines the padding and font size of the button, providing different visual weights.
        - `xs`: Extra small
        - `sm`: Small
        - `base`: Medium / Default
        - `lg`: Large
        - `xl`: Extra large
- **`isDisabled`**:
    - Type: `Boolean`
    - Default: `false`
    - Purpose: If `true`, the button is visually styled as disabled (opacity, cursor) and the native `disabled` attribute is set, preventing clicks.

## Slots

- **Default Slot**:
    - Purpose: The content of the button, typically text, but can also include icons or other inline elements.
    - Example:
      ```html
      <ButtonPrimary>
        Submit Application
      </ButtonPrimary>

      <ButtonPrimary size="sm">
        <IconSave class="w-4 h-4 mr-2" /> Save Changes
      </ButtonPrimary>
      ```

## Events

- **`click`**:
    - Payload: The native click event object.
    - Purpose: Emitted when the button is clicked (and not disabled).
    - Usage:
      ```html
      <ButtonPrimary @click="handleSubmit">
        Save
      </ButtonPrimary>
      ```

## Usage Examples

1.  **Basic Primary Button (Default Size)**:
    ```html
    <ButtonPrimary @click="performAction">
      Confirm Purchase
    </ButtonPrimary>
    ```

2.  **Small Primary Button, Disabled State**:
    ```html
    <ButtonPrimary size="sm" :is-disabled="formIsInvalid" @click="saveSettings">
      Save Settings
    </ButtonPrimary>
    ```
    *Source Example*: `pages/admin/hairstyles.vue` uses `<ButtonPrimary size="sm" @click="openAddNewModal">Add New</ButtonPrimary>`.

3.  **Large Primary Button with Icon Inside**:
    ```html
    <ButtonPrimary size="lg" @click="startUpload">
      <IconUpload class="w-6 h-6 mr-3" />
      Upload Files
    </ButtonPrimary>
    ```

4.  **Primary Button with Custom Tailwind Classes (e.g., full width)**:
    ```html
    <ButtonPrimary class="w-full mt-4" @click="proceedToNextStep">
      Continue
    </ButtonPrimary>
    ```
    *Source Example*: `pages/profile/trustpilot.vue` uses `<ButtonPrimary class="w-full" @click="submitRequest">`.

## Styling
- Base style is `bg-brand-500` (a specific shade of teal/blue based on `tailwind.config.js`) with `text-white`.
- Hover state changes background to `hover:bg-brand-800` (a darker shade).
- Focus state applies `focus:ring-brand-200`.
- Disabled state applies `cursor-not-allowed opacity-40`.
- Padding and font size are determined by the `size` prop.
- Additional layout or spacing classes (like `w-full`, `mt-4`) can be added via the `class` attribute on the component.

## Best Practices
- Use `<ButtonPrimary>` for the main positive action in a given context (e.g., form submission, confirmation, starting a process).
- Avoid using too many primary buttons in close proximity to prevent diluting their visual importance.
- Utilize the `size` prop to match the button's visual hierarchy with surrounding elements.
- Clearly indicate loading states within or around the button if the click action is asynchronous (e.g., by changing slot content or using a separate loading indicator).
- Use the `isDisabled` prop to prevent actions when pre-conditions are not met (e.g., invalid form).
