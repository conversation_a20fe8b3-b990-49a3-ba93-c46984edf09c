document.addEventListener("DOMContentLoaded", function () {
    // Get the script tag that loaded this script
    const scriptTag = document.querySelector('script[src*="team-page.js"][data-id][data-team-id]');

    if (!scriptTag) {
        return;
    }

    // Read the data attributes
    const dataId = scriptTag.getAttribute('data-id');
    const dataTeamId = scriptTag.getAttribute('data-team-id');
    const dataDev = scriptTag.hasAttribute('data-dev');
    const dataStyling = scriptTag.getAttribute('data-style');

    // Find the target element by data-id
    const targetElement = document.getElementById(dataId);

    if (!targetElement || !dataTeamId) {
        return;
    }

    // Create the iframe
    const iframe = document.createElement('iframe');
    const url = dataDev ? 'http://localhost:3000' : 'https://headshotpro.com';
    const queryParams = dataStyling ? '?style=' + dataStyling : '';
    iframe.src = `${url}/team-page/${dataTeamId}${queryParams}`;
    iframe.style.width = '100%';
    iframe.style.border = 'none';

    // Append the iframe to the target element
    targetElement.appendChild(iframe);

    // Create the <a> tag
    const link = document.createElement('a');
    link.href = `https://headshotpro.com/?ref=team-page-${dataTeamId}`;
    link.textContent = 'Team page created using HeadshotPro | Professional Headshots for Teams';
    link.style.display = 'block';
    link.style.textAlign = 'center';
    link.style.marginTop = '10px';
    link.style.fontSize = '12px';
    link.style.color = '#787878';
    link.style.textDecoration = 'none';
    link.target = '_blank';

    // Append the <a> tag after the iframe
    targetElement.appendChild(link);

    // Function to resize the iframe
    function resizeIframe(height) {
        iframe.style.height = height + 'px';
    }

    // Listen for messages from the iframe
    window.addEventListener('message', function(event) {
        if (event.data.type === 'GENERATOR_EVENT') {
            resizeIframe(event.data.payload.height);
        }
    });
});
