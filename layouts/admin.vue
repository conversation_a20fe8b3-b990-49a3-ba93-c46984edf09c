<template>
  <!-- <div class="">
    <template v-if="isLoading">
      <div class="flex min-h-screen w-full bg-gray-50 text-black">
        <div class="flex h-screen min-h-screen w-full flex-col items-center justify-center space-y-4">
          <Logo class="w-[90px] text-black" />
          <LoadingSpinner class="mx-auto" title="Logging you in..." />
        </div>
      </div>
    </template>
    <template v-else>
      <div class="top-0 left-0 z-10 flex w-full items-center justify-start space-x-8 border-b border-gray-100 bg-white p-2 px-8">
        <nuxt-link to="/app">
          <Logo class="w-[160px] text-white" />
        </nuxt-link>
        <div>
          <ul class="flex items-center justify-end space-x-4">
            <li v-for="item in navigation" :key="item.href" class="text-sm text-gray-700 hover:font-bold">
              <nuxt-link :to="item.href">
                {{ item.title }}
              </nuxt-link>
            </li>
          </ul>
        </div>
      </div>
      <Nuxt class="min-h-screen w-full bg-gray-50 text-black" />
    </template>
  </div> -->
  <div>
    <template v-if="isLoading">
      <div class="flex min-h-screen w-full bg-gray-50 text-black">
        <div class="flex h-screen min-h-screen w-full flex-col items-center justify-center space-y-4">
          <Logo class="w-[90px] text-black" />
          <LoadingSpinner class="mx-auto" title="Logging you in..." />
        </div>
      </div>
    </template>
    <div v-else class="flex-col md:flex-row flex">
      <div class="flex md:hidden w-full bg-white border-b border-black/10 p-1 items-center justify-center">
        <Logo class="w-[100px] text-white" />
      </div>
      <div class="hidden md:block w-[200px] bg-white border-r border-black/10 sticky h-screen max-h-screen">
        <div class="p-4 flex flex-col space-y-4">
          <div>
            <nuxt-link to="/admin">
              <Logo class="w-[120px] text-white" />
            </nuxt-link>
          </div>
          <div>
            <ul class="flex flex-col justify-start items-start space-y-4">
              <li v-for="item in navigation" :key="item.href" class="text-sm text-gray-700 hover:font-bold">
                <nuxt-link :to="item.href">
                  {{ item.title }}
                </nuxt-link>
              </li>
            </ul>
          </div>
          <div />
        </div>
      </div>
      <div class="w-full">
        <Nuxt class="min-h-screen max-h-screen overflow-y-scroll w-full bg-gray-50 text-black" />
        <portal-target name="modal" />
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data () {
    return {
      unsubscribeAuthChange: () => {},
      isLoading: true,
      navigationItems: [
        { level: ['admin', 'support', 'dev', 'marketer', 'sales'], title: 'Feedback', href: '/admin/feedback' },
        { level: ['admin', 'support', 'dev', 'marketer', 'sales'], title: 'Blog', href: '/admin/blog' },
        { level: ['admin', 'support', 'dev', 'marketer', 'sales'], title: 'Changelog', href: '/admin/changelog' },
        { level: ['admin', 'support', 'dev', 'marketer', 'sales'], title: 'Models', href: '/admin/model' },
        { level: ['admin', 'support', 'dev', 'marketer', 'sales'], title: 'Coupons', href: '/admin/coupons' },
        { level: ['admin', 'support', 'dev', 'marketer', 'sales'], title: 'Giftcards', href: '/admin/giftcard' },
        { level: ['admin', 'dev', 'marketer', 'sales'], title: 'Reviews', href: '/admin/reviews' },
        { level: ['admin', 'dev', 'marketer', 'sales'], title: 'Emails', href: '/admin/email' },
        { level: ['admin', 'support', 'dev', 'sales'], title: 'Refund', href: '/admin/refund' },
        { level: ['admin', 'support', 'dev', 'sales'], title: 'Support Requests', href: '/admin/support' },
        { level: ['admin', 'dev'], title: 'Hairstyles', href: '/admin/hairstyles' },
        { level: ['admin', 'dev'], title: 'Emotions', href: '/admin/emotions' },
        { level: ['admin', 'dev', 'sales'], title: 'Clothing', href: '/admin/clothing' },
        { level: ['admin', 'dev', 'sales'], title: 'Styles', href: '/admin/style' },
        { level: ['admin', 'dev', 'sales'], title: 'Catalog', href: '/admin/catalog' },
        { level: ['admin', 'dev', 'marketer'], title: 'Parameters', href: '/admin/parameters' },
        { level: ['admin', 'dev', 'support', 'sales'], title: 'Users', href: '/admin/user' },
        { level: ['admin', 'dev', 'sales'], title: 'Referral Program', href: '/admin/referral' },
        { level: ['admin', 'dev', 'support', 'sales'], title: 'Organization', href: '/admin/organization' },
        { level: ['admin', 'dev', 'sales'], title: 'Transactions', href: '/admin/transactions' },
        { level: ['admin', 'dev', 'sales'], title: 'Invoices', href: '/admin/invoices' },
        { level: ['admin', 'dev', 'marketer', 'adwords', 'sales'], title: 'Uploads', href: '/admin/upload' },
        { level: ['admin', 'dev', 'marketer', 'adwords', 'sales'], title: 'Playground', href: '/admin/playground' }
      ]
    }
  },
  computed: {
    navigation () {
      const { adminRole } = this
      return this.navigationItems.filter((item) => {
        return item.level.includes(adminRole)
      })
    }
  },
  mounted () {
    this.initPageLoad()
    // await this.$fire.authReady()
    this.unsubscribeAuthChange = this.$fire.auth.onAuthStateChanged(async (user) => {
      if (user) {
        await this.setupUserDetails(user)
        const response = await this.$axios.$get('/auth/admin/check')
        const { isAdmin } = response
        if (isAdmin === true) {
          this.isLoading = false
        } else {
          this.$router.push('/')
        }
      } else {
        this.$router.push('/auth/login')
      }
    })
  },
  beforeDestroy () {
    this.unsubscribeAuthChange()
  }
}
</script>

<style scoped>
.nuxt-link-active {
  font-weight: bold;
}
</style>
