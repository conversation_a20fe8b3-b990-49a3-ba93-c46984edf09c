<template>
  <div class="">
    <MaintenanceWrapper>
      <div class="print:hidden w-full">
        <template v-if="isLoading">
          <div class="flex min-h-screen w-full bg-gray-100 text-black items-center justify-center">
            <Card class="max-w-[300px] w-full">
              <div class="flex items-center justify-center flex-col space-y-2">
                <Logo class="w-[150px] text-black" />
                <LoadingSpinner class="mx-auto" :title="loadingMessage" />
              </div>
            </Card>
          </div>
        </template>
        <template v-else>
          <!-- <Header /> -->
          <Nuxt class="min-h-screen w-full bg-gray-50 text-black" />
          <portal-target name="modal" />
        </template>
        <SupportModal
          v-if="showSupportModal"
          :version="version"
          @closeModal="showSupportModal = false"
        />
        <SupportFloat />
      </div>
      <div class="hidden print:block w-full">
        <portal-target name="print" />
      </div>
    </MaintenanceWrapper>
  </div>
</template>
<script>
export default {
  data () {
    return {
      unsubscribeAuthChange: () => {},
      isLoading: true,
      axiosInterceptor: () => {}
    }
  },
  computed: {
    loadingMessage () {
      const defaultMessage = 'Logging you in...'
      if (this.$route.query.loadingMessage) {
        if (this.$route.query.loadingMessage === 'adjusting_currency') {
          return 'Adjusting currency...'
        }
        return defaultMessage
      }
      return defaultMessage
    },

    showSupportModal: {
      get () {
        return this.$store.state.modal.support
      },
      set (value) {
        this.$store.commit('SET_MODAL', { name: 'support', value })
      }
    },
    version () {
      // If checkout or /app/add use old otherwise new
      return this.$route.path.includes('checkout') || this.$route.path.includes('app/add') ? 'old' : 'new'
    }
  },
  mounted () {
    this.initPageLoad()

    // Add both request and response interceptors
    this.axiosInterceptor = {
      request: this.$axios.interceptors.request.use(async (config) => {
        if (this.isLoggedIn) {
          const firebaseAuthTokenRefreshedAt = this.$store.state.user.firebaseAuthTokenRefreshedAt
          if (!firebaseAuthTokenRefreshedAt || (new Date() - firebaseAuthTokenRefreshedAt) > 1000 * 60 * 5) {
            const idToken = await this.$fire.auth.currentUser.getIdToken(true)
            this.$axios.defaults.headers.common.idToken = idToken
            this.$store.commit('user/SET_FIREBASE_AUTH_TOKEN_REFRESHED_AT', new Date())
          }
        }
        return config
      }),
      response: this.$axios.interceptors.response.use(
        (response) => {
          return response
        },
        async (error) => {
          // Check if we have a failed request
          if (error.config && error.response?.status === 401 && error.response?.data?.message?.includes('Incorrect token')) {
            // Add retry count to config if it doesn't exist
            error.config.__retryCount = error.config.__retryCount || 0

            // Check if we've maxed out retry attempts
            if (error.config.__retryCount >= 2) { // 2 retries = 3 total attempts
              this.logout()
              return Promise.reject(error)
            }

            // Increment retry count
            error.config.__retryCount += 1

            console.log(`refreshing token (attempt ${error.config.__retryCount} of 2)`)

            // Refresh the token
            const idToken = await this.$fire.auth.currentUser.getIdToken(true)
            this.$axios.defaults.headers.common.idToken = idToken
            this.$store.commit('user/SET_FIREBASE_AUTH_TOKEN_REFRESHED_AT', new Date())

            // Clone the original request config and retry with new token
            const newConfig = { ...error.config }
            newConfig.headers.idToken = idToken

            // Retry the request with new token
            return this.$axios(newConfig)
          }

          // If we can't retry, reject with the original error
          return Promise.reject(error)
        }
      )
    }

    this.isLoading = true
    // await this.$fire.authReady()
    this.unsubscribeAuthChange = this.$fire.auth.onAuthStateChanged(async (user) => {
      if (user) {
        await this.setupUserDetails(user)
        this.isLoading = false
      } else {
        this.$router.push({ path: '/auth/login', query: { redirect: encodeURIComponent(this.$route.fullPath) } })
        this.isLoading = false
      }
    })
    // }
  },
  beforeDestroy () {
    this.unsubscribeAuthChange()
    if (this.axiosInterceptor) {
      this.$axios.interceptors.request.eject(this.axiosInterceptor.request)
      this.$axios.interceptors.response.eject(this.axiosInterceptor.response)
    }
  }
}
</script>

<style scoped>
.checkout-modal-open {
  overflow: hidden !important;
}
</style>
