<template>
  <Nuxt class="bg-gray-50" />
</template>
<script>
export default {
  mounted () {
    this.initPageLoad()
  },
  methods: {
    handleReferral () {
      const referralCode = this.$route.query.ref
      // const campaign = this.$route.query.campaign

      if (referralCode) {
        this.$cookies.set('hp_referred_by', referralCode, {
          path: '/',
          maxAge: 60 * 60 * 24 * 45
        })
      }
    }
  }
}
</script>
