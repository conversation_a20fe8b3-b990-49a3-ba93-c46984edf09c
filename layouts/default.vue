<template>
  <div
    class="min-h-screen w-full bg-white"
    :class="{
      'iframe-site': isIframe,
    }"
  >
    <!-- <div class="w-full bg-orange-500 text-white text-sm text-center p-2">
      We’re currently experiencing downtime due to a global Google outage. Please try again in a few hours.
    </div> -->
    <Nuxt />
    <portal-target name="modal" />
    <SupportModal v-if="showSupportModal" @closeModal="showSupportModal = false" />
    <SupportFloat />
  </div>
</template>

<script>
export default {
  data () {
    return {
      unsubscribeAuthChange: () => {}
    }
  },
  computed: {
    showSupportModal: {
      get () {
        return this.$store.state.modal.support
      },
      set (value) {
        this.$store.commit('SET_MODAL', { name: 'support', value })
      }
    },
    maxPercentage: {
      get () {
        return this.$store.state.maxPercentage
      },
      set (value) {
        this.$store.commit('SET_MAX_PERCENTAGE', value)
      }
    },
    maxPixels: {
      get () {
        return this.$store.state.maxPixels
      },
      set (value) {
        this.$store.commit('SET_MAX_PIXELS', value)
      }
    }
  },
  watch: {
    '$route' (to, from) {
      // Logic to execute before route changes
      this.handleRouteChange()
    }
  },
  created () {
    if (!this.$cookies.get('hpai_version')) {
      this.$cookies.set('hpai_version', this.randLetter(), {
        path: '/',
        maxAge: 60 * 60 * 24 * 365
      })
    }
  },
  mounted () {
    // this.initParityPricing()
    this.initPageLoad()

    this.unsubscribeAuthChange = this.$fire.auth.onAuthStateChanged(async (user) => {
      if (user) {
        await this.setupUserDetails(user)
      }
    })
    // For Posthog scroll tracking
    window.addEventListener('scroll', this.handleScroll)
    window.addEventListener('beforeunload', this.handleBeforeUnload) // Add this line
  },
  beforeDestroy () {
    this.unsubscribeAuthChange()
    // For Posthog scroll tracking
    window.removeEventListener('scroll', this.handleScroll)
  },
  methods: {
    randLetter () {
      const letters = ['a', 'b']
      const letter = letters[Math.floor(Math.random() * letters.length)]
      return letter
    },
    handleScroll () {
      // For Posthog scroll tracking
      if (!process.client) { return }
      const { maxPercentage, maxPixels } = this
      const lastPercentage = Math.min(1, (window.innerHeight + window.pageYOffset) / document.body.offsetHeight)
      const lastPixels = window.innerHeight + window.pageYOffset
      if (lastPercentage > maxPercentage.current) {
        maxPercentage.current = lastPercentage
      }

      if (lastPixels > maxPixels.current) {
        maxPixels.current = lastPixels
      }
    },
    handleRouteChange () {
      this.captureScrollData()
    },
    handleBeforeUnload () {
      // Capture the event when a user leaves the website
      this.captureScrollData()
    },
    captureScrollData () {
      // Assuming maxPercentage and maxPixels are reactive data properties of this component
      const { maxPercentage, maxPixels } = this

      // Calculate last scroll positions
      const lastPercentage = Math.min(1, (window.innerHeight + window.pageYOffset) / document.body.offsetHeight)
      const lastPixels = window.innerHeight + window.pageYOffset

      // Posthog capture event
      this.$posthog.capture('$user_interaction:left_page', {
        'max scroll percentage': maxPercentage,
        'max scroll pixels': maxPixels,
        'last scroll percentage': lastPercentage,
        'last scroll pixels': lastPixels,
        scrolled: maxPixels > 0
      })
    }
  }
}
</script>

<style>
/* @font-face {
  font-family: "Plus Jakarta Sans";
  src: url("/fonts/PlusJakartaSans/PlusJakartaSans-Bold.woff");
  font-weight: 700;
  font-display: swap;
}
@font-face {
  font-family: "Plus Jakarta Sans";
  src: url("/fonts/PlusJakartaSans/PlusJakartaSans-Medium.woff");
  font-weight: 600;
  font-display: swap;
}
@font-face {
  font-family: "Plus Jakarta Sans";
  src: url("/fonts/PlusJakartaSans/PlusJakartaSans-Regular.woff");
  font-weight: 500;
  font-display: swap;
} */

@font-face {
  font-family: "Plus Jakarta Sans";
  src: url("/fonts/PlusJakartaSans/PlusJakartaSans-Regular.woff2");
  font-weight: 400;
  font-display: swap;
}

@font-face {
  font-family: "Plus Jakarta Sans";
  src: url("/fonts/PlusJakartaSans/PlusJakartaSans-Medium.woff2");
  font-weight: 500;
  font-display: swap;
}

@font-face {
  font-family: "Plus Jakarta Sans";
  src: url("/fonts/PlusJakartaSans/PlusJakartaSans-SemiBold.woff2");
  font-weight: 600;
  font-display: swap;
}

@font-face {
  font-family: "Plus Jakarta Sans";
  src: url("/fonts/PlusJakartaSans/PlusJakartaSans-Bold.woff2");
  font-weight: 700;
  font-display: swap;
}

@font-face {
  font-family: "Plus Jakarta Sans";
  src: url("/fonts/PlusJakartaSans/PlusJakartaSans-ExtraBold.woff2");
  font-weight: 800;
  font-display: swap;
}

@font-face {
  font-family: "Architects Daughter";
  src: url("/fonts/ArchitectDaughter/architectsdaughter.woff2");
  font-weight: 800;
  font-display: swap;
}

</style>
