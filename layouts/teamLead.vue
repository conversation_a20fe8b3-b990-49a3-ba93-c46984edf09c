<template>
  <div class="flex h-screen relative bg-[#FAFAFA]">
    <template v-if="isLoading">
      <div class="flex min-h-screen w-full bg-gray-50 text-black">
        <div class="flex h-screen min-h-screen w-full flex-col items-center justify-center space-y-4">
          <Logo class="w-[150px] text-black" />
          <LoadingSpinner class="mx-auto" title="Logging you in..." />
        </div>
      </div>
    </template>
    <template v-else>
      <div class="print:hidden flex flex-col w-full lg:flex-row">
        <HeaderApp />
        <div class="h-screen w-full overflow-y-auto scroll-container">
          <div class="mx-auto max-w-7xl pt-20 pb-20 px-4 sm:px-6 md:pb-8 lg:px-8 lg:py-16">
            <Nuxt class="w-full" />
          </div>
        </div>
        <SupportModal
          v-if="showSupportModal"
          version="new"
          @closeModal="showSupportModal = false"
        />
        <portal-target name="modal" />
      </div>
      <div class="hidden print:block w-full">
        <portal-target name="print" />
      </div>
    </template>
    <SupportFloat />
  </div>
</template>

<script>
export default {
  data () {
    return {
      unsubscribeAuthChange: () => {},
      isLoading: true,
      axiosInterceptor: () => {}
    }
  },
  computed: {
    showSupportModal: {
      get () {
        return this.$store.state.modal.support
      },
      set (value) {
        this.$store.commit('SET_MODAL', { name: 'support', value })
      }
    },
    isTeamLead () {
      return this.$store.state?.user?.role === 'TeamLead' || null
    }
  },
  mounted () {
    this.initPageLoad()

    this.axiosInterceptor = {
      request: this.$axios.interceptors.request.use(async (config) => {
        if (this.isLoggedIn) {
          const firebaseAuthTokenRefreshedAt = this.$store.state.user.firebaseAuthTokenRefreshedAt
          if (!firebaseAuthTokenRefreshedAt || (new Date() - firebaseAuthTokenRefreshedAt) > 1000 * 60 * 5) {
            const idToken = await this.$fire.auth.currentUser.getIdToken(true)
            this.$axios.defaults.headers.common.idToken = idToken
            this.$store.commit('user/SET_FIREBASE_AUTH_TOKEN_REFRESHED_AT', new Date())
          }
        }
        return config
      }),
      response: this.$axios.interceptors.response.use(
        (response) => {
          return response
        },
        async (error) => {
          // Check if we have a failed request
          if (error.config && error.response?.status === 401 && error.response?.data?.message?.includes('Incorrect token')) {
            // Add retry count to config if it doesn't exist
            error.config.__retryCount = error.config.__retryCount || 0

            // Check if we've maxed out retry attempts
            if (error.config.__retryCount >= 2) { // 2 retries = 3 total attempts
              this.logout()
              return Promise.reject(error)
            }

            // Increment retry count
            error.config.__retryCount += 1

            console.log(`refreshing token (attempt ${error.config.__retryCount} of 2)`)

            // Refresh the token
            const idToken = await this.$fire.auth.currentUser.getIdToken(true)
            this.$axios.defaults.headers.common.idToken = idToken
            this.$store.commit('user/SET_FIREBASE_AUTH_TOKEN_REFRESHED_AT', new Date())

            // Clone the original request config and retry with new token
            const newConfig = { ...error.config }
            newConfig.headers.idToken = idToken

            // Retry the request with new token
            return this.$axios(newConfig)
          }

          // If we can't retry, reject with the original error
          return Promise.reject(error)
        }
      )
    }

    this.isLoading = true
    this.unsubscribeAuthChange = this.$fire.auth.onAuthStateChanged(async (user) => {
      if (user) {
        await this.setupUserDetails(user)
        if (!this.$store.state?.organization?.organization) {
          this.$router.push('/app/admin/team/new')
        } else if (this.$store.state.user?.role !== 'TeamLead') {
          this.$toast.error('You are not authorized to access this page')
          this.$router.push('/app')
          this.isLoading = false
        }
        this.isLoading = false
      } else {
        this.$router.push('/auth/login?redirect=' + encodeURIComponent(this.$route.fullPath))
        this.isLoading = false
      }
    })
  },
  beforeDestroy () {
    this.unsubscribeAuthChange()

    if (this.axiosInterceptor) {
      this.$axios.interceptors.request.eject(this.axiosInterceptor.request)
      this.$axios.interceptors.response.eject(this.axiosInterceptor.response)
    }
  }
}
</script>
