/*! @license Firebase v3.7.5
    Build: 3.7.5-rc.1
    Terms: https://firebase.google.com/terms/ */
var firebase = null; (function() { /*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");},ca=ba(this),l=this||self,da=function(a,b,c){return a.call.apply(a.bind,arguments)},ea=function(a,b,c){if(!a)throw Error();if(2<arguments.length){var d=
Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}},m=function(a,b,c){m=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?da:ea;return m.apply(null,arguments)},p=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}},q=function(a,
b){function c(){}c.prototype=b.prototype;a.ia=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.base=function(d,e,g){for(var k=Array(arguments.length-2),f=2;f<arguments.length;f++)k[f-2]=arguments[f];return b.prototype[e].apply(d,k)}};function r(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,r);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));void 0!==b&&(this.cause=b)}q(r,Error);r.prototype.name="CustomError";function t(a,b){a=a.split("%s");for(var c="",d=a.length-1,e=0;e<d;e++)c+=a[e]+(e<b.length?b[e]:"%s");r.call(this,c+a[d])}q(t,r);t.prototype.name="AssertionError";function u(a,b,c,d){var e="Assertion failed";if(c){e+=": "+c;var g=d}else a&&(e+=": "+a,g=b);throw new t(""+e,g||[]);}
var w=function(a,b,c){a||u("",null,b,Array.prototype.slice.call(arguments,2))},x=function(a,b,c){if("function"!==typeof a){var d=typeof a;d="object"!=d?d:a?Array.isArray(a)?"array":d:"null";u("Expected function but got %s: %s.",[d,a],b,Array.prototype.slice.call(arguments,2))}};var y=function(a,b){this.ba=100;this.X=a;this.da=b;this.F=0;this.D=null};y.prototype.get=function(){if(0<this.F){this.F--;var a=this.D;this.D=a.next;a.next=null}else a=this.X();return a};y.prototype.put=function(a){this.da(a);this.F<this.ba&&(this.F++,a.next=this.D,this.D=a)};function z(){var a=l.navigator;return a&&(a=a.userAgent)?a:""}function B(a){return-1!=z().indexOf(a)};function C(){return B("iPhone")&&!B("iPod")&&!B("iPad")};B("Opera");B("Trident")||B("MSIE");B("Edge");!B("Gecko")||-1!=z().toLowerCase().indexOf("webkit")&&!B("Edge")||B("Trident")||B("MSIE")||B("Edge");-1!=z().toLowerCase().indexOf("webkit")&&!B("Edge")&&B("Mobile");B("Macintosh");B("Windows");B("Linux")||B("CrOS");var D=l.navigator||null;D&&(D.appVersion||"").indexOf("X11");B("Android");C();B("iPad");B("iPod");C()||B("iPad")||B("iPod");z().toLowerCase().indexOf("kaios");var E=function(){};var fa=function(){var a=document;var b="IFRAME";"application/xhtml+xml"===a.contentType&&(b=b.toLowerCase());return a.createElement(b)};var F,ha=function(){var a=l.MessageChannel;"undefined"===typeof a&&"undefined"!==typeof window&&window.postMessage&&window.addEventListener&&!B("Presto")&&(a=function(){var e=fa();e.style.display="none";document.documentElement.appendChild(e);var g=e.contentWindow;e=g.document;e.open();e.close();var k="callImmediate"+Math.random(),f="file:"==g.location.protocol?"*":g.location.protocol+"//"+g.location.host;e=m(function(h){if(("*"==f||h.origin==f)&&h.data==k)this.port1.onmessage()},this);g.addEventListener("message",
e,!1);this.port1={};this.port2={postMessage:function(){g.postMessage(k,f)}}});if("undefined"!==typeof a&&!B("Trident")&&!B("MSIE")){var b=new a,c={},d=c;b.port1.onmessage=function(){if(void 0!==c.next){c=c.next;var e=c.P;c.P=null;e()}};return function(e){d.next={P:e};d=d.next;b.port2.postMessage(0)}}return function(e){l.setTimeout(e,0)}};function G(a){l.setTimeout(function(){throw a;},0)};var H=function(){this.G=this.o=null};H.prototype.add=function(a,b){var c=ia.get();c.set(a,b);this.G?this.G.next=c:(w(!this.o),this.o=c);this.G=c};H.prototype.remove=function(){var a=null;this.o&&(a=this.o,this.o=this.o.next,this.o||(this.G=null),a.next=null);return a};var ia=new y(function(){return new I},function(a){return a.reset()}),I=function(){this.next=this.scope=this.I=null};I.prototype.set=function(a,b){this.I=a;this.scope=b;this.next=null};
I.prototype.reset=function(){this.next=this.scope=this.I=null};var J,K=!1,ja=new H,L=function(a,b){J||ka();K||(J(),K=!0);ja.add(a,b)},ka=function(){if(l.Promise&&l.Promise.resolve){var a=l.Promise.resolve(void 0);J=function(){a.then(la)}}else J=function(){var b=la;"function"!==typeof l.setImmediate||l.Window&&l.Window.prototype&&!B("Edge")&&l.Window.prototype.setImmediate==l.setImmediate?(F||(F=ha()),F(b)):l.setImmediate(b)}},la=function(){for(var a;a=ja.remove();){try{a.I.call(a.scope)}catch(b){G(b)}ia.put(a)}K=!1};var O=function(a,b){this.g=0;this.V=void 0;this.s=this.i=this.m=null;this.B=this.H=!1;if(a!=E)try{var c=this;a.call(b,function(d){M(c,2,d)},function(d){if(!(d instanceof N))try{if(d instanceof Error)throw d;throw Error("Promise rejected.");}catch(e){}M(c,3,d)})}catch(d){M(this,3,d)}},ma=function(){this.next=this.context=this.u=this.l=this.child=null;this.v=!1};ma.prototype.reset=function(){this.context=this.u=this.l=this.child=null;this.v=!1};
var na=new y(function(){return new ma},function(a){a.reset()}),oa=function(a,b,c){var d=na.get();d.l=a;d.u=b;d.context=c;return d},qa=function(a,b,c){pa(a,b,c,null)||L(p(b,a))};O.prototype.then=function(a,b,c){null!=a&&x(a,"opt_onFulfilled should be a function.");null!=b&&x(b,"opt_onRejected should be a function. Did you pass opt_context as the second argument instead of the third?");return ra(this,"function"===typeof a?a:null,"function"===typeof b?b:null,c)};O.prototype.$goog_Thenable=!0;
O.prototype.W=function(a,b){return ra(this,null,a,b)};O.prototype.catch=O.prototype.W;O.prototype.cancel=function(a){if(0==this.g){var b=new N(a);L(function(){sa(this,b)},this)}};
var sa=function(a,b){if(0==a.g)if(a.m){var c=a.m;if(c.i){for(var d=0,e=null,g=null,k=c.i;k&&(k.v||(d++,k.child==a&&(e=k),!(e&&1<d)));k=k.next)e||(g=k);e&&(0==c.g&&1==d?sa(c,b):(g?(d=g,w(c.i),w(null!=d),d.next==c.s&&(c.s=d),d.next=d.next.next):ta(c),ua(c,e,3,b)))}a.m=null}else M(a,3,b)},wa=function(a,b){a.i||2!=a.g&&3!=a.g||va(a);w(null!=b.l);a.s?a.s.next=b:a.i=b;a.s=b},ra=function(a,b,c,d){var e=oa(null,null,null);e.child=new O(function(g,k){e.l=b?function(f){try{var h=b.call(d,f);g(h)}catch(n){k(n)}}:
g;e.u=c?function(f){try{var h=c.call(d,f);void 0===h&&f instanceof N?k(f):g(h)}catch(n){k(n)}}:k});e.child.m=a;wa(a,e);return e.child};O.prototype.fa=function(a){w(1==this.g);this.g=0;M(this,2,a)};O.prototype.ga=function(a){w(1==this.g);this.g=0;M(this,3,a)};
var M=function(a,b,c){0==a.g&&(a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself")),a.g=1,pa(c,a.fa,a.ga,a)||(a.V=c,a.g=b,a.m=null,va(a),3!=b||c instanceof N||xa(a,c)))},pa=function(a,b,c,d){if(a instanceof O)return null!=b&&x(b,"opt_onFulfilled should be a function."),null!=c&&x(c,"opt_onRejected should be a function. Did you pass opt_context as the second argument instead of the third?"),wa(a,oa(b||E,c||null,d)),!0;if(a)try{var e=!!a.$goog_Thenable}catch(k){e=!1}else e=!1;if(e)return a.then(b,
c,d),!0;e=typeof a;if("object"==e&&null!=a||"function"==e)try{var g=a.then;if("function"===typeof g)return ya(a,g,b,c,d),!0}catch(k){return c.call(d,k),!0}return!1},ya=function(a,b,c,d,e){var g=!1,k=function(h){g||(g=!0,c.call(e,h))},f=function(h){g||(g=!0,d.call(e,h))};try{b.call(a,k,f)}catch(h){f(h)}},va=function(a){a.H||(a.H=!0,L(a.Y,a))},ta=function(a){var b=null;a.i&&(b=a.i,a.i=b.next,b.next=null);a.i||(a.s=null);null!=b&&w(null!=b.l);return b};
O.prototype.Y=function(){for(var a;a=ta(this);)ua(this,a,this.g,this.V);this.H=!1};var ua=function(a,b,c,d){if(3==c&&b.u&&!b.v)for(;a&&a.B;a=a.m)a.B=!1;if(b.child)b.child.m=null,za(b,c,d);else try{b.v?b.l.call(b.context):za(b,c,d)}catch(e){Aa.call(null,e)}na.put(b)},za=function(a,b,c){2==b?a.l.call(a.context,c):a.u&&a.u.call(a.context,c)},xa=function(a,b){a.B=!0;L(function(){a.B&&Aa.call(null,b)})},Aa=G,N=function(a){r.call(this,a)};q(N,r);N.prototype.name="cancel";O.all=function(a){return new O(function(b,c){var d=a.length,e=[];if(d)for(var g=function(n,v){d--;e[n]=v;0==d&&b(e)},k=function(n){c(n)},f=0,h;f<a.length;f++)h=a[f],qa(h,p(g,f),k);else b(e)})};O.resolve=function(a){if(a instanceof O)return a;var b=new O(E);M(b,2,a);return b};O.reject=function(a){return new O(function(b,c){c(a)})};O.prototype["catch"]=O.prototype.W;var P=O;"undefined"!==typeof Promise&&(P=Promise);function Q(a,b){if(!(b instanceof Object))return b;switch(b.constructor){case Date:return new Date(b.getTime());case Object:void 0===a&&(a={});break;case Array:a=[];break;default:return b}for(var c in b)b.hasOwnProperty(c)&&(a[c]=Q(a[c],b[c]));return a};var Ba=Error.captureStackTrace,S=function(a,b){this.code=a;this.message=b;if(Ba)Ba(this,R.prototype.create);else{var c=Error.apply(this,arguments);this.name="FirebaseError";Object.defineProperty(this,"stack",{get:function(){return c.stack}})}};S.prototype=Object.create(Error.prototype);S.prototype.constructor=S;S.prototype.name="FirebaseError";var R=function(a,b,c){this.service=a;this.ea=b;this.errors=c;this.pattern=/\{\$([^}]+)}/g};
R.prototype.create=function(a,b){void 0===b&&(b={});var c=this.errors[a];a=this.service+"/"+a;c=void 0===c?"Error":c.replace(this.pattern,function(e,g){e=b[g];return void 0!==e?e.toString():"<"+g+"?>"});c=this.ea+": "+c+" ("+a+").";c=new S(a,c);for(var d in b)b.hasOwnProperty(d)&&"_"!==d.slice(-1)&&(c[d]=b[d]);return c};var Ca=P;function Da(a,b){a=new T(a,b);return a.subscribe.bind(a)}var T=function(a,b){var c=this;this.h=[];this.U=0;this.task=Ca.resolve();this.A=!1;this.K=b;this.task.then(function(){a(c)}).catch(function(d){c.error(d)})};T.prototype.next=function(a){U(this,function(b){b.next(a)})};T.prototype.error=function(a){U(this,function(b){b.error(a)});this.close(a)};T.prototype.complete=function(){U(this,function(a){a.complete()});this.close()};
T.prototype.subscribe=function(a,b,c){var d=this;if(void 0===a&&void 0===b&&void 0===c)throw Error("Missing Observer.");var e=Ea(a)?a:{next:a,error:b,complete:c};void 0===e.next&&(e.next=V);void 0===e.error&&(e.error=V);void 0===e.complete&&(e.complete=V);a=this.ha.bind(this,this.h.length);this.A&&this.task.then(function(){try{d.R?e.error(d.R):e.complete()}catch(g){}});this.h.push(e);return a};
T.prototype.ha=function(a){void 0!==this.h&&void 0!==this.h[a]&&(delete this.h[a],--this.U,0===this.U&&void 0!==this.K&&this.K(this))};var U=function(a,b){if(!a.A)for(var c=0;c<a.h.length;c++)Fa(a,c,b)},Fa=function(a,b,c){a.task.then(function(){if(void 0!==a.h&&void 0!==a.h[b])try{c(a.h[b])}catch(d){"undefined"!==typeof console&&console.error&&console.error(d)}})};T.prototype.close=function(a){var b=this;this.A||(this.A=!0,void 0!==a&&(this.R=a),this.task.then(function(){b.h=void 0;b.K=void 0}))};
function Ea(a){if("object"!==typeof a||null===a)return!1;var b=["next","error","complete"];var c="undefined"!=typeof Symbol&&Symbol.iterator&&b[Symbol.iterator];b=c?c.call(b):{next:aa(b)};for(c=b.next();!c.done;c=b.next())if(c=c.value,c in a&&"function"===typeof a[c])return!0;return!1}function V(){};var W=P,X=function(a,b,c){var d=this;this.S=c;this.T=!1;this.j={};this.J=b;this.M=Q(void 0,a);a="serviceAccount"in this.M;("credential"in this.M||a)&&"undefined"!==typeof console&&console.log("The '"+(a?"serviceAccount":"credential")+"' property specified in the first argument to initializeApp() is deprecated and will be removed in the next major version. You should instead use the 'firebase-admin' package. See https://firebase.google.com/docs/admin/setup for details on how to get started.");Object.keys(c.INTERNAL.factories).forEach(function(e){var g=
c.INTERNAL.useAsService(d,e);null!==g&&(g=d.aa.bind(d,g),d[e]=g)})};X.prototype.delete=function(){var a=this;return(new W(function(b){Y(a);b()})).then(function(){a.S.INTERNAL.removeApp(a.J);var b=[];Object.keys(a.j).forEach(function(c){Object.keys(a.j[c]).forEach(function(d){b.push(a.j[c][d])})});return W.all(b.filter(function(c){return"INTERNAL"in c}).map(function(c){return c.INTERNAL.delete()}))}).then(function(){a.T=!0;a.j={}})};
X.prototype.aa=function(a,b){Y(this);"undefined"===typeof this.j[a]&&(this.j[a]={});var c=b||"[DEFAULT]";return"undefined"===typeof this.j[a][c]?(b=this.S.INTERNAL.factories[a](this,this.Z.bind(this),b),this.j[a][c]=b):this.j[a][c]};X.prototype.Z=function(a){Q(this,a)};var Y=function(a){a.T&&Z("app-deleted",{name:a.J})};ca.Object.defineProperties(X.prototype,{name:{configurable:!0,enumerable:!0,get:function(){Y(this);return this.J}},options:{configurable:!0,enumerable:!0,get:function(){Y(this);return this.M}}});
X.prototype.name&&X.prototype.options||X.prototype.delete||console.log("dc");
function Ga(){function a(f){f=f||"[DEFAULT]";var h=d[f];void 0===h&&Z("no-app",{name:f});return h}function b(f,h){Object.keys(e).forEach(function(n){n=c(f,n);if(null!==n&&g[n])g[n](h,f)})}function c(f,h){if("serverAuth"===h)return null;var n=h;f=f.options;"auth"===h&&(f.serviceAccount||f.credential)&&(n="serverAuth","serverAuth"in e||Z("sa-not-supported"));return n}var d={},e={},g={},k={__esModule:!0,initializeApp:function(f,h){void 0===h?h="[DEFAULT]":("string"!==typeof h||""===h)&&Z("bad-app-name",
{name:h+""});void 0!==d[h]&&Z("duplicate-app",{name:h});f=new X(f,h,k);d[h]=f;b(f,"create");void 0!=f.INTERNAL&&void 0!=f.INTERNAL.getToken||Q(f,{INTERNAL:{getUid:function(){return null},getToken:function(){return W.resolve(null)},addAuthTokenListener:function(){},removeAuthTokenListener:function(){}}});return f},app:a,apps:null,Promise:W,SDK_VERSION:"0.0.0",INTERNAL:{registerService:function(f,h,n,v,Ha){e[f]&&Z("duplicate-service",{name:f});e[f]=Ha?h:function(A,Ia){return h(A,Ia,"[DEFAULT]")};v&&
(g[f]=v);v=function(A){void 0===A&&(A=a());"function"!==typeof A[f]&&Z("invalid-app-argument",{name:f});return A[f]()};void 0!==n&&Q(v,n);return k[f]=v},createFirebaseNamespace:Ga,extendNamespace:function(f){Q(k,f)},createSubscribe:Da,ErrorFactory:R,removeApp:function(f){b(d[f],"delete");delete d[f]},factories:e,useAsService:c,Promise:O,deepExtend:Q}};k["default"]=k;Object.defineProperty(k,"apps",{get:function(){return Object.keys(d).map(function(f){return d[f]})}});a.App=X;return k}
function Z(a,b){throw Ja.create(a,b);}
var Ja=new R("app","Firebase",{"no-app":"No Firebase App '{$name}' has been created - call Firebase App.initializeApp()","bad-app-name":"Illegal App name: '{$name}","duplicate-app":"Firebase App named '{$name}' already exists","app-deleted":"Firebase App named '{$name}' already deleted","duplicate-service":"Firebase service named '{$name}' already registered","sa-not-supported":"Initializing the Firebase SDK with a service account is only allowed in a Node.js environment. On client devices, you should instead initialize the SDK with an api key and auth domain","invalid-app-argument":"firebase.{$name}() takes either no argument or a Firebase App instance."});"undefined"!==typeof firebase&&(firebase=Ga()); }).call(this);
firebase.SDK_VERSION = "3.7.5";
(function(){/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var l,ba=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ca="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},da=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");
},ea=da(this),fa=function(a,b){if(b)a:{var c=ea;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&ca(c,a,{configurable:!0,writable:!0,value:b})}};
fa("Symbol",function(a){if(a)return a;var b=function(f,g){this.Gi=f;ca(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.Gi};var c="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});
fa("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=ea[b[c]];"function"===typeof d&&"function"!=typeof d.prototype[a]&&ca(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ha(ba(this))}})}return a});
var ha=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a},ia=function(a){var b="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];return b?b.call(a):{next:ba(a)}},ja="function"==typeof Object.create?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ka;
if("function"==typeof Object.setPrototypeOf)ka=Object.setPrototypeOf;else{var la;a:{var ma={a:!0},na={};try{na.__proto__=ma;la=na.a;break a}catch(a){}la=!1}ka=la?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var oa=ka,m=function(a,b){a.prototype=ja(b.prototype);a.prototype.constructor=a;if(oa)oa(a,b);else for(var c in b)if("prototype"!=c)if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Xb=b.prototype};
fa("Promise",function(a){function b(){this.nb=null}function c(g){return g instanceof e?g:new e(function(h){h(g)})}if(a)return a;b.prototype.Gg=function(g){if(null==this.nb){this.nb=[];var h=this;this.Hg(function(){h.Wi()})}this.nb.push(g)};var d=ea.setTimeout;b.prototype.Hg=function(g){d(g,0)};b.prototype.Wi=function(){for(;this.nb&&this.nb.length;){var g=this.nb;this.nb=[];for(var h=0;h<g.length;++h){var k=g[h];g[h]=null;try{k()}catch(n){this.Ni(n)}}}this.nb=null};b.prototype.Ni=function(g){this.Hg(function(){throw g;
})};var e=function(g){this.R=0;this.ma=void 0;this.uc=[];this.Ch=!1;var h=this.qf();try{g(h.resolve,h.reject)}catch(k){h.reject(k)}};e.prototype.qf=function(){function g(n){return function(p){k||(k=!0,n.call(h,p))}}var h=this,k=!1;return{resolve:g(this.Vj),reject:g(this.mg)}};e.prototype.Vj=function(g){if(g===this)this.mg(new TypeError("A Promise cannot resolve to itself"));else if(g instanceof e)this.jk(g);else{a:switch(typeof g){case "object":var h=null!=g;break a;case "function":h=!0;break a;default:h=
!1}h?this.Uj(g):this.lh(g)}};e.prototype.Uj=function(g){var h=void 0;try{h=g.then}catch(k){this.mg(k);return}"function"==typeof h?this.kk(h,g):this.lh(g)};e.prototype.mg=function(g){this.li(2,g)};e.prototype.lh=function(g){this.li(1,g)};e.prototype.li=function(g,h){if(0!=this.R)throw Error("Cannot settle("+g+", "+h+"): Promise already settled in state"+this.R);this.R=g;this.ma=h;2===this.R&&this.bk();this.Yi()};e.prototype.bk=function(){var g=this;d(function(){if(g.Gj()){var h=ea.console;"undefined"!==
typeof h&&h.error(g.ma)}},1)};e.prototype.Gj=function(){if(this.Ch)return!1;var g=ea.CustomEvent,h=ea.Event,k=ea.dispatchEvent;if("undefined"===typeof k)return!0;"function"===typeof g?g=new g("unhandledrejection",{cancelable:!0}):"function"===typeof h?g=new h("unhandledrejection",{cancelable:!0}):(g=ea.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.ma;return k(g)};e.prototype.Yi=function(){if(null!=this.uc){for(var g=0;g<this.uc.length;++g)f.Gg(this.uc[g]);
this.uc=null}};var f=new b;e.prototype.jk=function(g){var h=this.qf();g.Sd(h.resolve,h.reject)};e.prototype.kk=function(g,h){var k=this.qf();try{g.call(h,k.resolve,k.reject)}catch(n){k.reject(n)}};e.prototype.then=function(g,h){function k(t,z){return"function"==typeof t?function(aa){try{n(t(aa))}catch(mb){p(mb)}}:z}var n,p,r=new e(function(t,z){n=t;p=z});this.Sd(k(g,n),k(h,p));return r};e.prototype.catch=function(g){return this.then(void 0,g)};e.prototype.Sd=function(g,h){function k(){switch(n.R){case 1:g(n.ma);
break;case 2:h(n.ma);break;default:throw Error("Unexpected state: "+n.R);}}var n=this;null==this.uc?f.Gg(k):this.uc.push(k);this.Ch=!0};e.resolve=c;e.reject=function(g){return new e(function(h,k){k(g)})};e.race=function(g){return new e(function(h,k){for(var n=ia(g),p=n.next();!p.done;p=n.next())c(p.value).Sd(h,k)})};e.all=function(g){var h=ia(g),k=h.next();return k.done?c([]):new e(function(n,p){function r(aa){return function(mb){t[aa]=mb;z--;0==z&&n(t)}}var t=[],z=0;do t.push(void 0),z++,c(k.value).Sd(r(t.length-
1),p),k=h.next();while(!k.done)})};return e});var pa=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};
fa("WeakMap",function(a){function b(){}function c(k){var n=typeof k;return"object"===n&&null!==k||"function"===n}function d(k){if(!pa(k,f)){var n=new b;ca(k,f,{value:n})}}function e(k){var n=Object[k];n&&(Object[k]=function(p){if(p instanceof b)return p;Object.isExtensible(p)&&d(p);return n(p)})}if(function(){if(!a||!Object.seal)return!1;try{var k=Object.seal({}),n=Object.seal({}),p=new a([[k,2],[n,3]]);if(2!=p.get(k)||3!=p.get(n))return!1;p.delete(k);p.set(n,4);return!p.has(k)&&4==p.get(n)}catch(r){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var g=0,h=function(k){this.Fa=(g+=Math.random()+1).toString();if(k){k=ia(k);for(var n;!(n=k.next()).done;)n=n.value,this.set(n[0],n[1])}};h.prototype.set=function(k,n){if(!c(k))throw Error("Invalid WeakMap key");d(k);if(!pa(k,f))throw Error("WeakMap key fail: "+k);k[f][this.Fa]=n;return this};h.prototype.get=function(k){return c(k)&&pa(k,f)?k[f][this.Fa]:void 0};h.prototype.has=function(k){return c(k)&&pa(k,f)&&pa(k[f],
this.Fa)};h.prototype.delete=function(k){return c(k)&&pa(k,f)&&pa(k[f],this.Fa)?delete k[f][this.Fa]:!1};return h});
fa("Map",function(a){if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var h=Object.seal({x:4}),k=new a(ia([[h,"s"]]));if("s"!=k.get(h)||1!=k.size||k.get({x:4})||k.set({x:4},"t")!=k||2!=k.size)return!1;var n=k.entries(),p=n.next();if(p.done||p.value[0]!=h||"s"!=p.value[1])return!1;p=n.next();return p.done||4!=p.value[0].x||"t"!=p.value[1]||!n.next().done?!1:!0}catch(r){return!1}}())return a;var b=new WeakMap,c=function(h){this.Sc={};this.ra=
f();this.size=0;if(h){h=ia(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}};c.prototype.set=function(h,k){h=0===h?0:h;var n=d(this,h);n.list||(n.list=this.Sc[n.id]=[]);n.ha?n.ha.value=k:(n.ha={next:this.ra,hb:this.ra.hb,head:this.ra,key:h,value:k},n.list.push(n.ha),this.ra.hb.next=n.ha,this.ra.hb=n.ha,this.size++);return this};c.prototype.delete=function(h){h=d(this,h);return h.ha&&h.list?(h.list.splice(h.index,1),h.list.length||delete this.Sc[h.id],h.ha.hb.next=h.ha.next,h.ha.next.hb=
h.ha.hb,h.ha.head=null,this.size--,!0):!1};c.prototype.clear=function(){this.Sc={};this.ra=this.ra.hb=f();this.size=0};c.prototype.has=function(h){return!!d(this,h).ha};c.prototype.get=function(h){return(h=d(this,h).ha)&&h.value};c.prototype.entries=function(){return e(this,function(h){return[h.key,h.value]})};c.prototype.keys=function(){return e(this,function(h){return h.key})};c.prototype.values=function(){return e(this,function(h){return h.value})};c.prototype.forEach=function(h,k){for(var n=this.entries(),
p;!(p=n.next()).done;)p=p.value,h.call(k,p[1],p[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(h,k){var n=k&&typeof k;"object"==n||"function"==n?b.has(k)?n=b.get(k):(n=""+ ++g,b.set(k,n)):n="p_"+k;var p=h.Sc[n];if(p&&pa(h.Sc,n))for(h=0;h<p.length;h++){var r=p[h];if(k!==k&&r.key!==r.key||k===r.key)return{id:n,list:p,index:h,ha:r}}return{id:n,list:p,index:-1,ha:void 0}},e=function(h,k){var n=h.ra;return ha(function(){if(n){for(;n.head!=h.ra;)n=n.hb;for(;n.next!=n.head;)return n=
n.next,{done:!1,value:k(n)};n=null}return{done:!0,value:void 0}})},f=function(){var h={};return h.hb=h.next=h.head=h},g=0;return c});fa("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var g=d[f];if(b.call(c,g,f,d)){b=g;break a}}b=void 0}return b}});
var ra=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};fa("Array.prototype.entries",function(a){return a?a:function(){return ra(this,function(b,c){return[b,c]})}});fa("Array.prototype.values",function(a){return a?a:function(){return ra(this,function(b,c){return c})}});
fa("Array.prototype.keys",function(a){return a?a:function(){return ra(this,function(b){return b})}});fa("Array.from",function(a){return a?a:function(b,c,d){c=null!=c?c:function(h){return h};var e=[],f="undefined"!=typeof Symbol&&Symbol.iterator&&b[Symbol.iterator];if("function"==typeof f){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}});
fa("Object.is",function(a){return a?a:function(b,c){return b===c?0!==b||1/b===1/c:b!==b&&c!==c}});fa("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(0>c&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});
fa("String.prototype.includes",function(a){return a?a:function(b,c){if(null==this)throw new TypeError("The 'this' value for String.prototype.includes must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype.includes must not be a regular expression");return-1!==(this+"").indexOf(b,c||0)}});
var sa=sa||{},q=this||self,ta=function(a){var b=typeof a;return"object"!=b?b:a?Array.isArray(a)?"array":b:"null"},ua=function(a){var b=ta(a);return"array"==b||"object"==b&&"number"==typeof a.length},u=function(a){var b=typeof a;return"object"==b&&null!=a||"function"==b},va=function(a,b,c){return a.call.apply(a.bind,arguments)},wa=function(a,b,c){if(!a)throw Error();if(2<arguments.length){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,
d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}},v=function(a,b,c){v=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?va:wa;return v.apply(null,arguments)},xa=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}},w=function(a,b){function c(){}c.prototype=b.prototype;a.Xb=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Pk=function(d,e,f){for(var g=
Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}},ya=function(a){return a};function za(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,za);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));void 0!==b&&(this.cause=b)}w(za,Error);za.prototype.name="CustomError";var Aa;function Ba(a,b){a=a.split("%s");for(var c="",d=a.length-1,e=0;e<d;e++)c+=a[e]+(e<b.length?b[e]:"%s");za.call(this,c+a[d])}w(Ba,za);Ba.prototype.name="AssertionError";function Ca(a,b,c,d){var e="Assertion failed";if(c){e+=": "+c;var f=d}else a&&(e+=": "+a,f=b);throw new Ba(""+e,f||[]);}
var x=function(a,b,c){a||Ca("",null,b,Array.prototype.slice.call(arguments,2));return a},Da=function(a,b){throw new Ba("Failure"+(a?": "+a:""),Array.prototype.slice.call(arguments,1));},Ea=function(a,b,c){"number"!==typeof a&&Ca("Expected number but got %s: %s.",[ta(a),a],b,Array.prototype.slice.call(arguments,2));return a},Fa=function(a,b,c){"string"!==typeof a&&Ca("Expected string but got %s: %s.",[ta(a),a],b,Array.prototype.slice.call(arguments,2))},Ga=function(a,b,c){"function"!==typeof a&&Ca("Expected function but got %s: %s.",
[ta(a),a],b,Array.prototype.slice.call(arguments,2))};/*

 SPDX-License-Identifier: Apache-2.0
*/
var Ha=Array.prototype.indexOf?function(a,b){x(null!=a.length);return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if("string"===typeof a)return"string"!==typeof b||1!=b.length?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1},y=Array.prototype.forEach?function(a,b){x(null!=a.length);Array.prototype.forEach.call(a,b,void 0)}:function(a,b){for(var c=a.length,d="string"===typeof a?a.split(""):a,e=0;e<c;e++)e in d&&b.call(void 0,d[e],e,a)};
function Ia(a,b){for(var c="string"===typeof a?a.split(""):a,d=a.length-1;0<=d;--d)d in c&&b.call(void 0,c[d],d,a)}
var Ja=Array.prototype.filter?function(a,b){x(null!=a.length);return Array.prototype.filter.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=[],e=0,f="string"===typeof a?a.split(""):a,g=0;g<c;g++)if(g in f){var h=f[g];b.call(void 0,h,g,a)&&(d[e++]=h)}return d},Ka=Array.prototype.map?function(a,b){x(null!=a.length);return Array.prototype.map.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=Array(c),e="string"===typeof a?a.split(""):a,f=0;f<c;f++)f in e&&(d[f]=b.call(void 0,e[f],f,a));return d},
La=Array.prototype.some?function(a,b){x(null!=a.length);return Array.prototype.some.call(a,b,void 0)}:function(a,b){for(var c=a.length,d="string"===typeof a?a.split(""):a,e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return!0;return!1};function Ma(a,b){return 0<=Ha(a,b)}function Na(a,b){b=Ha(a,b);var c;(c=0<=b)&&Oa(a,b);return c}function Oa(a,b){x(null!=a.length);return 1==Array.prototype.splice.call(a,b,1).length}function Pa(a,b){var c=0;Ia(a,function(d,e){b.call(void 0,d,e,a)&&Oa(a,e)&&c++})}
function Qa(a){var b=a.length;if(0<b){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};function Ra(a,b){for(var c in a)b.call(void 0,a[c],c,a)}function Sa(a,b){for(var c in a)if(b.call(void 0,a[c],c,a))return!0;return!1}function Ta(a){for(var b in a)return!1;return!0}function Ua(a){var b={},c;for(c in a)b[c]=a[c];return b}var Va="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");
function Wa(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<Va.length;f++)c=Va[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};var Xa,Ya=function(){if(void 0===Xa){var a=null,b=q.trustedTypes;if(b&&b.createPolicy)try{a=b.createPolicy("goog#html",{createHTML:ya,createScript:ya,createScriptURL:ya})}catch(c){q.console&&q.console.error(c.message)}Xa=a}return Xa};var ab=function(a,b){this.zg=a===Za&&b||"";this.Ji=$a};ab.prototype.tb=!0;ab.prototype.bb=function(){return this.zg};ab.prototype.toString=function(){return"Const{"+this.zg+"}"};var bb=function(a){if(a instanceof ab&&a.constructor===ab&&a.Ji===$a)return a.zg;Da("expected object of type Const, got '"+a+"'");return"type_error:Const"},cb=function(a){return new ab(Za,a)},$a={},Za={};var eb=function(a,b){this.hg=b===db?a:""};eb.prototype.toString=function(){return this.hg+""};eb.prototype.tb=!0;eb.prototype.bb=function(){return this.hg.toString()};
var fb=function(a){if(a instanceof eb&&a.constructor===eb)return a.hg;Da("expected object of type TrustedResourceUrl, got '"+a+"' of type "+ta(a));return"type_error:TrustedResourceUrl"},jb=function(a,b){var c=bb(a);if(!gb.test(c))throw Error("Invalid TrustedResourceUrl format: "+c);a=c.replace(hb,function(d,e){if(!Object.prototype.hasOwnProperty.call(b,e))throw Error('Found marker, "'+e+'", in format string, "'+c+'", but no valid label mapping found in args: '+JSON.stringify(b));d=b[e];return d instanceof
ab?bb(d):encodeURIComponent(String(d))});return ib(a)},hb=/%{(\w+)}/g,gb=RegExp("^((https:)?//[0-9a-z.:[\\]-]+/|/[^/\\\\]|[^:/\\\\%]+/|[^:/\\\\%]*[?#]|about:blank#)","i"),db={},ib=function(a){var b=Ya();a=b?b.createScriptURL(a):a;return new eb(a,db)};var kb=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]},tb=function(a){if(!lb.test(a))return a;-1!=a.indexOf("&")&&(a=a.replace(nb,"&amp;"));-1!=a.indexOf("<")&&(a=a.replace(ob,"&lt;"));-1!=a.indexOf(">")&&(a=a.replace(pb,"&gt;"));-1!=a.indexOf('"')&&(a=a.replace(qb,"&quot;"));-1!=a.indexOf("'")&&(a=a.replace(rb,"&#39;"));-1!=a.indexOf("\x00")&&(a=a.replace(sb,"&#0;"));return a},nb=/&/g,ob=/</g,pb=/>/g,qb=/"/g,rb=/'/g,sb=/\x00/g,lb=
/[\x00&<>"']/,A=function(a,b){return-1!=a.indexOf(b)},ub=function(a,b){return a<b?-1:a>b?1:0};var wb=function(a,b){this.gg=b===vb?a:""};wb.prototype.toString=function(){return this.gg.toString()};wb.prototype.tb=!0;wb.prototype.bb=function(){return this.gg.toString()};
var xb=function(a){if(a instanceof wb&&a.constructor===wb)return a.gg;Da("expected object of type SafeUrl, got '"+a+"' of type "+ta(a));return"type_error:SafeUrl"},yb=/^data:(.*);base64,[a-z0-9+\/]+=*$/i,zb=/^(?:(?:https?|mailto|ftp):|[^:/?#]*(?:[/?#]|$))/i,Ab=function(a){if(a instanceof wb)return a;a="object"==typeof a&&a.tb?a.bb():String(a);zb.test(a)?a=new wb(a,vb):(a=String(a).replace(/(%0A|%0D)/g,""),a=a.match(yb)?new wb(a,vb):null);return a},Bb=function(a){if(a instanceof wb)return a;a="object"==
typeof a&&a.tb?a.bb():String(a);x(zb.test(a),"%s does not match the safe URL pattern",a)||(a="about:invalid#zClosurez");return new wb(a,vb)},vb={},Cb=new wb("about:invalid#zClosurez",vb);var Db={},Eb=function(a,b){this.fg=b===Db?a:"";this.tb=!0};Eb.prototype.toString=function(){return this.fg.toString()};Eb.prototype.bb=function(){return this.fg};var Fb=new Eb("",Db);function Gb(){var a=q.navigator;return a&&(a=a.userAgent)?a:""}function B(a){return A(Gb(),a)};var Hb={},Ib=function(a,b){this.eg=b===Hb?a:"";this.tb=!0};Ib.prototype.bb=function(){return this.eg.toString()};Ib.prototype.toString=function(){return this.eg.toString()};
var Jb=function(a){if(a instanceof Ib&&a.constructor===Ib)return a.eg;Da("expected object of type SafeHtml, got '"+a+"' of type "+ta(a));return"type_error:SafeHtml"},Lb=function(a){return a instanceof Ib?a:Kb(tb("object"==typeof a&&a.tb?a.bb():String(a)))},Kb=function(a){var b=Ya();a=b?b.createHTML(a):a;return new Ib(a,Hb)},Mb=new Ib(q.trustedTypes&&q.trustedTypes.emptyHTML||"",Hb);var Nb=function(a,b){Fa(bb(a),"must provide justification");x(!/^[\s\xa0]*$/.test(bb(a)),"must provide non-empty justification");return Kb(b)};var Ob=function(a,b){a:{try{var c=a&&a.ownerDocument,d=c&&(c.defaultView||c.parentWindow);d=d||q;if(d.Element&&d.Location){var e=d;break a}}catch(g){}e=null}if(e&&"undefined"!=typeof e[b]&&(!a||!(a instanceof e[b])&&(a instanceof e.Location||a instanceof e.Element))){if(u(a))try{var f=a.constructor.displayName||a.constructor.name||Object.prototype.toString.call(a)}catch(g){f="<object could not be stringified>"}else f=void 0===a?"undefined":null===a?"null":typeof a;Da("Argument is not a %s (or a non-Element, non-Location mock); got: %s",
b,f)}};var Pb=function(){},Qb=function(a){return"function"===typeof a};var Rb=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}}(function(){if("undefined"===typeof document)return!1;var a=document.createElement("div"),b=document.createElement("div");b.appendChild(document.createElement("div"));a.appendChild(b);if(!a.firstChild)return!1;b=a.firstChild.firstChild;a.innerHTML=Jb(Mb);return!b.parentElement}),Tb=function(a,b){Ob(a,"HTMLScriptElement");var c=Sb("script[nonce]",a.ownerDocument&&a.ownerDocument.defaultView);c&&a.setAttribute("nonce",c);a.src=
fb(b)},Ub=function(a,b,c,d){a=a instanceof wb?a:Bb(a);b=b||q;c=c instanceof ab?bb(c):c||"";return void 0!==d?b.open(xb(a),c,d):b.open(xb(a),c)},Vb=/^[\w+/_-]+[=]{0,2}$/,Sb=function(a,b){b=(b||q).document;return b.querySelector?(a=b.querySelector(a))&&(a=a.nonce||a.getAttribute("nonce"))&&Vb.test(a)?a:"":""};var Wb=function(a,b){for(var c=a.split("%s"),d="",e=Array.prototype.slice.call(arguments,1);e.length&&1<c.length;)d+=c.shift()+e.shift();return d+c.join("%s")};var Xb=function(a){if(a.qb&&"function"==typeof a.qb)return a.qb();if("undefined"!==typeof Map&&a instanceof Map||"undefined"!==typeof Set&&a instanceof Set)return Array.from(a.values());if("string"===typeof a)return a.split("");if(ua(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}b=[];c=0;for(d in a)b[c++]=a[d];return b},Yb=function(a){if(a.ce&&"function"==typeof a.ce)return a.ce();if(!a.qb||"function"!=typeof a.qb){if("undefined"!==typeof Map&&a instanceof Map)return Array.from(a.keys());
if(!("undefined"!==typeof Set&&a instanceof Set)){if(ua(a)||"string"===typeof a){var b=[];a=a.length;for(var c=0;c<a;c++)b.push(c);return b}b=[];c=0;for(var d in a)b[c++]=d;return b}}},Zb=function(a,b,c){if(a.forEach&&"function"==typeof a.forEach)a.forEach(b,c);else if(ua(a)||"string"===typeof a)Array.prototype.forEach.call(a,b,c);else for(var d=Yb(a),e=Xb(a),f=e.length,g=0;g<f;g++)b.call(c,e[g],d&&d[g],a)};var $b=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),ac=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(0<=d){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}};var bc=function(a){this.ga=this.Zb=this.ua="";this.Ua=null;this.Xc=this.fb="";this.Ba=this.Aj=!1;if(a instanceof bc){this.Ba=a.Ba;cc(this,a.ua);var b=a.Zb;dc(this);this.Zb=b;ec(this,a.ga);fc(this,a.Ua);gc(this,a.fb);hc(this,a.ya.clone());ic(this,a.Xc)}else a&&(b=String(a).match($b))?(this.Ba=!1,cc(this,b[1]||"",!0),a=b[2]||"",dc(this),this.Zb=jc(a),ec(this,b[3]||"",!0),fc(this,b[4]),gc(this,b[5]||"",!0),hc(this,b[6]||"",!0),ic(this,b[7]||"",!0)):(this.Ba=!1,this.ya=new kc(null,this.Ba))};
bc.prototype.toString=function(){var a=[],b=this.ua;b&&a.push(lc(b,mc,!0),":");var c=this.ga;if(c||"file"==b)a.push("//"),(b=this.Zb)&&a.push(lc(b,mc,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.Ua,null!=c&&a.push(":",String(c));if(c=this.fb)this.ga&&"/"!=c.charAt(0)&&a.push("/"),a.push(lc(c,"/"==c.charAt(0)?nc:oc,!0));(c=this.ya.toString())&&a.push("?",c);(c=this.Xc)&&a.push("#",lc(c,pc));return a.join("")};
bc.prototype.resolve=function(a){var b=this.clone(),c=!!a.ua;c?cc(b,a.ua):c=!!a.Zb;if(c){var d=a.Zb;dc(b);b.Zb=d}else c=!!a.ga;c?ec(b,a.ga):c=null!=a.Ua;d=a.fb;if(c)fc(b,a.Ua);else if(c=!!a.fb){if("/"!=d.charAt(0))if(this.ga&&!this.fb)d="/"+d;else{var e=b.fb.lastIndexOf("/");-1!=e&&(d=b.fb.slice(0,e+1)+d)}e=d;if(".."==e||"."==e)d="";else if(A(e,"./")||A(e,"/.")){d=0==e.lastIndexOf("/",0);e=e.split("/");for(var f=[],g=0;g<e.length;){var h=e[g++];"."==h?d&&g==e.length&&f.push(""):".."==h?((1<f.length||
1==f.length&&""!=f[0])&&f.pop(),d&&g==e.length&&f.push("")):(f.push(h),d=!0)}d=f.join("/")}else d=e}c?gc(b,d):c=""!==a.ya.toString();c?hc(b,a.ya.clone()):c=!!a.Xc;c&&ic(b,a.Xc);return b};bc.prototype.clone=function(){return new bc(this)};
var cc=function(a,b,c){dc(a);a.ua=c?jc(b,!0):b;a.ua&&(a.ua=a.ua.replace(/:$/,""))},ec=function(a,b,c){dc(a);a.ga=c?jc(b,!0):b},fc=function(a,b){dc(a);if(b){b=Number(b);if(isNaN(b)||0>b)throw Error("Bad port number "+b);a.Ua=b}else a.Ua=null},gc=function(a,b,c){dc(a);a.fb=c?jc(b,!0):b;return a},hc=function(a,b,c){dc(a);b instanceof kc?(a.ya=b,a.ya.tg(a.Ba)):(c||(b=lc(b,qc)),a.ya=new kc(b,a.Ba));return a};bc.prototype.getQuery=function(){return this.ya.toString()};
var C=function(a,b,c){dc(a);a.ya.set(b,c)},D=function(a,b){return a.ya.get(b)},ic=function(a,b,c){dc(a);a.Xc=c?jc(b):b;return a};bc.prototype.removeParameter=function(a){dc(this);this.ya.remove(a);return this};var dc=function(a){if(a.Aj)throw Error("Tried to modify a read-only Uri");};bc.prototype.tg=function(a){this.Ba=a;this.ya&&this.ya.tg(a)};
var E=function(a){return a instanceof bc?a.clone():new bc(a)},rc=function(a,b,c,d,e,f){var g=new bc(null);a&&cc(g,a);b&&ec(g,b);c&&fc(g,c);d&&gc(g,d);e&&hc(g,e);f&&ic(g,f);return g},jc=function(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""},lc=function(a,b,c){return"string"===typeof a?(a=encodeURI(a).replace(b,sc),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null},sc=function(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)},mc=/[#\/\?@]/g,
oc=/[#\?:]/g,nc=/[#\?]/g,qc=/[#\?@]/g,pc=/#/g,kc=function(a,b){this.ea=this.K=null;this.va=a||null;this.Ba=!!b},tc=function(a){a.K||(a.K=new Map,a.ea=0,a.va&&ac(a.va,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))},vc=function(a){var b=Yb(a);if("undefined"==typeof b)throw Error("Keys are undefined");var c=new kc(null);a=Xb(a);for(var d=0;d<b.length;d++){var e=b[d],f=a[d];Array.isArray(f)?uc(c,e,f):c.add(e,f)}return c};l=kc.prototype;
l.add=function(a,b){tc(this);this.va=null;a=this.wa(a);var c=this.K.get(a);c||this.K.set(a,c=[]);c.push(b);this.ea=Ea(this.ea)+1;return this};l.remove=function(a){tc(this);a=this.wa(a);return this.K.has(a)?(this.va=null,this.ea=Ea(this.ea)-this.K.get(a).length,this.K.delete(a)):!1};l.clear=function(){this.K=this.va=null;this.ea=0};l.Ah=function(){tc(this);return 0==this.ea};l.Pc=function(a){tc(this);a=this.wa(a);return this.K.has(a)};
l.forEach=function(a,b){tc(this);this.K.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};l.ce=function(){tc(this);for(var a=Array.from(this.K.values()),b=Array.from(this.K.keys()),c=[],d=0;d<b.length;d++)for(var e=a[d],f=0;f<e.length;f++)c.push(b[d]);return c};l.qb=function(a){tc(this);var b=[];if("string"===typeof a)this.Pc(a)&&(b=b.concat(this.K.get(this.wa(a))));else{a=Array.from(this.K.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};
l.set=function(a,b){tc(this);this.va=null;a=this.wa(a);this.Pc(a)&&(this.ea=Ea(this.ea)-this.K.get(a).length);this.K.set(a,[b]);this.ea=Ea(this.ea)+1;return this};l.get=function(a,b){if(!a)return b;a=this.qb(a);return 0<a.length?String(a[0]):b};var uc=function(a,b,c){a.remove(b);0<c.length&&(a.va=null,a.K.set(a.wa(b),Qa(c)),a.ea=Ea(a.ea)+c.length)};l=kc.prototype;
l.toString=function(){if(this.va)return this.va;if(!this.K)return"";for(var a=[],b=Array.from(this.K.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.qb(d);for(var f=0;f<d.length;f++){var g=e;""!==d[f]&&(g+="="+encodeURIComponent(String(d[f])));a.push(g)}}return this.va=a.join("&")};l.clone=function(){var a=new kc;a.va=this.va;this.K&&(a.K=new Map(this.K),a.ea=this.ea);return a};l.wa=function(a){a=String(a);this.Ba&&(a=a.toLowerCase());return a};
l.tg=function(a){a&&!this.Ba&&(tc(this),this.va=null,this.K.forEach(function(b,c){var d=c.toLowerCase();c!=d&&(this.remove(c),uc(this,d,b))},this));this.Ba=a};l.extend=function(a){for(var b=0;b<arguments.length;b++)Zb(arguments[b],function(c,d){this.add(d,c)},this)};var wc={Sk:!0},xc=function(){throw Error("Do not instantiate directly");};xc.prototype.Tg=null;xc.prototype.toString=function(){return this.content};var yc=function(){xc.call(this)};w(yc,xc);yc.prototype.Ug=wc;var zc=function(a){var b=null!=a&&a.Ug===wc;b&&x(a.constructor===yc);return b};var Ac=Object.freeze||function(a){return a};var Bc=function(a){Bc[" "](a);return a};Bc[" "]=function(){};var Dc=function(a){var b=Cc;return Object.prototype.hasOwnProperty.call(b,9)?b[9]:b[9]=a(9)};var Ec=B("Opera"),Fc=B("Trident")||B("MSIE"),Gc=B("Edge"),Hc=Gc||Fc,Ic=B("Gecko")&&!(A(Gb().toLowerCase(),"webkit")&&!B("Edge"))&&!(B("Trident")||B("MSIE"))&&!B("Edge"),Jc=A(Gb().toLowerCase(),"webkit")&&!B("Edge"),Kc=function(){var a=q.document;return a?a.documentMode:void 0},Lc;
a:{var Mc="",Nc=function(){var a=Gb();if(Ic)return/rv:([^\);]+)(\)|;)/.exec(a);if(Gc)return/Edge\/([\d\.]+)/.exec(a);if(Fc)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(Jc)return/WebKit\/(\S+)/.exec(a);if(Ec)return/(?:Version)[ \/]?(\S+)/.exec(a)}();Nc&&(Mc=Nc?Nc[1]:"");if(Fc){var Oc=Kc();if(null!=Oc&&Oc>parseFloat(Mc)){Lc=String(Oc);break a}}Lc=Mc}
var Pc=Lc,Cc={},Qc=function(){return Dc(function(){for(var a=0,b=kb(String(Pc)).split("."),c=kb("9").split("."),d=Math.max(b.length,c.length),e=0;0==a&&e<d;e++){var f=b[e]||"",g=c[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];g=/(\d*)(\D*)(.*)/.exec(g)||["","","",""];if(0==f[0].length&&0==g[0].length)break;a=ub(0==f[1].length?0:parseInt(f[1],10),0==g[1].length?0:parseInt(g[1],10))||ub(0==f[2].length,0==g[2].length)||ub(f[2],g[2]);f=f[3];g=g[3]}while(0==a)}return 0<=a})},Rc;
if(q.document&&Fc){var Sc=Kc();Rc=Sc?Sc:parseInt(Pc,10)||void 0}else Rc=void 0;var Tc=Rc;var Uc=function(a){if(null!=a)switch(a.Tg){case 1:return 1;case -1:return-1;case 0:return 0}return null},Yc=function(a){return zc(a)?a:a instanceof Ib?Vc(Jb(a).toString()):a instanceof Ib?Vc(Jb(a).toString()):Vc(String(String(a)).replace(Wc,Xc),Uc(a))},Vc=function(a){function b(c){this.content=c}b.prototype=a.prototype;return function(c,d){c=new b(String(c));void 0!==d&&(c.Tg=d);return c}}(yc),Zc={},F=function(a){if(zc(a)){var b=String;a=String(a.content).replace($c,"").replace(ad,"&lt;");b=b(a).replace(bd,
Xc)}else b=String(a).replace(Wc,Xc);return b},cd=function(a,b,c,d){a||(a=c instanceof Function?c.displayName||c.name||"unknown type name":c instanceof Object?c.constructor.displayName||c.constructor.name||Object.prototype.toString.call(c):null===c?"null":typeof c,Da("expected @param "+b+" of type "+d+(", but got "+a)+"."))},dd={},ed=function(){x(dd===dd,"found an incorrect call marker, was an internal function called from the top level?")},fd={"\x00":"&#0;","\t":"&#9;","\n":"&#10;","\v":"&#11;","\f":"&#12;",
"\r":"&#13;"," ":"&#32;",'"':"&quot;","&":"&amp;","'":"&#39;","-":"&#45;","/":"&#47;","<":"&lt;","=":"&#61;",">":"&gt;","`":"&#96;","\u0085":"&#133;","\u00a0":"&#160;","\u2028":"&#8232;","\u2029":"&#8233;"},Xc=function(a){return fd[a]},Wc=/[\x00\x22\x26\x27\x3c\x3e]/g,bd=/[\x00\x22\x27\x3c\x3e]/g,$c=/<(?:!|\/?([a-zA-Z][a-zA-Z0-9:\-]*))(?:[^>'"]|"[^"]*"|'[^']*')*>/g,ad=/</g;try{(new self.OffscreenCanvas(0,0)).getContext("2d")}catch(a){};var gd=function(a){var b=document;return"string"===typeof a?b.getElementById(a):a},id=function(a,b){Ra(b,function(c,d){c&&"object"==typeof c&&c.tb&&(c=c.bb());"style"==d?a.style.cssText=c:"class"==d?a.className=c:"for"==d?a.htmlFor=c:hd.hasOwnProperty(d)?a.setAttribute(hd[d],c):0==d.lastIndexOf("aria-",0)||0==d.lastIndexOf("data-",0)?a.setAttribute(d,c):a[d]=c})},hd={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",
nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"},kd=function(a,b,c){return jd(document,arguments)},jd=function(a,b){var c=b[1],d=ld(a,String(b[0]));c&&("string"===typeof c?d.className=c:Array.isArray(c)?d.className=c.join(" "):id(d,c));2<b.length&&md(a,d,b,2);return d},md=function(a,b,c,d){function e(h){h&&b.appendChild("string"===typeof h?a.createTextNode(h):h)}for(;d<c.length;d++){var f=c[d];if(!ua(f)||u(f)&&0<f.nodeType)e(f);else{a:{if(f&&"number"==
typeof f.length){if(u(f)){var g="function"==typeof f.item||"string"==typeof f.item;break a}if("function"===typeof f){g="function"==typeof f.item;break a}}g=!1}y(g?Qa(f):f,e)}}},ld=function(a,b){b=String(b);"application/xhtml+xml"===a.contentType&&(b=b.toLowerCase());return a.createElement(b)},nd=function(a,b){x(null!=a&&null!=b,"goog.dom.appendChild expects non-null arguments");a.appendChild(b)},od=function(a){for(var b;b=a.firstChild;)a.removeChild(b)},pd=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):
null},qd=function(){var a=document.body;if(void 0!==a.lastElementChild)a=a.lastElementChild;else for(a=a.lastChild;a&&1!=a.nodeType;)a=a.previousSibling;return a},rd=function(a){x(a,"Node cannot be null or undefined.");return 9==a.nodeType?a:a.ownerDocument||a.document},sd=function(a,b){x(null!=a,"goog.dom.setTextContent expects a non-null value for node");if("textContent"in a)a.textContent=b;else if(3==a.nodeType)a.data=String(b);else if(a.firstChild&&3==a.firstChild.nodeType){for(;a.lastChild!=
a.firstChild;)a.removeChild(x(a.lastChild));a.firstChild.data=String(b)}else{od(a);var c=rd(a);a.appendChild(c.createTextNode(String(b)))}},td={SCRIPT:1,STYLE:1,HEAD:1,IFRAME:1,OBJECT:1},ud={IMG:" ",BR:"\n"},wd=function(a){var b=[];vd(a,b,!0);a=b.join("");a=a.replace(/ \xAD /g," ").replace(/\xAD/g,"");a=a.replace(/\u200B/g,"");a=a.replace(/ +/g," ");" "!=a&&(a=a.replace(/^\s*/,""));return a},vd=function(a,b,c){if(!(a.nodeName in td))if(3==a.nodeType)c?b.push(String(a.nodeValue).replace(/(\r\n|\r|\n)/g,
"")):b.push(a.nodeValue);else if(a.nodeName in ud)b.push(ud[a.nodeName]);else for(a=a.firstChild;a;)vd(a,b,c),a=a.nextSibling},xd=function(a){this.Ra=a||q.document||document};xd.prototype.getElementsByTagName=function(a,b){return(b||this.Ra).getElementsByTagName(String(a))};var yd=function(a,b){a=a.Ra;b=b&&"*"!=b?String(b).toUpperCase():"";b=a.querySelectorAll&&a.querySelector&&b?a.querySelectorAll(b+""):a.getElementsByTagName(b||"*");return b};l=xd.prototype;
l.Wg=function(a,b,c){return jd(this.Ra,arguments)};l.createElement=function(a){return ld(this.Ra,a)};l.createTextNode=function(a){return this.Ra.createTextNode(String(a))};l.getWindow=function(){var a=this.Ra;return a.parentWindow||a.defaultView};l.appendChild=nd;l.append=function(a,b){md(rd(a),a,arguments,1)};l.canHaveChildren=function(a){if(1!=a.nodeType)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};
l.removeNode=pd;l.contains=function(a,b){if(!a||!b)return!1;if(a.contains&&1==b.nodeType)return a==b||a.contains(b);if("undefined"!=typeof a.compareDocumentPosition)return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};/*
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var zd=/^<(body|caption|col|colgroup|head|html|tr|td|th|tbody|thead|tfoot)>/i,Ad={};var Bd=function(a,b){return Zc["fireauth.oauthhelper.ui.soy.pendingScreen"]?Zc["fireauth.oauthhelper.ui.soy.pendingScreen"](a,b):Vc('<div id="pending-screen"><div id="progressBar" class="'+F("mdl-progress")+" "+F("mdl-js-progress")+" "+F("mdl-progress__indeterminate")+" "+F("firebase-progress-bar")+'"></div></div>')},Cd=function(a,b){a=a||{};a=a.appName;ed();Zc["fireauth.oauthhelper.ui.soy.continueScreen"]?b=Zc["fireauth.oauthhelper.ui.soy.continueScreen"]({appName:a},b):(cd(null==a||"string"===typeof a,
"appName",a,"null|string|undefined"),b='<div id="continue-screen" class="'+F("mdl-card")+" "+F("mdl-shadow--2dp")+" "+F("firebase-container")+'"><button id="continue" class="'+F("mdl-button")+" "+F("mdl-button--raised")+" "+F("mdl-button--colored")+'">',a?(a="Continue to "+Yc(a),b+=a):b+="Continue to the app",b=Vc(b+"</button></div>"));return b},Dd=function(a,b){a=a.errorMessage;ed();Zc["fireauth.oauthhelper.ui.soy.errorScreen"]?b=Zc["fireauth.oauthhelper.ui.soy.errorScreen"]({errorMessage:a},b):
(cd("string"===typeof a,"errorMessage",a,"string"),b=Vc('<div id="error-screen" class="'+F("mdl-card")+" "+F("mdl-shadow--2dp")+" "+F("firebase-container")+'">'+Yc(a)+"</div>"));return b},Ed=function(a,b){if(Zc["fireauth.oauthhelper.ui.soy.appVerificationScreen"])return Zc["fireauth.oauthhelper.ui.soy.appVerificationScreen"](a,b);a='<div id="app-verification-screen" class="'+F("mdl-card")+" "+F("mdl-shadow--2dp")+" "+F("firebase-container")+'"><button id="verify" class="'+F("mdl-button")+" "+F("firebase-hidden-button")+
'">';return Vc(a+'Verify</button><div id="status-container"><h1 class="firebase-title" id="status-container-label">Verifying you\'re not a robot...</h1><div id="app-verification-progress-bar" class="mdl-progress mdl-js-progress mdl-progress__indeterminate firebase-middle-progress-bar"></div></div></div>')};var Fd=function(a){return Array.prototype.map.call(a,function(b){b=b.toString(16);return 1<b.length?b:"0"+b}).join("")};var Gd=null,Id=function(a){var b=[];Hd(a,function(c){b.push(c)});return b},Hd=function(a,b){function c(k){for(;d<a.length;){var n=a.charAt(d++),p=Gd[n];if(null!=p)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return k}Jd();for(var d=0;;){var e=c(-1),f=c(0),g=c(64),h=c(64);if(64===h&&-1===e)break;b(e<<2|f>>4);64!=g&&(b(f<<4&240|g>>2),64!=h&&b(g<<6&192|h))}},Jd=function(){if(!Gd){Gd={};for(var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),
b=["+/=","+/","-_=","-_.","-_"],c=0;5>c;c++)for(var d=a.concat(b[c].split("")),e=0;e<d.length;e++){var f=d[e],g=Gd[f];void 0===g?Gd[f]=e:x(g===e)}}};var Kd=function(){this.blockSize=-1};var Nd=function(a,b){this.blockSize=-1;this.blockSize=64;this.Vd=q.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.Te=this.qc=0;this.F=[];this.Hj=a;this.yh=b;this.zk=q.Int32Array?new Int32Array(64):Array(64);void 0===Ld&&(Ld=q.Int32Array?new Int32Array(Md):Md);this.reset()},Ld;w(Nd,Kd);for(var Od=[],Pd=0;63>Pd;Pd++)Od[Pd]=0;var Qd=function(a){return Array.prototype.concat.apply([],arguments)}(128,Od);
Nd.prototype.reset=function(){this.Te=this.qc=0;this.F=q.Int32Array?new Int32Array(this.yh):Qa(this.yh)};
var Rd=function(a){var b=a.Vd;x(b.length==a.blockSize);for(var c=a.zk,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=4*d;for(b=16;64>b;b++){e=c[b-15]|0;d=c[b-2]|0;var f=(c[b-16]|0)+((e>>>7|e<<25)^(e>>>18|e<<14)^e>>>3)|0,g=(c[b-7]|0)+((d>>>17|d<<15)^(d>>>19|d<<13)^d>>>10)|0;c[b]=f+g|0}d=a.F[0]|0;e=a.F[1]|0;var h=a.F[2]|0,k=a.F[3]|0,n=a.F[4]|0,p=a.F[5]|0,r=a.F[6]|0;f=a.F[7]|0;for(b=0;64>b;b++){var t=((d>>>2|d<<30)^(d>>>13|d<<19)^(d>>>22|d<<10))+(d&e^d&h^e&h)|0;g=n&p^~n&r;f=f+((n>>>
6|n<<26)^(n>>>11|n<<21)^(n>>>25|n<<7))|0;g=g+(Ld[b]|0)|0;g=f+(g+(c[b]|0)|0)|0;f=r;r=p;p=n;n=k+g|0;k=h;h=e;e=d;d=g+t|0}a.F[0]=a.F[0]+d|0;a.F[1]=a.F[1]+e|0;a.F[2]=a.F[2]+h|0;a.F[3]=a.F[3]+k|0;a.F[4]=a.F[4]+n|0;a.F[5]=a.F[5]+p|0;a.F[6]=a.F[6]+r|0;a.F[7]=a.F[7]+f|0};
Nd.prototype.update=function(a,b){void 0===b&&(b=a.length);var c=0,d=this.qc;if("string"===typeof a)for(;c<b;)this.Vd[d++]=a.charCodeAt(c++),d==this.blockSize&&(Rd(this),d=0);else if(ua(a))for(;c<b;){var e=a[c++];if(!("number"==typeof e&&0<=e&&255>=e&&e==(e|0)))throw Error("message must be a byte array");this.Vd[d++]=e;d==this.blockSize&&(Rd(this),d=0)}else throw Error("message must be string or array");this.qc=d;this.Te+=b};
Nd.prototype.digest=function(){var a=[],b=8*this.Te;56>this.qc?this.update(Qd,56-this.qc):this.update(Qd,this.blockSize-(this.qc-56));for(var c=63;56<=c;c--)this.Vd[c]=b&255,b/=256;Rd(this);for(c=b=0;c<this.Hj;c++)for(var d=24;0<=d;d-=8)a[b++]=this.F[c]>>d&255;return a};
var Md=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298];var Td=function(){Nd.call(this,8,Sd)};w(Td,Nd);var Sd=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Ud=function(){this.Wc=this.Wc;this.ye=this.ye};Ud.prototype.Wc=!1;Ud.prototype.isDisposed=function(){return this.Wc};Ud.prototype.Hb=function(){this.Wc||(this.Wc=!0,this.Vc())};Ud.prototype.Vc=function(){if(this.ye)for(;this.ye.length;)this.ye.shift()()};var G=function(a,b){this.type=a;this.currentTarget=this.target=b;this.defaultPrevented=this.rd=!1};G.prototype.stopPropagation=function(){this.rd=!0};G.prototype.preventDefault=function(){this.defaultPrevented=!0};var Vd=function(){if(!q.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{q.addEventListener("test",function(){},b),q.removeEventListener("test",function(){},b)}catch(c){}return a}();var Wd=function(a,b){G.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.qa=null;a&&this.init(a,b)};w(Wd,G);var Xd=Ac({2:"touch",3:"pen",4:"mouse"});
Wd.prototype.init=function(a,b){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=b;if(b=a.relatedTarget){if(Ic){a:{try{Bc(b.nodeName);var e=!0;break a}catch(f){}e=!1}e||(b=null)}}else"mouseover"==c?b=a.fromElement:"mouseout"==c&&(b=a.toElement);this.relatedTarget=b;d?(this.clientX=void 0!==d.clientX?d.clientX:d.pageX,this.clientY=void 0!==d.clientY?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=
d.screenY||0):(this.offsetX=Jc||void 0!==a.offsetX?a.offsetX:a.layerX,this.offsetY=Jc||void 0!==a.offsetY?a.offsetY:a.layerY,this.clientX=void 0!==a.clientX?a.clientX:a.pageX,this.clientY=void 0!==a.clientY?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||("keypress"==c?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.pointerId=
a.pointerId||0;this.pointerType="string"===typeof a.pointerType?a.pointerType:Xd[a.pointerType]||"";this.state=a.state;this.qa=a;a.defaultPrevented&&Wd.Xb.preventDefault.call(this)};Wd.prototype.stopPropagation=function(){Wd.Xb.stopPropagation.call(this);this.qa.stopPropagation?this.qa.stopPropagation():this.qa.cancelBubble=!0};Wd.prototype.preventDefault=function(){Wd.Xb.preventDefault.call(this);var a=this.qa;a.preventDefault?a.preventDefault():a.returnValue=!1};Wd.prototype.bj=function(){return this.qa};var Yd="closure_listenable_"+(1E6*Math.random()|0);var Zd=0;var $d=function(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.ge=e;this.key=++Zd;this.wd=this.Rd=!1},ae=function(a){a.wd=!0;a.listener=null;a.proxy=null;a.src=null;a.ge=null};var be=function(a){this.src=a;this.ia={};this.Fd=0};be.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.ia[f];a||(a=this.ia[f]=[],this.Fd++);var g=ce(a,b,d,e);-1<g?(b=a[g],c||(b.Rd=!1)):(b=new $d(b,this.src,f,!!d,e),b.Rd=c,a.push(b));return b};be.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.ia))return!1;var e=this.ia[a];b=ce(e,b,c,d);return-1<b?(ae(e[b]),Oa(e,b),0==e.length&&(delete this.ia[a],this.Fd--),!0):!1};
var de=function(a,b){var c=b.type;c in a.ia&&Na(a.ia[c],b)&&(ae(b),0==a.ia[c].length&&(delete a.ia[c],a.Fd--))};be.prototype.Ef=function(a,b,c,d){a=this.ia[a.toString()];var e=-1;a&&(e=ce(a,b,c,d));return-1<e?a[e]:null};be.prototype.hasListener=function(a,b){var c=void 0!==a,d=c?a.toString():"",e=void 0!==b;return Sa(this.ia,function(f){for(var g=0;g<f.length;++g)if(!(c&&f[g].type!=d||e&&f[g].capture!=b))return!0;return!1})};
var ce=function(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.wd&&f.listener==b&&f.capture==!!c&&f.ge==d)return e}return-1};var ee="closure_lm_"+(1E6*Math.random()|0),fe={},ge=0,ie=function(a,b,c,d,e){if(d&&d.once)return he(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)ie(a,b[f],c,d,e);return null}c=je(c);return a&&a[Yd]?a.listen(b,c,u(d)?!!d.capture:!!d,e):ke(a,b,c,!1,d,e)},ke=function(a,b,c,d,e,f){if(!b)throw Error("Invalid event type");var g=u(e)?!!e.capture:!!e,h=le(a);h||(a[ee]=h=new be(a));c=h.add(b,c,d,g,f);if(c.proxy)return c;d=me();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)Vd||(e=g),void 0===
e&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(ne(b.toString()),d);else if(a.addListener&&a.removeListener)x("change"===b,"MediaQueryList only has a change event"),a.addListener(d);else throw Error("addEventListener and attachEvent are unavailable.");ge++;return c},me=function(){var a=pe,b=function(c){return a.call(b.src,b.listener,c)};return b},he=function(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)he(a,b[f],c,d,e);return null}c=je(c);return a&&
a[Yd]?qe(a,b,c,u(d)?!!d.capture:!!d,e):ke(a,b,c,!0,d,e)},re=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)re(a,b[f],c,d,e);else d=u(d)?!!d.capture:!!d,c=je(c),a&&a[Yd]?a.Ea.remove(String(b),c,d,e):a&&(a=le(a))&&(b=a.Ef(b,c,d,e))&&se(b)},se=function(a){if("number"!==typeof a&&a&&!a.wd){var b=a.src;if(b&&b[Yd])de(b.Ea,a);else{var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(ne(c),d):b.addListener&&b.removeListener&&b.removeListener(d);
ge--;(c=le(b))?(de(c,a),0==c.Fd&&(c.src=null,b[ee]=null)):ae(a)}}},ne=function(a){return a in fe?fe[a]:fe[a]="on"+a},pe=function(a,b){if(a.wd)a=!0;else{b=new Wd(b,this);var c=a.listener,d=a.ge||a.src;a.Rd&&se(a);a=c.call(d,b)}return a},le=function(a){a=a[ee];return a instanceof be?a:null},te="__closure_events_fn_"+(1E9*Math.random()>>>0),je=function(a){x(a,"Listener can not be null.");if("function"===typeof a)return a;x(a.handleEvent,"An object listener must have handleEvent method.");a[te]||(a[te]=
function(b){return a.handleEvent(b)});return a[te]};var ue=function(){Ud.call(this);this.Ea=new be(this);this.Ki=this;this.Yf=null};w(ue,Ud);ue.prototype[Yd]=!0;l=ue.prototype;l.addEventListener=function(a,b,c,d){ie(this,a,b,c,d)};l.removeEventListener=function(a,b,c,d){re(this,a,b,c,d)};
l.dispatchEvent=function(a){ve(this);var b=this.Yf;if(b){var c=[];for(var d=1;b;b=b.Yf)c.push(b),x(1E3>++d,"infinite loop")}b=this.Ki;d=a.type||a;if("string"===typeof a)a=new G(a,b);else if(a instanceof G)a.target=a.target||b;else{var e=a;a=new G(d,b);Wa(a,e)}e=!0;if(c)for(var f=c.length-1;!a.rd&&0<=f;f--){var g=a.currentTarget=c[f];e=we(g,d,!0,a)&&e}a.rd||(g=a.currentTarget=b,e=we(g,d,!0,a)&&e,a.rd||(e=we(g,d,!1,a)&&e));if(c)for(f=0;!a.rd&&f<c.length;f++)g=a.currentTarget=c[f],e=we(g,d,!1,a)&&e;
return e};l.Vc=function(){ue.Xb.Vc.call(this);if(this.Ea){var a=this.Ea,b=0,c;for(c in a.ia){for(var d=a.ia[c],e=0;e<d.length;e++)++b,ae(d[e]);delete a.ia[c];a.Fd--}}this.Yf=null};l.listen=function(a,b,c,d){ve(this);return this.Ea.add(String(a),b,!1,c,d)};
var qe=function(a,b,c,d,e){return a.Ea.add(String(b),c,!0,d,e)},we=function(a,b,c,d){b=a.Ea.ia[String(b)];if(!b)return!0;b=b.concat();for(var e=!0,f=0;f<b.length;++f){var g=b[f];if(g&&!g.wd&&g.capture==c){var h=g.listener,k=g.ge||g.src;g.Rd&&de(a.Ea,g);e=!1!==h.call(k,d)&&e}}return e&&!d.defaultPrevented};ue.prototype.Ef=function(a,b,c,d){return this.Ea.Ef(String(a),b,c,d)};ue.prototype.hasListener=function(a,b){return this.Ea.hasListener(void 0!==a?String(a):void 0,b)};
var ve=function(a){x(a.Ea,"Event target is not initialized. Did you call the superclass (goog.events.EventTarget) constructor?")};var xe=function(){};xe.prototype.Mg=null;var ye=function(a){return a.Mg||(a.Mg=a.me())};var ze,Ae=function(){};w(Ae,xe);Ae.prototype.Qc=function(){var a=Be(this);return a?new ActiveXObject(a):new XMLHttpRequest};Ae.prototype.me=function(){var a={};Be(this)&&(a[0]=!0,a[1]=!0);return a};
var Be=function(a){if(!a.wh&&"undefined"==typeof XMLHttpRequest&&"undefined"!=typeof ActiveXObject){for(var b=["MSXML2.XMLHTTP.6.0","MSXML2.XMLHTTP.3.0","MSXML2.XMLHTTP","Microsoft.XMLHTTP"],c=0;c<b.length;c++){var d=b[c];try{return new ActiveXObject(d),a.wh=d}catch(e){}}throw Error("Could not create ActiveXObject. ActiveX might be disabled, or MSXML might not be installed");}return a.wh};ze=new Ae;var Ce=function(){};w(Ce,xe);Ce.prototype.Qc=function(){var a=new XMLHttpRequest;if("withCredentials"in a)return a;if("undefined"!=typeof XDomainRequest)return new De;throw Error("Unsupported browser");};Ce.prototype.me=function(){return{}};
var De=function(){this.Oa=new XDomainRequest;this.readyState=0;this.onreadystatechange=null;this.responseType=this.responseText=this.response="";this.status=-1;this.responseXML=null;this.statusText="";this.Oa.onload=v(this.hj,this);this.Oa.onerror=v(this.qh,this);this.Oa.onprogress=v(this.ij,this);this.Oa.ontimeout=v(this.mj,this)};l=De.prototype;l.open=function(a,b,c){if(null!=c&&!c)throw Error("Only async requests are supported.");this.Oa.open(a,b)};
l.send=function(a){if(a)if("string"==typeof a)this.Oa.send(a);else throw Error("Only string data is supported");else this.Oa.send()};l.abort=function(){this.Oa.abort()};l.setRequestHeader=function(){};l.getResponseHeader=function(a){return"content-type"==a.toLowerCase()?this.Oa.contentType:""};l.hj=function(){this.status=200;this.response=this.responseText=this.Oa.responseText;Ee(this,4)};l.qh=function(){this.status=500;this.response=this.responseText="";Ee(this,4)};l.mj=function(){this.qh()};
l.ij=function(){this.status=200;Ee(this,1)};var Ee=function(a,b){a.readyState=b;if(a.onreadystatechange)a.onreadystatechange()};De.prototype.getAllResponseHeaders=function(){return"content-type: "+this.Oa.contentType};var Fe=function(a,b){this.name=a;this.value=b};Fe.prototype.toString=function(){return this.name};var Ge=new Fe("OFF",Infinity),He=new Fe("SEVERE",1E3),Ie=new Fe("WARNING",900),Je=new Fe("CONFIG",700),Ke=new Fe("FINE",500),Le=function(){this.Td=0;this.clear()},Me;Le.prototype.clear=function(){this.Kg=Array(this.Td);this.Yg=-1;this.Bh=!1};var Ne=function(a,b,c){this.reset(a||Ge,b,c,void 0,void 0)};Ne.prototype.reset=function(){};
var Oe=function(a,b){this.level=null;this.qj=[];this.parent=(void 0===b?null:b)||null;this.children=[];this.Ih={getName:function(){return a}}},Pe=function(a){if(a.level)return a.level;if(a.parent)return Pe(a.parent);Da("Root logger has no level set.");return Ge},Qe=function(a,b){for(;a;)a.qj.forEach(function(c){c(b)}),a=a.parent},Re=function(){this.entries={};var a=new Oe("");a.level=Je;this.entries[""]=a},Se,Te=function(a,b){var c=a.entries[b];if(c)return c;c=Te(a,b.slice(0,Math.max(b.lastIndexOf("."),
0)));var d=new Oe(b,c);a.entries[b]=d;c.children.push(d);return d},Ue=function(){Se||(Se=new Re);return Se},Ve=function(a,b,c){var d;if(d=a)if(d=a&&b){d=b.value;var e=a?Pe(Te(Ue(),a.getName())):Ge;d=d>=e.value}if(d){b=b||Ge;d=Te(Ue(),a.getName());"function"===typeof c&&(c=c());Me||(Me=new Le);e=Me;a=a.getName();if(0<e.Td){var f=(e.Yg+1)%e.Td;e.Yg=f;e.Bh?(e=e.Kg[f],e.reset(b,c,a),a=e):(e.Bh=f==e.Td-1,a=e.Kg[f]=new Ne(b,c,a))}else a=new Ne(b,c,a);Qe(d,a)}},We=function(a,b){a&&Ve(a,He,b)},Xe=function(a,
b){a&&Ve(a,Ke,b)};var Ye=function(a){this.Id=a.Bk||null;this.Bd=a.Tk||!1;this.ec=this.Gb=void 0};w(Ye,xe);Ye.prototype.Qc=function(){var a=new Ze(this.Id,this.Bd);this.Gb&&a.rg(this.Gb);this.ec&&a.ji(this.ec);return a};Ye.prototype.me=function(a){return function(){return a}}({});Ye.prototype.rg=function(a){this.Gb=a};Ye.prototype.ji=function(a){this.ec=a};
var Ze=function(a,b){ue.call(this);this.Id=a;this.Bd=b;this.ec=this.Gb=void 0;this.status=this.readyState=0;this.responseType=this.responseText=this.response=this.statusText="";this.onreadystatechange=this.responseXML=null;this.ng=new Headers;this.Ec=null;this.Kh="GET";this.Yb="";this.Sa=!1;this.P=Te(Ue(),"goog.net.FetchXmlHttp").Ih;this.Ag=this.Rc=this.Zd=null};w(Ze,ue);
Ze.prototype.open=function(a,b,c){x(!!c,"Only async requests are supported.");if(0!=this.readyState)throw this.abort(),Error("Error reopening a connection");this.Kh=a;this.Yb=b;this.readyState=1;$e(this)};Ze.prototype.send=function(a){if(1!=this.readyState)throw this.abort(),Error("need to call open() first. ");this.Sa=!0;var b={headers:this.ng,method:this.Kh,credentials:this.Gb,cache:this.ec};a&&(b.body=a);(this.Id||q).fetch(new Request(this.Yb,b)).then(this.lj.bind(this),this.fe.bind(this))};
Ze.prototype.abort=function(){var a=this;this.response=this.responseText="";this.ng=new Headers;this.status=0;this.Rc&&this.Rc.cancel("Request was aborted.").catch(function(){var b=a.P;b&&Ve(b,Ie,"Fetch reader cancellation error.")});1<=this.readyState&&this.Sa&&4!=this.readyState&&(this.Sa=!1,af(this));this.readyState=0};
Ze.prototype.lj=function(a){if(this.Sa&&(this.Zd=a,this.Ec||(this.status=this.Zd.status,this.statusText=this.Zd.statusText,this.Ec=a.headers,this.readyState=2,$e(this)),this.Sa&&(this.readyState=3,$e(this),this.Sa)))if("arraybuffer"===this.responseType)a.arrayBuffer().then(this.jj.bind(this),this.fe.bind(this));else if("undefined"!==typeof q.ReadableStream&&"body"in a){this.Rc=a.body.getReader();if(this.Bd){if(this.responseType)throw Error('responseType must be empty for "streamBinaryChunks" mode responses.');
this.response=[]}else this.response=this.responseText="",this.Ag=new TextDecoder;bf(this)}else a.text().then(this.kj.bind(this),this.fe.bind(this))};var bf=function(a){a.Rc.read().then(a.fj.bind(a)).catch(a.fe.bind(a))};Ze.prototype.fj=function(a){if(this.Sa){if(this.Bd&&a.value)this.response.push(a.value);else if(!this.Bd){var b=a.value?a.value:new Uint8Array(0);if(b=this.Ag.decode(b,{stream:!a.done}))this.response=this.responseText+=b}a.done?af(this):$e(this);3==this.readyState&&bf(this)}};
Ze.prototype.kj=function(a){this.Sa&&(this.response=this.responseText=a,af(this))};Ze.prototype.jj=function(a){this.Sa&&(this.response=a,af(this))};Ze.prototype.fe=function(){var a=this.P;a&&Ve(a,Ie,"Failed to fetch url "+this.Yb);this.Sa&&af(this)};var af=function(a){a.readyState=4;a.Zd=null;a.Rc=null;a.Ag=null;$e(a)};l=Ze.prototype;l.setRequestHeader=function(a,b){this.ng.append(a,b)};
l.getResponseHeader=function(a){return this.Ec?this.Ec.get(a.toLowerCase())||"":((a=this.P)&&Ve(a,Ie,"Attempting to get response header but no headers have been received for url: "+this.Yb),"")};l.getAllResponseHeaders=function(){if(!this.Ec){var a=this.P;a&&Ve(a,Ie,"Attempting to get all response headers but no headers have been received for url: "+this.Yb);return""}a=[];for(var b=this.Ec.entries(),c=b.next();!c.done;)c=c.value,a.push(c[0]+": "+c[1]),c=b.next();return a.join("\r\n")};
l.rg=function(a){this.Gb=a};l.ji=function(a){this.ec=a};var $e=function(a){a.onreadystatechange&&a.onreadystatechange.call(a)};Object.defineProperty(Ze.prototype,"withCredentials",{get:function(){return"include"===this.Gb},set:function(a){this.rg(a?"include":"same-origin")}});var cf=function(a,b){this.Cj=100;this.Ri=a;this.Tj=b;this.we=0;this.ra=null};cf.prototype.get=function(){if(0<this.we){this.we--;var a=this.ra;this.ra=a.next;a.next=null}else a=this.Ri();return a};cf.prototype.put=function(a){this.Tj(a);this.we<this.Cj&&(this.we++,a.next=this.ra,this.ra=a)};var df,ef=function(){var a=q.MessageChannel;"undefined"===typeof a&&"undefined"!==typeof window&&window.postMessage&&window.addEventListener&&!B("Presto")&&(a=function(){var e=ld(document,"IFRAME");e.style.display="none";document.documentElement.appendChild(e);var f=e.contentWindow;e=f.document;e.open();e.close();var g="callImmediate"+Math.random(),h="file:"==f.location.protocol?"*":f.location.protocol+"//"+f.location.host;e=v(function(k){if(("*"==h||k.origin==h)&&k.data==g)this.port1.onmessage()},
this);f.addEventListener("message",e,!1);this.port1={};this.port2={postMessage:function(){f.postMessage(g,h)}}});if("undefined"!==typeof a&&!B("Trident")&&!B("MSIE")){var b=new a,c={},d=c;b.port1.onmessage=function(){if(void 0!==c.next){c=c.next;var e=c.Pg;c.Pg=null;e()}};return function(e){d.next={Pg:e};d=d.next;b.port2.postMessage(0)}}return function(e){q.setTimeout(e,0)}};function ff(a){q.setTimeout(function(){throw a;},0)};var gf=function(){this.Ye=this.bc=null};gf.prototype.add=function(a,b){var c=hf.get();c.set(a,b);this.Ye?this.Ye.next=c:(x(!this.bc),this.bc=c);this.Ye=c};gf.prototype.remove=function(){var a=null;this.bc&&(a=this.bc,this.bc=this.bc.next,this.bc||(this.Ye=null),a.next=null);return a};var hf=new cf(function(){return new jf},function(a){return a.reset()}),jf=function(){this.next=this.scope=this.Cf=null};jf.prototype.set=function(a,b){this.Cf=a;this.scope=b;this.next=null};
jf.prototype.reset=function(){this.next=this.scope=this.Cf=null};var kf,lf=!1,mf=new gf,of=function(a,b){kf||nf();lf||(kf(),lf=!0);mf.add(a,b)},nf=function(){if(q.Promise&&q.Promise.resolve){var a=q.Promise.resolve(void 0);kf=function(){a.then(pf)}}else kf=function(){var b=pf;"function"!==typeof q.setImmediate||q.Window&&q.Window.prototype&&!B("Edge")&&q.Window.prototype.setImmediate==q.setImmediate?(df||(df=ef()),df(b)):q.setImmediate(b)}},pf=function(){for(var a;a=mf.remove();){try{a.Cf.call(a.scope)}catch(b){ff(b)}hf.put(a)}lf=!1};function qf(){};var rf=function(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};var H=function(a,b){this.R=0;this.ma=void 0;this.fc=this.Za=this.ta=null;this.ee=this.yf=!1;if(a!=Pb)try{var c=this;a.call(b,function(d){sf(c,2,d)},function(d){if(!(d instanceof tf))try{if(d instanceof Error)throw d;throw Error("Promise rejected.");}catch(e){}sf(c,3,d)})}catch(d){sf(this,3,d)}},uf=function(){this.next=this.context=this.tc=this.Rb=this.child=null;this.Mc=!1};uf.prototype.reset=function(){this.context=this.tc=this.Rb=this.child=null;this.Mc=!1};
var vf=new cf(function(){return new uf},function(a){a.reset()}),wf=function(a,b,c){var d=vf.get();d.Rb=a;d.tc=b;d.context=c;return d},I=function(a){if(a instanceof H)return a;var b=new H(Pb);sf(b,2,a);return b},J=function(a){return new H(function(b,c){c(a)})},yf=function(a,b,c){xf(a,b,c,null)||of(xa(b,a))},zf=function(a){return new H(function(b,c){var d=a.length,e=[];if(d)for(var f=function(n,p){d--;e[n]=p;0==d&&b(e)},g=function(n){c(n)},h=0,k;h<a.length;h++)k=a[h],yf(k,xa(f,h),g);else b(e)})},Af=
function(a){return new H(function(b){var c=a.length,d=[];if(c)for(var e=function(h,k,n){c--;d[h]=k?{mh:!0,value:n}:{mh:!1,reason:n};0==c&&b(d)},f=0,g;f<a.length;f++)g=a[f],yf(g,xa(e,f,!0),xa(e,f,!1));else b(d)})},Bf=function(a){return new H(function(b,c){var d=a.length,e=[];if(d)for(var f=function(n){b(n)},g=function(n,p){d--;e[n]=p;0==d&&c(e)},h=0,k;h<a.length;h++)k=a[h],yf(k,f,xa(g,h));else b(void 0)})};
H.prototype.then=function(a,b,c){null!=a&&Ga(a,"opt_onFulfilled should be a function.");null!=b&&Ga(b,"opt_onRejected should be a function. Did you pass opt_context as the second argument instead of the third?");return Cf(this,"function"===typeof a?a:null,"function"===typeof b?b:null,c)};H.prototype.$goog_Thenable=!0;H.prototype.Bb=function(a,b){a=wf(a,a,b);a.Mc=!0;Df(this,a);return this};H.prototype.h=function(a,b){return Cf(this,null,a,b)};H.prototype.catch=H.prototype.h;
H.prototype.cancel=function(a){if(0==this.R){var b=new tf(a);of(function(){Ef(this,b)},this)}};
var Ef=function(a,b){if(0==a.R)if(a.ta){var c=a.ta;if(c.Za){for(var d=0,e=null,f=null,g=c.Za;g&&(g.Mc||(d++,g.child==a&&(e=g),!(e&&1<d)));g=g.next)e||(f=g);e&&(0==c.R&&1==d?Ef(c,b):(f?(d=f,x(c.Za),x(null!=d),d.next==c.fc&&(c.fc=d),d.next=d.next.next):Ff(c),Gf(c,e,3,b)))}a.ta=null}else sf(a,3,b)},Df=function(a,b){a.Za||2!=a.R&&3!=a.R||Hf(a);x(null!=b.Rb);a.fc?a.fc.next=b:a.Za=b;a.fc=b},Cf=function(a,b,c,d){var e=wf(null,null,null);e.child=new H(function(f,g){e.Rb=b?function(h){try{var k=b.call(d,h);
f(k)}catch(n){g(n)}}:f;e.tc=c?function(h){try{var k=c.call(d,h);void 0===k&&h instanceof tf?g(h):f(k)}catch(n){g(n)}}:g});e.child.ta=a;Df(a,e);return e.child};H.prototype.tk=function(a){x(1==this.R);this.R=0;sf(this,2,a)};H.prototype.vk=function(a){x(1==this.R);this.R=0;sf(this,3,a)};
var sf=function(a,b,c){0==a.R&&(a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself")),a.R=1,xf(c,a.tk,a.vk,a)||(a.ma=c,a.R=b,a.ta=null,Hf(a),3!=b||c instanceof tf||If(a,c)))},xf=function(a,b,c,d){if(a instanceof H)return null!=b&&Ga(b,"opt_onFulfilled should be a function."),null!=c&&Ga(c,"opt_onRejected should be a function. Did you pass opt_context as the second argument instead of the third?"),Df(a,wf(b||Pb,c||null,d)),!0;if(rf(a))return a.then(b,c,d),!0;if(u(a))try{var e=a.then;if("function"===
typeof e)return Jf(a,e,b,c,d),!0}catch(f){return c.call(d,f),!0}return!1},Jf=function(a,b,c,d,e){var f=!1,g=function(k){f||(f=!0,c.call(e,k))},h=function(k){f||(f=!0,d.call(e,k))};try{b.call(a,g,h)}catch(k){h(k)}},Hf=function(a){a.yf||(a.yf=!0,of(a.Xi,a))},Ff=function(a){var b=null;a.Za&&(b=a.Za,a.Za=b.next,b.next=null);a.Za||(a.fc=null);null!=b&&x(null!=b.Rb);return b};H.prototype.Xi=function(){for(var a;a=Ff(this);)Gf(this,a,this.R,this.ma);this.yf=!1};
var Gf=function(a,b,c,d){if(3==c&&b.tc&&!b.Mc)for(;a&&a.ee;a=a.ta)a.ee=!1;if(b.child)b.child.ta=null,Kf(b,c,d);else try{b.Mc?b.Rb.call(b.context):Kf(b,c,d)}catch(e){Lf.call(null,e)}vf.put(b)},Kf=function(a,b,c){2==b?a.Rb.call(a.context,c):a.tc&&a.tc.call(a.context,c)},If=function(a,b){a.ee=!0;of(function(){a.ee&&Lf.call(null,b)})},Lf=ff,tf=function(a){za.call(this,a)};w(tf,za);tf.prototype.name="cancel";/*

 Copyright 2005, 2007 Bob Ippolito. All Rights Reserved.
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: MIT
*/
var Mf=function(a,b){this.Me=[];this.Nh=a;this.dh=b||null;this.dd=this.mc=!1;this.ma=void 0;this.wg=this.Jg=this.hf=!1;this.Ue=0;this.ta=null;this.jf=0};w(Mf,qf);Mf.prototype.cancel=function(a){if(this.mc)this.ma instanceof Mf&&this.ma.cancel();else{if(this.ta){var b=this.ta;delete this.ta;a?b.cancel(a):(b.jf--,0>=b.jf&&b.cancel())}this.Nh?this.Nh.call(this.dh,this):this.wg=!0;this.mc||Nf(this,new Of(this))}};Mf.prototype.Vg=function(a,b){this.hf=!1;Pf(this,a,b)};
var Pf=function(a,b,c){a.mc=!0;a.ma=c;a.dd=!b;Qf(a)},Sf=function(a){if(a.mc){if(!a.wg)throw new Rf(a);a.wg=!1}};Mf.prototype.callback=function(a){Sf(this);Tf(a);Pf(this,!0,a)};var Nf=function(a,b){Sf(a);Tf(b);Pf(a,!1,b)},Tf=function(a){x(!(a instanceof Mf),"An execution sequence may not be initiated with a blocking Deferred.")},Vf=function(a,b){Uf(a,null,b)},Uf=function(a,b,c,d){x(!a.Jg,"Blocking Deferreds can not be re-used");a.Me.push([b,c,d]);a.mc&&Qf(a)};
Mf.prototype.then=function(a,b,c){var d,e,f=new H(function(g,h){e=g;d=h});Uf(this,e,function(g){g instanceof Of?f.cancel():d(g);return Wf},this);return f.then(a,b,c)};Mf.prototype.$goog_Thenable=!0;
var Xf=function(a){return La(a.Me,function(b){return"function"===typeof b[1]})},Wf={},Qf=function(a){if(a.Ue&&a.mc&&Xf(a)){var b=a.Ue,c=Yf[b];c&&(q.clearTimeout(c.Fa),delete Yf[b]);a.Ue=0}a.ta&&(a.ta.jf--,delete a.ta);b=a.ma;for(var d=c=!1;a.Me.length&&!a.hf;){var e=a.Me.shift(),f=e[0],g=e[1];e=e[2];if(f=a.dd?g:f)try{var h=f.call(e||a.dh,b);h===Wf&&(h=void 0);void 0!==h&&(a.dd=a.dd&&(h==b||h instanceof Error),a.ma=b=h);if(rf(b)||"function"===typeof q.Promise&&b instanceof q.Promise)d=!0,a.hf=!0}catch(k){b=
k,a.dd=!0,Xf(a)||(c=!0)}}a.ma=b;d&&(h=v(a.Vg,a,!0),d=v(a.Vg,a,!1),b instanceof Mf?(Uf(b,h,d),b.Jg=!0):b.then(h,d));c&&(b=new Zf(b),Yf[b.Fa]=b,a.Ue=b.Fa)},Rf=function(){za.call(this)};w(Rf,za);Rf.prototype.message="Deferred has already fired";Rf.prototype.name="AlreadyCalledError";var Of=function(){za.call(this)};w(Of,za);Of.prototype.message="Deferred was canceled";Of.prototype.name="CanceledError";var Zf=function(a){this.Fa=q.setTimeout(v(this.rk,this),0);this.Y=a};
Zf.prototype.rk=function(){x(Yf[this.Fa],"Cannot throw an error that is not scheduled.");delete Yf[this.Fa];throw this.Y;};var Yf={};var dg=function(a){var b={},c=b.document||document,d=fb(a).toString(),e=(new xd(c)).createElement("SCRIPT"),f={ci:e,Ed:void 0},g=new Mf($f,f),h=null,k=null!=b.timeout?b.timeout:5E3;0<k&&(h=window.setTimeout(function(){ag(e,!0);Nf(g,new bg(1,"Timeout reached for loading script "+d))},k),f.Ed=h);e.onload=e.onreadystatechange=function(){e.readyState&&"loaded"!=e.readyState&&"complete"!=e.readyState||(ag(e,b.Qk||!1,h),g.callback(null))};e.onerror=function(){ag(e,!0,h);Nf(g,new bg(0,"Error while loading script "+
d))};f=b.attributes||{};Wa(f,{type:"text/javascript",charset:"UTF-8"});id(e,f);Tb(e,a);cg(c).appendChild(e);return g},cg=function(a){var b=(a||document).getElementsByTagName("HEAD");return b&&0!==b.length?b[0]:a.documentElement},$f=function(){if(this&&this.ci){var a=this.ci;a&&"SCRIPT"==a.tagName&&ag(a,!0,this.Ed)}},ag=function(a,b,c){null!=c&&q.clearTimeout(c);a.onload=function(){};a.onerror=function(){};a.onreadystatechange=function(){};b&&window.setTimeout(function(){pd(a)},0)},bg=function(a,b){var c=
"Jsloader error (code #"+a+")";b&&(c+=": "+b);za.call(this,c);this.code=a};w(bg,za);var eg=function(a,b,c){if("function"===typeof a)c&&(a=v(a,c));else if(a&&"function"==typeof a.handleEvent)a=v(a.handleEvent,a);else throw Error("Invalid listener argument");return 2147483647<Number(b)?-1:q.setTimeout(a,b||0)},fg=function(a){var b=null;return(new H(function(c,d){b=eg(function(){c(void 0)},a);-1==b&&d(Error("Failed to schedule timer."))})).h(function(c){q.clearTimeout(b);throw c;})};var K=function(a){ue.call(this);this.headers=new Map;this.af=a||null;this.lb=!1;this.Ze=this.j=null;this.jd=this.Gh=this.qe="";this.Nb=this.Kf=this.le=this.wf=!1;this.Gc=0;this.Re=null;this.He="";this.Ve=this.Pj=this.Fi=!1;this.Bg=null};w(K,ue);K.prototype.P=Te(Ue(),"goog.net.XhrIo").Ih;var gg=/^https?$/i,hg=["POST","PUT"];K.prototype.setTrustToken=function(a){this.Bg=a};
K.prototype.send=function(a,b,c,d){if(this.j)throw Error("[goog.net.XhrIo] Object is active with another request="+this.qe+"; newUri="+a);b=b?b.toUpperCase():"GET";this.qe=a;this.jd="";this.Gh=b;this.wf=!1;this.lb=!0;this.j=this.af?this.af.Qc():ze.Qc();this.Ze=this.af?ye(this.af):ye(ze);this.j.onreadystatechange=v(this.Uh,this);this.Pj&&"onprogress"in this.j&&(this.j.onprogress=v(function(g){this.Th(g,!0)},this),this.j.upload&&(this.j.upload.onprogress=v(this.Th,this)));try{Xe(this.P,ig(this,"Opening Xhr")),
this.Kf=!0,this.j.open(b,String(a),!0),this.Kf=!1}catch(g){Xe(this.P,ig(this,"Error opening Xhr: "+g.message));this.Y(5,g);return}a=c||"";c=new Map(this.headers);if(d)if(Object.getPrototypeOf(d)===Object.prototype)for(var e in d)c.set(e,d[e]);else if("function"===typeof d.keys&&"function"===typeof d.get){e=ia(d.keys());for(var f=e.next();!f.done;f=e.next())f=f.value,c.set(f,d.get(f))}else throw Error("Unknown input type for opt_headers: "+String(d));d=Array.from(c.keys()).find(function(g){return"content-type"==
g.toLowerCase()});e=q.FormData&&a instanceof q.FormData;!Ma(hg,b)||d||e||c.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");b=ia(c);for(d=b.next();!d.done;d=b.next())c=ia(d.value),d=c.next().value,c=c.next().value,this.j.setRequestHeader(d,c);this.He&&(this.j.responseType=this.He);"withCredentials"in this.j&&this.j.withCredentials!==this.Fi&&(this.j.withCredentials=this.Fi);if("setTrustToken"in this.j&&this.Bg)try{this.j.setTrustToken(this.Bg)}catch(g){Xe(this.P,ig(this,"Error SetTrustToken: "+
g.message))}try{jg(this),0<this.Gc&&(this.Ve=kg(this.j),Xe(this.P,ig(this,"Will abort after "+this.Gc+"ms if incomplete, xhr2 "+this.Ve)),this.Ve?(this.j.timeout=this.Gc,this.j.ontimeout=v(this.Ed,this)):this.Re=eg(this.Ed,this.Gc,this)),Xe(this.P,ig(this,"Sending request")),this.le=!0,this.j.send(a),this.le=!1}catch(g){Xe(this.P,ig(this,"Send error: "+g.message)),this.Y(5,g)}};var kg=function(a){return Fc&&Qc()&&"number"===typeof a.timeout&&void 0!==a.ontimeout};
K.prototype.Ed=function(){"undefined"!=typeof sa&&this.j&&(this.jd="Timed out after "+this.Gc+"ms, aborting",Xe(this.P,ig(this,this.jd)),this.dispatchEvent("timeout"),this.abort(8))};K.prototype.Y=function(a,b){this.lb=!1;this.j&&(this.Nb=!0,this.j.abort(),this.Nb=!1);this.jd=b;lg(this);mg(this)};var lg=function(a){a.wf||(a.wf=!0,a.dispatchEvent("complete"),a.dispatchEvent("error"))};
K.prototype.abort=function(){this.j&&this.lb&&(Xe(this.P,ig(this,"Aborting")),this.lb=!1,this.Nb=!0,this.j.abort(),this.Nb=!1,this.dispatchEvent("complete"),this.dispatchEvent("abort"),mg(this))};K.prototype.Vc=function(){this.j&&(this.lb&&(this.lb=!1,this.Nb=!0,this.j.abort(),this.Nb=!1),mg(this,!0));K.Xb.Vc.call(this)};K.prototype.Uh=function(){this.isDisposed()||(this.Kf||this.le||this.Nb?ng(this):this.Kj())};K.prototype.Kj=function(){ng(this)};
var ng=function(a){if(a.lb&&"undefined"!=typeof sa)if(a.Ze[1]&&4==og(a)&&2==pg(a))Xe(a.P,ig(a,"Local request error detected and ignored"));else if(a.le&&4==og(a))eg(a.Uh,0,a);else if(a.dispatchEvent("readystatechange"),4==og(a)){Xe(a.P,ig(a,"Request complete"));a.lb=!1;try{var b=pg(a);a:switch(b){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var c=!0;break a;default:c=!1}var d;if(!(d=c)){var e;if(e=0===b){var f=String(a.qe).match($b)[1]||null;!f&&q.self&&q.self.location&&(f=q.self.location.protocol.slice(0,
-1));e=!gg.test(f?f.toLowerCase():"")}d=e}if(d)a.dispatchEvent("complete"),a.dispatchEvent("success");else{try{var g=2<og(a)?a.j.statusText:""}catch(h){Xe(a.P,"Can not get status: "+h.message),g=""}a.jd=g+" ["+pg(a)+"]";lg(a)}}finally{mg(a)}}};K.prototype.Th=function(a,b){x("progress"===a.type,"goog.net.EventType.PROGRESS is of the same type as raw XHR progress.");this.dispatchEvent(qg(a,"progress"));this.dispatchEvent(qg(a,b?"downloadprogress":"uploadprogress"))};
var qg=function(a,b){return{type:b,lengthComputable:a.lengthComputable,loaded:a.loaded,total:a.total}},mg=function(a,b){if(a.j){jg(a);var c=a.j,d=a.Ze[0]?function(){}:null;a.j=null;a.Ze=null;b||a.dispatchEvent("ready");try{c.onreadystatechange=d}catch(e){We(a.P,"Problem encountered resetting onreadystatechange: "+e.message)}}},jg=function(a){a.j&&a.Ve&&(a.j.ontimeout=null);a.Re&&(q.clearTimeout(a.Re),a.Re=null)};K.prototype.isActive=function(){return!!this.j};
var og=function(a){return a.j?a.j.readyState:0},pg=function(a){try{return 2<og(a)?a.j.status:-1}catch(b){return-1}};
K.prototype.getResponse=function(){try{if(!this.j)return null;if("response"in this.j)return this.j.response;switch(this.He){case "":case "text":return this.j.responseText;case "arraybuffer":if("mozResponseArrayBuffer"in this.j)return this.j.mozResponseArrayBuffer}We(this.P,"Response type "+this.He+" is not supported on this browser");return null}catch(a){return Xe(this.P,"Can not get response: "+a.message),null}};
K.prototype.getResponseHeader=function(a){if(this.j&&4==og(this))return a=this.j.getResponseHeader(a),null===a?void 0:a};K.prototype.getAllResponseHeaders=function(){return this.j&&2<=og(this)?this.j.getAllResponseHeaders()||"":""};var ig=function(a,b){return b+" ["+a.Gh+" "+a.qe+" "+pg(a)+"]"};var rg={Ek:{ae:"https://staging-identitytoolkit.sandbox.googleapis.com/identitytoolkit/v3/relyingparty/",Je:"https://staging-securetoken.sandbox.googleapis.com/v1/token",ie:"https://staging-identitytoolkit.sandbox.googleapis.com/v2/",id:"b"},Lk:{ae:"https://www.googleapis.com/identitytoolkit/v3/relyingparty/",Je:"https://securetoken.googleapis.com/v1/token",ie:"https://identitytoolkit.googleapis.com/v2/",id:"p"},Mk:{ae:"https://staging-www.sandbox.googleapis.com/identitytoolkit/v3/relyingparty/",
Je:"https://staging-securetoken.sandbox.googleapis.com/v1/token",ie:"https://staging-identitytoolkit.sandbox.googleapis.com/v2/",id:"s"},Nk:{ae:"https://www-googleapis-test.sandbox.google.com/identitytoolkit/v3/relyingparty/",Je:"https://test-securetoken.sandbox.googleapis.com/v1/token",ie:"https://test-identitytoolkit.sandbox.googleapis.com/v2/",id:"t"}},sg=function(a){for(var b in rg)if(rg[b].id===a)return a=rg[b],{firebaseEndpoint:a.ae,secureTokenEndpoint:a.Je,identityPlatformEndpoint:a.ie};return null},
tg;tg=sg("__EID__")?"__EID__":void 0;var ug=function(){var a=L();return Fc&&!!Tc&&11==Tc||/Edge\/\d+/.test(a)},vg=function(){return q.window&&q.window.location.href||self&&self.location&&self.location.href||""},wg=function(a,b){b=b||q.window;var c="about:blank";a&&(c=xb(Ab(a)||Cb));b.location.href=c},xg=function(a,b,c){b=b||q.window;c?b.location.replace(a):b.location.replace(xb(Ab(a)||Cb))},yg=function(a,b){var c=[],d;for(d in a)d in b?typeof a[d]!=typeof b[d]?c.push(d):"object"==typeof a[d]&&null!=a[d]&&null!=b[d]?0<yg(a[d],b[d]).length&&
c.push(d):a[d]!==b[d]&&c.push(d):c.push(d);for(d in b)d in a||c.push(d);return c},Ag=function(){var a=L();a="Chrome"!=zg(a)?null:(a=a.match(/\sChrome\/(\d+)/i))&&2==a.length?parseInt(a[1],10):null;return a&&30>a?!1:!Fc||!Tc||9<Tc},Bg=function(a){a=(a||L()).toLowerCase();return a.match(/android/)||a.match(/webos/)||a.match(/iphone|ipad|ipod/)||a.match(/blackberry/)||a.match(/windows phone/)||a.match(/iemobile/)?!0:!1},Cg=function(a){a=a||q.window;try{a.close()}catch(b){}},Dg=function(a,b,c){var d=
Math.floor(1E9*Math.random()).toString();b=b||500;c=c||600;var e=(window.screen.availHeight-c)/2,f=(window.screen.availWidth-b)/2;b={width:b,height:c,top:0<e?e:0,left:0<f?f:0,location:!0,resizable:!0,statusbar:!0,toolbar:!1};c=L().toLowerCase();d&&(b.target=d,A(c,"crios/")&&(b.target="_blank"));"Firefox"==zg(L())&&(a=a||"http://localhost",b.scrollbars=!0);e=a||"";b||(b={});a=window;d=e instanceof wb?e:Ab("undefined"!=typeof e.href?e.href:String(e))||Cb;f=void 0!==self.crossOriginIsolated;c="strict-origin-when-cross-origin";
window.Request&&(c=(new Request("/")).referrerPolicy);var g="unsafe-url"===c;c=b.noreferrer;if(f&&c){if(g)throw Error("Cannot use the noreferrer option on a page that sets a referrer-policy of `unsafe-url` in modern browsers!");c=!1}e=b.target||e.target;f=[];for(h in b)switch(h){case "width":case "height":case "top":case "left":f.push(h+"="+b[h]);break;case "target":case "noopener":case "noreferrer":break;default:f.push(h+"="+(b[h]?1:0))}var h=f.join(",");(B("iPhone")&&!B("iPod")&&!B("iPad")||B("iPad")||
B("iPod"))&&a.navigator&&a.navigator.standalone&&e&&"_self"!=e?(h=ld(document,"A"),Ob(h,"HTMLAnchorElement"),d=d instanceof wb?d:Bb(d),h.href=xb(d),h.target=e,c&&(h.rel="noreferrer"),d=document.createEvent("MouseEvent"),d.initMouseEvent("click",!0,!0,a,1),h.dispatchEvent(d),h={}):c?(h=Ub("",a,e,h),d=xb(d),h&&(Hc&&A(d,";")&&(d="'"+d.replace(/'/g,"%27")+"'"),h.opener=null,""===d&&(d="javascript:''"),a=cb("b/12014412, meta tag with sanitized URL"),d=tb(d),a=Nb(a,'<meta name="referrer" content="no-referrer"><meta http-equiv="refresh" content="0; url='+
d+'">'),(d=h.document)&&d.write&&(d.write(Jb(a)),d.close()))):((h=Ub(d,a,e,h))&&b.noopener&&(h.opener=null),h&&b.noreferrer&&(h.opener=null));if(h)try{h.focus()}catch(k){}return h},Eg=function(){var a=L().match(/OS (\d+)_.*CriOS\/(\d+)\./i);return a&&2<a.length?10<=parseInt(a[1],10)&&55<=parseInt(a[2],10):!1},Fg=function(a){return new H(function(b){var c=function(){fg(2E3).then(function(){if(!a||a.closed)b();else return c()})};return c()})},Hg=function(a,b){var c=E(b);b=c.ua;c=c.ga;for(var d=0;d<
a.length;d++){var e=a[d];var f=c;var g=b;0==e.indexOf("chrome-extension://")?f=E(e).ga==f&&"chrome-extension"==g:"http"!=g&&"https"!=g?f=!1:Gg.test(e)?f=f==e:(e=e.split(".").join("\\."),f=(new RegExp("^(.+\\."+e+"|"+e+")$","i")).test(f));if(f)return!0}return!1},Gg=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,Ig=/^[^@]+@[^@]+$/,Jg=function(){var a=null;return(new H(function(b){"complete"==q.document.readyState?b():(a=function(){b()},he(window,"load",a))})).h(function(b){re(window,"load",a);throw b;})},Lg=
function(){return Kg()?Jg().then(function(){return new H(function(a,b){var c=q.document,d=setTimeout(function(){b(Error("Cordova framework is not ready."))},1E3);c.addEventListener("deviceready",function(){clearTimeout(d);a()},!1)})}):J(Error("Cordova must run in an Android or iOS file scheme."))},Kg=function(){var a=L();return!("file:"!==Mg()&&"ionic:"!==Mg()||!a.toLowerCase().match(/iphone|ipad|ipod|android/))},Ng=function(a){a=a||q.window;try{return!(!a||a==a.top)}catch(b){return!1}},Og=function(){var a=
q.window;try{var b=a&&a.opener;return!(!b||!Ng(b))}catch(c){return!1}},Pg=function(){return"undefined"!==typeof q.WorkerGlobalScope&&"function"===typeof q.importScripts},Qg=function(){return firebase.INTERNAL.hasOwnProperty("reactNative")?"ReactNative":firebase.INTERNAL.hasOwnProperty("node")?"Node":Pg()?"Worker":"Browser"},Rg=function(){var a=Qg();return"ReactNative"===a||"Node"===a},Sg=function(){for(var a=50,b=[];0<a;)b.push("1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ".charAt(Math.floor(62*
Math.random()))),a--;return b.join("")},zg=function(a){var b=a.toLowerCase();if(A(b,"opera/")||A(b,"opr/")||A(b,"opios/"))return"Opera";if(A(b,"iemobile"))return"IEMobile";if(A(b,"msie")||A(b,"trident/"))return"IE";if(A(b,"edge/"))return"Edge";if(A(b,"firefox/"))return"Firefox";if(A(b,"silk/"))return"Silk";if(A(b,"blackberry"))return"Blackberry";if(A(b,"webos"))return"Webos";if(!A(b,"safari/")||A(b,"chrome/")||A(b,"crios/")||A(b,"android"))if(!A(b,"chrome/")&&!A(b,"crios/")||A(b,"edge/")){if(A(b,
"android"))return"Android";if((a=a.match(RegExp("([a-zA-Z\\d\\.]+)/[a-zA-Z\\d\\.]*$")))&&2==a.length)return a[1]}else return"Chrome";else return"Safari";return"Other"},Tg={Fk:"FirebaseCore-web",Hk:"FirebaseUI-web",Kk:"gcip-iap"},Ug=function(a,b,c){c=c||[];var d=[],e={},f;for(f in Tg)e[Tg[f]]=!0;for(f=0;f<c.length;f++)"undefined"!==typeof e[c[f]]&&(delete e[c[f]],d.push(c[f]));d.sort();c=d;c.length||(c=["FirebaseCore-web"]);d=Qg();"Browser"===d?(e=L(),d=zg(e)):"Worker"===d&&(e=L(),d=zg(e)+"-"+d);return d+
"/"+a+"/"+b+"/"+c.join(",")},L=function(){return q.navigator&&q.navigator.userAgent||""},M=function(a,b){a=a.split(".");b=b||q;for(var c=0;c<a.length&&"object"==typeof b&&null!=b;c++)b=b[a[c]];c!=a.length&&(b=void 0);return b},Wg=function(){try{var a=q.localStorage,b=Vg();if(a)return a.setItem(b,"1"),a.removeItem(b),ug()?!!q.indexedDB:!0}catch(c){return Pg()&&!!q.indexedDB}return!1},Yg=function(){return(Xg()||"chrome-extension:"===Mg()||Kg())&&!Rg()&&Wg()&&!Pg()},Xg=function(){return"http:"===Mg()||
"https:"===Mg()},Mg=function(){return q.location&&q.location.protocol||null},Zg=function(a){a=a||L();return Bg(a)||"Firefox"==zg(a)?!1:!0},$g=function(a){return"undefined"===typeof a?null:JSON.stringify(a)},ah=function(a){var b={},c;for(c in a)a.hasOwnProperty(c)&&null!==a[c]&&void 0!==a[c]&&(b[c]=a[c]);return b},bh=function(a,b){a=Ua(a);for(var c=0;c<b.length;c++){var d=b[c];d in a&&delete a[d]}return a},ch=function(a){if(null!==a)return JSON.parse(a)},Vg=function(a){return a?a:""+Math.floor(1E9*
Math.random()).toString()},dh=function(a){a=a||L();return"Safari"==zg(a)||a.toLowerCase().match(/iphone|ipad|ipod/)?!1:!0},eh=function(){var a=q.___jsl;if(a&&a.H)for(var b in a.H)if(a.H[b].r=a.H[b].r||[],a.H[b].L=a.H[b].L||[],a.H[b].r=a.H[b].L.concat(),a.CP)for(var c=0;c<a.CP.length;c++)a.CP[c]=null},fh=function(a,b){if(a>b)throw Error("Short delay should be less than long delay!");this.ni=a;this.Fj=b;a=L();b=Qg();this.yj=Bg(a)||"ReactNative"===b};
fh.prototype.get=function(){var a=q.navigator;return(a&&"boolean"===typeof a.onLine&&(Xg()||"chrome-extension:"===Mg()||"undefined"!==typeof a.connection)?a.onLine:1)?this.yj?this.Fj:this.ni:Math.min(5E3,this.ni)};
var gh=function(){var a=q.document;return a&&"undefined"!==typeof a.visibilityState?"visible"==a.visibilityState:!0},hh=function(){var a=q.document,b=null;return gh()||!a?I():(new H(function(c){b=function(){gh()&&(a.removeEventListener("visibilitychange",b,!1),c())};a.addEventListener("visibilitychange",b,!1)})).h(function(c){a.removeEventListener("visibilitychange",b,!1);throw c;})},ih=function(a){"undefined"!==typeof console&&"function"===typeof console.error&&console.error(a)},jh=function(a){try{var b=
new Date(parseInt(a,10));if(!isNaN(b.getTime())&&!/[^0-9]/.test(a))return b.toUTCString()}catch(c){}return null},mh=function(){return!(!M("fireauth.oauthhelper",q)&&!M("fireauth.iframe",q))},nh=function(){if(q.document)try{var a=kd("META",{name:"referrer",content:"no-referrer"}),b=document.getElementsByTagName("HEAD");b.length&&b[0].appendChild(a)}catch(c){}},oh=function(){var a=q.navigator;return a&&a.serviceWorker&&a.serviceWorker.controller||null},ph=function(){var a=q.navigator;return a&&a.serviceWorker?
I().then(function(){return a.serviceWorker.ready}).then(function(b){return b.active||null}).h(function(){return null}):I(null)},qh=function(a){var b={};a.split("&").forEach(function(c){c=c.split("=");1<c.length&&(b[decodeURIComponent(c[0])]=decodeURIComponent(c[1]))});return b};var rh={},sh=function(a){rh[a]||(rh[a]=!0,"undefined"!==typeof console&&"function"===typeof console.warn&&console.warn(a))};var th;try{var uh={};Object.defineProperty(uh,"abcd",{configurable:!0,enumerable:!0,value:1});Object.defineProperty(uh,"abcd",{configurable:!0,enumerable:!0,value:2});th=2==uh.abcd}catch(a){th=!1}
var N=function(a,b,c){th?Object.defineProperty(a,b,{configurable:!0,enumerable:!0,value:c}):a[b]=c},O=function(a,b){if(b)for(var c in b)b.hasOwnProperty(c)&&N(a,c,b[c])},vh=function(a){var b={};O(b,a);return b},wh=function(a,b){if(!b||!b.length)return!0;if(!a)return!1;for(var c=0;c<b.length;c++){var d=a[b[c]];if(void 0===d||null===d||""===d)return!1}return!0},xh=function(a){var b=a;if("object"==typeof a&&null!=a){b="length"in a?[]:{};for(var c in a)N(b,c,xh(a[c]))}return b};var yh="oauth_consumer_key oauth_nonce oauth_signature oauth_signature_method oauth_timestamp oauth_token oauth_version".split(" "),zh=["client_id","response_type","scope","redirect_uri","state"],Ah={Gk:{hd:"locale",xc:700,wc:600,providerId:"facebook.com",xd:zh},Ik:{hd:null,xc:500,wc:750,providerId:"github.com",xd:zh},Jk:{hd:"hl",xc:515,wc:680,providerId:"google.com",xd:zh},Ok:{hd:"lang",xc:485,wc:705,providerId:"twitter.com",xd:yh},Dk:{hd:"locale",xc:640,wc:600,providerId:"apple.com",xd:[]}},Bh=
function(a){for(var b in Ah)if(Ah[b].providerId==a)return Ah[b];return null},Ch=function(a){return(a=Bh(a))&&a.xd||[]},Dh=function(a){return"string"===typeof a&&0==a.indexOf("saml.")};var P=function(a,b,c){this.code="auth/"+a;this.message=b||Eh[a]||"";this.ei=c||null};w(P,Error);P.prototype.m=function(){var a={code:this.code,message:this.message};this.ei&&(a.serverResponse=this.ei);return a};P.prototype.toJSON=function(){return this.m()};
var Fh=function(a){var b=a&&a.code;return b?new P(b.substring(5),a.message,a.serverResponse):null},Eh={"admin-restricted-operation":"This operation is restricted to administrators only.","argument-error":"","app-not-authorized":"This app, identified by the domain where it's hosted, is not authorized to use Firebase Authentication with the provided API key. Review your key configuration in the Google API console.","app-not-installed":"The requested mobile application corresponding to the identifier (Android package name or iOS bundle ID) provided is not installed on this device.",
"bad-request":"The requested action is invalid.","captcha-check-failed":"The reCAPTCHA response token provided is either invalid, expired, already used or the domain associated with it does not match the list of whitelisted domains.","code-expired":"The SMS code has expired. Please re-send the verification code to try again.","cordova-not-ready":"Cordova framework is not ready.","cors-unsupported":"This browser is not supported.","credential-already-in-use":"This credential is already associated with a different user account.",
"custom-token-mismatch":"The custom token corresponds to a different audience.","requires-recent-login":"This operation is sensitive and requires recent authentication. Log in again before retrying this request.","dynamic-link-not-activated":"Please activate Dynamic Links in the Firebase Console and agree to the terms and conditions.","email-change-needs-verification":"Multi-factor users must always have a verified email.","email-already-in-use":"The email address is already in use by another account.",
"expired-action-code":"The action code has expired. ","cancelled-popup-request":"This operation has been cancelled due to another conflicting popup being opened.","internal-error":"An internal error has occurred.","invalid-app-credential":"The phone verification request contains an invalid application verifier. The reCAPTCHA token response is either invalid or expired.","invalid-app-id":"The mobile app identifier is not registed for the current project.","invalid-user-token":"This user's credential isn't valid for this project. This can happen if the user's token has been tampered with, or if the user isn't for the project associated with this API key.",
"invalid-auth-event":"An internal error has occurred.","invalid-verification-code":"The SMS verification code used to create the phone auth credential is invalid. Please resend the verification code sms and be sure to use the verification code provided by the user.","invalid-continue-uri":"The continue URL provided in the request is invalid.","invalid-cordova-configuration":"The following Cordova plugins must be installed to enable OAuth sign-in: cordova-plugin-buildinfo, cordova-universal-links-plugin, cordova-plugin-browsertab, cordova-plugin-inappbrowser and cordova-plugin-customurlscheme.",
"invalid-custom-token":"The custom token format is incorrect. Please check the documentation.","invalid-dynamic-link-domain":"The provided dynamic link domain is not configured or authorized for the current project.","invalid-email":"The email address is badly formatted.","invalid-api-key":"Your API key is invalid, please check you have copied it correctly.","invalid-cert-hash":"The SHA-1 certificate hash provided is invalid.","invalid-credential":"The supplied auth credential is malformed or has expired.",
"invalid-message-payload":"The email template corresponding to this action contains invalid characters in its message. Please fix by going to the Auth email templates section in the Firebase Console.","invalid-multi-factor-session":"The request does not contain a valid proof of first factor successful sign-in.","invalid-oauth-provider":"EmailAuthProvider is not supported for this operation. This operation only supports OAuth providers.","invalid-oauth-client-id":"The OAuth client ID provided is either invalid or does not match the specified API key.",
"unauthorized-domain":"This domain is not authorized for OAuth operations for your Firebase project. Edit the list of authorized domains from the Firebase console.","invalid-action-code":"The action code is invalid. This can happen if the code is malformed, expired, or has already been used.","wrong-password":"The password is invalid or the user does not have a password.","invalid-persistence-type":"The specified persistence type is invalid. It can only be local, session or none.","invalid-phone-number":"The format of the phone number provided is incorrect. Please enter the phone number in a format that can be parsed into E.164 format. E.164 phone numbers are written in the format [+][country code][subscriber number including area code].",
"invalid-provider-id":"The specified provider ID is invalid.","invalid-recipient-email":"The email corresponding to this action failed to send as the provided recipient email address is invalid.","invalid-sender":"The email template corresponding to this action contains an invalid sender email or name. Please fix by going to the Auth email templates section in the Firebase Console.","invalid-verification-id":"The verification ID used to create the phone auth credential is invalid.","invalid-tenant-id":"The Auth instance's tenant ID is invalid.",
"multi-factor-info-not-found":"The user does not have a second factor matching the identifier provided.","multi-factor-auth-required":"Proof of ownership of a second factor is required to complete sign-in.","missing-android-pkg-name":"An Android Package Name must be provided if the Android App is required to be installed.","auth-domain-config-required":"Be sure to include authDomain when calling firebase.initializeApp(), by following the instructions in the Firebase console.","missing-app-credential":"The phone verification request is missing an application verifier assertion. A reCAPTCHA response token needs to be provided.",
"missing-verification-code":"The phone auth credential was created with an empty SMS verification code.","missing-continue-uri":"A continue URL must be provided in the request.","missing-iframe-start":"An internal error has occurred.","missing-ios-bundle-id":"An iOS Bundle ID must be provided if an App Store ID is provided.","missing-multi-factor-info":"No second factor identifier is provided.","missing-multi-factor-session":"The request is missing proof of first factor successful sign-in.","missing-or-invalid-nonce":"The request does not contain a valid nonce. This can occur if the SHA-256 hash of the provided raw nonce does not match the hashed nonce in the ID token payload.",
"missing-phone-number":"To send verification codes, provide a phone number for the recipient.","missing-verification-id":"The phone auth credential was created with an empty verification ID.","app-deleted":"This instance of FirebaseApp has been deleted.","account-exists-with-different-credential":"An account already exists with the same email address but different sign-in credentials. Sign in using a provider associated with this email address.","network-request-failed":"A network error (such as timeout, interrupted connection or unreachable host) has occurred.",
"no-auth-event":"An internal error has occurred.","no-such-provider":"User was not linked to an account with the given provider.","null-user":"A null user object was provided as the argument for an operation which requires a non-null user object.","operation-not-allowed":"The given sign-in provider is disabled for this Firebase project. Enable it in the Firebase console, under the sign-in method tab of the Auth section.","operation-not-supported-in-this-environment":'This operation is not supported in the environment this application is running on. "location.protocol" must be http, https or chrome-extension and web storage must be enabled.',
"password-does-not-meet-requirements":"The provided password does not meet the configured requirements.","popup-blocked":"Unable to establish a connection with the popup. It may have been blocked by the browser.","popup-closed-by-user":"The popup has been closed by the user before finalizing the operation.","provider-already-linked":"User can only be linked to one identity for the given provider.","quota-exceeded":"The project's quota for this operation has been exceeded.","redirect-cancelled-by-user":"The redirect operation has been cancelled by the user before finalizing.",
"redirect-operation-pending":"A redirect sign-in operation is already pending.","rejected-credential":"The request contains malformed or mismatching credentials.","second-factor-already-in-use":"The second factor is already enrolled on this account.","maximum-second-factor-count-exceeded":"The maximum allowed number of second factors on a user has been exceeded.","tenant-id-mismatch":"The provided tenant ID does not match the Auth instance's tenant ID",timeout:"The operation has timed out.","user-token-expired":"The user's credential is no longer valid. The user must sign in again.",
"too-many-requests":"We have blocked all requests from this device due to unusual activity. Try again later.","unauthorized-continue-uri":"The domain of the continue URL is not whitelisted.  Please whitelist the domain in the Firebase console.","unsupported-first-factor":"Enrolling a second factor or signing in with a multi-factor account requires sign-in with a supported first factor.","unsupported-persistence-type":"The current environment does not support the specified persistence type.","unsupported-tenant-operation":"This operation is not supported in a multi-tenant context.",
"unverified-email":"The operation requires a verified email.","user-cancelled":"The user did not grant your application the permissions it requested.","user-not-found":"There is no user record corresponding to this identifier. The user may have been deleted.","user-disabled":"The user account has been disabled by an administrator.","user-mismatch":"The supplied credentials do not correspond to the previously signed in user.","user-signed-out":"","weak-password":"The password must be 6 characters long or more.",
"web-storage-unsupported":"This browser is not supported or 3rd party cookies and data may be disabled."};var Gh=function(a,b,c,d,e,f,g){this.Ic=a;this.T=b||null;this.Xa=c||null;this.zd=d||null;this.dg=f||null;this.C=g||null;this.Y=e||null;if(this.Xa||this.Y){if(this.Xa&&this.Y)throw new P("invalid-auth-event");if(this.Xa&&!this.zd)throw new P("invalid-auth-event");}else throw new P("invalid-auth-event");};l=Gh.prototype;l.getType=function(){return this.Ic};l.getUid=function(){var a=[];a.push(this.Ic);this.T&&a.push(this.T);this.zd&&a.push(this.zd);this.C&&a.push(this.C);return a.join("-")};l.nc=function(){return this.zd};
l.getError=function(){return this.Y};l.m=function(){return{type:this.Ic,eventId:this.T,urlResponse:this.Xa,sessionId:this.zd,postBody:this.dg,tenantId:this.C,error:this.Y&&this.Y.m()}};var Hh=function(a){a=a||{};return a.type?new Gh(a.type,a.eventId,a.urlResponse,a.sessionId,a.error&&Fh(a.error),a.postBody,a.tenantId):null};var Ih=function(a,b,c,d,e,f,g,h,k,n,p,r,t,z,aa,mb,qa){this.A=a;this.B=b;this.Ic=c;this.T=d||null;this.Cc=e||null;this.Qa=f||null;this.tf=g||null;this.dc=h||null;this.ed=k||null;this.fh=n||null;this.U=p||[];this.Fb=r||null;this.mi=t||null;this.C=z||null;this.zc=aa||null;this.Kb=mb||null;this.Yh=qa||null};Ih.prototype.getType=function(){return this.Ic};
Ih.prototype.m=function(){return{apiKey:this.A,appName:this.B,type:this.Ic,eventId:this.T,redirectUrl:this.Cc,clientVersion:this.Qa,displayName:this.tf,apn:this.dc,ibi:this.ed,eid:this.fh,fw:this.U,clientId:this.Fb,sha1Cert:this.mi,tenantId:this.C,providerId:this.zc,appId:this.Kb,publicKey:this.Yh}};var Jh=function(a){var b=a&&(a.phoneInfo?"phone":null);if(b&&a&&a.mfaEnrollmentId){N(this,"uid",a.mfaEnrollmentId);N(this,"displayName",a.displayName||null);var c=null;a.enrolledAt&&(c=(new Date(a.enrolledAt)).toUTCString());N(this,"enrollmentTime",c);N(this,"factorId",b)}else throw new P("internal-error","Internal assert: invalid MultiFactorInfo object");};Jh.prototype.m=function(){return{uid:this.uid,displayName:this.displayName,factorId:this.factorId,enrollmentTime:this.enrollmentTime}};
var Lh=function(a){try{var b=new Kh(a)}catch(c){b=null}return b},Kh=function(a){Jh.call(this,a);N(this,"phoneNumber",a.phoneInfo)};w(Kh,Jh);Kh.prototype.m=function(){var a=Kh.Xb.m.call(this);a.phoneNumber=this.phoneNumber;return a};var Mh=function(a){var b={},c=a.email,d=a.newEmail,e=a.requestType;a=Lh(a.mfaInfo);if(!e||"EMAIL_SIGNIN"!=e&&"VERIFY_AND_CHANGE_EMAIL"!=e&&!c||"VERIFY_AND_CHANGE_EMAIL"==e&&!d||"REVERT_SECOND_FACTOR_ADDITION"==e&&!a)throw Error("Invalid checkActionCode response!");"VERIFY_AND_CHANGE_EMAIL"==e?(b.fromEmail=c||null,b.previousEmail=c||null,b.email=d):(b.fromEmail=d||null,b.previousEmail=d||null,b.email=c||null);b.multiFactorInfo=a||null;N(this,"operation",e);N(this,"data",xh(b))};var Oh=function(a){a=E(a);var b=D(a,"apiKey")||null,c=D(a,"oobCode")||null,d=D(a,"mode")||null;d=d?Nh[d]||null:null;if(!b||!c||!d)throw new P("argument-error","apiKey, oobCodeand mode are required in a valid action code URL.");O(this,{apiKey:b,operation:d,code:c,continueUrl:D(a,"continueUrl")||null,languageCode:D(a,"languageCode")||null,tenantId:D(a,"tenantId")||null})},Ph=function(a){try{return new Oh(a)}catch(b){return null}},Nh={recoverEmail:"RECOVER_EMAIL",resetPassword:"PASSWORD_RESET",revertSecondFactorAddition:"REVERT_SECOND_FACTOR_ADDITION",
signIn:"EMAIL_SIGNIN",verifyAndChangeEmail:"VERIFY_AND_CHANGE_EMAIL",verifyEmail:"VERIFY_EMAIL"};var Qh=function(a,b,c,d,e,f,g){this.hh="https://"+d+"/__/auth/handler?firebaseError="+encodeURIComponent($g((new P("app-not-installed")).m()));N(this,"fallbackUrl",this.hh);this.Bf=a;N(this,"fdlDomain",a);this.Be=b;N(this,"platform",b);this.Ld=c;N(this,"appIdentifier",c);this.J=d;N(this,"authDomain",d);this.re=e;N(this,"payload",e);this.B=null;N(this,"appName",null);this.Fb=f||null;N(this,"clientId",this.Fb);this.Kb=g||null;N(this,"firebaseAppId",this.Kb)},Rh=function(a,b){a.B=b||null;N(a,"appName",
b)},Sh=function(a){var b=E(a);a=D(b,"fdlDomain");var c=D(b,"platform"),d=D(b,"appIdentifier"),e=D(b,"authDomain"),f=D(b,"link");b=D(b,"appName");return a&&c&&d&&e&&f&&b?(a=new Qh(a,c,d,e,f),Rh(a,b),a):null},Th=function(a){var b=E(a),c=D(b,"link"),d=D(E(c),"link");b=D(b,"deep_link_id");return D(E(b),"link")||b||d||c||a},Uh=function(a,b){var c=rc("https",a.Bf,null,"/");"android"==a.Be?(C(c,"apn",a.Ld),C(c,"afl",b)):"ios"==a.Be&&(C(c,"ibi",a.Ld),C(c,"ifl",b));return c};
Qh.prototype.toString=function(a){if("android_non_gmscore"==this.Be)a=this.re;else if(this.Bf)if(a){a=rc("https",this.J,null,"/__/auth/callback");C(a,"fdlDomain",this.Bf);C(a,"platform",this.Be);C(a,"appIdentifier",this.Ld);C(a,"authDomain",this.J);C(a,"link",this.re);C(a,"appName",this.B||"");var b=Uh(this,a.toString());C(b,"link",a.toString());a=b.toString()}else a=Uh(this,this.hh),C(a,"link",this.re),a=a.toString();else a=this.Fb?this.Fb.split(".").reverse().join("."):this.Kb?"app-"+this.Kb.replace(/:/g,
"-"):this.Ld,a=rc(a,this.Fb||this.Kb?"firebaseauth":"google",null,"/link"),C(a,"deep_link_id",this.re),a=a.toString();return a};var Vh=function(a){var b="unauthorized-domain",c=void 0,d=E(a);a=d.ga;d=d.ua;"chrome-extension"==d?c=Wb("This chrome extension ID (chrome-extension://%s) is not authorized to run this operation. Add it to the OAuth redirect domains list in the Firebase console -> Auth section -> Sign in method tab.",a):"http"==d||"https"==d?c=Wb("This domain (%s) is not authorized to run this operation. Add it to the OAuth redirect domains list in the Firebase console -> Auth section -> Sign in method tab.",a):b=
"operation-not-supported-in-this-environment";P.call(this,b,c)};m(Vh,P);var Xh=function(a){var b=Wh(a);if(!(b&&b.sub&&b.iss&&b.aud&&b.exp))throw Error("Invalid JWT");this.Bj=a;this.zf=b.exp;this.Dj=b.sub;a=Date.now()/1E3;this.rj=b.iat||(a>this.zf?this.zf:a);this.Ib=b.email||null;this.zc=b.provider_id||b.firebase&&b.firebase.sign_in_provider||null;this.C=b.firebase&&b.firebase.tenant||null;this.Mi=!!b.is_anonymous||"anonymous"==this.zc;this.tf=b.display_name||null};Xh.prototype.getEmail=function(){return this.Ib};Xh.prototype.isAnonymous=function(){return this.Mi};
Xh.prototype.toString=function(){return this.Bj};
var Yh=function(a){try{return new Xh(a)}catch(b){return null}},Wh=function(a){if(!a)return null;a=a.split(".");if(3!=a.length)return null;a=a[1];for(var b=(4-a.length%4)%4,c=0;c<b;c++)a+=".";try{var d=Id(a);a=[];for(c=b=0;b<d.length;){var e=d[b++];if(128>e)a[c++]=String.fromCharCode(e);else if(191<e&&224>e){var f=d[b++];a[c++]=String.fromCharCode((e&31)<<6|f&63)}else if(239<e&&365>e){f=d[b++];var g=d[b++],h=d[b++],k=((e&7)<<18|(f&63)<<12|(g&63)<<6|h&63)-65536;a[c++]=String.fromCharCode(55296+(k>>
10));a[c++]=String.fromCharCode(56320+(k&1023))}else f=d[b++],g=d[b++],a[c++]=String.fromCharCode((e&15)<<12|(f&63)<<6|g&63)}return JSON.parse(a.join(""))}catch(n){}return null};var Zh=function(a){var b=Wh(a);if(!(b&&b.exp&&b.auth_time&&b.iat))throw new P("internal-error","An internal error occurred. The token obtained by Firebase appears to be malformed. Please retry the operation.");O(this,{token:a,expirationTime:jh(1E3*b.exp),authTime:jh(1E3*b.auth_time),issuedAtTime:jh(1E3*b.iat),signInProvider:b.firebase&&b.firebase.sign_in_provider?b.firebase.sign_in_provider:null,signInSecondFactor:b.firebase&&b.firebase.sign_in_second_factor?b.firebase.sign_in_second_factor:null,
claims:b})};var $h=function(a,b){if(!a&&!b)throw new P("internal-error","Internal assert: no raw session string available");if(a&&b)throw new P("internal-error","Internal assert: unable to determine the session type");this.he=a||null;this.Lh=b||null;this.type=this.he?"enroll":"signin"};$h.prototype.bd=function(){return this.he?I(this.he):I(this.Lh)};$h.prototype.m=function(){return"enroll"==this.type?{multiFactorSession:{idToken:this.he}}:{multiFactorSession:{pendingCredential:this.Lh}}};var ai=function(){};ai.prototype.Mb=function(){};ai.prototype.sc=function(){};ai.prototype.kd=function(){};ai.prototype.m=function(){};
var bi=function(a,b){return a.then(function(c){if(c.idToken){var d=Yh(c.idToken);if(!d||b!=d.Dj)throw new P("user-mismatch");return c}throw new P("user-mismatch");}).h(function(c){throw c&&c.code&&"auth/user-not-found"==c.code?new P("user-mismatch"):c;})},ci=function(a,b){if(b)this.gb=b;else throw new P("internal-error","failed to construct a credential");N(this,"providerId",a);N(this,"signInMethod",a)};l=ci.prototype;l.Mb=function(a){return di(a,this.Pb())};
l.sc=function(a,b){var c=this.Pb();c.idToken=b;return ei(a,c)};l.kd=function(a,b){var c=this.Pb();return bi(fi(a,c),b)};l.Pb=function(){return{pendingToken:this.gb,requestUri:"http://localhost"}};l.m=function(){return{providerId:this.providerId,signInMethod:this.signInMethod,pendingToken:this.gb}};
var gi=function(a){if(a&&a.providerId&&a.signInMethod&&0==a.providerId.indexOf("saml.")&&a.pendingToken)try{return new ci(a.providerId,a.pendingToken)}catch(b){}return null},hi=function(a,b,c){this.gb=null;if(b.idToken||b.accessToken)b.idToken&&N(this,"idToken",b.idToken),b.accessToken&&N(this,"accessToken",b.accessToken),b.nonce&&!b.pendingToken&&N(this,"nonce",b.nonce),b.pendingToken&&(this.gb=b.pendingToken);else if(b.oauthToken&&b.oauthTokenSecret)N(this,"accessToken",b.oauthToken),N(this,"secret",
b.oauthTokenSecret);else throw new P("internal-error","failed to construct a credential");N(this,"providerId",a);N(this,"signInMethod",c)};l=hi.prototype;l.Mb=function(a){return di(a,this.Pb())};l.sc=function(a,b){var c=this.Pb();c.idToken=b;return ei(a,c)};l.kd=function(a,b){var c=this.Pb();return bi(fi(a,c),b)};
l.Pb=function(){var a={};this.idToken&&(a.id_token=this.idToken);this.accessToken&&(a.access_token=this.accessToken);this.secret&&(a.oauth_token_secret=this.secret);a.providerId=this.providerId;this.nonce&&!this.gb&&(a.nonce=this.nonce);a={postBody:vc(a).toString(),requestUri:"http://localhost"};this.gb&&(delete a.postBody,a.pendingToken=this.gb);return a};
l.m=function(){var a={providerId:this.providerId,signInMethod:this.signInMethod};this.idToken&&(a.oauthIdToken=this.idToken);this.accessToken&&(a.oauthAccessToken=this.accessToken);this.secret&&(a.oauthTokenSecret=this.secret);this.nonce&&(a.nonce=this.nonce);this.gb&&(a.pendingToken=this.gb);return a};
var ii=function(a){if(a&&a.providerId&&a.signInMethod){var b={idToken:a.oauthIdToken,accessToken:a.oauthTokenSecret?null:a.oauthAccessToken,oauthTokenSecret:a.oauthTokenSecret,oauthToken:a.oauthTokenSecret&&a.oauthAccessToken,nonce:a.nonce,pendingToken:a.pendingToken};try{return new hi(a.providerId,b,a.signInMethod)}catch(c){}}return null},ji=function(a,b){this.Sj=b||[];O(this,{providerId:a,isOAuthProvider:!0});this.ah={};this.Of=(Bh(a)||{}).hd||null;this.sf=null};
ji.prototype.setCustomParameters=function(a){this.ah=Ua(a);return this};var ki=function(a){if(!Dh(a))throw new P("argument-error",'SAML provider IDs must be prefixed with "saml."');ji.call(this,a,[])};w(ki,ji);var li=function(a){ji.call(this,a,zh);this.og=[]};w(li,ji);li.prototype.addScope=function(a){Ma(this.og,a)||this.og.push(a);return this};li.prototype.ph=function(){return Qa(this.og)};
li.prototype.credential=function(a,b){a=u(a)?{idToken:a.idToken||null,accessToken:a.accessToken||null,nonce:a.rawNonce||null}:{idToken:a||null,accessToken:b||null};if(!a.idToken&&!a.accessToken)throw new P("argument-error","credential failed: must provide the ID token and/or the access token.");return new hi(this.providerId,a,this.providerId)};var mi=function(){li.call(this,"facebook.com")};w(mi,li);N(mi,"PROVIDER_ID","facebook.com");N(mi,"FACEBOOK_SIGN_IN_METHOD","facebook.com");
var ni=function(a){if(!a)throw new P("argument-error","credential failed: expected 1 argument (the OAuth access token).");var b=a;u(a)&&(b=a.accessToken);return(new mi).credential({accessToken:b})},oi=function(){li.call(this,"github.com")};w(oi,li);N(oi,"PROVIDER_ID","github.com");N(oi,"GITHUB_SIGN_IN_METHOD","github.com");
var pi=function(a){if(!a)throw new P("argument-error","credential failed: expected 1 argument (the OAuth access token).");var b=a;u(a)&&(b=a.accessToken);return(new oi).credential({accessToken:b})},qi=function(){li.call(this,"google.com");this.addScope("profile")};w(qi,li);N(qi,"PROVIDER_ID","google.com");N(qi,"GOOGLE_SIGN_IN_METHOD","google.com");
var ri=function(a,b){var c=a;u(a)&&(c=a.idToken,b=a.accessToken);return(new qi).credential({idToken:c,accessToken:b})},si=function(){ji.call(this,"twitter.com",yh)};w(si,ji);N(si,"PROVIDER_ID","twitter.com");N(si,"TWITTER_SIGN_IN_METHOD","twitter.com");
var ti=function(a,b){var c=a;u(c)||(c={oauthToken:a,oauthTokenSecret:b});if(!c.oauthToken||!c.oauthTokenSecret)throw new P("argument-error","credential failed: expected 2 arguments (the OAuth access token and secret).");return new hi("twitter.com",c,"twitter.com")},vi=function(a,b,c){this.Ib=a;this.ld=b;N(this,"providerId","password");N(this,"signInMethod",c===ui.EMAIL_LINK_SIGN_IN_METHOD?ui.EMAIL_LINK_SIGN_IN_METHOD:ui.EMAIL_PASSWORD_SIGN_IN_METHOD)};
vi.prototype.Mb=function(a){return this.signInMethod==ui.EMAIL_LINK_SIGN_IN_METHOD?Q(a,wi,{email:this.Ib,oobCode:this.ld}):Q(a,xi,{email:this.Ib,password:this.ld})};vi.prototype.sc=function(a,b){return this.signInMethod==ui.EMAIL_LINK_SIGN_IN_METHOD?Q(a,yi,{idToken:b,email:this.Ib,oobCode:this.ld}):Q(a,zi,{idToken:b,email:this.Ib,password:this.ld})};vi.prototype.kd=function(a,b){return bi(this.Mb(a),b)};vi.prototype.m=function(){return{email:this.Ib,password:this.ld,signInMethod:this.signInMethod}};
var Ai=function(a){return a&&a.email&&a.password?new vi(a.email,a.password,a.signInMethod):null},ui=function(){O(this,{providerId:"password",isOAuthProvider:!1})},Ci=function(a,b){b=Bi(b);if(!b)throw new P("argument-error","Invalid email link!");return new vi(a,b.code,ui.EMAIL_LINK_SIGN_IN_METHOD)},Bi=function(a){a=Th(a);return(a=Ph(a))&&"EMAIL_SIGNIN"===a.operation?a:null};O(ui,{PROVIDER_ID:"password"});O(ui,{EMAIL_LINK_SIGN_IN_METHOD:"emailLink"});O(ui,{EMAIL_PASSWORD_SIGN_IN_METHOD:"password"});
var Di=function(a){if(!(a.verificationId&&a.We||a.Dd&&a.phoneNumber))throw new P("internal-error");this.V=a;N(this,"providerId","phone");this.providerId="phone";N(this,"signInMethod","phone")};Di.prototype.Mb=function(a){return a.verifyPhoneNumber(Ei(this))};Di.prototype.sc=function(a,b){var c=Ei(this);c.idToken=b;return Q(a,Fi,c)};Di.prototype.kd=function(a,b){var c=Ei(this);c.operation="REAUTH";a=Q(a,Gi,c);return bi(a,b)};
Di.prototype.m=function(){var a={providerId:"phone"};this.V.verificationId&&(a.verificationId=this.V.verificationId);this.V.We&&(a.verificationCode=this.V.We);this.V.Dd&&(a.temporaryProof=this.V.Dd);this.V.phoneNumber&&(a.phoneNumber=this.V.phoneNumber);return a};
var Hi=function(a){if(a&&"phone"===a.providerId&&(a.verificationId&&a.verificationCode||a.temporaryProof&&a.phoneNumber)){var b={};y(["verificationId","verificationCode","temporaryProof","phoneNumber"],function(c){a[c]&&(b[c]=a[c])});return new Di(b)}return null},Ei=function(a){return a.V.Dd&&a.V.phoneNumber?{temporaryProof:a.V.Dd,phoneNumber:a.V.phoneNumber}:{sessionInfo:a.V.verificationId,code:a.V.We}},Ii=function(a){try{this.Od=a||firebase.auth()}catch(b){throw new P("argument-error","Either an instance of firebase.auth.Auth must be passed as an argument to the firebase.auth.PhoneAuthProvider constructor, or the default firebase App instance must be initialized via firebase.initializeApp().");
}O(this,{providerId:"phone",isOAuthProvider:!1})};
Ii.prototype.verifyPhoneNumber=function(a,b){var c=this.Od.i;return I(b.verify()).then(function(d){if("string"!==typeof d)throw new P("argument-error","An implementation of firebase.auth.ApplicationVerifier.prototype.verify() must return a firebase.Promise that resolves with a string.");switch(b.type){case "recaptcha":var e=u(a)?a.session:null,f=u(a)?a.phoneNumber:a;return(e&&"enroll"==e.type?e.bd().then(function(g){return Ji(c,{idToken:g,phoneEnrollmentInfo:{phoneNumber:f,recaptchaToken:d}})}):e&&
"signin"==e.type?e.bd().then(function(g){return Ki(c,{mfaPendingCredential:g,mfaEnrollmentId:a.multiFactorHint&&a.multiFactorHint.uid||a.multiFactorUid,phoneSignInInfo:{recaptchaToken:d}})}):Li(c,{phoneNumber:f,recaptchaToken:d})).then(function(g){"function"===typeof b.reset&&b.reset();return g},function(g){"function"===typeof b.reset&&b.reset();throw g;});default:throw new P("argument-error",'Only firebase.auth.ApplicationVerifiers with type="recaptcha" are currently supported.');}})};
var Mi=function(a,b){if(!a)throw new P("missing-verification-id");if(!b)throw new P("missing-verification-code");return new Di({verificationId:a,We:b})};O(Ii,{PROVIDER_ID:"phone"});O(Ii,{PHONE_SIGN_IN_METHOD:"phone"});
var Ni=function(a){if(a.temporaryProof&&a.phoneNumber)return new Di({Dd:a.temporaryProof,phoneNumber:a.phoneNumber});var b=a&&a.providerId;if(!b||"password"===b)return null;var c=a&&a.oauthAccessToken,d=a&&a.oauthTokenSecret,e=a&&a.nonce,f=a&&a.oauthIdToken,g=a&&a.pendingToken;try{switch(b){case "google.com":return ri(f,c);case "facebook.com":return ni(c);case "github.com":return pi(c);case "twitter.com":return ti(c,d);default:return c||d||f||g?g?0==b.indexOf("saml.")?new ci(b,g):new hi(b,{pendingToken:g,
idToken:a.oauthIdToken,accessToken:a.oauthAccessToken},b):(new li(b)).credential({idToken:f,accessToken:c,rawNonce:e}):null}}catch(h){return null}},Oi=function(a){if(!a.isOAuthProvider)throw new P("invalid-oauth-provider");};var Pi=function(a,b,c){P.call(this,a,c);a=b||{};a.email&&N(this,"email",a.email);a.phoneNumber&&N(this,"phoneNumber",a.phoneNumber);a.credential&&N(this,"credential",a.credential);a.tenantId&&N(this,"tenantId",a.tenantId)};m(Pi,P);Pi.prototype.m=function(){var a={code:this.code,message:this.message};this.email&&(a.email=this.email);this.phoneNumber&&(a.phoneNumber=this.phoneNumber);this.tenantId&&(a.tenantId=this.tenantId);var b=this.credential&&this.credential.m();b&&Wa(a,b);return a};
Pi.prototype.toJSON=function(){return this.m()};var Qi=function(a){if(a.code){var b=a.code||"";0==b.indexOf("auth/")&&(b=b.substring(5));var c={credential:Ni(a),tenantId:a.tenantId};if(a.email)c.email=a.email;else if(a.phoneNumber)c.phoneNumber=a.phoneNumber;else if(!c.credential)return new P(b,a.message||void 0);return new Pi(b,c,a.message)}return null};var Ri=function(a){this.Ck=a};m(Ri,xe);Ri.prototype.Qc=function(){return new this.Ck};Ri.prototype.me=function(){return{}};
var Wi=function(a,b,c){this.A=a;b=b||{};this.di=b.secureTokenEndpoint||"https://securetoken.googleapis.com/v1/token";this.ek=b.secureTokenTimeout||Si;this.Ke=Ua(b.secureTokenHeaders||Ti);this.kh=b.firebaseEndpoint||"https://www.googleapis.com/identitytoolkit/v3/relyingparty/";this.vh=b.identityPlatformEndpoint||"https://identitytoolkit.googleapis.com/v2/";this.aj=b.firebaseTimeout||Ui;this.kc=Ua(b.firebaseHeaders||Vi);c&&(this.kc["X-Client-Version"]=c,this.Ke["X-Client-Version"]=c);a="Node"==Qg();
a=q.XMLHttpRequest||a&&firebase.INTERNAL.node&&firebase.INTERNAL.node.XMLHttpRequest;if(!a&&!Pg())throw new P("internal-error","The XMLHttpRequest compatibility library was not found.");this.Ie=void 0;Pg()?this.Ie=new Ye({Bk:self}):Rg()?this.Ie=new Ri(a):this.Ie=new Ce;this.C=null},Xi,Yi=function(a,b){b?a.kc["X-Firebase-Locale"]=b:delete a.kc["X-Firebase-Locale"]},$i=function(a,b){b&&(a.di=Zi("https://securetoken.googleapis.com/v1/token",b),a.kh=Zi("https://www.googleapis.com/identitytoolkit/v3/relyingparty/",
b),a.vh=Zi("https://identitytoolkit.googleapis.com/v2/",b))},Zi=function(a,b){a=E(a);b=E(b.url);gc(a,a.ga+a.fb);cc(a,b.ua);ec(a,b.ga);fc(a,b.Ua);return a.toString()},aj=function(a,b){b?(a.kc["X-Client-Version"]=b,a.Ke["X-Client-Version"]=b):(delete a.kc["X-Client-Version"],delete a.Ke["X-Client-Version"])},cj=function(a,b,c,d,e,f,g){Ag()||Pg()?a=v(a.gk,a):(Xi||(Xi=new H(function(h,k){bj(h,k)})),a=v(a.fk,a));a(b,c,d,e,f,g)};
Wi.prototype.gk=function(a,b,c,d,e,f){if(Pg()&&("undefined"===typeof q.fetch||"undefined"===typeof q.Headers||"undefined"===typeof q.Request))throw new P("operation-not-supported-in-this-environment","fetch, Headers and Request native APIs or equivalent Polyfills must be available to support HTTP requests from a Worker environment.");var g=new K(this.Ie);if(f){g.Gc=Math.max(0,f);var h=setTimeout(function(){g.dispatchEvent("timeout")},f)}g.listen("complete",function(){h&&clearTimeout(h);var k=null;
try{var n=JSON,p=n.parse;try{var r=this.j?this.j.responseText:""}catch(t){Xe(this.P,"Can not get responseText: "+t.message),r=""}k=p.call(n,r)||null}catch(t){k=null}b&&b(k)});qe(g,"ready",function(){h&&clearTimeout(h);this.Hb()});qe(g,"timeout",function(){h&&clearTimeout(h);this.Hb();b&&b(null)});g.send(a,c,d,e)};
var bj=function(a,b){if(((window.gapi||{}).client||{}).request)a();else{q[dj]=function(){((window.gapi||{}).client||{}).request?a():b(Error("CORS_UNSUPPORTED"))};var c=jb(ej,{onload:dj});Vf(dg(c),function(){b(Error("CORS_UNSUPPORTED"))})}};
Wi.prototype.fk=function(a,b,c,d,e){var f=this;Xi.then(function(){window.gapi.client.setApiKey(f.A);var g=window.gapi.auth.getToken();window.gapi.auth.setToken(null);window.gapi.client.request({path:a,method:c,body:d,headers:e,authType:"none",callback:function(h){window.gapi.auth.setToken(g);b&&b(h)}})}).h(function(g){b&&b({error:{message:g&&g.message||"CORS_UNSUPPORTED"}})})};
var gj=function(a,b){return new H(function(c,d){"refresh_token"==b.grant_type&&b.refresh_token||"authorization_code"==b.grant_type&&b.code?cj(a,a.di+"?key="+encodeURIComponent(a.A),function(e){e?e.error?d(fj(e)):e.access_token&&e.refresh_token?c(e):d(new P("internal-error")):d(new P("network-request-failed"))},"POST",vc(b).toString(),a.Ke,a.ek.get()):d(new P("internal-error"))})},hj=function(a,b,c,d,e,f,g){var h=E(b+c);C(h,"key",a.A);g&&C(h,"cb",Date.now().toString());var k="GET"==d;if(k)for(var n in e)e.hasOwnProperty(n)&&
C(h,n,e[n]);return new H(function(p,r){cj(a,h.toString(),function(t){t?t.error?r(fj(t,f||{})):p(t):r(new P("network-request-failed"))},d,k?void 0:JSON.stringify(ah(e)),a.kc,a.aj.get())})},ij=function(a){a=a.email;if("string"!==typeof a||!Ig.test(a))throw new P("invalid-email");},jj=function(a){"email"in a&&ij(a)},lj=function(a,b,c,d,e,f){var g=Dh(b);var h={};e&&e.length?(h[b]=e.join(","),e=JSON.stringify(h)):e=null;c={identifier:null,providerId:b,continueUri:c,customParameter:d||{},oauthScope:e,sessionId:f};
g&&(delete c.customParameter,delete c.oauthScope);f&&"google.com"==b&&(c.authFlowType="CODE_FLOW");return Q(a,kj,c)},nj=function(a,b){return Q(a,mj,{identifier:b,continueUri:Xg()?vg():"http://localhost"}).then(function(c){return c.signinMethods||[]})},pj=function(a){return Q(a,oj,{}).then(function(b){return b.authorizedDomains||[]})},qj=function(a,b){return Q(a,oj,{iosBundleId:b}).then(function(){})},rj=function(a,b,c){b={androidPackageName:b};c&&(b.sha1Cert=c);return Q(a,oj,b).then(function(){})},
sj=function(a,b){return Q(a,oj,{clientId:b}).then(function(){})},tj=function(a,b){return Q(a,oj,{firebaseAppId:b}).then(function(){})},uj=function(a){if(!a.idToken){if(a.mfaPendingCredential)throw new P("multi-factor-auth-required",null,Ua(a));throw new P("internal-error");}},vj=function(a){if(a.phoneNumber||a.temporaryProof){if(!a.phoneNumber||!a.temporaryProof)throw new P("internal-error");}else{if(!a.sessionInfo)throw new P("missing-verification-id");if(!a.code)throw new P("missing-verification-code");
}};l=Wi.prototype;l.signInAnonymously=function(){return Q(this,wj,{})};l.updateEmail=function(a,b){return Q(this,xj,{idToken:a,email:b})};l.updatePassword=function(a,b){return Q(this,zi,{idToken:a,password:b})};l.updateProfile=function(a,b){var c={idToken:a},d=[];Ra(yj,function(e,f){var g=b[f];null===g?d.push(e):f in b&&(c[f]=g)});d.length&&(c.deleteAttribute=d);return Q(this,xj,c)};l.sendPasswordResetEmail=function(a,b){a={requestType:"PASSWORD_RESET",email:a};Wa(a,b);return Q(this,zj,a)};
l.sendSignInLinkToEmail=function(a,b){a={requestType:"EMAIL_SIGNIN",email:a};Wa(a,b);return Q(this,Aj,a)};l.sendEmailVerification=function(a,b){a={requestType:"VERIFY_EMAIL",idToken:a};Wa(a,b);return Q(this,Bj,a)};l.verifyBeforeUpdateEmail=function(a,b,c){a={requestType:"VERIFY_AND_CHANGE_EMAIL",idToken:a,newEmail:b};Wa(a,c);return Q(this,Cj,a)};var Li=function(a,b){return Q(a,Dj,b)};Wi.prototype.verifyPhoneNumber=function(a){return Q(this,Ej,a)};
var Ji=function(a,b){return Q(a,Fj,b).then(function(c){return c.phoneSessionInfo.sessionInfo})},Gj=function(a){if(!a.phoneVerificationInfo)throw new P("internal-error");if(!a.phoneVerificationInfo.sessionInfo)throw new P("missing-verification-id");if(!a.phoneVerificationInfo.code)throw new P("missing-verification-code");},Ki=function(a,b){return Q(a,Hj,b).then(function(c){return c.phoneResponseInfo.sessionInfo})},Jj=function(a,b,c){return Q(a,Ij,{idToken:b,deleteProvider:c})},Kj=function(a){if(!a.requestUri||
!a.sessionId&&!a.postBody&&!a.pendingToken)throw new P("internal-error");},Lj=function(a,b){b.oauthIdToken&&b.providerId&&0==b.providerId.indexOf("oidc.")&&!b.pendingToken&&(a.sessionId?b.nonce=a.sessionId:a.postBody&&(a=new kc(a.postBody),a.Pc("nonce")&&(b.nonce=a.get("nonce"))));return b},Nj=function(a){var b=null;a.needConfirmation?(a.code="account-exists-with-different-credential",b=Qi(a)):"FEDERATED_USER_ID_ALREADY_LINKED"==a.errorMessage?(a.code="credential-already-in-use",b=Qi(a)):"EMAIL_EXISTS"==
a.errorMessage?(a.code="email-already-in-use",b=Qi(a)):a.errorMessage&&(b=Mj(a.errorMessage));if(b)throw b;uj(a)},di=function(a,b){b.returnIdpCredential=!0;return Q(a,Oj,b)},ei=function(a,b){b.returnIdpCredential=!0;return Q(a,Pj,b)},fi=function(a,b){b.returnIdpCredential=!0;b.autoCreate=!1;return Q(a,Qj,b)},Rj=function(a){if(!a.oobCode)throw new P("invalid-action-code");};Wi.prototype.confirmPasswordReset=function(a,b){return Q(this,Sj,{oobCode:a,newPassword:b})};
Wi.prototype.checkActionCode=function(a){return Q(this,Tj,{oobCode:a})};Wi.prototype.applyActionCode=function(a){return Q(this,Uj,{oobCode:a})};
var Q=function(a,b,c){if(!wh(c,b.ba))return J(new P("internal-error"));var d=!!b.Gd,e=b.httpMethod||"POST",f;return I(c).then(b.G).then(function(){b.Ka&&(c.returnSecureToken=!0);b.I&&a.C&&"undefined"===typeof c.tenantId&&(c.tenantId=a.C);return d?hj(a,a.vh,b.endpoint,e,c,b.Zg,b.Qd||!1):hj(a,a.kh,b.endpoint,e,c,b.Zg,b.Qd||!1)}).then(function(g){f=g;return b.Fe?b.Fe(c,f):f}).then(b.O).then(function(){if(!b.Va)return f;if(!(b.Va in f))throw new P("internal-error");return f[b.Va]})},Mj=function(a){return fj({error:{errors:[{message:a}],
code:400,message:a}})},fj=function(a,b){var c=(a.error&&a.error.errors&&a.error.errors[0]||{}).reason||"";var d={keyInvalid:"invalid-api-key",ipRefererBlocked:"app-not-authorized"};if(c=d[c]?new P(d[c]):null)return c;c=a.error&&a.error.message||"";d={INVALID_CUSTOM_TOKEN:"invalid-custom-token",CREDENTIAL_MISMATCH:"custom-token-mismatch",MISSING_CUSTOM_TOKEN:"internal-error",INVALID_IDENTIFIER:"invalid-email",MISSING_CONTINUE_URI:"internal-error",INVALID_EMAIL:"invalid-email",INVALID_PASSWORD:"wrong-password",
USER_DISABLED:"user-disabled",MISSING_PASSWORD:"internal-error",EMAIL_EXISTS:"email-already-in-use",PASSWORD_LOGIN_DISABLED:"operation-not-allowed",INVALID_IDP_RESPONSE:"invalid-credential",INVALID_PENDING_TOKEN:"invalid-credential",FEDERATED_USER_ID_ALREADY_LINKED:"credential-already-in-use",MISSING_OR_INVALID_NONCE:"missing-or-invalid-nonce",INVALID_MESSAGE_PAYLOAD:"invalid-message-payload",INVALID_RECIPIENT_EMAIL:"invalid-recipient-email",INVALID_SENDER:"invalid-sender",EMAIL_NOT_FOUND:"user-not-found",
RESET_PASSWORD_EXCEED_LIMIT:"too-many-requests",EXPIRED_OOB_CODE:"expired-action-code",INVALID_OOB_CODE:"invalid-action-code",MISSING_OOB_CODE:"internal-error",INVALID_PROVIDER_ID:"invalid-provider-id",CREDENTIAL_TOO_OLD_LOGIN_AGAIN:"requires-recent-login",INVALID_ID_TOKEN:"invalid-user-token",TOKEN_EXPIRED:"user-token-expired",USER_NOT_FOUND:"user-token-expired",CORS_UNSUPPORTED:"cors-unsupported",DYNAMIC_LINK_NOT_ACTIVATED:"dynamic-link-not-activated",INVALID_APP_ID:"invalid-app-id",TOO_MANY_ATTEMPTS_TRY_LATER:"too-many-requests",
WEAK_PASSWORD:"weak-password",PASSWORD_DOES_NOT_MEET_REQUIREMENTS:"password-does-not-meet-requirements",OPERATION_NOT_ALLOWED:"operation-not-allowed",USER_CANCELLED:"user-cancelled",CAPTCHA_CHECK_FAILED:"captcha-check-failed",INVALID_APP_CREDENTIAL:"invalid-app-credential",INVALID_CODE:"invalid-verification-code",INVALID_PHONE_NUMBER:"invalid-phone-number",INVALID_SESSION_INFO:"invalid-verification-id",INVALID_TEMPORARY_PROOF:"invalid-credential",INVALID_TENANT_ID:"invalid-tenant-id",MISSING_APP_CREDENTIAL:"missing-app-credential",
MISSING_CODE:"missing-verification-code",MISSING_PHONE_NUMBER:"missing-phone-number",MISSING_SESSION_INFO:"missing-verification-id",QUOTA_EXCEEDED:"quota-exceeded",SESSION_EXPIRED:"code-expired",REJECTED_CREDENTIAL:"rejected-credential",INVALID_CONTINUE_URI:"invalid-continue-uri",MISSING_ANDROID_PACKAGE_NAME:"missing-android-pkg-name",MISSING_IOS_BUNDLE_ID:"missing-ios-bundle-id",UNAUTHORIZED_DOMAIN:"unauthorized-continue-uri",INVALID_DYNAMIC_LINK_DOMAIN:"invalid-dynamic-link-domain",INVALID_OAUTH_CLIENT_ID:"invalid-oauth-client-id",
INVALID_CERT_HASH:"invalid-cert-hash",UNSUPPORTED_TENANT_OPERATION:"unsupported-tenant-operation",TENANT_ID_MISMATCH:"tenant-id-mismatch",ADMIN_ONLY_OPERATION:"admin-restricted-operation",INVALID_MFA_PENDING_CREDENTIAL:"invalid-multi-factor-session",MFA_ENROLLMENT_NOT_FOUND:"multi-factor-info-not-found",MISSING_MFA_PENDING_CREDENTIAL:"missing-multi-factor-session",MISSING_MFA_ENROLLMENT_ID:"missing-multi-factor-info",EMAIL_CHANGE_NEEDS_VERIFICATION:"email-change-needs-verification",SECOND_FACTOR_EXISTS:"second-factor-already-in-use",
SECOND_FACTOR_LIMIT_EXCEEDED:"maximum-second-factor-count-exceeded",UNSUPPORTED_FIRST_FACTOR:"unsupported-first-factor",UNVERIFIED_EMAIL:"unverified-email"};b=b||{};Wa(d,b);b=(b=c.match(/^[^\s]+\s*:\s*([\s\S]*)$/))&&1<b.length?b[1]:void 0;for(var e in d)if(0===c.indexOf(e))return new P(d[e],b);!b&&a&&(b=$g(a));return new P("internal-error",b)},Si=new fh(3E4,6E4),Ti={"Content-Type":"application/x-www-form-urlencoded"},Ui=new fh(3E4,6E4),Vi={"Content-Type":"application/json"},ej=cb("https://apis.google.com/js/client.js?onload=%{onload}"),
dj="__fcb"+Math.floor(1E6*Math.random()).toString(),yj={displayName:"DISPLAY_NAME",photoUrl:"PHOTO_URL"},Uj={endpoint:"setAccountInfo",G:Rj,Va:"email",I:!0},Tj={endpoint:"resetPassword",G:Rj,O:function(a){var b=a.requestType;if(!b||!a.email&&"EMAIL_SIGNIN"!=b&&"VERIFY_AND_CHANGE_EMAIL"!=b)throw new P("internal-error");},I:!0},Vj={endpoint:"signupNewUser",G:function(a){ij(a);if(!a.password)throw new P("weak-password");},O:uj,Ka:!0,I:!0},mj={endpoint:"createAuthUri",I:!0},Wj={endpoint:"deleteAccount",
ba:["idToken"]},Ij={endpoint:"setAccountInfo",ba:["idToken","deleteProvider"],G:function(a){if(!Array.isArray(a.deleteProvider))throw new P("internal-error");}},wi={endpoint:"emailLinkSignin",ba:["email","oobCode"],G:ij,O:uj,Ka:!0,I:!0},yi={endpoint:"emailLinkSignin",ba:["idToken","email","oobCode"],G:ij,O:uj,Ka:!0},Xj={endpoint:"accounts/mfaEnrollment:finalize",ba:["idToken","phoneVerificationInfo"],G:Gj,O:uj,I:!0,Gd:!0},Yj={endpoint:"accounts/mfaSignIn:finalize",ba:["mfaPendingCredential","phoneVerificationInfo"],
G:Gj,O:uj,I:!0,Gd:!0},Zj={endpoint:"getAccountInfo"},kj={endpoint:"createAuthUri",G:function(a){if(!a.continueUri)throw new P("missing-continue-uri");if(!a.providerId)throw new P("internal-error","A provider ID must be provided in the request.");},O:function(a){if(!a.authUri)throw new P("internal-error","Unable to determine the authorization endpoint for the specified provider. This may be an issue in the provider configuration.");if(!a.sessionId)throw new P("internal-error");},I:!0},Aj={endpoint:"getOobConfirmationCode",
ba:["requestType"],G:function(a){if("EMAIL_SIGNIN"!=a.requestType)throw new P("internal-error");ij(a)},Va:"email",I:!0},Bj={endpoint:"getOobConfirmationCode",ba:["idToken","requestType"],G:function(a){if("VERIFY_EMAIL"!=a.requestType)throw new P("internal-error");},Va:"email",I:!0},Cj={endpoint:"getOobConfirmationCode",ba:["idToken","newEmail","requestType"],G:function(a){if("VERIFY_AND_CHANGE_EMAIL"!=a.requestType)throw new P("internal-error");},Va:"email",I:!0},zj={endpoint:"getOobConfirmationCode",
ba:["requestType"],G:function(a){if("PASSWORD_RESET"!=a.requestType)throw new P("internal-error");ij(a)},Va:"email",I:!0},oj={Qd:!0,endpoint:"getProjectConfig",httpMethod:"GET"},ak={Qd:!0,endpoint:"getRecaptchaParam",httpMethod:"GET",O:function(a){if(!a.recaptchaSiteKey)throw new P("internal-error");}},Sj={endpoint:"resetPassword",G:Rj,Va:"email",I:!0},bk={Qd:!0,endpoint:"getProjectConfig",httpMethod:"GET",Va:"dynamicLinksDomain"},Dj={endpoint:"sendVerificationCode",ba:["phoneNumber","recaptchaToken"],
Va:"sessionInfo",I:!0},xj={endpoint:"setAccountInfo",ba:["idToken"],G:jj,Ka:!0},zi={endpoint:"setAccountInfo",ba:["idToken"],G:function(a){jj(a);if(!a.password)throw new P("weak-password");},O:uj,Ka:!0},wj={endpoint:"signupNewUser",O:uj,Ka:!0,I:!0},Fj={endpoint:"accounts/mfaEnrollment:start",ba:["idToken","phoneEnrollmentInfo"],G:function(a){if(!a.phoneEnrollmentInfo)throw new P("internal-error");if(!a.phoneEnrollmentInfo.phoneNumber)throw new P("missing-phone-number");if(!a.phoneEnrollmentInfo.recaptchaToken)throw new P("missing-app-credential");
},O:function(a){if(!a.phoneSessionInfo||!a.phoneSessionInfo.sessionInfo)throw new P("internal-error");},I:!0,Gd:!0},Hj={endpoint:"accounts/mfaSignIn:start",ba:["mfaPendingCredential","mfaEnrollmentId","phoneSignInInfo"],G:function(a){if(!a.phoneSignInInfo||!a.phoneSignInInfo.recaptchaToken)throw new P("missing-app-credential");},O:function(a){if(!a.phoneResponseInfo||!a.phoneResponseInfo.sessionInfo)throw new P("internal-error");},I:!0,Gd:!0},Oj={endpoint:"verifyAssertion",G:Kj,Fe:Lj,O:Nj,Ka:!0,I:!0},
Qj={endpoint:"verifyAssertion",G:Kj,Fe:Lj,O:function(a){if(a.errorMessage&&"USER_NOT_FOUND"==a.errorMessage)throw new P("user-not-found");if(a.errorMessage)throw Mj(a.errorMessage);uj(a)},Ka:!0,I:!0},Pj={endpoint:"verifyAssertion",G:function(a){Kj(a);if(!a.idToken)throw new P("internal-error");},Fe:Lj,O:Nj,Ka:!0},ck={endpoint:"verifyCustomToken",G:function(a){if(!a.token)throw new P("invalid-custom-token");},O:uj,Ka:!0,I:!0},xi={endpoint:"verifyPassword",G:function(a){ij(a);if(!a.password)throw new P("wrong-password");
},O:uj,Ka:!0,I:!0},Ej={endpoint:"verifyPhoneNumber",G:vj,O:uj,I:!0},Fi={endpoint:"verifyPhoneNumber",G:function(a){if(!a.idToken)throw new P("internal-error");vj(a)},O:function(a){if(a.temporaryProof)throw a.code="credential-already-in-use",Qi(a);uj(a)}},Gi={Zg:{USER_NOT_FOUND:"user-not-found"},endpoint:"verifyPhoneNumber",G:vj,O:uj,I:!0},dk={endpoint:"accounts/mfaEnrollment:withdraw",ba:["idToken","mfaEnrollmentId"],O:function(a){if(!!a.idToken^!!a.refreshToken)throw new P("internal-error");},I:!0,
Gd:!0};var fk=function(a){this.Yb=a;this.ke=null;this.Wf=ek(this)},ek=function(a){return gk().then(function(){return new H(function(b,c){M("gapi.iframes.getContext")().open({where:document.body,url:a.Yb,messageHandlersFilter:M("gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER"),attributes:{style:{position:"absolute",top:"-100px",width:"1px",height:"1px"}},dontclear:!0},function(d){a.ke=d;a.ke.restyle({setHideOnLeave:!1});var e=setTimeout(function(){c(Error("Network Error"))},hk.get()),f=function(){clearTimeout(e);
b()};d.ping(f).then(f,function(){c(Error("Network Error"))})})})})};fk.prototype.sendMessage=function(a){var b=this;return this.Wf.then(function(){return new H(function(c){b.ke.send(a.type,a,c,M("gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER"))})})};
var ik=function(a,b){a.Wf.then(function(){a.ke.register("authEvent",b,M("gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER"))})},gk=function(){return jk?jk:jk=(new H(function(a,b){var c=function(){eh();M("gapi.load")("gapi.iframes",{callback:a,ontimeout:function(){eh();b(Error("Network Error"))},timeout:kk.get()})};if(M("gapi.iframes.Iframe"))a();else if(M("gapi.load"))c();else{var d="__iframefcb"+Math.floor(1E6*Math.random()).toString();q[d]=function(){M("gapi.load")?c():b(Error("Network Error"))};d=jb(lk,
{onload:d});I(dg(d)).h(function(){b(Error("Network Error"))})}})).h(function(a){jk=null;throw a;})},lk=cb("https://apis.google.com/js/api.js?onload=%{onload}"),kk=new fh(3E4,6E4),hk=new fh(5E3,15E3),jk=null;var mk=function(a,b,c,d){this.J=a;this.A=b;this.B=c;this.s=d;this.ac=null;this.s?(a=E(this.s.url),a=rc(a.ua,a.ga,a.Ua,"/emulator/auth/iframe",null,null)):a=rc("https",this.J,null,"/__/auth/iframe",null,null);this.jb=a;C(this.jb,"apiKey",this.A);C(this.jb,"appName",this.B);this.pa=null;this.U=[]};mk.prototype.vg=function(a){this.ac=a;return this};mk.prototype.sg=function(a){this.pa=a;return this};
mk.prototype.toString=function(){this.ac?C(this.jb,"v",this.ac):this.jb.removeParameter("v");this.pa?C(this.jb,"eid",this.pa):this.jb.removeParameter("eid");this.U.length?C(this.jb,"fw",this.U.join(",")):this.jb.removeParameter("fw");return this.jb.toString()};var nk=function(a,b,c,d,e,f){this.J=a;this.A=b;this.B=c;this.Oi=d;this.s=f;this.ac=this.T=this.Cc=null;this.Ac=e;this.C=this.pa=null};nk.prototype.ug=function(a){this.C=a;return this};nk.prototype.vg=function(a){this.ac=a;return this};
nk.prototype.sg=function(a){this.pa=a;return this};
nk.prototype.toString=function(){if(this.s){var a=E(this.s.url);a=rc(a.ua,a.ga,a.Ua,"/emulator/auth/handler",null,null)}else a=rc("https",this.J,null,"/__/auth/handler",null,null);C(a,"apiKey",this.A);C(a,"appName",this.B);C(a,"authType",this.Oi);if(this.Ac.isOAuthProvider){var b=this.Ac;try{var c=firebase.app(this.B).auth().Ga}catch(f){c=null}b.sf=c;C(a,"providerId",this.Ac.providerId);c=this.Ac;b=ah(c.ah);for(var d in b)b[d]=b[d].toString();d=bh(b,c.Sj);c.Of&&c.sf&&!d[c.Of]&&(d[c.Of]=c.sf);Ta(d)||
C(a,"customParameters",$g(d))}"function"===typeof this.Ac.ph&&(d=this.Ac.ph(),d.length&&C(a,"scopes",d.join(",")));this.Cc?C(a,"redirectUrl",this.Cc):a.removeParameter("redirectUrl");this.T?C(a,"eventId",this.T):a.removeParameter("eventId");this.ac?C(a,"v",this.ac):a.removeParameter("v");if(this.Jd)for(var e in this.Jd)this.Jd.hasOwnProperty(e)&&!D(a,e)&&C(a,e,this.Jd[e]);this.C?C(a,"tid",this.C):a.removeParameter("tid");this.pa?C(a,"eid",this.pa):a.removeParameter("eid");e=ok(this.B);e.length&&C(a,
"fw",e.join(","));return a.toString()};var ok=function(a){try{return Qa(firebase.app(a).auth().U)}catch(b){return[]}},pk=function(a,b,c,d,e,f){this.J=a;this.A=b;this.B=c;this.s=f;this.Qa=d||null;this.pa=e||null;this.i=this.Jf=this.nh=null;this.Ya=[];this.ne=this.eb=null},qk=function(a){var b=b||vg();return pj(a).then(function(c){if(!Hg(c,b))throw new Vh(vg());})};l=pk.prototype;
l.initialize=function(){if(this.ne)return this.ne;var a=this;return this.ne=Jg().then(function(){if(!a.Jf){var b=a.Qa,c=a.pa,d=ok(a.B);b=(new mk(a.J,a.A,a.B,a.s)).vg(b).sg(c);b.U=Qa(d||[]);a.Jf=b.toString()}a.je=new fk(a.Jf);rk(a)})};l.Ad=function(a,b,c){var d=new P("popup-closed-by-user"),e=new P("web-storage-unsupported"),f=this,g=!1;return this.Ob().then(function(){sk(f).then(function(h){h||(a&&Cg(a),b(e),g=!0)})}).h(function(){}).then(function(){if(!g)return Fg(a)}).then(function(){if(!g)return fg(c).then(function(){b(d)})})};
l.oi=function(){var a=L();return!Zg(a)&&!dh(a)};l.rh=function(){return!1};
l.pd=function(a,b,c,d,e,f,g,h){if(!a)return J(new P("popup-blocked"));if(g&&!Zg())return this.Ob().h(function(n){Cg(a);e(n)}),d(),I();this.eb||(this.eb=qk(tk(this)));var k=this;return this.eb.then(function(){var n=k.Ob().h(function(p){Cg(a);e(p);throw p;});d();return n}).then(function(){Oi(c);if(!g){var n=uk(k.J,k.A,k.B,b,c,null,f,k.Qa,void 0,k.pa,h,k.s);wg(n,a)}}).h(function(n){"auth/network-request-failed"==n.code&&(k.eb=null);throw n;})};
var tk=function(a){a.i||(a.nh=a.Qa?Ug("JsCore",a.Qa,ok(a.B)):null,a.i=new Wi(a.A,sg(a.pa),a.nh),a.s&&$i(a.i,a.s));return a.i};pk.prototype.qd=function(a,b,c,d){this.eb||(this.eb=qk(tk(this)));var e=this;return this.eb.then(function(){Oi(b);var f=uk(e.J,e.A,e.B,a,b,vg(),c,e.Qa,void 0,e.pa,d,e.s);wg(f)}).h(function(f){"auth/network-request-failed"==f.code&&(e.eb=null);throw f;})};
pk.prototype.Ob=function(){var a=this;return this.initialize().then(function(){return a.je.Wf}).h(function(){a.eb=null;throw new P("network-request-failed");})};pk.prototype.ui=function(){return!0};
var uk=function(a,b,c,d,e,f,g,h,k,n,p,r){a=new nk(a,b,c,d,e,r);a.Cc=f;a.T=g;f=a.vg(h);f.Jd=Ua(k||null);return f.sg(n).ug(p).toString()},rk=function(a){if(!a.je)throw Error("IfcHandler must be initialized!");ik(a.je,function(b){var c={};if(b&&b.authEvent){var d=!1;b=Hh(b.authEvent);for(c=0;c<a.Ya.length;c++)d=a.Ya[c](b)||d;c={};c.status=d?"ACK":"ERROR";return I(c)}c.status="ERROR";return I(c)})},sk=function(a){var b={type:"webStorageSupport"};return a.initialize().then(function(){return a.je.sendMessage(b)}).then(function(c){if(c&&
c.length&&"undefined"!==typeof c[0].webStorageSupport)return c[0].webStorageSupport;throw Error();})};pk.prototype.cc=function(a){this.Ya.push(a)};pk.prototype.vd=function(a){Pa(this.Ya,function(b){return b==a})};function vk(){}vk.prototype.render=function(){};vk.prototype.reset=function(){};vk.prototype.getResponse=function(){};vk.prototype.execute=function(){};var wk=function(){this.ic=q.grecaptcha?Infinity:0;this.pc=null;this.mf="__rcb"+Math.floor(1E6*Math.random()).toString()};
wk.prototype.Hh=function(a){var b=this;return new H(function(c,d){var e=setTimeout(function(){d(new P("network-request-failed"))},xk.get());if(!q.grecaptcha||a!==b.pc&&!b.ic){q[b.mf]=function(){if(q.grecaptcha){b.pc=a;var g=q.grecaptcha.render;q.grecaptcha.render=function(h,k){h=g(h,k);b.ic++;return h};clearTimeout(e);c(q.grecaptcha)}else clearTimeout(e),d(new P("internal-error"));delete q[b.mf]};var f=jb(yk,{onload:b.mf,hl:a||""});I(dg(f)).h(function(){clearTimeout(e);d(new P("internal-error","Unable to load external reCAPTCHA dependencies!"))})}else clearTimeout(e),
c(q.grecaptcha)})};wk.prototype.Qg=function(){this.ic--};var yk=cb("https://www.google.com/recaptcha/api.js?trustedtypes=true&onload=%{onload}&render=explicit&hl=%{hl}"),xk=new fh(3E4,6E4),zk=null;var Ak=function(){this.Rf={};this.ic=1E12};Ak.prototype.render=function(a,b){this.Rf[this.ic.toString()]=new Bk(a,b);return this.ic++};Ak.prototype.reset=function(a){var b=Ck(this,a);a=Dk(a);b&&a&&(b.delete(),delete this.Rf[a])};Ak.prototype.getResponse=function(a){return(a=Ck(this,a))?a.getResponse():null};Ak.prototype.execute=function(a){(a=Ck(this,a))&&a.execute()};
var Ck=function(a,b){return(b=Dk(b))?a.Rf[b]||null:null},Dk=function(a){return(a="undefined"===typeof a?1E12:a)?a.toString():null},Ek=null,Bk=function(a,b){this.Aa=!1;this.V=b;this.Hc=this.Ge=null;this.Dh="invisible"!==this.V.size;this.S=gd(a);var c=this;this.Oh=function(){c.execute()};this.Dh?this.execute():ie(this.S,"click",this.Oh)};Bk.prototype.getResponse=function(){Fk(this);return this.Ge};
Bk.prototype.execute=function(){Fk(this);var a=this;this.Hc||(this.Hc=setTimeout(function(){a.Ge=Sg();var b=a.V.callback,c=a.V["expired-callback"];if(b)try{b(a.Ge)}catch(d){}a.Hc=setTimeout(function(){a.Hc=null;a.Ge=null;if(c)try{c()}catch(d){}a.Dh&&a.execute()},6E4)},500))};Bk.prototype.delete=function(){Fk(this);this.Aa=!0;clearTimeout(this.Hc);this.Hc=null;re(this.S,"click",this.Oh)};var Fk=function(a){if(a.Aa)throw Error("reCAPTCHA mock was already deleted!");};var Gk=function(){};Gk.prototype.Hh=function(){Ek||(Ek=new Ak);return I(Ek)};Gk.prototype.Qg=function(){};var Hk=null;var Ik=function(a,b,c,d,e,f,g){N(this,"type","recaptcha");this.Lc=this.Nc=null;this.Tc=!1;this.Oc=b;this.cd=null;g?(Hk||(Hk=new Gk),g=Hk):(zk||(zk=new wk),g=zk);this.Zh=g;this.Ta=c||{theme:"light",type:"image"};this.Z=[];if(this.Ta.sitekey)throw new P("argument-error","sitekey should not be provided for reCAPTCHA as one is automatically provisioned for the current project.");this.oe="invisible"===this.Ta.size;if(!q.document)throw new P("operation-not-supported-in-this-environment","RecaptchaVerifier is only supported in a browser HTTP/HTTPS environment with DOM support.");
if(!gd(b)||!this.oe&&gd(b).hasChildNodes())throw new P("argument-error","reCAPTCHA container is either not found or already contains inner elements!");this.i=new Wi(a,f||null,e||null);this.dj=d||function(){return null};var h=this;this.Se=[];var k=this.Ta.callback;this.Ta.callback=function(p){h.Uc(p);if("function"===typeof k)k(p);else if("string"===typeof k){var r=M(k,q);"function"===typeof r&&r(p)}};var n=this.Ta["expired-callback"];this.Ta["expired-callback"]=function(){h.Uc(null);if("function"===
typeof n)n();else if("string"===typeof n){var p=M(n,q);"function"===typeof p&&p()}}};Ik.prototype.Uc=function(a){for(var b=0;b<this.Se.length;b++)try{this.Se[b](a)}catch(c){}};var Jk=function(a,b){Pa(a.Se,function(c){return c==b})};l=Ik.prototype;l.l=function(a){var b=this;this.Z.push(a);a.Bb(function(){Na(b.Z,a)});return a};
l.gd=function(){var a=this;return this.Nc?this.Nc:this.Nc=this.l(I().then(function(){if(Xg()&&!Pg())return Jg();throw new P("operation-not-supported-in-this-environment","RecaptchaVerifier is only supported in a browser HTTP/HTTPS environment.");}).then(function(){return a.Zh.Hh(a.dj())}).then(function(b){a.cd=b;return Q(a.i,ak,{})}).then(function(b){a.Ta.sitekey=b.recaptchaSiteKey}).h(function(b){a.Nc=null;throw b;}))};
l.render=function(){Kk(this);var a=this;return this.l(this.gd().then(function(){if(null===a.Lc){var b=a.Oc;if(!a.oe){var c=gd(b);b=kd("DIV");c.appendChild(b)}a.Lc=a.cd.render(b,a.Ta)}return a.Lc}))};l.verify=function(){Kk(this);var a=this;return this.l(this.render().then(function(b){return new H(function(c){var d=a.cd.getResponse(b);if(d)c(d);else{var e=function(f){f&&(Jk(a,e),c(f))};a.Se.push(e);a.oe&&a.cd.execute(a.Lc)}})}))};l.reset=function(){Kk(this);null!==this.Lc&&this.cd.reset(this.Lc)};
var Kk=function(a){if(a.Tc)throw new P("internal-error","RecaptchaVerifier instance has been destroyed.");};Ik.prototype.clear=function(){Kk(this);this.Tc=!0;this.Zh.Qg();for(var a=0;a<this.Z.length;a++)this.Z[a].cancel("RecaptchaVerifier instance has been destroyed.");this.oe||od(gd(this.Oc))};
var Lk=function(a,b,c){var d=!1;try{this.u=c||firebase.app()}catch(g){throw new P("argument-error","No firebase.app.App instance is currently initialized.");}if(this.u.options&&this.u.options.apiKey)c=this.u.options.apiKey;else throw new P("invalid-api-key");var e=this,f=null;try{f=Qa(this.u.auth().U)}catch(g){}try{d=this.u.auth().settings.appVerificationDisabledForTesting}catch(g){}f=firebase.SDK_VERSION?Ug("JsCore",firebase.SDK_VERSION,f):null;Ik.call(this,c,a,b,function(){try{var g=e.u.auth().Ga}catch(h){g=
null}return g},f,sg(tg),d)};w(Lk,Ik);var Mk=function(a,b){this.Ak=a;this.nk=b||"*"};Mk.prototype.postMessage=function(a,b){this.Ak.postMessage(a,this.nk,b)};var Nk=function(a){this.Id=a};Nk.prototype.postMessage=function(a,b){this.Id.postMessage(a,b)};var Ok=function(a){this.Oj=a;this.Rg=!1;this.te=[]},Pk=function(a,b,c,d){var e=Date.now();return a.send(b,c).h(function(f){if(f&&"connection_unavailable"===f.message)throw f;var g=Date.now()-e;if(g>d)throw f;return Pk(a,b,c,d-g)})};
Ok.prototype.send=function(a,b,c){b=void 0===b?null:b;c=void 0===c?!1:c;var d=this,e;b=b||{};var f,g,h,k=null;if(this.Rg)return J(Error("connection_unavailable"));var n=c?800:50,p="undefined"!==typeof MessageChannel?new MessageChannel:null;return(new H(function(r,t){p?(e=""+Math.floor(Math.random()*Math.pow(10,20)).toString(),p.port1.start(),g=setTimeout(function(){t(Error("unsupported_event"))},n),f=function(z){z.data.eventId===e&&("ack"===z.data.status?(clearTimeout(g),h=setTimeout(function(){t(Error("timeout"))},
3E3)):"done"===z.data.status?(clearTimeout(h),"undefined"!==typeof z.data.response?r(z.data.response):t(Error("unknown_error"))):(clearTimeout(g),clearTimeout(h),t(Error("invalid_response"))))},k={messageChannel:p,onMessage:f},d.te.push(k),p.port1.addEventListener("message",f),d.Oj.postMessage({eventType:a,eventId:e,data:b},[p.port2])):t(Error("connection_unavailable"))})).then(function(r){Qk(d,k);return r}).h(function(r){Qk(d,k);throw r;})};
var Qk=function(a,b){if(b){var c=b.messageChannel,d=b.onMessage;c&&(c.port1.removeEventListener("message",d),c.port1.close());Pa(a.te,function(e){return e==b})}};Ok.prototype.close=function(){for(;0<this.te.length;)Qk(this,this.te[0]);this.Rg=!0};var Rk=function(a){this.xf=a;this.Da={};this.Jh=v(this.gj,this)},Tk=function(){var a=Pg()?self:null;y(Sk,function(c){c.xf==a&&(b=c)});if(!b){var b=new Rk(a);Sk.push(b)}return b};
Rk.prototype.gj=function(a){var b=a.data.eventType,c=a.data.eventId,d=this.Da[b];if(d&&0<d.length){a.ports[0].postMessage({status:"ack",eventId:c,eventType:b,response:null});var e=[];y(d,function(f){e.push(I().then(function(){return f(a.origin,a.data.data)}))});Af(e).then(function(f){var g=[];y(f,function(h){g.push({fulfilled:h.mh,value:h.value,reason:h.reason?h.reason.message:void 0})});y(g,function(h){for(var k in h)"undefined"===typeof h[k]&&delete h[k]});a.ports[0].postMessage({status:"done",
eventId:c,eventType:b,response:g})})}};Rk.prototype.subscribe=function(a,b){Ta(this.Da)&&this.xf.addEventListener("message",this.Jh);"undefined"===typeof this.Da[a]&&(this.Da[a]=[]);this.Da[a].push(b)};Rk.prototype.unsubscribe=function(a,b){"undefined"!==typeof this.Da[a]&&b?(Pa(this.Da[a],function(c){return c==b}),0==this.Da[a].length&&delete this.Da[a]):b||delete this.Da[a];Ta(this.Da)&&this.xf.removeEventListener("message",this.Jh)};var Sk=[];var Uk=function(a){this.za=a||firebase.INTERNAL.reactNative&&firebase.INTERNAL.reactNative.AsyncStorage;if(!this.za)throw new P("internal-error","The React Native compatibility library was not found.");this.type="asyncStorage"};l=Uk.prototype;l.get=function(a){return I(this.za.getItem(a)).then(function(b){return b&&ch(b)})};l.set=function(a,b){return I(this.za.setItem(a,$g(b)))};l.remove=function(a){return I(this.za.removeItem(a))};l.mb=function(){};l.zb=function(){};function Vk(){this.storage={};this.type="inMemory"}l=Vk.prototype;l.get=function(a){return I(this.storage[a])};l.set=function(a,b){this.storage[a]=b;return I()};l.remove=function(a){delete this.storage[a];return I()};l.mb=function(){};l.zb=function(){};var Yk=function(){if(!Wk()){if("Node"==Qg())throw new P("internal-error","The LocalStorage compatibility library was not found.");throw new P("web-storage-unsupported");}this.za=Xk()||firebase.INTERNAL.node.localStorage;this.type="localStorage"},Xk=function(){try{var a=q.localStorage,b=Vg();a&&(a.setItem(b,"1"),a.removeItem(b));return a}catch(c){return null}},Wk=function(){var a="Node"==Qg();a=Xk()||a&&firebase.INTERNAL.node&&firebase.INTERNAL.node.localStorage;if(!a)return!1;try{return a.setItem("__sak",
"1"),a.removeItem("__sak"),!0}catch(b){return!1}};l=Yk.prototype;l.get=function(a){var b=this;return I().then(function(){var c=b.za.getItem(a);return ch(c)})};l.set=function(a,b){var c=this;return I().then(function(){var d=$g(b);null===d?c.remove(a):c.za.setItem(a,d)})};l.remove=function(a){var b=this;return I().then(function(){b.za.removeItem(a)})};l.mb=function(a){q.window&&ie(q.window,"storage",a)};l.zb=function(a){q.window&&re(q.window,"storage",a)};var Zk=function(){this.za={};this.type="nullStorage"};l=Zk.prototype;l.get=function(){return I(null)};l.set=function(){return I()};l.remove=function(){return I()};l.mb=function(){};l.zb=function(){};var bl=function(){if(!$k()){if("Node"==Qg())throw new P("internal-error","The SessionStorage compatibility library was not found.");throw new P("web-storage-unsupported");}this.za=al()||firebase.INTERNAL.node.sessionStorage;this.type="sessionStorage"},al=function(){try{var a=q.sessionStorage,b=Vg();a&&(a.setItem(b,"1"),a.removeItem(b));return a}catch(c){return null}},$k=function(){var a="Node"==Qg();a=al()||a&&firebase.INTERNAL.node&&firebase.INTERNAL.node.sessionStorage;if(!a)return!1;try{return a.setItem("__sak",
"1"),a.removeItem("__sak"),!0}catch(b){return!1}};l=bl.prototype;l.get=function(a){var b=this;return I().then(function(){var c=b.za.getItem(a);return ch(c)})};l.set=function(a,b){var c=this;return I().then(function(){var d=$g(b);null===d?c.remove(a):c.za.setItem(a,d)})};l.remove=function(a){var b=this;return I().then(function(){b.za.removeItem(a)})};l.mb=function(){};l.zb=function(){};var el=function(){if(!cl())throw new P("web-storage-unsupported");this.bh="firebaseLocalStorageDb";this.ve="firebaseLocalStorage";this.rf="fbase_key";this.Di="value";this.yk=1;this.ka={};this.Wa=[];this.md=0;this.xh=q.indexedDB;this.type="indexedDB";this.Le=this.lg=this.Ce=this.ag=null;this.gi=!1;this.bf=null;var a=this;Pg()&&self?(this.lg=Tk(),this.lg.subscribe("keyChanged",function(b,c){return dl(a).then(function(d){0<d.length&&y(a.Wa,function(e){e(d)});return{keyProcessed:Ma(d,c.key)}})}),this.lg.subscribe("ping",
function(){return I(["keyChanged"])})):ph().then(function(b){if(a.bf=b)a.Le=new Ok(new Nk(b)),a.Le.send("ping",null,!0).then(function(c){c[0].fulfilled&&Ma(c[0].value,"keyChanged")&&(a.gi=!0)}).h(function(){})})},fl,gl=function(a){return new H(function(b,c){var d=a.xh.deleteDatabase(a.bh);d.onsuccess=function(){b()};d.onerror=function(e){c(Error(e.target.error))}})},hl=function(a){return new H(function(b,c){var d=a.xh.open(a.bh,a.yk);d.onerror=function(e){try{e.preventDefault()}catch(f){}c(Error(e.target.error))};
d.onupgradeneeded=function(e){e=e.target.result;try{e.createObjectStore(a.ve,{keyPath:a.rf})}catch(f){c(f)}};d.onsuccess=function(e){e=e.target.result;e.objectStoreNames.contains(a.ve)?b(e):gl(a).then(function(){return hl(a)}).then(function(f){b(f)}).h(function(f){c(f)})}})},il=function(a){a.Lf||(a.Lf=hl(a));return a.Lf},jl=function(a,b){var c=0,d=function(e,f){il(a).then(b).then(e).h(function(g){if(3<++c)f(g);else return il(a).then(function(h){h.close();a.Lf=void 0;return d(e,f)}).h(function(h){f(h)})})};
return new H(d)},cl=function(){try{return!!q.indexedDB}catch(a){return!1}},kl=function(a,b){return b.objectStore(a.ve)},ll=function(a,b,c){return b.transaction([a.ve],c?"readwrite":"readonly")},ml=function(a){return new H(function(b,c){a.onsuccess=function(d){d&&d.target?b(d.target.result):b()};a.onerror=function(d){c(d.target.error)}})};
el.prototype.set=function(a,b){var c=this,d=!1;return jl(this,function(e){e=kl(c,ll(c,e,!0));return ml(e.get(a))}).then(function(e){return jl(c,function(f){f=kl(c,ll(c,f,!0));if(e)return e.value=b,ml(f.put(e));c.md++;d=!0;var g={};g[c.rf]=a;g[c.Di]=b;return ml(f.add(g))})}).then(function(){c.ka[a]=b;return nl(c,a)}).Bb(function(){d&&c.md--})};var nl=function(a,b){return a.Le&&a.bf&&oh()===a.bf?a.Le.send("keyChanged",{key:b},a.gi).then(function(){}).h(function(){}):I()};
el.prototype.get=function(a){var b=this;return jl(this,function(c){return ml(kl(b,ll(b,c,!1)).get(a))}).then(function(c){return c&&c.value})};el.prototype.remove=function(a){var b=!1,c=this;return jl(this,function(d){b=!0;c.md++;return ml(kl(c,ll(c,d,!0))["delete"](a))}).then(function(){delete c.ka[a];return nl(c,a)}).Bb(function(){b&&c.md--})};
var dl=function(a){return il(a).then(function(b){var c=kl(a,ll(a,b,!1));return c.getAll?ml(c.getAll()):new H(function(d,e){var f=[],g=c.openCursor();g.onsuccess=function(h){(h=h.target.result)?(f.push(h.value),h["continue"]()):d(f)};g.onerror=function(h){e(h.target.error)}})}).then(function(b){var c={},d=[];if(0==a.md){for(d=0;d<b.length;d++)c[b[d][a.rf]]=b[d][a.Di];d=yg(a.ka,c);a.ka=c}return d})};el.prototype.mb=function(a){0==this.Wa.length&&this.xg();this.Wa.push(a)};
el.prototype.zb=function(a){Pa(this.Wa,function(b){return b==a});0==this.Wa.length&&this.Oe()};el.prototype.xg=function(){var a=this;this.Oe();var b=function(){a.Ce=setTimeout(function(){a.ag=dl(a).then(function(c){0<c.length&&y(a.Wa,function(d){d(c)})}).then(function(){b()}).h(function(c){"STOP_EVENT"!=c.message&&b()})},800)};b()};el.prototype.Oe=function(){this.ag&&this.ag.cancel("STOP_EVENT");this.Ce&&(clearTimeout(this.Ce),this.Ce=null)};function ol(a){var b=this,c=null;this.Wa=[];this.type="indexedDB";this.gh=a;this.Cg=I().then(function(){if(cl()){var d=Vg(),e="__sak"+d;fl||(fl=new el);c=fl;return c.set(e,d).then(function(){return c.get(e)}).then(function(f){if(f!==d)throw Error("indexedDB not supported!");return c.remove(e)}).then(function(){return c}).h(function(){return b.gh})}return b.gh}).then(function(d){b.type=d.type;d.mb(function(e){y(b.Wa,function(f){f(e)})});return d})}l=ol.prototype;l.get=function(a){return this.Cg.then(function(b){return b.get(a)})};
l.set=function(a,b){return this.Cg.then(function(c){return c.set(a,b)})};l.remove=function(a){return this.Cg.then(function(b){return b.remove(a)})};l.mb=function(a){this.Wa.push(a)};l.zb=function(a){Pa(this.Wa,function(b){return b==a})};var tl=function(){this.vf={Browser:pl,Node:ql,ReactNative:rl,Worker:sl}[Qg()]},ul,pl={M:Yk,Pe:bl},ql={M:Yk,Pe:bl},rl={M:Uk,Pe:Zk},sl={M:Yk,Pe:Zk};var vl=function(){this.df=!1;Object.defineProperty(this,"appVerificationDisabled",{get:function(){return this.df},set:function(a){this.df=a},enumerable:!1})};var wl=function(a){this.cb(a)};
wl.prototype.cb=function(a){var b=a.url;if("undefined"===typeof b)throw new P("missing-continue-uri");if("string"!==typeof b||"string"===typeof b&&!b.length)throw new P("invalid-continue-uri");this.Qi=b;this.Fg=this.dc=null;this.zh=!1;var c=a.android;if(c&&"object"===typeof c){b=c.packageName;var d=c.installApp;c=c.minimumVersion;if("string"===typeof b&&b.length){this.dc=b;if("undefined"!==typeof d&&"boolean"!==typeof d)throw new P("argument-error","installApp property must be a boolean when specified.");this.zh=
!!d;if("undefined"!==typeof c&&("string"!==typeof c||"string"===typeof c&&!c.length))throw new P("argument-error","minimumVersion property must be a non empty string when specified.");this.Fg=c||null}else{if("undefined"!==typeof b)throw new P("argument-error","packageName property must be a non empty string when specified.");if("undefined"!==typeof d||"undefined"!==typeof c)throw new P("missing-android-pkg-name");}}else if("undefined"!==typeof c)throw new P("argument-error","android property must be a non null object when specified.");
this.ed=null;if((b=a.iOS)&&"object"===typeof b)if(b=b.bundleId,"string"===typeof b&&b.length)this.ed=b;else{if("undefined"!==typeof b)throw new P("argument-error","bundleId property must be a non empty string when specified.");}else if("undefined"!==typeof b)throw new P("argument-error","iOS property must be a non null object when specified.");b=a.handleCodeInApp;if("undefined"!==typeof b&&"boolean"!==typeof b)throw new P("argument-error","handleCodeInApp property must be a boolean when specified.");
this.Og=!!b;a=a.dynamicLinkDomain;if("undefined"!==typeof a&&("string"!==typeof a||"string"===typeof a&&!a.length))throw new P("argument-error","dynamicLinkDomain property must be a non empty string when specified.");this.Ti=a||null};var xl=function(a){var b={};b.continueUrl=a.Qi;b.canHandleCodeInApp=a.Og;if(b.androidPackageName=a.dc)b.androidMinimumVersion=a.Fg,b.androidInstallApp=a.zh;b.iOSBundleId=a.ed;b.dynamicLinkDomain=a.Ti;for(var c in b)null===b[c]&&delete b[c];return b};var yl=function(a,b){this.Si=b;N(this,"verificationId",a)};yl.prototype.confirm=function(a){a=Mi(this.verificationId,a);return this.Si(a)};var zl=function(a,b,c,d){return(new Ii(a)).verifyPhoneNumber(b,c).then(function(e){return new yl(e,d)})};var Al=function(a,b,c){this.Mj=a;this.Xj=b;this.ej=c;this.se=3E4;this.Eg=96E4;this.Yj=!1;this.vc=null;this.Qb=this.se;if(this.Eg<this.se)throw Error("Proactive refresh lower bound greater than upper bound!");};Al.prototype.start=function(){this.Qb=this.se;Bl(this,!0)};
var Cl=function(a,b){if(b)return a.Qb=a.se,a.ej();b=a.Qb;a.Qb*=2;a.Qb>a.Eg&&(a.Qb=a.Eg);return b},Bl=function(a,b){a.stop();a.vc=fg(Cl(a,b)).then(function(){return a.Yj?I():hh()}).then(function(){return a.Mj()}).then(function(){Bl(a,!0)}).h(function(c){a.Xj(c)&&Bl(a,!1)})};Al.prototype.stop=function(){this.vc&&(this.vc.cancel(),this.vc=null)};var Jl=function(a){var b={};b["facebook.com"]=Dl;b["google.com"]=El;b["github.com"]=Fl;b["twitter.com"]=Gl;var c=a&&a.providerId;try{if(c)return b[c]?new b[c](a):new Hl(a);if("undefined"!==typeof a.idToken)return new Il(a)}catch(d){}return null},Il=function(a){var b=a.providerId;if(!b&&a.idToken){var c=Yh(a.idToken);c&&c.zc&&(b=c.zc)}if(!b)throw Error("Invalid additional user info!");if("anonymous"==b||"custom"==b)b=null;c=!1;"undefined"!==typeof a.isNewUser?c=!!a.isNewUser:"identitytoolkit#SignupNewUserResponse"===
a.kind&&(c=!0);N(this,"providerId",b);N(this,"isNewUser",c)},Hl=function(a){Il.call(this,a);a=ch(a.rawUserInfo||"{}");N(this,"profile",xh(a||{}))};m(Hl,Il);var Dl=function(a){Hl.call(this,a);if("facebook.com"!=this.providerId)throw Error("Invalid provider ID!");};m(Dl,Hl);var Fl=function(a){Hl.call(this,a);if("github.com"!=this.providerId)throw Error("Invalid provider ID!");N(this,"username",this.profile&&this.profile.login||null)};m(Fl,Hl);
var El=function(a){Hl.call(this,a);if("google.com"!=this.providerId)throw Error("Invalid provider ID!");};m(El,Hl);var Gl=function(a){Hl.call(this,a);if("twitter.com"!=this.providerId)throw Error("Invalid provider ID!");N(this,"username",a.screenName||null)};m(Gl,Hl);var Kl={LOCAL:"local",NONE:"none",SESSION:"session"},Ll=function(a){var b=new P("invalid-persistence-type"),c=new P("unsupported-persistence-type");a:{for(d in Kl)if(Kl[d]==a){var d=!0;break a}d=!1}if(!d||"string"!==typeof a)throw b;switch(Qg()){case "ReactNative":if("session"===a)throw c;break;case "Node":if("none"!==a)throw c;break;case "Worker":if("session"===a||!cl()&&"none"!==a)throw c;break;default:if(!Wg()&&"none"!==a)throw c;}},Ml=function(){var a=!dh(L())&&Ng()?!0:!1,b=Zg(),c=Wg();this.Mh=
"firebase";this.pg=":";this.Zj=a;this.bi=b;this.Ei=c;this.sa={};ul||(ul=new tl);a=ul;try{this.Vh=!ug()&&mh()||!q.indexedDB?new a.vf.M:new ol(Pg()?new Vk:new a.vf.M)}catch(d){this.Vh=new Vk,this.bi=!0}try{this.si=new a.vf.Pe}catch(d){this.si=new Vk}this.sj=new Vk;this.yg=v(this.pi,this);this.ka={}},Nl,Ol=function(){Nl||(Nl=new Ml);return Nl},Pl=function(a,b){switch(b){case "session":return a.si;case "none":return a.sj;default:return a.Vh}};
Ml.prototype.wa=function(a,b){return this.Mh+this.pg+a.name+(b?this.pg+b:"")};var Ql=function(a,b,c){var d=a.wa(b,c),e=Pl(a,b.M);return a.get(b,c).then(function(f){var g=null;try{g=ch(q.localStorage.getItem(d))}catch(h){}if(g&&!f)return q.localStorage.removeItem(d),a.set(b,g,c);g&&f&&"localStorage"!=e.type&&q.localStorage.removeItem(d)})};l=Ml.prototype;l.get=function(a,b){return Pl(this,a.M).get(this.wa(a,b))};l.remove=function(a,b){b=this.wa(a,b);"local"==a.M&&(this.ka[b]=null);return Pl(this,a.M).remove(b)};
l.set=function(a,b,c){var d=this.wa(a,c),e=this,f=Pl(this,a.M);return f.set(d,b).then(function(){return f.get(d)}).then(function(g){"local"==a.M&&(e.ka[d]=g)})};l.addListener=function(a,b,c){a=this.wa(a,b);this.Ei&&(this.ka[a]=q.localStorage.getItem(a));Ta(this.sa)&&this.xg();this.sa[a]||(this.sa[a]=[]);this.sa[a].push(c)};l.removeListener=function(a,b,c){a=this.wa(a,b);this.sa[a]&&(Pa(this.sa[a],function(d){return d==c}),0==this.sa[a].length&&delete this.sa[a]);Ta(this.sa)&&this.Oe()};
l.xg=function(){Pl(this,"local").mb(this.yg);this.bi||(ug()||!mh())&&q.indexedDB||!this.Ei||Rl(this)};var Rl=function(a){Sl(a);a.Qf=setInterval(function(){for(var b in a.sa){var c=q.localStorage.getItem(b),d=a.ka[b];c!=d&&(a.ka[b]=c,c=new Wd({type:"storage",key:b,target:window,oldValue:d,newValue:c,Zf:!0}),a.pi(c))}},1E3)},Sl=function(a){a.Qf&&(clearInterval(a.Qf),a.Qf=null)};Ml.prototype.Oe=function(){Pl(this,"local").zb(this.yg);Sl(this)};
Ml.prototype.pi=function(a){if(a&&a.bj){var b=a.qa.key;if(null==b)for(var c in this.sa){var d=this.ka[c];"undefined"===typeof d&&(d=null);var e=q.localStorage.getItem(c);e!==d&&(this.ka[c]=e,this.kf(c))}else if(0==b.indexOf(this.Mh+this.pg)&&this.sa[b]){"undefined"!==typeof a.qa.Zf?Pl(this,"local").zb(this.yg):Sl(this);if(this.Zj)if(c=q.localStorage.getItem(b),d=a.qa.newValue,d!==c)null!==d?q.localStorage.setItem(b,d):q.localStorage.removeItem(b);else if(this.ka[b]===d&&"undefined"===typeof a.qa.Zf)return;
var f=this;c=function(){if("undefined"!==typeof a.qa.Zf||f.ka[b]!==q.localStorage.getItem(b))f.ka[b]=q.localStorage.getItem(b),f.kf(b)};Fc&&Tc&&10==Tc&&q.localStorage.getItem(b)!==a.qa.newValue&&a.qa.newValue!==a.qa.oldValue?setTimeout(c,10):c()}}else y(a,v(this.kf,this))};Ml.prototype.kf=function(a){this.sa[a]&&y(this.sa[a],function(b){b()})};var Tl=function(a){this.D=a;this.o=Ol()},Vl=function(a){return a.o.get(Ul,a.D).then(function(b){return Hh(b)})};Tl.prototype.cc=function(a){this.o.addListener(Ul,this.D,a)};Tl.prototype.vd=function(a){this.o.removeListener(Ul,this.D,a)};var Ul={name:"authEvent",M:"local"},Wl={name:"redirectEvent",M:"session"};var Xl=function(){this.o=Ol()};Xl.prototype.nc=function(a){return this.o.get(Yl,a)};var $l=function(a){return a.o.get(Zl).then(function(b){b=b||{};return b.type&&b.apiKey?new Ih(b.apiKey,b.appName||"",b.type,b.eventId,b.redirectUrl,b.clientVersion,b.displayName,b.apn,b.ibi,b.eid,b.fw,b.clientId,b.sha1Cert,b.tenantId,b.providerId,b.appId,b.publicKey):null})},am=function(a,b,c){return a.o.set(Ul,c.m(),b)},Zl={name:"oauthHelperState",M:"session"},Yl={name:"sessionId",M:"session"};var bm=function(){this.Sf=null;this.Ud=[]};bm.prototype.subscribe=function(a){var b=this;this.Ud.push(a);this.Sf||(this.Sf=function(c){for(var d=0;d<b.Ud.length;d++)b.Ud[d](c)},a=M("universalLinks.subscribe",q),"function"===typeof a&&a(null,this.Sf))};bm.prototype.unsubscribe=function(a){Pa(this.Ud,function(b){return b==a})};var cm=null;var dm=function(a,b,c,d,e,f){this.J=a;this.A=b;this.B=c;this.s=f;this.Qa=d||null;this.pa=e||null;this.ri=b+":"+c;this.ak=new Xl;this.oh=new Tl(this.ri);this.Mf=null;this.Ya=[];this.vj=500;this.Qj=2E3;this.fd=this.Ae=null},em=function(a){return new P("invalid-cordova-configuration",a)};
dm.prototype.Ob=function(){return this.gd?this.gd:this.gd=Lg().then(function(){if("function"!==typeof M("universalLinks.subscribe",q))throw em("cordova-universal-links-plugin-fix is not installed");if("undefined"===typeof M("BuildInfo.packageName",q))throw em("cordova-plugin-buildinfo is not installed");if("function"!==typeof M("cordova.plugins.browsertab.openUrl",q))throw em("cordova-plugin-browsertab is not installed");if("function"!==typeof M("cordova.InAppBrowser.open",q))throw em("cordova-plugin-inappbrowser is not installed");
},function(){throw new P("cordova-not-ready");})};var fm=function(){for(var a=20,b=[];0<a;)b.push("1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ".charAt(Math.floor(62*Math.random()))),a--;return b.join("")},gm=function(a){var b=new Td;b.update(a);return Fd(b.digest())};l=dm.prototype;l.Ad=function(a,b){b(new P("operation-not-supported-in-this-environment"));return I()};l.pd=function(){return J(new P("operation-not-supported-in-this-environment"))};l.ui=function(){return!1};l.oi=function(){return!0};
l.rh=function(){return!0};
l.qd=function(a,b,c,d){if(this.Ae)return J(new P("redirect-operation-pending"));var e=this,f=q.document,g=null,h=null,k=null,n=null;return this.Ae=I().then(function(){Oi(b);return hm(e)}).then(function(){return im(e,a,b,c,d)}).then(function(){return(new H(function(p,r){h=function(){var t=M("cordova.plugins.browsertab.close",q);p();"function"===typeof t&&t();e.fd&&"function"===typeof e.fd.close&&(e.fd.close(),e.fd=null);return!1};e.cc(h);k=function(){g||(g=fg(e.Qj).then(function(){r(new P("redirect-cancelled-by-user"))}))};n=
function(){gh()&&k()};f.addEventListener("resume",k,!1);L().toLowerCase().match(/android/)||f.addEventListener("visibilitychange",n,!1)})).h(function(p){return jm(e).then(function(){throw p;})})}).Bb(function(){k&&f.removeEventListener("resume",k,!1);n&&f.removeEventListener("visibilitychange",n,!1);g&&g.cancel();h&&e.vd(h);e.Ae=null})};
var im=function(a,b,c,d,e){var f=fm(),g=new Gh(b,d,null,f,new P("no-auth-event"),null,e),h=M("BuildInfo.packageName",q);if("string"!==typeof h)throw new P("invalid-cordova-configuration");var k=M("BuildInfo.displayName",q),n={};if(L().toLowerCase().match(/iphone|ipad|ipod/))n.ibi=h;else if(L().toLowerCase().match(/android/))n.apn=h;else return J(new P("operation-not-supported-in-this-environment"));k&&(n.appDisplayName=k);f=gm(f);n.sessionId=f;var p=uk(a.J,a.A,a.B,b,c,null,d,a.Qa,n,a.pa,e,a.s);return a.Ob().then(function(){return am(a.ak,
a.ri,g)}).then(function(){var r=M("cordova.plugins.browsertab.isAvailable",q);if("function"!==typeof r)throw new P("invalid-cordova-configuration");var t=null;r(function(z){if(z){t=M("cordova.plugins.browsertab.openUrl",q);if("function"!==typeof t)throw new P("invalid-cordova-configuration");t(p)}else{t=M("cordova.InAppBrowser.open",q);if("function"!==typeof t)throw new P("invalid-cordova-configuration");z=t;var aa=L();aa=!(!aa.match(/(iPad|iPhone|iPod).*OS 7_\d/i)&&!aa.match(/(iPad|iPhone|iPod).*OS 8_\d/i));
a.fd=z(p,aa?"_blank":"_system","location=yes")}})})};dm.prototype.Uc=function(a){for(var b=0;b<this.Ya.length;b++)try{this.Ya[b](a)}catch(c){}};
var hm=function(a){a.Mf||(a.Mf=a.Ob().then(function(){return new H(function(b){var c=function(d){b(d);a.vd(c);return!1};a.cc(c);km(a)})}));return a.Mf},jm=function(a){var b=null;return Vl(a.oh).then(function(c){b=c;c=a.oh;return c.o.remove(Ul,c.D)}).then(function(){return b})},km=function(a){var b=new Gh("unknown",null,null,null,new P("no-auth-event")),c=!1,d=fg(a.vj).then(function(){return jm(a).then(function(){c||a.Uc(b)})}),e=function(g){c=!0;d&&d.cancel();jm(a).then(function(h){var k=b;if(h&&
g&&g.url){var n=null;k=Th(g.url);-1!=k.indexOf("/__/auth/callback")&&(n=E(k),n=ch(D(n,"firebaseError")||null),n=(n="object"===typeof n?Fh(n):null)?new Gh(h.getType(),h.T,null,null,n,null,h.C):new Gh(h.getType(),h.T,k,h.nc(),null,null,h.C));k=n||b}a.Uc(k)})},f=q.handleOpenURL;q.handleOpenURL=function(g){0==g.toLowerCase().indexOf(M("BuildInfo.packageName",q).toLowerCase()+"://")&&e({url:g});if("function"===typeof f)try{f(g)}catch(h){console.error(h)}};cm||(cm=new bm);cm.subscribe(e)};
dm.prototype.cc=function(a){this.Ya.push(a);hm(this).h(function(b){"auth/invalid-cordova-configuration"===b.code&&(b=new Gh("unknown",null,null,null,new P("no-auth-event")),a(b))})};dm.prototype.vd=function(a){Pa(this.Ya,function(b){return b==a})};var lm=function(a){this.D=a;this.o=Ol()},nm=function(a){return a.o.set(mm,"pending",a.D)},om=function(a){return a.o.remove(mm,a.D)},pm=function(a){return a.o.get(mm,a.D).then(function(b){return"pending"==b})},mm={name:"pendingRedirect",M:"session"};var um=function(a,b,c,d){this.Ee={};this.Pf=0;this.J=a;this.A=b;this.B=c;this.s=d;this.Cd=[];this.rc=!1;this.ff=v(this.Gf,this);this.vb=new qm(this);this.cg=new rm(this);this.nd=new lm(sm(this.A,this.B));this.Cb={};this.Cb.unknown=this.vb;this.Cb.signInViaRedirect=this.vb;this.Cb.linkViaRedirect=this.vb;this.Cb.reauthViaRedirect=this.vb;this.Cb.signInViaPopup=this.cg;this.Cb.linkViaPopup=this.cg;this.Cb.reauthViaPopup=this.cg;this.xa=tm(this.J,this.A,this.B,tg,this.s)},tm=function(a,b,c,d,e){var f=
firebase.SDK_VERSION||null;return Kg()?new dm(a,b,c,f,d,e):new pk(a,b,c,f,d,e)};um.prototype.reset=function(){this.rc=!1;this.xa.vd(this.ff);this.xa=tm(this.J,this.A,this.B,null,this.s);this.Ee={}};um.prototype.hc=function(){this.vb.hc()};um.prototype.initialize=function(){var a=this;this.rc||(this.rc=!0,this.xa.cc(this.ff));var b=this.xa;return this.xa.Ob().h(function(c){a.xa==b&&a.reset();throw c;})};
var xm=function(a){a.xa.oi()&&a.initialize().h(function(b){var c=new Gh("unknown",null,null,null,new P("operation-not-supported-in-this-environment"));vm(b)&&a.Gf(c)});a.xa.rh()||wm(a.vb)};l=um.prototype;l.subscribe=function(a){Ma(this.Cd,a)||this.Cd.push(a);if(!this.rc){var b=this;pm(this.nd).then(function(c){c?om(b.nd).then(function(){b.initialize().h(function(d){var e=new Gh("unknown",null,null,null,new P("operation-not-supported-in-this-environment"));vm(d)&&b.Gf(e)})}):xm(b)}).h(function(){xm(b)})}};
l.unsubscribe=function(a){Pa(this.Cd,function(b){return b==a})};l.Gf=function(a){if(!a)throw new P("invalid-auth-event");6E5<=Date.now()-this.Pf&&(this.Ee={},this.Pf=0);if(a&&a.getUid()&&this.Ee.hasOwnProperty(a.getUid()))return!1;for(var b=!1,c=0;c<this.Cd.length;c++){var d=this.Cd[c];if(d.Ng(a.getType(),a.T)){if(b=this.Cb[a.getType()])b.Wh(a,d),a&&(a.nc()||a.T)&&(this.Ee[a.getUid()]=!0,this.Pf=Date.now());b=!0;break}}wm(this.vb);return b};l.getRedirectResult=function(){return this.vb.getRedirectResult()};
l.pd=function(a,b,c,d,e,f){var g=this;return this.xa.pd(a,b,c,function(){g.rc||(g.rc=!0,g.xa.cc(g.ff))},function(){g.reset()},d,e,f)};var vm=function(a){return a&&"auth/cordova-not-ready"==a.code?!0:!1};um.prototype.qd=function(a,b,c,d){var e=this,f;return nm(this.nd).then(function(){return e.xa.qd(a,b,c,d).h(function(g){if(vm(g))throw new P("operation-not-supported-in-this-environment");f=g;return om(e.nd).then(function(){throw f;})}).then(function(){return e.xa.ui()?new H(function(){}):om(e.nd).then(function(){return e.getRedirectResult()}).then(function(){}).h(function(){})})})};
um.prototype.Ad=function(a,b,c,d){return this.xa.Ad(c,function(e){a.Vb(b,null,e,d)},ym.get())};var sm=function(a,b,c){a=a+":"+b;c&&(a=a+":"+c.url);return a},Am=function(a,b,c,d){var e=sm(b,c,d);zm[e]||(zm[e]=new um(a,b,c,d));return zm[e]},ym=new fh(2E3,1E4),Bm=new fh(3E4,6E4),zm={},qm=function(a){this.o=a;this.Dc=null;this.td=[];this.sd=[];this.Bc=null;this.wi=this.ud=!1};qm.prototype.reset=function(){this.Dc=null;this.Bc&&(this.Bc.cancel(),this.Bc=null)};
qm.prototype.Wh=function(a,b){if(a){this.reset();this.ud=!0;var c=a.getType(),d=a.T,e=a.getError()&&"auth/web-storage-unsupported"==a.getError().code,f=a.getError()&&"auth/operation-not-supported-in-this-environment"==a.getError().code;this.wi=!(!e&&!f);"unknown"!=c||e||f?a.Y?this.ig(a,b):b.Zc(c,d)?this.jg(a,b):J(new P("invalid-auth-event")):(Cm(this,!1,null,null),I())}else J(new P("invalid-auth-event"))};var wm=function(a){a.ud||(a.ud=!0,Cm(a,!1,null,null))};
qm.prototype.hc=function(){this.ud&&!this.wi&&Cm(this,!1,null,null)};qm.prototype.ig=function(a){Cm(this,!0,null,a.getError());I()};qm.prototype.jg=function(a,b){var c=this,d=a.T,e=a.getType();b=b.Zc(e,d);d=a.Xa;e=a.nc();var f=a.dg,g=a.C,h=!!a.getType().match(/Redirect$/);b(d,e,g,f).then(function(k){Cm(c,h,k,null)}).h(function(k){Cm(c,h,null,k)})};
var Dm=function(a,b){a.Dc=function(){return J(b)};if(a.sd.length)for(var c=0;c<a.sd.length;c++)a.sd[c](b)},Em=function(a,b){a.Dc=function(){return I(b)};if(a.td.length)for(var c=0;c<a.td.length;c++)a.td[c](b)},Cm=function(a,b,c,d){b?d?Dm(a,d):Em(a,c):Em(a,{user:null});a.td=[];a.sd=[]};qm.prototype.getRedirectResult=function(){var a=this;return new H(function(b,c){a.Dc?a.Dc().then(b,c):(a.td.push(b),a.sd.push(c),Fm(a))})};
var Fm=function(a){var b=new P("timeout");a.Bc&&a.Bc.cancel();a.Bc=fg(Bm.get()).then(function(){a.Dc||(a.ud=!0,Cm(a,!0,null,b))})},rm=function(a){this.o=a};rm.prototype.Wh=function(a,b){if(a){var c=a.getType(),d=a.T;a.Y?this.ig(a,b):b.Zc(c,d)?this.jg(a,b):J(new P("invalid-auth-event"))}else J(new P("invalid-auth-event"))};rm.prototype.ig=function(a,b){var c=a.T,d=a.getType();b.Vb(d,null,a.getError(),c);I()};
rm.prototype.jg=function(a,b){var c=a.T,d=a.getType(),e=b.Zc(d,c),f=a.Xa,g=a.nc();e(f,g,a.C,a.dg).then(function(h){b.Vb(d,h,null,c)}).h(function(h){b.Vb(d,null,h,c)})};var Gm=function(a,b,c){var d=b&&b.mfaPendingCredential;if(!d)throw new P("argument-error","Internal assert: Invalid MultiFactorResolver");this.Od=a;this.Ui=Ua(b);this.Jj=c;this.ii=new $h(null,d);this.sh=[];var e=this;y(b.mfaInfo||[],function(f){(f=Lh(f))&&e.sh.push(f)});N(this,"auth",this.Od);N(this,"session",this.ii);N(this,"hints",this.sh)};
Gm.prototype.resolveSignIn=function(a){var b=this;return a.process(this.Od.i,this.ii).then(function(c){var d=Ua(b.Ui);delete d.mfaInfo;delete d.mfaPendingCredential;Wa(d,c);return b.Jj(d)})};var Hm=function(a,b,c,d){P.call(this,"multi-factor-auth-required",d,b);this.Wj=new Gm(a,b,c);N(this,"resolver",this.Wj)};m(Hm,P);var Im=function(a,b,c){if(a&&u(a.serverResponse)&&"auth/multi-factor-auth-required"===a.code)try{return new Hm(b,a.serverResponse,c,a.message)}catch(d){}return null};var Jm=function(){};Jm.prototype.process=function(a,b,c){return"enroll"==b.type?Km(this,a,b,c):Lm(this,a,b)};var Km=function(a,b,c,d){return c.bd().then(function(e){e={idToken:e};"undefined"!==typeof d&&(e.displayName=d);Wa(e,{phoneVerificationInfo:Ei(a.Uf)});return Q(b,Xj,e)})},Lm=function(a,b,c){return c.bd().then(function(d){d={mfaPendingCredential:d};Wa(d,{phoneVerificationInfo:Ei(a.Uf)});return Q(b,Yj,d)})},Mm=function(a){N(this,"factorId",a.providerId);this.Uf=a};w(Mm,Jm);
var Nm=function(a){Mm.call(this,a);if(this.Uf.providerId!=Ii.PROVIDER_ID)throw new P("argument-error","firebase.auth.PhoneMultiFactorAssertion requires a valid firebase.auth.PhoneAuthCredential");};w(Nm,Mm);var Om=function(a,b){G.call(this,a);for(var c in b)this[c]=b[c]};m(Om,G);var Qm=function(a,b){this.kb=a;this.Yd=[];this.wk=v(this.pj,this);ie(this.kb,"userReloaded",this.wk);var c=[];b&&b.multiFactor&&b.multiFactor.enrolledFactors&&y(b.multiFactor.enrolledFactors,function(d){var e=null,f={};if(d){d.uid&&(f.mfaEnrollmentId=d.uid);d.displayName&&(f.displayName=d.displayName);d.enrollmentTime&&(f.enrolledAt=(new Date(d.enrollmentTime)).toISOString());d.phoneNumber&&(f.phoneInfo=d.phoneNumber);try{e=new Kh(f)}catch(g){}d=e}else d=null;d&&c.push(d)});Pm(this,c)},Rm=function(a){var b=
[];y(a.mfaInfo||[],function(c){(c=Lh(c))&&b.push(c)});return b};Qm.prototype.pj=function(a){Pm(this,Rm(a.xk))};var Pm=function(a,b){a.Yd=b;N(a,"enrolledFactors",b)};l=Qm.prototype;l.copy=function(a){Pm(this,a.Yd)};l.getSession=function(){return this.kb.getIdToken().then(function(a){return new $h(a,null)})};l.enroll=function(a,b){var c=this,d=this.kb.i;return this.getSession().then(function(e){return a.process(d,e,b)}).then(function(e){Sm(c.kb,e);return c.kb.reload()})};
l.unenroll=function(a){var b=this,c="string"===typeof a?a:a.uid,d=this.kb.i;return this.kb.getIdToken().then(function(e){return Q(d,dk,{idToken:e,mfaEnrollmentId:c})}).then(function(e){var f=Ja(b.Yd,function(g){return g.uid!=c});Pm(b,f);Sm(b.kb,e);return b.kb.reload().h(function(g){if("auth/user-token-expired"!=g.code)throw g;})})};l.m=function(){return{multiFactor:{enrolledFactors:Ka(this.Yd,function(a){return a.m()})}}};var Tm=function(a){this.i=a;this.na=this.la=null;this.jc=Date.now()};Tm.prototype.m=function(){return{apiKey:this.i.A,refreshToken:this.la,accessToken:this.na&&this.na.toString(),expirationTime:this.jc}};var Um=function(a,b){"undefined"===typeof b&&(a.na?(b=a.na,b=b.zf-b.rj):b=0);a.jc=Date.now()+1E3*b},Vm=function(a,b){a.na=Yh(b.idToken||"");a.la=b.refreshToken;b=b.expiresIn;Um(a,"undefined"!==typeof b?Number(b):void 0)};Tm.prototype.copy=function(a){this.na=a.na;this.la=a.la;this.jc=a.jc};
var Wm=function(a,b){return gj(a.i,b).then(function(c){a.na=Yh(c.access_token);a.la=c.refresh_token;Um(a,c.expires_in);return{accessToken:a.na.toString(),refreshToken:a.la}}).h(function(c){"auth/user-token-expired"==c.code&&(a.la=null);throw c;})};Tm.prototype.getToken=function(a){a=!!a;return this.na&&!this.la?J(new P("user-token-expired")):a||!this.na||Date.now()>this.jc-3E4?this.la?Wm(this,{grant_type:"refresh_token",refresh_token:this.la}):I(null):I({accessToken:this.na.toString(),refreshToken:this.la})};var Xm=function(a,b){this.Xg=a||null;this.Fh=b||null;O(this,{lastSignInTime:jh(b||null),creationTime:jh(a||null)})};Xm.prototype.clone=function(){return new Xm(this.Xg,this.Fh)};Xm.prototype.m=function(){return{lastLoginAt:this.Fh,createdAt:this.Xg}};
var Ym=function(a,b,c,d,e,f){O(this,{uid:a,displayName:d||null,photoURL:e||null,email:c||null,phoneNumber:f||null,providerId:b})},R=function(a,b,c){ue.call(this);this.Z=[];this.A=a.apiKey;this.B=a.appName;this.J=a.authDomain||null;var d=firebase.SDK_VERSION?Ug("JsCore",firebase.SDK_VERSION):null;this.i=new Wi(this.A,sg(tg),d);(this.s=a.emulatorConfig||null)&&$i(this.i,this.s);this.La=new Tm(this.i);Zm(this,b.idToken);Vm(this.La,b);N(this,"refreshToken",this.La.la);$m(this,c||{});this.od=!1;this.J&&
Yg()&&(this.v=Am(this.J,this.A,this.B,this.s));this.Ne=[];this.Ma=null;this.yc=an(this);this.Kc=v(this.If,this);var e=this;this.Ga=null;this.Rh=function(f){e.Fc(f.languageCode)};this.Nf=null;this.Ph=function(f){bn(e,f.emulatorConfig)};this.uf=null;this.U=[];this.Qh=function(f){cn(e,f.Yc)};this.Df=null;this.ue=new Qm(this,c);N(this,"multiFactor",this.ue)};m(R,ue);R.prototype.Fc=function(a){this.Ga=a;Yi(this.i,a)};
var bn=function(a,b){a.s=b;$i(a.i,b);a.v&&(b=a.v,a.v=Am(a.J,a.A,a.B,a.s),a.od&&(b.unsubscribe(a),a.v.subscribe(a)))},dn=function(a,b){a.Nf&&re(a.Nf,"languageCodeChanged",a.Rh);(a.Nf=b)&&ie(b,"languageCodeChanged",a.Rh)},en=function(a,b){a.uf&&re(a.uf,"emulatorConfigChanged",a.Ph);(a.uf=b)&&ie(b,"emulatorConfigChanged",a.Ph)},cn=function(a,b){a.U=b;aj(a.i,firebase.SDK_VERSION?Ug("JsCore",firebase.SDK_VERSION,a.U):null)},fn=function(a,b){a.Df&&re(a.Df,"frameworkChanged",a.Qh);(a.Df=b)&&ie(b,"frameworkChanged",
a.Qh)};R.prototype.If=function(){this.yc.vc&&(this.yc.stop(),this.yc.start())};
var gn=function(a){try{return firebase.app(a.B).auth()}catch(b){throw new P("internal-error","No firebase.auth.Auth instance is available for the Firebase App '"+a.B+"'!");}},an=function(a){return new Al(function(){return a.getIdToken(!0)},function(b){return b&&"auth/network-request-failed"==b.code?!0:!1},function(){var b=a.La.jc-Date.now()-3E5;return 0<b?b:0})},hn=function(a){a.Tc||a.yc.vc||(a.yc.start(),re(a,"tokenChanged",a.Kc),ie(a,"tokenChanged",a.Kc))},jn=function(a){re(a,"tokenChanged",a.Kc);
a.yc.stop()},Zm=function(a,b){a.Eh=b;N(a,"_lat",b)},kn=function(a,b){Pa(a.Ne,function(c){return c==b})},ln=function(a){for(var b=[],c=0;c<a.Ne.length;c++)b.push(a.Ne[c](a));return Af(b).then(function(){return a})},mn=function(a){a.v&&!a.od&&(a.od=!0,a.v.subscribe(a))},$m=function(a,b){O(a,{uid:b.uid,displayName:b.displayName||null,photoURL:b.photoURL||null,email:b.email||null,emailVerified:b.emailVerified||!1,phoneNumber:b.phoneNumber||null,isAnonymous:b.isAnonymous||!1,tenantId:b.tenantId||null,
metadata:new Xm(b.createdAt,b.lastLoginAt),providerData:[]});a.i.C=a.tenantId},nn=function(){},on=function(a){return I().then(function(){if(a.Tc)throw new P("app-deleted");})},pn=function(a){return Ka(a.providerData,function(b){return b.providerId})},rn=function(a,b){b&&(qn(a,b.providerId),a.providerData.push(b))},qn=function(a,b){Pa(a.providerData,function(c){return c.providerId==b})},sn=function(a,b,c){("uid"!=b||c)&&a.hasOwnProperty(b)&&N(a,b,c)};
R.prototype.copy=function(a){var b=this;b!=a&&(O(this,{uid:a.uid,displayName:a.displayName,photoURL:a.photoURL,email:a.email,emailVerified:a.emailVerified,phoneNumber:a.phoneNumber,isAnonymous:a.isAnonymous,tenantId:a.tenantId,providerData:[]}),a.metadata?N(this,"metadata",a.metadata.clone()):N(this,"metadata",new Xm),y(a.providerData,function(c){rn(b,c)}),this.La.copy(a.La),N(this,"refreshToken",this.La.la),this.ue.copy(a.ue))};R.prototype.reload=function(){var a=this;return this.l(on(this).then(function(){return tn(a).then(function(){return ln(a)}).then(nn)}))};
var tn=function(a){return a.getIdToken().then(function(b){var c=a.isAnonymous;return Q(a.i,Zj,{idToken:b}).then(v(a.Nj,a)).then(function(){c||sn(a,"isAnonymous",!1);return b})})};R.prototype.getIdTokenResult=function(a){return this.getIdToken(a).then(function(b){return new Zh(b)})};
R.prototype.getIdToken=function(a){var b=this;return this.l(on(this).then(function(){return b.La.getToken(a)}).then(function(c){if(!c)throw new P("internal-error");c.accessToken!=b.Eh&&(Zm(b,c.accessToken),b.ub());sn(b,"refreshToken",c.refreshToken);return c.accessToken}))};var Sm=function(a,b){b.idToken&&a.Eh!=b.idToken&&(Vm(a.La,b),a.ub(),Zm(a,b.idToken),sn(a,"refreshToken",a.La.la))};R.prototype.ub=function(){this.dispatchEvent(new Om("tokenChanged"))};
R.prototype.Nj=function(a){a=a.users;if(!a||!a.length)throw new P("internal-error");a=a[0];$m(this,{uid:a.localId,displayName:a.displayName,photoURL:a.photoUrl,email:a.email,emailVerified:!!a.emailVerified,phoneNumber:a.phoneNumber,lastLoginAt:a.lastLoginAt,createdAt:a.createdAt,tenantId:a.tenantId});for(var b=un(a),c=0;c<b.length;c++)rn(this,b[c]);sn(this,"isAnonymous",!(this.email&&a.passwordHash)&&!(this.providerData&&this.providerData.length));this.dispatchEvent(new Om("userReloaded",{xk:a}))};
var un=function(a){return(a=a.providerUserInfo)&&a.length?Ka(a,function(b){return new Ym(b.rawId,b.providerId,b.email,b.displayName,b.photoUrl,b.phoneNumber)}):[]};R.prototype.reauthenticateAndRetrieveDataWithCredential=function(a){sh("firebase.User.prototype.reauthenticateAndRetrieveDataWithCredential is deprecated. Please use firebase.User.prototype.reauthenticateWithCredential instead.");return this.reauthenticateWithCredential(a)};
R.prototype.reauthenticateWithCredential=function(a){var b=this,c=null;return this.l(a.kd(this.i,this.uid).then(function(d){Sm(b,d);c=vn(b,d,"reauthenticate");b.Ma=null;return b.reload()}).then(function(){return c}),!0)};var wn=function(a,b){return tn(a).then(function(){if(Ma(pn(a),b))return ln(a).then(function(){throw new P("provider-already-linked");})})};
R.prototype.linkAndRetrieveDataWithCredential=function(a){sh("firebase.User.prototype.linkAndRetrieveDataWithCredential is deprecated. Please use firebase.User.prototype.linkWithCredential instead.");return this.linkWithCredential(a)};R.prototype.linkWithCredential=function(a){var b=this,c=null;return this.l(wn(this,a.providerId).then(function(){return b.getIdToken()}).then(function(d){return a.sc(b.i,d)}).then(function(d){c=vn(b,d,"link");return xn(b,d)}).then(function(){return c}))};
R.prototype.linkWithPhoneNumber=function(a,b){var c=this;return this.l(wn(this,"phone").then(function(){return zl(gn(c),a,b,v(c.linkWithCredential,c))}))};R.prototype.reauthenticateWithPhoneNumber=function(a,b){var c=this;return this.l(I().then(function(){return zl(gn(c),a,b,v(c.reauthenticateWithCredential,c))}),!0)};var vn=function(a,b,c){var d=Ni(b);b=Jl(b);return vh({user:a,credential:d,additionalUserInfo:b,operationType:c})},xn=function(a,b){Sm(a,b);return a.reload().then(function(){return a})};
l=R.prototype;l.updateEmail=function(a){var b=this;return this.l(this.getIdToken().then(function(c){return b.i.updateEmail(c,a)}).then(function(c){Sm(b,c);return b.reload()}))};l.updatePhoneNumber=function(a){var b=this;return this.l(this.getIdToken().then(function(c){return a.sc(b.i,c)}).then(function(c){Sm(b,c);return b.reload()}))};l.updatePassword=function(a){var b=this;return this.l(this.getIdToken().then(function(c){return b.i.updatePassword(c,a)}).then(function(c){Sm(b,c);return b.reload()}))};
l.updateProfile=function(a){if(void 0===a.displayName&&void 0===a.photoURL)return on(this);var b=this;return this.l(this.getIdToken().then(function(c){return b.i.updateProfile(c,{displayName:a.displayName,photoUrl:a.photoURL})}).then(function(c){Sm(b,c);sn(b,"displayName",c.displayName||null);sn(b,"photoURL",c.photoUrl||null);y(b.providerData,function(d){"password"===d.providerId&&(N(d,"displayName",b.displayName),N(d,"photoURL",b.photoURL))});return ln(b)}).then(nn))};
l.unlink=function(a){var b=this;return this.l(tn(this).then(function(c){return Ma(pn(b),a)?Jj(b.i,c,[a]).then(function(d){var e={};y(d.providerUserInfo||[],function(f){e[f.providerId]=!0});y(pn(b),function(f){e[f]||qn(b,f)});e[Ii.PROVIDER_ID]||N(b,"phoneNumber",null);return ln(b)}):ln(b).then(function(){throw new P("no-such-provider");})}))};
l.delete=function(){var a=this;return this.l(this.getIdToken().then(function(b){return Q(a.i,Wj,{idToken:b})}).then(function(){a.dispatchEvent(new Om("userDeleted"))})).then(function(){for(var b=0;b<a.Z.length;b++)a.Z[b].cancel("app-deleted");dn(a,null);en(a,null);fn(a,null);a.Z=[];a.Tc=!0;jn(a);N(a,"refreshToken",null);a.v&&a.v.unsubscribe(a)})};
l.Ng=function(a,b){return"linkViaPopup"==a&&(this.Ia||null)==b&&this.Ha||"reauthViaPopup"==a&&(this.Ia||null)==b&&this.Ha||"linkViaRedirect"==a&&(this.wb||null)==b||"reauthViaRedirect"==a&&(this.wb||null)==b?!0:!1};l.Vb=function(a,b,c,d){"linkViaPopup"!=a&&"reauthViaPopup"!=a||d!=(this.Ia||null)||(c&&this.Sb?this.Sb(c):b&&!c&&this.Ha&&this.Ha(b),this.aa&&(this.aa.cancel(),this.aa=null),delete this.Ha,delete this.Sb)};
l.Zc=function(a,b){return"linkViaPopup"==a&&b==(this.Ia||null)?v(this.ih,this):"reauthViaPopup"==a&&b==(this.Ia||null)?v(this.jh,this):"linkViaRedirect"==a&&(this.wb||null)==b?v(this.ih,this):"reauthViaRedirect"==a&&(this.wb||null)==b?v(this.jh,this):null};l.be=function(){return Vg(this.uid+":::")};l.linkWithPopup=function(a){var b=this;return yn(this,"linkViaPopup",a,function(){return wn(b,a.providerId).then(function(){return ln(b)})},!1)};
l.reauthenticateWithPopup=function(a){return yn(this,"reauthViaPopup",a,function(){return I()},!0)};
var yn=function(a,b,c,d,e){if(!Yg())return J(new P("operation-not-supported-in-this-environment"));if(a.Ma&&!e)return J(a.Ma);var f=Bh(c.providerId),g=a.be(),h=null;(!Zg()||Ng())&&a.J&&c.isOAuthProvider&&(h=uk(a.J,a.A,a.B,b,c,null,g,firebase.SDK_VERSION||null,null,null,a.tenantId,a.s));var k=Dg(h,f&&f.xc,f&&f.wc);d=d().then(function(){zn(a);if(!e)return a.getIdToken().then(function(){})}).then(function(){return a.v.pd(k,b,c,g,!!h,a.tenantId)}).then(function(){return new H(function(n,p){a.Vb(b,null,
new P("cancelled-popup-request"),a.Ia||null);a.Ha=n;a.Sb=p;a.Ia=g;a.aa=a.v.Ad(a,b,k,g)})}).then(function(n){k&&Cg(k);return n?vh(n):null}).h(function(n){k&&Cg(k);throw n;});return a.l(d,e)};R.prototype.linkWithRedirect=function(a){var b=this;return An(this,"linkViaRedirect",a,function(){return wn(b,a.providerId)},!1)};R.prototype.reauthenticateWithRedirect=function(a){return An(this,"reauthViaRedirect",a,function(){return I()},!0)};
var An=function(a,b,c,d,e){if(!Yg())return J(new P("operation-not-supported-in-this-environment"));if(a.Ma&&!e)return J(a.Ma);var f=null,g=a.be();d=d().then(function(){zn(a);if(!e)return a.getIdToken().then(function(){})}).then(function(){a.wb=g;return ln(a)}).then(function(h){a.xb&&(h=a.xb,h=h.o.set(Bn,a.m(),h.D));return h}).then(function(){return a.v.qd(b,c,g,a.tenantId)}).h(function(h){f=h;if(a.xb)return Cn(a.xb);throw f;}).then(function(){if(f)throw f;});return a.l(d,e)},zn=function(a){if(!a.v||
!a.od){if(a.v&&!a.od)throw new P("internal-error");throw new P("auth-domain-config-required");}};l=R.prototype;l.ih=function(a,b,c,d){var e=this;this.aa&&(this.aa.cancel(),this.aa=null);var f=null;c=this.getIdToken().then(function(g){return ei(e.i,{requestUri:a,postBody:d,sessionId:b,idToken:g})}).then(function(g){f=vn(e,g,"link");return xn(e,g)}).then(function(){return f});return this.l(c)};
l.jh=function(a,b,c,d){var e=this;this.aa&&(this.aa.cancel(),this.aa=null);var f=null,g=I().then(function(){return bi(fi(e.i,{requestUri:a,sessionId:b,postBody:d,tenantId:c}),e.uid)}).then(function(h){f=vn(e,h,"reauthenticate");Sm(e,h);e.Ma=null;return e.reload()}).then(function(){return f});return this.l(g,!0)};
l.sendEmailVerification=function(a){var b=this,c=null;return this.l(this.getIdToken().then(function(d){c=d;return"undefined"===typeof a||Ta(a)?{}:xl(new wl(a))}).then(function(d){return b.i.sendEmailVerification(c,d)}).then(function(d){if(b.email!=d)return b.reload()}).then(function(){}))};
l.verifyBeforeUpdateEmail=function(a,b){var c=this,d=null;return this.l(this.getIdToken().then(function(e){d=e;return"undefined"===typeof b||Ta(b)?{}:xl(new wl(b))}).then(function(e){return c.i.verifyBeforeUpdateEmail(d,a,e)}).then(function(e){if(c.email!=e)return c.reload()}).then(function(){}))};
l.l=function(a,b){var c=this,d=Dn(this,a,b);this.Z.push(d);d.Bb(function(){Na(c.Z,d)});return d.h(function(e){var f=null;e&&"auth/multi-factor-auth-required"===e.code&&(f=Im(e.m(),gn(c),v(c.Hf,c)));throw f||e;})};l.Hf=function(a){var b=null,c=this;a=bi(I(a),c.uid).then(function(d){b=vn(c,d,"reauthenticate");Sm(c,d);c.Ma=null;return c.reload()}).then(function(){return b});return this.l(a,!0)};
var Dn=function(a,b,c){return a.Ma&&!c?(b.cancel(),J(a.Ma)):b.h(function(d){!d||"auth/user-disabled"!=d.code&&"auth/user-token-expired"!=d.code||(a.Ma||a.dispatchEvent(new Om("userInvalidated")),a.Ma=d);throw d;})};R.prototype.toJSON=function(){return this.m()};
R.prototype.m=function(){var a={uid:this.uid,displayName:this.displayName,photoURL:this.photoURL,email:this.email,emailVerified:this.emailVerified,phoneNumber:this.phoneNumber,isAnonymous:this.isAnonymous,tenantId:this.tenantId,providerData:[],apiKey:this.A,appName:this.B,authDomain:this.J,stsTokenManager:this.La.m(),redirectEventId:this.wb||null};this.metadata&&Wa(a,this.metadata.m());y(this.providerData,function(b){var c=a.providerData,d=c.push,e={},f;for(f in b)b.hasOwnProperty(f)&&(e[f]=b[f]);
d.call(c,e)});Wa(a,this.ue.m());return a};
var En=function(a){if(!a.apiKey)return null;var b={apiKey:a.apiKey,authDomain:a.authDomain,appName:a.appName,emulatorConfig:a.emulatorConfig},c={};if(a.stsTokenManager&&a.stsTokenManager.accessToken){c.idToken=a.stsTokenManager.accessToken;c.refreshToken=a.stsTokenManager.refreshToken||null;var d=a.stsTokenManager.expirationTime;d&&(c.expiresIn=(d-Date.now())/1E3)}else return null;var e=new R(b,c,a);a.providerData&&y(a.providerData,function(f){f&&rn(e,vh(f))});a.redirectEventId&&(e.wb=a.redirectEventId);
return e},Fn=function(a,b,c,d){var e=new R(a,b);c&&(e.xb=c);d&&cn(e,d);return e.reload().then(function(){return e})},Gn=function(a,b,c,d){b=b||{apiKey:a.A,authDomain:a.J,appName:a.B};var e=a.La,f={};f.idToken=e.na&&e.na.toString();f.refreshToken=e.la;b=new R(b,f);c&&(b.xb=c);d&&cn(b,d);b.copy(a);return b};N(R.prototype,"providerId","firebase");var Hn=function(a){this.D=a;this.o=Ol()},Cn=function(a){return a.o.remove(Bn,a.D)},In=function(a,b){return a.o.get(Bn,a.D).then(function(c){c&&b&&(c.authDomain=b);return En(c||{})})},Bn={name:"redirectUser",M:"session"};var Kn=function(a){this.D=a;this.o=Ol();this.oa=null;this.Xf=this.cb();this.o.addListener(Jn("local"),this.D,v(this.lk,this))};Kn.prototype.lk=function(){var a=this,b=Jn("local");Ln(this,function(){return I().then(function(){return a.oa&&"local"!=a.oa.M?a.o.get(b,a.D):null}).then(function(c){if(c)return Mn(a,"local").then(function(){a.oa=b})})})};var Mn=function(a,b){var c=[],d;for(d in Kl)Kl[d]!==b&&c.push(a.o.remove(Jn(Kl[d]),a.D));c.push(a.o.remove(Nn,a.D));return zf(c)};
Kn.prototype.cb=function(){var a=this,b=Jn("local"),c=Jn("session"),d=Jn("none");return Ql(this.o,b,this.D).then(function(){return a.o.get(c,a.D)}).then(function(e){return e?c:a.o.get(d,a.D).then(function(f){return f?d:a.o.get(b,a.D).then(function(g){return g?b:a.o.get(Nn,a.D).then(function(h){return h?Jn(h):b})})})}).then(function(e){a.oa=e;return Mn(a,e.M)}).h(function(){a.oa||(a.oa=b)})};var Jn=function(a){return{name:"authUser",M:a}};
Kn.prototype.setPersistence=function(a){var b=null,c=this;Ll(a);return Ln(this,function(){return a!=c.oa.M?c.o.get(c.oa,c.D).then(function(d){b=d;return Mn(c,a)}).then(function(){c.oa=Jn(a);if(b)return c.o.set(c.oa,b,c.D)}):I()})};
var On=function(a){return Ln(a,function(){return a.o.set(Nn,a.oa.M,a.D)})},Pn=function(a,b){return Ln(a,function(){return a.o.set(a.oa,b.m(),a.D)})},Qn=function(a){return Ln(a,function(){return a.o.remove(a.oa,a.D)})},Rn=function(a,b,c){return Ln(a,function(){return a.o.get(a.oa,a.D).then(function(d){d&&b&&(d.authDomain=b);d&&c&&(d.emulatorConfig=c);return En(d||{})})})},Ln=function(a,b){a.Xf=a.Xf.then(b,b);return a.Xf},Nn={name:"persistence",M:"session"};var S=function(a){ue.call(this);this.Aa=!1;this.ki=new vl;N(this,"settings",this.ki);N(this,"app",a);if(this.u().options&&this.u().options.apiKey)a=firebase.SDK_VERSION?Ug("JsCore",firebase.SDK_VERSION):null,this.i=new Wi(this.u().options&&this.u().options.apiKey,sg(tg),a);else throw new P("invalid-api-key");this.Z=[];this.Eb=[];this.Jc=[];this.Ij=firebase.INTERNAL.createSubscribe(v(this.tj,this));this.Hd=void 0;this.Lj=firebase.INTERNAL.createSubscribe(v(this.uj,this));Sn(this,null);this.Na=new Kn(this.oc());
this.Ub=new Hn(this.oc());this.Nd=this.l(Tn(this));this.Ja=this.l(Un(this));this.pe=!1;this.Ff=v(this.mk,this);this.Ci=v(this.rb,this);this.Kc=v(this.If,this);this.Ai=v(this.nj,this);this.Bi=v(this.oj,this);this.v=null;Vn(this);this.INTERNAL={};this.INTERNAL["delete"]=v(this.delete,this);this.INTERNAL.logFramework=v(this.Ej,this);this.Lb=0;Wn(this);this.U=[];this.s=null};m(S,ue);S.prototype.setPersistence=function(a){a=this.Na.setPersistence(a);return this.l(a)};
S.prototype.Fc=function(a){this.Ga===a||this.Aa||(this.Ga=a,Yi(this.i,this.Ga),this.dispatchEvent(new Xn(this.Ga)))};S.prototype.useDeviceLanguage=function(){var a=q.navigator;this.Fc(a?a.languages&&a.languages[0]||a.language||a.userLanguage||null:null)};
S.prototype.useEmulator=function(a,b){if(!this.s){if(this.v)throw new P("argument-error","useEmulator() must be called immediately following firebase.auth() initialization.");b=b?!!b.disableWarnings:!1;Yn(b);this.s={url:a,disableWarnings:b};this.ki.df=!0;$i(this.i,this.s);this.dispatchEvent(new Zn(this.s))}};
var Yn=function(a){"undefined"!==typeof console&&"function"===typeof console.info&&console.info("WARNING: You are using the Auth Emulator, which is intended for local testing only.  Do not use with production credentials.");q.document&&!a&&Jg().then(function(){var b=q.document.createElement("p");b.innerText="Running in emulator mode. Do not use with production credentials.";b.style.position="fixed";b.style.width="100%";b.style.backgroundColor="#ffffff";b.style.border=".1em solid #000000";b.style.color=
"#b50000";b.style.bottom="0px";b.style.left="0px";b.style.margin="0px";b.style.zIndex=1E4;b.style.textAlign="center";b.classList.add("firebase-emulator-warning");q.document.body.appendChild(b)})};S.prototype.Ej=function(a){this.U.push(a);aj(this.i,firebase.SDK_VERSION?Ug("JsCore",firebase.SDK_VERSION,this.U):null);this.dispatchEvent(new $n(this.U))};S.prototype.ug=function(a){this.C===a||this.Aa||(this.C=a,this.i.C=this.C)};
var Wn=function(a){Object.defineProperty(a,"lc",{get:function(){return this.Ga},set:function(b){this.Fc(b)},enumerable:!1});a.Ga=null;Object.defineProperty(a,"ti",{get:function(){return this.C},set:function(b){this.ug(b)},enumerable:!1});a.C=null;Object.defineProperty(a,"emulatorConfig",{get:function(){if(this.s){var b=E(this.s.url);b=vh({protocol:b.ua,host:b.ga,port:b.Ua,options:vh({disableWarnings:this.s.disableWarnings})})}else b=null;return b},enumerable:!1})};
S.prototype.toJSON=function(){return{apiKey:this.u().options.apiKey,authDomain:this.u().options.authDomain,appName:this.u().name,currentUser:T(this)&&T(this).m()}};
var ao=function(a){return a.Vi||J(new P("auth-domain-config-required"))},Vn=function(a){var b=a.u().options.authDomain,c=a.u().options.apiKey;b&&Yg()&&(a.Vi=a.Nd.then(function(){if(!a.Aa){a.v=Am(b,c,a.u().name,a.s);a.v.subscribe(a);T(a)&&mn(T(a));if(a.yb){mn(a.yb);var d=a.yb;d.Fc(a.Ga);dn(d,a);d=a.yb;cn(d,a.U);fn(d,a);d=a.yb;bn(d,a.s);en(d,a);a.yb=null}return a.v}}))};l=S.prototype;
l.Ng=function(a,b){switch(a){case "unknown":case "signInViaRedirect":return!0;case "signInViaPopup":return this.Ia==b&&!!this.Ha;default:return!1}};l.Vb=function(a,b,c,d){"signInViaPopup"==a&&this.Ia==d&&(c&&this.Sb?this.Sb(c):b&&!c&&this.Ha&&this.Ha(b),this.aa&&(this.aa.cancel(),this.aa=null),delete this.Ha,delete this.Sb)};l.Zc=function(a,b){return"signInViaRedirect"==a||"signInViaPopup"==a&&this.Ia==b&&this.Ha?v(this.Zi,this):null};
l.Zi=function(a,b,c,d){var e=this,f={requestUri:a,postBody:d,sessionId:b,tenantId:c};this.aa&&(this.aa.cancel(),this.aa=null);return e.Nd.then(function(){return bo(e,di(e.i,f))})};l.be=function(){return Vg()};
l.signInWithPopup=function(a){if(!Yg())return J(new P("operation-not-supported-in-this-environment"));var b=this,c=Bh(a.providerId),d=this.be(),e=null;(!Zg()||Ng())&&this.u().options.authDomain&&a.isOAuthProvider&&(e=uk(this.u().options.authDomain,this.u().options.apiKey,this.u().name,"signInViaPopup",a,null,d,firebase.SDK_VERSION||null,null,null,this.C,this.s));var f=Dg(e,c&&c.xc,c&&c.wc);c=ao(this).then(function(g){return g.pd(f,"signInViaPopup",a,d,!!e,b.C)}).then(function(){return new H(function(g,
h){b.Vb("signInViaPopup",null,new P("cancelled-popup-request"),b.Ia);b.Ha=g;b.Sb=h;b.Ia=d;b.aa=b.v.Ad(b,"signInViaPopup",f,d)})}).then(function(g){f&&Cg(f);return g?vh(g):null}).h(function(g){f&&Cg(f);throw g;});return this.l(c)};l.signInWithRedirect=function(a){if(!Yg())return J(new P("operation-not-supported-in-this-environment"));var b=this,c=ao(this).then(function(){return On(b.Na)}).then(function(){return b.v.qd("signInViaRedirect",a,void 0,b.C)});return this.l(c)};
var co=function(a){if(!Yg())return J(new P("operation-not-supported-in-this-environment"));var b=ao(a).then(function(){return a.v.getRedirectResult()}).then(function(c){return c?vh(c):null});return a.l(b)};S.prototype.getRedirectResult=function(){var a=this;return co(this).then(function(b){a.v&&a.v.hc();return b}).h(function(b){a.v&&a.v.hc();throw b;})};
S.prototype.updateCurrentUser=function(a){if(!a)return J(new P("null-user"));if(this.C!=a.tenantId)return J(new P("tenant-id-mismatch"));var b=this,c={};c.apiKey=this.u().options.apiKey;c.authDomain=this.u().options.authDomain;c.appName=this.u().name;var d=Gn(a,c,b.Ub,Qa(b.U));return this.l(this.Ja.then(function(){if(b.u().options.apiKey!=a.A)return d.reload()}).then(function(){if(T(b)&&a.uid==T(b).uid)return T(b).copy(a),b.rb(a);Sn(b,d);mn(d);return b.rb(d)}).then(function(){b.ub()}))};
var eo=function(a,b){var c={};c.apiKey=a.u().options.apiKey;c.authDomain=a.u().options.authDomain;c.appName=a.u().name;a.s&&(c.emulatorConfig=a.s);return a.Nd.then(function(){return Fn(c,b,a.Ub,Qa(a.U))}).then(function(d){if(T(a)&&d.uid==T(a).uid)return T(a).copy(d),a.rb(d);Sn(a,d);mn(d);return a.rb(d)}).then(function(){a.ub()})},Sn=function(a,b){T(a)&&(kn(T(a),a.Ci),re(T(a),"tokenChanged",a.Kc),re(T(a),"userDeleted",a.Ai),re(T(a),"userInvalidated",a.Bi),jn(T(a)));b&&(b.Ne.push(a.Ci),ie(b,"tokenChanged",
a.Kc),ie(b,"userDeleted",a.Ai),ie(b,"userInvalidated",a.Bi),0<a.Lb&&hn(b));N(a,"currentUser",b);b&&(b.Fc(a.Ga),dn(b,a),cn(b,a.U),fn(b,a),bn(b,a.s),en(b,a))};S.prototype.signOut=function(){var a=this,b=this.Ja.then(function(){a.v&&a.v.hc();if(!T(a))return I();Sn(a,null);return Qn(a.Na).then(function(){a.ub()})});return this.l(b)};
var fo=function(a){var b=a.u().options.authDomain;b=In(a.Ub,b).then(function(c){if(a.yb=c)c.xb=a.Ub;return Cn(a.Ub)});return a.l(b)},Tn=function(a){var b=a.u().options.authDomain,c=fo(a).then(function(){return Rn(a.Na,b,a.s)}).then(function(d){return d?(d.xb=a.Ub,a.yb&&(a.yb.wb||null)==(d.wb||null)?d:d.reload().then(function(){return Pn(a.Na,d).then(function(){return d})}).h(function(e){return"auth/network-request-failed"==e.code?d:Qn(a.Na)})):null}).then(function(d){Sn(a,d||null)});return a.l(c)},
Un=function(a){return a.Nd.then(function(){return co(a)}).h(function(){}).then(function(){if(!a.Aa)return a.Ff()}).h(function(){}).then(function(){if(!a.Aa){a.pe=!0;var b=a.Na;b.o.addListener(Jn("local"),b.D,a.Ff)}})};l=S.prototype;
l.mk=function(){var a=this,b=this.u().options.authDomain;return Rn(this.Na,b).then(function(c){if(!a.Aa){var d;if(d=T(a)&&c){d=T(a).uid;var e=c.uid;d=void 0===d||null===d||""===d||void 0===e||null===e||""===e?!1:d==e}if(d)return T(a).copy(c),T(a).getIdToken();if(T(a)||c)Sn(a,c),c&&(mn(c),c.xb=a.Ub),a.v&&a.v.subscribe(a),a.ub()}})};l.rb=function(a){return Pn(this.Na,a)};l.If=function(){this.ub();this.rb(T(this))};l.nj=function(){this.signOut()};l.oj=function(){this.signOut()};
var bo=function(a,b){var c=null,d=null;return a.l(b.then(function(e){c=Ni(e);d=Jl(e);return eo(a,e)},function(e){var f=null;e&&"auth/multi-factor-auth-required"===e.code&&(f=Im(e.m(),a,v(a.Hf,a)));throw f||e;}).then(function(){return vh({user:T(a),credential:c,additionalUserInfo:d,operationType:"signIn"})}))};l=S.prototype;l.Hf=function(a){var b=this;return this.Ja.then(function(){return bo(b,I(a))})};l.tj=function(a){var b=this;this.addAuthTokenListener(function(){a.next(T(b))})};
l.uj=function(a){var b=this;go(this,function(){a.next(T(b))})};l.onIdTokenChanged=function(a,b,c){var d=this;this.pe&&firebase.Promise.resolve().then(function(){"function"===typeof a?a(T(d)):"function"===typeof a.next&&a.next(T(d))});return this.Ij(a,b,c)};l.onAuthStateChanged=function(a,b,c){var d=this;this.pe&&firebase.Promise.resolve().then(function(){d.Hd=d.getUid();"function"===typeof a?a(T(d)):"function"===typeof a.next&&a.next(T(d))});return this.Lj(a,b,c)};
l.cj=function(a){var b=this,c=this.Ja.then(function(){return T(b)?T(b).getIdToken(a).then(function(d){return{accessToken:d}}):null});return this.l(c)};l.signInWithCustomToken=function(a){var b=this;return this.Ja.then(function(){return bo(b,Q(b.i,ck,{token:a}))}).then(function(c){var d=c.user;sn(d,"isAnonymous",!1);b.rb(d);return c})};l.signInWithEmailAndPassword=function(a,b){var c=this;return this.Ja.then(function(){return bo(c,Q(c.i,xi,{email:a,password:b}))})};
l.createUserWithEmailAndPassword=function(a,b){var c=this;return this.Ja.then(function(){return bo(c,Q(c.i,Vj,{email:a,password:b}))})};l.signInWithCredential=function(a){var b=this;return this.Ja.then(function(){return bo(b,a.Mb(b.i))})};l.signInAndRetrieveDataWithCredential=function(a){sh("firebase.auth.Auth.prototype.signInAndRetrieveDataWithCredential is deprecated. Please use firebase.auth.Auth.prototype.signInWithCredential instead.");return this.signInWithCredential(a)};
l.signInAnonymously=function(){var a=this;return this.Ja.then(function(){var b=T(a);if(b&&b.isAnonymous){var c=vh({providerId:null,isNewUser:!1});return vh({user:b,credential:null,additionalUserInfo:c,operationType:"signIn"})}return bo(a,a.i.signInAnonymously()).then(function(d){var e=d.user;sn(e,"isAnonymous",!0);a.rb(e);return d})})};l.oc=function(){var a=this.u().options.apiKey,b=this.u().name;return a+":"+b};l.u=function(){return this.app};var T=function(a){return a.currentUser};
S.prototype.getUid=function(){return T(this)&&T(this).uid||null};var ho=function(a){return T(a)&&T(a)._lat||null};l=S.prototype;l.ub=function(){if(this.pe){for(var a=0;a<this.Eb.length;a++)if(this.Eb[a])this.Eb[a](ho(this));if(this.Hd!==this.getUid()&&this.Jc.length)for(this.Hd=this.getUid(),a=0;a<this.Jc.length;a++)if(this.Jc[a])this.Jc[a](ho(this))}};l.Li=function(a){this.addAuthTokenListener(a);this.Lb++;0<this.Lb&&T(this)&&hn(T(this))};
l.Rj=function(a){var b=this;y(this.Eb,function(c){c==a&&b.Lb--});0>this.Lb&&(this.Lb=0);0==this.Lb&&T(this)&&jn(T(this));this.removeAuthTokenListener(a)};l.addAuthTokenListener=function(a){var b=this;this.Eb.push(a);this.l(this.Ja.then(function(){b.Aa||Ma(b.Eb,a)&&a(ho(b))}))};l.removeAuthTokenListener=function(a){Pa(this.Eb,function(b){return b==a})};var go=function(a,b){a.Jc.push(b);a.l(a.Ja.then(function(){!a.Aa&&Ma(a.Jc,b)&&a.Hd!==a.getUid()&&(a.Hd=a.getUid(),b(ho(a)))}))};l=S.prototype;
l.delete=function(){this.Aa=!0;for(var a=0;a<this.Z.length;a++)this.Z[a].cancel("app-deleted");this.Z=[];this.Na&&(a=this.Na,a.o.removeListener(Jn("local"),a.D,this.Ff));this.v&&(this.v.unsubscribe(this),this.v.hc());return firebase.Promise.resolve()};l.l=function(a){var b=this;this.Z.push(a);a.Bb(function(){Na(b.Z,a)});return a};l.fetchSignInMethodsForEmail=function(a){return this.l(nj(this.i,a))};l.isSignInWithEmailLink=function(a){return!!Bi(a)};
l.sendSignInLinkToEmail=function(a,b){var c=this;return this.l(I().then(function(){var d=new wl(b);if(!d.Og)throw new P("argument-error","handleCodeInApp must be true when sending sign in link to email");return xl(d)}).then(function(d){return c.i.sendSignInLinkToEmail(a,d)}).then(function(){}))};l.verifyPasswordResetCode=function(a){return this.checkActionCode(a).then(function(b){return b.data.email})};l.confirmPasswordReset=function(a,b){return this.l(this.i.confirmPasswordReset(a,b).then(function(){}))};
l.checkActionCode=function(a){return this.l(this.i.checkActionCode(a).then(function(b){return new Mh(b)}))};l.applyActionCode=function(a){return this.l(this.i.applyActionCode(a).then(function(){}))};l.sendPasswordResetEmail=function(a,b){var c=this;return this.l(I().then(function(){return"undefined"===typeof b||Ta(b)?{}:xl(new wl(b))}).then(function(d){return c.i.sendPasswordResetEmail(a,d)}).then(function(){}))};
l.signInWithPhoneNumber=function(a,b){return this.l(zl(this,a,b,v(this.signInWithCredential,this)))};l.signInWithEmailLink=function(a,b){var c=this;return this.l(I().then(function(){b=b||vg();var d=Ci(a,b),e=Bi(b);if(!e)throw new P("argument-error","Invalid email link!");if(e.tenantId!==c.C)throw new P("tenant-id-mismatch");return c.signInWithCredential(d)}))};var Xn=function(a){G.call(this,"languageCodeChanged");this.languageCode=a};m(Xn,G);
var Zn=function(a){G.call(this,"emulatorConfigChanged");this.emulatorConfig=a};m(Zn,G);var $n=function(a){G.call(this,"frameworkChanged");this.Yc=a};m($n,G);var jo=function(a,b,c,d){a:{c=Array.prototype.slice.call(c);var e=0;for(var f=!1,g=0;g<b.length;g++)if(b[g].optional)f=!0;else{if(f)throw new P("internal-error","Argument validator encountered a required argument after an optional argument.");e++}f=b.length;if(c.length<e||f<c.length)d="Expected "+(e==f?1==e?"1 argument":e+" arguments":e+"-"+f+" arguments")+" but got "+c.length+".";else{for(e=0;e<c.length;e++)if(f=b[e].optional&&void 0===c[e],!b[e].X(c[e])&&!f){b=b[e];if(0>e||e>=io.length)throw new P("internal-error",
"Argument validator received an unsupported number of arguments.");c=io[e];d=(d?"":c+" argument ")+(b.name?'"'+b.name+'" ':"")+"must be "+b.W+".";break a}d=null}}if(d)throw new P("argument-error",a+" failed: "+d);},io="First Second Third Fourth Fifth Sixth Seventh Eighth Ninth".split(" "),U=function(a,b){return{name:a||"",W:"a valid string",optional:!!b,X:function(c){return"string"===typeof c}}},ko=function(a,b){return{name:a||"",W:"a boolean",optional:!!b,X:function(c){return"boolean"===typeof c}}},
V=function(a,b){return{name:a||"",W:"a valid object",optional:!!b,X:u}},lo=function(a,b){return{name:a||"",W:"a function",optional:!!b,X:Qb}},mo=function(a,b){return{name:a||"",W:"null",optional:!!b,X:function(c){return null===c}}},no=function(){return{name:"",W:"an HTML element",optional:!1,X:function(a){return!!(a&&a instanceof Element)}}},oo=function(){return{name:"auth",W:"an instance of Firebase Auth",optional:!0,X:function(a){return!!(a&&a instanceof S)}}},po=function(){return{name:"app",W:"an instance of Firebase App",
optional:!0,X:function(a){return!!(a&&a instanceof firebase.app.App)}}},qo=function(a){return{name:a?a+"Credential":"credential",W:a?"a valid "+a+" credential":"a valid credential",optional:!1,X:function(b){if(!b)return!1;var c=!a||b.providerId===a;return!(!b.Mb||!c)}}},ro=function(){return{name:"multiFactorAssertion",W:"a valid multiFactorAssertion",optional:!1,X:function(a){return a?!!a.process:!1}}},so=function(){return{name:"authProvider",W:"a valid Auth provider",optional:!1,X:function(a){return!!(a&&
a.providerId&&a.hasOwnProperty&&a.hasOwnProperty("isOAuthProvider"))}}},to=function(a,b){return u(a)&&"string"===typeof a.type&&a.type===b&&"function"===typeof a.bd},uo=function(a){return u(a)&&"string"===typeof a.uid},vo=function(){return{name:"applicationVerifier",W:"an implementation of firebase.auth.ApplicationVerifier",optional:!1,X:function(a){return!(!a||"string"!==typeof a.type||"function"!==typeof a.verify)}}},W=function(a,b,c,d){return{name:c||"",W:a.W+" or "+b.W,optional:!!d,X:function(e){return a.X(e)||
b.X(e)}}};var X=function(a,b){for(var c in b){var d=b[c].name;a[d]=wo(d,a[c],b[c].g)}},xo=function(a,b){for(var c in b){var d=b[c].name;d!==c&&Object.defineProperty(a,d,{get:xa(function(e){return this[e]},c),set:xa(function(e,f,g,h){jo(e,[g],[h],!0);this[f]=h},d,c,b[c].ef),enumerable:!0})}},Y=function(a,b,c,d){a[b]=wo(b,c,d)},wo=function(a,b,c){if(!c)return b;var d=yo(a);a=function(){var f=Array.prototype.slice.call(arguments);jo(d,c,f);return b.apply(this,f)};for(var e in b)a[e]=b[e];for(e in b.prototype)a.prototype[e]=
b.prototype[e];return a},yo=function(a){a=a.split(".");return a[a.length-1]};function zo(){}N(zo,"FACTOR_ID","phone");X(S.prototype,{applyActionCode:{name:"applyActionCode",g:[U("code")]},checkActionCode:{name:"checkActionCode",g:[U("code")]},confirmPasswordReset:{name:"confirmPasswordReset",g:[U("code"),U("newPassword")]},createUserWithEmailAndPassword:{name:"createUserWithEmailAndPassword",g:[U("email"),U("password")]},fetchSignInMethodsForEmail:{name:"fetchSignInMethodsForEmail",g:[U("email")]},getRedirectResult:{name:"getRedirectResult",g:[]},isSignInWithEmailLink:{name:"isSignInWithEmailLink",g:[U("emailLink")]},
onAuthStateChanged:{name:"onAuthStateChanged",g:[W(V(),lo(),"nextOrObserver"),lo("opt_error",!0),lo("opt_completed",!0)]},onIdTokenChanged:{name:"onIdTokenChanged",g:[W(V(),lo(),"nextOrObserver"),lo("opt_error",!0),lo("opt_completed",!0)]},sendPasswordResetEmail:{name:"sendPasswordResetEmail",g:[U("email"),W(V("opt_actionCodeSettings",!0),mo(null,!0),"opt_actionCodeSettings",!0)]},sendSignInLinkToEmail:{name:"sendSignInLinkToEmail",g:[U("email"),V("actionCodeSettings")]},setPersistence:{name:"setPersistence",
g:[U("persistence")]},signInAndRetrieveDataWithCredential:{name:"signInAndRetrieveDataWithCredential",g:[qo()]},signInAnonymously:{name:"signInAnonymously",g:[]},signInWithCredential:{name:"signInWithCredential",g:[qo()]},signInWithCustomToken:{name:"signInWithCustomToken",g:[U("token")]},signInWithEmailAndPassword:{name:"signInWithEmailAndPassword",g:[U("email"),U("password")]},signInWithEmailLink:{name:"signInWithEmailLink",g:[U("email"),U("emailLink",!0)]},signInWithPhoneNumber:{name:"signInWithPhoneNumber",
g:[U("phoneNumber"),vo()]},signInWithPopup:{name:"signInWithPopup",g:[so()]},signInWithRedirect:{name:"signInWithRedirect",g:[so()]},updateCurrentUser:{name:"updateCurrentUser",g:[W(function(a){return{name:"user",W:"an instance of Firebase User",optional:!!a,X:function(b){return!!(b&&b instanceof R)}}}(),mo(),"user")]},signOut:{name:"signOut",g:[]},toJSON:{name:"toJSON",g:[U(null,!0)]},useDeviceLanguage:{name:"useDeviceLanguage",g:[]},useEmulator:{name:"useEmulator",g:[U("url"),V("options",!0)]},
verifyPasswordResetCode:{name:"verifyPasswordResetCode",g:[U("code")]}});xo(S.prototype,{lc:{name:"languageCode",ef:W(U(),mo(),"languageCode")},ti:{name:"tenantId",ef:W(U(),mo(),"tenantId")}});S.Persistence=Kl;S.Persistence.LOCAL="local";S.Persistence.SESSION="session";S.Persistence.NONE="none";
X(R.prototype,{"delete":{name:"delete",g:[]},getIdTokenResult:{name:"getIdTokenResult",g:[ko("opt_forceRefresh",!0)]},getIdToken:{name:"getIdToken",g:[ko("opt_forceRefresh",!0)]},linkAndRetrieveDataWithCredential:{name:"linkAndRetrieveDataWithCredential",g:[qo()]},linkWithCredential:{name:"linkWithCredential",g:[qo()]},linkWithPhoneNumber:{name:"linkWithPhoneNumber",g:[U("phoneNumber"),vo()]},linkWithPopup:{name:"linkWithPopup",g:[so()]},linkWithRedirect:{name:"linkWithRedirect",g:[so()]},reauthenticateAndRetrieveDataWithCredential:{name:"reauthenticateAndRetrieveDataWithCredential",
g:[qo()]},reauthenticateWithCredential:{name:"reauthenticateWithCredential",g:[qo()]},reauthenticateWithPhoneNumber:{name:"reauthenticateWithPhoneNumber",g:[U("phoneNumber"),vo()]},reauthenticateWithPopup:{name:"reauthenticateWithPopup",g:[so()]},reauthenticateWithRedirect:{name:"reauthenticateWithRedirect",g:[so()]},reload:{name:"reload",g:[]},sendEmailVerification:{name:"sendEmailVerification",g:[W(V("opt_actionCodeSettings",!0),mo(null,!0),"opt_actionCodeSettings",!0)]},toJSON:{name:"toJSON",g:[U(null,
!0)]},unlink:{name:"unlink",g:[U("provider")]},updateEmail:{name:"updateEmail",g:[U("email")]},updatePassword:{name:"updatePassword",g:[U("password")]},updatePhoneNumber:{name:"updatePhoneNumber",g:[qo("phone")]},updateProfile:{name:"updateProfile",g:[V("profile")]},verifyBeforeUpdateEmail:{name:"verifyBeforeUpdateEmail",g:[U("email"),W(V("opt_actionCodeSettings",!0),mo(null,!0),"opt_actionCodeSettings",!0)]}});X(Ak.prototype,{execute:{name:"execute"},render:{name:"render"},reset:{name:"reset"},getResponse:{name:"getResponse"}});
X(vk.prototype,{execute:{name:"execute"},render:{name:"render"},reset:{name:"reset"},getResponse:{name:"getResponse"}});X(H.prototype,{Bb:{name:"finally"},h:{name:"catch"},then:{name:"then"}});xo(vl.prototype,{appVerificationDisabled:{name:"appVerificationDisabledForTesting",ef:ko("appVerificationDisabledForTesting")}});X(yl.prototype,{confirm:{name:"confirm",g:[U("verificationCode")]}});
Y(ai,"fromJSON",function(a){a="string"===typeof a?JSON.parse(a):a;for(var b,c=[ii,Ai,Hi,gi],d=0;d<c.length;d++)if(b=c[d](a))return b;return null},[W(U(),V(),"json")]);Y(ui,"credential",function(a,b){return new vi(a,b)},[U("email"),U("password")]);X(vi.prototype,{m:{name:"toJSON",g:[U(null,!0)]}});X(mi.prototype,{addScope:{name:"addScope",g:[U("scope")]},setCustomParameters:{name:"setCustomParameters",g:[V("customOAuthParameters")]}});Y(mi,"credential",ni,[W(U(),V(),"token")]);
Y(ui,"credentialWithLink",Ci,[U("email"),U("emailLink")]);X(oi.prototype,{addScope:{name:"addScope",g:[U("scope")]},setCustomParameters:{name:"setCustomParameters",g:[V("customOAuthParameters")]}});Y(oi,"credential",pi,[W(U(),V(),"token")]);X(qi.prototype,{addScope:{name:"addScope",g:[U("scope")]},setCustomParameters:{name:"setCustomParameters",g:[V("customOAuthParameters")]}});Y(qi,"credential",ri,[W(U(),W(V(),mo()),"idToken"),W(U(),mo(),"accessToken",!0)]);
X(si.prototype,{setCustomParameters:{name:"setCustomParameters",g:[V("customOAuthParameters")]}});Y(si,"credential",ti,[W(U(),V(),"token"),U("secret",!0)]);X(li.prototype,{addScope:{name:"addScope",g:[U("scope")]},credential:{name:"credential",g:[W(U(),W(V(),mo()),"optionsOrIdToken"),W(U(),mo(),"accessToken",!0)]},setCustomParameters:{name:"setCustomParameters",g:[V("customOAuthParameters")]}});X(hi.prototype,{m:{name:"toJSON",g:[U(null,!0)]}});X(ci.prototype,{m:{name:"toJSON",g:[U(null,!0)]}});
Y(Ii,"credential",Mi,[U("verificationId"),U("verificationCode")]);
X(Ii.prototype,{verifyPhoneNumber:{name:"verifyPhoneNumber",g:[W(U(),function(a,b){return{name:a||"phoneInfoOptions",W:"valid phone info options",optional:!!b,X:function(c){return c?c.session&&c.phoneNumber?to(c.session,"enroll")&&"string"===typeof c.phoneNumber:c.session&&c.multiFactorHint?to(c.session,"signin")&&uo(c.multiFactorHint):c.session&&c.multiFactorUid?to(c.session,"signin")&&"string"===typeof c.multiFactorUid:c.phoneNumber?"string"===typeof c.phoneNumber:!1:!1}}}(),"phoneInfoOptions"),
vo()]}});X(Di.prototype,{m:{name:"toJSON",g:[U(null,!0)]}});X(P.prototype,{toJSON:{name:"toJSON",g:[U(null,!0)]}});X(Pi.prototype,{toJSON:{name:"toJSON",g:[U(null,!0)]}});X(Vh.prototype,{toJSON:{name:"toJSON",g:[U(null,!0)]}});X(Hm.prototype,{toJSON:{name:"toJSON",g:[U(null,!0)]}});X(Gm.prototype,{resolveSignIn:{name:"resolveSignIn",g:[ro()]}});
X(Qm.prototype,{getSession:{name:"getSession",g:[]},enroll:{name:"enroll",g:[ro(),U("displayName",!0)]},unenroll:{name:"unenroll",g:[W({name:"multiFactorInfo",W:"a valid multiFactorInfo",optional:!1,X:uo},U(),"multiFactorInfoIdentifier")]}});X(Lk.prototype,{clear:{name:"clear",g:[]},render:{name:"render",g:[]},verify:{name:"verify",g:[]}});Y(Oh,"parseLink",Ph,[U("link")]);Y(zo,"assertion",function(a){return new Nm(a)},[qo("phone")]);
(function(){if("undefined"!==typeof firebase&&firebase.INTERNAL&&firebase.INTERNAL.registerService){var a={ActionCodeInfo:{Operation:{EMAIL_SIGNIN:"EMAIL_SIGNIN",PASSWORD_RESET:"PASSWORD_RESET",RECOVER_EMAIL:"RECOVER_EMAIL",REVERT_SECOND_FACTOR_ADDITION:"REVERT_SECOND_FACTOR_ADDITION",VERIFY_AND_CHANGE_EMAIL:"VERIFY_AND_CHANGE_EMAIL",VERIFY_EMAIL:"VERIFY_EMAIL"}},Auth:S,AuthCredential:ai,Error:P};Y(a,"EmailAuthProvider",ui,[]);Y(a,"FacebookAuthProvider",mi,[]);Y(a,"GithubAuthProvider",oi,[]);Y(a,
"GoogleAuthProvider",qi,[]);Y(a,"TwitterAuthProvider",si,[]);Y(a,"OAuthProvider",li,[U("providerId")]);Y(a,"SAMLAuthProvider",ki,[U("providerId")]);Y(a,"PhoneAuthProvider",Ii,[oo()]);Y(a,"RecaptchaVerifier",Lk,[W(U(),no(),"recaptchaContainer"),V("recaptchaParameters",!0),po()]);Y(a,"ActionCodeURL",Oh,[]);Y(a,"PhoneMultiFactorGenerator",zo,[]);firebase.INTERNAL.registerService("auth",function(b,c){b=new S(b);c({INTERNAL:{getUid:v(b.getUid,b),getToken:v(b.cj,b),addAuthTokenListener:v(b.Li,b),removeAuthTokenListener:v(b.Rj,
b)}});return b},a,function(b,c){if("create"===b)try{c.auth()}catch(d){}});firebase.INTERNAL.extendNamespace({User:R})}else throw Error("Cannot find the firebase namespace; be sure to include firebase-app.js before this library.");})();var Ao=function(){this.Ra=("undefined"==typeof document?null:document)||{cookie:""}};l=Ao.prototype;l.isEnabled=function(){if(!q.navigator.cookieEnabled)return!1;if(!this.Ah())return!0;this.set("TESTCOOKIESENABLED","1",{Tf:60});if("1"!==this.get("TESTCOOKIESENABLED"))return!1;this.remove("TESTCOOKIESENABLED");return!0};
l.set=function(a,b,c){var d=!1;if("object"===typeof c){var e=c.Rk;d=c.dk||!1;var f=c.domain||void 0;var g=c.path||void 0;var h=c.Tf}if(/[;=\s]/.test(a))throw Error('Invalid cookie name "'+a+'"');if(/[;\r\n]/.test(b))throw Error('Invalid cookie value "'+b+'"');void 0===h&&(h=-1);this.qg(a+"="+b+(f?";domain="+f:"")+(g?";path="+g:"")+(0>h?"":0==h?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+1E3*h)).toUTCString())+(d?";secure":"")+(null!=e?";samesite="+e:""))};
l.get=function(a,b){for(var c=a+"=",d=(this.ad()||"").split(";"),e=0,f;e<d.length;e++){f=kb(d[e]);if(0==f.lastIndexOf(c,0))return f.slice(c.length);if(f==a)return""}return b};l.remove=function(a,b,c){var d=this.Pc(a);this.set(a,"",{Tf:0,path:b,domain:c});return d};l.ce=function(){return Bo(this).keys};l.qb=function(){return Bo(this).values};l.Ah=function(){return!this.ad()};l.Pc=function(a){return void 0!==this.get(a)};l.clear=function(){for(var a=Bo(this).keys,b=a.length-1;0<=b;b--)this.remove(a[b])};
l.qg=function(a){this.Ra.cookie=a};l.ad=function(){return this.Ra.cookie};var Bo=function(a){a=(a.ad()||"").split(";");for(var b=[],c=[],d,e,f=0;f<a.length;f++)e=kb(a[f]),d=e.indexOf("="),-1==d?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));return{keys:b,values:c}},Co=new Ao;var Do=/\.(firebaseapp\-staging\.com|staging\-web\.app)$/;var Eo=function(){var a=q.EXPERIMENTS||{};this.N={};var b;for(b in a){var c=""+a[b].id;this.N[c]=a[b];"undefined"!==typeof this.N[c].stagingRollout&&(0>this.N[c].stagingRollout&&(this.N[c].stagingRollout=0),1<this.N[c].stagingRollout&&(this.N[c].stagingRollout=1));"undefined"!==typeof this.N[c].rollout&&(0>this.N[c].rollout&&(this.N[c].rollout=0),1<this.N[c].rollout&&(this.N[c].rollout=1))}};
Eo.prototype.isEnabled=function(a){var b=a.id;a=a.id.toString();if("undefined"!==typeof this.N[a]){var c=void 0===c?q.window:c;if(!(c&&c.navigator&&c.navigator.cookieEnabled)||this.N[a].expiration&&this.N[a].expiration.getTime()<=Date.now())return!!this.N[a].defaultValue;var d;c=this.ad("e_gcip_"+b);null===c&&(c=parseInt(1E4*Math.random(),10)/1E4,this.N[b.toString()].expiration&&(d=parseInt((this.N[b.toString()].expiration.getTime()-Date.now())/1E3,10)),this.qg("e_gcip_"+b,c.toString(),d));b=parseFloat(c);
var e;return(e=void 0===e?q.window:e)&&e.location&&e.location.hostname&&Do.test(e.location.hostname)&&"undefined"!==typeof this.N[a].stagingRollout?0===b?!1:b<=this.N[a].stagingRollout:"undefined"!==typeof this.N[a].rollout?0===b?!1:b<=this.N[a].rollout:!!this.N[a].defaultValue}return!1};Eo.prototype.qg=function(a,b,c){Co.set(a,b,{Tf:c?c:2592E3,path:"/__/auth/",domain:q.window.location.hostname,dk:!0})};Eo.prototype.ad=function(a){return Co.get(a)||null};var Fo=function(){this.Af=new Eo};
Fo.prototype.xj=function(){var a=(q.EXPERIMENTS||{}).DISPLAY_CONTINUE_BUTTON_IF_NOT_REDIRECT;return"undefined"===typeof a?!1:this.Af.isEnabled(a)};Fo.prototype.zj=function(){var a=(q.EXPERIMENTS||{}).POPUP_POST_MESSAGE_TO_IFRAME;return"undefined"===typeof a?!1:this.Af.isEnabled(a)};Fo.prototype.wj=function(){var a=(q.EXPERIMENTS||{}).CHECK_OAUTH_STATE_STORED_BEFORE_REDIRECT;return"undefined"===typeof a?!1:this.Af.isEnabled(a)};var Go=new Fo,Ho=Go.xj.bind(Go),Io=Go.zj.bind(Go),Jo=Go.wj.bind(Go);var Ko=function(a){return"string"==typeof a.className?a.className:a.getAttribute&&a.getAttribute("class")||""},Lo=function(a,b){if(a.classList)a.classList.add(b);else if(a.classList?!a.classList.contains(b):!Ma(a.classList?a.classList:Ko(a).match(/\S+/g)||[],b)){var c=Ko(a);b=c+(0<c.length?" "+b:b);"string"==typeof a.className?a.className=b:a.setAttribute&&a.setAttribute("class",b)}};function Mo(a,b){a=q[a];return a&&a.prototype?(b=Object.getOwnPropertyDescriptor(a.prototype,b))&&b.get||null:null}Mo("Element","attributes")||Mo("Node","attributes");Mo("Element","innerHTML")||Mo("HTMLElement","innerHTML");Mo("Node","nodeName");Mo("Node","nodeType");Mo("Node","parentNode");Mo("Node","childNodes");Mo("HTMLElement","style")||Mo("Element","style");Mo("HTMLStyleElement","sheet");Mo("Element","namespaceURI")||Mo("Node","namespaceURI");Te(Ue(),"goog.html.sanitizer.SafeDomTreeProcessor");var No=function(a,b){b instanceof Eb&&b.constructor===Eb?b=b.fg:(Da("expected object of type SafeStyleSheet, got '"+b+"' of type "+ta(b)),b="type_error:SafeStyleSheet");Fc&&void 0!==a.cssText?a.cssText=b:q.trustedTypes?sd(a,b):a.innerHTML=b};/*

 Copyright 2015 Google Inc. All Rights Reserved.

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
*/
var Z={xi:function(){},yi:function(){},zi:function(){},Dg:function(){},ai:function(){},register:function(){},eh:function(){}};
Z=function(){function a(p,r){for(var t=0;t<k.length;t++)if(k[t].className===p)return"undefined"!==typeof r&&(k[t]=r),k[t];return!1}function b(p){p=p.getAttribute("data-upgraded");return null===p?[""]:p.split(",")}function c(p,r){return-1!==b(p).indexOf(r)}function d(p,r,t){if("CustomEvent"in window&&"function"===typeof window.CustomEvent)return new CustomEvent(p,{bubbles:r,cancelable:t});var z=document.createEvent("Events");z.initEvent(p,r,t);return z}function e(p,r){if("undefined"===typeof p&&"undefined"===
typeof r)for(p=0;p<k.length;p++)e(k[p].className,k[p].ob);else{if("undefined"===typeof r){var t=a(p);t&&(r=t.ob)}r=document.querySelectorAll("."+r);for(t=0;t<r.length;t++)f(r[t],p)}}function f(p,r){if(!("object"===typeof p&&p instanceof Element))throw Error("Invalid argument provided to upgrade MDL element.");var t=d("mdl-componentupgrading",!0,!0);p.dispatchEvent(t);if(!t.defaultPrevented){t=b(p);var z=[];if(r)c(p,r)||z.push(a(r));else{var aa=p.classList;k.forEach(function(oe){aa.contains(oe.ob)&&
-1===z.indexOf(oe)&&!c(p,oe.className)&&z.push(oe)})}r=0;for(var mb=z.length,qa;r<mb;r++){if(qa=z[r]){t.push(qa.className);p.setAttribute("data-upgraded",t.join(","));var kh=new qa.Pi(p);kh.mdlComponentConfigInternal_=qa;n.push(kh);for(var lh=0,mp=qa.lf.length;lh<mp;lh++)qa.lf[lh](p);qa.Xe&&(p[qa.className]=kh)}else throw Error("Unable to find a registered component for the given class.");qa=d("mdl-componentupgraded",!0,!1);p.dispatchEvent(qa)}}}function g(p){Array.isArray(p)||(p=p instanceof Element?
[p]:Array.prototype.slice.call(p));for(var r=0,t=p.length,z;r<t;r++)z=p[r],z instanceof HTMLElement&&(f(z),0<z.children.length&&g(z.children))}function h(p){if(p){n.splice(n.indexOf(p),1);var r=p.S.getAttribute("data-upgraded").split(",");r.splice(r.indexOf(p.mdlComponentConfigInternal_.pf),1);p.S.setAttribute("data-upgraded",r.join(","));r=d("mdl-componentdowngraded",!0,!1);p.S.dispatchEvent(r)}}var k=[],n=[];return{xi:e,yi:f,zi:g,Dg:function(){for(var p=0;p<k.length;p++)e(k[p].className)},ai:function(p,
r){(p=a(p))&&p.lf.push(r)},register:function(p){var r=!0;if("undefined"!==typeof p.Xe||"undefined"!==typeof p.widget)r=p.Xe||p.widget;var t={Pi:p.constructor||p.constructor,className:p.pf||p.classAsString,ob:p.ob||p.cssClass,Xe:r,lf:[]};k.forEach(function(z){if(z.ob===t.ob)throw Error("The provided cssClass has already been registered: "+z.ob);if(z.className===t.className)throw Error("The provided className has already been registered");});if(p.constructor.prototype.hasOwnProperty("mdlComponentConfigInternal_"))throw Error("MDL component classes must not have mdlComponentConfigInternal_ defined as a property.");
a(p.pf,t)||k.push(t)},eh:function(p){var r=function(z){n.filter(function(aa){return aa.S===z}).forEach(h)};if(p instanceof Array||p instanceof NodeList)for(var t=0;t<p.length;t++)r(p[t]);else if(p instanceof Node)r(p);else throw Error("Invalid argument provided to downgrade MDL nodes.");}}}();Z.upgradeDom=Z.xi;Z.upgradeElement=Z.yi;Z.upgradeElements=Z.zi;Z.upgradeAllRegistered=Z.Dg;Z.registerUpgradedCallback=Z.ai;Z.register=Z.register;Z.downgradeElements=Z.eh;window.componentHandler=Z;
window.addEventListener("load",function(){"classList"in document.createElement("div")&&"querySelector"in document&&"addEventListener"in window&&Array.prototype.forEach&&(document.documentElement.classList.add("mdl-js"),Z.Dg())});(function(){var a=function(b){this.S=b;this.init()};window.MaterialProgress=a;a.prototype.Hi={Ii:"mdl-progress__indeterminate"};a.prototype.ik=function(b){this.S.classList.contains(this.Hi.Ii)||(this.Xh.style.width=b+"%")};a.prototype.setProgress=a.prototype.ik;a.prototype.hk=function(b){this.Lg.style.width=b+"%";this.Ig.style.width=100-b+"%"};a.prototype.setBuffer=a.prototype.hk;a.prototype.init=function(){if(this.S){var b=document.createElement("div");b.className="progressbar bar bar1";this.S.appendChild(b);
this.Xh=b;b=document.createElement("div");b.className="bufferbar bar bar2";this.S.appendChild(b);this.Lg=b;b=document.createElement("div");b.className="auxbar bar bar3";this.S.appendChild(b);this.Ig=b;this.Xh.style.width="0%";this.Lg.style.width="100%";this.Ig.style.width="0%";this.S.classList.add("is-upgraded")}};Z.register({constructor:a,pf:"MaterialProgress",ob:"mdl-js-progress",Xe:!0})})();var Oo={"244437093285883777":{ar:"\u062c\u0627\u0631\u064d \u0627\u0644\u062a\u0623\u0643\u0651\u062f \u0645\u0646 \u0623\u0646\u0643 \u0644\u0633\u062a \u0628\u0631\u0646\u0627\u0645\u062c \u0631\u0648\u0628\u0648\u062a...",ar_xb:"\u200f\u202eVerifying\u202c\u200f \u200f\u202eyou\u202c\u200f'\u200f\u202ere\u202c\u200f \u200f\u202enot\u202c\u200f \u200f\u202ea\u202c\u200f \u200f\u202erobot\u202c\u200f...",bg:"\u041f\u043e\u0442\u0432\u044a\u0440\u0436\u0434\u0430\u0432\u0430 \u0441\u0435, \u0447\u0435 \u043d\u0435 \u0441\u0442\u0435 \u0440\u043e\u0431\u043e\u0442\u2026",
ca:"S'est\u00e0 comprovant que no siguis un robot...",cs:"Ov\u011b\u0159ujeme, zda nejste robot\u2026",da:"Bekr\u00e6fter, at du ikke er en robot\u2026",de:"Best\u00e4tigen Sie bitte, dass Sie kein Roboter sind.",el:"\u0395\u03c0\u03b1\u03bb\u03ae\u03b8\u03b5\u03c5\u03c3\u03b7 \u03cc\u03c4\u03b9 \u03b4\u03b5\u03bd \u03b5\u03af\u03c3\u03c4\u03b5 \u03c1\u03bf\u03bc\u03c0\u03cc\u03c4\u2026",en:"Verifying you're not a robot...",en_gb:"Verifying you're not a robot...",en_xa:"[V\u00e9\u0155\u00ee\u0192\u00fd\u00ee\u00f1\u011d \u00fd\u00f6\u00fb'\u0155\u00e9 \u00f1\u00f6\u0163 \u00e5 \u0155\u00f6\u0431\u00f6\u0163... one two three four five six seven]",
es:"Estamos comprobando que no eres un robot...",es_419:"Estamos verificando que no eres un robot\u2026",fa:"\u062f\u0631\u062d\u0627\u0644 \u062a\u0623\u06cc\u06cc\u062f \u0627\u06cc\u0646\u06a9\u0647 \u0634\u0645\u0627 \u0631\u0628\u0627\u062a \u0646\u06cc\u0633\u062a\u06cc\u062f\u2026",fi:"Tarkistamme, ettet ole robotti\u2026",fil:"Vine-verify na hindi ka robot...",fr:"Nous v\u00e9rifions que vous n'\u00eates pas un robot\u2026",hi:"\u0906\u092a \u0930\u094b\u092c\u094b\u091f \u0928\u0939\u0940\u0902 \u0939\u0948\u0902 \u0907\u0938\u0915\u0940 \u092a\u0941\u0937\u094d\u091f\u093f \u0915\u0940 \u091c\u093e \u0930\u0939\u0940 \u0939\u0948...",
hr:"Potvr\u0111ujemo da niste robot...",hu:"Annak ellen\u0151rz\u00e9se, hogy \u00d6n nem robot...",id:"Memverifikasi bahwa Anda bukan robot...",it:"Stiamo verificando che non sei un robot\u2026",iw:"\u05de\u05d5\u05d5\u05d3\u05d0 \u05e9\u05d0\u05d9\u05e0\u05da \u05e8\u05d5\u05d1\u05d5\u05d8...",ja:"\u30ed\u30dc\u30c3\u30c8\u306b\u3088\u308b\u64cd\u4f5c\u3067\u306a\u3044\u3053\u3068\u3092\u78ba\u8a8d\u3057\u3066\u3044\u307e\u3059...",ko:"\ub85c\ubd07\uc774 \uc544\ub2cc \uc2e4\uc81c \uc0ac\uc6a9\uc790\uc784\uc744 \ud655\uc778 \uc911...",
lt:"Tikrinama, ar nesate robotas...",lv:"Notiek verifik\u0101cija, lai p\u0101rliecin\u0101tos, ka neesat robots...",nl:"Verifi\u00ebren of u geen robot bent...",no:"Bekrefter at du ikke er en robot ...",pl:"Potwierd\u017a, \u017ce nie jeste\u015b robotem.",pt:"Verificando se voc\u00ea n\u00e3o \u00e9 um rob\u00f4...",pt_pt:"A verificar se \u00e9 um rob\u00f4...",ro:"Se verific\u0103 dac\u0103 sunte\u021bi un robot...",ru:"\u041d\u0430\u043c \u043d\u0443\u0436\u043d\u043e \u0443\u0431\u0435\u0434\u0438\u0442\u044c\u0441\u044f, \u0447\u0442\u043e \u0432\u044b \u043d\u0435 \u0440\u043e\u0431\u043e\u0442...",
sk:"Overuje sa, \u017ee nie ste robot...",sl:"Preverjamo, da niste robot ...",sr:"\u041f\u043e\u0442\u0432\u0440\u0452\u0443\u0458\u0435\u043c\u043e \u0434\u0430 \u043d\u0438\u0441\u0442\u0435 \u0440\u043e\u0431\u043e\u0442\u2026",sv:"Verifierar att du inte \u00e4r en robot \u2026",th:"\u0e01\u0e33\u0e25\u0e31\u0e07\u0e22\u0e37\u0e19\u0e22\u0e31\u0e19\u0e27\u0e48\u0e32\u0e04\u0e38\u0e13\u0e44\u0e21\u0e48\u0e43\u0e0a\u0e48\u0e2b\u0e38\u0e48\u0e19\u0e22\u0e19\u0e15\u0e4c...",tr:"Robot olmad\u0131\u011f\u0131n\u0131z do\u011frulan\u0131yor...",
uk:"\u041f\u0435\u0440\u0435\u0432\u0456\u0440\u044f\u0454\u043c\u043e, \u0447\u0438 \u0432\u0438 \u043d\u0435 \u0440\u043e\u0431\u043e\u0442\u2026",vi:"\u0110ang x\u00e1c minh b\u1ea1n kh\u00f4ng ph\u1ea3i l\u00e0 r\u00f4 b\u1ed1t...",zh_cn:"\u6b63\u5728\u9a8c\u8bc1\u60a8\u662f\u5426\u4e3a\u673a\u5668\u4eba\u2026",zh_tw:"\u6b63\u5728\u9a57\u8b49\u60a8\u662f\u5426\u70ba\u81ea\u52d5\u7a0b\u5f0f..."}},Po=null,Qo="ar ar_xb iw fa ps sd so tk ug ur he yi syc ks ku".split(" "),Ro=function(a,b){if(!Po){Po=
{};for(var c in Oo)Po[Oo[c].en]=c}b=b.replace("-","_").toLowerCase();return"undefined"!==typeof Po[a]&&(c=Po[a],"undefined"!==typeof Oo[c][b])?Oo[c][b]:a};var So=function(a,b){this.qk=a;this.pk=b;this.S=null};
So.prototype.render=function(a){var b=(0,this.qk)(this.pk||Ad,void 0);var c=(Aa||(Aa=new xd)).createElement("DIV");if(u(b))if(b instanceof xc){if(b.Ug!==wc)throw Error("Sanitized content was not of kind HTML.");b=Nb(cb("Soy SanitizedContent of kind HTML produces SafeHtml-contract-compliant value."),b.toString())}else Da("Soy template output is unsafe for use as HTML: "+b),b=Lb("zSoyz");else b=Lb(String(b));var d=b.bb(),e=d.match(zd);x(!e,"This template starts with a %s, which cannot be a child of a <div>, as required by soy internals. Consider using goog.soy.renderElement instead.\nTemplate output: %s",e&&
e[0],d);if(Rb())for(;c.lastChild;)c.removeChild(c.lastChild);c.innerHTML=Jb(b);1==c.childNodes.length&&(b=c.firstChild,1==b.nodeType&&(c=b));this.S=c;nd(a,this.S);this.Kd()};So.prototype.Kd=function(){};So.prototype.Pd=function(){};So.prototype.Hb=function(){this.S&&(this.Pd(),pd(this.S),this.S=null)};var To=function(){So.call(this,Bd)};m(To,So);To.prototype.Kd=function(){var a=gd("progressBar");window.componentHandler&&window.componentHandler.upgradeElement&&window.componentHandler.upgradeElement(a)};
To.prototype.Pd=function(){var a=gd("progressBar");window.componentHandler&&window.componentHandler.downgradeElements&&window.componentHandler.downgradeElements(a)};var Uo=function(a,b){So.call(this,Cd,{appName:a});this.xe=b;this.ab=null};m(Uo,So);Uo.prototype.Kd=function(){var a=gd("continue"),b=this;this.ab=ie(a,"click",function(){b.xe()})};Uo.prototype.Pd=function(){this.ab&&(se(this.ab),this.ab=null)};var Vo=function(a){So.call(this,Dd,{errorMessage:a})};m(Vo,So);
var Wo=function(a,b){So.call(this,Ed,{});this.xe=a||null;this.pc=b||null;this.ab=null};m(Wo,So);Wo.prototype.Kd=function(){var a=gd("verify"),b=this;this.xe&&(this.ab=ie(a,"click",function(){b.xe()}));a=gd("app-verification-progress-bar");window.componentHandler&&window.componentHandler.upgradeElement&&window.componentHandler.upgradeElement(a);if((a=gd("status-container-label"))&&this.pc){var c=Ro(wd(a),this.pc);sd(a,c);Ma(Qo,this.pc.replace("-","_").toLowerCase())&&Lo(a,"firebase-rtl")}};
var Xo=function(){var a=gd("status-container");Lo(a,"firebase-hidden")};Wo.prototype.Pd=function(){this.ab&&(se(this.ab),this.ab=null);var a=gd("app-verification-progress-bar");window.componentHandler&&window.componentHandler.downgradeElements&&window.componentHandler.downgradeElements(a)};var Yo=function(a){this.kg=kd("DIV",{id:"progressBarContainer"});var b=this;this.Ab=new Xl;this.uri=E(a);this.apiKey=D(this.uri,"apiKey")||null;this.appName=D(this.uri,"appName")||"";this.authType=D(this.uri,"authType")||null;this.Jb=D(this.uri,"eventId")||null;this.Ca=D(this.uri,"redirectUrl")||null;this.ze=D(this.uri,"v")||null;this.ck=(a=D(this.uri,"scopes"))?a.split(","):[];this.Wd={};this.sessionId=D(this.uri,"sessionId")||null;this.cf=D(this.uri,"appDisplayName")||null;this.Pa=D(this.uri,"apn")||
null;this.sb=D(this.uri,"ibi")||null;this.Xd=D(this.uri,"eid")||null;this.clientId=D(this.uri,"clientId")||null;this.pb=D(this.uri,"appId")||null;this.uh=D(this.uri,"hl")||null;this.Wb=D(this.uri,"sha1Cert")||null;this.publicKey=D(this.uri,"publicKey")||null;this.Qe=D(this.uri,"tid")||null;a=ch(D(this.uri,"customParameters")||"{}");this.Wd="object"==typeof a?a||{}:{};this.providerId=D(this.uri,"providerId")||null;this.De="string"===typeof q.POST_BODY&&"{{POST_BODY}}"!=q.POST_BODY&&0!=q.POST_BODY.length?
q.POST_BODY:null;this.providerId&&(this.Wd=bh(this.Wd,Ch(this.providerId)));this.Yc=(a=D(this.uri,"fw"))?a.split(","):[];this.mode="oauth";this.Tb=null;this.Sh=this.Vf=Jg().then(function(){document.body.appendChild(b.kg);b.Tb=new To;b.Tb.render(b.kg)});this.ib=null},Zo={},$o={gf:["facebook.com"]},ap={},bp={gf:["facebook.com","apple.com"]},cp={},dp=function(a){if(a.Ca)try{return hc(ic(gc(E(a.Ca),""),""),"").toString()}catch(b){}else{if(a.Pa)return"file://asset/www/index.html?apn="+encodeURIComponent(a.Pa);
if(a.sb)return"file://asset/www/index.html?ibi="+encodeURIComponent(a.sb)}return null},ep=function(a,b){return D(a,b)},fp=function(a){if(!a.apiKey)throw ih("No API key available"),new P("bad-request");a.ib=new Wi(a.apiKey,sg(a.Xd),Ug("Handler","2.9.2",a.Yc));a.ib.C=a.Qe},hp=function(a){gp(a);a.Vf.then(function(){a.Tb=new To;a.Tb.render(a.kg)})},gp=function(a){a.Sh&&a.Sh.cancel();a.Tb&&(a.Tb.Hb(),a.Tb=null)};
Yo.prototype.oc=function(){return this.apiKey?this.apiKey+":"+(this.appName||""):null};Yo.prototype.handleError=function(a){var b=this;if("auth/bad-request"===a.code)return Jg().then(function(){gp(b);(new Vo(a.message)).render(document.body)});var c=new Gh(this.authType||"unknown",this.Jb,null,null,a);return ip(this,c)};
var jp=function(a,b,c,d){b=new Gh(a.authType,a.Jb,b,c,null,d,a.Qe);return ip(a,b)},ip=function(a,b){var c=a.oc(),d=!1,e=I();Eg()&&!a.Ca&&(e=fg(500));return e.then(function(){if((a.Pa||a.sb)&&a.ib)return d=!0,kp(a.ib,b,a.Pa,a.sb,a.clientId,a.Wb,a.cf,a.Jb,a.pb,a.publicKey);if("verifyApp"===a.authType&&b.Y)return d=!0,lp(b.getError());if(c){var f=a.Ab;return b.getType().match(/Redirect$/)?f.o.set(Wl,b.m(),c):np(f,c,b)}}).then(function(){return a.Ab.o.remove(Zl)}).then(function(){if(c)return a.Ab.o.remove(Yl,
c)}).Bb(function(){d?gp(a):/redirect/i.test(a.authType||"")?(gp(a),nh(),xg(a.Ca)):a.ze&&Zg()&&!Og()?a.apiKey?setTimeout(function(){gp(a);Cg()},15E3):(gp(a),Cg()):(gp(a),Cg())})},kp=function(a,b,c,d,e,f,g,h,k,n){return I().then(function(){if("verifyApp"===b.getType()&&d){if(!e&&!k)throw new P("invalid-app-id");op(null,b,c,d,e,f,g,h,k,n)}else return"verifyApp"===b.getType()&&c?op(null,b,c,d,e,f,g,h,k,n):c?rj(a,c,f).then(function(){var p=null;p=f?I("playservices.app.goo.gl"):Q(a,bk,{returnDynamicLink:!0});
return p.then(function(r){op(r,b,c,d,e,f,g,h,k,n)})}):(e?sj(a,e):k?tj(a,k):qj(a,d)).then(function(){op(null,b,c,d,e,f,g,h,k,n)})}).h(function(p){return lp(p)})},lp=function(a){var b=!a||"auth/dynamic-link-not-activated"!=a.code&&"auth/invalid-app-id"!=a.code&&"auth/invalid-oauth-client-id"!=a.code&&"auth/invalid-api-key"!=a.code&&"auth/internal-error"!=a.code&&"auth/invalid-cert-hash"!=a.code?(Zc["fireauth.oauthhelper.ui.soy.temporaryErrorMessage"]?Zc["fireauth.oauthhelper.ui.soy.temporaryErrorMessage"](void 0,
void 0):"An error occurred. Please try again later.").toString():a.message;return Jg().then(function(){(new Vo(b)).render(document.body)})},op=function(a,b,c,d,e,f,g,h,k,n){n=n?"android_non_gmscore":c?"android":"ios";var p=c?c:d,r=null;f?(r=pp(b,c,h,n),p="com.google.android.gms"):(e||k)&&d&&"verifyApp"!==b.getType()&&!b.Y?(r=window.location.protocol+"//"+window.location.host+"/__/auth/callback?authType="+encodeURIComponent(b.getType())+"&link="+encodeURIComponent(b.Xa),h&&(r+="&eventId="+h)):b.Y?
(r=window.location.protocol+"//"+window.location.host+"/__/auth/callback?firebaseError="+encodeURIComponent($g(b.getError().m()))+"&authType="+encodeURIComponent(b.getType()),h&&(r+="&eventId="+h)):r=b.Xa.replace("/handler","/callback");a=new Qh(a,n,p,window.location.host,r,e,k);"android"==n?(Rh(a,g),xg(a.toString(!f))):xg(a.toString(),void 0,!0);setTimeout(function(){wg(null)},4E3)},pp=function(a,b,c,d){if("android_non_gmscore"===d){d=a.Xa;var e=window.location.protocol+"//"+window.location.host+
"/__/auth/handler";b="intent://firebase.auth/#Intent;scheme="+("verifyApp"===a.getType()?"recaptcha":"genericidp")+";package="+b+";S.authType="+a.getType()+";";c&&(b+="S.eventId="+c+";");a.Y?b+="S.firebaseError="+encodeURIComponent($g(a.getError().m()))+";":(b+="S.link="+encodeURIComponent(d)+";",b+="B.encryptionEnabled=false;");b+="S.browser_fallback_url="+encodeURIComponent(e)+";";a=b+"end;"}else d=rc("https","fir-auth-gms.firebaseapp.com",null,"/",null,null),C(d,"authType",a.getType()),C(d,"cpn",
b),c&&C(d,"eventId",c),a.Y?C(d,"firebaseError",$g(a.getError().m())):C(d,"link",a.Xa),a=d.toString();return a},np=function(a,b,c){var d=q.window&&q.window.opener||null;if(Io()&&d&&/popup/i.test(c.getType()||"")){for(var e=[],f=b.split(":"),g=0;g<d.frames.length;g++){var h=d.frames[g];try{var k=E(h.location.href);f[0]===D(k,"apiKey")&&f[1]===D(k,"appName")&&e.push(new Ok(new Mk(h,h.location.origin)))}catch(p){}}if(0===e.length)return am(a,b,c);var n=[];e.forEach(function(p){var r=!1;n.push(Pk(p,"sendAuthEvent",
{storageKey:b,authEvent:c.m()},2E4).then(function(t){for(var z=0;z<t.length;z++)t[z].fulfilled&&t[z].value&&(r=t[z].value);if(!r)throw Error("Unable to send the auth event");}))});return Bf(n).h(function(){return am(a,b,c)}).then(function(){e.forEach(function(p){p.close()})})}return am(a,b,c)};var qp=function(a){this.Sg=!1;this.Oc=kd("DIV");if("verifyApp"!==ep(E(a),"authType"))throw Error("Invalid mode!");this.Ta={size:"invisible",type:"image",tabindex:0,theme:"light"};Yo.call(this,a);this.Md=new Wo(null,this.uh);this.nf=this.Db=null;this.mode="verifyApp"};w(qp,Yo);qp.prototype.start=function(){var a=this;return this.cb().then(function(){return rp(a)}).h(function(b){a.Db&&a.Db.clear();hp(a);a.Md&&a.Md.Hb();return b instanceof P?a.handleError(b):a.handleError(new P("internal-error"))})};
qp.prototype.cb=function(){var a=this;return I().then(function(){if(!(a.apiKey&&a.sb&&(a.clientId||a.pb)||a.apiKey&&a.Pa&&a.Wb&&a.publicKey)){if(!a.apiKey)throw new P("invalid-api-key");if(!a.clientId&&!a.pb)throw new P("invalid-app-id");throw new P("internal-error");}}).then(function(){fp(a);if(a.clientId)return sj(a.ib,a.clientId);if(a.pb)return tj(a.ib,a.pb);if(a.Pa&&a.Wb)return rj(a.ib,a.Pa,a.Wb);throw new P("internal-error");})};
var rp=function(a){var b,c;return a.Vf.then(function(){c=function(){var d=qd();d&&d.style.width&&(d.style.width="")};a.Sg||(a.Sg=!0,document.body.appendChild(a.Oc));gp(a);a.Md.render(a.Oc);b=gd("verify");a.Db=new Ik(a.apiKey,b,a.Ta,function(){return a.uh},a.ze,sg(a.Xd));return a.Db.render()}).then(function(){b.click();return fg(2500)}).then(function(){c();Xo();a.nf=setInterval(function(){qd().style.visibility&&"hidden"===qd().style.visibility&&b.click()},1E3);q.window.addEventListener("orientationchange",
c);q.window.addEventListener("resize",c);return a.Db.verify()}).then(function(d){hp(a);clearInterval(a.nf);q.window.removeEventListener("orientationchange",c);q.window.removeEventListener("resize",c);a.Db.clear();a.Db=null;d=window.location.protocol+"//"+window.location.host+"/__/auth/callback?authType="+encodeURIComponent(a.mode)+"&recaptchaToken="+encodeURIComponent(d);a.Jb&&(d+="&eventId="+encodeURIComponent(a.Jb));a.Md.Hb();return jp(a,d,"blank")}).h(function(d){clearInterval(a.nf);q.window.removeEventListener("orientationchange",
c);q.window.removeEventListener("resize",c);throw d;})};var sp=function(a){this.yd=[];Yo.call(this,a)};w(sp,Yo);
var tp=function(a){var b=q.window&&q.window.opener||null;if(!a.Ca&&b&&/popup/i.test(a.authType||"")){for(var c=0;c<b.frames.length;c++){var d=b.frames[c];try{var e=E(d.location.href);a.apiKey===D(e,"apiKey")&&a.appName===D(e,"appName")&&a.yd.push(new Ok(new Mk(d,d.location.origin)))}catch(f){}}b=[];for(c=0;c<a.yd.length;c++)b.push(Pk(a.yd[c],"getParentOrigin",null,2E4).then(function(f){for(var g=0;g<f.length;g++)f[g].fulfilled&&f[g].value&&(a.Ca=f[g].value);if(!a.Ca)throw Error("Unable to determine origin");
}));return Bf(b).h(function(){}).then(function(){for(var f=0;f<a.yd.length;f++)a.yd[f].close()})}return I()};sp.prototype.start=function(){var a=this;return this.cb().then(function(){return up(a,a.providerId,a.Wd,a.ck,a.sessionId)}).h(function(b){return b instanceof P?a.handleError(b):a.handleError(new P("internal-error"))})};
sp.prototype.cb=function(){var a=this;return I().then(function(){fp(a);if(a.apiKey&&a.authType&&a.providerId){var b=new Ih(a.apiKey,a.appName,a.authType,a.Jb,a.Ca,a.ze,a.cf,a.Pa,a.sb,a.Xd,a.Yc,a.clientId,a.Wb,a.Qe,a.providerId,a.pb,a.publicKey);return(a.Ca?pj(a.ib).h(function(){ih("Unable to verify that the app domain is authorized");throw new P("bad-request");}).then(function(c){if(!Hg(c,a.Ca))throw ih("App domain is unauthorized"),new P("bad-request");}):I()).then(function(){var c=a.Pa?a.Wb?$o:
ap:a.sb?a.clientId||a.pb?bp:cp:Zo;if(c.gf&&c.gf.includes(a.providerId))throw new P("operation-not-supported-in-this-environment","This web-based operation for the "+a.providerId+" provider is not supported in this environment. Please use the identity provider's native SDK instead.");return a.Ab.o.set(Zl,b.m())})}ih("Request is missing required data");throw new P("bad-request");})};
var up=function(a,b,c,d,e){var f,g=hc(ic(E(a.uri.toString()),""),"").toString(),h=null;return lj(a.ib,b,g,c,d,e).then(function(k){f=k;return tp(a)}).then(function(){var k=E(f.authUri),n=dp(a),p;(p=(p=q.window)&&p.innerWidth&&p.innerHeight?{width:parseFloat(p.innerWidth),height:parseFloat(p.innerHeight)}:null)&&800>p.width&&"facebook.com"==b&&!a.Ca&&C(k,"display","popup");n&&C(k,"context_uri",n);h=k.toString();k=a.oc();return a.Ab.o.set(Yl,f.sessionId,k)}).then(function(){return vp(a)}).then(function(){gp(a);
var k=vg();xg(h);Ho()&&"Safari"===zg(L())&&setTimeout(function(){k===vg()&&(new Uo(null,function(){a.handleError(new P("user-cancelled"))})).render(document.body)},1E3)})},vp=function(a){if(!Jo())return I();var b=0;return new H(function(c,d){var e=function(){10<=b++?(ih("Unable to save initial state."),d(new P("bad-request","Unable to save initial state. This may happen if browser sessionStorage is inaccessible."))):$l(a.Ab).then(function(f){f?c():setTimeout(e,1)})};e()})};var wp=function(a){Yo.call(this,a)};m(wp,Yo);wp.prototype.start=function(){var a=this;return this.cb().then(function(){return xp(a)}).h(function(b){return b instanceof P?a.handleError(b):a.handleError(new P("internal-error"))})};
wp.prototype.cb=function(){var a=this;return $l(this.Ab).then(function(b){if(!b)throw ih("Unable to process request due to missing initial state."),new P("bad-request","Unable to process request due to missing initial state. This may happen if browser sessionStorage is inaccessible or accidentally cleared.");var c=b&&b.A,d=b&&b.getType();if(!c)throw new P("invalid-api-key");if(!d)throw new P("internal-error");a.apiKey=b.A;a.Xd=b.fh;a.Yc=b.U;fp(a);a.appName=b.B;a.authType=b.getType();a.Jb=b.T;a.Ca=
b.Cc;a.ze=b.Qa;a.cf=b.tf;a.Pa=b.dc;a.sb=b.ed;a.clientId=b.Fb;a.Wb=b.mi;a.Qe=b.C;a.providerId=b.zc;a.pb=b.Kb;a.publicKey=b.Yh})};var yp=function(a,b){if(a.De&&"apple.com"===a.providerId){b=E(b);var c=qh(a.De);a.De=null;for(var d in c)c.hasOwnProperty(d)&&C(b,d,c[d]);return b.toString()}return b},xp=function(a){var b=yp(a,a.uri.toString()),c=a.oc();return a.Ab.nc(c).then(function(d){if(!d)throw new P("internal-error");return jp(a,b,d,a.De)})};var zp=null;function Ap(a){Bp();Jg().then(function(){var b=new Uo(a.appName,function(){b.Hb();xg(a.toString());setTimeout(function(){wg(null)},4E3)});b.render(document.body)})}function Cp(a){var b=a;try{var c=Fh(ch(a));c&&c.message&&(b=c.message)}catch(d){}Bp();Jg().then(function(){(new Vo(b)).render(document.body)})}
function Bp(){var a=bb(cb('.mdl-card{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;font-size:16px;font-weight:400;min-height:200px;overflow:hidden;width:330px;z-index:1;position:relative;background:#fff;border-radius:2px;-moz-box-sizing:border-box;box-sizing:border-box}.mdl-card__media{background-color:#ff4081;background-repeat:repeat;background-position:50% 50%;background-size:cover;background-origin:padding-box;background-attachment:scroll;-moz-box-sizing:border-box;box-sizing:border-box}.mdl-card__title{-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;color:#000;display:block;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:stretch;-webkit-justify-content:stretch;-moz-box-pack:stretch;-ms-flex-pack:stretch;justify-content:stretch;line-height:normal;padding:16px;-webkit-perspective-origin:165px 56px;perspective-origin:165px 56px;-webkit-transform-origin:165px 56px;transform-origin:165px 56px;-moz-box-sizing:border-box;box-sizing:border-box}.mdl-card__title.mdl-card--border{border-bottom:1px solid rgba(0,0,0,.1)}.mdl-card__title-text{-webkit-align-self:flex-end;-ms-flex-item-align:end;align-self:flex-end;color:inherit;display:block;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;font-size:24px;font-weight:300;line-height:normal;overflow:hidden;-webkit-transform-origin:149px 48px;transform-origin:149px 48px;margin:0}.mdl-card__subtitle-text{font-size:14px;color:rgba(0,0,0,.54);margin:0}.mdl-card__supporting-text{color:rgba(0,0,0,.54);font-size:1rem;line-height:18px;overflow:hidden;padding:16px;width:90%}.mdl-card__supporting-text.mdl-card--border{border-bottom:1px solid rgba(0,0,0,.1)}.mdl-card__actions{font-size:16px;line-height:normal;width:100%;background-color:transparent;padding:8px;-moz-box-sizing:border-box;box-sizing:border-box}.mdl-card__actions.mdl-card--border{border-top:1px solid rgba(0,0,0,.1)}.mdl-card--expand{-webkit-box-flex:1;-webkit-flex-grow:1;-moz-box-flex:1;-ms-flex-positive:1;flex-grow:1}.mdl-card__menu{position:absolute;right:16px;top:16px}.mdl-button{background:transparent;border:none;border-radius:2px;color:#000;position:relative;height:36px;margin:0;min-width:64px;padding:0 16px;display:inline-block;font-family:Roboto,Helvetica,Arial,sans-serif;font-size:14px;font-weight:500;text-transform:uppercase;line-height:1;letter-spacing:0;overflow:hidden;will-change:box-shadow;-webkit-transition:box-shadow .2s cubic-bezier(.4,0,1,1),background-color .2s cubic-bezier(.4,0,.2,1),color .2s cubic-bezier(.4,0,.2,1);transition:box-shadow .2s cubic-bezier(.4,0,1,1),background-color .2s cubic-bezier(.4,0,.2,1),color .2s cubic-bezier(.4,0,.2,1);outline:none;cursor:pointer;text-decoration:none;text-align:center;line-height:36px;vertical-align:middle}.mdl-button::-moz-focus-inner{border:0}.mdl-button:hover{background-color:hsla(0,0%,62%,.2)}.mdl-button:focus:not(:active){background-color:rgba(0,0,0,.12)}.mdl-button:active{background-color:hsla(0,0%,62%,.4)}.mdl-button.mdl-button--colored{color:#3f51b5}.mdl-button.mdl-button--colored:focus:not(:active){background-color:rgba(0,0,0,.12)}input.mdl-button[type=submit]{-webkit-appearance:none}.mdl-button--raised{background:hsla(0,0%,62%,.2);box-shadow:0 2px 2px 0 rgba(0,0,0,.14),0 3px 1px -2px rgba(0,0,0,.2),0 1px 5px 0 rgba(0,0,0,.12)}.mdl-button--raised:active{box-shadow:0 4px 5px 0 rgba(0,0,0,.14),0 1px 10px 0 rgba(0,0,0,.12),0 2px 4px -1px rgba(0,0,0,.2);background-color:hsla(0,0%,62%,.4)}.mdl-button--raised:focus:not(:active){box-shadow:0 0 8px rgba(0,0,0,.18),0 8px 16px rgba(0,0,0,.36);background-color:hsla(0,0%,62%,.4)}.mdl-button--raised.mdl-button--colored{background:#3f51b5;color:#fff}.mdl-button--raised.mdl-button--colored:hover{background-color:#3f51b5}.mdl-button--raised.mdl-button--colored:active{background-color:#3f51b5}.mdl-button--raised.mdl-button--colored:focus:not(:active){background-color:#3f51b5}.mdl-button--raised.mdl-button--colored .mdl-ripple{background:#fff}.mdl-button--fab{border-radius:50%;font-size:24px;height:56px;margin:auto;min-width:56px;width:56px;padding:0;overflow:hidden;background:hsla(0,0%,62%,.2);box-shadow:0 1px 1.5px 0 rgba(0,0,0,.12),0 1px 1px 0 rgba(0,0,0,.24);position:relative;line-height:normal}.mdl-button--fab .material-icons{position:absolute;top:50%;left:50%;-webkit-transform:translate(-12px,-12px);transform:translate(-12px,-12px);line-height:24px;width:24px}.mdl-button--fab.mdl-button--mini-fab{height:40px;min-width:40px;width:40px}.mdl-button--fab .mdl-button__ripple-container{border-radius:50%;-webkit-mask-image:-webkit-radial-gradient(circle,#fff,#000)}.mdl-button--fab:active{box-shadow:0 4px 5px 0 rgba(0,0,0,.14),0 1px 10px 0 rgba(0,0,0,.12),0 2px 4px -1px rgba(0,0,0,.2);background-color:hsla(0,0%,62%,.4)}.mdl-button--fab:focus:not(:active){box-shadow:0 0 8px rgba(0,0,0,.18),0 8px 16px rgba(0,0,0,.36);background-color:hsla(0,0%,62%,.4)}.mdl-button--fab.mdl-button--colored{background:#ff4081;color:#fff}.mdl-button--fab.mdl-button--colored:hover{background-color:#ff4081}.mdl-button--fab.mdl-button--colored:focus:not(:active){background-color:#ff4081}.mdl-button--fab.mdl-button--colored:active{background-color:#ff4081}.mdl-button--fab.mdl-button--colored .mdl-ripple{background:#fff}.mdl-button--icon{border-radius:50%;font-size:24px;height:32px;margin-left:0;margin-right:0;min-width:32px;width:32px;padding:0;overflow:hidden;color:inherit;line-height:normal}.mdl-button--icon .material-icons{position:absolute;top:50%;left:50%;-webkit-transform:translate(-12px,-12px);transform:translate(-12px,-12px);line-height:24px;width:24px}.mdl-button--icon.mdl-button--mini-icon{height:24px;min-width:24px;width:24px}.mdl-button--icon.mdl-button--mini-icon .material-icons{top:0;left:0}.mdl-button--icon .mdl-button__ripple-container{border-radius:50%;-webkit-mask-image:-webkit-radial-gradient(circle,#fff,#000)}.mdl-button__ripple-container{display:block;height:100%;left:0;position:absolute;top:0;width:100%;z-index:0;overflow:hidden}.mdl-button.mdl-button--disabled .mdl-button__ripple-container .mdl-ripple,.mdl-button[disabled] .mdl-button__ripple-container .mdl-ripple{background-color:transparent}.mdl-button--primary.mdl-button--primary{color:#3f51b5}.mdl-button--primary.mdl-button--primary .mdl-ripple{background:#fff}.mdl-button--primary.mdl-button--primary.mdl-button--fab,.mdl-button--primary.mdl-button--primary.mdl-button--raised{color:#fff;background-color:#3f51b5}.mdl-button--accent.mdl-button--accent{color:#ff4081}.mdl-button--accent.mdl-button--accent .mdl-ripple{background:#fff}.mdl-button--accent.mdl-button--accent.mdl-button--fab,.mdl-button--accent.mdl-button--accent.mdl-button--raised{color:#fff;background-color:#ff4081}.mdl-button.mdl-button--disabled.mdl-button--disabled,.mdl-button[disabled][disabled]{color:rgba(0,0,0,.26);cursor:default;background-color:transparent}.mdl-button--fab.mdl-button--disabled.mdl-button--disabled,.mdl-button--fab[disabled][disabled]{background-color:rgba(0,0,0,.12);color:rgba(0,0,0,.26)}.mdl-button--raised.mdl-button--disabled.mdl-button--disabled,.mdl-button--raised[disabled][disabled]{background-color:rgba(0,0,0,.12);color:rgba(0,0,0,.26);box-shadow:none}.mdl-button--colored.mdl-button--disabled.mdl-button--disabled,.mdl-button--colored[disabled][disabled]{color:rgba(0,0,0,.26)}.mdl-button .material-icons{vertical-align:middle}.mdl-progress{display:block;position:relative;height:4px;width:500px;max-width:100%}.mdl-progress>.bar{display:block;position:absolute;top:0;bottom:0;width:0;-webkit-transition:width .2s cubic-bezier(.4,0,.2,1);transition:width .2s cubic-bezier(.4,0,.2,1)}.mdl-progress>.progressbar{background-color:#3f51b5;z-index:1;left:0}.mdl-progress>.bufferbar{background-image:-webkit-gradient(linear,left top,right top,from(hsla(0,0%,100%,.7)),to(hsla(0,0%,100%,.7))),-webkit-gradient(linear,left top,right top,from(#3f51b5),to(#3f51b5));background-image:-webkit-linear-gradient(left,hsla(0,0%,100%,.7),hsla(0,0%,100%,.7)),-webkit-linear-gradient(left,#3f51b5,#3f51b5);background-image:linear-gradient(90deg,hsla(0,0%,100%,.7),hsla(0,0%,100%,.7)),linear-gradient(90deg,#3f51b5,#3f51b5);z-index:0;left:0}.mdl-progress>.auxbar{right:0}@supports (-webkit-appearance:none){.mdl-progress:not(.mdl-progress--indeterminate):not(.mdl-progress--indeterminate)>.auxbar,.mdl-progress:not(.mdl-progress__indeterminate):not(.mdl-progress__indeterminate)>.auxbar{background-image:-webkit-gradient(linear,left top,right top,from(hsla(0,0%,100%,.7)),to(hsla(0,0%,100%,.7))),-webkit-gradient(linear,left top,right top,from(#3f51b5),to(#3f51b5));background-image:-webkit-linear-gradient(left,hsla(0,0%,100%,.7),hsla(0,0%,100%,.7)),-webkit-linear-gradient(left,#3f51b5,#3f51b5);background-image:linear-gradient(90deg,hsla(0,0%,100%,.7),hsla(0,0%,100%,.7)),linear-gradient(90deg,#3f51b5,#3f51b5);-webkit-mask:url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIj8+Cjxzdmcgd2lkdGg9IjEyIiBoZWlnaHQ9IjQiIHZpZXdQb3J0PSIwIDAgMTIgNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgogIDxlbGxpcHNlIGN4PSIyIiBjeT0iMiIgcng9IjIiIHJ5PSIyIj4KICAgIDxhbmltYXRlIGF0dHJpYnV0ZU5hbWU9ImN4IiBmcm9tPSIyIiB0bz0iLTEwIiBkdXI9IjAuNnMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAvPgogIDwvZWxsaXBzZT4KICA8ZWxsaXBzZSBjeD0iMTQiIGN5PSIyIiByeD0iMiIgcnk9IjIiIGNsYXNzPSJsb2FkZXIiPgogICAgPGFuaW1hdGUgYXR0cmlidXRlTmFtZT0iY3giIGZyb209IjE0IiB0bz0iMiIgZHVyPSIwLjZzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgLz4KICA8L2VsbGlwc2U+Cjwvc3ZnPgo=");mask:url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIj8+Cjxzdmcgd2lkdGg9IjEyIiBoZWlnaHQ9IjQiIHZpZXdQb3J0PSIwIDAgMTIgNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgogIDxlbGxpcHNlIGN4PSIyIiBjeT0iMiIgcng9IjIiIHJ5PSIyIj4KICAgIDxhbmltYXRlIGF0dHJpYnV0ZU5hbWU9ImN4IiBmcm9tPSIyIiB0bz0iLTEwIiBkdXI9IjAuNnMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAvPgogIDwvZWxsaXBzZT4KICA8ZWxsaXBzZSBjeD0iMTQiIGN5PSIyIiByeD0iMiIgcnk9IjIiIGNsYXNzPSJsb2FkZXIiPgogICAgPGFuaW1hdGUgYXR0cmlidXRlTmFtZT0iY3giIGZyb209IjE0IiB0bz0iMiIgZHVyPSIwLjZzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgLz4KICA8L2VsbGlwc2U+Cjwvc3ZnPgo=")}}.mdl-progress:not(.mdl-progress--indeterminate)>.auxbar,.mdl-progress:not(.mdl-progress__indeterminate)>.auxbar{background-image:-webkit-gradient(linear,left top,right top,from(hsla(0,0%,100%,.9)),to(hsla(0,0%,100%,.9))),-webkit-gradient(linear,left top,right top,from(#3f51b5),to(#3f51b5));background-image:-webkit-linear-gradient(left,hsla(0,0%,100%,.9),hsla(0,0%,100%,.9)),-webkit-linear-gradient(left,#3f51b5,#3f51b5);background-image:linear-gradient(90deg,hsla(0,0%,100%,.9),hsla(0,0%,100%,.9)),linear-gradient(90deg,#3f51b5,#3f51b5)}.mdl-progress.mdl-progress--indeterminate>.bar1,.mdl-progress.mdl-progress__indeterminate>.bar1{background-color:#3f51b5;-webkit-animation-name:indeterminate1;animation-name:indeterminate1;-webkit-animation-duration:2s;animation-duration:2s;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;-webkit-animation-timing-function:linear;animation-timing-function:linear}.mdl-progress.mdl-progress--indeterminate>.bar3,.mdl-progress.mdl-progress__indeterminate>.bar3{background-image:none;background-color:#3f51b5;-webkit-animation-name:indeterminate2;animation-name:indeterminate2;-webkit-animation-duration:2s;animation-duration:2s;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;-webkit-animation-timing-function:linear;animation-timing-function:linear}@-webkit-keyframes indeterminate1{0%{left:0;width:0}50%{left:25%;width:75%}75%{left:100%;width:0}}@keyframes indeterminate1{0%{left:0;width:0}50%{left:25%;width:75%}75%{left:100%;width:0}}@-webkit-keyframes indeterminate2{0%{left:0;width:0}50%{left:0;width:0}75%{left:0;width:25%}to{left:100%;width:0}}@keyframes indeterminate2{0%{left:0;width:0}50%{left:0;width:0}75%{left:0;width:25%}to{left:100%;width:0}}body{margin:0}.firebase-container{background-color:#fff;box-sizing:border-box;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;color:rgba(0,0,0,.87);direction:ltr;font:16px Roboto,arial,sans-serif;margin:0 auto;max-width:360px;overflow:hidden;padding-top:8px;position:relative;width:100%}.firebase-progress-bar{height:5px;left:0;position:absolute;top:0;width:100%}.firebase-hidden-button{height:1px;visibility:hidden;width:1px}.firebase-container#app-verification-screen{top:100px}.firebase-title{color:rgba(0,0,0,.87);direction:ltr;font-size:24px;font-weight:500;line-height:24px;margin:0;padding:0;text-align:center}.firebase-middle-progress-bar{height:5px;margin-left:auto;margin-right:auto;top:20px;width:250px}.firebase-hidden{display:none}.firebase-rtl{direction:rtl;text-align:right}@media (max-width:520px){.firebase-container{box-shadow:none;max-width:none;width:100%}}'));0===
a.length?a=Fb:(x(!A(a,"<"),"Forbidden '<' character in style sheet string: "+a),a=new Eb(a,Db));var b=Aa||(Aa=new xd),c=b.Ra;if(Fc&&c.createStyleSheet)b=c.createStyleSheet(),No(b,a);else{c=yd(b,"HEAD")[0];if(!c){var d=yd(b,"BODY")[0];c=b.Wg("HEAD");d.parentNode.insertBefore(c,d)}d=b.Wg("STYLE");var e=Sb('style[nonce],link[rel="stylesheet"][nonce]');e&&d.setAttribute("nonce",e);No(d,a);b.appendChild(c,d)}};var Dp=function(){var a=vg();switch(D(E(a),"blank")?"blank":"verifyApp"===ep(E(a),"authType")?"verifyApp":Sh(a)?"dynamicLink":D(E(a),"firebaseError")?"error":ep(E(a),"apiKey")?"starter":"finisher"){case "blank":wg(null);return;case "dynamicLink":a=Sh(a);Ap(a);return;case "error":a=D(E(a),"firebaseError")||null;Cp(a);return;case "starter":zp=new sp(a);break;case "finisher":zp=new wp(a);break;case "verifyApp":zp=new qp(a)}Bp();zp.start()},Ep=["fireauth","oauthhelper","widget","initialize"],Fp=q;
Ep[0]in Fp||"undefined"==typeof Fp.execScript||Fp.execScript("var "+Ep[0]);for(var Gp;Ep.length&&(Gp=Ep.shift());)Ep.length||void 0===Dp?Fp=Fp[Gp]&&Fp[Gp]!==Object.prototype[Gp]?Fp[Gp]:Fp[Gp]={}:Fp[Gp]=Dp;}).call(this);
