<template>
  <div>
    <StudioHeader />
    <div class="flex h-[calc(100vh-50px)] max-h-[calc(100vh-50px)] overflow-hidden">
      <template v-if="photos.length > 0">
        <StudioEditorSidebar class="h-[calc(100vh-50px)]" />
        <StudioEditorPhotoGrid class="max-h-[calc(100vh-50px)] overflow-y-scroll" />
      </template>
      <template v-else-if="photos.length === 0 && photoTemplates.length > 0">
        <div class="w-full h-screen max-h-[calc(100vh-50px)] flex items-center justify-center flex-col p-8 bg-gray-200">
          <Card class="mx-auto max-w-3xl max-h-[650px]">
            <div class="flex flex-col items-center justify-center p-4 text-center gap-2 max-w-xl mx-auto">
              <Pill>BETA</Pill>
              <H3>Welcome to the studio!</H3>
              <Paragraph size="md">
                HeadshotPro studio allows you full control over your photos. Select a photo template and start creating your own photos.
              </Paragraph>
            </div>
            <div class="flex flex-col text-center gap-1 items-center justify-start">
              <Heading>
                Select a photo template to start
              </Heading>
              <div class="grid grid-cols-4 gap-4 p-4 bg-slate-100 rounded-lg max-h-[400px] overflow-y-scroll">
                <div v-for="photoTemplate in photoTemplates" :key="photoTemplate._id" class="w-full h-full cursor-pointer hover:scale-105 transition-all duration-200" @click="createInitialPhoto(photoTemplate)">
                  <ImageDns v-if="photoTemplate?.url" :src="photoTemplate?.url" alt="Photo Template" class="w-full h-full object-cover" />
                  <div v-else class="w-full h-full flex items-center justify-center">
                    <IconPhoto class="w-10 h-10 text-gray-500" />
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </template>
      <template v-else>
        <div class="grid grid-cols-6 gap-2 p-8 w-full">
          <template v-for="(item, index) in 32">
            <SkeletonBlock :key="index" height="300px" />
          </template>
        </div>
      </template>
    </div>
    <Popup v-if="screenTooSmall" size="md" :show="screenTooSmall">
      <div class="flex flex-col items-center justify-center h-full text-center">
        <Heading>🚫 Please visit on desktop</Heading>
        <Paragraph>This tool is not optimized for mobile devices. Please visit on desktop to use this tool.</Paragraph>
      </div>
    </Popup>
    <StudioCreditPurchaseModal />
  </div>
</template>

<script>
import Pill from '@/components/landingpage/common/Pill.vue'
import H3 from '@/components/landingpage/common/H3.vue'
import Heading from '@/components/landingpage/common/H5.vue'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
export default {
  components: {
    Pill,
    H3,
    Heading,
    Paragraph
  },
  layout: 'protected',
  data () {
    return {
      checkPhotosInterval: null,
      screenTooSmall: false
    }
  },
  // layout: 'protected',
  computed: {
    photoTemplates () {
      return this.$store.state.studio.photoTemplates.filter(template => template.gender === this.gender)
    },
    gender () {
      return this.$store.state.studio.settings.gender
    },
    settings () {
      return this.$store.state.studio.settings
    },
    photos () {
      return this.$store.state.studio.photos
    },
    pendingPhotos () {
      return this.photos.filter(photo => photo.status !== 'completed' && photo.status !== 'failed' && photo.status !== 'deleted')
    }
  },
  async mounted () {
    this.$loading.show({
      title: 'Getting everything ready...'
    })
    this.$store.commit('studio/SET_MODEL_ID', this.$route.params.id)
    if (this.photoTemplates.length === 0) {
      this.$store.dispatch('studio/fetchPhotoTemplates')
    }
    this.$store.commit('studio/UPDATE_SETTINGS', { key: 'modelId', value: this.$route.params.id })
    await Promise.all([
      this.$store.dispatch('studio/fetchModel'),
      this.$store.dispatch('studio/fetchPhotos', this.$route.params.id),
      this.$store.dispatch('studio/fetchOutfits'),
      this.$store.dispatch('studio/fetchEmotions')
    ])
    this.$loading.hide()
    this.checkPhotosInterval = setInterval(() => {
      this.checkPendingPhotos()
    }, 5000)
    this.handleResize()
    window.addEventListener('resize', this.handleResize)
    this.$posthog.startSessionRecording(true)
  },
  beforeDestroy () {
    clearInterval(this.checkPhotosInterval)
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    selectPhotoTemplate (photoTemplateId) {
      console.log(photoTemplateId)
      this.$store.commit('studio/UPDATE_SETTINGS', { key: 'photoTemplateId', value: photoTemplateId })
    },
    async checkPendingPhotos () {
      if (this.pendingPhotos.length) {
        await this.$store.dispatch('studio/fetchPendingPhotos')
      }
    },
    handleResize () {
      this.screenTooSmall = window.innerWidth < 700
    },
    async createInitialPhoto (photoTemplate) {
      try {
        this.$loading.show({
          title: 'Creating your first photo...'
        })
        await this.$store.commit('studio/UPDATE_SETTINGS', { key: 'photoTemplateId', value: photoTemplate._id })
        await this.$store.dispatch('studio/createPhoto')
      } catch (err) {
        console.log(err)
        this.$loading.hide()
        this.$toast.error('Failed to create photo')
      } finally {
        this.$loading.hide()
      }
    }
  }
}
</script>

<style>

</style>
