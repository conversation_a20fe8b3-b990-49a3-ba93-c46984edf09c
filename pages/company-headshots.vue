<template>
  <div>
    <LandingpageB2bNavigation @showSalesModal="showSalesModal = true" />
    <main class="flex flex-col flex-1">
      <LandingpageB2bHero @showSalesModal="showSalesModal = true" />
      <LandingpageB2bReviews />
      <LandingpageB2bTeamHeadshots />
      <LandingpageB2bForAdmins />
      <LandingpageB2bForMembers />
      <LandingpageB2bSecurity />
      <LandingpageB2bUseCases />
      <LandingpageB2bZapierIntegration @showSalesModal="showSalesModal = true" />
      <LandingpageB2bVersus />
      <LandingpageB2bPricing @showSalesModal="showSalesModal = true" />
      <LandingpageB2bFAQ />
      <LandingpageB2bFeatures />
      <LandingpageB2bCTA @showSalesModal="showSalesModal = true" />
    </main>
    <MarketingFooter />
    <MarketingSalesModal v-if="showSalesModal" @close="showSalesModal = false" />
  </div>
</template>

<script>
export default {
  data () {
    return {
      showSalesModal: false,
      showCustomBackdrop: false
    }
  },
  head () {
    const { path } = this.$route
    const base = 'https://www.headshotpro.com'
    return {
      title: this.title + ' | HeadshotPro',
      link: [
        {
          rel: 'canonical',
          href: base + this.localePath(path)
        },
        {
          rel: 'alternate',
          hreflang: 'en',
          href: base + this.localePath(path, 'en')
        },
        {
          rel: 'alternate',
          hreflang: 'es',
          href: base + this.localePath(path, 'es')
        },
        {
          rel: 'alternate',
          hreflang: 'de',
          href: base + this.localePath(path, 'de')
        },
        {
          rel: 'alternate',
          hreflang: 'x-default',
          href: base + this.localePath(path, 'en')
        }
      ],
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.description
        },
        {
          hid: 'twitter:title',
          name: 'twitter:title',
          content: this.title
        },
        {
          hid: 'twitter:description',
          name: 'twitter:description',
          content: this.description
        },
        {
          hid: 'twitter:image:alt',
          name: 'twitter:image:alt',
          content: this.title
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.title
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.description
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        },
        {
          hid: 'thumbnail',
          name: 'thumbnail',
          content: 'https://www.headshotpro.com/og-b2b.jpg'
        },
        {
          hid: 'twitter:image:src',
          name: 'twitter:image:src',
          content: 'https://www.headshotpro.com/og-b2b.jpg'
        },
        {
          hid: 'twitter:image',
          name: 'twitter:image',
          content: 'https://www.headshotpro.com/og-b2b.jpg'
        },
        {
          hid: 'og:image',
          property: 'og:image',
          content: 'https://www.headshotpro.com/og-b2b.jpg'
        },
        {
          hid: 'og:image:secure_url',
          property: 'og:image:secure_url',
          content: this.content?.image || null
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        }
      ]
    }
  },
  computed: {
    title () {
      return this.$t('title')
    },
    description () {
      return this.$t('description')
    }
  }
}
</script>

<i18n>
  {
    "en": {
      "title": "Professional Company & Team Headshots",
      "description": "Getting professional headshots for your company and team has never been this easy. Upload your pictures and choose the style of your headshot. We will use state-of-the-art AI to generate over 120+ headshots for your team to choose from."
    },
    "es": {
      "title": "Fotos Profesionales para Empresas y Equipos",
      "description": "Conseguir fotos profesionales para tu empresa y equipo nunca había sido tan fácil. Sube tus fotos y elige el estilo de tu foto profesional. Usaremos IA de última generación para generar más de 120 fotos para que tu equipo elija."
    },
    "de": {
      "title": "Professionelle Firmen- & Team-Bewerbungsfotos",
      "description": "Professionelle Bewerbungsfotos für dein Unternehmen und Team waren noch nie so einfach. Lade deine Fotos hoch und wähle deinen Bewerbungsfoto-Stil. Wir nutzen modernste KI, um über 120 Bewerbungsfotos für dein Team zu erstellen."
    }
  }
</i18n>

<style>

</style>
