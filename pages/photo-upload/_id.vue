<template>
  <PostcheckoutWrapper :skip="true">
    <LoadingWrapper
      :is-loading="loading"
      title="Getting everything ready..."
      class="h-[calc(100dvh_-_65px)] w-screen"
    >
      <div v-if="!isValid" class="relative h-full w-full flex items-center justify-center">
        <div>
          <UploadPhoneExpiredLink />
        </div>
      </div>
      <div v-else class="flex h-[calc(100dvh_-_65px)] flex-col bg-gray-50">
        <template v-if="forceOnboarding || goodPhotos.length < minimumPhotos">
          <UploadPhoneOnboardingStep
            v-if="activeStep === 'Onboarding'"
            @next-step="activeStep = $event"
          />
          <UploadPhoneRequirementsStep
            v-else-if="activeStep === 'Terms'"
            @next-step="activeStep = 'Allow Camera'"
          />
          <UploadPhoneAllowCamera
            v-else-if="activeStep === 'Allow Camera'"
            :camera-allowed="cameraAllowed"
            :show-upload-from-gallery="$route.query.hasPicturesApproved === 'true'"
            @allowCamera="setupCamera"
            @next-step="activeStep = $event"
          />
          <UploadPhoneCameraSelection
            v-else-if="activeStep === 'Camera Selection'"
            :pause-for="pauseFor"
            @freeCamera="enableFreeCamera"
            @next-step="activeStep = $event"
            @uploadFromGallery="uploadFromGallery"
            @resetPause="pauseFor = 0"
          />
          <template v-if="activeStep === 'Capture'">
            <div class="w-full flex justify-center items-center p-2 bg-white gap-4">
              <span class="text-sm font-medium text-gray-600">Upload progress:</span>
              <PostcheckoutProgressIndicator :current="goodPhotos.length" :total="minimumPhotos" />
            </div>
            <div class="relative flex flex-1 items-center justify-center">
              <div
                v-if="!freeCameraMode"
                class="absolute top-0 z-[50] w-full border-b bg-gray-100/80 p-3 text-center text-base text-gray-700"
                :class="{'bounce-bar' : bounceLocation}"
              >
                <template v-if="currentPose === 'Smile'">
                  <template v-if="currentBackground.label === 'Window'">
                    <strong>Face a window</strong> and
                  </template>
                  <template v-else>
                    {{ currentBackground.label }} background and
                  </template>
                </template>
                <strong>{{ currentPose }}</strong>
              </div>
              <video
                ref="video"
                autoplay
                playsinline
                class="max-h-[calc(100dvh-9rem)] max-w-full -scale-x-100"
              />
              <div
                class="mask-overlay absolute left-0 top-0 h-full w-full bg-black/60 pointer-events-none z-20"
              />
              <div ref="flash" class="absolute left-0 top-0 w-full h-full bg-white pointer-events-none z-30 opacity-0" />
              <div v-if="isTransitioning" class="absolute inset-0 bg-black/50 flex items-center justify-center z-40">
                <div class="bg-white rounded-lg p-6 text-center">
                  <LoadingSpinnerWhite class="!text-primary-500 !w-8 !h-8 mx-auto mb-4" />
                  <p class="text-sm font-medium text-gray-700">Preparing camera...</p>
                </div>
              </div>
            </div>
            <UploadPhoneCameraActions
              :captured="captured"
              :free-camera-mode="freeCameraMode"
              :disabled="isTransitioning"
              @capture="capture"
              @retake="retake"
              @disableFreeCamera="disableFreeCamera"
            />
          </template>
        </template>
        <template v-else>
          <UploadPhoneFinalStep @next-step="activeStep = $event; forceOnboarding = true" />
        </template>
      </div>
    </LoadingWrapper>
    <Popup v-if="instructionsModal && !freeCameraMode && !isUploading && activeStep === 'Capture'" size="sm" @closeModal="instructionsModal = false">
      <UploadPhonePoseModal
        :current-background="currentBackground"
        :current-pose="currentPose"
        @closeModal="instructionsModal = false"
      />
    </Popup>
    <input
      ref="fileInput"
      type="file"
      accept="image/*"
      multiple
      style="display: none"
      @change="handleFileSelect"
    >
    <audio ref="shutterSound" src="/phone-upload/camera_sound.mp3" preload="auto" />

    <Popup v-if="isUploading">
      <div class="flex justify-center items-center">
        <LoadingSpinnerWhite class="!text-[#34A853] !w-5 !h-5" />
      </div>
      <div class="text-center mt-6">
        <p class="text-lg font-bold text-primary-500">
          Uploading image...
        </p>
      </div>
    </Popup>
  </PostcheckoutWrapper>
</template>
<script>
import phoneUploadMixin from '../../plugins/phoneUploadMixin'
import PostcheckoutMixin from '../../mixins/PostcheckoutMixin'
export default {
  mixins: [phoneUploadMixin, PostcheckoutMixin],
  layout: 'photo-upload',
  data () {
    return {
      isValid: false,
      loading: true,
      activeStep: 'Onboarding',
      forceOnboarding: false
    }
  },
  head () {
    return {
      title: 'Photo Upload | HeadshotPro',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'Easily upload your photos with our mobile-friendly photo upload tool.'
        },
        { hid: 'no-zoom', name: 'viewport', content: 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no' }
      ]
    }
  },
  async mounted () {
    await this.load()

    if (navigator.permissions && navigator.permissions.query) {
      try {
        const result = await navigator.permissions.query({ name: 'camera' })
        if (result.state === 'denied') {
          this.cameraAllowed = 'denied'
        } else {
          this.cameraAllowed = result.state
        }
      } catch (error) {
        this.$toast.error('Error checking camera permission: ' + error)
      }
    }
    if (this.$route.query.hasPicturesApproved === 'true') {
      this.activeStep = 'Camera Selection'
    }

    if (this.isValid) {
      this.startFetchInterval({
        modelId: this.$route.params.id
      })
    }
  },
  methods: {
    async load () {
      this.loading = true
      await this.$store.dispatch('onboarding/fetchModelInformation', {
        modelId: this.$route.params.id
      }).then(() => {
        this.isValid = true
        this.loading = false
      }).catch(() => {
        this.isValid = false
        this.loading = false
      })
    },
    uploadFileToServer (file) {
      return new Promise((resolve, reject) => {
        this.$store.dispatch('onboarding/addPhoto', {
          file
        }).then(() => {
          resolve()
        }).catch((error) => {
          reject(error)
        }).finally(() => {
          this.$refs.fileInput.value = ''
        })
      })
    }
  }
}
</script>

  <style scoped>
  .mask-overlay {
    mask-image: radial-gradient(
      ellipse 100px 125px at 50% 50%,
      transparent 95%,
      black 95.01%
    );
  }
  @keyframes flash {
    0% { opacity: 0; }
    50% { opacity: 1; }
    100% { opacity: 0; }
  }

  .flash-animation {
    animation: flash 0.5s;
  }

  .bounce-bar{
    @apply bg-[#F3CE49] text-black;
  }
  </style>

  <style>
  :root {
    touch-action: pan-x pan-y;
    height: 100%;
  }
  </style>
