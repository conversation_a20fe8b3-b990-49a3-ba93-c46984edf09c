<template>
  <div class="flex min-h-screen flex-col bg-gray-50">
    <Header />
    <main class="flex flex-1 flex-col">
      <section class="bg-[#F8FCFF] py-12 sm:py-16">
        <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8 2xl:px-0">
          <div class="max-w-3xl mx-auto text-left md:text-center">
            <p class="text-base font-normal text-paragraph">
              HeadshotPro Blog
            </p>
            <h1
              class="mt-3 text-2xl font-bold tracking-[-1.05px] sm:text-3xl lg:text-[42px] text-primary-500 lg:leading-[48px]"
            >
              Latest Headshot Photography Articles
            </h1>
            <p class="mt-3 text-base font-medium sm:text-lg text-[#474368]">
              Insights, ideas, and inspiration for headshot photography.
            </p>
          </div>

          <div class="grid grid-cols-1 gap-4 mt-8 xl:gap-x-8 sm:gap-6 sm:grid-cols-2 md:grid-cols-3 sm:mt-12">
            <BlogItem
              href="/best-ai-headshot-generators"
              :thumbnail-url="require('@/assets/img/best-ai-headshot-generator/blog-post-cover.png')"
              excerpt="A professional photoshoot costs hundreds of dollars, your existing photos aren't professional enough, and even the most perfect selfies you've taken at home feel too amateur for the workplace."
              title="The Best AI Headshot Generator (No Affiliate Links)"
              created-at="2024-06-25T03:55:25.012+00:00"
            />
            <BlogItem v-for="item in blogs" :key="item._id" :item="item" />
          </div>
        </div>
      </section>
    </main>
    <MarketingFooter />
  </div>
</template>

<script>
export default {
  async asyncData ({ params, $axios }) {
    const { data } = await $axios.$get('/blog/all')
    return {
      blogs: data
    }
  },
  // asyncData ({ redirect }) {
  //   redirect('/')
  // },
  head () {
    return {
      title: this.title,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.description
        },
        {
          hid: 'twitter:title',
          name: 'twitter:title',
          content: this.title
        },
        {
          hid: 'twitter:description',
          name: 'twitter:description',
          content: this.description
        },
        {
          hid: 'twitter:image:alt',
          name: 'twitter:image:alt',
          content: this.title
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.title
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.description
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        }
      ],
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com/blog'
        }
      ]
    }
  },
  computed: {
    title () {
      return 'Latest articles about headshot photography | HeadshotPro'
    },
    description () {
      return 'Insights, ideas, and inspiration for headshot photography.'
    }
  }

}
</script>

<style>

</style>
