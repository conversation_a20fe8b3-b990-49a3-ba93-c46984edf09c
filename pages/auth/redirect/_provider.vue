<template>
  <div class="bg-black w-full h-screen flex items-center justify-center ">
    <LoadingSpinnerSingle />
    <span class="text-white/70 text-sm ml-1.5">{{ loadingText }}</span>
  </div>
</template>

<script>
export default {
  layout: 'empty',
  data () {
    return {
      adminUid: null,
      unsubscribeAuthChange: () => {},
      isLoading: true,
      loadingText: 'Setting things up...'
    }
  },
  computed: {
    provider () {
      return this.$route.params.provider
    }
  },
  async created () {
    const redirectResults = await this.$fire.auth.getRedirectResult()
    if (redirectResults?.user) {
      this.loadingText = 'Creating your account...'
      // If user data from redirect, setup user details
      await this.createDatabaseAccount(redirectResults.user)
      this.unsubscribeAuthChange = this.$fire.auth.onAuthStateChanged(async (user) => {
        if (user) {
          this.loadingText = 'Logging you in...'
          await this.setupUserDetails(user)
          this.loadingText = 'Redirecting you to the app...'
          if (this.$route.query && this.$route.query.redirect) {
            this.$router.push(this.$route.query.redirect)
          } else {
            this.$router.push('/app')
          }
        } else {
          this.isLoading = false
        }
      })
    } else {
      this.signinWithProvider()
    }
  },
  beforeDestroy () {
    this.unsubscribeAuthChange()
  },
  methods: {
    signinWithProvider () {
      let provider
      if (this.provider === 'google') {
        provider = new this.$fireModule.auth.GoogleAuthProvider()
      } else if (this.provider === 'apple') {
        provider = new this.$fireModule.auth.OAuthProvider('apple.com')
      }
      // const provider = new this.$fireModule.auth.GoogleAuthProvider()
      this.$fire.auth.signInWithRedirect(provider).then((result) => {
        console.log(result)
      }).catch((e) => {
        this.$toast.error(e.message)
        console.log(e)
      })
    }
  }
}
</script>
<style>
</style>
