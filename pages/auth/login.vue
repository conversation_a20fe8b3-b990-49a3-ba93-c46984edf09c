<template>
  <AuthSignupPage :is-loading="isLoading" :loading-text="loadingText" />
</template>

<script>
export default {
  layout: 'empty',
  data () {
    return {
      adminUid: null,
      unsubscribeAuthChange: () => {},
      isLoading: true,
      loadingText: 'Setting things up...'
    }
  },
  head () {
    return {
      title: 'Login | HeadshotPro'
    }
  },
  created () {
    // const redirectResults = await this.$fire.auth.getRedirectResult()
    // if (redirectResults?.user) {
    //   this.loadingText = 'Creating your account...'
    //   // If user data from redirect, setup user details
    //   await this.createDatabaseAccount(redirectResults.user)
    // }
    this.unsubscribeAuthChange = this.$fire.auth.onAuthStateChanged(async (user) => {
      if (user) {
        this.loadingText = 'Logging you in...'
        await this.setupUserDetails(user)
        this.loadingText = 'Redirecting you to the app...'
        if (this.$route.query && this.$route.query.redirect) {
          const decodedRedirectUrl = decodeURIComponent(this.$route.query.redirect)
          this.$router.push(decodedRedirectUrl)
        } else {
          this.$router.push('/app')
        }
      } else {
        this.isLoading = false
      }
    })
  },

  beforeDestroy () {
    this.unsubscribeAuthChange()
  }
}
</script>
