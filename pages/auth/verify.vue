<template>
  <div class="flex min-h-screen w-full items-center justify-center bg-gray-100 text-black">
    <div class="mx-auto w-full max-w-xl">
      <a href="/">
        <Logo class="mx-auto mb-4 w-[100px]" />
      </a>
      <div class="flex w-full flex-col items-center justify-center space-y-4 rounded-lg bg-white py-6 shadow-md">
        <div class="pb-2 text-center">
          <h2 class="text-lg font-bold text-black">
            Verify your email
          </h2>
          <p class="mx-auto max-w-sm text-sm text-black">
            We need you to verify your email, so we can see if you're a real person. An email has been send to
            <strong>{{ $store.state.user.email }}</strong>
            .
            <br>
            Haven't received it?
          </p>
          <div class="flex w-full items-center justify-center py-4">
            <ButtonDark v-if="!send" size="sm" @click="sendVerificationEmail">
              Resend email
            </ButtonDark>
            <p v-else class="text-sm text-gray-700">
              ✅ Send
            </p>
          </div>
          <button class="text-xs text-gray-400 underline" @click="logout">
            Or logout from your account
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  layout: 'empty',
  data () {
    return {
      send: false
    }
  },
  head () {
    return {
      title: 'Verify account | HeadshotPro'
    }
  },
  created () {
    this.unsubscribeAuthChange = this.$fire.auth.onAuthStateChanged((user) => {
      if (user) {
        if (user.emailVerified) {
          this.$router.push('/auth/login')
        }
      } else {
        this.$router.push('/auth/login')
      }
    })
  },
  methods: {
    async sendVerificationEmail () {
      this.send = true
      const userEmail = this.$store.state.user.email
      return await this.$axios.$post('/auth/send-custom-verification-email', {
        userEmail
      })
    }
  }
}
</script>

<style></style>
