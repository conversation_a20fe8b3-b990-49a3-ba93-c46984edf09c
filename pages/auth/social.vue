<template>
  <AuthPageWrapper>
    <h1 class="mt-10 text-xl font-medium text-primary-500">
      Professional business headshots, without physical photo shoot
    </h1>
    <p class="mt-4 mb-4 text-sm text-black opacity-70">
      Please, kindly wait while we sign you in...
    </p>
    <LoadingWrapper :is-loading="true" title="Signing you in..." />
  </AuthPageWrapper>
</template>

<script>
export default {
  mounted () {
    if (!this.$route.query?.token) {
      this.$router.push('/auth/login')
      return
    }

    this.$fire.auth.signInWithCustomToken(this.$route.query?.token)
      .then((data) => {
        localStorage.setItem('authProvider', 'linkedin')
        this.$router.push('/app')
      })
      .catch((error) => {
        console.error(error)
        this.$router.push('/auth/login')
      })
  }
}
</script>
