<template>
  <section class=" text-black flex items-start md:items-center justify-center min-h-screen bg-image px-4 py-8 md:pt-0">
    <div class=" flex items-center justify-center flex-col">
      <nuxt-link to="/">
        <Logo class="w-[180px] text-white" />
      </nuxt-link>
      <Card class="mt-4 rounded-lg shadow  max-w-md p-8 w-full">
        <div class=" text-center w-full min-w-[300px]">
          <h2 class="font-medium text-xl">
            Log in or sign up in seconds
          </h2>
          <p class="text-sm text-gray-700 mb-4">
            Login or create an account to get started
          </p>
          <LoadingWrapper :is-loading="isLoading" :title="loadingText" class="w-full">
            <div v-if="type === null" class="flex items-center justify-between space-x-2 ">
              <ButtonDark @click="type = 'login'">
                I have an account
              </ButtonDark>
              <ButtonWhite @click="type = 'signup'">
                I'm new here
              </ButtonWhite>
            </div>
            <template v-else-if="type === 'signup'">
              <AuthSignupForm />
              <button class="text-gray-600 text-xs mt-4 block underline w-full text-center" @click="type = 'login'">
                Or, login to existing account
              </button>
            </template>
            <template v-else-if="type === 'login'">
              <AuthLoginForm />
              <button class="text-gray-600 text-xs mt-4 block underline w-full text-center" @click="type = 'signup'">
                Or, create a new account
              </button>
            </template>
          </LoadingWrapper>
        </div>
      </Card>
    </div>
  </section>
</template>

<script>

export default {
  layout: 'empty',
  data () {
    return {
      adminUid: null,
      unsubscribeAuthChange: () => {},
      isLoading: true,
      loadingText: 'Setting things up...',
      type: null
    }
  },
  head () {
    return {
      title: 'Create an account with email | HeadshotPro'
    }
  },
  created () {
    this.unsubscribeAuthChange = this.$fire.auth.onAuthStateChanged(async (user) => {
      if (user) {
        this.loadingText = 'Logging you in...'
        await this.setupUserDetails(user)
        this.loadingText = 'Redirecting you to the app...'
        localStorage.setItem('authProvider', 'email')
        if (this.$route.query && this.$route.query.redirect) {
          this.$router.push(this.$route.query.redirect)
        } else {
          this.$router.push('/app')
        }
      } else {
        this.isLoading = false
      }
    })
  },

  beforeDestroy () {
    this.unsubscribeAuthChange()
  }
}
</script>
