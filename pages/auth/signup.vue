<template>
  <div class="flex min-h-screen w-full items-center justify-center bg-gray-100 text-black">
    <div class="mx-auto w-full max-w-xl">
      <nuxt-link to="/">
        <Logo class="mx-auto mb-4 w-[180px]" />
      </nuxt-link>
      <LoadingWrapper :is-loading="isLoading" title="Getting everything ready...">
        <div class="flex w-full flex-col items-center justify-center space-y-4 rounded-lg bg-white py-6 shadow-md">
          <template v-if="!hasInviteCookie && organization?.name">
            Please enable cookies in your browser to continue.
          </template>
          <template v-else>
            <template v-if="(invite && !organization?.name) || !isValid">
              <div class="pb-2 text-center">
                <h2 class="text-xl font-medium mb-2">
                  Invalid invite link.
                </h2>
                <p class="text-sm text-gray-500">
                  This invite link either got used or is expired. Please contact your organization administrator to get a new invite.
                </p>
              </div>
            </template>
            <div v-else class="pb-2 text-center">
              <h2 class="text-xl font-medium mb-6">
                <template v-if="organization?.name">
                  Create an account to join <br><strong>{{ organization.name }}</strong> and create your headshots
                </template>
                <template v-else>
                  Create a free account to start generating your headshots
                </template>
              </h2>
              <AuthGoogleButton class="block w-full rounded-md" />
              <nuxt-link to="/auth/email">
                <ButtonWhite class="mt-2 w-full">
                  <IconSolidEnveloppe class="mr-1.5 h-4 w-4 text-gray-700" />
                  <span>Sign in with email</span>
                </ButtonWhite>
              </nuxt-link>
              <p class="mx-auto mt-4 max-w-sm text-sm text-black">
                Already have an account?
                <nuxt-link to="/auth/login" class="inline-flex text-blue-500 underline hover:text-blue-600">
                  Login here.
                </nuxt-link>
              </p>
            </div>
          </template>
        </div>
      </LoadingWrapper>
    </div>
  </div>
</template>

<script>
export default {
  layout: 'empty',
  data () {
    return {
      unsubscribeAuthChange: () => {},
      userData: {},
      tool: null,
      isLoading: true,
      success: false,
      organization: null,
      hasInviteCookie: false,
      inviteRecord: null,
      isValid: true
    }
  },
  head () {
    return {
      title: 'Create an account | HeadshotPro'
    }
  },
  computed: {
    token () {
      return this.$route.query.token
    },
    invite () {
      return this.$route.query.invite
    }
  },
  async created () {
    if (this.invite) {
      const response = await this.$axios.$get('/invite/single/' + this.invite)
      if (response.success) {
        if (response?.invite?.used === true && response?.invite?.valid === true && !response?.invite?.isGeneric) {
          this.isValid = false
        } else {
          this.organization = response?.invite?.organization
          this.inviteRecord = response?.invite
        }
      }
    }
    this.unsubscribeAuthChange = this.$fire.auth.onAuthStateChanged(async (user) => {
      if (user) {
        await this.setupUserDetails(user)
        if (this.inviteRecord?.isGeneric && this.organization) {
          this.joinOrg()
        } else {
          this.$router.push('/auth/login')
        }
      }
      this.isLoading = false
    })
  },
  mounted () {
    if (this.$route?.query?.invite) {
      this.$cookies.set('hshq_invite', this.$route.query.invite, {
        path: '/',
        maxAge: 60 * 60 * 24 * 45
      })
    }
    // Check if cookies has been set/is allowed by browser
    if (this.$cookies.get('hshq_invite')) {
      this.hasInviteCookie = true
    }
  },
  beforeDestroy () {
    this.unsubscribeAuthChange()
  },
  methods: {
    joinOrg () {
      if (confirm(`Do you want to join ${this.organization.name}?`)) {
        this.$axios.$post('/invite/accept', { inviteUid: this.inviteRecord.uid })
          .then((res) => {
            this.$toast.success('You have joined ' + this.organization.name + '!')
            this.$router.push('/auth/login')
          })
          .catch((err) => {
            this.handleError(err)
            // this.$router.push('/auth/login')
          })
      } else {
        this.$router.push('/auth/login')
      }
    }
  }
}
</script>

<style></style>
