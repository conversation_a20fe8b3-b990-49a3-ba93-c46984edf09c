<template>
  <div class="w-full">
    <InputTextArea v-model="token" />
    <ButtonPrimary @click="login">
      Login
    </ButtonPrimary>
  </div>
</template>

<script>
export default {
  layout: 'empty',
  data () {
    return {
      token: null
    }
  },
  head () {
    return {
      title: 'Token | HeadshotPro'
    }
  },
  methods: {
    login () {
      if (this.token && this.token.length > 0) {
        this.$fire.auth.signInWithCustomToken(this.token).then((data) => {
          this.$router.push('/app')
        })
      }
    }
  }
}
</script>

<style>

</style>
