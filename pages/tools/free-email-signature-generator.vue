<template>
  <div class="w-full bg-[#F9FCFF] min-h-3/4">
    <Header class="z-10 relative" />
    <div v-if="$route.query.photo" class="pt-4 md:pt-16" />
    <ToolsToolBanner
      v-else
      pretitle="Get a free professional email signature"
      :title="isExclusiveAccess ? 'Exclusive Email Signature Generator' : 'Free Email Signature Generator'"
      subtitle="100% free. Done in 30 seconds."
    >
      <template #description>
        Create your own email signature with our Free Email Signature Generator.
        Upload your own picture or use our
        <NuxtLink to="/" class="underline text-blue-500">
          professional AI Headshot generator
        </NuxtLink>
        to create a professional headshot.
      </template>
    </ToolsToolBanner>
    <div class="max-w-7xl mx-auto px-4">
      <div class="grid xl:grid-cols-2 gap-16">
        <div class="order-last xl:order-first">
          <div class="sticky top-28">
            <p class="text-gray-700 font-bold mb-4">
              Email Signature Preview
            </p>
            <GalleryPreviewPartialsEmailBody>
              <component
                :is="currentTemplate.template"
                ref="signature"
                :key="'photo-' + photoVersion"
                :photo="editedPhoto || photo"
                :name="name"
                :email="email"
                :phone="phone"
                :position="position"
                :website="website"
              />
            </GalleryPreviewPartialsEmailBody>
          </div>
        </div>
        <div>
          <p class="text-gray-700 font-bold mb-4">
            Your data
          </p>
          <div class="w-full p-8 border border-gray-300 bg-white rounded-xl overflow-hidden">
            <LoadingWrapper :is-loading="isLoading">
              <div class="space-y-4">
                <div v-show="isExclusiveAccess">
                  <p class="block text-sm font-medium leading-5 text-gray-900">
                    Template
                  </p>
                  <div class="grid grid-cols-1 gap-4 mt-4 md:grid-cols-3">
                    <button
                      v-for="t in templates"
                      :key="t.id"
                      type="button"
                      class="border-solid border-2 p-4 rounded-md"
                      :class="{
                        'border-teal-500': template === t.id,
                        'border-gray-100 hover:bg-gray-50': template !== t.id
                      }"
                      @click="template = t.id"
                    >
                      <img :src="t.src" :alt="t.alt">
                    </button>
                  </div>
                </div>
                <p v-show="isExclusiveAccess" class="block text-sm font-medium leading-5 text-gray-900">
                  Your photo
                </p>
                <div class="flex items-center justify-start space-x-4">
                  <template v-if="hasValidPhoto">
                    <img :src="photo" alt="Your photo" class="h-24 w-24 rounded-full object-cover object-top flex-shrink-0">
                    <ButtonWhite size="sm" @click="openModal">
                      Customize
                    </ButtonWhite>
                    <ButtonWhite size="sm" @click="resetPicture">
                      Reset
                    </ButtonWhite>
                  </template>
                  <template v-else>
                    <ToolsTeamPagePartialsPicture
                      image-class="h-24 w-24 rounded-full object-cover overflow-hidden"
                      :is-editable="true"
                      initial-value=""
                      @update="updatePicture"
                    />
                  </template>
                </div>
                <div class="w-full pt-1">
                  <Input v-model="name" label="Your name" placeholder="John Doe" class="w-full" />
                </div>
                <Input v-model="position" label="Position" placeholder="AI Model" class="w-full" />
                <Input v-model="phone" label="Your phone number" placeholder="****** 567 890" class="w-full" />
                <Input v-model="email" label="Your email" placeholder="<EMAIL>" class="w-full" />
                <Input v-model="website" label="Website" placeholder="https://example.com" class="w-full" />
              </div>
            </LoadingWrapper>
          </div>
        </div>
      </div>
      <div class="mt-12 flex items-center justify-center">
        <ButtonPrimary size="lg" @click="copySignature">
          Copy signature
        </ButtonPrimary>
      </div>
      <div class="mt-4 text-center">
        <p class="mt-2 text-gray-300">
          <a href="https://support.google.com/mail/answer/8395?hl=en" target="_blank" rel="noopener noreferrer" class="text-xs text-gray-600 hover:underline">
            How to add a signature in Gmail
          </a>
          <span>&nbsp;&nbsp;|&nbsp;&nbsp;</span>
          <a href="https://support.microsoft.com/en-us/office/create-and-add-an-email-signature-in-outlook-8ee5d4f4-68fd-464a-a1c1-0e1c80bb27f2" target="_blank" rel="noopener noreferrer" class="text-xs text-gray-600 hover:underline">
            How to add a signature in Outlook
          </a>
        </p>
      </div>
    </div>
    <div v-if="$route.query.photo" class="pt-4 md:pt-16" />
    <Modal
      v-if="showModal"
      max-width="sm:max-w-6xl"
      @close="showModal = false"
    >
      <LoadingWrapper :is-loading="isLoading" title="Loading your photo">
        <Editor id="editor" :key="photo" class="relative z-10" :inline="true" @confirm="showModal = false" />
      </LoadingWrapper>
    </Modal>
    <LandingpageV2Faq />
    <MarketingUpgradeYourHeadshots v-if="!$route.query.photo" class="py-12 mt-16" />
    <MarketingFooter />
  </div>
</template>

<script>
import copy from 'copy-to-clipboard'

export default {
  data () {
    return {
      isLoading: false,
      name: '',
      email: '',
      phone: '',
      website: '',
      position: 'AI Headshot Model | Professional Headshot Taker',
      photo: null,
      showModal: false,
      photoVersion: 0,
      template: 'style-1',
      templates: [
        {
          src: require('@/assets/img/email-signature/style-1.png'),
          alt: 'Style 1',
          id: 'style-1',
          template: 'GalleryPreviewPartialsEmailSignature'
        },
        {
          src: require('@/assets/img/email-signature/style-2.png'),
          alt: 'Style 2',
          id: 'style-2',
          template: 'GalleryPreviewPartialsEmailSignatureStyle2'
        },
        {
          src: require('@/assets/img/email-signature/style-3.png'),
          alt: 'Style 3',
          id: 'style-3',
          template: 'GalleryPreviewPartialsEmailSignatureStyle3'
        }
      ]
    }
  },
  head () {
    return {
      title: this.title + ' | HeadshotPro',
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com/tools/free-email-signature-generator'
        }
      ],
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.description
        },
        {
          hid: 'twitter:title',
          name: 'twitter:title',
          content: this.title
        },
        {
          hid: 'twitter:description',
          name: 'twitter:description',
          content: this.description
        },
        {
          hid: 'twitter:image:alt',
          name: 'twitter:image:alt',
          content: this.title
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.title
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.description
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        }
      ]
    }
  },
  computed: {
    title () {
      return '100% Free Email Signature Generator'
    },
    description () {
      return 'Get ready to stand out on LinkedIn or your CV with our free AI headshot generator! Upload your favorite photo and let our tool do the work for you. With just a few clicks, you\'ll have access to hundreds of unique and customized headshots to choose from.'
    },
    editedPhoto () {
      return this.$store.state.editor.pfp.result
    },
    isExclusiveAccess () {
      return this.$route.query.access === 'granted' && !!this.$route.query.bento_uuid
    },
    currentTemplate () {
      return this.templates.find(template => template.id === this.template)
    },
    hasValidPhoto () {
      return this.photo && this.photo.trim() !== '' && this.photo !== 'null' && this.photo !== 'undefined'
    }
  },
  mounted () {
    this.$store.commit('editor/UPDATE_PFP', { key: 'result', value: null })
    this.setDataFromUrl()
  },
  methods: {
    openModal () {
      this.$store.commit('editor/UPDATE_PFP', { key: 'src', value: this.photo })
      this.$store.commit('editor/UPDATE_PFP', { key: 'baseSrc', value: this.photo })
      this.showModal = true
    },
    setDataFromUrl () {
      const { name, email, phone, website } = this.$route.query
      this.name = name || 'Your name'
      this.email = email || '<EMAIL>'
      this.phone = phone || '****** 567 890'
      this.website = website || 'https://example.com'
      this.setPhotoFromUrl()
    },
    setPhotoFromUrl () {
      const { photo } = this.$route.query
      if (!photo) {
        this.photo = null
        return
      }

      this.isLoading = true
      this.photo = null

      if (!photo.startsWith('https://')) {
        this.$store.commit('editor/UPDATE_PFP', { key: 'src', value: null })
        this.$store.commit('editor/UPDATE_PFP', { key: 'baseSrc', value: null })
        this.photo = null
        this.isLoading = false
        return
      }

      // try to fetch the photo from the URL to validate it
      fetch(photo)
        .then((res) => {
          if (!res.ok) {
            throw new Error('Failed to fetch photo')
          }

          this.$store.commit('editor/UPDATE_PFP', { key: 'src', value: photo })
          this.$store.commit('editor/UPDATE_PFP', { key: 'baseSrc', value: photo })
          this.photo = photo
          this.photoVersion++
        })
        .catch(() => {
          this.$store.commit('editor/UPDATE_PFP', { key: 'src', value: null })
          this.$store.commit('editor/UPDATE_PFP', { key: 'baseSrc', value: null })
          this.photo = null
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    copySignature () {
      if (!this.photo || !this.name || !this.email) {
        this.$toast.error('Please upload an image and add your name and email')
        return
      }

      const wrapper = this.$refs.signature.$refs.signatureWrapper
      let content = wrapper.innerHTML
      content = content.replace('display: none', 'display: block')
      copy(content, {
        format: 'text/html'
      })
      this.$toast.success('Email signature copied to clipboard!')
    },
    updatePicture (url) {
      this.photo = url
      this.photoVersion++
    },
    resetPicture () {
      this.photo = null
      this.$store.commit('editor/UPDATE_PFP', { key: 'result', value: null })
      this.photoVersion++
    }
  }
}
</script>

  <style>

  </style>
