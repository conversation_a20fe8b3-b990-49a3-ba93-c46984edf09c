<template>
  <div class="w-full bg-[#F8FCFF] min-h-3/4">
    <Header class="z-10 relative" />
    <div v-if="$route.query.photo" class="pt-4 md:pt-16" />
    <EditorHero v-if="!$route.query.photo" />
    <LoadingWrapper :is-loading="isLoading" title="Loading your photo">
      <Editor id="editor" class="relative z-10" shows-modal="email" />
    </LoadingWrapper>
    <EditorFAQ :items="faq" />
    <MarketingUpgradeYourHeadshots v-if="!$route.query.photo" class="py-12 mt-16" />
    <MarketingFooter />
  </div>
</template>

<script>
export default {
  data () {
    return {
      isLoading: false,
      faq: [
        {
          question: 'What is the difference between your free headshot generator and your paid headshot generator?',
          answer: 'This free AI tool does its best to generate you a professional headshot by removing the background of your uploaded image and replacing it with a simple pattern. HeadshotPro’s paid headshot generator creates photo realistic AI headshots (starting at $29) comparable with what you would expect from a professional photographer (starting from $150-$300).'
        },
        {
          question: 'Do I have to enter my credit card information to use this free headshot generator?',
          answer: 'No. You don’t have to enter any personal information to use HeadshotPro’s free headshot generator—it’s 100% free.'
        },
        {
          question: 'What is the best free AI headshot generator app?',
          answer: 'To be honest, we don’t really recommend using a free headshot in a professional setting, but the results you’ll get from this free tool are good enough to use in a pinch. We recommend using HeadshotPro’s paid service to get real professional headshots.'
        },
        {
          question: 'Do you store the photos I upload?',
          answer: 'No. This is a privacy-first headshot generator. All image processing is done on your local browser. The image you upload never touches our servers.'
        },
        {
          question: 'Where can I use my free AI headshots?',
          answer: 'You can use them wherever you like! Most people like to use them on LinkedIn, Twitter, or for their CV.'
        },
        {
          question: 'How much do AI headshots cost?',
          answer: 'The simple headshots you get from this tool are 100% free. Our paid service starts from as little as $29 for professional headshots—but make sure you check out our pricing page. '
        }
      ]
    }
  },
  head () {
    return {
      title: this.title + ' | HeadshotPro',
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com/tools/free-headshot-generator'
        }
      ],
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.description
        },
        {
          hid: 'twitter:title',
          name: 'twitter:title',
          content: this.title
        },
        {
          hid: 'twitter:description',
          name: 'twitter:description',
          content: this.description
        },
        {
          hid: 'twitter:image:alt',
          name: 'twitter:image:alt',
          content: this.title
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.title
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.description
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        }
      ]
    }
  },
  computed: {
    title () {
      return '100% Free AI Headshot Generator'
    },
    description () {
      return 'Get ready to stand out on LinkedIn or your CV with our free AI headshot generator! Upload your favorite photo and let our tool do the work for you. With just a few clicks, you\'ll have access to hundreds of unique and customized headshots to choose from.'
    }
  },
  mounted () {
    if (typeof window === 'undefined') {
      return
    }

    this.setPhotoFromUrl()
  },
  methods: {
    setPhotoFromUrl () {
      const { photo } = this.$route.query
      if (!photo) {
        return
      }

      this.isLoading = true
      if (!photo.startsWith('https://')) {
        this.$store.commit('editor/UPDATE_PFP', { key: 'src', value: null })
        this.$store.commit('editor/UPDATE_PFP', { key: 'baseSrc', value: null })
        this.isLoading = false
        return
      }
      // try to fetch the photo from the URL to validate it
      this.$axios.get(photo)
        .then((res) => {
          this.$store.commit('editor/UPDATE_PFP', { key: 'src', value: photo })
          this.$store.commit('editor/UPDATE_PFP', { key: 'baseSrc', value: photo })
        })
        .catch((err) => {
          console.error(err)
          this.$store.commit('editor/UPDATE_PFP', { key: 'src', value: null })
          this.$store.commit('editor/UPDATE_PFP', { key: 'baseSrc', value: null })
        })
        .finally(() => {
          this.isLoading = false
        })
    }
  }
}
</script>

<style>

</style>
