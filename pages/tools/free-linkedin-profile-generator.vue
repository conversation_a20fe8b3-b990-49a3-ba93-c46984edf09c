<template>
  <div class="w-full bg-[#F8FCFF] min-h-3/4">
    <Header class="z-10 relative" />
    <div class="py-32" :style="`${isLoading || result ? 'padding: 32px 0 !important;' : ''}`">
      <div>
        <ToolsToolBanner
          pretitle="Turn your resume into a LinkedIn profile for free"
          subtitle="No email required. 100% free. Done in 1 minute."
        >
          <template #raw-title>
            <h1 class="text-3xl mt-4 font-bold  tracking-tight text-brand-500 sm:text-4xl xl:text-4xl sm:tracking-tight">
              Free <IconSolidLinkedIn class="h-8 w-8 inline-block" /> LinkedIn Profile Generator
            </h1>
          </template>
          <template #description>
            Update your old LinkedIn profile in 1 minute or less with our free LinkedIn Profile Generator. Upload your CV and our AI will generate your profile from your resume, including your name, headline, bio, experience, education, certifications, languages, and more.
          </template>
        </ToolsToolBanner>
        <div v-if="!isLoading && !result">
          <div class="flex justify-center items-center space-x-4">
            <label class="cursor-pointer hover:bg-brand-800 focus:ring-brand-200 flex flex-shrink-0 content-center items-center justify-center rounded-md border border-transparent bg-brand-500 px-6 py-3 text-center text-base font-medium text-white shadow-sm transition duration-100 focus:outline-none focus:ring-2 focus:ring-offset-2">
              <input type="file" accept="application/pdf" class="sr-only" @change="uploadCv">
              <span>Upload your CV now (PDF)</span>
              <IconPlus class="w-3 h-3 text-white ml-1.5" />
            </label>
          </div>
        </div>
        <div v-if="!isLoading && result" class="flex px-4 justify-center mt-8">
          <ButtonPrimary size="sm" @click="startAgain">
            Start again
          </ButtonPrimary>
        </div>
      </div>
    </div>

    <div v-if="isLoading || profileId" class="max-w-5xl mx-auto">
      <div class="w-full mt-4 border border-gray-300 bg-white rounded-xl overflow-hidden">
        <LinkedinHeader />
        <div class="px-10 relative pb-10">
          <LinkedinProfileImage :is-loading="isLoading" />
          <LinkedinParagraphPlaceholder placeholder-class="w-full max-w-[10rem]" size="2xl" class="font-bold mt-4" :loading="!result?.name">
            {{ result?.name || 'Your name' }}
          </LinkedinParagraphPlaceholder>
          <LinkedinParagraphPlaceholder placeholder-class="w-full max-w-[14rem] mt-1" :loading="!result?.headline?.status || result?.headline?.status === 'pending'">
            {{ result?.headline?.result || 'Your headline' }}
          </LinkedinParagraphPlaceholder>
          <p class="text-sm text-gray-500 mt-4">
            World Wide Web and around · <span class="text-[#2D64BC] font-bold">Contact information</span>
          </p>
          <p class="text-sm text-[#2D64BC] mt-4 font-bold">
            More than 500 contacts
          </p>
          <p class="font-bold text-xl mt-8">
            About
          </p>
          <LinkedinParagraphPlaceholder :loading="!result?.bio?.status || result?.bio?.status === 'pending'">
            {{ result?.bio?.result || 'Your bio should go here' }}
          </LinkedinParagraphPlaceholder>
          <LinkedinParagraphPlaceholder :loading="!result?.bio?.status || result?.bio?.status === 'pending'" :fake="true" placeholder-class="mt-1" />
          <LinkedinParagraphPlaceholder :loading="!result?.bio?.status || result?.bio?.status === 'pending'" :fake="true" placeholder-class="mt-1" />
          <LinkedinParagraphPlaceholder :loading="!result?.bio?.status || result?.bio?.status === 'pending'" :fake="true" placeholder-class="w-1/2 mt-1" />
          <LinkedinProfileSection
            :loading="!result?.experience?.status || result?.experience?.status === 'pending'"
            title="Experience"
            :show-image="true"
            :fields="[{key: 'position', placeholder: 'Position'}, {key: 'company', placeholder: 'Company name'}, {key: 'time', placeholder: 'From, to'}, {key: 'description', placeholder: 'Short description of your role in this company'}]"
            :items="isFinished('experience') ? result.experience.result : Array.from({ length: 3 }, () => ({}))"
          />
          <LinkedinProfileSection
            :loading="!result?.education?.status || result?.education?.status === 'pending'"
            title="Education"
            :show-image="true"
            :fields="[{key: 'degree', placeholder: 'Degree name'}, {key: 'entity', placeholder: 'University name'}, {key: 'time', placeholder: 'From, to'}]"
            :items="isFinished('education') ? result.education.result : Array.from({ length: 2 }, () => ({}))"
          />
          <LinkedinProfileSection
            :loading="!result?.certifications?.status || result?.certifications?.status === 'pending'"
            title="Certifications and licenses"
            :show-image="true"
            :fields="[{key: 'title', placeholder: 'Certification title'}, {key: 'entity', placeholder: 'Issuer name'}, {key: 'time', placeholder: 'From, to'}]"
            :items="isFinished('certifications') ? result.certifications.result : Array.from({ length: 2 }, () => ({}))"
          />
          <LinkedinProfileSection
            :loading="!result?.languages?.status || result?.languages?.status === 'pending'"
            title="Languages"
            :show-image="false"
            :fields="[{key: 'name', placeholder: 'Language name'}, {key: 'level', placeholder: 'Language level'}]"
            :items="isFinished('languages') ? result.languages.result : Array.from({ length: 2 }, () => ({}))"
          />
        </div>
      </div>
    </div>
    <MarketingSteps :steps="steps" title="How to create a LinkedIn profile from your resume" />
    <EditorFAQ :items="faq" subtitle="Answers to common questions about our LinkedIn Profile Generator." />
    <MarketingUpgradeYourHeadshots class="py-12 mt-16" />
    <MarketingFooter />
  </div>
</template>

<script>
export default {
  data () {
    return {
      isLoading: false,
      profileId: null,
      result: null,
      interval: null,
      steps: [
        {
          name: 'Upload your resume above',
          description:
              'Upload the latest version of your resume (PDF). Make sure it includes all the information you want to include in your LinkedIn profile.'
        },
        {
          name: 'Our AI goes to work',
          description:
              'AI will use the information from your resume to update your LinkedIn profile.'
        },
        {
          name: 'Your LinkedIn profile is done',
          description:
              'Voila! Your LinkedIn profile is created from your resume. Now copy and paste it to your profile and press save.'
        }
      ],
      faq: [
        {
          question: 'Do I have to pay to upload my resume to LinkedIn?',
          answer: 'No. This tool is 100% free.'
        },
        {
          question: 'What does this tool do?',
          answer: 'This tools generates a LinkedIn profile from your resume. It will include your name, headline, bio, experience, education, certifications, languages, and more.'
        },
        {
          question: 'I want to upload my resume file (PDF) to my profile for others to download. How do I do that?',
          answer: 'Sure! Just follow these steps: <ul style="padding: 12px 0px"><li>1. Open your LinkedIn profile</li><li>2. Click "Add profile section"</li><li>3. In the pop-up window, click “Add featured."</li><li>4. Click on the plus (+) sign and select “Add media.”</li><li>5. Select your resume and click “Upload.”</li></ul>There you go. Now visitors can view and download your resume via the "Featured" section of your LinkedIn profile.'
        },
        {
          question: 'Where can I get a professional profile picture for LinkedIn?',
          answer: 'HeadshotPro generates professional AI headshots for hundreds of dollars less than a traditional photo shoot. Get your AI headshots here.'
        }
      ]
    }
  },
  head () {
    return {
      title: this.title + ' | HeadshotPro',
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com/tools/free-linkedin-profile-generator'
        }
      ],
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.description
        },
        {
          hid: 'twitter:title',
          name: 'twitter:title',
          content: this.title
        },
        {
          hid: 'twitter:description',
          name: 'twitter:description',
          content: this.description
        },
        {
          hid: 'twitter:image:alt',
          name: 'twitter:image:alt',
          content: this.title
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.title
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.description
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        },
        {
          hid: 'twitter:image:src',
          name: 'twitter:image:src',
          content: 'https://www.headshotpro.com/og-linked-resume-tool.jpg'
        },
        {
          hid: 'twitter:image',
          name: 'twitter:image',
          content: 'https://www.headshotpro.com/og-linked-resume-tool.jpg'
        },
        {
          hid: 'og:image',
          property: 'og:image',
          content: 'https://www.headshotpro.com/og-linked-resume-tool.jpg'
        },
        {
          hid: 'og:image:secure_url',
          property: 'og:image:secure_url',
          content: 'https://www.headshotpro.com/og-linked-resume-tool.jpg'
        }

      ]
    }
  },
  computed: {
    title () {
      return 'The Fastest Way to Add a Resume to LinkedIn with AI (2024)'
    },
    description () {
      return 'Instantly add your resume to LinkedIn with our 100% free AI tool. 100% free. No signup required. The fastest way to add your resume to LinkedIn.'
    }
  },
  methods: {
    uploadCv ($event) {
      if (!$event.target.files || $event.target.files.length === 0) {
        return
      }

      const data = new FormData()
      data.append('files', $event.target.files[0])
      this.submit(data)
    },
    submit (data) {
      this.isLoading = true
      this.result = null

      this.$axios.post('/tools/linkedin/generate-profile', data)
        .then((response) => {
          this.$posthog.capture('$tool:free-linkedin-profile-generator')
          this.isLoading = false
          if (!response.data?.data) {
            this.$toast.error(response.data?.errorMessage || 'Unknown error. Please, try again')
            return
          }

          this.profileId = response.data.data.id
          this.interval = setInterval(() => {
            this.fetchResult()
          }, 5000)
        })
        .catch((error) => {
          this.$toast.error(error.response?.data?.errorMessage || 'Unknown error. Please, try again')
          this.isLoading = false
        })
    },
    startAgain () {
      this.isLoading = false
      this.profileId = null
      this.result = null
      clearInterval(this.interval)
    },
    fetchResult () {
      this.$axios.get(`/tools/linkedin/generate-profile/${this.profileId}`)
        .then((response) => {
          if (response.data?.success) {
            this.result = response.data.data
            let finished = true
            for (const key in this.result) {
              if (this.result[key].status === 'pending') {
                finished = false
                break
              }
            }

            if (finished) {
              clearInterval(this.interval)
            }
          }
        })
    },
    isFinished (field) {
      return this.result?.[field]?.status && this.result?.[field]?.status !== 'pending'
    },
    isFinishedSuccessfully (field) {
      return this.result?.[field]?.status === 'success'
    }
  }
}
</script>

    <style>

    </style>
