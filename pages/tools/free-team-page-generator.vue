<template>
  <div class="w-full bg-gray-50 min-h-3/4">
    <Header class="z-10 relative" />
    <LoadingWrapper :is-loading="isLoading">
      <div>
        <div>
          <section class="relative bg-gray-50">
            <ToolsToolBanner
              pretitle="Get a free professional team page for your website"
              title="Free Team Page Generator"
              subtitle="No email required. 100% free. Done in 5 minutes."
            >
              <template #description>
                Add your team members, choose which template you like, and get a professional team page that you can embed in your website.
              </template>
              <div class="flex justify-center items-center mt-8">
                <ButtonPrimary v-if="!teamId" @click="start">
                  Get started
                </ButtonPrimary>
                <ButtonPrimary v-else @click="restart">
                  Create another team page
                </ButtonPrimary>
              </div>
            </ToolsToolBanner>
            <div class="bg-white py-12">
              <ToolsTeamPageGenerator
                v-if="teamId"
                :key="teamId"
                :team-id="teamId"
                :max-members="100"
                @restart="restart"
              />
              <div v-if="teamId" class="flex justify-center flex-wrap mt-8">
                <ToolsTeamPageSaveTeam :team-id="teamId" :already-saved="alreadySaved" />
              </div>
            </div>
          </section>
        </div>
      </div>
    </LoadingWrapper>
    <LandingpageV2Faq />
    <MarketingUpgradeYourHeadshots class="py-12" />
    <MarketingFooter />
  </div>
</template>

<script>
export default {
  data () {
    return {
      isLoading: false,
      teamId: null,
      alreadySaved: false
    }
  },
  head () {
    return {
      title: this.title + ' | HeadshotPro',
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com/tools/free-team-page-generator'
        }
      ],
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.description
        },
        {
          hid: 'twitter:title',
          name: 'twitter:title',
          content: this.title
        },
        {
          hid: 'twitter:description',
          name: 'twitter:description',
          content: this.description
        },
        {
          hid: 'twitter:image:alt',
          name: 'twitter:image:alt',
          content: this.title
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.title
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.description
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        }
      ]
    }
  },
  computed: {
    title () {
      return '100% Free Team Page Generator'
    },
    description () {
      return 'Get a free professional team page for your website. No email required. 100% free. Done in 5 minutes.'
    }
  },
  mounted () {
    if (typeof window !== 'undefined') {
      if (this.$route.query.magicLink) {
        this.getFromMagicLink()
      } else if (localStorage.getItem('teamId')) {
        this.getFromLocalStorage()
      }
    }
  },
  methods: {
    start () {
      this.isLoading = true
      this.$axios.post('/tools/team-page/create')
        .then((res) => {
          localStorage.setItem('teamId', res.data.data._id)
          this.teamId = res.data.data._id
          this.alreadySaved = res.data.data.hasMagicLink || false
        })
        .catch((err) => {
          console.error(err)
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    restart () {
      if (!confirm('Are you sure you want to start again?')) {
        return
      }

      localStorage.removeItem('teamId')
      this.teamId = null
      window.scrollTo(0, 0)
    },
    getFromMagicLink () {
      this.isLoading = true
      this.$axios.get(`/tools/team-page/page/magic-link/${this.$route.query.magicLink}`)
        .then((res) => {
          localStorage.setItem('teamId', res.data.data._id)
          this.teamId = res.data.data._id
          this.alreadySaved = true
        })
        .catch((err) => {
          console.error(err)
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    getFromLocalStorage () {
      this.isLoading = true
      this.$axios.get(`/tools/team-page/page/${localStorage.getItem('teamId')}`)
        .then((res) => {
          localStorage.setItem('teamId', res.data.data._id)
          this.teamId = res.data.data._id
          this.alreadySaved = res.data.data.hasMagicLink || false
        })
        .catch((err) => {
          console.error(err)
        })
        .finally(() => {
          this.isLoading = false
        })
    }
  }
}
</script>

    <style>

    </style>
