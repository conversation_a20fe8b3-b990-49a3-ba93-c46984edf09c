<template>
  <div>
    <Header />
    <div v-if="$route?.query?.payment === 'success'" class="w-full bg-green-500 p-2 text-center font-medium text-sm text-white">
      Payment successful! Check your email for further details.
    </div>
    <div class="md:min-h-[calc(100vh-100px)] p-4 md:px-0 md:py-12 bg-gray-50">
      <ToolsAvatarGeneratorHeader
        :portrait="portrait"
        :rate-limit="rateLimit"
        :loading="loading"
        @open-file-picker="triggerFileInput"
        @file-selected="handleImageUpload"
        @showUpsell="showUpsellModal = true"
      />
      <div
        v-if="imageBase64 || portrait"
        class="mx-auto grid max-w-4xl grid-cols-1 gap-4 bg-white p-2 shadow-sm ring-1 ring-gray-900/5 sm:grid-cols-2 sm:rounded-xl mt-8"
      >
        <ToolsAvatarGeneratorPreview
          :src="imageBase64"
          :loading="loading"
          :style="`display: ${portrait && screenWidth < 768 ? 'none' : 'block'}`"
        />
        <template v-if="portrait">
          <div class="relative">
            <ToolsAvatarGeneratorPreview
              :src="portrait"
              :loading="loading"
            />
            <div class="w-[calc(100%-32px)] ml-[16px] p-2 rounded-md absolute bottom-4 bg-white/90 backdrop-blur-sm flex items-center justify-between border border-black/20">
              <img class="h-5 w-auto " src="@/assets/img/logo.svg" alt="HeadshotPro logo">
              <ButtonPrimary
                size="sm"
                type="button"
                @click="downloadPortrait"
              >
                <span class="hidden md:flex">Download avatar &darr;</span>
                <span class="flex md:hidden">Download &darr;</span>
              </ButtonPrimary>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="flex flex-col gap-4 rounded-lg bg-gray-100 p-4">
            <p class="text-center text-sm text-gray-500">
              Choose a style
            </p>
            <ToolsAvatarGeneratorStylePicker
              :selected-style="selectedStyle"
              @style-selected="selectedStyle = $event"
            />
            <ButtonPrimary
              v-if="!portrait"
              type="button"
              class="mt-auto w-full"
              :disabled="loading"
              :class="{ 'cursor-not-allowed opacity-50': loading }"
              @click="generatePrompt"
            >
              {{ loading ? "Generating (20s max)..." : "Generate Avatar &rarr;" }}
            </ButtonPrimary>
          </div>
        </template>
        <div class="col-span-full py-2">
          <p class="text-center text-xs text-gray-500">
            Note: These images will auto deleted in 1 hour. We do not save your
            images anywhere.
          </p>
        </div>
      </div>
      <ToolsAvatarGeneratorPreviewImages />
    </div>
    <LandingpageV2Faq />
    <MarketingReviewCTA class="py-12" />
    <MarketingFooter />
    <!-- <Popup v-if="showUpsellModal" size="4xl" @closeModal="showUpsellModal = false">
      <div class="p-4 md:p-8 md:px-16 max-w-4xl">
        <div class="mx-auto text-center">
          <p class="text-sm md:text-lg font-bold text-teal-500">
            Love your avatar or want to try more?
          </p>
          <h3 class="mt-3 font-medium tracking-tight text-3xl text-primary-500">
            Get 20 extra styles for $4.99
          </h3>
          <p class="text-base text-gray-700 max-w-lg mx-auto my-2">
            We'll generate 20 more avatars for you, and email them to you within 72 hours. Speed depends on demand, as OpenAI limits us to 50 avatars/minute.
          </p>

          <template v-if="!loading">
            <div class="w-full max-w-lg grid grid-cols-1 gap-2 rounded-md bg-gray-100 mx-auto p-4 mt-6">
              <Input v-model="purchaseEmail" label="Your email address" class="w-full" placeholder="Email address" />
              <Input v-model="repeatPurchaseEmail" label="Repeat email address" class="w-full" placeholder="Repeat email address" />
              <div class="flex items-center justify-start space-x-2">
                <div class="w-10 h-10 overflow-hidden rounded-md">
                  <img v-if="imageBase64" :src="imageBase64" class="w-10">
                </div>
                <input
                  ref="fileInput"
                  type="file"
                  accept="image/*"
                  @change="handleImageUpload($event.target.files[0])"
                >
              </div>
              <ButtonPrimary size="sm" class="gradient-bg w-full" @click="toCheckout()">
                Order now
              </ButtonPrimary>
            </div>
          </template>
          <template v-else>
            <LoadingSpinner title="Loading..." />
          </template>
        </div>
        <div class="text-center">
          <p class="mt-6 font-medium text-xl text-primary-500">
            <span class=" font-bold  text-teal-500"> {{ $store.state.stats.photos }}</span> AI headshots already generated <br class="hidden md:block">for <span class=" font-bold  text-yellow-500"> {{ $store.state.stats.users }}</span> happy customers!
          </p>
          <MarketingLogoCloud class="w-full max-w-2xl mx-auto mt-8" />
        </div>
      </div>
    </Popup> -->
    <Popup v-if="showUpsellModal" size="4xl" @closeModal="showUpsellModal = false">
      <div class="p-4 md:p-8 md:px-16 max-w-4xl">
        <div class="mx-auto text-center">
          <h2 class="text-sm md:text-lg font-bold text-teal-500">
            Looking for a professional headshot?
          </h2>
          <p class="mt-6 font-medium tracking-tight text-2xl md:text-3xl text-primary-500">
            Get your professional headshot, <br>without a physical photo shoot
          </p>
          <p class="mt-4 text-base font-normal text-gray-600">
            <strong>3,439,182</strong> AI headshots already created for <strong>20,691</strong> happy customers!
          </p>
        </div>
        <div class="flex items-center justify-center py-4">
          <a target="_blank" title="HeadshotPro" href="https://www.headshotpro.com?ref=avatar-generator">
            <ButtonPrimary>
              Get your headshots now
            </ButtonPrimary>
          </a>
        </div>
        <div class="flex items-center justify-center w-full space-x-2 text-sm animate-pulse my-4">
          <IconChevron class="w-4 h-4 text-black/50 transform rotate-90" />
          <span>All of these photos are 100% AI generated.</span>
          <IconChevron class="w-4 h-4 text-black/50 transform rotate-90" />
        </div>
        <img src="@/assets/img/headshot-examples.jpg" class="w-full rounded-md overflow-hidden">
      </div>
    </Popup>
  </div>
</template>

<script>
export default {
  layout: 'tools',
  middleware ({ redirect }) {
    redirect(301, '/tools/free-headshot-generator')
  },
  data () {
    return {
      portrait: '', // 'https://oaidalleapiprodscus.blob.core.windows.net/private/org-tkcJaDOtwgb0afLAN0HqtEwl/user-cjuCjm0eJSst8e74B4kizORu/img-iqN8ziD28x3PwaTLDzZqe3D9.png?st=2023-11-29T03%3A39%3A36Z&se=2023-11-29T05%3A39%3A36Z&sp=r&sv=2021-08-06&sr=b&rscd=inline&rsct=image/png&skoid=6aaadede-4fb3-4698-a8f6-684d7786b067&sktid=a48cca56-e6da-484e-a814-9c849652bcb3&skt=2023-11-28T20%3A02%3A10Z&ske=2023-11-29T20%3A02%3A10Z&sks=b&skv=2021-08-06&sig=xuQWx9eYrQjfPsEYfDTE3EDWKiKWQ3CSOYTFKpNsFIU%3D',
      imageBase64: null, // 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAApgAAAKYB3X3/OAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAANCSURBVEiJtZZPbBtFFMZ/M7ubXdtdb1xSFyeilBapySVU8h8OoFaooFSqiihIVIpQBKci6KEg9Q6H9kovIHoCIVQJJCKE1ENFjnAgcaSGC6rEnxBwA04Tx43t2FnvDAfjkNibxgHxnWb2e/u992bee7tCa00YFsffekFY+nUzFtjW0LrvjRXrCDIAaPLlW0nHL0SsZtVoaF98mLrx3pdhOqLtYPHChahZcYYO7KvPFxvRl5XPp1sN3adWiD1ZAqD6XYK1b/dvE5IWryTt2udLFedwc1+9kLp+vbbpoDh+6TklxBeAi9TL0taeWpdmZzQDry0AcO+jQ12RyohqqoYoo8RDwJrU+qXkjWtfi8Xxt58BdQuwQs9qC/afLwCw8tnQbqYAPsgxE1S6F3EAIXux2oQFKm0ihMsOF71dHYx+f3NND68ghCu1YIoePPQN1pGRABkJ6Bus96CutRZMydTl+TvuiRW1m3n0eDl0vRPcEysqdXn+jsQPsrHMquGeXEaY4Yk4wxWcY5V/9scqOMOVUFthatyTy8QyqwZ+kDURKoMWxNKr2EeqVKcTNOajqKoBgOE28U4tdQl5p5bwCw7BWquaZSzAPlwjlithJtp3pTImSqQRrb2Z8PHGigD4RZuNX6JYj6wj7O4TFLbCO/Mn/m8R+h6rYSUb3ekokRY6f/YukArN979jcW+V/S8g0eT/N3VN3kTqWbQ428m9/8k0P/1aIhF36PccEl6EhOcAUCrXKZXXWS3XKd2vc/TRBG9O5ELC17MmWubD2nKhUKZa26Ba2+D3P+4/MNCFwg59oWVeYhkzgN/JDR8deKBoD7Y+ljEjGZ0sosXVTvbc6RHirr2reNy1OXd6pJsQ+gqjk8VWFYmHrwBzW/n+uMPFiRwHB2I7ih8ciHFxIkd/3Omk5tCDV1t+2nNu5sxxpDFNx+huNhVT3/zMDz8usXC3ddaHBj1GHj/As08fwTS7Kt1HBTmyN29vdwAw+/wbwLVOJ3uAD1wi/dUH7Qei66PfyuRj4Ik9is+hglfbkbfR3cnZm7chlUWLdwmprtCohX4HUtlOcQjLYCu+fzGJH2QRKvP3UNz8bWk1qMxjGTOMThZ3kvgLI5AzFfo379UAAAAASUVORK5CYII=', // simple abse64 string
      loading: false,
      selectedStyle: 'flat illustration',
      dalle3Prompt: null,
      rateLimit: 0,
      showUpsellModal: false,
      purchaseEmail: null,
      repeatPurchaseEmail: null
    }
  },
  head () {
    return {
      title: this.title,
      description: this.description,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.description
        },
        {
          hid: 'twitter:title',
          name: 'twitter:title',
          content: this.title
        },
        {
          hid: 'twitter:description',
          name: 'twitter:description',
          content: this.description
        },
        {
          hid: 'twitter:image:alt',
          name: 'twitter:image:alt',
          content: this.title
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.title
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.description
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        },
        {
          hid: 'thumbnail',
          name: 'thumbnail',
          content: 'https://www.headshotpro.com/og-tool-avatar-maker.jpg'
        },
        {
          hid: 'twitter:image:src',
          name: 'twitter:image:src',
          content: 'https://www.headshotpro.com/og-tool-avatar-maker.jpg'
        },
        {
          hid: 'twitter:image',
          name: 'twitter:image',
          content: 'https://www.headshotpro.com/og-tool-avatar-maker.jpg'
        },
        {
          hid: 'og:image',
          property: 'og:image',
          content: 'https://www.headshotpro.com/og-tool-avatar-maker.jpg'
        },
        {
          hid: 'og:image:secure_url',
          property: 'og:image:secure_url',
          content: 'https://www.headshotpro.com/og-tool-avatar-maker.jpg'
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: 'https://www.headshotpro.com/og-tool-avatar-maker.jpg'
        }
      ]
    }
  },
  computed: {
    title () {
      return 'Free Profile Picture Generator using DALL-E 3'
    },
    description () {
      return "Create a cute avatar from your photo. Powered by DALL-E 3. We'll analyse your photo, describe it in words, and generate a new image from scratch."
    }
  },
  async mounted () {
    await this.getRateLimit()
    await this.$recaptcha.init()
  },
  methods: {
    async generatePrompt () {
      try {
        this.loading = true
        if (!this.imageBase64) {
          return
        }
        const token = await this.$recaptcha.execute('login')
        if (!token) { return this.$toast.open({ type: 'error', message: 'Please accept the reCAPTCHA.' }) }

        const { data, success, errorMessage } = await this.$axios.$post(
          '/tools/avatar-generator/analyse',
          {
            captcha: token,
            imageUrl: this.imageBase64,
            style: this.selectedStyle
          }
        )
        if (success) {
          this.dalle3Prompt = data
          this.generateAvatarImage()
        }
        if (errorMessage) {
          if (errorMessage.includes('429')) {
            return this.$toast.error(
              'You have reached the limit of 5 images per day. Please try again tomorrow.'
            )
          }
          throw new Error(data.errorMessage)
        }
      } catch (error) {
        this.loading = false
        if (error?.response?.status === 429) {
          return this.$toast.error(
            error?.response?.data?.errorMessage ??
            'You have reached the limit of 5 images per day. Please try again tomorrow.'
          )
        }
        return this.$toast.error('Something went wrong. Please try again.')
      }
    },
    async generateAvatarImage () {
      if (!this.dalle3Prompt) {
        return
      }
      try {
        this.loading = true
        const token = await this.$recaptcha.execute('login')
        if (!token) { return this.$toast.open({ type: 'error', message: 'Please accept the reCAPTCHA.' }) }
        const { data, success, errorMessage } = await this.$axios.$post(
          '/tools/avatar-generator/generate',
          {
            prompt: this.dalle3Prompt,
            captcha: token
          }
        )
        if (success) {
          this.portrait = data
        }
        if (errorMessage) {
          if (errorMessage.includes('429')) {
            return this.$toast.error(
              'You have reached the limit of 5 images per day. Please try again tomorrow.'
            )
          }
          throw new Error(data.errorMessage)
        }
        this.loading = false
      } catch (error) {
        this.loading = false
        if (error?.response?.status === 429) {
          return this.$toast.error(
            error?.response?.data?.errorMessage ??
            'You have reached the limit of 5 images per day. Please try again tomorrow.'
          )
        }
        return this.$toast.error('Something went wrong. Please try again.')
      }
    },
    triggerFileInput (fileInput) {
      fileInput.click()
    },
    handleImageUpload (file) {
      if (file) {
        const img = new Image()
        const reader = new FileReader()
        reader.onload = (e) => {
          img.src = e.target.result
          img.onload = () => {
            const width = img.width
            const height = img.height
            const maxDimension = 512
            let newWidth
            let newHeight
            if (width > height) {
              // If width is larger, scale based on width
              newWidth = maxDimension
              newHeight = (height * maxDimension) / width
            } else {
              // If height is larger or they are equal, scale based on height
              newWidth = (width * maxDimension) / height
              newHeight = maxDimension
            }
            const canvas = document.createElement('canvas')
            canvas.width = newWidth
            canvas.height = newHeight
            const ctx = canvas.getContext('2d')
            ctx.drawImage(img, 0, 0, newWidth, newHeight)
            this.imageBase64 = canvas.toDataURL('image/png')
          }
        }

        reader.readAsDataURL(file)
      }
    },
    downloadPortrait () {
      this.$toast.open({
        message: 'Downloading photo. Make sure to accept the download popup in your browser!',
        type: 'info',
        duration: 2000
      })
      const link = document.createElement('a')
      link.href = this.portrait
      link.download = 'headshotpro-avatar.png'
      link.target = '_blank'
      link.click()
      setTimeout(() => {
        this.showUpsellModal = true
      }, 2000)
    },

    async getRateLimit () {
      try {
        const data = await this.$axios.$get('/tools/avatar-generator/rate-limit')
        this.rateLimit = data?.rateLimit ?? 0
      } catch (error) {
        this.rateLimit = 0
      }
    },

    async toCheckout () {
      try {
        const { imageBase64, purchaseEmail, repeatPurchaseEmail } = this
        if (purchaseEmail !== repeatPurchaseEmail) {
          return this.$toast.error('Email addresses do not match.')
        }
        if (!imageBase64) {
          return this.$toast.error('Please upload a photo first.')
        }
        if (!purchaseEmail) {
          return this.$toast.error('Please enter your email address.')
        }
        this.loading = true
        const { success, data, errorMessage } = await this.$axios.$post('/checkout/stripe/create-avatar-session', {
          currency: 'usd',
          // priceId: 'price_1OHn2iImxLpARiYC8IwIKSFF',
          priceId: 'price_1OHoREImxLpARiYCwa26GEgA',
          quantity: 1,
          email: purchaseEmail,
          imageBase64
        })

        if (!success) {
          throw new Error(errorMessage)
        }
        window.location.href = data.url
        this.loading = false
      } catch (err) {
        this.loading = false
        return this.$toast.error('Something went wrong. Please try again.')
      }
    }
  }
}
</script>
