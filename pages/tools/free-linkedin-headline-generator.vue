<template>
  <div class="w-full bg-[#F8FCFF] min-h-3/4">
    <Header class="z-10 relative" />
    <div class="py-32" :style="`${isLoading || result ? 'padding: 32px 0 !important;' : ''}`">
      <div>
        <section class="relative pb-8">
          <ToolsToolBanner
            pretitle="Get a free professional headline for LinkedIn, CVs, and social media profiles."
            subtitle="No email required. 100% free. Done in 30 seconds."
          >
            <template #raw-title>
              <h1 class="text-3xl mt-4 font-bold  tracking-tight text-brand-500 sm:text-4xl xl:text-4xl sm:tracking-tight">
                Free <IconSolidLinkedIn class="h-8 w-8 inline-block" /> LinkedIn Headline Generator
              </h1>
            </template>
            <template #description>
              Upload your resume to our free tool and you’ll get back a strong LinkedIn headline that commands attention to your LinkedIn profile.
            </template>
            <div v-if="!isLoading && !result" class="mt-12">
              <template v-if="mode === 'cv'">
                <div class="flex flex-col justify-center items-center space-y-4 md:flex-row md:space-x-4 md:space-y-0">
                  <label class="cursor-pointer hover:bg-brand-800 focus:ring-brand-200 flex flex-shrink-0 content-center items-center justify-center rounded-md border border-transparent bg-brand-500 px-6 py-3 text-center text-base font-medium text-white shadow-sm transition duration-100 focus:outline-none focus:ring-2 focus:ring-offset-2">
                    <input type="file" accept="application/pdf" class="sr-only" @change="uploadCv">
                    <span>Upload your CV now (PDF)</span>
                  </label>
                  <button type="button" class="text-gray-700" @click="mode = 'manual'">
                    or <span class="underline">write manually</span>
                  </button>
                </div>
              </template>
              <template v-else>
                <div class="flex flex-col items-center justify-center px-4">
                  <textarea
                    v-model="manualText"
                    placeholder="My name is X and I'm a..."
                    class="w-full bg-white rounded-md border border-gray-300 max-w-2xl text-gray-700 p-4"
                    autofocus
                  />
                  <p class="text-sm text-gray-500 mt-2 w-full max-w-2xl">
                    Minimum 100 characters. Currently {{ manualText.trim().length }}
                  </p>
                </div>
                <div class="flex justify-center items-center space-x-8 mt-4">
                  <ButtonPrimary @click="sendText">
                    Generate
                  </ButtonPrimary>
                  <button type="button" class="text-gray-700" @click="mode = 'cv'">
                    or <span class="underline">upload your CV in PDF</span>
                  </button>
                </div>
              </template>
            </div>
          </ToolsToolBanner>
        </section>
      </div>
    </div>

    <div v-if="isLoading || result" class="max-w-5xl mx-auto">
      <div class="w-full mt-4 border border-gray-300 bg-white rounded-xl overflow-hidden">
        <LinkedinHeader />
        <div class="px-10 relative pb-10">
          <LinkedinProfileImage :is-loading="isLoading" />
          <p v-if="!isLoading" class="text-2xl font-bold mt-4">
            {{ result.name || 'Your name' }}
          </p>
          <span v-else class="block h-8 w-full max-w-[10rem] bg-gray-100 rounded mt-4" />
          <p v-if="!isLoading" class="text-base flex items-center justify-start">
            <span>{{ result.headlines[selectedIndex] }}</span>
          </p>
          <span v-else class="h-6 w-full max-w-[16rem] bg-gray-100 rounded mt-2" />
          <div v-if="!isLoading" class="flex justify-start items-center space-x-4 mt-2">
            <ButtonWhite :disabled="selectedIndex === 0" size="xs" @click="selectedIndex = Math.max(0, selectedIndex - 1)">
              Previous
            </ButtonWhite>
            <span>{{ selectedIndex + 1 }}/{{ result.headlines.length }}</span>
            <ButtonWhite :disabled="selectedIndex === result.headlines.length - 1" size="xs" @click="selectedIndex = Math.min(result.headlines.length - 1, selectedIndex + 1)">
              Next
            </ButtonWhite>
          </div>
          <p class="text-sm text-gray-500 mt-4">
            World Wide Web and around · <span class="text-[#2D64BC] font-bold">Contact information</span>
          </p>
          <p class="text-sm text-[#2D64BC] mt-4 font-bold">
            More than 500 contacts
          </p>
          <p class="font-bold text-xl mt-8">
            About
          </p>
          <p class="mt-4">
            Want us to generate your LinkedIn bio. <nuxt-link to="/tools/free-linkedin-bio-generator" class="underline text-blue-500">
              Use this tool.
            </nuxt-link>
          </p>
        </div>
      </div>
      <div v-if="!isLoading" class="flex px-4 justify-center mt-8">
        <ButtonPrimary size="sm" @click="startAgain">
          Start again
        </ButtonPrimary>
      </div>
    </div>
    <div class="bg-white px-6 py-32 lg:px-8">
      <div class="mx-auto max-w-3xl text-base leading-7 text-gray-700">
        <p class="text-base font-medium leading-7 text-teal-600">
          About this tool
        </p>
        <h2 class="mt-4 text-xl font-bold tracking-tight text-brand-500 sm:text-2xl">
          What is the LinkedIn Headline Generator?
        </h2>
        <p>It’s a free tool created by HeadshotPro to help you optimize your LinkedIn profile with AI. Use this tool to update your old LinkedIn Headline and ensure your LinkedIn profile is complete.  </p>
        <h2 class="mt-4 text-xl font-bold tracking-tight text-brand-500 sm:text-2xl">
          How does the LinkedIn Headline Generator work?
        </h2>
        <p>Our AI model analyzes your resume to learn about your work history, skills, and strengths. Then it summarizes all those data points into a professionally written LinkedIn Headline. No more wasting time brainstorming on your own—just use our free tool!</p>
        <h2 class="mt-4 text-xl font-bold tracking-tight text-brand-500 sm:text-2xl">
          How else can I optimize my LinkedIn profile with AI?
        </h2>
        <p>
          HeadshotPro has created several free AI-powered LinkedIn tools you can use to optimize your LinkedIn profile.
          <ul>
            <li>
              <nuxt-link to="/tools/free-headshot-generator" class="underline text-blue-500">
                Free Headshot Generator
              </nuxt-link>
            </li>
            <li>
              <nuxt-link to="/tools/free-linkedin-bio-generator" class="underline text-blue-500">
                LinkedIn Summary Generator
              </nuxt-link>
            </li>
            <li>
              <nuxt-link to="/tools/free-linkedin-profile-generator" class="underline text-blue-500">
                LinkedIn Profile Generator
              </nuxt-link>
            </li>
          </ul>
        <!-- For our complete collection of free tools for LinkedIn, check out our tools page. -->
        </p>
      </div>
    </div>
    <EditorFAQ :items="faq" subtitle="Answers to common questions about our LinkedIn Headline Generator." />
    <MarketingUpgradeYourHeadshots class="py-12 mt-16" />
    <MarketingFooter />
  </div>
</template>

<script>
export default {
  data () {
    return {
      isLoading: false,
      result: null,
      selectedIndex: 0,
      faq: [
        {
          question: 'Do I have to create an account first?',
          answer: 'No! You’ll get your results instantly on the page, no emails required.'
        },
        {
          question: 'Do you keep my resume?',
          answer: 'This free tool is 100% privacy friendly. Your personal data is never stored on our servers.'
        },
        {
          question: 'Where can I get a professional profile picture for LinkedIn?',
          answer: 'HeadshotPro generates professional AI headshots for hundreds of dollars less than a traditional photo shoot. Get your AI headshots here.'
        }
      ],
      mode: 'cv',
      manualText: ''
    }
  },
  head () {
    return {
      title: this.title + ' | HeadshotPro',
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com/tools/free-linkedin-headline-generator'
        }
      ],
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.description
        },
        {
          hid: 'twitter:title',
          name: 'twitter:title',
          content: this.title
        },
        {
          hid: 'twitter:description',
          name: 'twitter:description',
          content: this.description
        },
        {
          hid: 'twitter:image:alt',
          name: 'twitter:image:alt',
          content: this.title
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.title
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.description
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        }
      ]
    }
  },
  computed: {
    title () {
      return '100% Free LinkedIn Headline Generator'
    },
    description () {
      return 'Time to update your LinkedIn headline? Let our AI do it. 100% free. No signup required.'
    },
    manualTextInvalid () {
      return !this.manualText?.trim()?.length || this.manualText?.trim().length < 100
    }
  },
  methods: {
    uploadCv ($event) {
      if (!$event.target.files || $event.target.files.length === 0) {
        return
      }

      const data = new FormData()
      data.append('files', $event.target.files[0])
      this.submit(data)
    },
    sendText () {
      if (this.manualTextInvalid) {
        this.$toast.error('Please enter at least 100 characters')
        return
      }

      this.submit({ text: this.manualText })
    },
    submit (data) {
      this.isLoading = true
      this.result = null
      this.selectedIndex = 0
      this.manualText = ''

      this.$axios.post('/tools/linkedin/generate-headline', data)
        .then((response) => {
          this.isLoading = false
          if (!response.data?.data) {
            this.$toast.error(response.data?.errorMessage || 'Unknown error. Please, try again')
            return
          }
          this.$posthog.capture('$tool:free-linkedin-headline-generator')

          this.result = response.data.data
        })
        .catch((error) => {
          this.$toast.error(error.response?.data?.errorMessage || 'Unknown error. Please, try again')
          this.isLoading = false
        })
    },
    startAgain () {
      this.isLoading = false
      this.result = null
      this.selectedIndex = 0
      this.mode = 'cv'
      this.manualText = ''
    }
  }
}
</script>

  <style>

  </style>
