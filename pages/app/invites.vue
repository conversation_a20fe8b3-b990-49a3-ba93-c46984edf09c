<template>
  <section class="flex min-h-screen items-start justify-center px-0 py-4 text-black md:items-center">
    <div class="flex flex-col items-center justify-center">
      <nuxt-link to="/app">
        <Logo class="w-[180px] text-white" />
      </nuxt-link>
      <p class="mt-0.5 text-xs text-gray-500">
        Create your perfect headshot with HeadshotPro
      </p>

      <Invite />
    </div>
  </section>
</template>

<script>
export default {
  layout: 'protected',
  data () {
    return {
      isLoading: false
    }
  }
}
</script>

<style></style>
