<template>
  <div class="relative bg-gray-100 min-h-screen">
    <div v-if="$route.query.purchase === 'true' || $route.query.payment === 'success'" class="text-xs text-white w-full text-center p-2 bg-green-500">
      Purchase successful! New headshots are being processed and will be available in your account in max 60 minutes.
    </div>
    <!-- <HeaderPersonalApp /> -->
    <div class="w-full bg-white border-b border-gray-200 p-3 px-2 md:px-4 z-50 md:sticky md:top-0">
      <div class="max-w-8xl mx-auto px-4 flex justify-between gap-4">
        <div class="flex items-center justify-start">
          <NuxtLink to="/app">
            <Logo class="w-[140px]" />
          </NuxtLink>
        </div>
        <div class="flex items-center justify-end gap-2">
          <div v-if="isStillGenerating" class="hidden sm:block">
            <span class="text-sm bg-yellow-100 text-yellow-800 px-4 py-[6px] rounded-md border border-yellow-200 block">Generating headshots</span>
          </div>

          <!-- <div v-if="!isStillGenerating && canShareHeadshots" class="hidden sm:block">
            <ButtonGradient size="sm" class="gap-2" @click="shareHeadshots">
              <IconShare class="w-4 h-4" />
              <span>Let your friends vote!</span>
            </ButtonGradient>
          </div> -->

          <div class="hidden sm:block">
            <nuxt-link to="/app" class="inline-flex">
              <ButtonWhite size="sm">
                <span class="hidden md:flex">Back to overview</span>
                <span class="flex md:hidden">Back</span>
              </ButtonWhite>
            </nuxt-link>
          </div>

          <ButtonWhite v-if="item.organization" size="sm" @click="showShareResultsModal = true">
            Share results
          </ButtonWhite>

          <nuxt-link v-if="shouldShowResultPage && shouldShowUpsell" :to="`/app/results/${$route.params.id}/unlock-more?step=1`" class="inline-flex">
            <ButtonPrimary size="sm" class="block sm:hidden relative">
              <span class="hidden md:flex gap-2 items-center">
                <span>Get more photos</span>
                <IconPlus class="!w-4 !h-4 scale-125" />
              </span>
              <span class="flex md:hidden gap-2 items-center">
                <IconPlus class="!w-4 !h-4 scale-125" />
                <span>Get more</span>
              </span>
              <span v-if="item.freeStyles > 0" class="absolute -top-2 -right-2 bg-teal-500 text-white font-bold text-xs px-2 py-1 rounded-full">
                {{ item.freeStyles }}
              </span>
            </ButtonPrimary>
          </nuxt-link>

          <div v-if="!shouldShowResultPage">
            <ButtonPrimary size="sm" :is-disabled="$store.state.results.selected.length === 0" @click="selectKeepers">
              <template v-if="$store.state.results.selected.length > 0">
                <span class="hidden md:block">Select <span class="tabular-nums">{{ $store.state.results.selected.length }}</span> keepers</span>
              </template>
              <template v-else>
                <span class="hidden md:block">Select a keeper first</span>
              </template>
              <span class="md:hidden">Continue</span>
            </ButtonPrimary>
          </div>
        </div>
      </div>
    </div>
    <LoadingWrapper :is-loading="isLoading" :show-spinner="false">
      <template #loading>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 h-screen w-full p-4">
          <template v-for="(_, index) in 24">
            <SkeletonBlock :key="`skeleton-${index}`" height="300px" />
          </template>
        </div>
      </template>
      <template v-if="shouldShowResultPage">
        <GalleryResultPage />
      </template>
      <template v-else>
        <GallerySelectionPage ref="gallerySelectionPage" :initial="true" @fetch="fetchModel()" @shareHeadshots="shareHeadshots" />
      </template>
    </LoadingWrapper>

    <VotingModal v-if="showVotingModal" :share-token="shareToken" :model-id="item._id" @close="showVotingModal = false" />
    <GalleryShareModal v-if="showShareResultsModal" :item="item" @close="showShareResultsModal = false" />
  </div>
</template>

<script>
export default {
  layout: 'protected',
  data () {
    return {
      isLoading: true,
      previewImage: null,
      shareToken: null,
      showShareResultsModal: false
    }
  },
  head () {
    return {
      title: 'Your headshots | HeadshotPro'
    }
  },
  computed: {
    showVotingModal: {
      get () {
        return this.$store.state.results.showVotingModal
      },
      set (value) {
        this.$store.commit('results/SET_SHOW_VOTING_MODAL', value)
      }
    },
    percentageOfNsfw () {
      try {
        const totalNsfw = this.item?.totalNsfw || 0
        const totalPhotos = this.item?.images?.length + totalNsfw
        return totalPhotos > 0 ? (totalNsfw / totalPhotos * 100).toFixed(0) : 0
      } catch (e) {
        return 0
      }
    },
    shouldShowResultPage () {
      return this.favoritePhotos.length > 0
    },
    item () {
      return this.$store.state.results.item
    },
    activeImages () {
      return this.item?.images?.filter(photo => photo?.status === 'active') || []
    },
    unprocessedPhotos () {
      let items = this.activeImages.filter(photo => photo?.likedStatus === 'none') || []
      items = items.filter(item => item?.nsfw !== true)
      return items.filter(item => item !== null)
    },
    favoritePhotos () {
      const items = this.activeImages.filter(photo => photo?.likedStatus === 'favorite' || photo?.likedStatus === 'loved') || []
      return items.filter(item => item !== null)
    },
    isStillGenerating () {
      return this.item?.status === 'generatingHeadshots' && this.item?.images?.length > 0
    }
  },
  watch: {
    showVotingModal (newValue, oldValue) {
      if (newValue === true) {
        this.$nextTick(() => {
          this.shareHeadshots()
        })
      }
    }
  },
  async mounted () {
    if (this.env !== 'development') {
      this.$nextTick(() => {
        document.addEventListener('contextmenu', event => event.preventDefault())
      })
    }
    if (this.$route.query.purchase === 'true') {
      this.$toast.success('Purchase successful! New headshots are being processed and will be available in your account in max 60 minutes.', {
        duration: 5000
      })
    }
    await this.fetchModel()
    const selected = localStorage.getItem('selected')
    if (selected) { this.selected = JSON.parse(selected) }
  },
  methods: {
    async fetchModel () {
      this.$loading.show({ title: 'Loading your headshots...' })
      this.isLoading = true
      const { data } = await this.$axios.$get(`/model/results/${this.$route.params.id}`)
      this.$store.commit('results/SET_ITEM', data)
      this.isLoading = false
      this.$loading.hide()
    },
    refreshResults () {
      this.fetchModel()
    },
    selectKeepers () {
      this.$refs.gallerySelectionPage.selectInitialFavorites()
    },
    async shareHeadshots () {
      try {
        this.$loading.show({ title: 'Creating voting link...' })
        const response = await this.$axios.$post(`/model/voting/${this.item._id}/share`)
        if (!response.success) {
          this.$toast.error('Something went wrong while sharing your headshots. Please try again later.')
          return
        }

        this.shareToken = response.data.shareToken
        this.$store.commit('results/UPDATE_ITEM', { shareToken: response.data.shareToken })
        // this.showVotingModal = true
        // this.$toast.success('Headshots shared!')
        this.$loading.hide()
      } catch (e) {
        this.$loading.hide()
        this.handleError(e)
      }
    }
  }
}
</script>

<style></style>
