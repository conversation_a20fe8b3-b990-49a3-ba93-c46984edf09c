<template>
  <div class="relative bg-gray-100 min-h-screen">
    <div class="w-full bg-white border-b border-gray-200 p-3 px-4 z-50 md:sticky md:top-0">
      <div class="max-w-8xl mx-auto px-4 flex justify-between gap-4">
        <div class="flex items-center justify-start">
          <NuxtLink to="/app">
            <Logo class="w-[140px]" />
          </NuxtLink>
        </div>
        <div class="flex items-center justify-end gap-2">
          <div class="hidden sm:block">
            <nuxt-link :to="`/app/results/${item._id}`" class="inline-flex">
              <ButtonWhite size="sm">
                <span class="hidden md:flex">Back to results</span>
                <span class="flex md:hidden">Back</span>
              </ButtonWhite>
            </nuxt-link>
          </div>
        </div>
      </div>
    </div>
    <LoadingWrapper :is-loading="isLoading">
      <div class="max-w-8xl mx-auto pt-4 md:pt-16 px-4 w-full min-h-screen space-y-8 pb-8" data-testid="gallery-result-page">
        <div class="w-full">
          <h1 class="text-xl sm:text-[25px] font-bold text-brand-500">
            Voting results
          </h1>
          <p class="text-sm sm:text-base text-slate-500 mt-2" data-testid="gallery-result-page-description">
            Here you can see what your friends have voted for. Let's dive in!
          </p>
          <div class="space-y-6 mt-6">
            <Card v-if="uniqueVoters === 0">
              <img src="@/static/undraw/undraw_upvote_frrh.svg" class="w-full max-w-xs h-auto block mx-auto">
              <p class="text-center text-sm text-paragraph mt-4">
                No one has voted yet. Refresh the page to see the results when they come in.
              </p>
            </Card>
            <template v-else>
              <div class="grid grid-cols-1 gap-4 xs:grid-cols-2 xs:gap-8 lg:grid-cols-4">
                <CardWithIcon icon="IconUserGroup" title="Voters" :value="formatNumber(uniqueVoters)" />
                <CardWithIcon icon="IconChartBar" title="Votes" :value="formatNumber(numberOfVotes)" />
                <CardWithIcon icon="IconPhoto" title="Voted images" :value="formatNumber(uniqueVotedImages)" />
                <CardWithIcon icon="IconTrophy" title="Most votes in one image" :value="formatNumber(maxVotesInOneImage)" />
              </div>
            </template>
          </div>

          <GalleryPhotoListCard
            class="mt-8"
            title="Top voted images"
            icon="IconTrophy"
            :photos="votedImages"
            description="These are the images that received the most votes. You can mark them as keepers or duds if you want."
            :icons="['keepers']"
          />

          <GalleryPhotoListCard
            class="mt-8"
            title="Unvoted images"
            icon="IconPhoto"
            :photos="unvotedImages"
            description="These are the images that received no votes. That doesn't mean they're not good, but voters could only choose between 3 photos!"
            :icons="['keepers']"
          />
        </div>
      </div>
    </LoadingWrapper>
  </div>
</template>

<script>

export default {
  layout: 'protected',
  data () {
    return {
      isLoading: true
    }
  },
  head () {
    return {
      title: 'Your voting results | HeadshotPro'
    }
  },
  computed: {
    item () {
      return this.$store.state.results.item
    },
    activeImages () {
      return this.item?.images?.filter(photo => photo?.status === 'active') || []
    },
    uniqueVoters () {
      const voters = {}
      this.activeImages.forEach((photo) => {
        if (photo.voters) {
          photo.voters.forEach((voter) => {
            voters[voter] = true
          })
        }
      })

      return Object.keys(voters).length
    },
    numberOfVotes () {
      return this.activeImages.reduce((acc, photo) => acc + (photo.votes || 0), 0)
    },
    uniqueVotedImages () {
      return this.activeImages.filter(photo => photo.votes > 0).length
    },
    maxVotesInOneImage () {
      return Math.max(...this.activeImages.map(photo => photo.votes || 0))
    },
    votedImages () {
      const images = this.activeImages.filter(photo => photo.votes > 0)
      images.sort((a, b) => b.votes - a.votes)
      return images
    },
    unvotedImages () {
      return this.activeImages.filter(photo => photo.votes === 0 || !photo.votes)
    }
  },
  async mounted () {
    if (this.env !== 'development') {
      this.$nextTick(() => {
        document.addEventListener('contextmenu', event => event.preventDefault())
      })
    }
    await this.fetchModel()
    if (!this.item.shareToken || this.isTeamMember) {
      this.$router.push(`/app/results/${this.item._id}`)
    }
  },
  methods: {
    async fetchModel () {
      this.isLoading = true
      const { data } = await this.$axios.$get(`/model/results/${this.$route.params.id}`)
      this.$store.commit('results/SET_ITEM', data)
      this.isLoading = false
    },
    refreshResults () {
      this.fetchModel()
    }
  }
}
</script>

<style></style>
