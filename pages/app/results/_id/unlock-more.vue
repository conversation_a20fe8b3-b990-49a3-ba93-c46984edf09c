<template>
  <div class="relative bg-gray-100 min-h-screen">
    <div class="w-full bg-white border-b border-gray-200 p-3 px-4 z-50 md:sticky md:top-0">
      <div class="max-w-7xl mx-auto px-4 flex sm:grid sm:grid-cols-2 gap-4">
        <div class="w-[80px] sm:hidden">
          <nuxt-link :to="`/app/results/${item._id}`" class="inline-flex">
            <ButtonWhite size="xs">
              <IconChevron class="w-2 h-2 rotate-180 mr-2" />
              <span>Back</span>
            </ButtonWhite>
          </nuxt-link>
        </div>
        <div class="flex items-center justify-center flex-1 sm:flex-none sm:justify-start">
          <NuxtLink to="/app">
            <Logo class="w-[100px] 2xs:w-[140px]" />
          </NuxtLink>
        </div>
        <div class="flex items-center justify-end space-x-2">
          <HeaderLanguageSelector />
          <div class="hidden sm:block">
            <nuxt-link :to="`/app/results/${item._id}`" class="inline-flex">
              <ButtonWhite size="sm">
                <span class="hidden md:flex">Back to results</span>
                <span class="flex md:hidden">Back</span>
              </ButtonWhite>
            </nuxt-link>
          </div>
          <ButtonPrimary size="sm" class="hidden sm:flex sm:w-[176px]" :is-disabled="!canContinue" @click="handleContinue">
            {{ currentStep === steps[0].id ? 'Continue' : 'Pay now' }}
            <IconPlus v-if="currentStep === steps[0].id" class="!w-3 !h-3 scale-150 ml-2" />
          </ButtonPrimary>
          <ButtonPrimary size="xs" class="sm:hidden" :is-disabled="!canContinue" @click="handleContinue">
            {{ currentStep === steps[0].id ? 'Continue' : 'Pay now' }}
          </ButtonPrimary>
        </div>
      </div>
    </div>
    <LoadingWrapper :is-loading="isLoading">
      <div class="max-w-7xl mx-auto pt-4 pb-20 lg:py-14 px-4">
        <div class="pb-8 lg:pb-10">
          <GallerySteps :steps="steps" :current-step="currentStep" />
        </div>
        <div class="w-full mb-8 space-y-1">
          <h1 class="text-[25px] font-bold leading-tight text-primary-500 text-left lg:text-center" data-testid="upsell-page-heading">
            {{ title }}
          </h1>
          <p class="text-slate-500 text-sm md:text-base text-left lg:max-w-[520px] lg:mx-auto lg:text-center" data-testid="upsell-page-description">
            {{ description }}
          </p>
        </div>

        <UpsellStyleSelection
          v-if="currentStep === steps[0].id"
          ref="styleSelection"
          :tiered-discount="tieredDiscount"
          :item="item"
          @next="confirmStyleSelection"
          @set-can-continue="setCanContinue"
        />
        <UpsellCheckout v-if="currentStep === steps[1].id" ref="checkout" :tiered-discount="tieredDiscount" @back="currentStep = steps[0].id" @set-can-continue="setCanContinue" />
      </div>
    </LoadingWrapper>
    <Popup v-if="isFulfilling">
      <div class="flex justify-center items-center">
        <LoadingSpinnerWhite class="!text-[#34A853] !w-5 !h-5" />
      </div>
      <div class="text-center mt-6">
        <p class="text-lg font-bold text-primary-500">
          Starting your photo shoot
        </p>
        <nuxt-link v-if="showForceRedirect" :to="`/app/results/${$store.state.upsell.basket.modelId}?purchase=true`" class="text-sm font-medium text-[#807D96] underline mt-4">
          Click here to force redirect
        </nuxt-link>
      </div>
    </Popup>
  </div>
</template>

<script>
export default {
  layout: 'protected',
  data () {
    return {
      isLoading: true,
      isFulfilling: false,
      showForceRedirect: false,
      productId: 'upsell',
      steps: [
        {
          label: 'Select additional',
          mobileLabel: 'Select',
          id: 1
        },
        {
          label: 'Checkout',
          mobileLabel: 'Checkout',
          id: 2
        },
        {
          label: 'Receive headshots',
          mobileLabel: 'Receive',
          id: 3
        }
      ],
      currentStep: 1,
      canContinue: false,
      tieredDiscount: {
        ranges: [
          { min: 0, max: 3, discount: 0 },
          { min: 3, max: 5, discount: 20 },
          { min: 5, max: 10, discount: 35 }
        ]
      }
    }
  },
  computed: {
    item () {
      return this.$store.state.results.item
    },
    title () {
      if (this.currentStep === this.steps[1].id) {
        return 'Checkout'
      }

      return 'Unlock additional photos'
    },
    description () {
      if (this.currentStep === this.steps[1].id) {
        return 'Review your order details and confirm your payment. Our system processes new styles within 2 hours since purchase. New remix credits will become available right after payment.'
      }

      return 'You\'ll get 10 headshots for every backdrop and clothing combo you select. Feel free to also change your personal information on the right side of the screen.'
    }
  },
  async mounted () {
    if (!this.shouldShowUpsell) {
      this.$router.push(`/app/results/${this.$route.params.id}`)
      return
    }

    this.$loading.show({ title: 'Loading all styles...' })
    await this.fetchData()
    this.$loading.hide()
    this.$posthog.capture('$upsell:unlock_more')
  },
  methods: {
    async fetchData () {
      try {
        this.isLoading = true
        if (this.$store.state.clothing.length === 0) {
          this.$store.dispatch('getClothing')
        }
        if (this.$store.state.styles.length === 0) {
          this.$store.dispatch('getStyles')
        }
        const { data } = await this.$axios.$get(`/model/results/${this.$route.params.id}`)
        this.$store.commit('results/SET_ITEM', data)
        await this.$store.dispatch('upsell/load', { modelId: this.$route.params.id })

        if (this.$route.query.step === '1') {
          this.currentStep = this.steps[0].id
        } else if (this.$store.state.upsell.basket.items.length > 0) {
          this.currentStep = this.steps[1].id
        } else {
          this.currentStep = this.steps[0].id
        }
      } catch (err) {
        this.handleError(err)
        this.$router.push(`/app/results/${this.$route.params.id}`)
      } finally {
        this.isLoading = false
      }
    },
    confirmStyleSelection () {
      this.$loading.show({ title: 'Redirecting to payment...' })
      const basket = this.$store.state.upsell.basket
      const items = JSON.parse(JSON.stringify(basket.items))
      const newItems = items.filter(item => item.type !== 'style')
      for (const style of this.$store.state.upsell.selectedStyles) {
        newItems.push({
          type: 'style',
          styleId: style.style,
          clothingId: style.clothing,
          productId: 'upsell'
        })
      }

      const appearance = this.$store.getters['upsell/appearance']

      this.$store.dispatch('upsell/updateBasket', {
        items: newItems,
        modelId: this.$route.params.id,
        appearance
      }).then(() => {
        const basketItems = this.$store.state.upsell.basket.items
        const hasOnlyStyles = basketItems.every(item => item.type === 'style')
        this.$posthog.capture('$upsell:selected_styles')
        if (hasOnlyStyles && this.$store.state.results.item.freeStyles >= basketItems.length && basketItems.length > 0) {
          this.confirmWithoutPayment()
          this.$posthog.capture('$upsell:free_styles')
        } else {
          this.currentStep = this.steps[1].id
        }
      }).catch((err) => {
        this.handleError(err)
      }).finally(() => {
        this.$loading.hide()
      })
    },
    confirmWithoutPayment () {
      if (this.isLoading) { return }

      this.isFulfilling = true
      this.isLoading = true
      this.$axios.post(`/upsell/basket/fulfill?modelId=${this.$store.state.upsell.basket.modelId}`, {
        discountCode: this.couponCode
      })
        .then((response) => {
          this.isLoading = false
          setTimeout(() => {
            this.showForceRedirect = true
          }, 1500)
          setTimeout(() => {
            this.isFulfilling = false
            this.$store.dispatch('upsell/load', { modelId: this.$store.state.upsell.basket.modelId })
            this.$router.push(`/app/results/${this.$store.state.upsell.basket.modelId}?purchase=true`)
          }, 5000)
        })
        .catch((error) => {
          this.isLoading = false
          this.isFulfilling = false
          this.handleError(error)
        })
    },
    handleContinue () {
      if (this.$refs.styleSelection) {
        this.$refs.styleSelection.goToNextStep()
      } else if (this.$refs.checkout) {
        this.$posthog.capture('$upsell:checkout')
        this.$refs.checkout.handleContinue()
      }
    },
    setCanContinue (val) {
      this.canContinue = val
    }
  }
}
</script>

<style>
@media screen and (max-width: 767px) {
  .chat-button {
    bottom: 5rem;
  }
}
</style>
