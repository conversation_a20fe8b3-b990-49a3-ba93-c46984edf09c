<template>
  <div class="relative bg-gray-100 min-h-screen">
    <div class="w-full bg-white border-b border-gray-200 p-3 px-4 z-50 md:sticky md:top-0">
      <div class="max-w-8xl mx-auto px-4 flex justify-center gap-4">
        <div class="flex items-center justify-center">
          <NuxtLink to="/">
            <Logo class="w-[140px]" />
          </NuxtLink>
        </div>
      </div>
    </div>
    <LoadingWrapper :is-loading="isLoading" :show-spinner="false" class="min-h-screen">
      <template #loading>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 h-screen w-full p-2 md:p-8">
          <template v-for="(_, index) in 24">
            <SkeletonBlock :key="`skeleton-${index}`" height="300px" />
          </template>
        </div>
      </template>
      <div v-if="model && model._id" class="pb-32 pt-6 lg:pt-16 lg:pb-0 max-w-8xl mx-auto">
        <div class="max-w-[766px] px-4 mx-auto pb-6 lg:pb-16 text-center gap-3 flex flex-col">
          <H3>
            Headshots results for {{ model.title }}
          </H3>
          <Paragraph size="sm">
            These are the headshots we generated for "{{ model.title }}". You can download them by clicking on the "Actions" menu on each photo.
          </Paragraph>
        </div>
        <div class="px-4 w-full min-h-screen flex flex-col lg:flex-row gap-8">
          <Card class="w-full">
            <header class="flex flex-col xs:flex-row gap-8 items-center justify-between">
              <h2 class="text-lg font-medium text-primary-500 items-center hidden xs:flex">
                <IconCamera class="h-6 w-6 text-paragraph mr-[10px]" />
                All photos
              </h2>
            </header>
            <div class="grid grid-cols-2 xs:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 gap-4 lg:mt-5">
              <template v-for="photo in photos">
                <GallerySharePhotoItem
                  :key="photo._id"
                  :photo="photo"
                  :icons="['remixes', 'keepers', 'favorite']"
                  @preview="previewPhoto(photo)"
                />
              </template>
            </div>
          </Card>
        </div>
      </div>
      <div v-else class="h-[calc(100vh/2)] flex items-center justify-center">
        <div class="max-w-2xl mx-auto">
          <Card>
            <div class="flex flex-col items-center justify-center gap-3 px-8 py-4">
              <H3>
                No headshots found
              </H3>
              <Paragraph size="sm">
                No headshots were found for this token.
              </Paragraph>
              <ButtonPrimary size="sm" @click="$router.push('/')">
                Back home
              </ButtonPrimary>
            </div>
          </Card>
        </div>
      </div>
      <Modal v-if="showPreviewModal" max-width="sm:max-w-5xl" data-testid="photo-list-card-preview-modal" @close="closePreviewModal">
        <GalleryPreviewModal :preview-image="previewImage" :preview-image-large="previewImageLarge" :all-photos="photos" />
      </Modal>
    </LoadingWrapper>
  </div>
</template>

<script>
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
import H3 from '@/components/landingpage/common/H3.vue'
export default {
  components: {
    Paragraph,
    H3
  },
  data () {
    return {
      isLoading: true,
      showPreviewModal: false,
      previewImage: null,
      previewImageLarge: null
    }
  },
  computed: {
    token () {
      return this.$route.params.token
    },
    model () {
      return this.$store.state.results.item
    },
    photos () {
      return this.model?.images?.filter(image => !image.nsfw && image.likedStatus !== 'dud') || []
    }
  },
  mounted () {
    this.fetchModel()
  },
  methods: {
    async fetchModel () {
      try {
        this.$loading.show({ title: 'Loading your headshots...' })
        const { data } = await this.$axios.$get(`/model/get-shared-model/${this.token}`)
        this.$store.commit('results/SET_ITEM', data)
        this.$loading.hide()
      } catch (error) {
        this.handleError(error)
      } finally {
        this.isLoading = false
        this.$loading.hide()
      }
    },
    previewPhoto (photo) {
      this.previewImage = photo?.small || photo?.image || photo?.thumbnail
      this.showPreviewModal = true
      this.previewImageLarge = photo?.image
    },
    closePreviewModal () {
      this.showPreviewModal = false
      this.previewImage = null
      this.previewImageLarge = null
    }
  }

}
</script>

<style>

</style>
