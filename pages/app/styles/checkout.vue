<template>
  <section class="flex min-h-screen items-start justify-center bg-gray-50 px-4 py-8 text-black md:items-center">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="mx-auto flex max-w-2xl flex-col items-center justify-center">
        <nuxt-link to="/">
          <Logo class="w-[180px] text-white" />
        </nuxt-link>

        <Card class="mt-6 w-full overflow-hidden rounded-lg shadow md:mt-10 md:w-[450px]">
          <div class="px-4 py-6 sm:px-8 sm:py-10">
            <LoadingWrapper :is-loading="isLoading" title="Loading your items...">
              <div class="w-full space-y-8">
                <template v-if="!isLoggedIn">
                  <div class="flex flex-col">
                    <div class="text-center">
                      <h2 class="text-xl font-medium">
                        Login or sign up to get started
                      </h2>
                      <p class="mb-4 text-sm text-gray-700">
                        You need an account to make a purchase.
                      </p>
                      <!-- <AuthSignupForm class="mt-3" /> -->
                      <AuthGoogleButton class="w-full" />
                      <AuthAppleButton class="mt-2 w-full" />
                      <nuxt-link to="/auth/email">
                        <ButtonWhite class="mt-2 w-full">
                          <IconSolidEnveloppe class="mr-1.5 h-4 w-4 text-gray-700" />
                          <span>Sign in with email</span>
                        </ButtonWhite>
                      </nuxt-link>
                      <p class="mt-4 text-xs text-gray-400">
                        When creating a new account, you agree to the
                        <a href="/legal/terms-and-conditions" target="_blank" class="underline">terms & conditions</a>
                        and
                        <a href="/legal/privacy-policy" target="_blank" class="underline">privacy policy</a>
                        .
                      </p>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <CheckoutSubtotal :price="formatPrice((cartTotal) / 100)" />
                  <div class="mt-2 flex flex-col overflow-hidden rounded-xl border border-gray-200">
                    <div v-for="item in paymentProviders" :key="item.name" class="grid w-full grid-cols-5 items-center border-b border-gray-200 p-4">
                      <div class="col-span-3 flex items-center justify-start space-x-2">
                        <img :src="item.image" class="h-4">
                        <span class="flex-shrink-0 font-medium text-gray-900">{{ item.name }}</span>
                      </div>
                      <div class="col-span-2 flex w-full justify-center md:w-auto md:justify-end">
                        <ButtonWhite v-if="item.name === 'PayPal'" size="sm" class="w-full md:w-auto" @click="toPayPal">
                          Select
                        </ButtonWhite>
                        <ButtonWhite v-else size="sm" class="w-full md:w-auto" @click="toCheckout(item.currency)">
                          Select
                        </ButtonWhite>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </LoadingWrapper>
          </div>
        </Card>
        <!-- <div v-if="!isLoading" class="mx-auto mt-8 flex max-w-2xl flex-col items-center justify-center space-y-3 space-x-4 md:flex-row md:space-y-0">
          <p class="text-sm text-gray-500">
            Got a question? Chat with us and we'll help you out!
          </p>
          <ButtonDark
            size="xs"
            @click="
              $crisp(['do', 'chat:show']);
              $crisp(['do', 'chat:open']);
            "
          >
            <IconSolidChat class="mr-1 h-3 w-3 text-white" />
            <span>Open livechat</span>
          </ButtonDark>
        </div> -->
      </div>
    </div>
  </section>
</template>

<script>
export default {
  layout: 'protected',
  data () {
    return {
      price: 299,
      isLoading: false,
      paymentProviders: [
        { image: require('@/assets/img/logo-credit-card.svg'), name: 'Credit card', currency: 'usd' },
        { image: require('@/assets/img/logo-paypal.svg'), name: 'PayPal', currency: 'usd' },
        // { image: require('@/assets/img/logo-google-pay.svg'), name: 'Google Pay', currency: 'usd' },
        // { image: require('@/assets/img/logo-apple-pay.svg'), name: 'Apple Pay', currency: 'usd' },
        { image: require('@/assets/img/logo-ideal.svg'), name: 'iDEAL', currency: 'eur' },
        { image: require('@/assets/img/logo-bancontact.svg'), name: 'Bancontact', currency: 'eur' },
        { image: require('@/assets/img/logo-giropay.svg'), name: 'Giropay', currency: 'eur' }
        // { image: require('@/assets/img/logo-grab-pay.svg'), name: 'Grab Pay', currency: 'sgd' }
      ]
    }
  },
  head () {
    return {
      title: 'Checkout | HeadshotPro'
    }
  },

  computed: {
    selectedStyles () {
      return this.$store.state.cart.upsell.selectedStyles || null
    },
    modelId () {
      return this.$store.state.cart.upsell.modelId || null
    },
    purchasedUpsell () {
      return this.$store.state.user.purchasedUpsell || false
    },
    cartTotal () {
      const { purchasedUpsell, selectedStyles, price } = this
      const totalPrice = selectedStyles.length * price
      if (!purchasedUpsell) { return totalPrice - 199 }
      return totalPrice
    }
  },
  mounted () {
    if (!this.selectedStyles || this.selectedStyles.length === 0) {
      this.handleError('No styles selected')
      this.$router.push('/app')
    }
    if (!this.modelId) {
      this.handleError('No model selected')
      this.$router.push('/app')
    }
  },
  methods: {
    async toPayPal () {
      this.isLoading = true
      const response = await this.$axios.$post('/checkout/paypal/create-payment-intent-upsell', {
        version: this.$cookies.get('postcrafts_version') || null,
        currency: 'usd',
        styles: this.selectedStyles,
        modelId: this.modelId,
        purchasedUpsell: this.purchasedUpsell,
        rewardfulId: this.userWasReferredViaInternalSystem ? null : (window?.Rewardful?.referral || null)
      })
      if (response && response.success) {
        window.location.href = response.url
        setTimeout(() => {
          this.isLoading = false
        }, 1000)
      } else {
        this.$toast.open({ type: 'error', message: 'Something went wrong while setting up your payment. Please contact support.' })
        this.isLoading = false
      }
    },
    async toCheckout (currency) {
      try {
        this.isLoading = true
        if (!this.selectedStyles || this.selectedStyles.length === 0) {
          throw new Error('No styles selected')
        }
        if (!this.modelId) {
          throw new Error('No model selected')
        }

        const { success, data, errorMessage } = await this.$axios.$post('/checkout/stripe/create-checkout-extra-styles', {
          currency: currency || 'usd',
          styles: this.selectedStyles,
          modelId: this.modelId,
          purchasedUpsell: this.purchasedUpsell,
          rewardfulId: this.userWasReferredViaInternalSystem ? null : (window?.Rewardful?.referral || null)
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        window.location.href = data.url
        setTimeout(() => {
          this.isLoading = false
        }, 5000)
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = false
      }
    }
  }
}
</script>
