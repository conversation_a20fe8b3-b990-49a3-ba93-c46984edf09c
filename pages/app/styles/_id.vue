<template>
  <section class="flex min-h-screen items-start justify-center px-0 py-4 text-black md:items-center">
    <div class="flex flex-col items-center justify-center">
      <nuxt-link to="/app">
        <Logo class="w-[180px] text-white" />
      </nuxt-link>
      <div v-if="$route.query.failed === 'true'" class="text-xs text-red-700 w-full text-center p-2 bg-red-500/20 rounded-md mt-2">
        Purchase failed - please try again.
      </div>
      <LoadingWrapper :is-loading="isLoading" title="Getting styles...">
        <div v-if="!purchasedUpsell" class="bg-green-500/5 mt-4 mb-0 text-center border border-green-500/10 w-full p-2 rounded-md">
          <p class="text-green-900 text-sm">
            <strong>Limited time offer:</strong> Your first additional style is only $1!
          </p>
        </div>
        <template v-if="selectedStyles && selectedStyles.length > 0">
          <StylesCart
            :key="selectedStyles.length"
            :purchased-upsell="purchasedUpsellS"
            :selected-styles="selectedStyles"
            :number-of-items="numberOfItems"
            :is-loading="isLoading"
            @purchase="purchase"
          />
        </template>

        <Styles :selected-styles="selectedStyles" :number-of-items="numberOfItems" @addStyle="addStyle" @removeStyle="removeStyle" />
      </LoadingWrapper>
    </div>
  </section>
</template>

<script>
export default {
  layout: 'protected',
  data () {
    return {
      selectedStyles: [],
      numberOfItems: 10,
      purchasedUpsell: false,
      isLoading: true
    }
  },
  head () {
    return {
      title: 'Purchase additional photos | HeadshotPro'
    }
  },
  created () {
    this.purchasedUpsell = this.$store.state.user?.purchasedUpsell || false
    this.isLoading = false
  },

  methods: {
    addStyle (style) {
      this.selectedStyles.push(style)
    },
    removeStyle (index) {
      this.selectedStyles.splice(index, 1)
    },
    purchase () {
      this.$store.commit('cart/SET_UPSELL_SELECTED_STYLES', this.selectedStyles)
      this.$store.commit('cart/SET_UPSELL_MODEL_ID', this.$route.params.id)
      this.$store.commit('cart/SET_PURCHASED_UPSELL', this.purchasedUpsell)
      this.$router.push('/app/styles/checkout')
    }
  }
}
</script>

<style></style>
