<template>
  <div>
    <AppTitle
      title="Backdrop & Clothing Combos"
      sub-title="Configure backdrop and clothing combinations for your team. Each backdrop must have exactly one male and one female outfit."
      with-mobile-actions
    >
      <ButtonWhite size="sm" @click="showFaq = true">
        <IconSmallQuestionMark class="size-4 text-[#ADB0B5] mr-1.5" />
        FAQ
      </ButtonWhite>
      <ButtonPrimary size="sm" :disabled="!hasValidCombos || isSaving" @click="saveCombos()">
        {{ saveButtonText }}
      </ButtonPrimary>
      <template #mobile>
        <ButtonPrimary size="sm" class="w-full" :disabled="!hasValidCombos || isSaving" @click="saveCombos()">
          {{ saveButtonText }}
        </ButtonPrimary>
      </template>
    </AppTitle>

    <!-- Progress and Requirements -->
    <Card class="mt-6">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-medium text-primary-500">
            Combo Progress
          </h3>
          <p class="text-sm text-gray-600">
            You need exactly 8 backdrop and clothing combinations
          </p>
        </div>
        <div class="text-right">
          <div class="text-2xl font-bold" :class="combos.length >= 8 ? 'text-green-600' : 'text-orange-600'">
            {{ combos.length }}/8
          </div>
          <p class="text-xs text-gray-500">
            {{ combos.length >= 8 ? 'Complete' : `${8 - combos.length} more needed` }}
          </p>
        </div>
      </div>

      <!-- Progress Bar -->
      <div class="mt-4">
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div
            class="h-2 rounded-full transition-all duration-300"
            :class="combos.length >= 8 ? 'bg-green-500' : 'bg-orange-500'"
            :style="{ width: `${Math.min((combos.length / 8) * 100, 100)}%` }"
          />
        </div>
      </div>

      <!-- Status Message -->
      <div class="mt-3">
        <div v-if="combos.length < 8" class="flex items-center text-orange-600">
          <IconExclamation class="w-4 h-4 mr-2" />
          <span class="text-sm">Bulk upload will be disabled until you have 8 combos</span>
        </div>
        <div v-else class="flex items-center text-green-600">
          <IconCheck class="w-4 h-4 mr-2" />
          <span class="text-sm">All team features are now available</span>
        </div>
      </div>
    </Card>

    <!-- Add Combo Section -->
    <Card class="mt-6">
      <div class="flex flex-col space-y-4">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-primary-500">
            Add New Combo
          </h3>
          <div v-if="combos.length >= 8" class="text-sm text-gray-500">
            Maximum reached (8/8)
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Backdrop Selection -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Select Backdrop
            </label>
            <!-- Search Input -->
            <Input
              v-model="searchQueries.backdrop"
              placeholder="Search backdrops..."
              class="mb-2"
              input-class="pl-10"
              size="sm"
            >
              <template #affix>
                <IconSearch class="!size-4 scale-125 text-paragraph absolute left-3 top-1/2 -translate-y-1/2" />
              </template>
            </Input>
            <div class="space-y-2 max-h-48 overflow-y-auto border rounded-lg p-2">
              <div
                v-for="style in filteredAvailableStyles"
                :key="style._id"
                class="flex items-center space-x-3 p-2 rounded cursor-pointer hover:bg-gray-50"
                :class="{ 'bg-blue-50 border border-blue-200': newCombo.style?._id === style._id }"
                @click="selectStyle(style)"
              >
                <ImageDns
                  v-if="style.image?.maleImage"
                  :src="style.image.maleImage"
                  class="w-12 h-12 object-cover rounded"
                  :alt="style.title"
                />
                <div class="flex-1">
                  <p class="text-sm font-medium">
                    {{ style.title }}
                  </p>
                </div>
              </div>
              <div v-if="filteredAvailableStyles.length === 0" class="text-center py-4 text-gray-500 text-sm">
                {{ searchQueries.backdrop ? 'No backdrops match your search' : 'No available backdrops' }}
              </div>
            </div>
          </div>

          <!-- Male Clothing Selection -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Male Outfit
            </label>
            <!-- Search Input -->
            <Input
              v-model="searchQueries.maleClothing"
              placeholder="Search male outfits..."
              class="mb-2"
              input-class="pl-10"
              size="sm"
            >
              <template #affix>
                <IconSearch class="!size-4 scale-125 text-paragraph absolute left-3 top-1/2 -translate-y-1/2" />
              </template>
            </Input>
            <div class="space-y-2 max-h-48 overflow-y-auto border rounded-lg p-2">
              <div
                v-for="clothing in filteredMaleClothing"
                :key="clothing._id"
                class="flex items-center space-x-3 p-2 rounded cursor-pointer hover:bg-gray-50"
                :class="{ 'bg-blue-50 border border-blue-200': newCombo.clothing?.male?._id === clothing._id }"
                @click="selectMaleClothing(clothing)"
              >
                <ImageDns
                  v-if="clothing.images?.male"
                  :src="clothing.images.male"
                  class="w-12 h-12 object-cover rounded"
                  :alt="clothing.type"
                />
                <div class="flex-1">
                  <p class="text-sm font-medium">
                    {{ clothing.type }}
                  </p>
                </div>
              </div>
              <div v-if="filteredMaleClothing.length === 0" class="text-center py-4 text-gray-500 text-sm">
                {{ searchQueries.maleClothing ? 'No male outfits match your search' : 'No male outfits available' }}
              </div>
            </div>
          </div>

          <!-- Female Clothing Selection -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Female Outfit
            </label>
            <!-- Search Input -->
            <Input
              v-model="searchQueries.femaleClothing"
              placeholder="Search female outfits..."
              input-class="pl-10"
              class="mb-2"
              size="sm"
            >
              <template #affix>
                <IconSearch class="!size-4 scale-125 text-paragraph absolute left-3 top-1/2 -translate-y-1/2" />
              </template>
            </Input>
            <div class="space-y-2 max-h-48 overflow-y-auto border rounded-lg p-2">
              <div
                v-for="clothing in filteredFemaleClothing"
                :key="clothing._id"
                class="flex items-center space-x-3 p-2 rounded cursor-pointer hover:bg-gray-50"
                :class="{ 'bg-blue-50 border border-blue-200': newCombo.clothing?.female?._id === clothing._id }"
                @click="selectFemaleClothing(clothing)"
              >
                <ImageDns
                  v-if="clothing.images?.female"
                  :src="clothing.images.female"
                  class="w-12 h-12 object-cover rounded"
                  :alt="clothing.type"
                />
                <div class="flex-1">
                  <p class="text-sm font-medium">
                    {{ clothing.type }}
                  </p>
                </div>
              </div>
              <div v-if="filteredFemaleClothing.length === 0" class="text-center py-4 text-gray-500 text-sm">
                {{ searchQueries.femaleClothing ? 'No female outfits match your search' : 'No female outfits available' }}
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-end">
          <ButtonPrimary
            size="sm"
            :disabled="!canAddCombo || combos.length >= 8"
            @click="addCombo()"
          >
            {{ combos.length >= 8 ? 'Maximum Reached' : 'Add Combo' }}
          </ButtonPrimary>
        </div>
      </div>
    </Card>

    <!-- Current Combos -->
    <Card v-if="combos.length > 0" class="mt-6">
      <div class="flex flex-col space-y-4">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-primary-500">
            Current Combos ({{ combos.length }})
          </h3>
          <ButtonWhite v-if="combos.length > 0" size="sm" @click="clearAllCombos()">
            Clear All
          </ButtonWhite>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div
            v-for="(combo, index) in combos"
            :key="`combo-${index}`"
            class="border rounded-lg p-4 space-y-3 group"
          >
            <!-- Style and Outfits in 3 columns -->
            <div class="grid grid-cols-3 gap-2">
              <!-- Backdrop -->
              <div class="text-center">
                <ImageDns
                  v-if="combo.style?.image?.maleImage"
                  :src="combo.style.image.maleImage"
                  class="w-full aspect-square object-cover rounded mb-2"
                  :alt="combo.style.title"
                />
                <p class="text-xs font-medium">
                  {{ combo.style?.title }}
                </p>
              </div>

              <!-- Male Clothing -->
              <div class="text-center">
                <ImageDns
                  v-if="combo.clothing?.male?.images?.male"
                  :src="combo.clothing.male.images.male"
                  class="w-4/5 mx-auto aspect-square object-cover rounded mb-2"
                  :alt="combo.clothing.male.type"
                />
                <p class="text-xs text-gray-600">
                  {{ combo.clothing?.male?.type }}
                </p>
              </div>

              <!-- Female Clothing -->
              <div class="text-center">
                <ImageDns
                  v-if="combo.clothing?.female?.images?.female"
                  :src="combo.clothing.female.images.female"
                  class="w-4/5 mx-auto aspect-square object-cover rounded mb-2"
                  :alt="combo.clothing.female.type"
                />
                <p class="text-xs text-gray-600">
                  {{ combo.clothing?.female?.type }}
                </p>
              </div>
            </div>

            <div class="justify-center hidden group-hover:flex">
              <ButtonWhite size="xs" @click="removeCombo(index)">
                Remove
              </ButtonWhite>
            </div>
          </div>
        </div>
      </div>
    </Card>

    <!-- FAQ Modal -->
    <FaqModal v-if="showFaq" :faqs="faqItems" @closeModal="showFaq = false" />
  </div>
</template>

<script>
import OrganizationMixin from '@/mixins/OrganizationMixin'

export default {
  mixins: [OrganizationMixin],
  layout: 'teamLead',
  data () {
    return {
      combos: [],
      newCombo: {
        style: null,
        clothing: {
          male: null,
          female: null
        }
      },
      searchQueries: {
        backdrop: '',
        maleClothing: '',
        femaleClothing: ''
      },
      isSaving: false,
      showFaq: false,
      faqItems: [
        {
          question: 'What are backdrop and clothing combos?',
          answer: 'Combos are predefined combinations of backdrops and clothing that your team members will use for their headshots. Each backdrop must have exactly one male outfit and one female outfit.'
        },
        {
          question: 'How many combos do I need?',
          answer: 'You need at least 8 combos to enable bulk upload and other advanced team features. This ensures variety in your team\'s headshots.'
        },
        {
          question: 'Can I change combos after setting them up?',
          answer: 'Yes, you can add, remove, or modify combos at any time. Changes will apply to new headshot generations but won\'t affect existing results.'
        },
        {
          question: 'What happens if I don\'t have enough combos?',
          answer: 'Some team features like bulk upload will be disabled until you have at least 8 combos configured.'
        },
        {
          question: 'How do I search for specific items?',
          answer: 'Use the search boxes above each selector to filter backdrops and clothing by name. This makes it easier to find specific items when you have many options.'
        }
      ]
    }
  },
  head () {
    return {
      title: 'Backdrop & Clothing Combos | HeadshotPro'
    }
  },
  computed: {
    availableStyles () {
      return this.$store.state.styles
    },
    filteredAvailableStyles () {
      if (!this.searchQueries.backdrop.trim()) {
        return this.availableStyles
      }
      const query = this.searchQueries.backdrop.toLowerCase().trim()
      return this.availableStyles.filter(style =>
        style.title?.toLowerCase().includes(query)
      )
    },
    maleClothing () {
      return this.$store.state.clothing.filter(item => item.gender?.includes('male'))
    },
    filteredMaleClothing () {
      if (!this.searchQueries.maleClothing.trim()) {
        return this.maleClothing
      }
      const query = this.searchQueries.maleClothing.toLowerCase().trim()
      return this.maleClothing.filter(clothing =>
        clothing.type?.toLowerCase().includes(query)
      )
    },
    femaleClothing () {
      return this.$store.state.clothing.filter(item => item.gender?.includes('female'))
    },
    filteredFemaleClothing () {
      if (!this.searchQueries.femaleClothing.trim()) {
        return this.femaleClothing
      }
      const query = this.searchQueries.femaleClothing.toLowerCase().trim()
      return this.femaleClothing.filter(clothing =>
        clothing.type?.toLowerCase().includes(query)
      )
    },
    canAddCombo () {
      return this.newCombo.style && this.newCombo.clothing.male && this.newCombo.clothing.female
    },
    hasValidCombos () {
      return this.combos.length >= 8 && this.combos.every(combo =>
        combo.style && combo.clothing?.male && combo.clothing?.female
      )
    },
    saveButtonText () {
      if (this.isSaving) {
        return 'Saving...'
      }
      if (this.combos.length < 8) {
        const remaining = 8 - this.combos.length
        return `Add ${remaining} more combo${remaining > 1 ? 's' : ''}`
      }
      return 'Save changes'
    }
  },
  async mounted () {
    try {
      this.$loading.show({
        title: 'Loading combos...'
      })
      if (!this.completedAllMandatorySteps) {
        this.$toast.error('Please complete your organization setup before configuring combos')
        this.$router.push('/app/admin')
        return
      }

      // Load styles and clothing if not already loaded
      if (this.$store.state.styles.length === 0) {
        await this.$store.dispatch('getStyles')
      }
      if (this.$store.state.clothing.length === 0) {
        await this.$store.dispatch('getClothing')
      }

      // Load existing combos
      await this.loadExistingCombos()
    } catch (error) {
      console.error('Failed to load combos:', error)
    } finally {
      this.$loading.hide()
    }
  },
  methods: {
    async loadExistingCombos () {
      try {
        const response = await this.$axios.$get('/organization/combos')
        if (response.success && response.data.combos) {
          this.combos = response.data.combos
        }
      } catch (error) {
        console.error('Failed to load existing combos:', error)
        // Fallback to store data
        const organization = this.$store.state.organization?.organization
        if (organization?.allowedOptions?.combos) {
          this.combos = JSON.parse(JSON.stringify(organization.allowedOptions.combos))
        }
      }
    },
    selectStyle (style) {
      this.newCombo.style = style
    },
    selectMaleClothing (clothing) {
      if (!this.newCombo.clothing) {
        this.newCombo.clothing = { male: null, female: null }
      }
      this.newCombo.clothing.male = clothing
    },
    selectFemaleClothing (clothing) {
      if (!this.newCombo.clothing) {
        this.newCombo.clothing = { male: null, female: null }
      }
      this.newCombo.clothing.female = clothing
    },
    addCombo () {
      if (!this.canAddCombo) { return }

      // Check if maximum combos reached
      if (this.combos.length >= 8) {
        this.$toast.error('Maximum of 8 combos allowed')
        return
      }

      this.combos.push(JSON.parse(JSON.stringify(this.newCombo)))

      // Reset form
      this.newCombo = {
        style: null,
        clothing: {
          male: null,
          female: null
        }
      }

      this.$toast.success('Combo added successfully')
    },
    removeCombo (index) {
      this.combos.splice(index, 1)
      this.$toast.success('Combo removed')
    },
    clearAllCombos () {
      if (confirm('Are you sure you want to remove all combos? This action cannot be undone.')) {
        this.combos = []
        this.$toast.success('All combos cleared')
      }
    },
    async saveCombos () {
      if (!this.hasValidCombos) {
        this.$toast.error('You need at least 8 valid combos to save')
        return
      }

      this.isSaving = true

      try {
        const payload = {
          combos: this.combos.map(combo => ({
            style: combo.style._id,
            clothing: {
              male: combo.clothing.male._id,
              female: combo.clothing.female._id
            }
          }))
        }

        const response = await this.$axios.$post('/organization/combos', payload)

        if (response.success) {
          // Update the store
          this.$store.commit('organization/SET_ORGANIZATION_COMBOS', this.combos)
          this.$toast.success('Combos saved successfully!')
        } else {
          throw new Error(response.errorMessage || 'Failed to save combos')
        }
      } catch (error) {
        this.$toast.error(`Failed to save combos: ${error.message}`)
      } finally {
        this.isSaving = false
      }
    }
  }
}
</script>

<style></style>
