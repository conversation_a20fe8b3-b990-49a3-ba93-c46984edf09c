<template>
  <div class="relative">
    <LoadingWrapper :is-loading="isLoading">
      <NuxtLink :to="`/app/admin`" class="text-sm text-paragraph font-medium flex items-center gap-2 text-[15px] group mb-4">
        <svg width="6" height="9" viewBox="0 0 6 9" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M4.78032 0.220011C4.92077 0.360637 4.99966 0.551261 4.99966 0.750011C4.99966 0.948762 4.92077 1.13939 4.78032 1.28001L2.06032 4.00001L4.78032 6.72001C4.854 6.78867 4.91311 6.87147 4.9541 6.96347C4.99509 7.05547 5.01713 7.15479 5.01891 7.25549C5.02069 7.35619 5.00216 7.45622 4.96444 7.54961C4.92672 7.643 4.87057 7.72783 4.79935 7.79905C4.72814 7.87027 4.6433 7.92641 4.54991 7.96413C4.45653 8.00185 4.3565 8.02038 4.25579 8.0186C4.15509 8.01683 4.05578 7.99478 3.96378 7.95379C3.87178 7.9128 3.78898 7.8537 3.72032 7.78001L0.470316 4.53001C0.329866 4.38939 0.250977 4.19876 0.250977 4.00001C0.250977 3.80126 0.329866 3.61064 0.470316 3.47001L3.72032 0.220011C3.86094 0.0795608 4.05157 0.000671387 4.25032 0.000671387C4.44907 0.000671387 4.63969 0.0795608 4.78032 0.220011Z" fill="#807D96" />
        </svg>
        <span class="group-hover:underline">Back to results</span>
      </NuxtLink>
      <GalleryResultPage :model="item" theme="segmented" />
    </LoadingWrapper>
    <Modal v-if="showPreviewModal" @close="showPreviewModal = false; previewImage = null;">
      <ImageDns :src="previewImage" class="border-2 border-white rounded-md" />
    </Modal>
  </div>
</template>

<script>
export default {
  layout: 'teamLead',
  data () {
    return {
      item: {},
      previewImage: null,
      showPreviewModal: false,
      isLoading: true,
      showDislikedPhotos: true
    }
  },
  head () {
    return {
      title: 'Results | HeadshotPro'
    }
  },
  computed: {
    favoritePhotos () {
      return this.item?.images?.filter(photo => ['favorite', 'loved'].includes(photo.likedStatus) && photo.status === 'active') || []
    },
    dislikedPhotos () {
      return this.item?.images?.filter(photo => !['favorite', 'loved'].includes(photo.likedStatus) && photo.status === 'active') || []
    }
  },
  async mounted () {
    const { success, data, errorMessage } = await this.$axios.$get(`/model/results/${this.$route.params.id}`)
    if (!success) {
      throw new Error(errorMessage)
    }
    this.$store.commit('results/SET_ITEM', data)
    this.item = data
    this.isLoading = false
    if (this.getLocalStorageSettings('showDislikedPhotos') !== null) {
      this.showDislikedPhotos = this.getLocalStorageSettings('showDislikedPhotos')
    }
  },
  methods: {
    downloadPhoto (photoId) {
      // Get the url from the photoId
      const photo = this.item.images.find(img => img._id === photoId)
      const url = photo?.image
      this.$toast.success('Your image will download shortly. Make sure to accept the download prompt.')
      fetch(url)
        .then(response => response.blob())
        .then((blob) => {
          const url = window.URL.createObjectURL(new Blob([blob]))
          const link = document.createElement('a')
          link.href = url
          const fileType = blob.type.split('/')[1]
          const fileName = `${photoId}-HeadshotPro.${fileType}`
          link.setAttribute('download', fileName)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        })
    }
  }

}
</script>

<style></style>
