<template>
  <div>
    <AppTitle title="API Key" sub-title="API keys are used to authenticate your requests to the API." with-mobile-actions>
      <ButtonPrimary v-if="!apiKey" size="sm" @click="generateApiKey()">
        Generate API Key
      </ButtonPrimary>
      <template v-else>
        <ButtonWhite size="sm" @click="copyApiKey()">
          Copy API Key to clipboard
        </ButtonWhite>
        <ButtonWhiteDelete size="sm" @click="deleteApiKey()">
          Delete API Key
        </ButtonWhiteDelete>
      </template>
      <template #mobile>
        <ButtonPrimary v-if="!apiKey" size="sm" class="w-full" @click="generateApiKey()">
          Generate API Key
        </ButtonPrimary>
        <template v-else>
          <div class="flex gap-2">
            <ButtonWhite size="sm" class="w-full" @click="copyApiKey()">
              Copy API Key
            </ButtonWhite>
            <ButtonWhiteDelete size="sm" class="w-full" @click="deleteApiKey()">
              Delete API Key
            </ButtonWhiteDelete>
          </div>
        </template>
      </template>
    </AppTitle>
    <Card class="mt-6 overflow-x-auto">
      <NuxtContent
        class="prose mx-auto max-w-none"
        :document="article"
      />
    </Card>
  </div>
</template>

<script>
import copy from 'copy-to-clipboard'

export default {
  layout: 'teamLead',
  async asyncData ({ $content, params, error }) {
    try {
      const article = await $content('api', 'index').fetch()

      return {
        article
      }
    } catch (err) {
      error({ statusCode: 404, message: 'Post not found' })
    }
  },
  data () {
    return {
      apiKey: null
    }
  },
  head () {
    return {
      title: 'API | HeadshotPro'
    }
  },
  async mounted () {
    this.$loading.show({
      title: 'Loading API Key...'
    })
    const { data, success, errorMessage } = await this.$axios.$get('/organization/api/key')
    if (success) {
      this.apiKey = data.apiKey
    } else {
      this.$toast.error(errorMessage)
    }
    this.$loading.hide()
  },
  methods: {
    async generateApiKey () {
      this.$loading.show({
        title: 'Generating API Key...'
      })
      const { data, success, errorMessage } = await this.$axios.$post('/organization/api/key')
      if (success) {
        this.apiKey = data.apiKey
      } else {
        this.$toast.error(errorMessage)
      }
      this.$loading.hide()
    },
    async deleteApiKey () {
      this.$loading.show({
        title: 'Deleting API Key...'
      })
      const { success, errorMessage } = await this.$axios.$delete('/organization/api/key')
      if (success) {
        this.apiKey = null
      } else {
        this.$toast.error(errorMessage)
      }
      this.$loading.hide()
    },
    copyApiKey () {
      copy(this.apiKey)
      this.$toast.success('API Key copied to clipboard')
    }
  }
}
</script>

<style></style>
