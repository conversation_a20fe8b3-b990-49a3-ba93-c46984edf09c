<template>
  <OnboardingTeamWrapper :current-group="2" :current-step="3" :centered="true">
    <template #left>
      <ButtonWhite size="sm" @click="goToPreviousStep">
        <IconChevron class="size-4 rotate-180 mr-2" />
        <span>Back</span>
      </ButtonWhite>
    </template>
    <template #actions>
      <ButtonWhite size="sm" @click="goToNextStep">
        <span>Skip this step</span>
        <IconChevron class="size-4 ml-3" />
      </ButtonWhite>
    </template>
    <template #mobile-actions>
      <ButtonPrimary class="w-full" @click="goToNextStep">
        <span>Continue</span>
        <IconChevron class="size-4 ml-3" />
      </ButtonPrimary>
      <div class="text-center mt-1">
        <button type="button" class="text-sm text-paragraph" @click="goToNextStep">
          or <span class="underline">skip this step</span>
        </button>
      </div>
    </template>
    <div class="w-full pt-8 pb-16">
      <div class="max-w-[584px] mx-auto flex flex-col items-center justify-center">
        <PostcheckoutStepper :step="5" :max-steps="7" size="small" active="bg-primary-500" />
        <h1 class="text-lg md:text-[25px] font-bold text-primary-500 mt-6 text-center">
          Bonus: Add a branded profile picture
        </h1>
        <p class="text-slate-500 text-sm md:text-base font-medium mt-1 sm:mt-3 text-center">
          Upload custom branding below so every headshot your team gets back can be downloaded as a branded profile picture.
        </p>
        <div class="pt-7">
          <OnboardingTeamBrandingPicker
            @skip="goToNextStep"
            @next="goToNextStep"
          />
        </div>
      </div>
    </div>
  </OnboardingTeamWrapper>
</template>

<script>
import OrganizationMixin from '../../../../../mixins/OrganizationMixin'

export default {
  mixins: [OrganizationMixin],
  layout: 'protected',
  data () {
    return {

    }
  },
  computed: {
    clothing () {
      return this.$store.state.clothing
    }
  },
  mounted () {
    if (!this.completionData.hasDetails) {
      return this.$router.push('/app/admin/team/new/intro')
    }
    this.$posthog.capture('$organization:onboarding:branding')
  },
  methods: {
    goToNextStep () {
      this.$router.push('/app/admin/team/new/payment')
    },
    goToPreviousStep () {
      this.$router.push('/app/admin/team/new/select-clothing')
    }
  }
}
</script>
