<template>
  <OnboardingTeamWrapper :current-group="0" :current-step="0">
    <LoadingWrapper :is-loading="true">
      <span />
    </LoadingWrapper>
  </OnboardingTeamWrapper>
</template>

<script>
import OrganizationMixin from '../../../../../mixins/OrganizationMixin'

export default {
  mixins: [OrganizationMixin],
  layout: 'protected',
  mounted () {
    if (this.$store.state.organization.organization) {
      if (!this.completionData.hasDetails) {
        return this.$router.push('/app/admin/team/new/intro')
      }

      if (!this.completionData.hasStyles) {
        return this.$router.push('/app/admin/team/new/select-style')
      }

      if (!this.completionData.hasClothing) {
        return this.$router.push('/app/admin/team/new/select-clothing')
      }

      if (!this.completionData.hasBranding) {
        return this.$router.push('/app/admin/team/new/branding')
      }

      if (!this.completionData.hasBilling) {
        return this.$router.push('/app/admin/team/new/payment')
      }

      if (!this.completionData.hasInvitedTeam) {
        return this.$router.push('/app/admin/team/new/invite-team')
      }

      return this.$router.push('/app/admin')
    }

    this.$router.push('/app/admin/team/new/intro')
  }
}
</script>
