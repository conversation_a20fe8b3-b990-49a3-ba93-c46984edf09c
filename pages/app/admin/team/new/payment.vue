<template>
  <OnboardingTeamWrapper :current-group="3" :current-step="1" :with-mobile-actions="true">
    <template #left>
      <ButtonWhite size="sm" @click="goToPreviousStep">
        <IconChevron class="size-4 rotate-180 mr-2" />
        <span>Back</span>
      </ButtonWhite>
    </template>
    <template #actions>
      <HeaderLanguageSelector />
      <ButtonWhite size="sm" @click="skipStep">
        <span>Skip this step</span>
        <IconChevron class="size-4 ml-1" />
      </ButtonWhite>
      <ButtonPrimary size="sm" @click="payNow">
        Pay now
      </ButtonPrimary>
    </template>
    <template #mobile-actions>
      <ButtonPrimary size="sm" class="w-full" @click="payNow">
        Pay now
      </ButtonPrimary>
      <div class="text-center mt-1">
        <button type="button" class="text-sm text-paragraph" @click="skipStep">
          or <span class="underline">skip this step</span>
        </button>
      </div>
    </template>
    <template #mobile-nav-actions>
      <ButtonWhite size="sm" @click="showFaqModal = true">
        <IconSmallQuestionMark class="size-4 mr-2" />
        <span>FAQs</span>
      </ButtonWhite>
    </template>
    <div class="w-full grid grid-cols-1 gap-8 md:grid-cols-5 md:gap-16 justify-start items-start md:pb-4">
      <div class="md:col-span-3">
        <div class="max-w-[575px] mx-auto md:mx-0 flex flex-col items-center justify-center text-center md:block md:text-left">
          <PostcheckoutStepper :step="6" :max-steps="7" size="small" active="bg-primary-500" />
          <h1 class="text-lg md:text-[25px] font-bold text-primary-500 mt-6">
            Pay the way you want
          </h1>
          <p class="text-slate-500 text-sm md:text-base font-medium mt-1 sm:mt-3">
            We support company and teams of any size. Pick the payment method you prefer and we'll get you sorted.
          </p>
        </div>
      </div>
      <div class="hidden col-span-2 md:grid grid-cols-1 gap-2 pt-2">
        <FaqSmallCollapsibleItem v-for="(item, index) in faqItems.slice(0, 2)" :key="'faq-item-' + index" :item="item" />
        <FaqSmallCollapsibleItem :item="viewAllFaqsItem" />
      </div>
    </div>
    <div class="pb-12 pt-2 xs:pt-8 md:pt-0">
      <OnboardingAddCard
        v-if="!completionData.hasBilling"
        ref="addCard"
        title="Purchase seats for your team"
        sub-title="Pre-purchase seats for your team. This is a one-time purchase. One seat equals one photo shoot for one person. When your seats run out, you need to purchase again. Seats never expire."
      />
      <Card v-else inner-class="flex flex-col justify-center items-center text-center gap-6">
        <p class="text-lg text-paragraph flex items-center gap-2">
          <IconCheck class="!w-8 !h-8 text-green-500 flex-shrink-0" />
          You've already purchased seats for your team. Please, go to the dashboard if you want to purchase more seats.
        </p>
        <ButtonPrimary @click="$router.push('/app/admin')">
          Go to dashboard
        </ButtonPrimary>
      </Card>
    </div>
    <FaqModal v-if="showFaqModal" :faqs="faqItems" @closeModal="showFaqModal = false" />
  </OnboardingTeamWrapper>
</template>

<script>
import OrganizationMixin from '../../../../../mixins/OrganizationMixin'

export default {
  mixins: [OrganizationMixin],
  layout: 'protected',
  data () {
    return {
      showFaqModal: false,
      viewAllFaqsItem: {
        question: 'View all frequently asked questions',
        onClick: () => {
          this.showFaqModal = true
        }
      },
      faqItems: [
        {
          question: 'Can I get an invoice?',
          answer: 'Yes, you can get an invoice for your purchase. Once you have completed the purchase, you can access your invoices from the billing dashboard.'
        },
        {
          question: 'Are you SOC 2 compliant?',
          answer: 'Yes, HeadshotPro is SOC 2 Type II compliant. This means our systems and processes related to security, availability, processing integrity, confidentiality, and privacy are independently audited and certified to meet stringent standards.'
        },
        {
          question: 'How does the process work for employees?',
          answer: 'Employees receive a secure link invite from their administrator. They then follow simple, guided steps on our platform to capture or upload about 15 casual selfies using any device (phone or computer) using our easy-to-use selfie guide. Our AI takes it from there, generating their professional headshots. The whole process is done online from anywhere.'
        },
        {
          question: 'How long does it take for an employee to provide their photos?',
          answer: 'The employee part is very quick! Following the guides to capture or upload the required selfies typically takes only 5-10 minutes.'
        },
        {
          question: "Is it easy for employees who aren't tech-savvy?",
          answer: "Absolutely. We've designed the process with clear, step-by-step visual guidance. No technical skills are needed – if an employee can take a selfie, they can easily use HeadshotPro."
        },
        {
          question: 'What do employees need to provide?',
          answer: 'Employees just need to capture or upload about 15 casual selfies showing their face from different angles. Our platform provides simple instructions on the types of photos that work best. No professional photos are required as input.'
        },
        {
          question: 'Will the AI headshots actually look like my team members?',
          answer: "Yes. Our AI technology is specifically designed to generate photorealistic headshots that accurately maintain the individual's recognizable likeness, while optimizing for professional quality, good lighting, and consistency."
        },
        {
          question: 'How does the quality compare to a traditional photographer?',
          answer: 'HeadshotPro delivers studio-quality, high-resolution results comparable to traditional photoshoots. Key advantages include significant cost savings (up to 90%), speed, convenience for remote teams, and guaranteed consistency across your entire organization – things that are often challenging or expensive with traditional methods.'
        },
        {
          question: "What if we're not happy with the results for a team member?",
          answer: "Our goal is your satisfaction. If the initial results aren't perfect, employees often achieve better outcomes by uploading a different set of input selfies following the guides closely. You can give every employee a second chance to get it right free of charge."
        },
        {
          question: 'How many headshot options do we receive per person?',
          answer: 'Each team member gets a wide variety to choose from. The standard package includes 80 unique professional headshot options plus 80 additional profile picture options automatically customized with your branding.'
        },
        {
          question: 'Can we customize the backgrounds and outfits?',
          answer: "Yes, administrators have significant control. You can select preferred styles from our library of 200+ professional backgrounds and outfits. Enterprise plans also allow for fully custom backgrounds. You can restrict the available outfit choices to ensure alignment with your company's brand and dress code."
        },
        {
          question: "How does the 'Branded PFP' feature work?",
          answer: "Within the platform, you can upload your company's brand color or backdrops. Our system then automatically generates 80 profile picture options for each user that incorporate these elements, ensuring perfect brand consistency across platforms like Slack or email."
        },
        {
          question: 'How does HeadshotPro work for remote or distributed teams?',
          answer: 'HeadshotPro is perfectly suited for remote and distributed teams. The entire process is 100% online, allowing team members to create their headshots from anywhere in the world, at any time. This ensures everyone gets a consistent, professional look without the logistical challenges or costs of coordinating in-person photoshoots.'
        },
        {
          question: 'How do we manage onboarding new hires after the initial batch?',
          answer: "It's very straightforward. Administrators can easily invite new team members individually or in groups at any time using email invites, shareable links generated from the management dashboard or using our API. Their headshots will automatically match the styles and branding guidelines set for your organization."
        },
        {
          question: 'How does the bulk discount work? Is it permanent?',
          answer: 'Yes, the bulk discounts are permanent! We offer lifetime tiered discounts based on the *total* number of seats ever purchased for your organization. The more seats you purchase over time (including future top-ups), the higher your discount level becomes, offering savings up to 60%.'
        },
        {
          question: 'What are the payment options available?',
          answer: "We offer flexibility. You can pre-purchase credits for your team (often achieving the best bulk discount), opt for pay-as-you-go where you're charged automatically based on usage, or arrange for payment by invoice for qualified Enterprise plans."
        },
        {
          question: 'How is employee photo data used and stored?',
          answer: 'Protecting your data is our priority. Employee photos are strictly used *only* to generate their headshots. All data, both uploaded photos and generated headshots, is encrypted at rest and in transit using industry-standard security measures. We adhere to strict privacy protocols.'
        },
        {
          question: 'Will my photos be used to train AI models?',
          answer: 'No, absolutely not. Your uploaded photos and the resulting headshots belong to you and are never used to train any other AI models. Furthermore, personal data (input photos) is automatically deleted 30 days after the headshots are generated to ensure privacy.'
        }
      ]
    }
  },
  mounted () {
    this.$posthog.capture('$organization:onboarding:payment')
  },
  methods: {
    skipStep () {
      this.$router.push('/app/admin')
    },
    payNow () {
      this.$refs.addCard.payNow()
    }
  }
}
</script>
