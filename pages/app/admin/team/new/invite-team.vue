<template>
  <OnboardingTeamWrapper :current-group="3" :current-step="2" :centered="true">
    <template #actions>
      <ButtonWhite size="sm" @click="$router.push('/app/admin')">
        <span>Invite team later</span>
        <IconChevron class="size-4 ml-3" />
      </ButtonWhite>
    </template>
    <template #mobile-actions>
      <ButtonPrimary class="w-full" @click="$router.push('/app/admin')">
        Finish onboarding
        <IconArrowRight class="ml-2 size-4" />
      </ButtonPrimary>
      <div class="text-center mt-1">
        <button type="button" class="text-sm text-paragraph" @click="$router.push('/app/admin')">
          or <span class="underline">invite team later</span>
        </button>
      </div>
    </template>
    <div class="w-full pt-8 pb-16">
      <div class="max-w-[584px] mx-auto flex flex-col items-center justify-center">
        <PostcheckoutStepper :step="7" :max-steps="7" size="small" active="bg-primary-500" />
        <h1 class="text-lg sm:text-[25px] font-bold text-primary-500 mt-6 text-center">
          Invite your team
        </h1>
        <p class="text-slate-500 text-sm sm:text-base font-medium mt-1 sm:mt-3 text-center">
          Get your team's headshots started and invite them now.
        </p>
        <div class="pt-7">
          <OnboardingAddTeam />
          <div class="hidden md:flex justify-between mt-6">
            <ButtonWhite size="sm" @click="$router.push('/app/admin')">
              Invite team later
            </ButtonWhite>
            <ButtonPrimary size="sm" @click="$router.push('/app/admin')">
              <span>Finish onboarding</span>
              <IconArrowRight class="w-4 h-4 ml-1.5" />
            </ButtonPrimary>
          </div>
        </div>
      </div>
    </div>
  </OnboardingTeamWrapper>
</template>

<script>
import OrganizationMixin from '../../../../../mixins/OrganizationMixin'

export default {
  mixins: [OrganizationMixin],
  layout: 'protected',
  mounted () {
    if (!this.completionData.hasDetails) {
      this.$router.push('/app/admin/team/new/intro')
    }

    if (!this.completionData.hasBilling) {
      this.$router.push('/app/admin/team/new/payment')
    }
    this.$posthog.capture('$organization:onboarding:invite-team')
  }
}
</script>
