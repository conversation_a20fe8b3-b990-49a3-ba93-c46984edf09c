<template>
  <OnboardingTeamWrapper :current-group="0" :current-step="0">
    <template #left>
      <NuxtLink to="/app/add/" class="block">
        <ButtonWhite size="sm">
          <IconChevron class="size-4 rotate-180 mr-2" />
          <span>Back</span>
        </ButtonWhite>
      </NuxtLink>
    </template>
    <!-- <template #header>
      <TrustpilotRatingSmall class="hidden lg:block" />
    </template> -->
    <template #actions>
      <ButtonPrimary size="sm" @click="goToNextStep">
        <span>Next step</span>
        <IconChevron class="size-4 ml-3" />
      </ButtonPrimary>
    </template>
    <template #mobile-actions>
      <div class="flex justify-center mb-4">
        <TrustpilotRatingSmall />
      </div>
      <ButtonPrimary class="w-full" @click="goToNextStep">
        <span>Next step</span>
        <IconChevron class="size-4 ml-2" />
      </ButtonPrimary>
    </template>
    <div class="w-full grid grid-cols-1 gap-4 items-center pt-4 pb-0 lg:grid-cols-2 lg:gap-[72px]">
      <div>
        <PostcheckoutStepper :step="1" :max-steps="7" size="small" />
        <H2 class="mt-4">
          Get uniform headshots <br>for your entire team
        </H2>
        <Paragraph size="md" class="mt-3">
          Create professional, matching headshots across departments, locations, and time zones. It only takes 10 minutes to set up.
        </Paragraph>
        <!-- <TrustpilotRatingSmall class="hidden lg:block mt-4" /> -->
        <ButtonPrimary class="mt-4 w-full xs:w-auto" @click="goToNextStep">
          <span>Continue to next step</span>
          <IconChevron class="size-4 ml-3" />
        </ButtonPrimary>
      </div>
      <div class="relative">
        <div class="absolute flex-col items-center hidden gap-1 top-[20px] left-[-140px] xl:flex">
          <p
            class="text-sm leading-4 rotate-[-11deg] py-4 text-left font-cursive text-paragraph tracking-[-0.056px]"
          >
            Watch our founder</br>
            demo the product
          </p>
          <svg
            class="w-auto h-10 text-paragraph absolute top-[38px] right-[8px]  transform scale-x-[-1]"
            width="24"
            height="25"
            viewBox="0 0 47 25"
            fill="currentColor"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M4.81908 19.3609C5.9942 20.8884 7.11094 22.3908 8.40942 24.0735C7.38964 24.4198 6.49228 24.0004 5.88212 23.3292C4.05163 21.3158 2.23622 19.2406 0.582761 17.0305C-0.367317 15.7701 0.109152 14.6109 1.46414 14.4767C3.72026 14.218 6.00463 14.1078 8.30406 13.9359C8.43589 13.9243 8.60445 13.8944 8.68448 13.9628C9.01969 14.175 9.30312 14.4671 9.62326 14.741C9.43117 14.9995 9.3257 15.4315 9.08371 15.4981C8.24755 15.7528 7.4048 15.9024 6.58371 16.0953C6.13644 16.2101 5.70423 16.2632 5.22494 16.7734C5.88878 17.0926 6.57427 17.4551 7.23811 17.7742C21.1326 23.7403 35.3256 19.3673 42.709 6.95583C43.6497 5.3481 44.3936 3.62178 45.2542 1.94559C45.5584 1.36018 45.8842 0.818157 46.1883 0.232744C46.3418 0.264552 46.4802 0.358081 46.6337 0.389889C46.7185 0.835425 46.9416 1.37449 46.8145 1.76315C46.4614 3.07764 46.1083 4.39213 45.58 5.63142C40.3708 17.9877 27.8529 24.6143 14.3228 22.0438C11.7352 21.5464 9.17311 20.5486 6.62415 19.7609C6.08367 19.597 5.59497 19.3531 5.06955 19.1275C4.97445 19.1208 4.88593 19.2191 4.81908 19.3609Z" fill="#807D96" />
          </svg>
        </div>
        <!-- <iframe
          class="w-full max-w-xl mx-auto mt-8 rounded-md overflow-hidden"
          height="340"
          src="https://www.youtube.com/embed/daTnpeL_kLI?si=JK8vCOLZ22HLBxai?controls=1&modestbranding=1&rel=0&disablekb=1&fs=0&showinfo=0"
          title="YouTube video player"
          frameborder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          referrerpolicy="strict-origin-when-cross-origin"
          allowfullscreen
        /> -->
        <VideoPlayer
          class="w-full max-w-[550px]"
          src="https://cdn2.headshotpror2.com/demo-b2b-compressed.mp4"
          :placeholder="require('@/assets/img/b2b/app-placeholder.jpg')"
          :autoplay="true"
          :markers="[{ time: 15, label: 'Onboarding' }, { time: 236, label: 'Dashboard' }, { time: 538, label: 'For your team' }]"
        />
      </div>
    </div>
    <div class="w-full">
      <!-- <div class="flex flex-col pt-8 md:pt-0 xl:flex-row justify-between items-start xl:items-center">
        <div>
          <h2 class="text-lg sm:text-[25px] font-bold text-primary-500 mt-6">
            Here's how it works
          </h2>
          <p class="text-sm sm:text-base text-slate-500 font-medium mt-2">
            Getting new headshots for your entire team is a simple 3 step process.
          </p>
        </div>
      </div> -->
      <div class="lg:flex items-center justify-start">
        <div class="my-6 lg:hidden">
          <InputSelect :value="tab" :options="tabs.map(t => ({ title: t.label, value: t.value }))" @input="tab = $event" />
        </div>
        <Tab :with-padding="false" class="hidden lg:block order-first xs:order-none my-6">
          <TabItem v-for="t in tabs" :key="t.value" :active="tab === t.value" data-testid="gallery-selection-page-tab-all" @click="tab = t.value">
            <span>{{ t.label }}</span>
          </TabItem>
        </Tab>
      </div>
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div v-for="card in cards" :key="card.title">
          <Card>
            <h3 class="font-bold text-primary-500">
              {{ card.title }}
            </h3>
            <p class="text-paragraph text-sm font-medium">
              {{ card.subtitle }}
            </p>
            <img v-if="card.image" :src="card.image" class="w-full aspect-[284/182] object-cover rounded-lg border border-black/5 mt-4">
            <div v-else class="aspect-[284/182] w-full bg-gray-100 rounded-lg border border-black/5 mt-4" />
          </Card>
        </div>
      </div>
    </div>
    <div class="py-16">
      <h2 class="text-lg sm:text-[25px] font-bold text-primary-500">
        Frequently asked questions
      </h2>
      <p class="text-sm sm:text-base text-slate-500 font-medium">
        Don't worry, we get them a lot. Here are our most frequently asked questions.
      </p>
      <p class="text-slate-500 text-sm italic mt-1">
        Still having trouble? Our support will gladly
        <button type="button" class="underline italic" @click="openLiveChat">
          help you out
        </button>!
      </p>
      <div class="grid w-full grid-cols-1 mt-8 md:grid-cols-2 gap-3 items-start">
        <FaqCollapsibleItem v-for="(item, index) in faqItems" :key="'faq-item-' + index" :item="item" />
      </div>
    </div>
  </OnboardingTeamWrapper>
</template>

<script>
import H2 from '@/components/landingpage/common/H2.vue'
import VideoPlayer from '@/components/landingpage/common/VideoPlayer.vue'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
export default {
  components: {
    VideoPlayer,
    H2,
    Paragraph
  },
  layout: 'protected',
  data () {
    return {
      tabs: [
        {
          label: 'Choose your styles',
          value: 'choose-styles'
        },
        {
          label: 'Onboard your team',
          value: 'onboard-team'
        },
        {
          label: 'Manage your shoots',
          value: 'manage-shoots'
        }
      ],
      tab: 'choose-styles'
    }
  },
  computed: {
    cards () {
      switch (this.tab) {
        case 'choose-styles':
          return [
            {
              title: 'Setup your branded PFPs',
              subtitle: 'Every headshot includes a more casual, branded profile picture.',
              image: require('@/static/teams/branded-pfps.png')
            },
            {
              title: 'Pick your backdrops',
              // subtitle: 'Pick from a wide list of options or upload your own custom backdrop.' // TODO: Add this back in when we have custom backdrops
              subtitle: 'Pick a background for your headshots from a wide list of options.',
              image: require('@/static/teams/select-backdrops.png')
            },
            {
              title: 'Pick your outfits',
              subtitle: 'Pick from our deep wardrobe of business casual, business formal, and casual professional options.',
              image: require('@/static/teams/select-clothing.png')
            }
          ]
        case 'onboard-team':
          return [
            {
              title: 'Quick 5-min employee process',
              subtitle: 'Employees complete their part easily in just minutes using simple, guided steps on any device, anywhere.',
              image: require('@/assets/img/b2b/feature-10.png')
            },
            {
              title: 'Invite your team members',
              subtitle: 'Onboard your team in just a few clicks by inviting them via email or via a custom join URL.',
              image: require('@/static/teams/invite.png')
            },
            {
              title: 'Flexible payment options',
              subtitle: "Pay via credit card or bank transfer. We'll make it easy for you to add us as a vendor.",
              image: require('@/static/teams/payment-options.png')
            }
          ]
        case 'manage-shoots':
          return [
            {
              title: "Track everyone's progress",
              subtitle: "See who has completed their headshots, who's in progress, and who hasn't started yet.",
              image: require('@/static/teams/track-progress.png')
            },
            {
              title: 'Automatic reminders',
              subtitle: 'Send automatic reminders to your team members to get their headshots done.',
              image: require('@/static/teams/automatic-reminders.png')
            },
            {
              title: "View everybody's results",
              subtitle: 'Check the results of your team members and help them pick their keepers.',
              image: require('@/static/teams/view-results.png')
            }
          ]
        default:
          return []
      }
    },
    faqItems () {
      const firstItem = {
        question: 'What discounts are available for teams?',
        answer: ''
      }

      for (const option of this.teamDiscountOptions) {
        if (option.discount > 0) {
          firstItem.answer += `- ${option.label} team members: ${option.discount}% off<br/>`
        }
      }

      firstItem.answer += '<br/><br/>Your discount will automatically be applied at checkout.'

      return [
        firstItem,
        {
          question: 'Does my discount apply to future credit purchases?',
          answer: 'Yes. The discount you earn when you place your initial order will be applied to all future credit purchases you make with HeadshotPro.'
        },
        {
          question: 'When does my discount expire?',
          answer: 'Your team’s bulk discount never expires.'
        },
        {
          question: 'Can I preview my headshots before I pay?',
          answer: 'Because HeadshotPro uses the very latest AI technology, we must pay upfront for every photo we create for our customers.<br/><br/>Long story short, this is why we require payment before you see your headshots: because if we don\'t pay to create your photos, your photos don\'t exist!'
        },
        {
          question: 'How long does the entire process take?',
          answer: 'Each of your team member’s headshots will be fully completed within 2 hours or less from the time they submit their photos to HeadshotPro via the easy onboarding process.<br/><br/>The entire onboarding process typically takes between 5 and 15 minutes for each team member. It’s suitable for all ages and abilities, being designed for users with no prior experience using AI applications.<br/><br/>The process of creating a new Team account for your company takes about 10-20 minutes. This includes the time required to select which backdrop and clothing items you’d like shown in your team’s headshots.'
        },
        {
          question: 'What does the start-to-finish process look like?',
          answer: 'HeadshotPro is a simple 3-step process:<br/><br/>1. Create an account and pay for AI headshot package of your choice (5-10 minutes)<br/>2. Upload your input photos (5-10 minutes)<br/>3. Our AI creates your professional headshots and emails you when it\'s done (2 hours or less)'
        },
        {
          question: 'What steps do team members have to take?',
          answer: 'Each team member, on their own time:<br/><br/>1. Accepts the invite sent to their email (or via join URL) shared by their Team Leader (30 seconds)<br/>2. Selects their clothing and backdrop combinations from the pool of allowed styles selected by their Team Leader (5 minutes)<br/>3. Uploads their selfies by conducting a guided photoshoot on the spot (recommended) or by uploading existing selfies they\'ve already taken (5-10 minutes).<br/><br/>After completing this 3-step process, 80 professional headshots will be created within 2 hours or less!'
        },
        {
          question: 'How much visibility do Team Leaders have over their team’s headshots?',
          answer: 'Team Leaders have access to a dashboard that shows exactly what stage each of their invited team members are in.<br/><br/>Once a team member has completed their onboarding, Team Leaders have full visibility into their team member’s completed AI headshots. Favorites selected by team members will be highlighted for greater visibility.'
        },
        {
          question: 'What if one of my team members doesn\'t like their headshots?',
          answer: 'Each team member is eligible to 1 free retry if they were unable to find at least 1 profile-worthy headshot in their order. Retries are issued by Team Leaders.'
        },
        {
          question: 'Is HeadshotPro SOC 2 certified?',
          answer: 'HeadshotPro is in the process of becoming SOC 2 certified. Check on our current progress via <a href="https://trust.oneleet.com/headshotpro?tab=overview" target="_blank" rel="noopener noreferrer" class="underline text-primary-500">our Trust Center</a>.'
        }
      ]
    }
  },
  mounted () {
    this.$posthog.capture('$organization:onboarding:intro')
    // Temporary redirection until we have the content ready
    // this.goToNextStep()
  },
  methods: {
    goToNextStep () {
      this.$router.push('/app/admin/team/new/company-details')
    }
  }
}
</script>
