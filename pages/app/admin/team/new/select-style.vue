<template>
  <OnboardingTeamWrapper :current-group="2" :current-step="1" mobile-padding="pb-32" :centered="totalNumberOfStyles >= maxStyles || wantsToContinue">
    <template #left>
      <ButtonWhite size="sm" @click="goToPreviousStep">
        <IconChevron class="size-4 rotate-180 mr-2" />
        <span>Back</span>
      </ButtonWhite>
    </template>
    <template #actions>
      <ButtonWhite size="sm" @click="goToNextStep">
        <span>Skip this step</span>
      </ButtonWhite>
      <ButtonPrimary size="sm" @click="clickContinue">
        <span>Continue</span>
        <IconChevron class="size-4 ml-3" />
      </ButtonPrimary>
    </template>
    <template #mobile-actions>
      <ButtonPrimary class="w-full xs:hidden" size="sm" @click="clickContinue">
        <span v-if="totalNumberOfStyles < maxStyles">Continue with {{ totalNumberOfStyles }}/{{ maxStyles }} backdrops</span>
        <span v-else>Continue</span>
        <IconChevron class="size-4 ml-3" />
      </ButtonPrimary>
      <ButtonPrimary class="w-full hidden xs:flex" @click="clickContinue">
        <span v-if="totalNumberOfStyles < maxStyles">Continue with {{ totalNumberOfStyles }}/{{ maxStyles }} backdrops</span>
        <span v-else>Continue</span>
        <IconChevron class="size-4 ml-3" />
      </ButtonPrimary>
      <div class="text-center mt-1">
        <button type="button" class="text-sm text-paragraph" @click="goToNextStep">
          or <span class="underline">skip this step</span>
        </button>
      </div>
    </template>
    <template #mobile-nav-actions>
      <ButtonWhite size="sm" @click="showFaqModal = true">
        <IconSmallQuestionMark class="size-4 mr-2" />
        <span>FAQs</span>
      </ButtonWhite>
    </template>
    <div v-if="totalNumberOfStyles < maxStyles && !wantsToContinue" class="w-full grid grid-cols-1 gap-8 md:grid-cols-2 md:gap-16 justify-start items-start pt-8 md:pb-16">
      <div>
        <div class="max-w-[520px] mx-auto md:mx-0 flex flex-col items-center justify-center text-center md:block md:text-left">
          <PostcheckoutStepper :step="3" :max-steps="7" size="small" active="bg-primary-500" />
          <h1 class="text-lg md:text-[25px] font-bold text-primary-500 mt-6">
            Select your team's backdrops
          </h1>
          <p class="text-sm md:text-base text-slate-500 font-medium mt-1 sm:mt-3">
            Pick 8 backdrops for your team members below. Everybody gets 10 photos per backdrop.
          </p>
        </div>
      </div>
      <div class="hidden md:grid grid-cols-1 gap-2 pt-2">
        <FaqSmallCollapsibleItem v-for="(item, index) in slicedFaqItems" :key="'faq-item-' + index" :item="item" />
        <FaqSmallCollapsibleItem v-if="faqItems.length > 3" :item="viewAllFaqsItem" />
      </div>
    </div>
    <div v-else class="w-full flex justify-center pt-8 pb-16">
      <div class="max-w-[520px] mx-auto md:mx-0 flex flex-col items-center justify-center text-center md:block md:text-left">
        <PostcheckoutStepper :step="3" :max-steps="7" size="small" class="justify-center" />
        <h1 class="text-lg md:text-[25px] font-bold text-primary-500 mt-6 text-center">
          Great choices! Ready to move on?
        </h1>
        <p class="text-sm md:text-base font-medium mt-3 text-center">
          We're ready to create your headshots now! Double check your selections below then click the button to move on to the next step.
        </p>
      </div>
    </div>
    <div class="mt-8 md:mt-0">
      <OnboardingTeamStylePicker
        ref="stylePicker"
        :styles="styles"
        :preselected-options="preselectedOptions"
        :selected-styles="selectedStyles"
        :max-selected-styles="maxStyles"
        :min-selected-styles="minStyles"
        :wants-to-continue="wantsToContinue"
        @select-style="selectStyle"
        @remove-style="removeStyle"
        @wants-to-continue="wantsToContinue = $event"
        @next="goToNextStep"
      />
    </div>
    <FaqModal v-if="showFaqModal" :faqs="faqItems" @closeModal="showFaqModal = false" />
  </OnboardingTeamWrapper>
</template>

<script>
import OrganizationMixin from '../../../../../mixins/OrganizationMixin'

export default {
  mixins: [OrganizationMixin],
  layout: 'protected',
  data () {
    return {
      maxStyles: 8,
      minStyles: 3,
      selectedStyles: [],
      wantsToContinue: false,
      showFaqModal: false,
      viewAllFaqsItem: {
        question: 'View all frequently asked questions',
        onClick: () => {
          this.showFaqModal = true
        }
      },
      faqItems: [
        {
          question: 'Can we select the same backdrop multiple times?',
          answer: 'Yes! Just click "Select again".'
        },
        {
          question: 'Can I add my own backdrop?',
          answer: 'Contact our sales team at <strong><EMAIL></strong> with your desired backdrop or outfit example. We\'ll create a custom solution tailored to your needs to ensure you get the perfect professional headshot.'
        },
        {
          question: 'How much will this cost?',
          answer: 'You\'re a few clicks away from finding out! Keep going or click here to jump to checkout.'
        }
      ]
    }
  },
  computed: {
    styles () {
      const styles = this.$store.state.styles
      const userOrganization = this.$store.state?.organization?.organization?._id
      const filteredStyles = styles.filter((style) => {
        if (!style?.organizationIds) { return true }
        if (style?.organizationIds?.length === 0) { return true }
        if (style?.organizationIds?.includes(userOrganization)) { return true }
        return false
      })
      return filteredStyles
    },
    preselectedOptions () {
      const preselected = this.$store.state.organization.preselectedOptions?.styles || []

      return this.styles.filter(style => preselected.includes(style._id))
    },
    totalNumberOfStyles () {
      return this.preselectedOptions.length + this.selectedStyles.length
    },
    slicedFaqItems () {
      if (this.faqItems.length <= 3) {
        return this.faqItems
      }

      return this.faqItems.slice(0, 2)
    }
  },
  mounted () {
    if (!this.completionData.hasDetails) {
      return this.$router.push('/app/admin/team/new/intro')
    }

    if (this.styles.length === 0) {
      this.$store.dispatch('getStyles')
        .then(() => {
          this.restoreState()
        })
    }
    this.$posthog.capture('$organization:onboarding:select-style')

    this.restoreState()

    this.$store.dispatch('organization/fetchPreselectedOptions')

    window.addEventListener('onboarding:beforeNavigate', this.handleBeforeNavigate)
  },
  beforeDestroy () {
    window.removeEventListener('onboarding:beforeNavigate', this.handleBeforeNavigate)
  },
  methods: {
    handleBeforeNavigate (event) {
      this.$refs.stylePicker.goToNextStep(true)
    },
    clickContinue () {
      this.$refs.stylePicker.goToNextStep()
    },
    restoreState () {
      if (this.$store.state.organization.organization?.style?.length > 0 || this.$store.state.organization.organization?.allowedOptions?.styles?.length > 0) {
        const styles = []
        const allStyles = this.$store.state?.organization?.organization?.allowedOptions?.styles?.length > 0 ? this.$store.state?.organization?.organization?.allowedOptions?.styles : (this.$store.state?.organization?.organization?.style || [])
        for (const style of allStyles) {
          const styleObject = this.styles.find(s => s._id === style._id || s._id === style)
          if (styleObject) {
            styles.push(styleObject)
          }
        }

        this.selectedStyles = styles
      }
    },
    selectStyle (style) {
      this.selectedStyles.push(style)
    },
    removeStyle (index) {
      this.selectedStyles.splice(index, 1)
      this.wantsToContinue = false
    },
    goToNextStep () {
      this.$router.push('/app/admin/team/new/select-clothing')
    },
    goToPreviousStep () {
      this.$router.push('/app/admin/team/new/company-details')
    }
  }
}
</script>
