<template>
  <OnboardingTeamWrapper :current-group="1" :current-step="1" :with-mobile-actions="false" :centered="true">
    <template #left>
      <ButtonWhite size="sm" @click="goToPreviousStep">
        <IconChevron class="size-4 rotate-180 mr-2" />
        <span>Back</span>
      </ButtonWhite>
    </template>
    <template #actions>
      <ButtonPrimary size="sm" @click="clickContinue">
        <span>Continue</span>
      </ButtonPrimary>
    </template>
    <div class="w-full flex justify-center items-center pt-8 pb-16">
      <div class="w-full max-w-[530px]">
        <div class="flex justify-center">
          <PostcheckoutStepper :step="2" :max-steps="7" size="small" active="bg-primary-500" />
        </div>
        <h1 class="text-lg sm:text-[25px] font-bold text-primary-500 mt-6 text-center">
          Tell us about your company
        </h1>
        <p class="text-sm sm:text-base text-slate-500 font-medium mt-1 sm:mt-4 text-center mb-8">
          Share a few key details about your team or company before you choose your styles.
        </p>
        <OnboardingOrganization ref="organization" @next="next" />
      </div>
    </div>
  </OnboardingTeamWrapper>
</template>

<script>
export default {
  layout: 'protected',
  mounted () {
    this.$posthog.capture('$organization:onboarding:company-details')
  },
  methods: {
    clickContinue () {
      this.$refs.organization.saveOrganization()
    },
    next () {
      this.$router.push('/app/admin/team/new/select-style')
    },
    goToPreviousStep () {
      this.$router.push('/app/admin/team/new/intro')
    }
  }
}
</script>
