<template>
  <div>
    <AppTitle title="Invoices & Quotes" sub-title="This is where you manage your invoices and quotes." />
    <Card class="mt-6">
      <header class="pb-4 flex justify-between items-center">
        <div>
          <h2 class="text-lg font-bold text-primary-500">
            Invoices & Quotes
          </h2>
          <p class="font-medium text-slate-500">
            Your invoices and quotes are here.
          </p>
        </div>
        <div>
          <ButtonDropdown
            :title="filter === 'all' ? 'Filters' : 'Filters (1)'"
            size="sm"
            icon="IconFilter"
            icon-position="before"
            :items="filterItems"
            @select="filter = $event"
          />
        </div>
      </header>
      <div v-if="filteredInvoices.length > 0" class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
          <div class="flex w-full">
            <Table :head="['Issued', 'Description', 'Amount', 'Status', 'Actions']" class="w-full">
              <template v-for="invoice in filteredInvoices">
                <TableRow v-if="invoice.amountTotal" :key="invoice.id">
                  <TableItem>
                    {{ formatDate(invoice.createdOn) }}
                  </TableItem>
                  <TableItem>
                    {{ invoice?.price?.name || `${invoice._id}` }}
                  </TableItem>
                  <TableItem>
                    <span
                      :class="{
                        'line-through': invoice.status === 'refunded',
                        'font-medium text-primary-500': invoice.status !== 'refunded'
                      }"
                    >
                      {{ formatPrice(invoice.amountTotal/100, invoice?.currency?.toLowerCase() || 'usd', 2, false) }}
                    </span>
                    <span v-if="invoice.status === 'refunded'">{{ formatPrice(Math.max(0, (invoice.amountTotal - (invoice.amountRefunded || 0)))/100, invoice?.currency?.toLowerCase() || 'usd', 2, false) }}</span>
                  </TableItem>
                  <TableItem>
                    <InvoiceStatusBadge :invoice="invoice" />
                  </TableItem>
                  <TableItem class="flex justify-end">
                    <nuxt-link v-if="isQuoted(invoice)" :to="`/invoice/${invoice.organization}/${invoice._id}`" target="_blank">
                      <ButtonWhite size="xs">
                        View quote
                      </ButtonWhite>
                    </nuxt-link>
                    <nuxt-link v-else :to="`/app/admin/invoice?id=${invoice._id}`">
                      <ButtonWhite size="xs">
                        View invoice
                      </ButtonWhite>
                    </nuxt-link>
                  </TableItem>
                </TableRow>
              </template>
            </Table>
          </div>
        </div>
      </div>
      <div v-else class="flex justify-center items-center p-6">
        <p v-if="filter === 'all'" class="text-sm text-slate-500">
          There are no invoices or quotes yet. Make your first payment or request a quote to get started.
        </p>
        <p v-else class="text-sm text-slate-500">
          There are no invoices or quotes that match your filter.
        </p>
      </div>
    </Card>
  </div>
</template>

<script>
export default {
  layout: 'teamLead',
  data () {
    return {
      invoices: [],
      isLoading: true,
      filter: 'all'
    }
  },
  head () {
    return {
      title: 'Invoices & Quotes | HeadshotPro'
    }
  },
  computed: {
    filterItems () {
      return [
        {
          title: 'All',
          value: 'all',
          icon: this.filter === 'all' ? 'IconCheck' : null
        },
        {
          title: 'Quotes',
          value: 'quoted',
          filter: invoice => this.isQuoted(invoice),
          icon: this.filter === 'quoted' ? 'IconCheck' : null
        },
        {
          title: 'Paid',
          value: 'paid',
          filter: invoice => this.isPaid(invoice),
          icon: this.filter === 'paid' ? 'IconCheck' : null
        },
        {
          title: 'Refunded',
          value: 'refunded',
          filter: invoice => this.isRefunded(invoice),
          icon: this.filter === 'refunded' ? 'IconCheck' : null
        }
      ]
    },
    filteredInvoices () {
      const filterItem = this.filterItems.find(item => item.value === this.filter)
      if (!filterItem?.filter) {
        return this.invoices
      }

      return this.invoices.filter(invoice => filterItem.filter(invoice))
    }
  },
  async mounted () {
    this.$loading.show({
      title: 'Loading invoices and quotes...'
    })
    this.invoices = await this.$axios.$get('/organization/invoice/all')
    this.$loading.hide()
  },
  methods: {
    isPaid (invoice) {
      return invoice.status === 'complete'
    },
    isRefunded (invoice) {
      return invoice.status === 'refunded'
    },
    isQuoted (invoice) {
      return invoice.status === 'quoted'
    },
    badgeText (invoice) {
      if (this.isPaid(invoice)) {
        return 'Paid'
      }

      if (this.isRefunded(invoice)) {
        return 'Refunded'
      }

      return this.slugToTitle(invoice.status)
    }
  }
}
</script>

<style></style>
