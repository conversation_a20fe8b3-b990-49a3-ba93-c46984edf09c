<template>
  <div>
    <AppTitle
      title="Bulk Upload"
      sub-title="Upload multiple ZIP files to quickly create models for your team members."
      with-mobile-actions
    >
      <ButtonWhite size="sm" @click="showFaq = true">
        <IconSmallQuestionMark class="size-4 text-[#ADB0B5] mr-1.5" />
        FAQ
      </ButtonWhite>
      <template #mobile>
        <ButtonWhite size="sm" class="w-full" @click="showFaq = true">
          <IconSmallQuestionMark class="size-4 text-[#ADB0B5] mr-1.5" />
          FAQ
        </ButtonWhite>
      </template>
    </AppTitle>

    <!-- Warning about quality check bypass -->
    <AlertWarning
      title="Quality Check Bypass"
      description="This system bypasses quality checks. Results might disappoint if bad quality photos are uploaded. Please ensure all photos meet our quality standards before uploading. Usage is at your own risk."
      class="mt-6"
    />

    <!-- Combo Selection (if not set) -->
    <Card v-if="!hasValidCombos" class="mt-6">
      <div class="flex flex-col space-y-4">
        <h3 class="text-lg font-medium text-primary-500">
          Setup Required
        </h3>
        <p class="text-sm text-gray-600">
          Before you can use bulk upload, you need to configure at least 8 backdrop and clothing combinations.
        </p>
        <div class="flex gap-3">
          <ButtonPrimary size="sm" @click="$router.push('/app/admin/combos')">
            Configure Combos
          </ButtonPrimary>
        </div>
      </div>
    </Card>

    <!-- Upload Interface -->
    <Card v-else class="mt-6">
      <div class="flex flex-col space-y-6">
        <!-- Upload Area -->
        <div class="flex flex-col space-y-4">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-primary-500">
              Upload ZIP Files
            </h3>
            <ButtonWhite size="sm" @click="$router.push('/app/admin/combos')">
              <IconCog6Tooth class="w-4 h-4 mr-1.5" />
              Configure Combos
            </ButtonWhite>
          </div>
          <p class="text-sm text-gray-600">
            Upload up to 20 ZIP files containing training photos (minimum 9 photos per ZIP, 15+ recommended). Each ZIP will create one model.
          </p>

          <div
            class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer transition-colors"
            :class="{
              'border-primary bg-primary/5': isDragging,
              'hover:border-gray-400': !isDragging && !isProcessing,
              'border-gray-400 bg-gray-50 cursor-not-allowed': isProcessing
            }"
            @click="!isProcessing && $refs.fileInput.click()"
            @dragenter.prevent="!isProcessing && (isDragging = true)"
            @dragover.prevent="!isProcessing && (isDragging = true)"
            @dragleave.prevent="isDragging = false"
            @drop.prevent="!isProcessing && handleFileDrop"
          >
            <div class="flex flex-col items-center justify-center">
              <div v-if="isProcessing" class="mb-4">
                <LoadingSpinnerSingle class="w-8 h-8" />
              </div>
              <IconCustomCloudUpload
                v-else
                :circle-class="isDragging ? 'animate-bounce' : 'hover:animate-bounce'"
                class="mb-4"
              />
              <h3
                class="text-base font-medium"
                :class="{
                  'text-primary': isDragging,
                  'text-gray-900': !isDragging && !isProcessing,
                  'text-gray-500': isProcessing
                }"
              >
                {{ processingStatus || (isProcessing ? 'Processing files...' : isDragging ? 'Drop your ZIP files here' : 'Click to upload ZIP files or drag and drop') }}
              </h3>
              <p class="mt-1 text-sm text-gray-500">
                {{ isProcessing ? 'Please wait while we extract and process your images' : 'ZIP files only, max 20 files at once' }}
              </p>
            </div>
          </div>

          <input
            ref="fileInput"
            type="file"
            class="hidden"
            accept=".zip"
            multiple
            :disabled="isProcessing"
            @change="handleFileUpload"
          >
        </div>

        <!-- Upload Progress -->
        <div v-if="uploadProgress.total > 0" class="bg-gray-50 rounded-lg p-4">
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center gap-2">
              <LoadingSpinnerSingle class="w-4 h-4" />
              <span class="text-sm font-medium text-gray-700">{{ processingStatus || 'Processing files...' }}</span>
            </div>
            <span class="text-sm text-gray-500">{{ uploadProgress.completed }}/{{ uploadProgress.total }}</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div
              class="bg-brand-500 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${Math.max(mainProgressPercentage, 2)}%` }"
            />
          </div>

          <div v-if="currentFileProgress.total > 0" class="mt-2">
            <div class="flex items-center justify-between mb-1">
              <span class="text-xs text-gray-600">{{ currentFileProgress.status }}</span>
              <span class="text-xs text-gray-500">{{ currentFileProgress.completed }}/{{ currentFileProgress.total }}</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-1">
              <div
                class="bg-brand-500/60 h-1 rounded-full transition-all duration-300"
                :style="{ width: `${Math.max(currentFileProgressPercentage, 2)}%` }"
              />
            </div>
          </div>
        </div>

        <!-- Uploaded Files Table -->
        <div v-if="uploadedFiles.length > 0" class="space-y-4">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-primary-500">
              Uploaded Files ({{ uploadedFiles.length }})
            </h3>
            <div class="flex items-center gap-2 text-sm text-gray-600">
              <span>Models uploaded: {{ successfulUploads }}</span>
              <span>•</span>
              <span>Credits remaining: {{ creditsRemaining }}</span>
            </div>
          </div>

          <Table :head="['Photos', 'Title', 'Gender', 'Ethnicity', 'Age', 'Actions']" class="bulk-upload-table">
            <TableRow v-for="(file, index) in uploadedFiles" :key="file.id">
              <TableItem>
                <div class="flex items-center space-x-2">
                  <!-- Photo Previews -->
                  <div class="flex items-center space-x-1">
                    <template v-if="file.photoPreviews && file.photoPreviews.length > 0">
                      <div
                        v-for="(preview, previewIndex) in file.photoPreviews.slice(0, 1)"
                        :key="previewIndex"
                        class="w-8 h-8 rounded overflow-hidden bg-gray-100 flex-shrink-0"
                      >
                        <img
                          :src="preview"
                          :alt="`Preview ${previewIndex + 1}`"
                          class="w-full h-full object-cover"
                        >
                      </div>
                    </template>
                    <div
                      v-else
                      class="w-8 h-8 rounded bg-gray-200 flex items-center justify-center flex-shrink-0"
                    >
                      <IconPhoto class="w-4 h-4 text-gray-400" />
                    </div>
                    <button
                      v-if="file.photoCount > 1 && file.photoPreviews && file.photoPreviews.length > 1"
                      class="w-8 h-8 rounded bg-gray-100 border border-gray-200 flex items-center justify-center text-xs font-medium text-gray-600 hover:bg-gray-200 transition-colors"
                      @click="showPhotoModal(file)"
                    >
                      +{{ file.photoCount - 1 }}
                    </button>
                    <button
                      v-else-if="file.photoPreviews && file.photoPreviews.length > 0"
                      class="text-xs text-gray-500 hover:text-gray-700 cursor-pointer"
                      @click="showPhotoModal(file)"
                    >
                      View all
                    </button>
                  </div>
                  <!-- Photo count with warning -->
                  <Tooltip
                    v-if="file.photoCount >= 9 && file.photoCount < 15"
                    info="For best results, we recommend uploading at least 15 photos. You can still proceed with 9+ photos."
                    position="top"
                  >
                    <template #icon>
                      <IconExclamation class="w-4 h-4 text-yellow-500" />
                    </template>
                  </Tooltip>
                </div>
              </TableItem>
              <TableItem>
                <Input
                  v-if="!file.isSubmitting && !file.isCompleted"
                  v-model="file.title"
                  placeholder="Enter title"
                  class="w-full"
                  :disabled="file.isSubmitting"
                />
                <span v-else class="font-medium">{{ file.title }}</span>
              </TableItem>
              <TableItem>
                <InputSelect
                  v-if="!file.isSubmitting && !file.isCompleted"
                  v-model="file.gender"
                  :options="genderOptions"
                  class="w-full"
                  :disabled="file.isSubmitting"
                />
                <div v-else class="flex items-center gap-1.5">
                  <IconMale v-if="file.gender === 'male'" class="size-4 -mb-0.5" />
                  <IconFemale v-if="file.gender === 'female'" class="size-4 -mb-0.5" />
                  <span class="capitalize">{{ file.gender }}</span>
                </div>
              </TableItem>
              <TableItem>
                <InputSelect
                  v-if="!file.isSubmitting && !file.isCompleted"
                  v-model="file.ethnicity"
                  :options="ethnicityOptions"
                  class="w-full"
                  :disabled="file.isSubmitting"
                />
                <span v-else>{{ getEthnicityTitle(file.ethnicity) }}</span>
              </TableItem>
              <TableItem>
                <InputSelect
                  v-if="!file.isSubmitting && !file.isCompleted"
                  v-model="file.age"
                  :options="ageOptions"
                  class="w-full"
                  :disabled="file.isSubmitting"
                />
                <span v-else>{{ file.age }}</span>
              </TableItem>
              <TableItem>
                <div class="flex items-center gap-2">
                  <ButtonWhite
                    v-if="!file.isSubmitting && !file.isCompleted"
                    size="xs"
                    @click="removeFile(index)"
                  >
                    Remove
                  </ButtonWhite>
                  <div v-if="!file.isSubmitting && !file.isCompleted" class="flex items-center gap-2">
                    <ButtonPrimary
                      v-if="canSubmitFile(file)"
                      size="xs"
                      @click="submitFile(file, index)"
                    >
                      Submit
                    </ButtonPrimary>
                    <div v-else class="flex items-center gap-1">
                      <ButtonPrimary
                        size="xs"
                        :disabled="true"
                        class="opacity-50 cursor-not-allowed"
                      >
                        Submit
                      </ButtonPrimary>
                      <Tooltip
                        v-if="file.photoCount < 9"
                        :info="`Need at least 9 photos to submit. Currently have ${file.photoCount} photos.`"
                        position="left"
                      />
                      <Tooltip
                        v-else-if="!file.title.trim()"
                        info="Title is required to submit."
                        position="left"
                      />
                      <Tooltip
                        v-else-if="!file.gender || !file.ethnicity || !file.age"
                        info="Gender, ethnicity, and age are required to submit."
                        position="left"
                      />
                    </div>
                  </div>
                  <div v-if="file.isSubmitting" class="flex items-center gap-2">
                    <LoadingSpinnerSingle class="w-4 h-4" />
                    <span class="text-sm text-gray-600">Submitting...</span>
                  </div>
                  <div v-if="file.isCompleted" class="flex items-center gap-2">
                    <IconCheck class="w-4 h-4 text-green-500" />
                    <span class="text-sm text-green-600">Completed</span>
                  </div>
                </div>
              </TableItem>
            </TableRow>
          </Table>
        </div>
      </div>
    </Card>

    <!-- Photo Preview Modal -->
    <Modal v-if="selectedFileForModal" @close="selectedFileForModal = null">
      <div class="p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">
            Photos for "{{ selectedFileForModal.title }}"
          </h3>
          <span class="text-sm text-gray-500">
            {{ selectedFileForModal.photoCount }} photos
          </span>
        </div>

        <div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-3 max-h-96 overflow-y-auto">
          <div
            v-for="(preview, index) in selectedFileForModal.photoPreviews"
            :key="index"
            class="group aspect-square relative rounded-lg overflow-hidden bg-gray-100"
          >
            <img
              :src="preview"
              :alt="`Photo ${index + 1}`"
              class="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
            >
            <div class="group-hover:opacity-100 opacity-0 transition-opacity duration-200 absolute top-0 right-0 bg-black/70 w-full h-full flex items-center justify-center">
              <div class="w-10 h-10 rounded-full bg-red-500 flex items-center justify-center hover:scale-105 transition-transform duration-200 hover:bg-red-600 cursor-pointer" @click="removePhoto(selectedFileForModal, index)">
                <IconMiniXMark class="w-4 h-4 text-white" />
              </div>
            </div>
          </div>
        </div>

        <div class="mt-4 flex justify-end">
          <ButtonWhite size="sm" @click="selectedFileForModal = null">
            Close
          </ButtonWhite>
        </div>
      </div>
    </Modal>

    <!-- FAQ Modal -->
    <FaqModal v-if="showFaq" :faqs="faqItems" @closeModal="showFaq = false" />
  </div>
</template>

<script>
import OrganizationMixin from '@/mixins/OrganizationMixin'
import ModelPersonalizationMixin from '@/mixins/ModelPersonalizationMixin'
import { detectFaces, getLargestFace, getBoundingBox, cropHead } from '@/utils/image'

export default {
  mixins: [OrganizationMixin, ModelPersonalizationMixin],
  layout: 'teamLead',
  data () {
    return {
      isDragging: false,
      isProcessing: false,
      processingStatus: '',
      uploadedFiles: [],
      uploadProgress: {
        total: 0,
        completed: 0
      },
      currentFileProgress: {
        total: 0,
        completed: 0,
        status: ''
      },
      successfulUploads: 0,
      showFaq: false,
      selectedFileForModal: null,
      faqItems: [
        {
          question: 'What is bulk upload?',
          answer: 'Bulk upload allows you to upload multiple ZIP files containing training photos to quickly create models for your team members without going through the individual onboarding process.'
        },
        {
          question: 'What should be in each ZIP file?',
          answer: 'Each ZIP file should contain at least 9 high-quality training photos of one person, though 15+ photos are recommended for best results. Supported formats include JPG, PNG, HEIC, HEIF, and AVIF. The photos should meet our standard quality requirements (minimum 512px, clear face visibility, good lighting).'
        },
        {
          question: 'How many ZIP files can I upload at once?',
          answer: 'You can upload up to 20 ZIP files at once. Each ZIP file will create one model.'
        },
        {
          question: 'What happens to quality checks?',
          answer: 'Bulk upload bypasses our automatic quality checks to speed up the process. Please ensure all photos meet our quality standards before uploading to avoid disappointing results.'
        },
        {
          question: 'How are credits deducted?',
          answer: 'One credit is deducted from your organization for each successfully submitted model, just like the regular model creation process.'
        }
      ],

      ageOptions: [
        { title: '18-25', value: '18-25' },
        { title: '26-35', value: '26-35' },
        { title: '36-45', value: '36-45' },
        { title: '46-55', value: '46-55' },
        { title: '56-65', value: '56-65' },
        { title: '65+', value: '65+' }
      ],
      genderOptions: [
        { title: 'Male', value: 'male' },
        { title: 'Female', value: 'female' }
      ]
    }
  },
  head () {
    return {
      title: 'Bulk Upload | HeadshotPro'
    }
  },
  computed: {
    hasValidCombos () {
      const organization = this.$store.state.organization?.organization
      if (!organization?.allowedOptions) { return false }

      const hasCombos = organization.allowedOptions.combos?.length >= 8

      return hasCombos
    },
    creditsRemaining () {
      return this.$store.state.organization?.organization?.credits || 0
    },
    ethnicityOptions () {
      return this.ethnicityList.filter(option => option.value !== '')
    },
    mainProgressPercentage () {
      if (this.uploadProgress.total === 0) { return 0 }
      return Math.round((this.uploadProgress.completed / this.uploadProgress.total) * 100)
    },
    currentFileProgressPercentage () {
      if (this.currentFileProgress.total === 0) { return 0 }
      return Math.round((this.currentFileProgress.completed / this.currentFileProgress.total) * 100)
    }
  },
  mounted () {
    if (!this.completedAllMandatorySteps) {
      this.$toast.error('Please complete your organization setup before using bulk upload')
      this.$router.push('/app/admin')
      return
    }

    // Check if bulk upload feature is enabled for this organization
    const organization = this.$store.state.organization?.organization
    if (!organization?.features?.bulkUpload) {
      this.$toast.error('Bulk upload feature is not enabled for your organization')
      this.$router.push('/app/admin')
    }
  },
  methods: {
    handleFileUpload (event) {
      const files = Array.from(event.target.files)
      this.processFiles(files)
      event.target.value = ''
    },
    handleFileDrop (event) {
      this.isDragging = false
      const files = Array.from(event.dataTransfer.files)
      this.processFiles(files)
    },
    async processFiles (files) {
      // Validate file types
      const zipFiles = files.filter(file => file.type === 'application/zip' || file.name.endsWith('.zip'))

      if (zipFiles.length !== files.length) {
        this.$toast.error('Only ZIP files are allowed')
        return
      }

      if (zipFiles.length > 20) {
        this.$toast.error('Maximum 20 files allowed at once')
        return
      }

      if (this.uploadedFiles.length + zipFiles.length > 20) {
        this.$toast.error(`Cannot upload more files. Current: ${this.uploadedFiles.length}, Trying to add: ${zipFiles.length}, Max: 20`)
        return
      }

      this.isProcessing = true
      this.uploadProgress.total = zipFiles.length
      this.uploadProgress.completed = 0

      for (let i = 0; i < zipFiles.length; i++) {
        const file = zipFiles[i]
        try {
          this.processingStatus = `Processing ${file.name} (${i + 1}/${zipFiles.length})...`
          await this.processZipFile(file)
          this.uploadProgress.completed = i + 1

          // Force Vue to update the DOM
          await this.$nextTick()
        } catch (error) {
          this.$toast.error(`Failed to process ${file.name}: ${error.message}`)
          this.uploadProgress.completed = i + 1
          await this.$nextTick()
        }
      }

      this.isProcessing = false
      this.processingStatus = ''

      // Keep progress visible for a moment before clearing
      setTimeout(() => {
        this.uploadProgress.total = 0
        this.uploadProgress.completed = 0
        this.currentFileProgress = { total: 0, completed: 0, status: '' }
      }, 1000)
    },
    async processZipFile (file) {
      try {
        // Load JSZip dynamically
        const JSZip = (await import('jszip')).default

        // Read ZIP file
        const zip = new JSZip()
        const zipData = await zip.loadAsync(file)

        // Filter for image files
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.heic', '.heif', '.avif']
        const imageFiles = []

        Object.keys(zipData.files).forEach((fileName) => {
          const zipEntry = zipData.files[fileName]
          const ext = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
          const baseName = fileName.split('/').pop()

          // Skip directories and hidden files
          if (zipEntry.dir) {
            return
          }
          if (baseName.startsWith('.')) {
            return
          }
          if (fileName.includes('__MACOSX/')) {
            return
          }
          if (fileName.includes('/.')) {
            return
          }

          // Skip common non-image files
          const skipFiles = ['thumbs.db', 'desktop.ini', '.ds_store']
          if (skipFiles.includes(baseName.toLowerCase())) {
            return
          }

          // Only include valid image extensions
          if (imageExtensions.includes(ext)) {
            imageFiles.push({ fileName, zipEntry })
          }
        })

        if (imageFiles.length === 0) {
          throw new Error('No valid image files found in ZIP')
        }

        if (imageFiles.length > 25) {
          throw new Error('Too many images in ZIP. Maximum 25 images allowed per ZIP file')
        }

        // Process images with concurrency limit
        this.currentFileProgress.total = imageFiles.length
        this.currentFileProgress.completed = 0
        this.currentFileProgress.status = 'Extracting and processing images...'

        const processedImages = []
        const photoPreviews = []
        let skippedCount = 0
        let fullBodyShotCount = 0

        // Process images with max 5 concurrent
        const concurrencyLimit = 5
        const processImage = async ({ fileName, zipEntry }) => {
          try {
            // Extract image data
            let imageData = await zipEntry.async('blob')

            // Convert HEIC/AVIF files to JPEG
            const ext = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
            if (ext === '.heic' || ext === '.heif') {
              try {
                const heic2any = (await import('heic2any')).default
                imageData = await heic2any({ blob: imageData, toType: 'image/jpeg' })
              } catch (heicError) {
                return { success: false, fileName, reason: 'heic_conversion_failed' }
              }
            } else if (ext === '.avif') {
              try {
                // Convert AVIF to JPEG using canvas
                const canvas = document.createElement('canvas')
                const ctx = canvas.getContext('2d')
                const img = new Image()

                await new Promise((resolve, reject) => {
                  img.onload = resolve
                  img.onerror = reject
                  img.src = URL.createObjectURL(imageData)
                })

                canvas.width = img.naturalWidth
                canvas.height = img.naturalHeight
                ctx.drawImage(img, 0, 0)

                imageData = await new Promise((resolve) => {
                  canvas.toBlob(resolve, 'image/jpeg', 0.9)
                })

                // Clean up object URL
                URL.revokeObjectURL(img.src)
              } catch (avifError) {
                return { success: false, fileName, reason: 'avif_conversion_failed' }
              }
            }

            // Detect faces and crop
            const faceResult = await detectFaces(imageData, false) // don't bypass face detection for bulk upload

            if (faceResult.errorMessage) {
              return { success: false, fileName, reason: 'face_detection_failed' }
            }

            if (!faceResult.data || !faceResult.data.boundingBoxes || !Array.isArray(faceResult.data.boundingBoxes) || faceResult.data.boundingBoxes.length === 0) {
              return { success: false, fileName, reason: 'no_faces_found' }
            }

            // Get the largest face
            const largestFace = getLargestFace(faceResult.data.boundingBoxes)
            const boundingBox = getBoundingBox(largestFace)

            // Check face coverage to determine if this should be a full body shot
            const faceCoveragePercentage = faceResult.data.faceCoveragePercentage || 0
            const shouldUseFullBody = faceCoveragePercentage < 20 && fullBodyShotCount < 3

            // Crop the image
            let cropResult
            if (shouldUseFullBody) {
              const { cropBody } = await import('@/utils/image')
              cropResult = await cropBody(imageData, boundingBox)
              if (cropResult.success) {
                fullBodyShotCount++
              }
            } else {
              cropResult = await cropHead(imageData, boundingBox, true, 256, 768)
            }

            if (!cropResult.success) {
              return { success: false, fileName, reason: 'face_cropping_failed' }
            }

            // Convert cropped image to blob
            const canvas = document.createElement('canvas')
            const ctx = canvas.getContext('2d')
            const img = new Image()

            await new Promise((resolve, reject) => {
              img.onload = resolve
              img.onerror = reject
              img.src = cropResult.image
            })

            canvas.width = 768
            canvas.height = 768
            ctx.drawImage(img, 0, 0, 768, 768)

            const croppedBlob = await new Promise((resolve) => {
              canvas.toBlob(resolve, 'image/jpeg', 0.9)
            })

            // Upload cropped image to backend
            const formData = new FormData()
            formData.append('image', croppedBlob, `${fileName.replace(/\.[^/.]+$/, '')}_cropped.jpg`)

            try {
              const uploadResponse = await this.$axios.$post('/model/bulk-upload/upload-image', formData, {
                headers: {
                  'Content-Type': 'multipart/form-data'
                }
              })

              if (uploadResponse.data?.imagePath) {
                return {
                  success: true,
                  fileName,
                  imagePath: uploadResponse.data.imagePath,
                  preview: uploadResponse.data.previewUrl || cropResult.image // fallback to client-side preview
                }
              } else {
                return { success: false, fileName, reason: 'upload_missing_path' }
              }
            } catch (uploadError) {
              return { success: false, fileName, reason: 'upload_failed' }
            }
          } catch (error) {
            return { success: false, fileName, reason: 'processing_error' }
          }
        }

        // Process images in batches with concurrency limit
        for (let i = 0; i < imageFiles.length; i += concurrencyLimit) {
          const batch = imageFiles.slice(i, i + concurrencyLimit)
          const batchNumber = Math.floor(i / concurrencyLimit) + 1
          const totalBatches = Math.ceil(imageFiles.length / concurrencyLimit)
          this.currentFileProgress.status = `Processing batch ${batchNumber}/${totalBatches}...`

          // Create promises with individual progress tracking
          const batchPromises = batch.map(async (imageFile, batchIndex) => {
            const globalIndex = i + batchIndex
            try {
              const result = await processImage(imageFile)

              // Update progress immediately after each image completes
              this.currentFileProgress.completed = globalIndex + 1
              this.currentFileProgress.status = `Processing image ${globalIndex + 1}/${imageFiles.length}...`

              // Force Vue to update the DOM and add small delay for visibility
              await this.$nextTick()
              await new Promise(resolve => setTimeout(resolve, 50))

              return result
            } catch (error) {
              this.currentFileProgress.completed = globalIndex + 1
              await this.$nextTick()
              throw error
            }
          })

          const batchResults = await Promise.allSettled(batchPromises)

          // Process results
          batchResults.forEach((result, index) => {
            if (result.status === 'fulfilled') {
              const processResult = result.value
              if (processResult.success) {
                processedImages.push(processResult.imagePath)
                photoPreviews.push(processResult.preview)
              } else {
                skippedCount++
              }
            } else {
              skippedCount++
            }
          })
        }

        if (processedImages.length === 0) {
          throw new Error('No images could be processed successfully. Please ensure your ZIP contains valid photos with clear faces.')
        }

        if (skippedCount > 0) {
          this.$toast.warning(`${skippedCount} images were skipped from ${file.name} due to face detection issues`)
        }

        // Add to uploaded files list
        const uploadedFile = {
          id: Date.now() + Math.random(),
          fileName: file.name,
          title: file.name.replace('.zip', ''),
          gender: 'male',
          ethnicity: 'caucasian',
          age: '26-35',
          photoCount: processedImages.length,
          trainingImages: processedImages,
          photoPreviews,
          isSubmitting: false,
          isCompleted: false
        }

        this.uploadedFiles.push(uploadedFile)
      } catch (error) {
        throw new Error(error.message || 'Failed to process ZIP file')
      }
    },
    canSubmitFile (file) {
      return file.title.trim() && file.gender && file.ethnicity && file.age && file.photoCount >= 9 && !file.isSubmitting && !file.isCompleted
    },
    async submitFile (file, index) {
      if (this.creditsRemaining <= 0) {
        this.$toast.error('Insufficient credits to create model')
        return
      }

      file.isSubmitting = true

      try {
        const payload = {
          title: file.title.trim(),
          gender: file.gender,
          ethnicity: file.ethnicity,
          age: file.age,
          trainingImages: file.trainingImages
        }

        await this.$axios.$post('/model/bulk-upload/create', payload)

        file.isCompleted = true
        this.successfulUploads++

        // Update organization credits in store
        const currentCredits = this.$store.state.organization?.organization?.credits || 0
        this.$store.commit('organization/SET_ORGANIZATION_CREDITS', Math.max(0, currentCredits - 1))

        this.$toast.success(`Model "${file.title}" created successfully`)
      } catch (error) {
        this.$toast.error(`Failed to create model: ${error.response?.data?.message || error.message}`)
      } finally {
        file.isSubmitting = false
      }
    },
    removeFile (index) {
      this.uploadedFiles.splice(index, 1)
    },
    showPhotoModal (file) {
      this.selectedFileForModal = file
    },
    openFullImage (imageSrc) {
      // Open image in new tab for full view
      window.open(imageSrc, '_blank')
    },
    getEthnicityTitle (value) {
      const ethnicity = this.ethnicityList.find(option => option.value === value)
      return ethnicity ? ethnicity.title : value
    },
    removePhoto (file, index) {
      file.trainingImages.splice(index, 1)
      file.photoCount = file.trainingImages.length
      file.photoPreviews.splice(index, 1)
    }
  }
}
</script>

<style scoped>
.bulk-upload-table :deep(td) {
  padding: 0.75rem !important;
}
</style>
