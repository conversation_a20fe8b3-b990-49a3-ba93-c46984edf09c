<template>
  <div>
    <div>
      <AppTitle
        title="Changelog"
        sub-title="Latest updates and improvements to HeadshotPro"
      />

      <main class="mt-6">
        <LoadingWrapper :is-loading="isLoading">
          <div v-if="changelogs.length > 0" class="space-y-8 mt-8">
            <div v-for="changelog in changelogs" :key="changelog._id" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div class="flex items-start justify-between mb-4">
                <div class="flex items-center space-x-3">
                  <span :class="getTypeBadgeClass(changelog.type)" class="px-3 py-1 rounded-full text-sm font-medium">
                    {{ changelog.type }}
                  </span>
                  <span v-if="!changelog.isViewed" class="bg-teal-500 text-white text-xs rounded-full px-2 py-1">
                    New
                  </span>
                </div>
                <span class="text-sm text-gray-500">
                  {{ formatDate(changelog.date) }}
                </span>
              </div>

              <h2 class="text-xl font-semibold text-gray-900 mb-3">
                {{ changelog.title }}
              </h2>

              <div class="prose prose-sm max-w-none text-gray-700" v-html="renderMarkdown(changelog.description)" />
            </div>
          </div>

          <Card v-else>
            <IconDocumentText class="h-12 w-12 text-gray-400 mb-4" />
            <h3 class="text-lg font-medium text-primary-500 mb-2">
              No changelog entries yet
            </h3>
            <p class="text-paragraph">
              Check back later for updates and improvements.
            </p>
          </Card>
        </LoadingWrapper>
      </main>
    </div>
  </div>
</template>

<script>
import { marked } from 'marked'

export default {
  layout: 'teamLead',
  data () {
    return {
      isLoading: false,
      changelogs: []
    }
  },
  async mounted () {
    await this.fetchChangelogs()
    this.markAllAsSeenAfterDelay()
    this.$posthog.capture('$page:changelog')
  },
  methods: {
    async fetchChangelogs () {
      this.isLoading = true
      try {
        const response = await this.$axios.$get('/changelog/public')
        // Create a deep copy to ensure we don't have references to store state
        const changelogsCopy = JSON.parse(JSON.stringify(response.data))
        this.changelogs = changelogsCopy
        this.$store.commit('SET_CHANGELOGS', changelogsCopy)
      } catch (error) {
        console.error('Error fetching changelogs:', error)
      } finally {
        this.isLoading = false
      }
    },
    getTypeBadgeClass (type) {
      const classes = {
        'New Feature': 'bg-green-100 text-green-800',
        Improvement: 'bg-blue-100 text-blue-800',
        Bugfix: 'bg-yellow-100 text-yellow-800'
      }
      return classes[type] || 'bg-gray-100 text-gray-800'
    },
    formatDate (date) {
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    },
    renderMarkdown (text) {
      return marked(text)
    },
    isChangelogSeen (changelogId) {
      const changelog = this.changelogs.find(c => c._id === changelogId)
      return changelog ? changelog.isViewed : false
    },
    async markChangelogAsSeen (changelogId) {
      try {
        await this.$axios.$post('/changelog/mark-viewed', { changelogId })

        // Create a deep copy to avoid mutating store state
        const updatedChangelogs = JSON.parse(JSON.stringify(this.changelogs))
        const changelog = updatedChangelogs.find(c => c._id === changelogId)
        if (changelog) {
          changelog.isViewed = true
        }

        // Update local state
        this.changelogs = updatedChangelogs

        // Update store with the new copy
        this.$store.commit('SET_CHANGELOGS', updatedChangelogs)
      } catch (error) {
        console.error('Error marking changelog as viewed:', error)
      }
    },
    markAllAsSeenAfterDelay () {
      // Mark all unviewed changelogs as seen after 3 seconds
      setTimeout(async () => {
        const unviewedChangelogs = this.changelogs.filter(changelog => !changelog.isViewed)

        for (const changelog of unviewedChangelogs) {
          await this.markChangelogAsSeen(changelog._id)
        }

        // Force header to refresh by emitting an event or calling a method
        this.$nextTick(() => {
          this.$nuxt.$emit('changelog-updated')
        })
      }, 3000)
    }
  }
}
</script>
