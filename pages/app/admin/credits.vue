<template>
  <div>
    <AppTitle title="Billing" sub-title="This is where you manage how you pay.">
      <div class="flex items-center gap-2">
        <span class="text-sm text-slate-500 font-medium">Change currency: </span>
        <HeaderLanguageSelector />
      </div>
    </AppTitle>

    <main class="mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2">
      <div class="space-y-6">
        <div>
          <TeamBillingAutoRecharge />
        </div>
        <div>
          <CheckoutB2BPayByInvoiceCard
            title="Purchase additional credits"
            subtitle="Pick the payment method you prefer and we got you sorted."
            :how-it-works="[]"
            seats-label="Amount of seats to purchase"
          />
        </div>
      </div>
      <div class="space-y-6">
        <div>
          <Card>
            <h2 class="text-lg font-semibold text-primary-500">
              Usage
            </h2>
            <p class="text-sm text-slate-500">
              This is how many credits you have used and how many you have left.
            </p>
            <div class="mt-8 flex flex-col xs:flex-row justify-start">
              <div class="py-3 border-black/10 border-b xs:border-b-0 xs:border-r xs:pr-8 xs:py-0">
                <p class="text-[15px] text-paragraph font-medium">
                  Credits left
                </p>
                <p class="text-[26px] font-medium text-primary-500">
                  {{ creditsLeft }}
                </p>
              </div>
              <div class="py-3 border-black/10 border-b xs:border-b-0 xs:border-r xs:px-8 xs:py-0">
                <p class="text-[15px] text-paragraph font-medium">
                  Credits used
                </p>
                <p class="text-[26px] font-medium text-primary-500">
                  {{ creditsUsed }}
                </p>
              </div>
              <div class="py-3 border-black/10 border-b xs:border-b-0 xs:pl-8 xs:py-0">
                <p class="text-[15px] text-paragraph font-medium">
                  Invites pending
                </p>
                <div v-if="isLoadingInvites" class="flex items-center justify-center mt-1">
                  <LoadingSpinnerGray class="!w-6 !h-6" />
                </div>
                <p v-else class="text-[26px] font-medium text-primary-500">
                  {{ invitesPending }}
                </p>
              </div>
            </div>
          </Card>
        </div>
        <div>
          <Card>
            <h2 class="text-lg font-semibold text-primary-500">
              Logs
            </h2>
            <p class="text-sm text-slate-500">
              Everything that has happened with your credits.
            </p>
            <div class="mt-8 relative">
              <div class="sm:max-h-[300px] overflow-auto" :class="{ 'sm:pb-20': creditLog.length >= 5 }">
                <Table :head="['Description', 'Date']" class="w-full">
                  <TableRow v-for="log in creditLog" :key="log._id">
                    <TableItem>
                      {{ log.reason }} ({{ log.mutation > 0 ? `+${log.mutation}` : log.mutation }} {{ Math.abs(log.mutation) > 1 ? 'credits' : 'credit' }})
                    </TableItem>
                    <TableItem class="whitespace-nowrap">
                      {{ formatDate(log.date) }}
                    </TableItem>
                  </TableRow>
                </Table>
              </div>
              <div v-if="creditLog.length >= 5" class="hidden absolute bottom-0 left-0 w-full h-20 bg-gradient-to-t from-white to-transparent pointer-events-none sm:flex items-end justify-center">
                <IconChevronDown class="w-6 h-6 text-paragraph/50" />
              </div>
            </div>
          </Card>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
export default {
  layout: 'teamLead',
  data () {
    return {
      isLoadingInvites: false,
      invitesPending: 0
    }
  },
  head () {
    return {
      title: 'Credits | HeadshotPro'
    }
  },
  computed: {
    creditLog () {
      if (!this.$store.state?.organization?.organization?.creditLog) {
        return []
      }

      const log = [...this.$store.state?.organization?.organization?.creditLog] || []
      return log.reverse()
    },
    creditsLeft () {
      return this.$store.state?.organization?.organization?.credits || 0
    },
    creditsUsed () {
      if (!this.$store.state?.organization?.organization?.creditLog) {
        return 0
      }

      const log = [...this.$store.state?.organization?.organization?.creditLog] || []
      return Math.abs(log.filter(log => log.mutation < 0).reduce((acc, curr) => acc + curr.mutation, 0))
    }
  },
  mounted () {
    this.getPendingInvites()
  },
  methods: {
    async getPendingInvites () {
      try {
        this.isLoadingInvites = true
        const { success, data, errorMessage } = await this.$axios.$get('/organization/team/pending')
        if (!success) {
          throw new Error(errorMessage)
        }
        this.invitesPending = data.total
      } catch (err) {
        console.log(err)
      } finally {
        this.isLoadingInvites = false
      }
    }
  }
}
</script>

<style></style>
