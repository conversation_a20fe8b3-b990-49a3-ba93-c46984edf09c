<template>
  <div>
    <AppTitle
      title="Backdrops"
      sub-title="Backdrops we'll use in your photo shoots."
      with-mobile-actions
    >
      <ButtonWhite size="sm" @click="showFaq = true">
        <IconSmallQuestionMark class="size-4 text-[#ADB0B5] mr-1.5" />
        FAQ
      </ButtonWhite>
      <ButtonPrimary size="sm" @click="saveAllowedOptions()">
        Save changes
      </ButtonPrimary>
      <template #mobile>
        <ButtonPrimary size="sm" class="w-full" @click="saveAllowedOptions()">
          Save changes
        </ButtonPrimary>
      </template>
    </AppTitle>
    <main class="mt-6">
      <OnboardingTeamStylePicker
        ref="stylePicker"
        :styles="styles"
        :preselected-options="[]"
        :selected-styles="selectAllowedStyles"
        :max-selected-styles="maxSelectedStyles"
        :min-selected-styles="minSelectedStyles"
        :wants-to-continue="false"
        :can-finish="false"
        @select-style="selectAllowedStyle"
        @remove-style="deleteAllowedStyle"
        @next="showSuccessMessage"
      />
    </main>
    <FaqModal v-if="showFaq" :faqs="faqItems" @closeModal="showFaq = false" />
  </div>
</template>

<script>
export default {
  layout: 'teamLead',
  data () {
    return {
      isSaving: false,
      isLoading: true,
      // new allowed options state
      selectAllowedStyles: [],
      maxSelectedStyles: 8,
      minSelectedStyles: 3,
      showFaq: false,
      faqItems: [
        {
          question: 'What is a backdrop?',
          answer: 'A backdrop is the background of a photo.'
        },
        {
          question: 'Can I add my own backdrop?',
          answer: 'Contact our sales team at <strong><EMAIL></strong> with your desired backdrop or outfit example. We\'ll create a custom solution tailored to your needs to ensure you get the perfect professional headshot.'
        }
      ]
    }
  },
  head () {
    return {
      title: 'Backdrops | HeadshotPro'
    }
  },
  computed: {
    styles () {
      const styles = this.$store.state.styles
      const userOrganization = this.$store.state?.organization?.organization?._id
      const filteredStyles = styles.filter((style) => {
        if (!style?.organizationIds) { return true }
        if (style?.organizationIds?.length === 0) { return true }
        if (style?.organizationIds?.includes(userOrganization)) { return true }
        return false
      })
      return filteredStyles
    },
    selectedStyle: {
      get () {
        const styles = this.$store.state.styles
        const allStyles = this.$store.state?.organization?.organization?.allowedOptions?.styles?.length > 0 ? this.$store.state?.organization?.organization?.allowedOptions?.styles : (this.$store.state?.organization?.organization?.style || [])
        return allStyles.map(style => styles.find(s => s._id === style._id || s._id === style)).filter(Boolean)
      },
      set (style) {
        this.$store.commit('organization/SET_STYLE', style)
      }
    }
  },
  mounted () {
    this.restoreState()
  },
  methods: {
    saveChangesBeforeLeaving () {
      this.saveAllowedOptions()
    },
    deleteAllowedStyle (index) {
      this.$store.commit('SET_HAS_UNSAVED_CHANGES', true)
      this.selectAllowedStyles.splice(index, 1)
    },
    saveAllowedOptions () {
      this.$refs.stylePicker.goToNextStep()
    },
    selectAllowedStyle (styleItem) {
      this.$store.commit('SET_HAS_UNSAVED_CHANGES', true)
      const style = this.styles.find(style => style._id === styleItem._id)
      if (!style) {
        return
      }

      this.selectAllowedStyles.push(style)
    },
    showSuccessMessage () {
      this.$store.commit('SET_HAS_UNSAVED_CHANGES', false)
      this.$toast.success('Your backdrop selections have been saved')
    },
    restoreState () {
      if (this.selectedStyle?.length > 0) {
        const styles = []
        for (const style of this.selectedStyle) {
          const styleObject = this.styles.find(s => s._id === style._id)
          if (styleObject) {
            styles.push(styleObject)
          }
        }

        this.selectAllowedStyles = JSON.parse(JSON.stringify(styles))
      }
    }
  }
}
</script>

<style></style>
