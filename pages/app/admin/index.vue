<template>
  <div>
    <TeamDashboardFinishOnboarding
      v-if="!completedAllMandatorySteps"
      :key="`finish-onboarding-${dashboardId}`"
      @invite="toggleInviteMember()"
    />
    <TeamDashboardNoInvites
      v-else-if="$store.state.user.numberOfModels === 0 && !completionData.hasInvitedTeam"
      :key="`no-invites-${dashboardId}`"
      @invite="toggleInviteMember()"
      @invite-link="toggleInviteMember(true)"
    />
    <TeamDashboard
      v-else
      :key="`dashboard-${dashboardId}`"
      @invite="toggleInviteMember()"
      @retry="showRetryModal = true; retryUserId = $event;"
      @removed-invites="refresh()"
    />
    <Modal v-if="showModal" @close="showModal = false">
      <OnboardingAddTeam
        :only-invite-link="onlyInviteLink"
        title="Add team-members"
        sub-title="Invite team-members to join your team. You can add multiple team members at once by seperating them with a comma or line break."
        @success="showModal = false"
      />
    </Modal>
    <Modal v-if="showRetryModal" @close="showRetryModal = false">
      <ResultRetry
        :user-id="retryUserId"
        @success="
          retryUserId = null;
          showRetryModal = false;
          refresh()
        "
      />
    </Modal>
  </div>
</template>

<script>
import OrganizationMixin from '@/mixins/OrganizationMixin'

export default {
  mixins: [OrganizationMixin],
  layout: 'teamLead',
  data () {
    return {
      showModal: false,
      showRetryModal: false,
      retryUserId: null,
      onlyInviteLink: false,
      dashboardId: 1
    }
  },
  head () {
    return {
      title: 'Manage your team | HeadshotPro'
    }
  },
  methods: {
    async refresh () {
      try {
        this.$loading.show({
          title: 'Loading...'
        })
        await this.getUserData()
        this.dashboardId++
      } catch (err) {
        this.$toast.error('Failed to load data')
      } finally {
        this.$loading.hide()
      }
    },
    toggleInviteMember (onlyInviteLink = false) {
      if (!this.completedAllMandatorySteps) {
        this.$toast.error('Please complete your payment and pick clothing and backgrounds before inviting team members')
        return this.$router.push('/app/admin/style')
      }

      this.showModal = true
      this.onlyInviteLink = onlyInviteLink
    }
  }
}
</script>

<style></style>
