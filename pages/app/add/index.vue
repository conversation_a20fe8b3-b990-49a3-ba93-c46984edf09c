<template>
  <PrecheckoutWrapper :back-url="activeStep > 1 ? localePath(`/app/add?step=${activeStep-1}`) : null" alignment="top">
    <PrecheckoutIntro v-if="activeStep === 1" @next="activeStep++" />
    <PrecheckoutPackagesV2 v-if="activeStep === 2" @next="activeStep++" />
    <!-- <PrecheckoutPackages v-else @next="activeStep++" /> -->
  </PrecheckoutWrapper>
</template>

<script>
import PosthogMixin from '@/mixins/PosthogMixin'
export default {
  mixins: [PosthogMixin],
  layout: 'protected',
  data () {
    return {
      activeStep: 1
    }
  },
  head () {
    return {
      title: this.$t('title') + ' | HeadshotPro',
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com' + this.$route.path
        }
      ]
    }
  },
  computed: {
    env () {
      return process.env.NODE_ENV
    },
    isTeamLead () {
      return this.$store.state?.user?.role === 'TeamLead' || null
    },
    hasInvites () {
      return this.$store.state?.user?.invites || null
    },
    showInviteWarning () {
      return this.$store.state?.showInviteWarning || null
    },
    role () {
      return this.$store.state?.user?.role || null
    },
    purchasedUpsell () {
      return this.$store.state?.user?.purchasedUpsell || false
    },
    user () {
      return this.$store.state.user
    },
    organization () {
      return this.$store.state.organization.organization
    }
  },
  watch: {
    $route (to, from) {
      if (to.query.step) {
        const step = parseInt(to.query.step)
        if (step > 0 && step < 3) {
          this.activeStep = step
        }
      }
    },
    activeStep (newValue, oldValue) {
      const user = this.$store.state.user
      if (newValue >= 2) {
        // User has package and should thus skip payment
        if (user && user.packages && user.packages.length > 0) {
          this.$router.push(this.localePath('/app/upload'))
        }
        // Team leads should never see this page, so another check
        if (user.role === 'TeamMember') {
          this.$router.push(this.localePath('/app/upload'))
        }

        if (user.role === 'TeamLead') {
          // If organization has no credits, redirect to payment page
          if (this.organization.credits <= 0) {
            this.$router.push(this.localePath('/app/admin/team/new?step=3'))
          }
          // If organization has credits, redirect to upload page
          if (this.organization.credits > 0) {
            this.$router.push(this.localePath('/app/upload'))
          }
        }
      }

      try {
        if (!this.isTeamMember) {
          this.$posthog.capture('$funnel:onboarding', {
            step: newValue,
            previousStep: oldValue
          })
        }

        this.$router.push({
          query: {
            ...this.$route.query,
            step: newValue
          }
        })
      } catch (err) {
        console.error(err)
      }
    }
  },
  mounted () {
    if (this.$route.query.payment === 'failed') {
      this.handleError(this.$t('Payment failed, please try again'))
    }

    if (this.$route.query.step) {
      const step = parseInt(this.$route.query.step)
      if (step > 0 && step < 4) {
        this.activeStep = step
      }
    }

    this.handleTeamMemberLogic()
    this.handleUserLogic()
    this.handleTeamLeadLogic()
  },
  methods: {
    handleUserLogic () {
      const { user } = this
      if (user.role !== 'User') { return }

      // Redirect to upload page if user has packages
      if (user && user.packages && user.packages.length > 0) {
        this.$router.push(this.localePath('/app/upload'), { query: this.$route.query })
      }
    },
    handleTeamMemberLogic () {
      const { user } = this
      // Only handle TeamLead logic if user is a TeamLead and organization exists
      if (user.role !== 'TeamMember') { return }

      // TeamMember should never see this page and instantly be redirected to upload page
      this.$router.push(this.localePath('/app/upload'), { query: this.$route.query })
    },
    handleTeamLeadLogic () {
      const { user, organization } = this
      // Only handle TeamLead logic if user is a TeamLead and organization exists
      if (user.role !== 'TeamLead') { return }

      // If organization does not have any styles, redirect to onboarding step page
      if (!organization.allowedOptions || !organization.allowedOptions.styles?.length) {
        this.$router.push({ path: this.localePath('/app/admin/team/new?step=1') })
        return
      }

      // If organization does not have any credits, redirect them to payment onboarding page
      if (organization?.credits <= 0) {
        if (user.hasRetry) {
          // TeamLead has a retry to let them upload their photos
          this.$router.push(this.localePath('/app/upload'))
          return
        }
        if (!organization?.creditLog || organization?.creditLog.length === 0) {
          this.$router.push({ path: this.localePath('/app/admin/team/new?step=3') })
          return
        } else {
          this.$router.push({ path: this.localePath('/app/admin/credits') })
          return
        }
      } else {
        // If organization has credits, redirect to upload page
        this.$router.push(this.localePath('/app/upload'))
      }

      // If user has packages, redirect to old add page
      if (user && user.packages && user.packages.length > 0) {
        this.$router.push(this.localePath('/app/upload'))
      }
    }
  }
}
</script>

<i18n>
  {
    "en": {
      "Payment failed, please try again": "Payment failed, please try again",
      "title": "Create your headshot"
    },
    "es": {
      "Payment failed, please try again": "El pago falló, por favor inténtalo de nuevo",
      "title": "Crea tu foto profesional"
    },
    "de": {
      "Payment failed, please try again": "Zahlung fehlgeschlagen, bitte versuche es erneut",
      "title": "Erstelle dein Bewerbungsfoto"
    }
  }
</i18n>
