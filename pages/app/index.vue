<template>
  <div class="flex flex-col min-h-screen bg-gray-50">
    <div
      class="flex w-full flex-col items-center justify-start antialiased flex-1 md:items-center md:justify-start"
    >
      <HeaderApplication />
      <main class="flex-1">
        <section class="relative pt-8 pb-32 sm:pt-12 md:pb-12">
          <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8">
            <div class="mx-auto text-left md:max-w-xl md:text-center">
              <InviteWarningBar v-if="hasInvites && showInviteWarning" />
              <h1 class="text-xl font-bold tracking-tight sm:text-2xl lg:text-3xl text-primary-500">
                Your headshots
              </h1>
              <p class="mt-2 text-base font-medium text-gray-500 md:max-w-md md:mx-auto">
                Results are automatically deleted after 30 days. Make sure to back them up.
              </p>
            </div>
            <!-- <FeedbackBar
              v-if="!isLoading && activeShoots?.length > 0 && !$store.state.user.rewards?.feedback && role === 'User'"
              :id="activeShoots[0]._id"
              class="md:max-w-lg mt-4"
            /> -->
            <!--<AlertWarningV2 text="Delayed results" class="md:max-w-lg mt-4">
              <p class="text-sm text-black/90">
                Results are slightly delayed due to an outage at our image generation servers. The issues have been resolved, and your results should be available within the next hour or two.
              </p>
            </AlertWarningV2>-->
            <!-- <AlertWarningV2 text="Scheduled maintenance" class="md:max-w-lg mt-4">
              <p class="text-xs text-black/90">
                Saturday, February 22nd, 2025, between 5 AM GMT and 9 AM GMT, we will be performing scheduled maintenance. If you need to access your headshots, please do so before this time.
              </p>
            </AlertWarningV2>-->
            <Invite v-if="hasInvites && hasInvites.length > 0 && showInviteWarning" class="max-w-lg" />
            <div class="p-6 mx-auto mt-8 space-y-5 bg-white border border-gray-200 rounded-lg shadow-sm md:max-w-lg">
              <LoadingSpinner v-if="isLoading" title="Loading your headshots..." />
              <template v-if="!isLoading">
                <template v-if="!isError">
                  <ShootList :shoots="shoots" @refresh="fetchShoots" />
                </template>
                <template v-else>
                  <AlertWarning title="Error loading data" description="We couldn't load your data. Please try a different browser or again later." />
                </template>
              </template>
            </div>

            <div v-if="!isError && activeShoots.length > 0 && shouldShowReferralUpsell" class="mt-8 md:max-w-lg">
              <ReferralBlock :model-id="activeShoots[activeShoots.length - 1]._id" orientation="vertical" />
            </div>

            <div class="flex items-center justify-center gap-6 mt-8 md:hidden">
              <nuxt-link v-if="hasInvites && !isTeamMember" to="/app/invites" class="text-sm font-medium text-gray-500 underline transition-all duration-150 inline-flex hover:text-gray-700">
                <span class="underline-none mr-1 flex h-4 w-4 animate-pulse items-center justify-center rounded-full bg-green-500 text-center text-xs text-white">!</span>
                <p class="text-sm text-gray-500 underline">
                  Invites
                </p>
              </nuxt-link>
              <nuxt-link v-if="isTeamLead" to="/app/admin" class="text-sm font-medium text-gray-500 underline transition-all duration-150 inline-flex hover:text-gray-700">
                For teams
              </nuxt-link>
              <template v-if="role !== 'TeamMember'">
                <nuxt-link
                  v-if="$store?.state?.user?.stripeId || $store?.state?.user?.paypalId"
                  to="/profile/invoices"
                  class="text-sm font-medium text-gray-500 underline transition-all duration-150 inline-flex hover:text-gray-700"
                >
                  Invoices
                </nuxt-link>

                <button
                  type="button"
                  class="text-sm font-medium text-gray-500 underline transition-all duration-150 inline-flex hover:text-gray-700"
                  @click="$store.commit('SET_MODAL', { name: 'support', value: true })"
                >
                  Support
                </button>
              </template>
              <nuxt-link v-if="$store?.state?.user?.stripeId" to="/profile" class="text-sm font-medium text-gray-500 underline transition-all duration-150 inline-flex hover:text-gray-700">
                Profile
              </nuxt-link>
              <button
                type="button"
                class="text-sm font-medium text-gray-500 underline transition-all duration-150 inline-flex hover:text-gray-700"
                @click="logout"
              >
                Logout
              </button>
            </div>
          </div>
        </section>
      </main>
    </div>
  </div>
</template>

<script>
export default {
  layout: 'protected',
  data () {
    return {
      isLoading: true,
      shoots: [],
      isError: false
    }
  },
  head () {
    return {
      title: 'Your headshots | HeadshotPro',
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com' + this.$route.path
        }
      ]
    }
  },
  computed: {
    activeShoots () {
      return this.shoots.filter((shoot) => {
        if (shoot?.favoriteImagesCount === 0) { return false }
        if (shoot?.status !== 'active') { return false }
        return true
      })
    },
    pendingShoots () {
      return this.shoots.filter(shoot => shoot?.status !== 'active')
    },
    env () {
      return process.env.NODE_ENV
    },
    isTeamLead () {
      return this.$store.state?.user?.role === 'TeamLead' || null
    },
    hasInvites () {
      return this.$store.state?.user?.invites || null
    },
    showInviteWarning () {
      return this.$store.state?.showInviteWarning || null
    },
    role () {
      return this.$store.state?.user?.role || null
    },
    purchasedUpsell () {
      return this.$store.state?.user?.purchasedUpsell || false
    }
  },
  async mounted () {
    if (this.isTeamLead && !this.$route.query.lead) {
      this.$router.push('/app/admin')
    }

    await this.fetchShoots()
    if (this.shoots && this.shoots.length === 1) {
      const atLeastOneIsWaiting = this.shoots.some(model =>
        ['pending', 'waiting', 'generatingHeadshots'].includes(model.status)
      )
      if (atLeastOneIsWaiting && !this.isTeamLead) {
        this.$router.push(this.localePath('/app/upload/waiting'))
      }
    }
  },
  methods: {
    async fetchShoots () {
      try {
        this.isLoading = true
        const { success, data, errorMessage } = await this.$axios.$get('/model/all')
        if (!success) {
          this.isError = true
          throw new Error(errorMessage)
        } else {
          this.shoots = data
          this.$store.commit('results/SET_SHOOTS', data)
        }
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = false
      }
    },
    async createPortalSession () {
      this.isLoading = true
      const response = await this.$axios.$post('/checkout/stripe/create-customer-portal-session')
      if (response && response.url) {
        window.open(response.url, '_blank')
      }
      this.isLoading = false
    }
  }
}
</script>

<style></style>
