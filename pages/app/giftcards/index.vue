<template>
  <div>
    <HeaderApplication />
    <main class="flex-1">
      <section class="relative pt-8 pb-32 sm:pt-12 md:pb-12">
        <div class="max-w-3xl w-full px-4 space-y-4 mx-auto sm:px-6 lg:px-8">
          <div class="flex flex-col space-y-1">
            <h2 class="text-xl font-bold text-primary-500">
              Giftcards
            </h2>
            <p class="mb-4 text-paragraph">
              Below are the giftcards you have purchased. Want to order more? <a href="mailto:<EMAIL>" class="underline">Contact support</a>.
            </p>
          </div>
          <LoadingWrapper :is-loading="isLoading" class="space-y-2">
            <template v-if="giftcards.length > 0">
              <div class="flex space-x-2">
                <ButtonWhite size="sm" @click="exportUnusedCodes">
                  Export unused codes
                </ButtonWhite>
                <ButtonWhite size="sm" @click="exportAllCodes">
                  Export all codes
                </ButtonWhite>
              </div>
              <template v-if="giftcards.length > 0">
                <TableSortable :head="tableHead" @sort="handleSort">
                  <template v-for="item in giftcards">
                    <TableRow v-for="coupon in item.coupons" :key="coupon._id">
                      <TableItem>
                        <div class="flex items-center space-x-2 text-xs">
                          <div class="w-2 h-2 rounded-full" :style="`background-color: ${coupon.claimed === true || coupon.uses >= coupon.maxUses ? 'green' : 'red'}`" />
                          <span>{{ coupon.claimed === true || coupon.uses >= coupon.maxUses ? 'Used' : 'Unused' }}</span>
                        </div>
                      </TableItem>
                      <TableItem>
                        {{ coupon.code }}
                      </TableItem>
                      <TableItem>
                        <span class="capitalize">{{ item.package }}</span>
                      </TableItem>
                      <TableItem>
                        <ButtonDropdown size="sm" title="Actions" :items="actionItems" @select="handleAction($event, coupon)" />
                      </TableItem>
                    </TableRow>
                  </template>
                </TableSortable>
              </template>
            </template>
            <template v-else>
              <p class="text-paragraph italic text-xs">
                You have not purchased any giftcards yet.
              </p>
            </template>
          </LoadingWrapper>
        </div>
      </section>
    </main>
  </div>
</template>

<script>
export default {
  layout: 'protected',
  data () {
    return {
      giftcards: [],
      isLoading: false,
      tableHead: ['Status', 'Code', 'Package', 'Actions'],
      actionItems: [
        { title: 'Copy redeem URL', value: 'copyRedeemUrl' }
      ]
    }
  },
  mounted () {
    this.fetchData()
  },
  methods: {
    handleSort (event) {
      this.sortBy = event.column
      this.sortDirection = event.direction
    },
    handleAction (action, item) {
      if (action === 'copyRedeemUrl') {
        this.copyRedeemUrl(item)
      }
    },
    fetchData () {
      this.isLoading = true
      this.$axios.get('/giftcard').then((res) => {
        console.log(res)
        this.giftcards = res.data.giftcards
        this.isLoading = false
      }).catch((err) => {
        this.$toast.error(err.response.data.err)
        this.isLoading = false
      })
    },
    exportAllCodes () {
      // Export this.giftcards.coupons as CSV file
      // Add a header row with the following columns: Code, Used, Package
      let csvContent = this.giftcards.map((giftcard) => {
        return giftcard.coupons.map((coupon) => {
          const used = coupon.claimed === true || coupon.uses >= coupon.maxUses ? 'Used' : 'Unused'
          const redeemUrl = `https://www.headshotpro.com/redeem?code=${coupon.code}`
          return `${coupon.code},${used},${giftcard.package},${redeemUrl}`
        }).join('\n')
      }).join('\n')
      const header = 'Code,Used,Redeem URL\n'
      csvContent = header + csvContent
      this.downloadCsv(csvContent)
    },
    exportUnusedCodes () {
      // Export this.giftcards.coupons as CSV file
      // Add a header row with the following columns: Code, Used, Package
      let csvContent = this.giftcards
        .filter(giftcard => giftcard.coupons.some(coupon => coupon.claimed === false && coupon.uses < coupon.maxUses))
        .map((giftcard) => {
          return giftcard.coupons.map((coupon) => {
            const redeemUrl = `https://www.headshotpro.com/redeem?code=${coupon.code}`
            return `${coupon.code},${giftcard.package},${redeemUrl}`
          }).join('\n')
        }).join('\n')
      const header = 'Code,Redeem URL\n'
      csvContent = header + csvContent
      this.downloadCsv(csvContent)
    },

    downloadCsv (csvContent) {
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'giftcards.csv'
      a.click()
    },
    copyRedeemUrl (item) {
      const { code, uses, maxUses, claimed } = item
      const used = claimed === true || uses >= maxUses
      if (used) {
        this.$toast.error('This code has already been used')
        return
      }

      navigator.clipboard.writeText(`https://www.headshotpro.com/redeem?code=${code}`)
      this.$toast.success('Redeem URL copied to clipboard')
    }
  }
}
</script>

<style>

</style>
