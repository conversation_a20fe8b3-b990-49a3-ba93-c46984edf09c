<template>
  <PostcheckoutWrapper alignment="top" :skip="true">
    <section class="relative pt-8 pb-28 sm:pt-12 md:pb-12">
      <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8 gap-8 flex flex-col">
        <PostcheckoutStepper :step="1" classes="md:justify-center" />

        <div class="mx-auto text-left md:max-w-2xl md:text-center">
          <h1 class="text-xl font-bold tracking-tight sm:text-2xl lg:text-3xl text-primary-500">
            Are you sure about managed upload?
          </h1>
          <p class="mt-2 text-base font-medium text-gray-500 md:max-w-md md:mx-auto">
            Our managed upload system is only for those who want to manage the full process for the team.
          </p>
          <div class="flex justify-center">
            <ButtonWhite size="sm" class="mt-4" @click="showDemoModal = true">
              <IconMiniPlay class="w-4 h-4 mr-2" />
              <span>View demo</span>
            </ButtonWhite>
          </div>
        </div>
        <div class="grid grid-cols-1 gap-8 md:grid-cols-2 max-w-5xl">
          <!-- Left Card: When to use -->
          <Card>
            <div class="flex flex-col gap-[14px] p-2">
              <div class="flex items-center gap-2">
                <div class="w-[20px] h-[20px] bg-green-500 rounded-full flex items-center justify-center">
                  <IconMiniCheck class="w-[12px] h-[12px] text-white" />
                </div>
                <H5 class="!text-green-500 !font-semibold !tracking-normal">
                  When to use
                </H5>
              </div>
              <Paragraph size="md" class="!font-medium">
                Use our managed upload system only when:
              </Paragraph>
              <ul class="flex flex-col gap-[8px]">
                <li v-for="item in items.do" :key="item" class="flex items-center justify-start gap-[8px]">
                  <IconMiniCheck class="w-5 h-5 text-green-500" />
                  <Paragraph size="xs">
                    {{ item }}
                  </Paragraph>
                </li>
              </ul>
              <Paragraph size="xs">
                The managed upload system should only be used if you want to manage the full photoshoot process, like a photographer would normally do.
              </Paragraph>
              <ButtonPrimary class="w-full !py-2.5" @click="next">
                <span class="hidden md:block">Yes, I will manage the photoshoot</span>
                <span class="block md:hidden">Yes, let's start</span>
                <IconArrowRight class="w-4 h-4 ml-1.5" />
              </ButtonPrimary>
            </div>
          </Card>
          <!-- DONT-->
          <Card>
            <div class="flex flex-col gap-[14px] p-2">
              <div class="flex items-center gap-2">
                <div class="w-[20px] h-[20px] bg-red-500 rounded-full flex items-center justify-center">
                  <IconMiniCheck class="w-[12px] h-[12px] text-white" />
                </div>
                <H5 class="!text-red-500 !font-semibold !tracking-normal">
                  When not to use
                </H5>
              </div>
              <Paragraph size="md" class="!font-medium">
                Don’t use our managed upload shoot system if:
              </Paragraph>
              <ul class="flex flex-col gap-[8px]">
                <li v-for="item in items.dont" :key="item" class="flex items-center justify-start gap-[8px]">
                  <IconMiniXMark class="w-5 h-5 text-red-500" />
                  <Paragraph size="xs">
                    {{ item }}
                  </Paragraph>
                </li>
              </ul>
              <Paragraph size="xs">
                We’ve created a easy to use onboarding process for members where they can capture their selfies in minutes. We recommend using this system.
              </Paragraph>
              <ButtonWhite class="w-full !py-2.5" @click="$router.push(localePath('/app/admin'))">
                <span>Take me back</span>
              </ButtonWhite>
            </div>
          </Card>
        </div>
      </div>
      <Modal v-if="showDemoModal" title="Managed upload demo" @close="showDemoModal = false">
        <VideoPlayer src="https://cdn2.headshotpror2.com/demo-managed-upload.mp4" />
      </Modal>
    </section>
  </PostcheckoutWrapper>
</template>

<script>
import PostcheckoutMixin from '../../../mixins/PostcheckoutMixin'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
import H5 from '@/components/landingpage/common/H5.vue'
import VideoPlayer from '@/components/landingpage/common/VideoPlayer.vue'
export default {
  components: {
    Paragraph,
    H5,
    VideoPlayer
  },
  mixins: [PostcheckoutMixin],
  layout: 'protected',
  data () {
    return {
      isLoading: false,
      showDemoModal: false,
      items: {
        do: [
          'You do not want your members to create an account',
          'You will capture or receive the photos yourself',
          'You will own the full process',
          'Your team members only get to see the final results'
        ],
        dont: [
          'You want your team to upload their selfies themselves',
          'You don\'t want to manage the photo shoot',
          'You want your members to have their own account',
          'You want your members to have their own account'
        ]
      }
    }
  },
  mounted () {
    this.$posthog.capture('$post_checkout:guided-shoot')
  },
  methods: {
    next () {
      if (this.isLoading) {
        return
      }

      this.isLoading = true
      this.$axios.$post('/onboarding/start')
        .then((res) => {
          if (res.data?.modelId) {
            this.$store.commit('onboardingv2/SET_MODEL_ID', res.data.modelId)
            this.$router.push({ path: this.localePath('/app/upload/photos') })
          } else {
            this.$toast.warning('Something went wrong. Please, try again.')
          }
          if (res?.data?.package && !this.isTeamMember) {
            this.$store.commit('user/SET_PACKAGES', [res.data.package])
          }
        })
        .catch((error) => {
          console.log('error', error)
          this.handleError(error)
        })
        .finally(() => {
          this.isLoading = false
        })
    }
  }
}
</script>

<style>
  @media screen and (max-width: 767px) {
    .chat-button {
      bottom: 6rem;
    }
  }
</style>
