<template>
  <PostcheckoutWrapper alignment="top" :back-url="localePath('/app/upload/intro')">
    <template #left>
      <PostcheckoutNavWhiteButton @click="$router.push(localePath('/app/upload/intro'))">
        {{ $t('Back') }}
      </PostcheckoutNavWhiteButton>
    </template>
    <template #right>
      <PostcheckoutNavBlueButton :disabled="!hasEnoughPhotos" @click="goToNextStep">
        {{ $t('Continue') }}
      </PostcheckoutNavBlueButton>
    </template>
    <section v-if="!hasEnoughPhotos" class="relative bg-gray-50">
      <div class="fixed inset-0 -z-1">
        <div class="absolute inset-y-0 left-0 w-full lg:w-1/2 bg-white" />
      </div>

      <div class="relative mx-auto max-w-7xl lg:grid lg:grid-cols-2">
        <div class="px-4 py-8 bg-white lg:px-8 lg:py-12 xl:pr-12">
          <PostcheckoutStepper :step="2" />
          <div class="mt-4 sm:mt-6">
            <h1 v-if="goodPhotos.length === 0" class="text-xl font-bold tracking-tight sm:text-2xl lg:text-3xl text-primary-500">
              {{ $t('uploadAtLeast', { photos: minimumPhotos }) }}
            </h1>
            <h1 v-else-if="!hasEnoughPhotos" class="text-xl font-bold tracking-tight sm:text-2xl lg:text-3xl text-primary-500">
              <template v-if="goodFacePhotos.length < (minimumPhotos - minimumFullBodyPhotos)">
                {{ $t('uploadMoreToContinue', { photos: minimumPhotos - minimumFullBodyPhotos - goodFacePhotos.length }) }}
              </template>
              <template v-else-if="goodFullBodyPhotos.length < (minimumFullBodyPhotos)">
                {{ $t('uploadMoreBodyPhotosToContinue', { photos: minimumFullBodyPhotos - goodFullBodyPhotos.length }) }}
              </template>
            </h1>
            <p class="mt-2 text-base font-medium text-gray-500">
              {{ $t('chooseCarefully') }}
            </p>

            <div class="mt-4">
              <button type="button" class="text-sm font-medium underline text-primary-500" @click="openRequirementsModal">
                {{ $t('Show photo requirements') }}
              </button>
            </div>

            <!-- Helpful microcopy for phone upload visibility -->
            <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div class="flex items-start gap-3">
                <svg class="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clip-rule="evenodd" />
                </svg>
                <div class="text-left">
                  <p class="text-sm font-medium text-blue-800">
                    {{ $t('📱 Photos from your phone will appear here automatically') }}
                  </p>
                  <p class="text-sm text-blue-700 mt-1">
                    {{ $t('🔄 If you just uploaded via phone, refresh this page to see your photos. ⏱️ Photo processing may take a few moments.') }}
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="mt-8 space-y-4">
            <PostcheckoutUploadImagesButton
              :text="$t('Capture selfies with your phone')"
              :mobile-text="$t('Start capturing selfies')"
              :recommended="true"
              @click="showQrModal = true"
            >
              <svg
                aria-hidden="true"
                class="text-gray-500 size-6"
                viewBox="0 0 27 27"
                fill="currentColor"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M19.125 2.25C20.3112 2.25 21.2829 3.16786 21.3688 4.33208L21.375 4.5V22.5C21.375 23.6862 20.4572 24.6579 19.2929 24.7438L19.125 24.75H7.875C6.68884 24.75 5.71706 23.8322 5.63117 22.6679L5.625 22.5V4.5C5.625 3.31384 6.54286 2.34206 7.70708 2.25617L7.875 2.25H19.125ZM19.125 4.5H7.875V22.5H19.125V4.5ZM14.0625 18C14.3386 18 14.5683 18.199 14.6159 18.4614L14.625 18.5625V19.6875C14.625 19.9636 14.426 20.1933 14.1636 20.2409L14.0625 20.25H12.9375C12.6614 20.25 12.4317 20.051 12.3841 19.7886L12.375 19.6875V18.5625C12.375 18.2864 12.574 18.0567 12.8364 18.0091L12.9375 18H14.0625Z"
                />
              </svg>
            </PostcheckoutUploadImagesButton>

            <div class="relative">
              <div class="absolute inset-0 flex items-center" aria-hidden="true">
                <div class="w-full border-t border-gray-200" />
              </div>
              <div class="relative flex justify-center">
                <span class="bg-white px-2.5 text-sm font-medium text-gray-400">
                  {{ $t('Or upload from one of your devices') }}
                </span>
              </div>
            </div>

            <PostcheckoutManuallyUpload />
            <PostcheckoutUploadFromDropbox />
          </div>
        </div>

        <PostcheckoutRequirements v-if="photos.length === 0" class="hidden lg:block" />
        <PostcheckoutUploadSummary v-else />
      </div>
    </section>
    <PostcheckoutYouUploadedEnoughPhotos v-else />

    <portal to="modal">
      <Popup v-if="showQrModal" size="md" @closeModal="showQrModal = false">
        <PostcheckoutQrModal @closeModal="showQrModal = false" />
      </Popup>
    </portal>
  </Postcheckoutwrapper>
</template>

<script>
import PostcheckoutMixin from '../../../mixins/PostcheckoutMixin'

export default {
  mixins: [PostcheckoutMixin],
  layout: 'protected',
  data () {
    return {
      showQrModal: false
    }
  },
  computed: {
    chatBottom () {
      return this.goodPhotos.length < this.minimumPhotos ? '1rem' : '6rem'
    },
    photos () {
      return this.$store.state.onboarding.photos
    },
    hasEnoughPhotos () {
      return this.$store.getters['onboarding/hasEnoughTotalPhotos']
    }
  },
  watch: {
    goodPhotos (newVal) {
      if (newVal.length >= this.minimumPhotos) {
        document.body.classList.add('with-good-photos')
      } else {
        document.body.classList.remove('with-good-photos')
      }
    }
  },
  mounted () {
    this.$posthog.capture('$post_checkout:upload')
    this.startFetchInterval()
    this.$posthog.startSessionRecording(true)
  },
  beforeDestroy () {
    document.body.classList.remove('with-good-photos')
  },
  methods: {
    goToNextStep () {
      this.$router.push(this.localePath('/app/upload/personal-info'))
    }
  }
}
</script>

<style>
  @media screen and (max-width: 767px) {
    .with-good-photos .chat-button {
      bottom: 6rem;
    }
  }
</style>

<i18n>
  {
    "en": {
      "uploadAtLeast": "Upload at least {photos} photos",
      "uploadMoreToContinue": "Upload {photos} more photos to continue",
      "uploadMoreBodyPhotosToContinue": "Upload {photos} more body photos to continue",
      "chooseCarefully": "Choose carefully! The quality of the photos you upload here will have a direct impact on the quality of the headshots you get back.",
      "Show photo requirements": "Show photo requirements",
      "Or upload from one of your devices": "Or upload from one of your devices",
      "Capture selfies with your phone": "Capture selfies with your phone",
      "Start capturing selfies": "Start capturing selfies",
      "📱 Photos from your phone will appear here automatically": "📱 Photos from your phone will appear here automatically",
      "🔄 If you just uploaded via phone, refresh this page to see your photos. ⏱️ Photo processing may take a few moments.": "🔄 If you just uploaded via phone, refresh this page to see your photos. ⏱️ Photo processing may take a few moments."
    },
    "es": {
      "uploadAtLeast": "Sube al menos {photos} fotos",

      "uploadMoreToContinue": "Sube {photos} fotos más para continuar",
      "uploadMoreBodyPhotosToContinue": "Sube {photos} fotos más del cuerpo para continuar",
      "chooseCarefully": "¡Elige con cuidado! La calidad de las fotos que subas aquí tendrá un impacto directo en la calidad de los retratos que recibas.",
      "Show photo requirements": "Mostrar requisitos para las fotos",
      "Or upload from one of your devices": "O sube desde uno de tus dispositivos",
      "Capture selfies with your phone": "Sácate selfies con tu móvil",
      "Start capturing selfies": "Sácate selfies con tu móvil",
      "📱 Photos from your phone will appear here automatically": "📱 Las fotos de tu móvil aparecerán aquí automáticamente",
      "🔄 If you just uploaded via phone, refresh this page to see your photos. ⏱️ Photo processing may take a few moments.": "🔄 Si acabas de subir fotos desde tu móvil, actualiza esta página para verlas. ⏱️ El procesamiento de fotos puede tardar unos momentos."
    },
    "de": {
      "uploadAtLeast": "Lade mindestens {photos} Fotos hoch",
      "uploadMoreToContinue": "Noch {photos} Fotos hochladen, um fortzufahren",
      "uploadMoreBodyPhotosToContinue": "Noch {photos} Körperfotos hochladen, um fortzufahren",
      "chooseCarefully": "Wähle sorgfältig! Die Qualität deiner Fotos beeinflusst direkt die Qualität deiner professionellen Bewerbungsfotos.",
      "Show photo requirements": "Foto-Anforderungen anzeigen",
      "Or upload from one of your devices": "Oder von deinen Geräten hochladen",
      "Capture selfies with your phone": "Selfies mit dem Handy aufnehmen",
      "Start capturing selfies": "Selfies aufnehmen",
      "📱 Photos from your phone will appear here automatically": "📱 Fotos von deinem Handy erscheinen hier automatisch",
      "🔄 If you just uploaded via phone, refresh this page to see your photos. ⏱️ Photo processing may take a few moments.": "🔄 Wenn du gerade über das Handy hochgeladen hast, aktualisiere diese Seite, um deine Fotos zu sehen. ⏱️ Die Fotoverarbeitung kann einige Momente dauern."
    }
  }
</i18n>
