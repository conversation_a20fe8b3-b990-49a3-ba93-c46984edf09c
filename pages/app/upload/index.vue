<template>
  <PrecheckoutWrapper>
    <LoadingWrapper :is-loading="true">
      <span />
    </LoadingWrapper>
  </PrecheckoutWrapper>
</template>

<script>
import PostcheckoutMixin from '../../../mixins/PostcheckoutMixin'

export default {
  mixins: [PostcheckoutMixin],
  layout: 'protected',
  mounted () {
    if (this.$route.query.payment === 'failed') {
      this.handleError(this.$t('Payment failed, please try again'))
    }

    this.$store.dispatch('onboarding/fetchModelInformation').then(() => {
      if (this.isTeamMember) {
        // this.$posthog.startSessionRecording()
      }
      this.$router.push({ path: this.localePath('/app/upload/intro') })
    }).catch((error) => {
      if (error.response.data?.errorMessage === 'Your organization has run out of credits.') {
        const errorPopup = this.$popup.show({
          title: 'Your organization has run out of credits.',
          message: 'Please contact your administrator to purchase more credits before you can continue.',
          buttonText: 'Ok, take me home',
          onButtonClick: () => {
            this.$popup.hide(errorPopup)
            this.$router.push({ path: this.localePath('/') })
          }
        })
      }
    })

    this.handleTeamLeadLogic()
  },
  methods: {
    handleTeamLeadLogic () {
      const organization = this.$store.state.organization.organization
      if (this.isTeamLead && organization) {
        if (!organization?.allowedOptions || !organization?.allowedOptions?.styles?.length) {
          this.$router.push({ path: this.localePath('/app/admin/team/new?step=1') })
          return
        }

        if (organization?.credits <= 0 && organization?.creditLog?.length === 0) {
          this.$router.push({ path: this.localePath('/app/admin/team/new?step=3') })
        }
      }
    }
  }
}
</script>

<i18n>
  {
    "en": {
      "Payment failed, please try again": "Payment failed, please try again"
    },
    "es": {
      "Payment failed, please try again": "El pago falló, por favor inténtalo de nuevo"
    },
    "de": {
      "Payment failed, please try again": "Zahlung fehlgeschlagen, bitte versuche es erneut"
    }
  }
</i18n>
