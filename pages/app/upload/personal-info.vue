<template>
  <PostcheckoutWrapper alignment="top">
    <template #left>
      <PostcheckoutNavWhiteButton @click="$router.push(localePath('/app/upload/photos'))">
        {{ $t('Back') }}
      </PostcheckoutNavWhiteButton>
    </template>
    <template #right>
      <PostcheckoutNavBlueButton :disabled="!canSubmit" @click="submit">
        {{ $t('Continue') }}
      </PostcheckoutNavBlueButton>
    </template>
    <section class="relative pt-8 pb-28 sm:pt-12 md:pb-12">
      <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8">
        <PostcheckoutStepper :step="3" classes="md:justify-center" />

        <div class="mx-auto mt-4 text-left sm:mt-6 md:max-w-xl md:text-center">
          <h1 class="text-xl font-bold tracking-tight sm:text-2xl lg:text-3xl text-primary-500">
            {{ $t('Add your personal info') }}
          </h1>
          <p class="mt-2 text-base font-medium text-gray-500 md:max-w-xl md:mx-auto">
            <span class="md:hidden">
              {{ $t('We need some information about you to train our AI. This information will not be shared with anyone and is deleted after your photo shoot.') }}
            </span>
            <span class="hidden md:block">
              {{ $t('This information will help guide our AI towards creating headshots that look like you. It will be deleted after your headshots are completed.') }}
            </span>
          </p>
        </div>

        <div
          class="p-4 mt-8 bg-white border border-gray-200 rounded-lg shadow-sm md:mx-auto md:max-w-md sm:p-6 md:p-8"
        >
          <form class="space-y-4" @submit.prevent="submit">
            <div>
              <Input v-model="info.name" data-testid="name-input" :label="$t('Name *')" char-regex="a-z\sA-Z\-_.," />
            </div>

            <div>
              <InputSelect v-model="info.age" data-testid="age-input" :options="ageList" :label="$t('Age *')" />
            </div>

            <div>
              <InputSelect v-model="info.ethnicity" data-testid="ethnicity-input" :options="ethnicityList" :label="$t('Ethnicity')" />
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <InputSelect v-model="info.height" data-testid="height-input" :options="heightList" :label="$t('Height')" />
              </div>
              <div>
                <InputSelect v-model="info.weight" data-testid="weight-input" :options="weightList" :label="$t('Weight')" />
              </div>
            </div>

            <div class="relative">
              <InputSelect v-model="info.bodyType" data-testid="body-type-input" :options="bodyTypeList" :label="$t('Body type')" />
              <div class="absolute top-0 right-0 z-10">
                <Tooltip>
                  <img src="@/assets/img/body-types.jpg" alt="" class="w-full h-auto">
                </Tooltip>
              </div>
            </div>

            <div>
              <p
                class="after:content-['*'] after:ml-0.5 after:text-primary-500 text-sm font-medium text-primary-500"
              >
                {{ $t('Eye color') }}
              </p>

              <div class="flex items-center flex-wrap mt-2 gap-2.5">
                <PostcheckoutEyeColorButton
                  v-for="eyeColor in eyeColors"
                  :key="eyeColor.id"
                  v-model="info.eyeColor"
                  :data-testid="`eye-color-input-${eyeColor.id}`"
                  :identifier="eyeColor.id"
                  :label="eyeColor.label"
                  :background-color="eyeColor.color"
                />
              </div>
            </div>

            <div>
              <p
                class="after:content-['*'] after:ml-0.5 after:text-primary-500 text-sm font-medium text-primary-500"
              >
                {{ $t('Gender') }}
              </p>

              <div class="flex mt-2 gap-2.5">
                <PostcheckoutGenderButton v-model="info.gender" identifier="male" :text="$t('Male')" data-testid="gender-input-male">
                  <IconMale class="size-5 -mb-0.5" />
                </PostcheckoutGenderButton>
                <PostcheckoutGenderButton v-model="info.gender" identifier="female" :text="$t('Female')" data-testid="gender-input-female">
                  <IconFemale class="size-5 -mb-0.5" />
                </PostcheckoutGenderButton>
              </div>
            </div>

            <div class="relative">
              <div class="absolute top-0 right-0 z-10">
                <Tooltip>
                  Please note your results won't follow these exact percentages, but we'll do what we can to follow your preferences.
                </Tooltip>
              </div>
              <InputSelect v-model="info.glasses" data-testid="glasses-input" :options="glassesList" :label="$t('Wear glasses in headshots?')" />
            </div>

            <div v-if="showHeadshotFormatOption">
              <p
                class="after:content-['*'] after:ml-0.5 after:text-primary-500 text-sm font-medium text-primary-500"
              >
                {{ $t('Headshot format') }}
              </p>

              <div class="flex items-center flex-wrap mt-2 gap-2.5">
                <PostcheckoutHeadshotFormatButton
                  v-for="format in headshotFormats"
                  :key="format.id"
                  v-model="info.headshotFormat"
                  :data-testid="`headshot-format-input-${format.id}`"
                  :identifier="format.id"
                  :label="format.label"
                />
              </div>
            </div>
          </form>
        </div>

        <div class="justify-end mt-4 hidden md:flex md:max-w-md md:mx-auto">
          <LoadingWrapper :is-loading="isLoading">
            <button
              type="button"
              class="text-sm inline-flex font-medium text-white rounded-lg shadow-sm bg-primary-500 pt-2 pb-2.5 px-3.5 border border-transparent transition-all duration-150 hover:bg-opacity-90 gap-1.5 items-center justify-center disabled:bg-opacity-20 sm:w-auto"
              :disabled="!canSubmit"
              data-testid="continue-button"
              @click="submit"
            >
              {{ $t('Continue') }}
            </button>
          </LoadingWrapper>
        </div>
      </div>
      <div class="fixed inset-x-0 bottom-0 py-4 bg-white border-t border-gray-100 shadow-sm md:hidden">
        <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8">
          <LoadingWrapper :is-loading="isLoading">
            <button
              type="button"
              class="text-base inline-flex w-full font-medium text-white rounded-lg shadow-sm bg-primary-500 gap-1.5 pt-2 pb-2.5 px-3.5 border border-transparent transition-all duration-150 hover:bg-opacity-90 items-center justify-center disabled:bg-opacity-20"
              :disabled="!canSubmit"
              @click="submit"
            >
              {{ $t('Continue') }}
            </button>
          </LoadingWrapper>
        </div>
      </div>
    </section>
  </PostcheckoutWrapper>
</template>

<script>
import PostcheckoutMixin from '../../../mixins/PostcheckoutMixin'

export default {
  mixins: [PostcheckoutMixin],
  layout: 'protected',
  data () {
    return {
      info: {
        name: '',
        age: '',
        ethnicity: '',
        eyeColor: 'hazel',
        gender: 'male',
        height: null,
        weight: null,
        bodyType: null,
        glasses: 'none',
        headshotFormat: '3:2'
      },
      showAdvanced: false,
      isLoading: false,
      headshotFormats: [
        {
          id: '3:2',
          label: '3:2 (Standard)'
        },
        {
          id: '4:5',
          label: '4:5 (Portrait)'
        }
      ]
    }
  },
  computed: {
    canSubmit () {
      return this.info.name.trim() !== '' && this.info.age.trim() !== '' && this.info.gender.trim() !== ''
    },
    currentPackage () {
      return this.$store.state?.user?.packages[0] || null
    },
    showHeadshotFormatOption () {
      return this.currentPackage === 'large'
    }
  },
  mounted () {
    if (this.photos.length < this.minimumPhotos) {
      this.$router.push(this.localePath('/app/upload/photos'))
      return
    }

    const { title, selectedAge, selectedSex, ethnicity, selectedEyeColor, selectedHeight, selectedBodyType, selectedGlasses, selectedWeight, headshotFormat } = this.$store.state.onboarding
    if (title) {
      this.info.name = title
    }
    if (selectedAge) {
      this.info.age = selectedAge
    }
    if (ethnicity) {
      this.info.ethnicity = ethnicity
    }
    if (selectedEyeColor) {
      this.info.eyeColor = selectedEyeColor
    }
    if (selectedSex) {
      this.info.gender = selectedSex
    }
    if (selectedHeight) {
      this.info.height = selectedHeight
    }
    if (selectedBodyType) {
      this.info.bodyType = selectedBodyType
    }
    if (selectedGlasses) {
      this.info.glasses = selectedGlasses
    }
    if (selectedWeight) {
      this.info.weight = selectedWeight
    }
    if (headshotFormat) {
      this.info.headshotFormat = headshotFormat
    }

    this.$posthog.capture('$post_checkout:personal_info')
  },
  methods: {
    submit () {
      if (!this.canSubmit || this.isLoading) {
        return
      }

      this.isLoading = true

      this.$store.dispatch('onboarding/savePersonalInfo', this.info)
        .then(() => {
          this.$router.push(this.localePath('/app/upload/select-style'))
        })
        .catch((error) => {
          this.handleError(error)
        })
        .finally(() => {
          this.isLoading = false
        })
    }
  }
}
</script>

<style>
  @media screen and (max-width: 767px) {
    .chat-button {
      bottom: 6rem;
    }
  }
</style>

<i18n>
  {
    "en": {
      "Add your personal info": "Add your personal info",
      "We need some information about you to train our AI. This information will not be shared with anyone and is deleted after your photo shoot.": "We need some information about you to train our AI. This information will not be shared with anyone and is deleted after your photo shoot.",
      "This information will help guide our AI towards creating headshots that look like you. It will be deleted after your headshots are completed.": "This information will help guide our AI towards creating headshots that look like you. It will be deleted after your headshots are completed.",
      "Name *": "Name *",
      "Age *": "Age *",
      "Ethnicity": "Ethnicity",
      "Eye color": "Eye color",
      "Gender": "Gender",
      "Headshot format": "Headshot format",
      "Recommended": "Recommended",
      "Wear glasses in headshots?": "Wear glasses in headshots?"
    },
    "es": {
      "Add your personal info": "Añade tu información personal",
      "We need some information about you to train our AI. This information will not be shared with anyone and is deleted after your photo shoot.": "Necesitamos información sobre ti para entrenar a nuestra IA. Esta información no se compartirá con nadie y se eliminará después de tu sesión de fotos.",
      "This information will help guide our AI towards creating headshots that look like you. It will be deleted after your headshots are completed.": "Esta información ayudará a nuestra IA a crear fotos profesionales que se parezcan a ti. Se eliminará después de que tus fotos estén listas.",
      "Name *": "Nombre *",
      "Age *": "Edad *",
      "Ethnicity": "Etnia",
      "Eye color": "Color de ojos",
      "Gender": "Género",
      "Headshot format": "Formato de foto",
      "Recommended": "Recomendado",
      "Wear glasses in headshots?": "¿Usar gafas en las fotos?"
    },
    "de": {
      "Add your personal info": "Persönliche Angaben hinzufügen",
      "We need some information about you to train our AI. This information will not be shared with anyone and is deleted after your photo shoot.": "Wir benötigen einige Informationen über dich, um unsere KI zu trainieren. Diese Daten werden mit niemandem geteilt und nach deinem Fotoshooting gelöscht.",
      "This information will help guide our AI towards creating headshots that look like you. It will be deleted after your headshots are completed.": "Diese Informationen helfen unserer KI dabei, Bewerbungsfotos zu erstellen, die wie du aussehen. Sie werden gelöscht, sobald deine Bewerbungsfotos fertig sind.",
      "Name *": "Name *",
      "Age *": "Alter *",
      "Ethnicity": "Ethnie",
      "Eye color": "Augenfarbe",
      "Gender": "Geschlecht",
      "Headshot format": "Foto-Format",
      "Recommended": "Empfohlen",
      "Wear glasses in headshots?": "Gläser in Bewerbungsfotos?"
    }
  }
</i18n>
