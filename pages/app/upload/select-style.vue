<template>
  <PostcheckoutWrapper alignment="top">
    <template #left>
      <PostcheckoutNavWhiteButton @click="$router.push(localePath('/app/upload/personal-info'))">
        {{ $t('Back') }}
      </PostcheckoutNavWhiteButton>
    </template>
    <template #right>
      <PostcheckoutNavBlueButton :disabled="!finished" @click="goToNextStep">
        {{ $t('Continue') }}
      </PostcheckoutNavBlueButton>
    </template>
    <section class="relative pt-8 pb-32 sm:pt-12 md:pb-12">
      <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8">
        <PostcheckoutStepper :step="4" classes="md:justify-center" />

        <div v-if="finished" class="mx-auto mt-4 text-left sm:mt-6 md:max-w-xl md:text-center">
          <h1 class="text-xl font-bold tracking-tight sm:text-2xl lg:text-3xl text-primary-500">
            {{ $t('Great choices! Ready to move on?') }}
          </h1>
          <p class="mt-2 text-base font-medium text-gray-500 md:max-w-xl md:mx-auto">
            {{ $t('We\'re ready to create your headshots now! Double check your selections below then click the button to move on to the final step.') }}
          </p>
        </div>
        <div v-else class="mx-auto mt-4 text-left sm:mt-6 md:max-w-xl md:text-center">
          <h1 class="text-xl font-bold tracking-tight sm:text-2xl lg:text-3xl text-primary-500">
            {{ $t('selectStyles', { styles: total }) }}
          </h1>
          <p class="mt-2 text-base font-medium text-gray-500 md:max-w-xl md:mx-auto">
            {{ $t('Choose which background and clothing styles you want to wear for your headshots or let us choose for you.') }}
          </p>
        </div>

        <div class="max-w-screen-xl mx-auto mt-8 sm:mt-12">
          <div class="gap-5 xl:flex" :class="{ 'justify-center': finished }">
            <div v-if="!finished" class="flex-1 p-5 bg-white border border-gray-200 rounded-lg shadow-sm">
              <div class="flex items-center justify-between gap-2">
                <div class="flex flex-col space-y-0.5">
                  <p class="flex-1 text-base font-medium tracking-tight text-primary-500">
                    {{ $t('All backdrops') }}
                  </p>
                </div>
                <div class="flex gap-4 justify-end items-center">
                  <ButtonWhite v-if="!isTeamMember" class="hidden md:flex" size="sm" @click="automaticallySelect">
                    {{ $t('Choose for me') }}
                  </ButtonWhite>

                  <PostcheckoutProgressIndicator class="hidden md:block" :current="selectedStyles.length" :total="total" bg="bg-gray-400" />
                </div>
              </div>

              <hr class="mt-4 border-gray-200">

              <div class="grid grid-cols-2 mt-5 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-5">
                <template v-for="(style, index) in styles">
                  <PostcheckoutStyleItem
                    v-if="style?._id"
                    :key="`style-${style._id}-${index}`"
                    :item="style"
                    :selected-styles="selectedStyles"
                    :popular="index < 3"
                    data-testid="style-item"
                    @select="selectStyle($event, style._id)"
                  />
                </template>
              </div>
            </div>

            <div class="w-full p-5 mt-5 bg-white border border-gray-200 rounded-lg shadow-sm xl:max-w-md xl:mt-0">
              <div class="flex justify-between items-center">
                <p class="text-base font-medium tracking-tight text-primary-500">
                  {{ $t('Selected combinations') }}
                </p>
                <button
                  type="button"
                  class="text-sm inline-flex font-medium text-white rounded-lg shadow-sm bg-primary-500 gap-1.5 pt-1 pb-1 px-2 border border-transparent transition-all duration-150 hover:bg-opacity-90 items-center justify-center disabled:bg-opacity-20"
                  :disabled="!finished"
                  data-testid="continue-button"
                  @click="goToNextStep"
                >
                  {{ $t('Continue') }}
                </button>
              </div>

              <hr class="mt-4 border-gray-200">

              <div class="mt-6">
                <div class="flex items-center justify-between gap-4">
                  <p class="flex-1 text-base font-medium tracking-tight text-gray-500">
                    {{ $t('Selected styles') }}
                  </p>

                  <PostcheckoutProgressIndicator class="hidden md:block" :current="selectedStyles.length" :total="total" bg="bg-gray-400" />
                </div>

                <div class="space-y-2.5 mt-3">
                  <div
                    v-for="(item, index) in selectedStyles"
                    :key="`${item.style}-${item.clothing}-${index}`"
                    class="flex items-center justify-between gap-3 px-3 pt-1.5 pb-2 bg-white border border-gray-200 rounded-lg shadow-sm"
                    data-testid="selected-style-item"
                  >
                    <p class="text-base font-medium tracking-tight text-primary-500">
                      {{ styleName(item.style) }}
                    </p>
                    <p class="text-sm font-medium text-gray-500">
                      {{ clothingName(item.clothing) }}
                    </p>
                    <button type="button" class="p-1 ml-auto text-red-500 hover:text-red-600" @click="removeStyle(index)">
                      <span class="sr-only">
                        {{ $t('Remove') }}
                      </span>
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-5">
                        <path
                          d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z"
                        />
                      </svg>
                    </button>
                  </div>
                  <div
                    v-for="i in Math.max(0, total - (selectedStyles?.length || 0))"
                    :key="i"
                    class="flex items-center justify-between gap-3 px-3 pt-2.5 pb-3 border-2 border-gray-300 border-dashed rounded-lg bg-gray-50"
                    data-testid="style-not-selected-item"
                  >
                    <p class="text-sm font-medium text-gray-500">
                      {{ $t('Style not yet selected') }}
                    </p>
                  </div>
                </div>
              </div>

              <div class="mt-6">
                <p class="text-base font-medium tracking-tight text-gray-500">
                  {{ $t('Preselected by us') }}
                </p>

                <div class="grid grid-cols-1 mt-3 gap-2.5 md:grid-cols-2">
                  <div
                    v-for="option in preselectedOptions"
                    :key="option.style + '_' + option.clothing"
                    class="flex flex-col items-left px-3 pt-2 pb-2.5 bg-white border border-gray-200 rounded-lg shadow-sm"
                  >
                    <p class="text-sm font-medium tracking-tight text-primary-500">
                      {{ option.style }}
                    </p>
                    <p class="text-xs font-medium text-gray-500">
                      {{ option.clothing }}
                    </p>
                  </div>
                </div>
              </div>

              <div class="hidden mt-6 lg:block">
                <button
                  type="button"
                  class="text-base inline-flex w-full font-medium text-white rounded-lg shadow-sm bg-primary-500 gap-1.5 pt-2 pb-2.5 px-3.5 border border-transparent transition-all duration-150 hover:bg-opacity-90 items-center justify-center disabled:bg-opacity-20"
                  :disabled="!finished"
                  data-testid="continue-button-lg"
                  @click="goToNextStep"
                >
                  {{ $t('Continue to next step') }}
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    class="size-5 -mb-0.5"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M3 10a.75.75 0 0 1 .75-.75h10.638L10.23 5.29a.75.75 0 1 1 1.04-1.08l5.5 5.25a.75.75 0 0 1 0 1.08l-5.5 5.25a.75.75 0 1 1-1.04-1.08l4.158-3.96H3.75A.75.75 0 0 1 3 10Z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="fixed inset-x-0 bottom-0 py-4 bg-white border-t border-gray-100 shadow-sm md:hidden">
        <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8">
          <button
            type="button"
            class="text-base inline-flex w-full font-medium text-white rounded-lg shadow-sm bg-primary-500 gap-1.5 pt-2 pb-2.5 px-3.5 border border-transparent transition-all duration-150 hover:bg-opacity-90 items-center justify-center disabled:bg-opacity-20"
            :disabled="!finished"
            @click="goToNextStep"
          >
            {{ $t('Continue') }}
          </button>
        </div>
      </div>
    </section>
  </PostcheckoutWrapper>
</template>

<script>
import PostcheckoutMixin from '../../../mixins/PostcheckoutMixin'

export default {
  mixins: [PostcheckoutMixin],
  layout: 'protected',
  data () {
    return {
      total: 3
    }
  },
  computed: {
    preselectedOptions () {
      const preselectedOptions = this.$store.state.onboarding.preselectedOptions
      // TeamMembers get their non-selected styles prefilled until a max of 8 styles
      if (this.isTeamMember) {
        return preselectedOptions.slice(0, 8 - this.selectedStyles.length)
      }
      return preselectedOptions
    },
    finished () {
      return this.selectedStyles.length >= this.total
    },
    organization () {
      return this.$store.state.organization?.organization
    }
  },
  watch: {
    selectedStyles () {
      if (this.selectedStyles.length > this.total) {
        for (const i in this.selectedStyles) {
          if (i >= this.total) {
            this.$store.commit('onboarding/REMOVE_STYLE', i)
          }
        }
      }
    }
  },
  mounted () {
    if (!this.$store.state.onboarding.selectedSex) {
      if (this.$route.query?.test !== 'true') {
        this.$router.push(this.localePath('/app/upload/personal-info'))
        return
      }
    }

    this.$store.dispatch('onboarding/fetchPreselectedOptions')
    this.$posthog.capture('$post_checkout:select_style')

    if (this.isTeamMember) {
      // Set total pickable styles to the number of styles in the organization
      this.total = this.organization.allowedOptions.styles.length
    } else {
      const packageId = this.$store.state?.user?.packages[0]
      if (packageId) {
        const selectedPackage = this.$store.state.packages[packageId]
        if (!selectedPackage) {
          const fallback = {
            tiny: 1,
            small: 2,
            medium: 6,
            large: 10
          }
          this.total = fallback[packageId]
        } else {
          this.total = selectedPackage?.meta?.styles
        }
      }
    }

    // Set selectedStyles based on localstorage if exists
    try {
      const selectedStylesFromStorage = JSON.parse(localStorage.getItem('onboarding.styles'))

      if (selectedStylesFromStorage) {
        // Check first if the styles and clothing are valid
        const validStyles = this.styles.filter((style) => {
          return selectedStylesFromStorage.some(selectedStyle => selectedStyle.style === style._id)
        })
        const validClothing = this.clothingItems.filter((clothing) => {
          return selectedStylesFromStorage.some(selectedStyle => selectedStyle.clothing === clothing._id)
        })

        // If valid, set the selectedStyles
        if (validStyles.length > 0 && validClothing.length > 0) {
          this.selectedStyles = selectedStylesFromStorage
        }
      }
    } catch (err) {
      console.error(err)
    }
    this.reloadStylesAndClothingIfMissing()
  },
  methods: {
    reloadStylesAndClothingIfMissing () {
      if (this.$store.state.styles.length === 0) {
        this.$store.dispatch('getStyles')
      }
      if (this.$store.state.clothing.length === 0) {
        this.$store.dispatch('getClothing')
      }
    },
    selectStyle (clothing, style) {
      try {
        if (this.selectedStyles?.length >= this.total) {
          throw new Error(this.$t('youCanOnlySelect', { total: this.total }))
        }

        this.$store.commit('onboarding/SET_SELECTED_STYLE', {
          style,
          clothing
        })
        this.$toast.success(this.$t('Style added.'))
        window?.scrollTo({
          top: 0,
          behavior: 'smooth'
        })
        // Update storage
        localStorage.setItem('onboarding.styles', JSON.stringify(this.selectedStyles))
      } catch (err) {
        this.handleError(err)
      }
    },
    removeStyle (index) {
      if (!confirm(this.$t('Are you sure you want to remove this style?'))) {
        return
      }

      this.$store.commit('onboarding/REMOVE_STYLE', index)
      this.$toast.success(this.$t('Style removed.'))

      // Update storage
      localStorage.setItem('onboarding.styles', JSON.stringify(this.selectedStyles))
    },
    selectOption (option) {
      for (const style of this.selectedStyles) {
        if (style.style === option.style._id && style.clothing === option.clothing._id) {
          return
        }
      }

      this.selectStyle(option.clothing._id, option.style._id)
    },
    automaticallySelect () {
      const options = []
      let lastClothing = 0
      for (const style of this.styles) {
        const clothing = this.clothingForGender[lastClothing]
        options.push({
          style,
          clothing
        })
        lastClothing += 1
      }

      for (const option of options) {
        if (this.selectedStyles.length >= this.total) {
          break
        }

        this.selectOption(option)
      }
    },
    goToNextStep () {
      if (!this.finished) {
        return
      }

      localStorage.setItem('onboarding.styles', JSON.stringify(this.selectedStyles))
      this.$router.push(this.localePath('/app/upload/confirm'))
    }
  }
}
</script>

<style>
  @media screen and (max-width: 767px) {
    .chat-button {
      bottom: 6rem;
    }
  }
</style>

<i18n>
  {
    "en": {
      "Great choices! Ready to move on?": "Great choices! Ready to move on?",
      "We're ready to create your headshots now! Double check your selections below then click the button to move on to the final step.": "We're ready to create your headshots now! Double check your selections below then click the button to move on to the final step.",
      "selectStyles": "Select {styles} styles for your headshots",
      "Choose which background and clothing styles you want to wear for your headshots or let us choose for you.": "Choose which background and clothing styles you want to wear for your headshots or let us choose for you.",
      "Portrait styles (for all genders)": "Portrait styles (for all genders)",
      "Backdrops": "Backdrops",
      "Choose for me": "Choose for me",
      "Selected styles": "Selected styles",
      "Style not yet selected": "Style not yet selected",
      "Preselected by us": "Preselected by us",
      "Continue to next step": "Continue to next step",
      "youCanOnlySelect": "You can only select {total} styles",
      "Style added.": "Style added.",
      "Are you sure you want to remove this style?": "Are you sure you want to remove this style?",
      "Style removed.": "Style removed."
    },
    "es": {
      "Backdrops": "Fondos",
      "Great choices! Ready to move on?": "¡Grandes elecciones! ¿Listo para continuar?",
      "We're ready to create your headshots now! Double check your selections below then click the button to move on to the final step.": "¡Estamos listos para crear tus fotos de perfil ahora! Revisa tus selecciones a continuación y luego haz clic en el botón para pasar al paso final.",
      "selectStyles": "Selecciona {styles} estilos para tus fotos de perfil",
      "Choose which background and clothing styles you want to wear for your headshots or let us choose for you.": "Elige qué estilos de fondo y ropa quieres usar para tus fotos de perfil o déjanos elegir por ti.",
      "Available styles": "Estilos de retrato (para todos los géneros)",
      "Choose for me": "Elige por mí",
      "Selected styles": "Estilos seleccionados",
      "Style not yet selected": "Estilo aún no seleccionado",
      "Preselected by us": "Preseleccionado por nosotros",
      "Continue to next step": "Continuar al siguiente paso",
      "youCanOnlySelect": "Solo puedes seleccionar {total} estilos",
      "Style added.": "Estilo añadido.",
      "Are you sure you want to remove this style?": "¿Estás seguro de que quieres eliminar este estilo?",
      "Style removed.": "Estilo eliminado.",
      "Portrait styles (for all genders)": "Estilos de retrato (para todos los géneros)",
      "All backdrops": "Todos los fondos",
      "Missing a backdrop? Request a custom one": "¿Falta un fondo? Solicita uno personalizado",
      "Selected combinations": "Combinaciones seleccionadas",
      "Continue": "Continuar",
      "Remove": "Eliminar",
      "Back": "Atrás",
      "here": "aquí"
    },
    "de": {
      "Great choices! Ready to move on?": "Tolle Auswahl! Bereit weiterzumachen?",
      "We're ready to create your headshots now! Double check your selections below then click the button to move on to the final step.": "Wir sind bereit, deine Bewerbungsfotos zu erstellen! Überprüfe deine Auswahl unten und klicke dann auf den Button, um zum letzten Schritt zu gelangen.",
      "selectStyles": "Wähle {styles} Stile für deine Bewerbungsfotos",
      "Choose which background and clothing styles you want to wear for your headshots or let us choose for you.": "Wähle, welche Hintergrund- und Kleidungsstile du für deine Bewerbungsfotos möchtest - oder lass uns für dich auswählen.",
      "Portrait styles (for all genders)": "Portrait-Stile (für alle Geschlechter)",
      "Backdrops": "Hintergründe",
      "Back": "Zurück",
      "Choose for me": "Für mich auswählen",
      "Selected styles": "Ausgewählte Stile",
      "Style not yet selected": "Stil noch nicht ausgewählt",
      "Preselected by us": "Von uns vorausgewählt",
      "Continue to next step": "Weiter zum nächsten Schritt",
      "Continue": "Weiter",
      "Selected combinations": "Ausgewählte Kombinationen",
      "youCanOnlySelect": "Du kannst nur {total} Stile auswählen",
      "Style added.": "Stil hinzugefügt.",
      "Are you sure you want to remove this style?": "Möchtest du diesen Stil wirklich entfernen?",
      "Style removed.": "Stil entfernt.",
      "Available styles": "Verfügbare Stile",
      "All backdrops": "Alle Hintergründe",
      "Missing a backdrop? Request a custom one": "Fehlt ein Hintergrund? Fordere einen individuellen an",
      "Remove": "Entfernen",
      "here": "hier"
    }
  }
</i18n>
