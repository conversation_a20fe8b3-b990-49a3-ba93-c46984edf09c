<template>
  <PostcheckoutWrapper alignment="top">
    <section class="relative pt-8 pb-28 sm:pt-12 md:pb-12">
      <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8">
        <PostcheckoutStepper :step="1" classes="md:justify-center" />

        <div class="mx-auto mt-4 text-left sm:mt-6 md:max-w-xl md:text-center">
          <h1 class="text-xl font-bold tracking-tight sm:text-2xl lg:text-3xl text-primary-500">
            {{ $t('Let\'s get your headshots done') }}
          </h1>
          <p class="mt-2 text-base font-medium text-gray-500 md:max-w-md md:mx-auto">
            {{ $t('This won\'t take very long. Let\'s get started.') }}
          </p>
        </div>

        <div class="grid max-w-5xl grid-cols-1 gap-6 mx-auto mt-8 text-left md:text-center sm:mt-12 md:grid-cols-3">
          <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:p-6">
            <div class="flex items-center gap-2 md:flex-col md:gap-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="md:mx-auto text-sky-400 size-6"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                />
              </svg>
              <p class="text-base font-medium tracking-tight sm:text-lg text-primary-500">
                {{ $t('Takes up to 10 minutes') }}
              </p>
            </div>
            <p class="mt-2 text-sm text-gray-500 sm:text-base">
              {{ $t('Make sure you have some available time before you get started.') }}
            </p>
          </div>

          <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:p-6">
            <div class="flex items-center gap-2 md:flex-col md:gap-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="md:mx-auto text-sky-400 size-6"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"
                />
              </svg>
              <p class="text-base font-medium tracking-tight sm:text-lg text-primary-500">
                {{ $t('Be rested and dressed up') }}
              </p>
            </div>
            <p class="mt-2 text-sm text-gray-500 sm:text-base">
              {{ $t('We will require you to take a few selfies. Make sure you look fresh.') }}
            </p>
          </div>

          <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:p-6">
            <div class="flex items-center gap-2 md:flex-col md:gap-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="md:mx-auto text-sky-400 size-6"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"
                />
              </svg>
              <p class="text-base font-medium tracking-tight sm:text-lg text-primary-500">
                {{ $t('Start at daytime') }}
              </p>
            </div>
            <p class="mt-2 text-sm text-gray-500 sm:text-base">
              {{ $t('For the best results, make sure it\'s day time or you are in well lit room.') }}
            </p>
          </div>
        </div>

        <div class="hidden max-w-xs mx-auto mt-8 sm:mt-12 md:block">
          <LoadingWrapper :is-loading="isLoading">
            <button
              type="button"
              class="text-base inline-flex w-full font-medium text-white rounded-lg shadow-sm bg-primary-500 gap-1.5 pt-2 pb-2.5 px-3.5 border border-transparent transition-all duration-150 hover:bg-opacity-90 items-center justify-center disabled:bg-opacity-20"
              data-testid="intro-continue-button"
              @click="next"
            >
              {{ $t('Ok, I am ready') }}
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-5 -mb-0.5">
                <path
                  fill-rule="evenodd"
                  d="M3 10a.75.75 0 0 1 .75-.75h10.638L10.23 5.29a.75.75 0 1 1 1.04-1.08l5.5 5.25a.75.75 0 0 1 0 1.08l-5.5 5.25a.75.75 0 1 1-1.04-1.08l4.158-3.96H3.75A.75.75 0 0 1 3 10Z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
          </LoadingWrapper>
        </div>
      </div>

      <div class="fixed inset-x-0 bottom-0 py-4 bg-white border-t border-gray-100 shadow-sm md:hidden">
        <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8">
          <button
            type="button"
            class="text-base inline-flex w-full font-medium text-white rounded-lg shadow-sm bg-primary-500 gap-1.5 pt-2 pb-2.5 px-3.5 border border-transparent transition-all duration-150 hover:bg-opacity-90 items-center justify-center disabled:bg-opacity-20"
            @click="next"
          >
            {{ $t('Ok, I am ready') }}
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-5 -mb-0.5">
              <path
                fill-rule="evenodd"
                d="M3 10a.75.75 0 0 1 .75-.75h10.638L10.23 5.29a.75.75 0 1 1 1.04-1.08l5.5 5.25a.75.75 0 0 1 0 1.08l-5.5 5.25a.75.75 0 1 1-1.04-1.08l4.158-3.96H3.75A.75.75 0 0 1 3 10Z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
        </div>
      </div>
    </section>
  </PostcheckoutWrapper>
</template>

<script>
import PostcheckoutMixin from '../../../mixins/PostcheckoutMixin'

export default {
  mixins: [PostcheckoutMixin],
  layout: 'protected',
  data () {
    return {
      isLoading: false
    }
  },
  mounted () {
    this.$posthog.capture('$post_checkout:intro')
  },
  methods: {
    next () {
      if (this.isLoading) {
        return
      }

      this.isLoading = true
      this.$axios.$post('/onboarding/start')
        .then((res) => {
          if (res.data?.modelId) {
            this.$store.commit('onboarding/SET_MODEL_ID', res.data.modelId)
            this.$router.push({ path: this.localePath('/app/upload/photos') })
          } else {
            this.$toast.warning('Something went wrong. Please, try again.')
          }
          if (res?.data?.package && !this.isTeamMember) {
            this.$store.commit('user/SET_PACKAGES', [res.data.package])
          }
        })
        .catch((error) => {
          console.log('error', error)
          this.handleError(error)
        })
        .finally(() => {
          this.isLoading = false
        })
    }
  }
}
</script>

<style>
  @media screen and (max-width: 767px) {
    .chat-button {
      bottom: 6rem;
    }
  }
</style>

<i18n>
  {
    "en": {
      "Let's get your headshots done": "Let's get your headshots done",
      "This won't take very long. Let's get started.": "This won't take very long. Let's get started.",
      "Takes up to 10 minutes": "Takes up to 10 minutes",
      "Make sure you have some available time before you get started.": "Make sure you have some available time before you get started.",
      "Be rested and dressed up": "Be rested and dressed up",
      "We will require you to take a few selfies. Make sure you look fresh.": "We will require you to take a few selfies. Make sure you look fresh.",
      "Start at daytime": "Start at daytime",
      "For the best results, make sure it's day time or you are in well lit room.": "For the best results, make sure it's day time or you are in well lit room.",
      "Ok, I am ready": "Ok, I am ready"
    },
    "es": {
      "Let's get your headshots done": "Hagamos tus fotos profesionales",
      "This won't take very long. Let's get started.": "Esto no tomará mucho tiempo. Empecemos.",
      "Takes up to 10 minutes": "Toma hasta 10 minutos",
      "Make sure you have some available time before you get started.": "Asegúrate de tener algo de tiempo disponible antes de comenzar.",
      "Be rested and dressed up": "Descansa y vístete",
      "We will require you to take a few selfies. Make sure you look fresh.": "Te pediremos que te tomes algunas selfies. Asegúrate de lucir fresco.",
      "Start at daytime": "Comienza de día",
      "For the best results, make sure it's day time or you are in well lit room.": "Para obtener los mejores resultados, asegúrate de que sea de día o estés en una habitación bien iluminada.",
      "Ok, I am ready": "Ok, estoy listo"
    },
    "de": {
      "Let's get your headshots done": "Lass uns deine Bewerbungsfotos erstellen",
      "This won't take very long. Let's get started.": "Das dauert nicht lange. Lassen wir anfangen.",
      "Takes up to 10 minutes": "Dauert bis zu 10 Minuten",
      "Make sure you have some available time before you get started.": "Stelle sicher, dass du etwas Zeit hast, bevor du anfängst.",
      "Be rested and dressed up": "Sei ausgeruht und gut angezogen",
      "We will require you to take a few selfies. Make sure you look fresh.": "Du musst ein paar Selfies aufnehmen. Achte darauf, dass du frisch aussiehst.",
      "Start at daytime": "Am Tag starten",
      "For the best results, make sure it's day time or you are in well lit room.": "Für die besten Ergebnisse sorge dafür, dass es Tag ist oder du in einem gut beleuchteten Raum bist.",
      "Ok, I am ready": "Ok, ich bin bereit"
    }
  }
</i18n>
