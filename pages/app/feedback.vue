<template>
  <div class="p-4 md:p-8 w-full flex items-center justify-center flex-col">
    <div class="flex flex-col items-center justify-center space-x-2">
      <nuxt-link to="/">
        <Logo class="w-[180px] mx-auto text-black" />
      </nuxt-link>
      <LoadingWrapper :is-loading="isLoading">
        <Card class="max-w-2xl mx-auto w-full mt-4">
          <div class="w-full">
            <template v-if="submitted">
              <div class="text-left">
                <Heading>
                  Thanks for your feedback
                </Heading>
                <Paragraph size="sm">
                  <template v-if="feedbackType === 'user'">
                    Your free headshots should be done in up to 2 hours.
                    <br>
                    You'll get an email when they're ready.
                  </template>
                  <template v-else>
                    We've applied a free credit to your account. <br>You can now start your <nuxt-link to="/app/add" class="underline">
                      free photo shoot over here.
                    </nuxt-link>
                  </template>
                </Paragraph>
                <LoadingSpinner title="Redirecting you..." class="mb-4" />
                <nuxt-link to="/app" class="text-xs text-gray-600 underline">
                  Click here if it takes too long.
                </nuxt-link>
              </div>
            </template>
            <template v-else-if="unauthorized">
              <div class="text-center">
                <Heading>
                  You cannot submit feedback at this time.
                </Heading>
                <Paragraph size="sm">
                  It looks like you've already received a free try or something went wrong.
                </Paragraph>
                <LoadingSpinner title="Redirecting you..." class="mb-4" />
                <nuxt-link to="/app" class="text-xs text-gray-600 underline">
                  Click here if it takes too long.
                </nuxt-link>
              </div>
            </template>
            <template v-else>
              <Heading>
                Share your feedback
              </Heading>
              <Paragraph size="sm" class="mb-4">
                We'd love to hear your thoughts so we can improve our service.
              </Paragraph>
              <LoadingWrapper :is-loading="isSubmitting" title="Submitting your feedback..">
                <div class="space-y-5">
                  <InputTextArea
                    v-for="question in feedback[feedbackType]"
                    :key="question.question"
                    v-model="question.answer"
                    :label="question.question"
                    :rows="2"
                  />
                  <div v-if="feedbackType === 'user'" class="space-y-2">
                    <div class="flex items-center content-center justify-between">
                      <label for="email" class="block text-sm font-semibold leading-5 text-primary-500">
                        {{ feedback[feedbackType].length + 1 }}. Can we showcase your headshots as examples of what we do?
                      </label>
                    </div>
                    <Paragraph size="sm">
                      Get free bonus headshots when you give us permission to showcase your keeper headshots. This helps us a lot!
                    </Paragraph>
                    <div class="flex items-start justify-start space-x-2 pt-0">
                      <input id="allowedPhotoUse" v-model="allowPhotoUse" type="checkbox" class=" mt-[4px]">
                      <label for="allowedPhotoUse" class="cursor-pointer text-sm text-gray-600">You can feature my keeper headshots on HeadshotPro.com <em>(+10 extra photos)</em></label>
                    </div>
                    <div class="flex items-start justify-start space-x-2 pt-0">
                      <input id="allowPhotosForMarketing" v-model="allowPhotosForMarketing" type="checkbox" class=" mt-[4px]">
                      <label for="allowPhotosForMarketing" class="cursor-pointer text-sm text-gray-600">You can feature my keeper headshots in your marketing materials <em>(+20 extra photos)</em></label>
                    </div>
                    <div class="flex items-start justify-start space-x-2 pt-0">
                      <input id="allowedSelfies" v-model="allowedSelfies" type="checkbox" class=" mt-[4px]">
                      <label for="allowedSelfies" class="cursor-pointer text-sm text-gray-600">You can feature my selfies and my headshots to promote HeadshotPro <em>(+30 extra photos)</em></label>
                    </div>
                  </div>
                  <!-- <div v-if="feedbackType === 'user'" class="space-y-2">
                    <div class="flex items-center content-center justify-between">
                      <label for="email" class="block text-sm font-medium leading-5 text-gray-700">
                        {{ feedback[feedbackType].length + 2 }}. Select the background and clothing for your free shoot
                      </label>
                    </div>
                    <div class="grid grid-cols-2 gap-2">
                      <InputSelect v-model="shoot.style" :options="getStyleOptions()" label="Background" />
                      <InputSelect v-model="shoot.clothing" :options="getClothingOptions()" label="Clothing" />
                    </div>
                  </div> -->

                  <ButtonPrimary class="w-full" @click="submit">
                    Submit feedback and claim {{ numberOfBonusPhotos }} free headshots
                  </ButtonPrimary>
                </div>
              </LoadingWrapper>
            </template>
          </div>
        </Card>
      </LoadingWrapper>
    </div>
  </div>
</template>

<script>
import Heading from '@/components/landingpage/common/H5.vue'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
export default {
  components: {
    Heading,
    Paragraph
  },
  layout: 'protected',
  data () {
    return {
      feedback: {
        user: [
          { question: '1. What problem did our product solve for you?', answer: '' },
          { question: '2. Can you give an example of how our product helped you?', answer: '' },
          { question: '3. How did you find out about us?', answer: '' },
          { question: '4. Why did you choose our product over others?', answer: '' },
          { question: '5. Is there anything we could have done better?', answer: '' }
        ],
        organization: [
          { question: '1. If HeadshotPro were a perfect solution for your company, how many team members would you need headshots for?', answer: '' },
          { question: '2. What problem(s) are you hoping HeadshotPro solves for your company?', answer: '' },
          { question: '3. How often do you typically update your company\'s headshots?', answer: '' },
          { question: '4. How do you currently update your teams headshots?', answer: '' }
        ]
      },
      feedbackType: 'user',
      allowPhotoUse: true,
      shoot: {
        style: null,
        clothing: null
      },
      isLoading: true,
      isSubmitting: false,
      submitted: false,
      unauthorized: false,
      modelId: null,
      allowPhotosForMarketing: false,
      allowedSelfies: false
    }
  },
  head () {
    return {
      title: 'Feedback | HeadshotPro'
    }
  },
  computed: {
    numberOfBonusPhotos () {
      let base = 10
      if (this.allowPhotoUse) { base += 10 }
      if (this.allowPhotosForMarketing) { base += 20 }
      if (this.allowedSelfies) { base += 30 }
      return base
    },
    selectedFeedback () {
      return this.feedback[this.feedbackType]
    },
    styleOptions () {
      return this.$store.state.styles.map((style) => {
        return {
          title: style.title,
          value: style._id
        }
      })
    },
    clothingOptions () {
      return this.$store.state.clothing.map((style) => {
        return {
          title: style.type,
          value: style._id
        }
      })
    },
    redirectUrl () {
      if (this.feedbackType === 'user') {
        return `/app/results/${this.modelId}/unlock-more`
      }
      return '/app'
    }
  },
  async mounted () {
    if (this.$route.query.type) {
      this.feedbackType = this.$route.query.type
    }

    this.handleOrganizationProtection()
    await this.handleUserProtection()
    this.isLoading = false
  },
  methods: {
    async handleUserProtection () {
      if (this.feedbackType !== 'user') { return }
      if (this.$store.state.clothing.length === 0) {
        this.$store.dispatch('getClothing')
      }
      if (this.$store.state.styles.length === 0) {
        this.$store.dispatch('getStyles')
      }
      const hasSubmitted = await this.$axios.$get('/feedback/check')
      if (hasSubmitted.submitted) {
        this.$toast.error('You have already submitted feedback.')
        this.$router.push('/app')
      }
      this.modelId = hasSubmitted?.modelId
      if (!this.modelId) {
        this.$toast.error('No model was selected.')
        this.$router.push('/app')
      }
    },
    handleOrganizationProtection () {
      if (this.feedbackType !== 'organization') { return }

      if (this.$store.state?.user?.role !== 'TeamLead') {
        this.$toast.error('You are not authorized to submit feedback.')
        this.unauthorized = true
        this.redirectToApp()
        return
      }

      // Check if organization has received free try
      const reasons = this.$store.state.organization.organization.creditLog.map(log => log.reason)
      if (reasons.includes('Free try')) {
        this.$toast.error('You have already received a free try.')
        this.unauthorized = true
        this.redirectToApp()
      }
    },
    getClothingOptions () {
      return this.$store.state.clothing.map(style => ({
        title: style.type,
        value: style._id
      }))
    },
    getStyleOptions () {
      return this.$store.state.styles.map((style) => {
        return {
          title: style.title,
          value: style._id
        }
      })
    },
    submit () {
      this.isSubmitting = true
      const { feedback, modelId, allowPhotoUse, shoot, feedbackType, allowPhotosForMarketing, allowedSelfies } = this

      if (feedback[feedbackType].some(question => question.answer === '')) {
        this.isSubmitting = false
        this.$toast.error('Please answer all questions.')
        return
      }

      // // Require style and clothing for user feedback
      // if (feedbackType === 'user' && (!shoot.style || !shoot.clothing)) {
      //   this.isSubmitting = false
      //   this.$toast.error('Please select a background and clothing for your free shoot.')
      //   return
      // }

      const endpoint = feedbackType === 'user' ? '/feedback' : '/feedback/organization'

      this.$axios.$post(endpoint, { feedback: feedback[feedbackType], modelId, allowPhotoUse, shoot, feedbackType: this.feedbackType, allowPhotosForMarketing, allowedSelfies })
        .then(() => {
          this.submitted = true
          this.$posthog.capture('$feedback:submitted', {
            // eslint-disable-next-line camelcase
            feedback_type: feedbackType
          })
          this.$toast.success('Thanks for your feedback!')
          if (feedbackType === 'user') {
            this.$store.commit('user/SET_REWARDS', { ...this.$store.state.user.rewards, feedback: true })
          }
          this.redirectToApp()
        })
        .catch((err) => {
          this.$toast.error('Something went wrong. Please try again later.')
          console.log(err)
        }).finally(() => {
          this.isSubmitting = false
        })
    },
    redirectToApp () {
      setTimeout(() => {
        this.$router.push(this.redirectUrl)
      }, 5000)
    }
  }

}
</script>

<style>

</style>
