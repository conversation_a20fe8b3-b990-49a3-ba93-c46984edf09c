<template>
  <section class="bg-gray-50 flex min-h-screen items-start justify-center px-4 py-8 text-black md:items-center">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="mx-auto flex max-w-3xl flex-col items-center justify-center">
        <nuxt-link to="/">
          <Logo class="w-[180px] text-white" />
        </nuxt-link>
        <LoadingWrapper :is-loading="isLoading">
          <Card class="mt-4 w-[500px] rounded-lg shadow">
            <div class="p-8 space-y-2">
              <template v-if="isEligable">
                <h2 class="text-xl font-bold">
                  Submit your Trustpilot review
                </h2>
                <p class="mb-4 text-sm text-gray-700">
                  Thank you so much for supporting HeadshotPro. Please fill in the name and date of your review so we can verify it and send you a free photo shoot.
                </p>
                <Input v-model="form.email" disabled label="Your email" />
                <Input v-model="form.name" description="Name used on your Trustpilot review" label="TrustPilot name" />
                <Input v-model="form.date" type="date" label="Date of review" />
                <ButtonPrimary class="w-full" @click="submitRequest">
                  Submit
                </ButtonPrimary>
              </template>
              <template v-else>
                <h2 class="text-xl font-bold">
                  You've already submitted a review
                </h2>
                <p class="mb-4 text-sm text-gray-700">
                  You have already submitted a review and are not eligible for another free photo shoot. Thanks again for your support!
                </p>
              </template>
            </div>
          </Card>
        </LoadingWrapper>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  layout: 'protected',
  data () {
    return {
      form: {
        email: '',
        name: '',
        date: ''
      },
      isEligable: false,
      eligableReason: '',
      isLoading: true
    }
  },
  head () {
    return {
      title: 'Trustpilot Review | HeadshotPro',
      meta: [
        { name: 'robots', content: 'noindex' }
      ]
    }
  },
  async mounted () {
    this.form.email = this.$store.state.user.email
    // await this.checkForPendingRefund()
    await this.checkIfEligable()
    this.isLoading = false
  },
  methods: {
    async checkIfEligable () {
      const response = await this.$axios.$get('/support/trustpilot/eligable')
      if (response && response.success) {
        this.isEligable = response.eligable
        this.eligableReason = response.reason
      }
    },
    async submitRequest () {
      const { name, email, date } = this.form

      if (!email || email.length === 0) { return this.$toast.warning('Please enter your email address.') }
      if (!name || name.length === 0) { return this.$toast.warning('Please provide your name.') }
      if (!date || date.length === 0) { return this.$toast.warning('Please provide the date you posted your review.') }
      this.isLoading = true
      const response = await this.$axios.$post('/support/trustpilot', {
        name: this.form.name,
        email: this.form.email
      })
      if (response && response.success) {
        this.$toast.success('Thank you for your review! Check your email for the next step.')
        setTimeout(() => {
          this.$router.push('/app')
          this.isLoading = false
        }, 3000)
      }
    }
  }

}
</script>

<style>

</style>
