<template>
  <div>
    <HeaderApplication />
    <div class="w-full p-8">
      <div class="max-w-6xl mx-auto">
        <div>
          <div class="space-y-4">
            <div class="grid grid-cols-1 grid-rows-3 md:grid-cols-3 md:grid-rows-1 gap-8">
              <Card class="p-4">
                <CardHeader>
                  <h3 class="text-base leading-6 font-medium text-black">
                    Change email
                  </h3>
                </CardHeader>
                <ProfileChangeEmailForm />
              </Card>
              <Card class="p-4">
                <CardHeader>
                  <h3 class="text-base leading-6 font-medium text-black">
                    Change password
                  </h3>
                </CardHeader>
                <ProfileChangePasswordForm />
              </Card>
              <Card class="p-4">
                <CardHeader>
                  <h3 class="text-base leading-6 font-medium text-black">
                    Change name
                  </h3>
                </CardHeader>
                <ProfileChangeNameForm />
              </Card>
              <!-- <Card v-if="$store?.state?.user?.stripeId" class="p-4">
                <CardHeader>
                  <h3 class="text-base leading-6 font-medium text-black">
                    Update payment method
                  </h3>
                </CardHeader>
                <div class="py-4">
                  <ButtonPrimary size="sm" @click="createPortalSession">
                    Change here
                  </ButtonPrimary>
                </div>
              </Card> -->
              <Card v-if="!isTeamMember" class="p-4">
                <CardHeader>
                  <h3 class="text-base leading-6 font-medium text-black">
                    Delete my account
                  </h3>
                </CardHeader>
                <div class="py-4">
                  <ButtonDelete @click="showDeleteAccountModal = true">
                    Delete my account
                  </ButtonDelete>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
    <Popup v-if="showDeleteAccountModal" size="lg" @closeModal="showDeleteAccountModal = false">
      <LoadingWrapper :is-loading="isLoading">
        <div class="space-y-4">
          <div class="space-y-2 flex flex-col">
            <h2 class="text-lg font-semibold">
              Are you sure you want to delete your account?
            </h2>
            <p class="text-sm text-gray-500">
              This action cannot be undone. We will delete all your data, your photos and any other information you have stored with us.
            </p>
          </div>
          <ul class="text-sm text-gray-500 font-medium list-disc pl-4 space-y-1">
            <li>We won't be able to refund any payments you have made.</li>
            <li>There is no way to restore your account once it is deleted.</li>
          </ul>
          <Input v-model="deleteAccountConfirmationEmail" placeholder="Type the email above" :label="`Type ${$store.state.user?.email} to delete your account`" />
          <div class="flex justify-start gap-2">
            <ButtonWhite size="sm" @click="showDeleteAccountModal = false">
              Cancel
            </ButtonWhite>
            <ButtonDelete size="sm" @click="deleteAccount">
              Delete my account
            </ButtonDelete>
          </div>
        </div>
      </LoadingWrapper>
    </Popup>
  </div>
</template>

<script>

export default {
  layout: 'protected',
  data () {
    return {
      showDeleteAccountModal: false,
      deleteAccountConfirmationEmail: '',
      isLoading: false
      // navigation: this.$store.state.navigation.settingsNavigation
    }
  },
  head () {
    return {
      title: 'Profile | HeadshotPro'
    }
  },
  methods: {
    async createPortalSession () {
      this.isLoading = true
      const response = await this.$axios.$post('/checkout/stripe/create-customer-portal-session')
      if (response && response.url) {
        window.open(response.url, '_blank')
      }
      this.isLoading = false
    },
    deleteAccount () {
      if (this.deleteAccountConfirmationEmail !== this.$store.state.user?.email) { return this.$toast.error('The email you typed does not match your account email.') }
      this.isLoading = true
      this.$axios.$post('/user/delete').then((response) => {
        if (response.success) {
          this.$toast.success('Your account has been deleted.')
          this.logout()
        } else {
          this.$toast.error(response.message)
        }
      }).catch((err) => {
        console.log(err)
        this.$toast.error(err.message)
      }).finally(() => {
        this.isLoading = false
      })
    }
  }

}
</script>

<style>

</style>
