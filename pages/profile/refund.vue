<template>
  <section class="bg-gray-50 flex min-h-screen items-start justify-center px-4 py-8 text-black md:items-center">
    <div class="mx-auto max-w-7xl w-full px-4 sm:px-6 lg:px-8">
      <div class="mx-auto flex max-w-xl w-full flex-col items-center justify-center">
        <nuxt-link to="/">
          <Logo class="w-[180px] text-white" />
        </nuxt-link>
        <div class="mt-8 w-full bg-white rounded-lg shadow p-8">
          <SupportForm :refund-only="true" />
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  layout: 'protected',
  data () {
    return {
      isLoading: true
    }
  },
  head () {
    return {
      title: 'Refund | HeadshotPro',
      meta: [
        { name: 'robots', content: 'noindex' }
      ]
    }
  },
  mounted () {
    this.isLoading = false
  }
}
</script>

<style>

</style>
