<template>
  <div>
    <HeaderSimple />
    <div class="w-full p-1 md:p-8 min-h-screen">
      <div class="max-w-7xl mx-auto">
        <!-- <template v-if="hasPurchasedBefore"> -->
        <div class="sm:px-4 lg:px-12">
          <Card class=" mx-auto p-4">
            <CardHeader>
              <h2 id="billing_history_heading" class="text-lg leading-6 font-medium text-gray-800">
                Billing history
              </h2>
            </CardHeader>
            <LoadingWrapper :is-loading="isLoading">
              <div class="mt-6 flex flex-col ">
                <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                  <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                    <div class="hidden md:flex w-full">
                      <Table class="w-full" :head="['Date', 'Order ID', 'Status', 'Amount', 'Product', 'Action']">
                        <template v-for="invoice in invoices">
                          <TableRow v-if="invoice.amountTotal" :key="invoice.id">
                            <TableItem>
                              {{ formatDate(invoice.createdOn) }}
                            </TableItem>
                            <TableItem>
                              {{ invoice._id }}
                            </TableItem>
                            <TableItem>
                              <span v-if="invoice.status === 'refunded'" class="text-orange-500">{{ slugToTitle(invoice.status) }}</span>
                              <span v-else>{{ slugToTitle(invoice.status) }}</span>
                            </TableItem>
                            <TableItem>
                              <span
                                :class="{
                                  'line-through': invoice.status === 'refunded'
                                }"
                              >
                                {{ formatPrice(invoice.amountTotal/100, invoice?.currency?.toUpperCase() || 'USD', 2, false) }}
                              </span>
                              <span v-if="invoice.status === 'refunded' && invoice?.amountRefunded > 0">
                                {{ formatPrice(Math.max(0, (invoice.amountTotal - (invoice.amountRefunded || 0)))/100, invoice?.currency?.toUpperCase() || 'USD', 2, false) }}
                              </span>
                            </TableItem>
                            <TableItem>
                              {{ invoice?.price?.name || '' }}
                            </TableItem>
                            <TableItem>
                              <nuxt-link :to="`/profile/invoice?id=${invoice._id}`" target="_blank">
                                <ButtonWhite size="xs">
                                  View invoice
                                </ButtonWhite>
                              </nuxt-link>
                            </TableItem>
                          </TableRow>
                        </template>
                      </Table>
                    </div>
                    <div class="flex md:hidden w-full">
                      <Table :head="['Date', 'Amount', 'Status', 'Action']">
                        <template v-for="invoice in invoices">
                          <TableRow v-if="invoice.amountTotal" :key="invoice.id">
                            <TableItem>
                              {{ formatDate(invoice.createdOn) }}
                            </TableItem>
                            <TableItem>
                              <span
                                :class="{
                                  'line-through': invoice.status === 'refunded'
                                }"
                              >
                                {{ formatPrice(invoice.amountTotal/100, invoice?.currency?.toUpperCase() || 'USD', 2, false) }}
                              </span>
                              <span v-if="invoice.status === 'refunded'">{{ formatPrice(Math.max(0, (invoice.amountTotal - (invoice.amountRefunded || 0)))/100, invoice?.currency?.toUpperCase() || 'USD', 2, false) }}</span>
                            </TableItem>
                            <TableItem>
                              <span v-if="invoice.status === 'refunded'" class="text-orange-500">{{ slugToTitle(invoice.status) }}</span>
                              <span v-else>{{ slugToTitle(invoice.status) }}</span>
                            </TableItem>
                            <TableItem>
                              <nuxt-link :to="`/profile/invoice?id=${invoice._id}`" target="_blank">
                                <ButtonWhite size="xs">
                                  View
                                </ButtonWhite>
                              </nuxt-link>
                            </TableItem>
                          </TableRow>
                        </template>
                      </Table>
                    </div>
                    <!-- md to xl -->
                    <!-- <div class="hidden md:flex w-full"> -->
                    <!-- <Table :head="['Status', 'Date', 'Amount', '']">
                        <TableRow v-for="charge in charges" :key="charge.id">
                          <TableItem>
                            <span class="capitalize">
                              {{ charge.status }}
                            </span>
                          </TableItem>
                          <TableItem>
                            <time datetime="2020-01-01">{{ formatDate(charge.created * 1000) }}</time>
                          </TableItem>
                          <TableItem>
                            {{ formatPrice(charge.amount_captured / 100, charge.currency) }}
                          </TableItem>
                          <TableItem>
                            <div v-if="charge.status === 'succeeded'" class="flex content-center items-center justify-center space-x-2">
                              <a :href="charge.receipt_url" target="_blank">
                                <ButtonWhite size="xs">
                                  View receipt
                                </ButtonWhite>
                              </a>
                              <nuxt-link :to="`/profile/invoice?id=${charge.id}&amount_captured=${charge.amount_captured}&created=${charge.created}&currency=${charge.currency}`" target="_blank">
                                <ButtonWhite size="xs">
                                  View invoice
                                </ButtonWhite>
                              </nuxt-link>
                            </div>
                          </TableItem>
                        </TableRow>
                      </Table>
                    </div>
                    <div class="flex md:hidden w-full">
                      <Table :head="['Date', 'Amount', '']">
                        <TableRow v-for="charge in charges" :key="charge.id">
                          <TableItem>
                            {{ formatPrice(charge.amount_captured / 100, charge.currency) }}
                          </TableItem>
                          <TableItem>
                            <nuxt-link :to="`/profile/invoice?id=${charge.id}&amount_captured=${charge.amount_captured}&created=${charge.created}&currency=${charge.currency}`" target="_blank" style="flex-shrink:0;">
                              <Button size="xs" style="flex-shrink:0;">
                                View invoice
                              </Button>
                            </nuxt-link>
                          </TableItem>
                        </TableRow>
                      </Table>
                    </div> -->
                  </div>
                </div>
              </div>
            </LoadingWrapper>
          </Card>
        </div>
      </div>
      <!-- </template>
      <template v-else>
        <Card class="max-w-2xl mx-auto mt-4 lg:mt-0 p-4 space-y-2">
          <h3 class="font-bold text-lg">
            You haven't purchased anything before
          </h3>
          <p class="text-sm text-gray-800">
            Come back here after you've purchases a paid plan to get your invoices.
          </p>
        </Card>
      </template> -->
    </div>
  </div>
</template>

<script>

export default {
  layout: 'protected',
  data () {
    return {
      charges: [],
      invoices: [],
      isLoading: true
    }
  },
  head () {
    return {
      title: 'Invoices | HeadshotPro'
    }
  },
  computed: {
    stripeCustomerId () {
      return this.$store.state.users.stripe.accountId
    }
  },
  async mounted () {
    // this.charges = await this.$axios.$get('/checkout/stripe/charges')
    this.invoices = await this.$axios.$get('/user/invoice/all')
    this.isLoading = false
  },

  methods: {
    async createPortalSession () {
      this.isLoading = true
      const response = await this.$axios.$post('/checkout/stripe/create-customer-portal-session')
      if (response && response.url) {
        window.open(response.url, '_blank')
      }
      this.isLoading = false
    }
  }
}
</script>

<style>

</style>
