<template>
  <div class="p-8">
    <div class="flex w-full justify-between space-x-2 pb-4">
      <Input v-model="search" placeholder="Search" class="w-full" />
      <InputSelect v-model="searchBy" :options="['uid', 'email', 'type']" class="w-1/4" />
      <ButtonPrimary size="sm" @click="fetchData">
        Search
      </ButtonPrimary>
    </div>
    <Card class="mx-auto w-full">
      <LoadingWrapper :is-loading="isLoading">
        <Table :head="['Date', 'Status', 'UID', 'Type', 'Actions']">
          <template v-for="item in supportRequests">
            <TableRow :key="item._id">
              <TableItem>{{ formatDateAndTime(item.createdAt) }}</TableItem>
              <TableItem>
                <span :class="{'text-orange-500': item.status === 'refunded', 'text-green-500': item.status === 'processed', 'text-gray-500': item.status === 'pending'}">{{ item.status }}</span>
              </TableItem>
              <TableItem>
                <nuxt-link class="underline text-sm" target="_blank" :to="`/admin/user?search=${item.uid}&searchBy=uid`">
                  {{ item.uid }}
                </nuxt-link>
              </TableItem>
              <TableItem>{{ item.type }}</TableItem>
              <TableItem>
                <ButtonWhite v-if="item.crispId" size="sm" @click="showInCrisp(item)">
                  See in Crisp
                </ButtonWhite>
              </TableItem>
            </TableRow>
          </template>
        </Table>
        <PaginationInfinity v-if="supportRequests.length > 50" @fetch="fetchData($event)" />
      </LoadingWrapper>
    </Card>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      supportRequests: [],
      isLoading: true,
      search: null,
      searchBy: 'email'
    }
  },
  async created () {
    if (this.$route.query.uid) {
      this.search = this.$route.query.uid
      this.searchBy = 'uid'
    }
    await this.fetchData()
    this.isLoading = false
  },
  methods: {
    async fetchData () {
      this.isLoading = true
      try {
        const response = await this.$axios.$get('/admin/support', {
          params: {
            page: this.supportRequests.length,
            // search: this.search
            ...(this.search && this.search.length > 0) ? { search: this.search.trim() } : {},
            ...(this.search && this.search.length > 0) ? { searchBy: this.searchBy.trim() } : {}
          }
        })
        this.supportRequests = response
        this.isLoading = false
      } catch (err) {
        this.$toast.error('Error fetching support requests')
        this.isLoading = false
      }
    },
    showInCrisp (item) {
      window.open(`https://app.crisp.chat/website/fd4779ea-13f4-448e-906d-ea3647f4d8c8/inbox/${item.crispId}/`, '_blank')
    }
  }
}
</script>

<style></style>
