<template>
  <div class="w-full p-4 space-y-4">
    <Tabs>
      <TabItem v-for="type in ['user', 'organization']" :key="type" :active="type === feedbackType" @click="feedbackType = type">
        <span class="capitalize">{{ type }}</span>
      </TabItem>
    </Tabs>
    <LoadingWrapper :is-loading="items.length === 0">
      <Table :head="tableColumns">
        <template v-for="item in filteredItems">
          <!-- <template v-if="inSearch(item)"> -->
          <TableRow :key="item._id">
            <TableItem>{{ formatDate(item.createdOn) }}</TableItem>
            <TableItem>{{ item.email }}</TableItem>
            <TableItem>
              <span v-if="item?.isFavorite">❤️</span>
              <span v-if="item?.allowPhotoUse">✅</span>
              <span v-if="item?.allowPhotosForMarketing">🎺</span>
              <span v-if="item?.allowedSelfies">👱‍♀️</span>
              <span v-if="item?.hasSavedPhoto">📸</span>
              <Tooltip>
                <ul>
                  <li>
                    <span>✅ Allow Photo Use</span>
                  </li>
                  <li>
                    <span>🎺 Allow Photos for Marketing</span>
                  </li>
                  <li>
                    <span>👱‍♀️ Allowed Selfies</span>
                  </li>
                  <li>
                    <span>📸 Already used</span>
                  </li>
                </ul>
              </Tooltip>
            </TableItem>
            <TableItem v-for="feedback in item.answers" :key="feedback.question">
              {{ feedback.answer }}
            </TableItem>
            <TableItem>
              <div class="flex items-center justify-end space-x-2">
                <a v-if="feedbackType === 'user'" target="_blank" :href="`${serverUrl}/admin/feedback/latest-model?uid=${item.uid}`">
                  <ButtonWhite size="sm">
                    Open
                  </ButtonWhite>
                </a>
                <ButtonWhite size="sm" @click="toggleFavorite(item._id)">
                  <span v-if="item?.isFavorite">Unfavorite</span>
                  <span v-else>Favorite</span>
                </ButtonWhite>
              </div>
            </TableItem>
          </TableRow>
        </template>
      </Table>
      <template v-if="isLoading">
        <LoadingSpinner />
      </template>
      <template v-else>
        <PaginationInfinity @fetch="fetchData($event)" />
      </template>
      <ButtonWhite class="absolute right-8 top-11" size="xs" @click="allowed = !allowed">
        {{ allowed ? 'Show All' : 'Show Allowed' }}
      </ButtonWhite>
    </LoadingWrapper>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      items: [],
      allowed: false,
      isLoading: true,
      feedbackType: 'user'
    }
  },
  computed: {
    serverUrl () {
      return process.env.SERVER_URL
    },
    tableColumns () {
      let headings = ['Allow', 'Date', 'Email']
      const questions = this.filteredItems[0]?.answers || []
      questions.forEach((question) => {
        headings.push(question.question)
      })
      headings = [...headings, 'Action']
      return headings
    },
    filteredItems () {
      let items = this.items
      if (this.allowed) {
        items = items.filter(item => item.allowPhotoUse)
      }
      if (this.feedbackType !== 'user') {
        items = items.filter(item => item.type === this.feedbackType)
      } else {
        items = items.filter(item => !item.type || item.type === 'user')
      }
      return items
    }
  },
  watch: {
    feedbackType (newValue, oldValue) {
      this.items = []
      this.fetchData()
    }
  },
  async mounted () {
    await this.fetchData()
    this.isLoading = false
  },
  methods: {
    async fetchData (page = 0) {
      try {
        this.isLoading = true
        const items = await this.$axios.$get(`/admin/feedback?page=${page}&type=${this.feedbackType}`)
        this.items = [...this.items, ...items]
        this.isLoading = false
      } catch (e) {
        this.isLoading = false
        console.log(e)
        this.$toast.error('Error fetching models')
      }
    },
    toggleFavorite (id) {
      this.$axios.$post('/admin/feedback/favorite/' + id)
        .then((response) => {
          if (response.success) {
            this.items = this.items.map((item) => {
              if (item._id === id) {
                item.isFavorite = !item.isFavorite
              }
              return item
            })
          }
        })
    }
  }
}
</script>
