<template>
  <div class="h-[calc(100vh-50px)] !min-h-[calc(100vh-50px)] max-h-[calc(100vh-50px)] overflow-hidden">
    <LoadingWrapper :is-loading="isLoading">
      <AdminBlogSettings />
      <div class="grid grid-cols-2 gap-8 w-full">
        <AdminBlogEditor />
        <AdminBlogPreview />
      </div>
    </LoadingWrapper>
  </div>
</template>

<script>

export default {
  layout: 'admin',
  data () {
    return {
      isLoading: true
    }
  },

  created () {
    this.$axios.$get('/admin/blog/single/' + this.$route.params.id)
      .then(({ success, data, errorMessage }) => {
        if (!success) { throw new Error(errorMessage) }
        this.$store.commit('admin/SET_POST', data)
        this.isLoading = false
      })
      .catch((err) => {
        this.handleError(err)
      })
  },
  mounted () {
    window.addEventListener('beforeunload', this.exitHandler)
  },
  methods: {
    exitHandler (e) {
      const confirmationMessage = 'If you leave before saving, your changes will be lost.';

      (e || window.event).returnValue = confirmationMessage // Gecko + IE
      return confirmationMessage // Gecko + Webkit, Safari, Chrome etc.
    }
  }

}
</script>
