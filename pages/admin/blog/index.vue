<template>
  <div class="p-8">
    <div class="flex w-full justify-between space-x-2 pb-4">
      <nuxt-link to="/admin/blog/new">
        <ButtonPrimary size="sm">
          New blog
        </ButtonPrimary>
      </nuxt-link>
    </div>
    <Card class="mx-auto w-full">
      <LoadingWrapper :is-loading="isLoading">
        <Table v-if="blogs.length > 0" :head="['Status', 'Category', 'Title', 'Date', 'Actions']">
          <template v-for="blog in blogs">
            <TableRow :key="blog._id">
              <TableItem>
                <span :style="`color:${getStatusColor(blog.status)}`">{{ blog.status }}</span>
              </TableItem>
              <TableItem>{{ slugToTitle(blog.category || 'blog') }}</TableItem>
              <TableItem>{{ blog.title }}</TableItem>
              <TableItem>{{ blog.createdAt }}</TableItem>
              <TableItem class="flex items-center space-x-2">
                <ButtonDelete v-if="blog.status !== 'deleted'" size="sm" @click="deleteItem(blog._id)">
                  Delete
                </ButtonDelete>
                <nuxt-link v-if="blog.status !== 'deleted'" :to="`/admin/blog/edit/${blog._id}`">
                  <ButtonWhite size="sm">
                    Edit
                  </ButtonWhite>
                </nuxt-link>
              </TableItem>
            </TableRow>
          </template>
        </Table>
        <p v-else>
          No blogs found
        </p>
      </LoadingWrapper>
    </Card>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      isLoading: true,
      blogs: []
    }
  },
  async mounted () {
    await this.fetchData()
    this.isLoading = false
    this.$store.dispatch('admin/resetPost')
  },
  methods: {
    getStatusColor (status) {
      switch (status) {
        case 'draft':
          return 'gray'
        case 'published':
          return 'green'
        case 'deleted':
          return 'red'
        default:
          return 'gray'
      }
    },
    async fetchData () {
      try {
        this.isLoading = true
        const { success, data, errorMessage } = await this.$axios.$get('/admin/blog/all')
        if (!success) { throw new Error(errorMessage) }
        this.blogs = data
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = false
      }
    },
    async deleteItem (id) {
      try {
        const { success, errorMessage } = await this.$axios.$delete(`/admin/blog/${id}`)
        if (!success) { throw new Error(errorMessage) }
        // Filter out current id and set status = deleted
        this.blogs = this.blogs.map((blog) => {
          if (blog._id === id) {
            blog.status = 'deleted'
          }
          return blog
        })
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = false
      }
    }
  }

}

</script>

<style></style>
