<template>
  <div class="h-[calc(100vh-50px)] !min-h-[calc(100vh-50px)] max-h-[calc(100vh-50px)] overflow-hidden">
    <AdminBlogSettings />
    <div class="grid grid-cols-2 gap-8 w-full">
      <AdminBlogEditor />
      <AdminBlogPreview />
    </div>
  </div>
</template>

<script>

export default {
  beforeRouteLeave (to, from, next) {
    if (confirm('Do you really want to leave? Any unsaved changes will be lost!')) {
      next()
    } else {
      next(false)
    }
  },
  layout: 'admin',
  created () {
    this.$store.dispatch('admin/resetPost')
  },
  mounted () {
    window.addEventListener('beforeunload', this.exitHandler)
  },
  beforeDestroy () {
    window.removeEventListener('beforeunload', this.beforeunload)
  },
  methods: {
    exitHandler (e) {
      e.preventDefault()
      e.returnValue = ''
    }
  }
}
</script>
