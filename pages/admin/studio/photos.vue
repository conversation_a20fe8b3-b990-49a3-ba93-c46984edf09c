<template>
  <div id="content" class="p-4 bg-muted-50 w-full">
    <div v-masonry="containerId" transition-duration="0.3s" item-selector=".item" class="w-full">
      <div v-for="photo in photos" :key="photo._id" v-masonry-tile class="w-1/2 md:w-1/3 lg:w-1/4 xl:w-1/5 3xl:w-1/6 item p-2" @click="openPhoto(photo)">
        <ImageDns v-if="photo?.images?.thumbnail" :src="photo.images.thumbnail" class="w-full h-full object-cover relative z-0" @load="refreshMasonry" />
      </div>
    </div>
    <PaginationInfinity v-if="canLoadMore" @fetch="fetchData($event)" />
    <Popup v-if="selectedPhoto" size="4xl" :show="!!selectedPhoto" @close="selectedPhoto = null">
      <ImageDns :src="selectedPhoto?.images?.hd || selectedPhoto?.images?.full || selectedPhoto?.images?.thumbnail" class="w-full h-full object-cover" />
    </Popup>
  </div>
</template>

<script>
export default {
  data () {
    return {
      selectedPhoto: null,
      photos: [],
      containerId: '',
      canLoadMore: true
    }
  },
  async mounted () {
    this.$loading.show()
    await this.fetchData()
    this.$loading.hide()
  },
  methods: {
    async fetchData () {
      const { success, data } = await this.$axios.$get('/admin/studio/photo', { params: { offset: this.photos.length, limit: 100 } })
      if (success) {
        this.photos = [...this.photos, ...data]
        if (data.length === 0) {
          this.canLoadMore = false
        }
      }
    },
    openPhoto (photo) {
      this.selectedPhoto = photo
    }
  }
}
</script>

<style>

</style>
