<template>
  <div class="p-4 space-y-4">
    <div class="flex justify-between items-center">
      <div class="flex items-center gap-2">
        <h1 class="text-2xl font-bold">
          Prompt Template
        </h1>
        <InputSelect v-model="filter.status" :options="['all', 'active', 'offline', 'draft']" placeholder="Status" />
        <InputSelect v-model="filter.gender" :options="['all', 'male', 'female']" placeholder="Gender" />
      </div>
      <div>
        <ButtonPrimary size="sm" @click="showCreateModal = true">
          Create
        </ButtonPrimary>
      </div>
    </div>
    <div class="grid grid-cols-7 gap-4">
      <div v-for="photoTemplate in filteredPhotoTemplates" :key="photoTemplate._id" class="bg-muted-50 min-h-[200px] h-full" :class="{'opacity-70 hover:opacity-100 transition-opacity duration-300': photoTemplate.status === 'draft'}">
        <div class="relative group min-h-[200px] h-full">
          <ImageDns v-if="photoTemplate.url" :src="photoTemplate.url" />
          <div v-else class="w-full h-full flex items-center justify-center bg-gray-100" />
          <h3 class="absolute bottom-0 left-0 p-2 bg-black/50 text-white text-xs">
            {{ photoTemplate.name }}
          </h3>
          <span class="absolute top-2 right-2 p-2 rounded-full px-2 py-1 text-xs bg-black/50 text-white opacity-60" :class="{'bg-green-500': photoTemplate.status === 'active', 'bg-red-500': photoTemplate.status === 'offline'}">
            {{ photoTemplate.status }}
          </span>
          <div class="absolute top-0 left-0 w-full h-full bg-black/50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col gap-2">
            <ButtonPrimary size="sm" @click="togglePhotoTemplateStatus(photoTemplate._id)">
              <span v-if="photoTemplate.status === 'draft' || photoTemplate.status === 'offline'">
                Toggle live
              </span>
              <span v-else>
                Toggle offline
              </span>
            </ButtonPrimary>
            <ButtonDelete size="sm" @click="deletePhotoTemplate(photoTemplate._id)">
              Delete
            </ButtonDelete>
            <ButtonWhite size="sm" @click="viewPhotoTemplate(photoTemplate._id)">
              View
            </ButtonWhite>
          </div>
        </div>
      </div>
    </div>
    <Popup v-if="showCreateModal" size="xl" @close="showCreateModal = false">
      <div class="p-4 flex flex-col gap-2">
        <h1>Create new photo template</h1>
        <Input v-model="url" placeholder="Image URL" />
        <InputSelect v-model="gender" :options="['male', 'female']" placeholder="Gender" />
        <ButtonPrimary size="sm" @click="createPhotoTemplate">
          Create
        </ButtonPrimary>
      </div>
    </Popup>
    <Popup v-if="showViewModal" size="xl" @close="showViewModal = false; selectedPhotoTemplate = null">
      <div v-if="selectedPhotoTemplate" class="flex flex-col gap-2">
        <h1>View photo template</h1>
        <Input v-model="selectedPhotoTemplate.name" placeholder="Name" label="Name" />
        <Input v-model="selectedPhotoTemplate.prompt" placeholder="Prompt" label="Prompt" />
        <InputSelect v-model="selectedPhotoTemplate.gender" :options="['male', 'female']" placeholder="Gender" label="Gender" />
        <Input v-for="key in Object.keys(selectedPhotoTemplate.defaults)" :key="key" :label="key" :value="selectedPhotoTemplate.defaults[key]" />
        <ButtonPrimary size="sm" @click="updatePhotoTemplate">
          Update
        </ButtonPrimary>
      </div>
    </Popup>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      photoTemplates: [],
      showCreateModal: false,
      showViewModal: false,
      selectedPhotoTemplate: null,
      url: null,
      gender: 'male',
      filter: {
        status: 'all',
        gender: 'all'
      }
    }
  },
  computed: {
    filteredPhotoTemplates () {
      return this.photoTemplates.filter((photoTemplate) => {
        if (this.filter.status !== 'all') {
          return photoTemplate.status === this.filter.status
        }
        if (this.filter.gender !== 'all') {
          return photoTemplate.gender === this.filter.gender
        }
        return true
      })
    }
  },
  watch: {
    filter: {
      handler () {
        // Update route query params without reloading the page
        this.$router.replace({
          query: {
            status: this.filter.status,
            gender: this.filter.gender
          }
        })
      },
      deep: true
    }
  },
  async mounted () {
    this.filter.status = this.$route.query.status || 'all'
    this.filter.gender = this.$route.query.gender || 'all'
    await this.fetchData()
  },
  methods: {
    updatePhotoTemplate () {
      this.$axios.$post('/admin/studio/phototemplate/update', {
        photoTemplateId: this.selectedPhotoTemplate._id,
        name: this.selectedPhotoTemplate.name,
        gender: this.selectedPhotoTemplate.gender,
        defaults: this.selectedPhotoTemplate.defaults
      }).then(({ success, data }) => {
        if (success) {
          this.$toast.success('Photo template updated')
          this.showViewModal = false
          this.selectedPhotoTemplate = null
          this.fetchData()
        }
      }).catch((err) => {
        this.$toast.error(err.response.data.message)
      }).finally(() => {
        this.$loading.hide()
      })
    },
    viewPhotoTemplate (photoTemplateId) {
      this.showViewModal = true
      this.selectedPhotoTemplate = this.photoTemplates.find(photoTemplate => photoTemplate._id === photoTemplateId)
    },
    createPhotoTemplate () {
      this.$axios.$post('/admin/studio/phototemplate/create', {
        url: this.url,
        gender: this.gender
      }).then((success, data) => {
        console.log(success, data)
        if (!success) {
          this.$toast.error(data.message)
          return
        }
        this.showCreateModal = false
        this.url = null
        this.$toast.success('Photo template created, reloading page in 20 seconds')
      }).catch((err) => {
        this.$toast.error(err.response.data.message)
      }).finally(() => {
        this.$loading.hide()
      })
    },
    togglePhotoTemplateStatus (photoTemplateId) {
      this.$axios.$post('/admin/studio/phototemplate/toggle-status', {
        photoTemplateId
      }).then(({ success, data }) => {
        if (success) {
          this.$toast.success('Photo template status updated')
          this.photoTemplates = this.photoTemplates.map((photoTemplate) => {
            if (photoTemplate._id === photoTemplateId) {
              photoTemplate.status = data.status
            }
            return photoTemplate
          })
        }
      }).catch((err) => {
        this.$toast.error(err.response.data.message)
      }).finally(() => {
        this.$loading.hide()
      })
    },
    async fetchData () {
      try {
        this.$loading.show()
        const { success, data } = await this.$axios.$get('/admin/studio/phototemplate', {
          params: {
            offset: this.photoTemplates.length,
            limit: 100
          }
        })

        if (!success) {
          this.$toast.error('Failed to fetch data')
          return
        }
        this.photoTemplates = data
      } catch (error) {
        console.log(error)
        this.$toast.error('Failed to fetch data')
      } finally {
        this.$loading.hide()
      }
    },
    deletePhotoTemplate (photoTemplateId) {
      this.$axios.$post('/admin/studio/phototemplate/delete', {
        photoTemplateId
      }).then(({ success, data }) => {
        if (success) {
          this.$toast.success('Photo template deleted')
          this.photoTemplates = this.photoTemplates.filter(photoTemplate => photoTemplate._id !== photoTemplateId)
        }
      }).catch((err) => {
        this.$toast.error(err.response.data.message)
      }).finally(() => {
        this.$loading.hide()
      })
    }
  }
}
</script>

<style>

</style>
