<template>
  <div class="p-8">
    <div class="flex w-full justify-between space-x-2 pb-4">
      <Input v-model="search" placeholder="Search" class="w-full" />
      <InputSelect v-model="searchBy" :options="allowedSearched" class="w-1/4" />
      <ButtonPrimary size="sm" @click="fetchData">
        Search
      </ButtonPrimary>
    </div>
    <Card class="mx-auto w-full">
      <LoadingWrapper :is-loading="isLoading">
        <Table :head="['Date', 'Status', 'UID', 'Amount', 'Amount refunded', 'Provider', 'Organization', 'Models', 'TransactionID', 'Actions']">
          <template v-for="item in transactions">
            <TableRow :key="item._id" class="group">
              <TableItem>{{ formatDate(item.createdOn) }}</TableItem>
              <TableItem>
                <span :class="{'text-orange-500': item.status === 'refunded', 'text-green-500': item.status === 'complete'}">{{ item.status }}</span>
              </TableItem>
              <TableItem>
                <nuxt-link class="underline text-sm" target="_blank" :to="`/admin/user?search=${item.uid}&searchBy=uid`">
                  <span class="group-hover:hidden">{{ item?.uid?.slice(0, 4) }}...{{ item?.uid?.slice(-4) }}</span>
                  <span class="hidden group-hover:block">{{ item?.uid }}</span>
                </nuxt-link>
              </TableItem>
              <TableItem>
                {{ formatPrice(item.amountTotal/100, item.currency) }}
                <span v-if="item?.items?.length > 0 && item?.items[0]?.quantity > 1" class="text-xs text-gray-500">
                  ({{ item.items[0].quantity }}x)
                </span>
              </TableItem>
              <TableItem>
                <span v-if="item.status === 'refunded'">
                  {{ formatPrice((item.amountRefunded || item.amountTotal) /100, item.currency) }}
                </span>
              </TableItem>
              <TableItem>{{ item.paymentProvider }}</TableItem>
              <TableItem>
                <nuxt-link v-if="item.organization" class="underline text-sm" target="_blank" :to="`/admin/organization?search=${item.organization}&searchBy=_id`">
                  <span class="group-hover:hidden">{{ item.organization.slice(0, 8) }}...</span>
                  <span class="hidden group-hover:block">{{ item.organization }}</span>
                </nuxt-link>
                <span v-else class="text-gray-400">-</span>
              </TableItem>
              <TableItem>
                <nuxt-link class="underline text-sm" target="_blank" :to="`/admin/model?search=${item.uid}&searchBy=uid`">
                  View models
                </nuxt-link>
              </TableItem>
              <!-- <TableItem>{{ item?.stripe?.customerId || item?.paypal?.customerId || item?.lemonsqueezy?.customerId }}</TableItem> -->
              <TableItem>
                <span class="hidden group-hover:block">{{ item?.transactionId }}</span>
                <span class="block group-hover:hidden">{{ item?.transactionId?.slice(0, 4) }}...{{ item?.transactionId?.slice(-4) }}</span>
              </TableItem>
              <TableItem>
                <a v-if="item?.paymentProvider === 'paypal'" class="underline" :href="`https://www.paypal.com/activity/payment/${item.transactionId}`" target="_blank">View transaction</a>
                <a v-if="item?.paymentProvider === 'stripe'" class="underline" :href="`https://dashboard.stripe.com/payments/${item?.id}`" target="_blank">View transaction</a>
                <a v-if="item?.paymentProvider === 'lemonsqueezy'" class="underline" :href="`https://app.lemonsqueezy.com/person/customers/${item?.lemonsqueezy?.customerId}`" target="_blank">View customer</a>
              </TableItem>
              <TableItem>
                <LoadingWrapper :is-loading="isLoading">
                  <div class="flex items-center justify-start space-x-2">
                    <ButtonDelete size="xs" @click="refundTransaction(item)">
                      Refund
                    </ButtonDelete>
                    <ButtonWhite size="xs" @click="showRefundModel = true; refundModel.amountToRefund = item.amountTotal; refundModel.item = item">
                      Refund partially
                    </ButtonWhite>
                  </div>
                </LoadingWrapper>
              </TableItem>
            </TableRow>
          </template>
        </Table>
        <PaginationInfinity v-if="transactions.length > 50" @fetch="fetchData($event)" />
      </LoadingWrapper>
    </Card>
    <Modal v-if="showRefundModel" title="Refund transaction" @close="showRefundModel = false; refundModel.amount = null; refundModel.item = null">
      <LoadingWrapper :is-loading="isLoading">
        <div class="p-4 min-w-[400px] space-y-4">
          <h3 class="font-bold text-lg">
            Refund partially
          </h3>
          <Input :value="refundModel.amountToRefund / 100" placeholder="Amount" @input="refundModel.amountToRefund = $event * 100" />
          <ButtonDelete v-if="refundModel.amountToRefund > 0 && refundModel.amountToRefund <= refundModel.item.amountTotal" size="sm" @click="refundTransaction(refundModel.item, refundModel.amountToRefund)">
            Refund
          </ButtonDelete>
          <span v-else class="text-xs text-red-500">Amount should be less than the total amount of ${{ refundModel.item.amountTotal/100 }}</span>
        </div>
      </LoadingWrapper>
    </Modal>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      allowedSearched: ['uid', 'displayName', 'email', 'transactionId', 'organization'],
      transactions: [],
      isLoading: true,
      search: null,
      showRefundModel: false,
      refundModel: {
        item: null,
        amountToRefund: null
      },
      searchBy: 'email'

    }
  },
  watch: {
    '$route.query': {
      handler (newQuery) {
        const { search, searchBy, id } = newQuery

        if (search !== undefined) {
          this.search = search
        }

        if (searchBy && this.allowedSearched.includes(searchBy)) {
          this.searchBy = searchBy
        }

        // Legacy support for 'id' parameter
        if (id && !search) {
          this.search = id
          this.searchBy = '_id'
        }

        this.fetchData()
      },
      deep: true
    }
  },
  async mounted () {
    const { search, searchBy, id } = this.$route.query

    if (search) {
      this.search = search
    }

    if (searchBy && this.allowedSearched.includes(searchBy)) {
      this.searchBy = searchBy
    }

    // Legacy support for 'id' parameter (maps to _id search)
    if (id && !search) {
      this.search = id
      this.searchBy = '_id'
    }
    await this.fetchData()
    this.isLoading = false
  },
  methods: {
    async fetchData () {
      this.isLoading = true
      try {
        const response = await this.$axios.$get('/admin/transaction', {
          params: {
            page: this.transactions.length,
            // search: this.search
            ...(this.search && this.search.length > 0) ? { search: this.search.trim() } : {},
            ...(this.search && this.search.length > 0) ? { searchBy: this.searchBy.trim() } : {}
          }
        })
        this.transactions = response
        this.isLoading = false
      } catch (err) {
        this.$toast.error('Error fetching transactions')
        this.isLoading = false
      }
    },
    refundTransaction (item, amount = null) {
      if (amount && amount > item.amountTotal) {
        this.$toast.error('Amount should be less than the total amount')
        return
      }
      this.isLoading = true
      this.$axios.$post('/admin/transaction/refund', {
        paymentIntentId: item._id,
        ...(amount) ? { amountToRefund: amount } : {}
      })
        .then((response) => {
          if (response.success) {
            this.$toast.success('Transaction refunded')
            this.transactions = this.transactions.map((transaction) => {
              if (transaction._id === item._id) {
                transaction.status = 'refunded'
                transaction.amountRefunded = amount
              }
              return transaction
            })
            this.showRefundModel = false
            this.refundModel.amount = null
            this.refundModel.item = null
          } else {
            this.$toast.error('Error refunding transaction')
          }
        }).catch(() => {
          this.$toast.error('Error refunding transaction')
        })
        .finally(() => {
          this.isLoading = false
        })
    }
  }
}
</script>

<style></style>
