<template>
  <section class="flex min-h-screen items-start justify-center p-2 text-black md:p-4">
    <div class="w-full max-w-7xl p-2 md:p-8 space-y-4">
      <AppTitle title="Giftcards" sub-title="Create and manage giftcard orders.">
        <ButtonPrimary size="sm" @click="openCreateModal">
          Create new
        </ButtonPrimary>
      </AppTitle>
      <LoadingWrapper :is-loading="isLoading">
        <TableSortable :head="tableHead" @sort="handleSort">
          <TableRow v-for="item in sortedItems" :key="item._id">
            <TableItem>
              {{ formatDate(item.createdAt) }}
            </TableItem>
            <TableItem>
              <nuxt-link :to="`/admin/user?search=${item.owner.uid}&searchBy=uid`">
                {{ item.owner.email }}
              </nuxt-link>
            </TableItem>
            <TableItem>
              <nuxt-link :to="`/admin/organization?search=${item.organization}&searchBy=id`">
                {{ item.organization }}
              </nuxt-link>
            </TableItem>
            <TableItem>
              {{ item.coupons.length }}
            </TableItem>
            <TableItem>
              <div class="flex space-x-2">
                <ButtonWhite size="sm" @click="setupEditItem(item)">
                  Edit
                </ButtonWhite>
                <ButtonDelete @click="deleteGiftcard(item._id)">
                  Delete
                </ButtonDelete>
              </div>
            </TableItem>
          </TableRow>
        </TableSortable>
      </LoadingWrapper>
    </div>
    <Modal v-if="showCreateModal" @close="showCreateModal = false">
      <LoadingWrapper :is-loading="isLoading">
        <div class="flex flex-col p-4 space-y-4 w-[500px]">
          <div class="space-y-1.5">
            <h2 class="text-xl font-bold">
              Create new giftcards
            </h2>
            <p class="text-sm text-gray-500">
              Create a new giftcard order. Ever order can have multiple Coupons created for it. Coupons can be redeemed on /redeem.
            </p>
          </div>
          <Input v-model="form.quantity" label="Quantity*" placeholder="Quantity" />
          <Input v-model="form.prefix" label="Prefix" placeholder="Prefix" description="This will be added to the beginning of the giftcard code." />
          <Input v-model="form.uid" label="User UID*" placeholder="User UID" description="The user who purchases it." />
          <Input v-model="form.organization" label="Organization" placeholder="Organization ID" description="Organization who purchases the giftcards." />
          <InputSelect v-model="form.package" label="Package" placeholder="Package" :options="['small', 'medium', 'large']" />
          <ButtonPrimary @click="createGiftcard">
            Create
          </ButtonPrimary>
        </div>
      </LoadingWrapper>
    </Modal>
    <Modal v-if="showEditModal" @close="closeEditModal">
      <LoadingWrapper :is-loading="isLoading">
        <div class="flex flex-col p-4 space-y-4 w-[700px]">
          <h2 class="text-xl font-bold">
            Edit giftcard
          </h2>
          <div class="grid grid-cols-2 gap-6">
            <div class="col-span-1 space-y-3">
              <Input v-model="form.prefix" label="Prefix" placeholder="Prefix" description="This will be added to the beginning of the giftcard code." />
              <Input v-model="form.uid" label="User UID*" placeholder="User UID" description="The user who purchases it." />
              <Input v-model="form.organization" label="Organization _ID" placeholder="Organization _ID" description="Organization who purchases the giftcards." />
              <InputSelect v-model="form.package" label="Package" placeholder="Package" :options="['small', 'medium', 'large']" />
              <ButtonPrimary @click="editGiftcard">
                Edit
              </ButtonPrimary>
            </div>
            <div class="col-span-1 space-y-1">
              <span class="text-sm text-gray-500">
                Add new coupons
              </span>

              <div class="flex space-x-2">
                <Input v-model="newCouponQuantity" placeholder="Quantity" />
                <ButtonWhite size="xs" @click="addCoupon">
                  Add
                </ButtonWhite>
              </div>
              <span class="text-sm text-gray-500">
                Current coupons ({{ editItem.coupons.length }})
              </span>
              <ul class="bg-gray-100 p-2 rounded-lg shadow-inner space-y-2">
                <li v-for="coupon in editItem.coupons" :key="coupon._id" class="group py-1.5 px-2 rounded-lg bg-white flex justify-between items-center">
                  <span class="text-sm">{{ coupon.code }}</span>
                  <div>
                    <span v-if="coupon.uses > 0" class="block group-hover:hidden text-xs text-orange-500">
                      Used
                    </span>
                    <ButtonDelete class="group-hover:block hidden" @click="deleteCoupon(coupon._id)">
                      Delete
                    </ButtonDelete>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </loadingwrapper>
      </div>
      </LoadingWrapper>
    </Modal>
  </section>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      isLoading: false,
      showCreateModal: false,
      showEditModal: false,
      form: {
        quantity: 1,
        prefix: null,
        uid: null,
        organization: null,
        package: 'medium'
      },
      editItem: null,
      giftcards: [],
      tableHead: ['Created At', 'User', 'Organization', 'Coupons', 'Actions'],
      newCouponQuantity: 1
    }
  },
  computed: {
    sortedItems () {
      return [...this.giftcards].sort((a, b) => a.createdAt - b.createdAt)
    }
  },
  mounted () {
    this.fetchData()
  },
  methods: {
    closeEditModal () {
      this.showEditModal = false
      this.editItem = null
      this.form = {
        quantity: 1,
        prefix: null,
        uid: null,
        organization: null,
        package: 'medium'
      }
    },
    setupEditItem (item) {
      this.form = {
        quantity: item.coupons.length,
        prefix: item.prefix,
        uid: item.owner.uid,
        organization: item.organization,
        package: item.package
      }
      this.editItem = item
      this.showEditModal = true
    },
    handleAction (action, item) {
      if (action === 'delete') {
        this.deleteGiftcard(item._id)
      }
    },
    fetchData () {
      this.isLoading = true
      this.$axios.get('/admin/giftcard').then((res) => {
        this.giftcards = res.data
        this.isLoading = false
      }).catch((error) => {
        this.isLoading = false
        this.$toast.error(error.response.data.message)
      })
    },
    openCreateModal () {
      this.showCreateModal = true
    },
    createGiftcard () {
      this.isLoading = true
      const { quantity, prefix, uid, organization } = this.form

      if (!uid) {
        this.$toast.error('Please fill out all fields.')
        this.isLoading = false
        return
      }

      this.$axios.post('/admin/giftcard', {
        quantity,
        prefix,
        uid,
        organization
      }).then(async () => {
        this.showCreateModal = false
        this.form = {
          quantity: 1,
          prefix: '',
          uid: '',
          organization: ''
        }
        await this.fetchData()
        this.isLoading = false
      }).catch((error) => {
        this.isLoading = false
        this.$toast.error(error.response.data.message)
      })
    },
    deleteGiftcard (id) {
      this.isLoading = true
      this.$axios.delete(`/admin/giftcard/${id}`).then(() => {
        this.fetchData()
        this.$toast.success('Deleted giftcard and all associated coupons.')
        this.isLoading = false
      }).catch((error) => {
        this.isLoading = false
        this.$toast.error(error.response.data.message)
      })
    },
    deleteCoupon (id) {
      this.isLoading = true
      this.$axios.delete(`/admin/giftcard/coupon/${id}`).then(() => {
        this.editItem.coupons = this.editItem.coupons.filter(coupon => coupon._id !== id)
        this.$toast.success('Deleted coupon.')
        this.isLoading = false
      })
    },
    addCoupon () {
      this.isLoading = true
      const { editItem, newCouponQuantity } = this
      if (!editItem) { return }

      this.$axios.post(`/admin/giftcard/coupon/${editItem._id}`, {
        quantity: newCouponQuantity
      }).then(() => {
        this.fetchData()
        this.showEditModal = false
        this.$toast.success('Added coupon.')
        this.isLoading = false
      }).catch((error) => {
        this.isLoading = false
        this.$toast.error(error.response.data.message)
      })
    }
  }
}
</script>

<style>

</style>
