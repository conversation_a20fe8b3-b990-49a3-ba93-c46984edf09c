<template>
  <div class="p-8">
    <div class="flex w-full justify-between space-x-2 pb-4">
      <Input v-model="search" placeholder="Search" class="w-full" @handleEnter="fetchData" />
      <InputSelect v-model="searchBy" :options="['_id', 'uid', 'displayName', 'email', 'organization']" class="w-1/4" />
      <ButtonPrimary size="sm" @click="fetchData">
        Search
      </ButtonPrimary>
    </div>
    <Card class="mx-auto w-full">
      <LoadingWrapper :is-loading="isLoading">
        <Table :head="['Signed Up', 'Email', 'Unused packages', 'Org', 'UID', 'Payment Provider', 'Transactions', 'Models', 'Actions']">
          <template v-for="item in users">
            <!-- <template v-if="inSearch(item)"> -->
            <TableRow :key="item._id">
              <TableItem>{{ formatDate(item.createdAt) }}</TableItem>
              <TableItem>{{ item.email }}</TableItem>
              <TableItem>{{ item.packages }}</TableItem>
              <TableItem>
                <div>
                  <a v-if="item?.organization" :href="`/admin/organization?id=${item.organization._id || item.organization}`" class="hover:underline">
                    {{ item.organization?.name || item.organization }}
                  </a>
                  <p v-if="item?.role && item.role !== 'User'" class="text-xs mt-1" :style="{ color: item.role === 'TeamLead' ? '#059669' : '#6b7280' }">
                    {{ getRoleDisplayName(item.role) }}
                  </p>
                </div>
              </TableItem>
              <TableItem>{{ stripLength(item.uid, 6) }}</TableItem>
              <TableItem>
                <a v-if="item?.user?.lemonsqueezy?.accountId" class="underline" :href="`https://app.lemonsqueezy.com/person/customers/${item?.user?.lemonsqueezy?.accountId}`" target="_blank">
                  Lemonsqueezy
                </a>
                <a v-else-if="item?.stripe?.accountId" class="underline" :href="`https://dashboard.stripe.com/customers/${item.stripe.accountId}`" target="_blank">
                  Stripe
                </a>
                <nuxt-link v-if="item?.paypal?.accountId" class="underline" :to="`/admin/transactions?search=${item.uid}&searchBy=uid`">
                  PayPal
                </nuxt-link>
                <!-- <p class="text-xs italic text-gray-500">
                  {{ item?.stripe?.accountId && item?.stripe?.accountId?.includes("cus_") ? "Stripe" : item?.paypal?.accountId ? "PayPal" : "None" }}
                </p> -->
              </TableItem>
              <TableItem>
                <NuxtLink class="underline" :to="`/admin/transactions?search=${item.uid}&searchBy=uid`">
                  View all
                </NuxtLink>
              </TableItem>
              <TableItem>
                <template v-if="item.models && item.models.length > 0">
                  <nuxt-link
                    v-if="item.models.length === 1"
                    :to="`/admin/model/${item.models[0]}`"
                    class="underline"
                  >
                    View result
                  </nuxt-link>
                  <nuxt-link
                    v-else
                    :to="`/admin/model?search=${item.uid}&searchBy=uid`"
                    class="underline"
                  >
                    View all ({{ item.models.length }})
                  </nuxt-link>
                </template>
                <span v-else class="text-gray-500 italic">No models</span>
              </TableItem>
              <TableItem>
                <ButtonDropdown v-if="dropdownItems.length > 0" size="sm" :items="dropdownItems" title="Actions" @select="handleDropdown($event, item.uid)" />
              </TableItem>
            </TableRow>
            <!-- </template> -->
          </template>
        </Table>
      </LoadingWrapper>
    </Card>
  </div>
</template>

<script>
import copy from 'copy-to-clipboard'

export default {
  layout: 'admin',
  data () {
    return {
      users: [],
      coupons: [],
      isLoading: true,
      search: null,
      searchBy: 'email',
      dropdownItems: [
        { title: 'Refund', value: 'refund' },
        { title: 'Add small package', value: 'add:package:small' },
        { title: 'Add medium package', value: 'add:package:medium' },
        { title: 'Add large packages', value: 'add:package:large' },
        { title: 'Block', value: 'block' },
        { title: 'Get user token', value: 'user-token' },
        { title: 'Remove from team', value: 'remove-team' },
        { title: 'See used coupons', value: 'see-used-coupons' }
      ]
    }
  },

  async created () {
    if (this.$route.query.user) {
      const query = this.$route.query.user
      this.search = query
      if (query.includes('@')) {
        this.searchBy = 'email'
      } else {
        this.searchBy = '_id'
      }
    }

    if (this.$route.query.search) {
      this.search = this.$route.query.search
    }
    if (this.$route.query.searchBy) {
      this.searchBy = this.$route.query.searchBy
    }
    await this.fetchData()
    this.isLoading = false
  },
  methods: {
    getRoleDisplayName (role) {
      switch (role) {
        case 'TeamLead':
          return 'Team Owner'
        case 'TeamMember':
          return 'Team Member'
        default:
          return role
      }
    },
    handleDropdown (event, uid) {
      if (event === 'refund') {
        this.refundUser(uid)
      }
      if (event === 'user-token') {
        this.getUserToken(uid)
      }
      if (event === 'block') {
        this.blockUser(uid)
      }
      if (event.includes('add:package')) {
        const pack = event.split(':')[2]
        this.addPackage(uid, pack)
      }
      if (event === 'activate') {
        this.activateUser(uid)
      }
      if (event === 'remove-team') {
        this.removeTeam(uid)
      }
      if (event === 'see-used-coupons') {
        this.showUsedCoupons(uid)
      }
    },
    async removeTeam (uid) {
      await this.$axios.$post(`/admin/user/remove-team/${uid}`)
      this.fetchData()
    },
    getUserToken (uid) {
      this.$axios.$get('/admin/user/token/' + uid)
        .then((data) => {
          console.log(data.token)
          copy(process.env.BASE_URL + '/auth/social?token=' + data.token)
          this.$toast.success('Token copied to clipboard')
        })
        .catch((err) => {
          this.$toast.error(err)
        })
    },
    blockUser (uid) {
      this.$axios
        .$post('/admin/user/block/' + uid)
        .then(() => {
          this.$toast.success('Blocked')
          this.fetchData()
        })
        .catch((err) => {
          this.$toast.error(err)
        })
    },
    refundUser (uid) {
      this.$axios
        .$post('/admin/user/refund/' + uid)
        .then(() => {
          this.$toast.success('Refunded')
          this.fetchData()
        })
        .catch((err) => {
          this.$toast.error(err)
        })
    },
    addPackage (uid, pack) {
      this.$axios
        .$post('/admin/user/add-package/' + uid, { package: pack })
        .then(() => {
          this.$toast.success('Token added')
          this.fetchData()
        })
        .catch((err) => {
          this.$toast.error(err)
        })
    },
    async fetchData () {
      this.isLoading = true

      // Update URL with search parameters
      const query = { ...this.$route.query }
      if (this.search && this.search.length > 0) {
        query.search = this.search.trim()
        query.searchBy = this.searchBy.trim()
      } else {
        delete query.search
        delete query.searchBy
      }

      // Update the URL without page reload
      this.$router.replace({ query })

      this.users = await this.$axios.$get('/admin/user', {
        params: {
          ...(this.search && this.search.length > 0) ? { search: this.search.trim() } : {},
          ...(this.search && this.search.length > 0) ? { searchBy: this.searchBy.trim() } : {}
        }
      })
      this.coupons = await this.$axios.$get('/admin/coupons')
      this.isLoading = false
    },
    inSearch (item) {
      if (this.search === null) {
        return true
      }
      const { search } = this
      if (!search || search.length === 0) {
        return true
      }
      if (!item) {
        return true
      }
      const { uid, email, stripe, tunes } = item
      if (uid && uid.toLowerCase().includes(search.toLowerCase())) {
        return true
      }
      if (email && email.toLowerCase().includes(search.toLowerCase())) {
        return true
      }
      if (stripe && stripe.accountId && stripe.accountId.toLowerCase().includes(search.toLowerCase())) {
        return true
      }
      if (tunes.includes(parseInt(search))) {
        return true
      }
      return false
    },
    showUsedCoupons (uid) {
      const usedCoupons = []
      for (const coupon of this.coupons) {
        if (coupon.claimedBy === uid || coupon.claimHistory?.includes(uid)) {
          usedCoupons.push(coupon.code)
        }
      }

      if (usedCoupons.length === 0) {
        alert('This user has used no coupons')
      } else {
        alert('These are the coupons used by this user:\n' + usedCoupons.join('\n'))
      }
    }
  }
}
</script>

<style></style>
