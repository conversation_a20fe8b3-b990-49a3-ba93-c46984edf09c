<template>
  <div class="p-8">
    <div class="flex w-full justify-between space-x-2 pb-4">
      <Input v-model="search" placeholder="Search" class="w-full" />
      <InputSelect v-model="searchBy" :options="['slug', 'title']" class="w-1/4" />
      <ButtonWhite size="sm" @click="fetchData">
        Search
      </ButtonWhite>
      <nuxt-link to="/admin/email/create">
        <ButtonPrimary size="sm">
          <span class="whitespace-nowrap">
            Create new
          </span>
        </ButtonPrimary>
      </nuxt-link>
    </div>
    <Card class="mx-auto w-full">
      <!-- <Table v-if="!isLoading" :head="['Slug', 'Title', 'Sends/Opens/Clicks', 'Open Rate', 'Created', 'Actions']"> -->
      <Table v-if="!isLoading" :head="['Slug', 'Title', 'Sends', 'Created', 'Actions']">
        <template v-for="item in items">
          <!-- <template v-if="inSearch(item)"> -->
          <TableRow :key="item._id">
            <TableItem>{{ item.slug }}</TableItem>
            <TableItem>{{ item.title }}</TableItem>
            <TableItem>
              <template v-if="item.analytics?.sends > 0">
                {{ item.analytics.sends }}
                <!-- {{ item.analytics.opens }}/ -->
                <!-- {{ item.analytics.clicks }} -->
              </template>
              <template v-else>
                Not send yet
              </template>
            </TableItem>
            <!-- <TableItem>
              <template v-if="item.analytics?.sends > 0">
                {{ ((item.analytics.opens / item.analytics.sends) * 100).toFixed(2) }}%
              </template>
            </TableItem> -->
            <TableItem>{{ formatLongDate(item.createdAt) }}</TableItem>
            <TableItem>
              <div class="flex space-x-2 items-center justify-start">
                <a target="_blank" :href="`${(env === 'development' ? 'http://localhost:5000' : 'https://server.headshotpro.com')}/admin/email/test/${item.slug}`">
                  <ButtonWhite size="sm">
                    Preview
                  </ButtonWhite>
                </a>
                <nuxt-link :to="`/admin/email/create?edit=true&id=${item._id}`">
                  <ButtonWhite size="sm">
                    Edit
                  </ButtonWhite>
                </nuxt-link>
                <ButtonDelete @click="deleteItem(item._id)">
                  Delete
                </ButtonDelete>
              </div>
            </TableItem>
          </TableRow>
        </template>
      </Table>
      <!-- <template v-if="isLoading">
        <LoadingSpinner />
      </template>
      <template v-else>
        <PaginationInfinity @fetch="fetchData($event)" /> -->
    </Card>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      items: [],
      isLoading: true,
      searchBy: 'title',
      search: null
    }
  },

  async created () {
    if (this.$route.query.search) {
      this.search = this.$route.query.search
    }
    if (this.$route.query.searchBy) {
      this.searchBy = this.$route.query.searchBy
    }
    await this.fetchData()
    this.isLoading = false
  },
  methods: {
    async fetchData () {
      try {
        this.isLoading = true
        const items = await this.$axios.$get(`/admin/email/all?results=${this.items.length}`, {
          params: {
            ...(this.search && this.search.length > 0) ? { search: this.search.trim() } : {},
            ...(this.search && this.search.length > 0) ? { searchBy: this.searchBy.trim() } : {}
          }
        })
        if (this.search && this.search.length > 0) {
          this.items = items
        } else {
          this.items = [...this.items, ...items]
        }
        this.isLoading = false
      } catch (e) {
        this.isLoading = false
        console.log(e)
        this.$toast.error('Error fetching items')
      }
    },
    deleteItem (id) {
      this.$axios.$delete(`/admin/email/${id}`)
        .then(() => {
          this.items = this.items.filter(item => item._id !== id)
          this.$toast.success('Item deleted')
        })
        .catch(() => {
          this.$toast.error('Error deleting item')
        })
    }
  }
}
</script>

<style>

</style>
