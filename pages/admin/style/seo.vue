<template>
  <div class="w-full">
    <LoadingWrapper :is-loading="isLoading">
      <div class="mx-auto max-w-7xl space-y-2 px-4 py-4 sm:px-6 lg:px-8">
        <AdminStyleSeoHeader :title="style?.title || ''" :live="style?.seo?.live || false" @publish="publish" @unpublish="unpublish" />
        <AdminStyleSeoHero :styles="style" :headline="headline" :sub-headline="subHeadline" :prompts="prompts" @change="setText($event)" />
        <AdminStyleSeoFeatures
          v-if="headline && subHeadline"
          :styles="style"
          :headline="headline"
          :sub-headline="subHeadline"
          :sections="sections"
          :prompts="prompts"
          @change="setText($event)"
          @changeSection="setSection($event)"
        />
      </div>
    </LoadingWrapper>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      isLoading: true,
      style: null,
      headline: '',
      subHeadline: '',
      sections: [
        { type: 'what', title: '', description: '', image: null },
        { type: 'when', title: '', description: '', image: null },
        { type: 'who', title: '', description: '', image: null }
      ]
    }
  },
  computed: {
    prompts () {
      return {
        headlinePrompt: { role: 'user', content: `Act as a professional copywriter. Write a 3 headlines for a landingpage on my website. This landingpage is specifically about professional headshots taken with a ${this.style?.title || ''} background. Write with a SEO focus on the keywords: professional headshots in ${this.style?.title || ''}. Rules for the headline: max 12 words, single sentence.` },
        subHeadlinePrompt: { role: 'user', content: `Now write 3 descriptions for this headline: ${this.headline}. This page tells you more about the setting, the benefits of that setting and when this might be a good option for your headshot.` },
        whatPrompt: { role: 'user', content: `Write a short paragraph of 60 - 90 words about the setting. The title of this paragraph is ${this.sections[0].title}. Don't add the title to the response!` },
        whenPrompt: { role: 'user', content: `Write a short paragraph of 60 - 90 words about the benefits of this setting. The title of this paragraph is ${this.sections[1].title}. Don't add the title to the response!` },
        whoPrompt: { role: 'user', content: `Write a short paragraph of 60 - 90 words about when this might be a good option. The title of this paragraph is ${this.sections[2].title}. Don't add the title to the response!` }
      }
    }
  },
  watch: {
    style () {
      if (this.style) {
        this.sections[0].title = `What are ${this.style.title} headshots?`
        this.sections[1].title = `When you should use ${this.style.title} headhosts?`
        this.sections[2].title = `For who are ${this.style.title} headhosts?`
      }
    }
  },
  mounted () {
    this.fetchStyle()
  },
  methods: {
    async fetchStyle () {
      try {
        this.isLoading = true
        const { success, data, errorMessage } = await this.$axios.$get(`/admin/style/seo/${this.$route.query.id}`)
        if (!success) {
          throw new Error(errorMessage)
        }
        this.style = data
        if (data?.seo?.headline) {
          this.headline = data.seo.headline
        }
        if (data?.seo?.subHeadline) {
          this.subHeadline = data.seo.subHeadline
        }
        if (data?.seo?.sections) {
          this.sections.forEach((section) => {
            const foundSection = data.seo.sections.find(dataSection => dataSection.type === section.type)
            if (foundSection) {
              section.title = foundSection.title
              section.description = foundSection.description
              section.image = foundSection.image
            }
          })
        }
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = false
      }
    },
    async publish () {
      try {
        this.isLoading = true
        const id = this.$route.query.id
        const { headline, subHeadline, sections } = this

        if (!headline || !subHeadline || sections.some(section => !section.title || !section.description || !section.image)) {
          throw new Error('Please fill in all fields')
        }

        const formData = new FormData()
        formData.append('id', id)
        formData.append('headline', headline)
        formData.append('subHeadline', subHeadline)
        formData.append('sections', JSON.stringify(sections))
        formData.append('what', sections[0].image)
        formData.append('when', sections[1].image)
        formData.append('who', sections[2].image)

        const { success, errorMessage } = await this.$axios.$post('/admin/style/seo', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.$toast.success('Successfully published')
        this.fetchStyle()
        // this.$router.push('/admin/style');
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = false
      }
    },
    async unpublish () {
      try {
        this.isLoading = true
        const { success, errorMessage } = await this.$axios.$post('/admin/style/seo/unpublish', {
          id: this.$route.query.id
        })
        if (!success) {
          throw new Error(errorMessage)
        }
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = false
      }
    },
    setText ({ field, value }) {
      this[field] = value
    },
    setSection ({ index, field, value }) {
      this.sections[index][field] = value
    }
  }
}
</script>

<style></style>
