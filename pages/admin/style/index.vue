<template>
  <section class="flex min-h-screen items-start justify-center p-2 text-black md:p-4">
    <div class="w-full max-w-7xl p-2 md:p-8">
      <AppTitle title="Styles" sub-title="Background styles people can pick from">
        <Input
          v-model="minFavorites"
          type="number"
          name="minFavorites"
          placeholder="Minimum favorites"
          label="Minimum favorites"
        />
        <InputSearch placeholder="Search" @input="filterStyles($event)" />
        <ButtonPrimary size="sm" @click="openAddNewModal">
          Add new
        </ButtonPrimary>
      </AppTitle>

      <Card class="mx-auto w-full" inner-class="overflow-x-auto">
        <LoadingWrapper :is-loading="isLoading">
          <TableSortable :head="tableHead" @sort="handleSort">
            <TableRow
              v-for="style in sortedStyles.filter(style => style.usage?.favorite >= minFavorites)"
              :key="style._id"
              :class="{ 'opacity-50 hover:opacity-100': style.status !== 'active'}"
            >
              <TableItem>
                <div class="flex flex-col space-y-2">
                  <!-- Active/Inactive Status -->
                  <div class="flex items-center">
                    <span
                      :class="{
                        'bg-green-100 text-green-800 border-green-200': style.status === 'active',
                        'bg-gray-100 text-gray-800 border-gray-200': style.status !== 'active'
                      }"
                      class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border"
                    >
                      <!-- Active Icon -->
                      <svg v-if="style.status === 'active'" class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                      </svg>
                      <!-- Inactive Icon -->
                      <svg v-else class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                      </svg>
                      {{ style.status === 'active' ? 'Active' : 'Inactive' }}
                    </span>
                  </div>

                  <!-- Processing Status Badge -->
                  <div v-if="style.processingStatus && style.processingStatus !== 'completed'">
                    <span
                      :class="{
                        'bg-yellow-100 text-yellow-800 border-yellow-200': style.processingStatus === 'pending',
                        'bg-blue-100 text-blue-800 border-blue-200': style.processingStatus === 'processing',
                        'bg-red-100 text-red-800 border-red-200': style.processingStatus === 'failed'
                      }"
                      class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border"
                    >
                      <!-- Processing Icon -->
                      <svg v-if="style.processingStatus === 'processing'" class="animate-spin w-4 h-4 mr-1.5" fill="none" viewBox="0 0 24 24">
                        <circle
                          class="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          stroke-width="4"
                        />
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                      </svg>
                      <!-- Pending Icon -->
                      <svg v-else-if="style.processingStatus === 'pending'" class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                      </svg>
                      <!-- Failed Icon -->
                      <svg v-else-if="style.processingStatus === 'failed'" class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                      </svg>
                      {{ style.processingStatus === 'pending' ? 'Pending' : style.processingStatus === 'processing' ? 'Processing' : 'Failed' }}
                    </span>
                  </div>

                  <span v-if="style.organizationIds && style.organizationIds.length > 0" class="text-xs text-gray-500">(Org. only)</span>
                </div>
              </TableItem>
              <TableItem>
                <div class="flex flex-col space-y-1">
                  <span class="text-sm font-medium text-gray-900">
                    {{ formatDateRelative(style.createdAt) }}
                  </span>
                  <span class="text-xs text-gray-500" :title="formatDateFull(style.createdAt)">
                    {{ formatDateShort(style.createdAt) }}
                  </span>
                </div>
              </TableItem>
              <TableItem>
                <div v-if="style.image?.maleImage || style.image?.femaleImage" class="flex items-center space-x-2">
                  <!-- Male Image -->
                  <div v-if="style.image?.maleImage" class="relative">
                    <img
                      :src="style.image.maleImage"
                      class="w-16 h-16 rounded-lg object-cover cursor-pointer border-2 border-gray-200 hover:border-blue-300 transition-colors"
                      :alt="`Male ${style.title}`"
                      @click="showEnlargedImage(style.image.maleImage)"
                      @error="$event.target.style.display='none'"
                    >
                    <div class="absolute -bottom-1 -right-1 bg-blue-100 text-blue-800 text-xs px-1.5 py-0.5 rounded-full border border-blue-200 font-medium">
                      M
                    </div>
                  </div>

                  <!-- Female Image -->
                  <div v-if="style.image?.femaleImage" class="relative">
                    <img
                      :src="style.image.femaleImage"
                      class="w-16 h-16 rounded-lg object-cover cursor-pointer border-2 border-gray-200 hover:border-pink-300 transition-colors"
                      :alt="`Female ${style.title}`"
                      @click="showEnlargedImage(style.image.femaleImage)"
                      @error="$event.target.style.display='none'"
                    >
                    <div class="absolute -bottom-1 -right-1 bg-pink-100 text-pink-800 text-xs px-1.5 py-0.5 rounded-full border border-pink-200 font-medium">
                      F
                    </div>
                  </div>
                </div>

                <!-- No Images Placeholder -->
                <div v-else class="w-16 h-16 rounded-lg bg-gray-100 border-2 border-dashed border-gray-300 flex items-center justify-center">
                  <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
              </TableItem>
              <TableItem>
                <div class="flex flex-col space-y-1 max-w-xs">
                  <span class="text-sm font-semibold text-gray-900 capitalize">
                    {{ style.title }}
                  </span>
                  <p class="text-xs text-gray-600 line-clamp-2 leading-relaxed" :title="style.location">
                    {{ style.location?.length > 100 ? style.location.substring(0, 100) + '...' : style.location }}
                  </p>
                </div>
              </TableItem>
              <TableItem>
                <div v-if="style.usage" class="space-y-2">
                  <!-- Usage Stats -->
                  <div class="grid grid-cols-2 gap-2 text-xs">
                    <div class="bg-gray-50 px-2 py-1 rounded">
                      <span class="text-gray-500">Total:</span>
                      <span class="font-medium ml-1">{{ style.usage?.total || 0 }}</span>
                    </div>
                    <div class="bg-blue-50 px-2 py-1 rounded">
                      <span class="text-blue-600">Favorites:</span>
                      <span class="font-medium ml-1 text-blue-800">{{ style.usage?.favorite || 0 }}</span>
                    </div>
                  </div>

                  <!-- Conversion Rate with Visual Indicator -->
                  <div class="space-y-1">
                    <div class="flex items-center justify-between text-xs">
                      <span class="text-gray-600">Conversion Rate</span>
                      <span
                        class="font-medium"
                        :class="{
                          'text-green-600': (style.usage?.conversionRate || 0) >= 0.15,
                          'text-yellow-600': (style.usage?.conversionRate || 0) >= 0.10 && (style.usage?.conversionRate || 0) < 0.15,
                          'text-red-600': (style.usage?.conversionRate || 0) < 0.10
                        }"
                      >
                        {{ ((style.usage?.conversionRate || 0) * 100).toFixed(1) }}%
                      </span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-1.5">
                      <div
                        class="h-1.5 rounded-full transition-all duration-300"
                        :class="{
                          'bg-green-500': (style.usage?.conversionRate || 0) >= 0.15,
                          'bg-yellow-500': (style.usage?.conversionRate || 0) >= 0.10 && (style.usage?.conversionRate || 0) < 0.15,
                          'bg-red-500': (style.usage?.conversionRate || 0) < 0.10
                        }"
                        :style="`width: ${Math.min((style.usage?.conversionRate || 0) * 100, 100)}%`"
                      />
                    </div>
                  </div>
                </div>
                <div v-else class="text-xs text-gray-400 italic">
                  No usage data
                </div>
              </TableItem>
              <TableItem>
                <ButtonDropdown
                  title="Actions"
                  theme="v2"
                  size="sm"
                  :items="getActionItems(style)"
                  @select="handleAction($event, style._id)"
                />
              </TableItem>
            </TableRow>
          </TableSortable>
        </LoadingWrapper>
      </Card>

      <Modal v-if="showEditModal" @close="closeModal">
        <LoadingWrapper :is-loading="isLoading">
          <div class="flex flex-col p-4 space-y-4 w-[500px]">
            <h2 class="text-xl font-bold">
              <template v-if="form._id">
                Edit style item
              </template>
              <template v-else>
                Add new style item
              </template>
            </h2>

            <!-- Mode Selection -->
            <div class="space-y-3">
              <label class="block text-sm font-medium text-gray-700">Choose Input Method</label>
              <div class="flex space-x-4">
                <label class="inline-flex items-center">
                  <input
                    v-model="inputMode"
                    type="radio"
                    value="manual"
                    class="form-radio h-4 w-4 text-blue-600"
                    @change="onInputModeChange"
                  >
                  <span class="ml-2 text-sm">Manual Entry</span>
                </label>
                <label class="inline-flex items-center">
                  <input
                    v-model="inputMode"
                    type="radio"
                    value="image"
                    class="form-radio h-4 w-4 text-blue-600"
                    @change="onInputModeChange"
                  >
                  <span class="ml-2 text-sm">Upload Image</span>
                </label>
              </div>
            </div>

            <!-- Manual Entry Fields -->
            <div v-if="inputMode === 'manual'" class="space-y-4">
              <Input v-model="form.title" label="Style Title" placeholder="e.g., Garden, Office, Studio" required />
              <Input v-model="form.location" label="Style Description" placeholder="Describe the background/location in detail" required />
            </div>

            <!-- Image Upload Field -->
            <div v-if="inputMode === 'image'" class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Upload Background Image</label>
              <div class="text-xs text-gray-500 mb-2">
                Upload an image to automatically generate title and description. Max size: 5MB. Formats: JPEG, PNG, JPG
              </div>
              <input
                id="fileextra"
                ref="fileextra"
                name="fileextra"
                type="file"
                accept="image/png, image/jpeg, image/jpg"
                enctype="multipart/form-data"
                class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                required
                @change="onFileSelected"
              >
              <div v-if="form.image && form.image.length > 0" class="text-sm text-green-600">
                ✓ Image selected: {{ form.image[0].name }}
              </div>
            </div>

            <InputArray :items="form.organizationIds" label="Organization Ids" @update="form.organizationIds = $event" />

            <ButtonPrimary :disabled="isLoading" @click="editItem()">
              <template v-if="isLoading">
                <span class="inline-flex items-center">
                  <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle
                      class="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      stroke-width="4"
                    />
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                  </svg>
                  Processing...
                </span>
              </template>
              <template v-else-if="form._id">
                Update Style Item
              </template>
              <template v-else>
                Create Style Item
              </template>
            </ButtonPrimary>
          </div>
        </LoadingWrapper>
      </Modal>
      <!-- Enhanced Image Modal -->
      <div
        v-if="enlargedImage"
        class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
        @click="enlargedImage = null"
      >
        <div class="relative max-w-4xl max-h-full p-4">
          <button
            class="absolute top-2 right-2 bg-white rounded-full p-2 shadow-lg hover:bg-gray-50 z-10"
            @click="enlargedImage = null"
          >
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
          <img
            :src="enlargedImage"
            class="max-h-[90vh] max-w-full object-contain rounded-lg"
            @click.stop
          >
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      isSaving: false,
      // form: {
      //   title: null,
      //   description: null,
      //   prompt: 'RAW photo, a sks {trigger} in {clothing} standing in the park, (high detailed skin:1.2), 8k uhd, dslr, soft lighting, high quality, film grain, Fujifilm XT3',
      //   images: {
      //     maleImage: null,
      //     femaleImage: null
      //   },
      //   thumbnail: null,
      //   location: null
      // },
      styles: [],
      filter: {
        initial: 'all',
        title: ''
      },
      selectedStyle: {},
      showEditModal: false,
      form: {},
      enlargedImage: null,
      tableHead: [
        { label: 'Status', key: 'status', sortable: true },
        { label: 'Date', key: 'createdAt', sortable: true },
        { label: 'Images', key: 'images', sortable: false },
        { label: 'Title/Prompt', key: 'title', sortable: false },
        { label: 'Total/Favorite', key: 'usage', sortable: true },
        { label: 'Actions', key: 'actions', sortable: false }
      ],
      sortBy: null,
      sortDirection: 'asc',
      minFavorites: 0,
      inputMode: 'image', // 'manual' or 'image'
      pollingItems: new Set(), // Track items being polled
      pollIntervals: new Map() // Track polling intervals
    }
  },
  computed: {
    sortedStyles () {
      const { sortBy, sortDirection } = this
      return [...(this.styles || [])].filter((item) => {
        if (this?.filter?.title) {
          return (item?.title?.toLowerCase()?.includes(this.filter.title?.toLowerCase()) || item?.location?.toLowerCase()?.includes(this.filter.title?.toLowerCase()))
        }
        return true
      }).sort((a, b) => {
        if (sortBy === 'usage') {
          const rateA = a.usage?.conversionRate || 0
          const rateB = b.usage?.conversionRate || 0
          return sortDirection === 'desc' ? rateB - rateA : rateA - rateB
        }
        if (sortBy === 'createdAt') {
          return sortDirection === 'desc' ? new Date(b.createdAt) - new Date(a.createdAt) : new Date(a.createdAt) - new Date(b.createdAt)
        }
        if (sortBy === 'status') {
          return sortDirection === 'desc' ? b.status.localeCompare(a.status) : a.status.localeCompare(b.status)
        }
        return 0
      })
    }
  },
  async mounted () {
    await this.fetchStyles()
    // Add keyboard listener for image modal
    document.addEventListener('keydown', this.handleKeydown)
  },
  beforeDestroy () {
    // Clean up all polling intervals
    this.pollIntervals.forEach((interval) => {
      clearInterval(interval)
    })
    this.pollIntervals.clear()
    this.pollingItems.clear()
    // Remove keyboard listener
    document.removeEventListener('keydown', this.handleKeydown)
  },
  methods: {
    handleSort (event) {
      this.sortBy = event.column
      this.sortDirection = event.direction
    },
    openAddNewModal () {
      this.resetForm()
      this.showEditModal = true
    },
    resetForm () {
      this.form = {
        title: '',
        location: '',
        image: null,
        _id: null,
        organizationIds: []
      }
      this.inputMode = 'image' // Reset to image mode
      // Clear file input
      if (this.$refs.fileextra) {
        this.$refs.fileextra.value = ''
      }
    },
    closeModal () {
      this.showEditModal = false
      this.resetForm()
    },
    onInputModeChange () {
      // Clear form data when switching modes
      this.form.title = ''
      this.form.location = ''
      this.form.image = null

      // Clear file input
      if (this.$refs.fileextra) {
        this.$refs.fileextra.value = ''
      }
    },
    onFileSelected (event) {
      const files = event.target.files
      if (files && files.length > 0) {
        const file = files[0]

        // Validate file size (5MB limit)
        if (file.size > 5 * 1024 * 1024) {
          this.$toast.error('Image file must be smaller than 5MB')
          event.target.value = ''
          return
        }

        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg']
        if (!allowedTypes.includes(file.type)) {
          this.$toast.error('Only JPEG, PNG, and JPG image formats are allowed')
          event.target.value = ''
          return
        }

        this.form.image = files
      }
    },
    onFileSelectedMale (event) {
      this.form.images.maleImage = event.target.files
    },
    onFileSelectedFemale (event) {
      this.form.images.femaleImage = event.target.files
    },

    getGridStyle () {
      // Grid column in css
      return `display: grid; grid-template-columns: repeat(${this.gridAmount}, 1fr); grid-gap: 1rem;`
    },
    editItem () {
      this.isLoading = true
      const { _id, image, location, title, organizationIds } = this.form
      const formData = new FormData()

      // Enhanced validation
      if (this.inputMode === 'manual') {
        if (!title) {
          this.$toast.error('You must provide a style title')
          this.isLoading = false
          return
        }
        if (!location) {
          this.$toast.error('You must provide a style description')
          this.isLoading = false
          return
        }
      } else if (this.inputMode === 'image') {
        if (!image || image.length === 0) {
          this.$toast.error('You must upload an image')
          this.isLoading = false
          return
        }
      }

      if (image && image.length > 0) {
        formData.append('images', image[0])
      }
      if (location) {
        formData.append('location', location)
      }
      if (title) {
        formData.append('title', title)
      }
      if (organizationIds && organizationIds.length > 0) {
        formData.append('organizationIds', organizationIds)
      }

      const handleSuccess = (response) => {
        if (response && response.success) {
          if (!_id && response.data?.styleId) {
            // New item created - start polling for progress
            this.$toast.success('Style item created! Processing in progress...')
            this.showEditModal = false
            this.resetForm()
            this.startPollingProgress(response.data.styleId)
            // Add item to list immediately with pending status
            this.fetchStyles()
          } else {
            // Updated existing item - check if processing is needed
            const needsProcessing = (image && image.length > 0) || (location && location !== this.styles.find(item => item._id === _id)?.location)

            if (needsProcessing) {
              const message = 'Style item updated successfully. Processing will complete in about 1-2 minutes.'
              this.$toast.success(message)
            } else {
              this.$toast.success('Style item updated successfully!')
            }

            this.isLoading = false
            this.showEditModal = false
            this.resetForm()
            setTimeout(() => {
              this.fetchStyles()
            }, 1000)
          }
        } else {
          this.$toast.error('Something went wrong')
          this.isLoading = false
        }
      }

      const handleError = (error) => {
        this.isLoading = false
        this.handleError(error)
      }

      if (_id) {
        formData.append('_id', _id)
        this.$axios.$put('/admin/style', formData)
          .then(handleSuccess)
          .catch(handleError)
      } else {
        this.$axios.$post('/admin/style', formData)
          .then(handleSuccess)
          .catch(handleError)
      }
    },
    editStyle (style) {
      this.showEditModal = true
      this.inputMode = 'manual' // Set to manual mode for editing
      this.form = {
        _id: style._id,
        location: style.location || '',
        title: style.title || '',
        organizationIds: style.organizationIds || [],
        image: null // Reset image for editing
      }
      this.selectedStyle = style
    },
    async fetchStyles () {
      try {
        this.isLoading = true
        const { success, data, errorMessage } = await this.$axios.$get('/admin/style')
        if (!success) {
          throw new Error(errorMessage)
        }
        this.styles = data || []
      } catch (err) {
        console.error('Fetch styles error:', err)
        this.handleError(err)
        this.styles = []
      } finally {
        this.isLoading = false
      }
    },
    async savePrompt () {
      try {
        this.isSaving = true

        const { title, location, thumbnail, image } = this.form
        if (!title && !location && !image) {
          return this.$toast.warning('Please fill all fields')
        }

        // if (!prompt.includes('{trigger}') || !prompt.includes('{clothing}')) {
        //   return this.$toast.warning('Please make sure prompt contains {trigger} and {clothing}')
        // }

        const formData = new FormData()
        if (title) {
          formData.append('title', title)
        }
        if (location) {
          formData.append('location', location)
        }
        if (thumbnail) {
          formData.append('images', thumbnail[0])
        }

        if (image) {
          formData.append('image', image[0])
        }

        const { success, errorMessage } = await this.$axios.$post('/admin/style', formData)
        if (!success) {
          throw new Error(errorMessage)
        }

        this.$toast.open('Success')

        this.form = {
          title: null,
          location: null,
          images: {
            maleImage: null,
            femaleImage: null
          }
        }

        this.showEditModal = false
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isSaving = false
      }
    },
    async generateDescription () {
      try {
        const { title } = this.form
        if (!title) {
          return this.$toast.error('Please enter a title first')
        }

        const prompt = `Task: Write 30 words paragraph based on a provided title.
  Title: Business & Corporate
  Description: Business headshots typically used on LinkedIn profiles, press releases, websites, advertisements, business cards, and presentations. Ideal for executives, entrepreneurs, startups, or anyone else in a business environment.
  """
  Task: Write 30 words paragraph based on a provided title.
  Title: Professional Acting
  Description: Aspiring and professional acting professionals need great headshots to get called back for new roles. This type is perfect for theatrical, commercial, voice, or dramatic headshots.
  """
  Task: Write 30 words paragraph based on a provided title.
  Title: Artistic Creative
  Description: Stylized headshots for websites, social media profiles, press releases, and book covers. In these photos, the worst thing you can be is boring. Ideal for artists, designers, musicians, creative writers, and thought leaders.
  """
  Task: Write 30 words paragraph based on a provided title.
  Title: ${title}
  Description:`
        const { success, data, errorMessage } = await this.$axios.$post('/admin/gpt3', {
          prompt
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.form.description = data
      } catch (err) {
        console.log(err)
        this.$toast.error(err)
      }
    },
    sortData () {
      this.styles = this.styles.sort((a, b) => {
        return b?.uses - a?.uses
      })
    },
    deleteStyle (style) {
      this.$axios
        .$delete(`/admin/style/${style._id}`)
        .then(() => {
          this.$toast.success('Style deleted')
          this.fetchStyles()
        })
        .catch(() => {
          this.$toast.error('Error deleting style')
        })
    },
    calculateConversionRate (downloads, uses) {
      const rate = (downloads * 100) / (uses * 8)
      return Math.floor(rate)
    },
    async toggleStatus (id) {
      try {
        const { success, errorMessage } = await this.$axios.$post(`/admin/style/${id}/status`)
        if (!success) {
          throw new Error(errorMessage)
        }
        const item = this.styles.find(item => item._id === id)
        if (item.status === 'active') {
          item.status = 'offline'
        } else {
          item.status = 'active'
        }
      } catch (err) {
        this.handleError(err)
      }
    },
    showEnlargedImage (imageUrl) {
      this.enlargedImage = imageUrl
    },
    filterStyles (event) {
      this.filter.title = event
    },
    formatDate (dateString) {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
      })
    },
    formatDateRelative (dateString) {
      const date = new Date(dateString)
      const now = new Date()
      const diffInSeconds = Math.floor((now - date) / 1000)

      if (diffInSeconds < 60) {
        return 'Just now'
      } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60)
        return `${minutes} min${minutes === 1 ? '' : 's'} ago`
      } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600)
        return `${hours} hour${hours === 1 ? '' : 's'} ago`
      } else if (diffInSeconds < 2592000) {
        const days = Math.floor(diffInSeconds / 86400)
        return `${days} day${days === 1 ? '' : 's'} ago`
      } else {
        return this.formatDateShort(dateString)
      }
    },
    formatDateShort (dateString) {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
      })
    },
    formatDateFull (dateString) {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    handleError (err) {
      console.error('Error:', err)
      let errorMessage = 'Something went wrong'
      if (err.response?.data?.errorMessage) {
        errorMessage = err.response.data.errorMessage
      } else if (err.response?.data?.message) {
        errorMessage = err.response.data.message
      } else if (err.message) {
        errorMessage = err.message
      }
      this.$toast.error(errorMessage)
    },
    startPollingProgress (styleId) {
      if (this.pollingItems.has(styleId)) {
        return // Already polling this item
      }

      this.pollingItems.add(styleId)
      this.isLoading = false

      const pollInterval = setInterval(async () => {
        try {
          const { success, data } = await this.$axios.$get(`/admin/style/${styleId}/status`)

          if (success && data) {
            // Update the item in the list
            const itemIndex = this.styles.findIndex(item => item._id === styleId)
            if (itemIndex !== -1) {
              this.styles.splice(itemIndex, 1, {
                ...this.styles[itemIndex],
                processingStatus: data.processingStatus,
                status: data.status,
                title: data.title || this.styles[itemIndex].title,
                location: data.location || this.styles[itemIndex].location,
                images: data.images || this.styles[itemIndex].images
              })
            }

            // Check if processing is complete
            if (data.processingStatus === 'completed') {
              this.stopPolling(styleId)
              this.$toast.success('Style item processing completed!')
              this.fetchStyles() // Refresh the full list
            } else if (data.processingStatus === 'failed') {
              this.stopPolling(styleId)
              this.$toast.error('Style item processing failed and has been removed')
              this.fetchStyles() // Refresh to remove the failed item
            }
          }
        } catch (error) {
          console.error('Polling error:', error)
          // Continue polling on error, but stop after too many failures
        }
      }, 2000) // Poll every 2 seconds

      this.pollIntervals.set(styleId, pollInterval)

      // Auto-stop polling after 5 minutes to prevent infinite polling
      setTimeout(() => {
        if (this.pollingItems.has(styleId)) {
          this.stopPolling(styleId)
          this.$toast.warning('Stopped polling for style item progress after 5 minutes')
        }
      }, 300000) // 5 minutes
    },
    stopPolling (styleId) {
      if (this.pollIntervals.has(styleId)) {
        clearInterval(this.pollIntervals.get(styleId))
        this.pollIntervals.delete(styleId)
      }
      this.pollingItems.delete(styleId)
    },

    // Keyboard handler for image modal
    handleKeydown (event) {
      if (event.key === 'Escape' && this.enlargedImage) {
        this.enlargedImage = null
      }
    },

    // Generate action items for dropdown
    getActionItems (style) {
      return [
        {
          title: 'Edit',
          value: 'edit',
          icon: 'EditIcon',
          color: '#6B7280'
        },
        {
          title: 'Duplicate',
          value: 'duplicate',
          icon: 'DuplicateIcon',
          color: '#8B5CF6'
        },
        {
          title: style.status === 'active' ? 'Deactivate' : 'Activate',
          value: 'toggle-status',
          icon: style.status === 'active' ? 'EyeSlashIcon' : 'EyeIcon',
          color: style.status === 'active' ? '#F59E0B' : '#10B981'
        },
        {
          title: 'Delete',
          value: 'delete',
          icon: 'TrashIcon',
          color: '#EF4444'
        }
      ]
    },

    // Handle dropdown action selection
    handleAction (action, styleId) {
      switch (action) {
        case 'edit':
          this.editStyle(this.styles.find(s => s._id === styleId))
          break
        case 'duplicate':
          this.setupDuplicateForm(styleId)
          break
        case 'toggle-status':
          this.toggleStatus(styleId)
          break
        case 'delete':
          this.deleteStyleWithConfirmation(styleId)
          break
      }
    },

    setupDuplicateForm (id) {
      this.showEditModal = true
      const style = this.styles.find(style => style._id === id)
      this.form = {
        _id: null, // Clear ID to create new item
        location: style?.location || '',
        title: style?.title || '',
        organizationIds: style?.organizationIds || [],
        image: null
      }
      // Use manual mode since we have the data pre-populated
      this.inputMode = 'manual'
    },

    deleteStyleWithConfirmation (styleId) {
      if (!confirm('Are you sure you want to delete this style item? This action cannot be undone.')) {
        return
      }
      const style = this.styles.find(s => s._id === styleId)
      this.deleteStyle(style)
    }
  }
}
</script>

<style></style>
