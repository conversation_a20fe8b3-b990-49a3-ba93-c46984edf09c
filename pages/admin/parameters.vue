<template>
  <section class="">
    <div class="p-4 max-w-6xl mx-auto flex flex-col space-y-4">
      <Card>
        <div class="flex flex-col space-y-2">
          <h2 class="text-xl font-bold">
            Available parameters
          </h2>
          <p class="mb-4 text-gray-700">
            These are the type of clothing people can choose to generate headshots in.
          </p>
        </div>
      </Card>
      <LoadingWrapper :is-loading="isLoading">
        <div class="flex flex-col space-y-4">
          <Card v-for="type in sortedItemsPerType" :key="type.type">
            <div class="flex flex-col space-y-2  w-full">
              <div class="flex items-center space-x-2 w-full justify-between">
                <h2 class="text-lg font-bold capitalize">
                  {{ type.type }}
                </h2>
                <Input v-model="minFavorites" type="number" name="minFavorites" placeholder="Minimum favorites" label="Minimum favorites" />
              </div>
              <TableSortable :head="tableHead" @sort="handleSort">
                <TableRow v-for="item in sortedItems(type.items).filter(item => item.usage?.favorite >= minFavorites)" :key="item._id">
                  <TableItem>
                    <span class="font-mono text-xs">{{ item.value }}</span>
                  </TableItem>
                  <TableItem>
                    {{ item.usage?.total || 0 }}
                  </TableItem>
                  <TableItem>
                    {{ item.usage?.downloaded || 0 }}
                  </TableItem>
                  <TableItem>
                    {{ item.usage?.favorite || 0 }}
                  </TableItem>
                  <TableItem>
                    {{ (item.usage?.conversionRate * 100).toFixed(2) || 0 }}%
                  </TableItem>
                </TableRow>
              </TableSortable>
            </div>
          </Card>
        </div>
      </LoadingWrapper>
    </div>
  </section>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      items: [],
      item: {
        type: null, value: null
      },
      isLoading: true,
      sortBy: null,
      sortDirection: 'asc',
      tableHead: [
        { label: 'Value', key: 'value', sortable: true },
        { label: 'Total', key: 'total', sortable: true },
        { label: 'Downloads', key: 'downloads', sortable: true },
        { label: 'Favorites', key: 'favorites', sortable: true },
        { label: 'Conversion Rate', key: 'conversionRate', sortable: true }
      ],
      minFavorites: 0
    }
  },
  computed: {
    sortedItemsPerType () {
      const types = [...new Set(this.items.map(item => item.type))]
      return types.map(type => ({
        type,
        items: this.items.filter(item => item.type === type).sort((a, b) => b.usage.total - a.usage.total)
      }))
    }
  },
  mounted () {
    this.fetch()
  },
  methods: {
    async fetch () {
      try {
        this.isLoading = true
        const { success, data, errorMessage } = await this.$axios.$get('/admin/parameters')
        if (!success) {
          throw new Error(errorMessage)
        }
        this.items = data
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = false
      }
    },
    async addItem () {
      try {
        this.isLoading = true
        const { success, data, errorMessage } = await this.$axios.$post('/admin/parameters', { ...this.item })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.item = { type: null, value: null }
        this.items.push(data)
        // this.fetch()
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = false
      }
    },
    async deleteItem (id) {
      try {
        const { success, errorMessage } = await this.$axios.$delete(`/admin/parameters/${id}`)
        if (!success) {
          throw new Error(errorMessage)
        }
        this.items = this.items.filter(item => item._id !== id)
      } catch (err) {
        this.handleError(err)
      }
    },
    async toggleStatus (id) {
      try {
        const { success, errorMessage } = await this.$axios.$post(`/admin/parameters/${id}/status`)
        if (!success) {
          throw new Error(errorMessage)
        }
        const item = this.items.find(item => item._id === id)
        item.active = !item.active
      } catch (err) {
        this.handleError(err)
      }
    },
    handleSort (event) {
      this.sortBy = event.column
      this.sortDirection = event.direction
    },
    sortedItems (items) {
      const { sortBy, sortDirection } = this
      return [...items].sort((a, b) => {
        if (sortBy === 'value') {
          return sortDirection === 'desc'
            ? b.value.localeCompare(a.value)
            : a.value.localeCompare(b.value)
        }
        if (sortBy === 'total') {
          return sortDirection === 'desc'
            ? (b.usage?.total || 0) - (a.usage?.total || 0)
            : (a.usage?.total || 0) - (b.usage?.total || 0)
        }
        if (sortBy === 'downloads') {
          return sortDirection === 'desc'
            ? (b.usage?.downloaded || 0) - (a.usage?.downloaded || 0)
            : (a.usage?.downloaded || 0) - (b.usage?.downloaded || 0)
        }
        if (sortBy === 'favorites') {
          return sortDirection === 'desc'
            ? (b.usage?.favorite || 0) - (a.usage?.favorite || 0)
            : (a.usage?.favorite || 0) - (b.usage?.favorite || 0)
        }
        if (sortBy === 'conversionRate') {
          return sortDirection === 'desc'
            ? (b.usage?.conversionRate || 0) - (a.usage?.conversionRate || 0)
            : (a.usage?.conversionRate || 0) - (b.usage?.conversionRate || 0)
        }
        return 0
      })
    }
  }
}
</script>

<style></style>
