<template>
  <div class="w-full p-4">
    <div class="grid grid-cols-4 gap-4">
      <Card>
        <div class="p-4 space-y-2">
          <Input v-model="link" placeholder="URL" label="URL" />
          <InputSelect v-model="type" :options="['twitter']" label="Type" />
          <ButtonPrimary size="sm" @click="addTestimonial">
            Add testimonial
          </ButtonPrimary>
        </div>
      </Card>
      <template v-for="item in reviews">
        <ReviewTweet :key="item._id" :item="item" />
      </template>
    </div>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      link: '',
      type: 'twitter',
      reviews: []
    }
  },
  async fetch () {
    const tweets = await this.$axios.$get('/reviews/wall-of-love/twitter')
    this.reviews = tweets
  },
  methods: {
    addTestimonial () {
      const { link, type } = this
      if (!link || !type) {
        return this.$toast.error('Please fill in all fields')
      }
      this.$axios.$post('/admin/review/' + type, { link })
        .then((response) => {
          if (response.success) {
            this.$toast.success('Testimonial added')
            this.$fetch()
          } else {
            this.$toast.warning(response.err)
          }
          this.link = ''
        })
        .catch((err) => {
          this.$toast.error(err.response.data.message)
        })
    }

  }

}
</script>

<style>

</style>
