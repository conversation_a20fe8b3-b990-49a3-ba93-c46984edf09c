<template>
  <section class="flex min-h-screen items-start justify-center px-0 py-4 text-black md:items-start p-4">
    <div class="p-8">
      <AppTitle title="Emotions" sub-title="Emotions people can choose from">
        <ButtonPrimary size="sm" @click="openAddNewModal">
          Add new
        </ButtonPrimary>
      </AppTitle>
      <LoadingWrapper :is-loading="isLoading">
        <div class="grid grid-cols-5 gap-4 mt-4">
          <template v-for="item in emotionItems">
            <Card :key="item._id" class="relative h-full" :style="`${(!item.active) ? 'opacity:0.5': ''}`">
              <div class="w-2 h-2 rounded-full absolute top-1 right-1" :style="`background-color: ${item.active ? 'green' : 'red'}`" />
              <div class="flex flex-col p-4 space-y-4 justify-between h-full">
                <div class="flex flex-col space-y-4 relative">
                  <div v-if="item.images?.male || item.images?.female" class="grid grid-cols-2">
                    <img v-if="item.images?.male" :src="item.images?.male" class="w-full">
                    <img v-if="item.images?.female" :src="item.images?.female" class="w-full">
                  </div>
                  <h3 class="font-bold text-md">
                    {{ item.type }}
                  </h3>
                  <p class="text-gray-600 text-sm">
                    {{ item.prompt }}
                  </p>
                  <p v-if="item?.gender && item.gender.length > 0" class="text-gray-600 text-sm">
                    <strong>Gender:</strong> {{ item.gender.join(', ') }}
                  </p>
                </div>
                <div class="flex items-center justify-start space-x-2">
                  <ButtonDelete size="sm" @click="deleteItem(item._id)">
                    Delete
                  </ButtonDelete>
                  <ButtonWhite size="sm" @click="setupEditForm(item._id)">
                    Edit
                  </ButtonWhite>
                  <ButtonWhite size="sm" @click="toggleStatus(item._id)">
                    {{ item.active ? 'Deactivate' : 'Activate' }}
                  </ButtonWhite>
                </div>
              </div>
            </Card>
          </template>
        </div>
      </LoadingWrapper>
    </div>
    <Modal v-if="showEditModal" @close="showEditModal = false; editId = null">
      <LoadingWrapper :is-loading="isLoading">
        <div class="flex flex-col p-4 space-y-4 w-[500px]">
          <InputSelect v-model="form.gender" label="Gender" :options="genderOptions" />
          <input
            id="fileextra"
            ref="fileextra"
            name="fileextra"
            type="file"
            accept="image/png, image/jpeg, image/jpg"
            enctype="multipart/form-data"
            @change="onFileSelected"
          >
          <ButtonPrimary @click="editItem()">
            Edit
          </ButtonPrimary>
        </div>
      </LoadingWrapper>
    </Modal>
  </section>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      emotionItems: [],
      item: null,
      isLoading: true,
      showEditModal: false,
      editId: null,
      form: {},
      genderOptions: [
        { title: 'Male', value: ['male'] },
        { title: 'Female', value: ['female'] },
        { title: 'All', value: ['male', 'female'] }
      ]
    }
  },

  mounted () {
    this.fetch()
  },
  methods: {
    openAddNewModal () {
      this.form = {
        gender: [],
        image: null,
        _id: null
      }
      this.showEditModal = true
    },
    onFileSelected (event) {
      this.form.image = event.target.files
    },
    onFileSelectedMale (event) {
      this.form.images.male = event.target.files
    },
    onFileSelectedFemale (event) {
      this.form.images.female = event.target.files
    },
    editItem () {
      this.isLoading = true
      const { _id, gender, image } = this.form
      const formData = new FormData()

      if (gender) {
        formData.append('gender', gender)
      }
      if (image) {
        console.log(image)
        formData.append('images', image[0] || null)
      }

      const handleSuccess = (response) => {
        if (response && response.success) {
          this.$toast.success('Emotion updated. Wait atleast 1 minute for it to finish processing.')
          this.isLoading = false
          this.showEditModal = false
        } else {
          this.$toast.error('Something went wrong')
          this.isLoading = false
        }
      }

      if (_id) {
        formData.append('_id', _id)
        this.$axios.$put('/admin/emotions', formData)
          .then((response) => {
            if (response && response.success) {
              handleSuccess(response)
            }
          })
      } else {
        this.$axios.$post('/admin/emotions', formData)
          .then((response) => {
            if (response && response.success) {
              handleSuccess(response)
            }
          })
      }
    },
    setupEditForm (id) {
      this.editId = id
      this.showEditModal = true
      const item = this.emotionItems.find(item => item._id === id)
      this.form = {
        type: item?.type || null,
        prompt: item?.prompt || null,
        _id: item._id,
        gender: item?.gender || [],
        images: item?.images || {
          female: null,
          male: null
        }
      }
    },
    async fetch () {
      try {
        this.isLoading = true
        const { success, data, errorMessage } = await this.$axios.$get('/admin/emotions')
        if (!success) {
          throw new Error(errorMessage)
        }
        this.emotionItems = data
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = false
      }
    },
    async addItem () {
      try {
        this.isLoading = true
        const { success, data, errorMessage } = await this.$axios.$post('/admin/emotions', { type: this.item })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.emotionItems.push(data)
        this.item = null
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = false
      }
    },
    async deleteItem (id) {
      try {
        const { success, errorMessage } = await this.$axios.$delete(`/admin/emotions/${id}`)
        if (!success) {
          throw new Error(errorMessage)
        }
        this.emotionItems = this.emotionItems.filter(item => item._id !== id)
      } catch (err) {
        this.handleError(err)
      }
    },
    async toggleStatus (id) {
      try {
        const { success, errorMessage } = await this.$axios.$post(`/admin/emotions/${id}/status`)
        if (!success) {
          throw new Error(errorMessage)
        }
        const item = this.emotionItems.find(item => item._id === id)
        item.active = !item.active
      } catch (err) {
        this.handleError(err)
      }
    }
  }
}
</script>

<style></style>
