<template>
  <div>
    <LoadingWrapper :is-loading="isLoading">
      <div class="w-full flex space-x-4 p-2 relative">
        <div class="w-[200px] xl:w-[400px] sticky">
          <Card class="overflow-x-scroll">
            <ul class="flex flex-col space-y-2 p-4">
              <li> <AdminTitleValue title="Name" :value="model.title" /> </li>
              <li>
                <template v-if="['support', 'admin'].includes(adminRole)">
                  <nuxt-link :to="`/admin/user?search=${model.uid}&searchBy=uid`">
                    <AdminTitleValue title="UID" :value="model.uid" />
                  </nuxt-link>
                </template>
                <template v-else>
                  <AdminTitleValue title="UID" :value="model.uid" />
                </template>
              </li>
              <li> <AdminTitleValue title="Date" :value="formatDate(model.createdAt)" /></li>
              <li> <AdminTitleValue title="Status" :value="model.status" /></li>
              <li> <AdminTitleValue title="Gender" :value="model.trigger" /></li>
              <li> <AdminTitleValue title="Package" :value="model.package" /></li>
              <li> <AdminTitleValue title="Version" :value="model?.modelVersion || ''" /></li>
              <li> <AdminTitleValue title="Test Version" :value="model?.testVersion || ''" /></li>
              <li> <AdminTitleValue title="Eye color" :value="model.eyeColor" /></li>
              <li> <AdminTitleValue title="Total photos" :value="model?.images?.length" /></li>
              <li v-if="model?.meta?.fluxLoraTrainingSteps">
                <AdminTitleValue title="Training steps" :value="model?.meta?.fluxLoraTrainingSteps" />
              </li>
              <li v-if="model?.models?.fluxlora">
                <AdminTitleValue title="Flux LoRA" :value="model?.models?.fluxlora" />
              </li>
              <template v-if="model?.appearance">
                <li v-for="(value, key) in model.appearance" :key="key">
                  <AdminAppearanceValue :title="key" :value="value" :model-id="model?._id" />
                </li>
              </template>
              <li v-if="model?.age">
                <AdminTitleValue title="Age" :value="model.age" />
              </li>
              <li> <AdminTitleValue title="Styles" :value="selectedStyles" /></li>
              <li> <AdminTitleValue title="Photos" :value="`${model?.imageCount}`" /></li>
              <li> <AdminTitleValue title="Downloads" :value="model?.downloads" /></li>
              <template v-if="['support', 'admin', 'marketer', 'sales'].includes(adminRole)">
                <li>
                  <ButtonWhite v-if="model.status !== 'active'" class="bg-green-500" size="sm" @click="activateModel">
                    Activate
                  </ButtonWhite>
                </li>
                <li>
                  <ButtonWhite size="sm" @click="showRerunModel = true">
                    Rerun
                  </ButtonWhite>
                </li>
                <li v-if="model.status === 'prechecking'">
                  <ButtonWhite size="sm" @click="setStatusToWaiting">
                    Status to waiting
                  </ButtonWhite>
                </li>
                <li>
                  <ButtonWhite size="sm" @click="restartGeneration">
                    Restart generations
                  </ButtonWhite>
                </li>
                <li>
                  <form class="flex" @submit.prevent="addExtraRegenerations">
                    <input v-model="extraRegenerations" type="number" min="0" class="border border-gray-300">
                    <ButtonWhite size="sm" type="submit">
                      Add extra regenerations
                    </ButtonWhite>
                  </form>
                </li>
                <li>
                  <form class="flex" @submit.prevent="setFreeStyles">
                    <input v-model="freeStyles" type="number" min="0" class="border border-gray-300">
                    <ButtonWhite size="sm" type="submit">
                      Set free styles
                    </ButtonWhite>
                  </form>
                </li>
                <li>
                  <ButtonDelete size="sm" @click="deleteResults">
                    Delete
                  </ButtonDelete>
                </li>
                <li>
                  <ButtonDelete size="sm" @click="wipeToWaiting">
                    Wipe to waiting
                  </ButtonDelete>
                </li>
                <li v-if="!model.inviteForReview">
                  <ButtonDelete size="sm" @click="inviteForReview">
                    Invite to Trustpilot
                  </ButtonDelete>
                </li>
              </template>
            </ul>
            <hr>
            <div v-if="model.feedback" class="p-4 space-y-2">
              <div class="flex items-center justify-start space-x-1">
                <h3 class="font-bold text-lg">
                  Feedback
                </h3>
                <span v-if="model.feedback?.isFavorite">❤️</span>
                <span v-if="model.feedback?.allowPhotoUse">✅</span>
                <span v-if="model.feedback?.hasSavedPhoto">📸</span>
                <span v-if="model.feedback?.allowPhotosForMarketing">🎺</span>
                <span v-if="model.feedback?.allowedSelfies">👱‍♀️</span>
              </div>
              <ul class="space-y-2 text-xs text-gray-700">
                <li v-for="feedback in model.feedback.answers" :key="feedback.question" class="flex flex-col space-y-1">
                  <strong>{{ feedback.question }}</strong>
                  <span>{{ feedback.answer }}</span>
                </li>
              </ul>
            </div>
          </Card>
        </div>
        <div class="w-full space-y-4">
          <div class="grid grid-cols-10 gap-1 bg-gray-100 p-2 shadow-inner">
            <div v-for="image in model.trainingImages" :key="image" class="group overflow-hidden relative">
              <ImageDns :src="image" />
              <div v-if="model.status === 'prechecking'" class="w-full h-full hidden items-center justify-center absolute top-0 left-0 group-hover:flex z-20">
                <ButtonDelete size="xs" class="hidden group-hover:flex" @click="deleteTrainingPhoto(image)">
                  Delete
                </ButtonDelete>
              </div>
            </div>
          </div>
          <div class="w-full">
            <h3 class="font-bold text-brand-500 text-2xl">
              Favorite photos
            </h3>
          </div>
          <PhotoGrid
            :images="favoritePhotos"
            :grid-cols="'grid-cols-2 md:grid-cols-3 2xl:grid-cols-4'"
            :show-resemblance-score="true"
            @download="downloadFull"
            @upscale="upscalePhoto"
            @delete="deletePhoto"
            @approve-review="toggleApproveReviewPopup"
            @remove-review="removeReviewApproval"
            @select="selectedImage = $event"
            @set-as-nsfw="setAsNsfw"
            @set-as-sfw="setAsNsfw"
          />
          <div class="w-full">
            <h3 class="font-bold text-red-900 text-2xl">
              Didn't make the cut
            </h3>
          </div>
          <PhotoGrid
            :images="dislikedPhotos"
            :grid-cols="'grid-cols-2 md:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5'"
            :show-resemblance-score="false"
            @download="downloadFull"
            @upscale="upscalePhoto"
            @delete="deletePhoto"
            @approve-review="toggleApproveReviewPopup"
            @remove-review="removeReviewApproval"
            @select="selectedImage = $event;"
            @set-as-nsfw="setAsNsfw"
            @set-as-sfw="setAsNsfw"
          />
        </div>
      </div>
    </LoadingWrapper>
    <Popup v-if="selectedImage" size="7xl" @closeModal="selectedImage = null">
      <div class="grid grid-cols-5">
        <ImageDns :src="selectedImage.upscaled || selectedImage.image" class="col-span-3" />
        <ul class="space-y-1 overflow-x-scroll col-span-2 px-4">
          <li class="text-sm">
            Photo ID: {{ selectedImage._id }}
          </li>
          <li v-for="(value, key) in selectedImage.meta" :key="key" class="flex flex-col text-xs">
            <template v-if="key !== 'pose'">
              <div class="font-bold">
                {{ key }}
              </div>
              <div class="">
                {{ value }}
              </div>
            </template>
          </li>
        </ul>
      </div>
    </Popup>
    <Popup v-if="showRerunModel" size="md" @closeModal="showRerunModel = false">
      <div class="space-y-2">
        <InputSelect v-model="style" :options="styles" label="Style" />
        <InputSelect v-model="clothing" :options="clothingItems" label="Clothing" />
        <LoadingWrapper :is-loading="isLoading">
          <ButtonPrimary @click="rerun">
            Rerun
          </ButtonPrimary>
        </LoadingWrapper>
      </div>
    </Popup>
    <Popup v-if="showApproveReviewPopup" size="2xl" @closeModal="showApproveReviewPopup = false; review = { photoId: null, trainingImage: null, quote: null, name: null }">
      <div class="space-y-2">
        <div class="grid grid-cols-2 gap-4">
          <div v-if="selectedReviewImage">
            <ImageDns :src="selectedReviewImage.upscaled || selectedReviewImage.image" />
            <div class="grid grid-cols-5 gap-1">
              <img
                v-for="image in model.trainingImages"
                :key="image"
                :src="image"
                class="cursor-pointer rounded-md"
                :class="{ 'border-2 border-green-500': review.trainingImage === image }"
                @click="review.trainingImage = image"
              >
            </div>
          </div>
          <div class="flex flex-col gap-2">
            <Input v-model="review.name" label="Name" />
            <InputTextArea v-model="review.quote" label="Quote" />
            <ButtonPrimary @click="approveForReviewPage">
              Approve
            </ButtonPrimary>
          </div>
        </div>
      </div>
    </Popup>
  </div>
</template>

<script>
export default {
  components: {
    PhotoGrid: () => import('@/components/admin/PhotoGrid')
  },
  layout: 'admin',
  data () {
    return {
      model: {},
      style: null,
      clothing: null,
      showRerunModel: false,
      isLoading: true,
      selectedImage: null,
      extraRegenerations: 0,
      freeStyles: 0,
      review: {
        photoId: null,
        trainingImage: null,
        quote: null,
        name: null
      },
      showApproveReviewPopup: false
    }
  },
  async fetch () {
    const response = await this.$axios.$get('/admin/model/single/' + this.$route.params.id)
    this.model = response.data

    const { success, errorMessage, data } = await this.$axios.$get('/clothing')
    if (!success) {
      throw new Error(errorMessage)
    }
    if (data && data.length > 0) {
      this.$store.commit('SET_CLOTHING', data)
    }

    this.freeStyles = this.model.freeStyles || 0

    await this.fetchStyles()
    this.isLoading = false
  },
  computed: {
    selectedStyles () {
      const { style } = this.model
      if (!style || style.length === 0) { return '' }
      const styles = style.map((item) => {
        return item.style?.title || ''
      })
      const clothings = style.map((item) => {
        return item.clothing?.type || ''
      })
      return styles.map((item, index) => {
        return item + ' - ' + clothings[index]
      }).join(', ')
    },
    clothingItems () {
      const clothing = this.$store.state.clothing.map((item) => {
        return { title: item.type, value: item._id }
      })
      return clothing.sort((a, b) => {
        return a.title.localeCompare(b.title)
      })
    },
    styles () {
      const styles = this.$store.state.styles.map((style) => {
        return { title: style.title, value: style._id }
      })
      return styles.sort((a, b) => {
        return a.title.localeCompare(b.title)
      })
    },
    favoritePhotos () {
      return this.model?.images?.filter(photo => ['favorite', 'loved'].includes(photo.likedStatus)) || []
    },
    dislikedPhotos () {
      const photos = this.model?.images?.filter(photo => !['favorite', 'loved'].includes(photo.likedStatus)) || []
      return photos.sort((a, b) => {
        const batchA = parseInt(a.meta?.batchNumber, 10) || 0
        const batchB = parseInt(b.meta?.batchNumber, 10) || 0
        return batchB - batchA // Sort in descending order (newest first)
      })
    },
    selectedReviewImage () {
      if (!this.review.photoId) { return null }
      return this.model?.images?.find(photo => photo._id === this.review.photoId)
    }
  },
  methods: {
    toggleApproveReviewPopup (id) {
      this.review = {
        photoId: id,
        trainingImage: null,
        quote: null,
        name: null
      }

      if (this.model.title) {
        this.review.name = this.model.title
      }
      this.showApproveReviewPopup = true
    },
    inviteForReview () {
      this.$axios.$post('/admin/reviews/invite/' + this.$route.params.id)
        .then((response) => {
          if (response.success) {
            this.$toast.success('Invited successfully')
            this.model.inviteForReview = true
          } else {
            this.$toast.error('Something went wrong')
          }
        })
        .catch((err) => {
          this.$toast.error('Something went wrong', err)
        })
    },
    setStatusToWaiting () {
      this.$axios.$post('/admin/model/status/waiting/' + this.model._id)
        .then((response) => {
          if (response.success) {
            this.$toast.success('Status changed to waiting')
            this.model.status = 'waiting'
          } else {
            this.$toast.error('Something went wrong')
          }
        })
        .catch((err) => {
          this.$toast.error('Something went wrong', err)
        })
    },
    deleteTrainingPhoto (image) {
      this.$axios.$post('/admin/model/trainingimage/delete', { image, modelId: this.$route.params.id })
        .then((response) => {
          if (response.success) {
            this.$toast.success('Deleted successfully')
            this.model.trainingImages = this.model.trainingImages.filter((item) => {
              return item !== image
            })
          }
        })
    },
    activateModel () {
      this.$axios.$post('/admin/model/activate/' + this.model._id)
        .then((response) => {
          if (response.success) {
            this.$toast.success('Activated successfully')
            this.model.status = 'active'
          } else {
            this.$toast.error('Something went wrong')
          }
        })
        .catch((err) => {
          this.$toast.error('Something went wrong', err)
        })
    },
    downloadFull (url) {
      if (url.includes('https://storage.googleapis.com/')) {
        this.downloadSingleFromUrl(url)
        return
      }

      this.$axios.$post('/admin/model/image/download', { url })
        .then((response) => {
          if (response.success) {
            // Download image
            this.downloadSingleFromUrl(response.signedUrl)
          }
        })
    },
    downloadSingleFromUrl (url) {
      this.$toast.success('Your image will download shortly. Make sure to accept the download prompt.')
      fetch(url)
        .then(response => response.blob())
        .then((blob) => {
          const url = window.URL.createObjectURL(new Blob([blob]))
          const link = document.createElement('a')
          link.href = url
          const fileName = `${this.$route.params.id}-HeadshotPro.jpg`
          link.setAttribute('download', fileName)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        })
    },
    deleteResults () {
      this.$axios.$delete('/admin/model/' + this.model._id)
        .then((response) => {
          if (response.success) {
            this.$toast.success('Deleted successfully')
            this.$router.push('/admin/user')
          } else {
            this.$toast.error('Something went wrong')
          }
        })
        .catch((err) => {
          console.log(err)
          this.$toast.error('Something went wrong')
        })
    },
    async fetchStyles () {
      const { success, data, errorMessage } = await this.$axios.$get('/styles/')
      if (!success) {
        throw new Error(errorMessage)
      }
      this.$store.commit('SET_STYLES', data.styles)
    },
    rerun () {
      if (!this.style || !this.clothing) {
        this.$toast.error('Please select style and clothing')
        return
      }
      this.isLoading = true
      this.$axios.$post('/admin/model/rerun/' + this.model._id, { styleId: this.style, clothingId: this.clothing })
        .then((response) => {
          if (response.success) {
            this.$toast.success('Rerun successfully')
            this.showRerunModel = false
          } else {
            this.$toast.error('Something went wrong')
          }
          this.isLoading = false
        })
        .catch((err) => {
          this.$toast.error('Something went wrong', err)
          this.isLoading = false
        })
    },

    upscalePhoto (imageId) {
      this.isLoading = true
      this.$axios.$post(`/admin/model/upscale/${this.$route.params.id}/${imageId}`)
        .then((response) => {
          if (response.success) {
            this.$toast.success('Upscaled successfully')
            this.showRerunModel = false
          } else {
            this.$toast.error('Something went wrong')
          }
          this.isLoading = false
        })
        .catch((err) => {
          this.$toast.error('Something went wrong', err)
          this.isLoading = false
        })
    },

    restartGeneration () {
      this.isLoading = true
      this.$axios.$post('/admin/model/restart/' + this.model._id)
        .then((response) => {
          if (response.success) {
            this.$toast.success('Restart successfully')
            this.showRerunModel = false
          } else {
            this.$toast.error(response.message || 'Something went wrong')
          }
          this.isLoading = false
        })
        .catch((err) => {
          this.$toast.error(err?.response?.data?.message || 'Something went wrong')
          this.isLoading = false
        })
    },

    addExtraRegenerations () {
      this.isLoading = true
      this.$axios.post('/admin/model/add-regenerations/' + this.model._id, {
        extraRegenerations: this.extraRegenerations
      })
        .then((response) => {
          if (response.data.success) {
            this.$toast.success('Successfully added regenerations')
            this.extraRegenerations = 0
          } else {
            this.$toast.error('Something went wrong')
          }

          this.isLoading = false
        })
        .catch((err) => {
          this.$toast.error('Something went wrong', err)
          this.isLoading = false
        })
    },
    setFreeStyles () {
      this.isLoading = true
      this.$axios.post('/admin/model/set-free-styles/' + this.model._id, {
        freeStyles: this.freeStyles
      })
        .then((response) => {
          if (response.data.success) {
            this.$toast.success('Successfully set free styles')
            this.freeStyles = response.data.freeStyles
          } else {
            this.$toast.error('Something went wrong')
          }

          this.isLoading = false
        })
        .catch((err) => {
          this.$toast.error('Something went wrong', err)
          this.isLoading = false
        })
    },
    deletePhoto (id) {
      this.$axios.$post(`/admin/model/result/delete/single/${id}/${this.$route.params.id}`)
        .then((response) => {
          const { success } = response
          if (success) {
            this.$toast.success('Deleted successfully')
            // Filter out
            this.model.images = this.model.images.filter((item) => {
              return item._id !== id
            })
          }
        })
    },
    wipeToWaiting () {
      this.$axios.$post('/admin/model/wipe/' + this.$route.params.id)
        .then((response) => {
          const { success } = response
          if (success) {
            this.$toast.success('Wiped successfully')
            // Filter out
            this.$router.push('/admin/model')
          }
        })
    },
    approveForReviewPage (id) {
      this.$loading.show({
        message: 'Approving photo for review page, could take up to 10 seconds.'
      })
      this.$axios.$post('/admin/model/result/approve-review/' + this.$route.params.id, {
        photoId: this.review.photoId,
        quote: this.review.quote,
        name: this.review.name,
        trainingImage: this.review.trainingImage
      })
        .then((response) => {
          if (response.success) {
            this.$toast.success(response?.message)
            // Filter out
            this.model.images = this.model.images.map((item) => {
              if (item._id === id) {
                item.approvedForReviewPage = true
              }
              return item
            })
          } else {
            this.$toast.error(response?.message || 'Something went wrong')
          }
        }).catch((err) => {
          this.$toast.error(err?.response?.data?.message || 'Something went wrong')
        }).finally(() => {
          this.$loading.hide()
        })
    },
    removeReviewApproval (id) {
      this.$axios.$post('/admin/model/result/remove-review/' + this.$route.params.id, { photoId: id })
        .then((response) => {
          if (response.success) {
            this.$toast.success(response?.message)
            // Filter out
            this.model.images = this.model.images.map((item) => {
              if (item._id === id) {
                item.approvedForReviewPage = false
              }
              return item
            })
          } else {
            this.$toast.error(response?.message || 'Something went wrong')
          }
        }).catch((err) => {
          this.$toast.error(err?.response?.data?.message || 'Something went wrong')
        })
    },
    setAsNsfw ({ id, nsfw }) {
      this.$axios.$post('/admin/model/result/set-as-nsfw/' + this.$route.params.id, { photoId: id, nsfw })
        .then((response) => {
          if (response.success) {
            this.$toast.success(response.message || 'Done!')
            this.model.images = this.model.images.map((item) => {
              if (item._id === id) {
                item.nsfw = nsfw
              }
              return item
            })
          }
        })
    }
  }
}
</script>

<style>

</style>
