<template>
  <div class="p-8">
    <div class="flex w-full justify-between space-x-2 pb-4">
      <Input v-model="search" placeholder="Search" class="w-full" />
      <InputSelect v-model="searchBy" :options="['uid', 'title', 'email', 'organization']" class="w-1/4" />
      <InputSelect v-model="searchStatus" :options="['all', 'in-progress', 'active', 'refunded', 'pending-refund']" class="w-1/4" />
      <ButtonPrimary size="sm" @click="fetchData">
        Search
      </ButtonPrimary>
    </div>
    <Card class="mx-auto w-full">
      <Table v-if="!isLoading" :head="['Date', 'Name', 'Status', 'Package', 'Restarts', 'Version', 'Downloads', 'Photos', 'Gender','Action']">
        <template v-for="item in models">
          <!-- <template v-if="inSearch(item)"> -->
          <TableRow :key="item._id">
            <TableItem><span class="text-xs">{{ formatLongDate(item.createdAt) }}</span></TableItem>
            <TableItem>{{ item.title }}</TableItem>
            <TableItem>
              <span :style="`color:${(item.status === 'active') ? 'green' : (item.status === 'refunded') ? 'red' : 'orange'}`">
                {{ item.status }}
              </span>
            </TableItem>
            <TableItem>{{ item.package }}</TableItem>
            <TableItem>{{ item.restarts }}/{{ item.trainingRestarts }}</TableItem>
            <TableItem>{{ item.testVersion }}</TableItem>
            <TableItem>
              <span :style="`color:${(item.downloads >= 10 ) ? 'green' : (item.downloads >= 5) ? 'orange' : 'red'}`">
                {{ item.downloads }}
              </span>
            </TableItem>
            <TableItem>
              {{ (item?.favoriteImagesCount || 0) }} / {{ item.totalPhotos }}
            </TableItem>
            <TableItem>
              {{ item.trigger }}
            </TableItem>
            <TableItem>
              <nuxt-link :to="`/admin/model/${item._id}`">
                <ButtonWhite size="sm">
                  View
                </ButtonWhite>
              </nuxt-link>
            </TableItem>
          </TableRow>
        </template>
      </Table>
      <template v-if="isLoading">
        <LoadingSpinner />
      </template>
      <template v-else>
        <PaginationInfinity @fetch="fetchData($event)" />
      </template>
    </Card>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      models: [],
      isLoading: true,
      searchBy: 'email',
      searchStatus: 'all',
      search: null,
      status: null
    }
  },

  async created () {
    if (this.$route.query.search) {
      this.search = this.$route.query.search
    }
    if (this.$route.query.searchBy) {
      this.searchBy = this.$route.query.searchBy
    }
    if (this.$route.query.searchStatus) {
      this.searchStatus = this.$route.query.searchStatus
    }
    if (this.$route.query.status) {
      this.status = this.$route.query.status
    }
    await this.fetchData()
    this.isLoading = false
  },
  methods: {
    async fetchData (page = 0) {
      try {
        this.isLoading = true
        const models = await this.$axios.$get(`/admin/model/all?page=${page}`, {
          params: {
            ...(this.search && this.search.length > 0) ? { search: this.search.trim() } : {},
            ...(this.search && this.search.length > 0) ? { searchBy: this.searchBy.trim() } : {},
            ...(this.searchStatus && this.searchStatus.length > 0) ? { status: this.searchStatus.trim() } : {}
          }
        })
        if ((this.search && this.search.length > 0) || (this.searchStatus !== 'all')) {
          this.models = models
        } else {
          this.models = [...this.models, ...models]
        }
        this.isLoading = false
      } catch (e) {
        this.isLoading = false
        console.log(e)
        this.$toast.error('Error fetching models')
      }
    }
  }
}
</script>

<style></style>
