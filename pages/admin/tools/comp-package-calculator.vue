<template>
  <div class="p-6 bg-gray-200 w-full min-h-screen h-full">
    <div v-if="isLoading" class="mx-auto p-6 max-w-7xl">
      Logging in...
    </div>
    <div v-if="!isLoading" class="mx-auto p-6 max-w-7xl">
      <template v-if="isAuthenticated">
        <h1 class="text-2xl font-bold mb-6">
          Compensation Allocation Calculator
        </h1>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6 w-full">
          <div class="flex flex-col gap-6 mb-6">
            <div class="bg-white p-4 rounded shadow">
              <h2 class="text-lg font-semibold mb-4">
                Employee Information
              </h2>
              <div class="mb-4">
                <label class="block mb-1">Name</label>
                <input
                  v-model="employeeData.name"
                  type="text"
                  class="w-full p-2 border rounded"
                >
              </div>
              <div class="mb-4">
                <label class="block mb-1">Monthly Salary (USD)</label>
                <input
                  v-model.number="employeeData.monthlySalary"
                  type="number"
                  class="w-full p-2 border rounded"
                >
              </div>
              <div class="mb-4">
                <label class="block mb-1">Profit Share (%)</label>
                <input
                  v-model.number="employeeData.profitSharePercentage"
                  type="number"
                  class="w-full p-2 border rounded"
                >
              </div>
              <div class="mb-4">
                <label class="block mb-1">Minimum profit</label>
                <input
                  v-model.number="employeeData.minimumProfit"
                  type="number"
                  class="w-full p-2 border rounded"
                >
              </div>
              <div class="mb-4">
                <label class="block mb-1">Base Grant Percentage (%)</label>
                <input
                  v-model.number="employeeData.baseGrantPercentage"
                  type="number"
                  class="w-full p-2 border rounded"
                  step="0.1"
                >
              </div>
              <div class="mb-4">
                <label class="block mb-1">Bonus Grant Percentage (%)(Estimation)</label>
                <input
                  v-model.number="employeeData.bonusGrantPercentage"
                  type="number"
                  class="w-full p-2 border rounded"
                  step="0.1"
                >
              </div>
              <div class="mb-4">
                <label class="block mb-1">
                  Allocation Preference
                </label>
                <input
                  v-model.number="employeeData.allocationPreference"
                  type="range"
                  class="w-full"
                  :min="minAllocation ?? (100-maxAllocationCap*100)"
                  :max="maxAllocation ?? (maxAllocationCap*100)"
                  step="10"
                >
                <div class="flex justify-between">
                  <span>Profit Share</span>
                  <span>{{ employeeData.allocationPreference.toFixed(0) }}%</span>
                  <span>Phantom Share</span>
                </div>
              </div>
            </div>
            <div class="bg-white p-4 rounded shadow">
              <h2 class="text-lg font-semibold mb-4">
                Company Settings
              </h2>
              <div class="mb-4">
                <label class="block mb-1">Company Value (USD)</label>
                <input
                  v-model.number="companySettings.companyValue"
                  type="number"
                  class="w-full p-2 border rounded"
                >
              </div>
              <div class="mb-4">
                <label class="block mb-1">Monthly Profit (USD)</label>
                <input
                  v-model.number="companySettings.monthlyProfit"
                  type="number"
                  class="w-full p-2 border rounded"
                >
              </div>
              <div class="mb-4">
                <label class="block mb-1">Expected Annual Growth Rate (%)</label>
                <input
                  v-model.number="companySettings.growthRate"
                  type="number"
                  class="w-full p-2 border rounded"
                  step="1"
                >
              </div>
              <div class="mb-4">
                <label class="block mb-1">Max Profit Share Cap (%)</label>
                <input
                  v-model.number="companySettings.profitShareCap"
                  disabled
                  type="number"
                  class="w-full p-2 border rounded"
                  step="5"
                >
              </div>
            </div>
          </div>
          <div v-if="results" class="bg-white p-4 rounded shadow w-full col-span-2">
            <h2 class="text-lg font-semibold mb-4">
              Compensation Results
            </h2>

            <div class="overflow-x-auto w-full">
              <table class="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th class="p-2 text-left font-medium bg-gray-50">
                      Metric
                    </th>
                    <th class="p-2 text-right font-medium bg-gray-50">
                      Value
                    </th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                  <tr>
                    <td class="p-2 font-medium">
                      Name
                    </td>
                    <td class="p-2 text-right">
                      {{ results.name }}
                    </td>
                  </tr>
                  <tr>
                    <td class="p-2 font-medium">
                      Monthly Salary
                    </td>
                    <td class="p-2 text-right">
                      <strong>{{ formatCurrency(results.monthlySalary) }}</strong>
                    </td>
                  </tr>
                  <tr class="bg-gray-50">
                    <td class="p-2 font-medium">
                      Phantom Share Comp
                    </td>
                    <td class="p-2 text-right">
                  &nbsp;
                    </td>
                  </tr>
                  <tr>
                    <td class="p-2 font-medium">
                      Phantom Share Percentage
                    </td>
                    <td class="p-2 text-right">
                      {{ results.phantomSharePercentage.toFixed(2) }}%
                    </td>
                  </tr>
                  <tr>
                    <td class="p-2 font-medium">
                      Bonus Grant Percentage
                    </td>
                    <td class="p-2 text-right">
                      {{ results.bonusGrandPercentage.toFixed(2) }}%
                    </td>
                  </tr>
                  <!-- <tr>
                    <td class="p-2 font-medium">
                      Allocation Modifier
                    </td>
                    <td class="p-2 text-right">
                      -{{ results.additionalBonusPhantomSharePercentage.toFixed(2) }}%
                    </td>
                  </tr> -->
                  <tr>
                    <td class="p-2 font-medium">
                      Total Phantom Share Percentage (Including Estimate Bonus)
                    </td>
                    <td class="p-2 text-right">
                      <strong>{{ results.totalPhanSharePercentage.toFixed(2) }}%</strong>
                    </td>
                  </tr>
                  <tr class="bg-gray-50">
                    <td class="p-2 font-medium">
                      Profit Share Comp
                    </td>
                    <td class="p-2 text-right">
                  &nbsp;
                    </td>
                  </tr>
                  <tr>
                    <td class="p-2 font-medium">
                      Profit Share Percentage
                    </td>
                    <td class="p-2 text-right">
                      {{ results.profitSharePercentage.toFixed(2) }}%
                    </td>
                  </tr>
                  <tr>
                    <td class="p-2 font-medium">
                      Profit Share Cap
                    </td>
                    <td class="p-2 text-right">
                      {{ results.profitShareCap.toFixed(2) }}%
                    </td>
                  </tr>
                  <tr>
                    <td class="p-2 font-medium">
                      Max Profit Share Per Month  <span class="text-xs text-gray-500 italic">(Salary * Profit Share Cap)</span>
                    </td>
                    <td class="p-2 text-right">
                      {{ results.maxProfitSharePerMonth }}
                    </td>
                  </tr>
                  <tr>
                    <td class="p-2 font-medium">
                      Profit Share at Current Profit  <span class="text-xs text-gray-500 italic">({{ formatCurrency(companySettings.monthlyProfit) }} - {{ formatCurrency(employeeData.minimumProfit) }} * {{ results.profitSharePercentage.toFixed(2) }}%)</span>
                    </td>
                    <td class="p-2 text-right">
                      <strong>{{ results.profitShareAtMonthlyProfit }}</strong>
                    </td>
                  </tr>

                  <tr class="bg-gray-50">
                    <td class="p-2 font-medium">
                      Yearly Compensation Breakdown
                    </td>
                    <td class="p-2 text-right">
                  &nbsp;
                    </td>
                  </tr>
                  <tr>
                    <td class="p-2 font-medium pl-4">
                      Yearly Phantom Share Comp <span class="text-xs text-gray-500 italic">(Company Value * Total Phantom Share Percentage / 4)</span>
                    </td>
                    <td class="p-2 text-right">
                      {{ results.yearlyPhantomShareComp }}
                    </td>
                  </tr>
                  <tr>
                    <td class="p-2 font-medium pl-4">
                      Yearly Max Profit Share Comp
                    </td>
                    <td class="p-2 text-right">
                      {{ results.yearlyMaxProfitShareComp }}
                    </td>
                  </tr>
                  <tr>
                    <td class="p-2 font-medium pl-4">
                      Total Yearly Bonus Comp
                    </td>
                    <td class="p-2 text-right  font-medium">
                      {{ results.totalYearlyBonusComp }}
                    </td>
                  </tr>
                  <tr>
                    <td class="p-2 font-medium pl-4">
                      Total Yearly Salary
                    </td>
                    <td class="p-2 text-right  font-medium">
                      {{ results.totalYearlySalary }}
                    </td>
                  </tr>
                  <tr>
                    <td class="p-2 font-medium pl-4">
                      Total Yearly Compensation
                    </td>
                    <td class="p-2 text-right font-bold">
                      {{ results.totalYearlyCompensation }}
                    </td>
                  </tr>
                  <tr class="bg-gray-50">
                    <td class="p-2 font-medium">
                      4-Year Value Projection
                    </td>
                    <td class="p-2 text-right">
                  &nbsp;
                    </td>
                  </tr>
                  <tr>
                    <td class="p-2 font-medium pl-4">
                      4-year Phantom Share Value (0% growth)
                    </td>
                    <td class="p-2 text-right">
                      {{ results.phantomShareValue4YearNoGrowth }}
                    </td>
                  </tr>
                  <tr>
                    <td class="p-2 font-medium pl-4">
                      4-year Phantom Share Value ({{ companySettings.growthRate.toFixed(0) }}% growth)
                    </td>
                    <td class="p-2 text-right">
                      {{ results.phantomShareValue4YearWithGrowth }}
                    </td>
                  </tr>
                  <tr>
                    <td class="p-2 font-medium pl-4">
                      4-year Profit Share Value
                    </td>
                    <td class="p-2 text-right">
                      {{ results.profitShareValue4Year }}
                    </td>
                  </tr>
                  <tr>
                    <td class="p-2 font-medium pl-4">
                      4-year Profit Share Value (with {{ companySettings.growthRate.toFixed(0) }}% growth)
                    </td>
                    <td class="p-2 text-right">
                      {{ results.profitShareValue4YearWithGrowth }}
                    </td>
                  </tr>
                  <tr>
                    <td class="p-2 font-medium pl-4">
                      Total Comp Value (no growth)
                    </td>
                    <td class="p-2 text-right font-bold">
                      {{ results.totalCompValueNoGrowth }}
                    </td>
                  </tr>
                  <tr>
                    <td class="p-2 font-medium pl-4">
                      Total Comp Value (with {{ companySettings.growthRate.toFixed(0) }}% growth)
                    </td>
                    <td class="p-2 text-right font-bold text-green-500">
                      {{ results.totalCompValueWithGrowth }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <Input v-model="enteredPassword" label="Password" />
        <ButtonPrimary @click="checkPassword">
          Auth
        </ButtonPrimary>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      isAuthenticated: false,
      enteredPassword: '',
      hardcodedPassword: 'headshotpro123!', // You can change this!
      unsubscribeAuthChange: () => {},
      isLoading: true,
      isInitializing: true,
      employeeData: {
        name: '',
        monthlySalary: 0,
        baseGrantPercentage: 0,
        bonusGrantPercentage: 0,
        allocationPreference: 75,
        profitSharePercentage: 0,
        minimumProfit: 0
      },
      companySettings: {
        companyValue: 16000000,
        profitShareCap: 75,
        growthRate: 20,
        monthlyProfit: 115000
      },
      results: null,
      maxAllocationCap: 1, // 0.75,
      minAllocation: null
    }
  },
  computed: {
    growthRatePercentage: {
      get () {
        return this.companySettings.growthRate * 100
      },
      set (value) {
        this.companySettings.growthRate = value / 100
      }
    }

  },

  watch: {
    employeeData: {
      handler (newVal) {
        this.calculateCompensation()
        this.updateQueryParams()
      },
      deep: true
    },
    companySettings: {
      handler (newVal) {
        this.calculateCompensation()
        this.updateQueryParams()
      },
      deep: true
    },
    minAllocation () {
      this.updateQueryParams()
    },
    maxAllocation () {
      this.updateQueryParams()
    }
  },

  // beforeDestroy () {
  //   this.unsubscribeAuthChange()
  // },
  mounted () {
    // this.unsubscribeAuthChange = this.$fire.auth.onAuthStateChanged(async (user) => {
    //   if (user) {
    //     await this.setupUserDetails(user)
    //     const response = await this.$axios.$get('/auth/admin/check')
    //     const { isAdmin } = response
    //     if (isAdmin === true) {
    this.isLoading = false
    const { name, salary, grantPercentage, bonusPercentage, profitShare, minAllocation, maxAllocation, allocationPreference, minimumProfit, profitShareCap } = this.$route.query
    if (name) {
      this.employeeData.name = name
    }
    if (salary) {
      this.employeeData.monthlySalary = parseFloat(salary)
    }
    if (grantPercentage) {
      this.employeeData.baseGrantPercentage = parseFloat(grantPercentage)
    }
    if (bonusPercentage) {
      this.employeeData.bonusGrantPercentage = parseFloat(bonusPercentage)
    }

    if (profitShare) {
      this.employeeData.profitSharePercentage = parseFloat(profitShare)
    }

    if (minAllocation) {
      this.minAllocation = parseFloat(minAllocation)
    }

    if (maxAllocation) {
      this.maxAllocation = parseFloat(maxAllocation)
    }

    if (allocationPreference) {
      this.employeeData.allocationPreference = parseFloat(allocationPreference)
    }

    if (minimumProfit) {
      this.employeeData.minimumProfit = parseFloat(minimumProfit)
    }

    if (profitShareCap) {
      this.companySettings.profitShareCap = parseFloat(profitShareCap)
    }

    this.calculateCompensation()

    // Set initialization complete after initial load
    this.$nextTick(() => {
      this.isInitializing = false
    })
    // } else {
    //   this.$router.push('/')
    // }
    //   } else {
    //     this.$router.push('/auth/login')
    //   }
    // })
  },

  methods: {
    checkPassword () {
      if (this.enteredPassword === this.hardcodedPassword) {
        this.isAuthenticated = true
      } else {
        alert('Incorrect password!')
      }
    },

    updateQueryParams () {
      // Don't update query params during initial load
      if (this.isInitializing) { return }

      const query = {}

      // Employee data params
      if (this.employeeData.name) { query.name = this.employeeData.name }
      if (this.employeeData.monthlySalary != null && this.employeeData.monthlySalary !== 0) {
        query.salary = this.employeeData.monthlySalary.toString()
      }
      if (this.employeeData.baseGrantPercentage != null && this.employeeData.baseGrantPercentage !== 0) {
        query.grantPercentage = this.employeeData.baseGrantPercentage.toString()
      }
      if (this.employeeData.bonusGrantPercentage != null && this.employeeData.bonusGrantPercentage !== 0) {
        query.bonusPercentage = this.employeeData.bonusGrantPercentage.toString()
      }
      if (this.employeeData.profitSharePercentage != null && this.employeeData.profitSharePercentage !== 0) {
        query.profitShare = this.employeeData.profitSharePercentage.toString()
      }
      if (this.employeeData.allocationPreference != null && this.employeeData.allocationPreference !== 75) {
        query.allocationPreference = this.employeeData.allocationPreference.toString()
      }
      if (this.employeeData.minimumProfit != null && this.employeeData.minimumProfit !== 0) {
        query.minimumProfit = this.employeeData.minimumProfit.toString()
      }

      // Company settings params
      if (this.companySettings.profitShareCap != null && this.companySettings.profitShareCap !== 75) {
        query.profitShareCap = this.companySettings.profitShareCap.toString()
      }

      // Allocation bounds
      if (this.minAllocation != null) {
        query.minAllocation = this.minAllocation.toString()
      }
      if (this.maxAllocation != null) {
        query.maxAllocation = this.maxAllocation.toString()
      }

      // Update route without creating new history entry
      this.$router.replace({ query }).catch(() => {
        // Ignore navigation duplicated errors
      })
    },
    calculateCompensation () {
      // eslint-disable-next-line no-unused-vars
      const { name, monthlySalary, bonusGrantPercentage, baseGrantPercentage, allocationPreference, minimumProfit = 0 } = this.employeeData

      const additionalBonusPhantomSharePercentage = 0// (baseGrantPercentage * (0.75 - this.maxAllocationCap))// * (1 - Math.exp(-0.04 * (75 - allocationPreference)))
      const phantomSharePercentage = Math.max((this.employeeData.baseGrantPercentage) * (this.employeeData.allocationPreference / 100) - additionalBonusPhantomSharePercentage, 0)
      // Exponential formula that approaches 0 as allocationPreference approaches 75
      // const profitShareCap = this.companySettings.profitShareCap * (1 - (this.employeeData.allocationPreference / 100))
      const profitShareCap = this.companySettings.profitShareCap

      const bonusGrandPercentage = (phantomSharePercentage > 0) ? ((bonusGrantPercentage) * this.employeeData.allocationPreference / 100) : 0

      const totalPhanSharePercentage = phantomSharePercentage + bonusGrandPercentage

      const yearlyPhantomShareComp = (this.companySettings.companyValue * (totalPhanSharePercentage / 100)) / 4

      const profitSharePercentage = (this.employeeData.profitSharePercentage) * (1 - (this.employeeData.allocationPreference / 100))// * (Math.exp(-0.02 * (this.employeeData.allocationPreference - 25)))

      const profitShareAtMonthlyProfit = Math.min((this.companySettings.monthlyProfit - minimumProfit) * (profitSharePercentage / 100), (monthlySalary * profitShareCap) / 100)

      const maxCappedProfitSharePerMonth = (monthlySalary * profitShareCap) / 100
      const maxProfitSharePerMonth = Math.min((this.companySettings.monthlyProfit / 10) * (profitShareCap / 100), maxCappedProfitSharePerMonth)
      const yearlyMaxProfitShareComp = Math.min(12 * (profitShareAtMonthlyProfit), maxProfitSharePerMonth * 12)
      const totalYearlyBonusComp = yearlyPhantomShareComp + yearlyMaxProfitShareComp
      const totalYearlySalary = monthlySalary * 12
      const totalYearlyCompensation = totalYearlySalary + totalYearlyBonusComp

      // 4 year

      const phantomShareValue4YearNoGrowth = yearlyPhantomShareComp * 4
      const phantomShareValue4YearWithGrowth = yearlyPhantomShareComp * Math.pow(1 + (this.companySettings.growthRate / 100), 4) * 4
      const profitShareValue4Year = yearlyMaxProfitShareComp * 4
      const profitShareValue4YearWithGrowth = Math.min(yearlyMaxProfitShareComp * Math.pow(1 + (this.companySettings.growthRate / 100), 4) * 4, 12 * maxProfitSharePerMonth * 4)
      const totalCompValueWithGrowth = phantomShareValue4YearWithGrowth + profitShareValue4YearWithGrowth
      const totalCompValueNoGrowth = phantomShareValue4YearNoGrowth + profitShareValue4Year

      this.results = {
        name,
        monthlySalary,
        bonusGrandPercentage,
        phantomSharePercentage,
        additionalBonusPhantomSharePercentage,
        totalPhanSharePercentage,
        profitShareCap,
        maxProfitSharePerMonth: this.formatCurrency(maxProfitSharePerMonth),
        yearlyPhantomShareComp: this.formatCurrency(yearlyPhantomShareComp),
        yearlyMaxProfitShareComp: this.formatCurrency(yearlyMaxProfitShareComp),
        totalYearlyBonusComp: this.formatCurrency(totalYearlyBonusComp),
        totalYearlySalary: this.formatCurrency(totalYearlySalary),
        totalYearlyCompensation: this.formatCurrency(totalYearlyCompensation),
        phantomShareValue4YearNoGrowth: this.formatCurrency(phantomShareValue4YearNoGrowth),
        phantomShareValue4YearWithGrowth: this.formatCurrency(phantomShareValue4YearWithGrowth),
        profitShareValue4Year: this.formatCurrency(profitShareValue4Year),
        profitShareValue4YearWithGrowth: this.formatCurrency(profitShareValue4YearWithGrowth),
        totalCompValueWithGrowth: this.formatCurrency(totalCompValueWithGrowth),
        totalCompValueNoGrowth: this.formatCurrency(totalCompValueNoGrowth),
        profitSharePercentage,
        profitShareAtMonthlyProfit: this.formatCurrency(profitShareAtMonthlyProfit)
      }
    },

    formatCurrency (value) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        maximumFractionDigits: 2
      }).format(value)
    }
  }

}
</script>

<style>

</style>
