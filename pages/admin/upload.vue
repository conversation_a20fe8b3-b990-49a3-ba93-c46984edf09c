<template>
  <div class="flex">
    <div class="max-w-md mx-auto p-6">
      <div class="w-[400px] p-6 border-r">
        <h1 class="text-2xl font-bold mb-6">
          File Upload
        </h1>
        <LoadingWrapper :is-loading="isLoading" title="Uploading...">
          <form class="space-y-4" @submit.prevent="submitForm">
            <div class="flex items-center justify-center w-full">
              <label v-if="!hasSelectedFiles" for="file-upload" class="flex flex-col items-center justify-center w-full h-64 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                <div class="flex flex-col items-center justify-center pt-5 pb-6">
                  <svg class="w-10 h-10 mb-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" /></svg>
                  <p class="mb-2 text-sm text-gray-500"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                  <p class="text-xs text-gray-500">SVG, PNG, JPG, GIF or PDF</p>
                </div>
                <input
                  id="file-upload"
                  ref="fileInput"
                  multiple
                  type="file"
                  class="hidden"
                  @change="handleFileChange"
                >
              </label>
              <div v-else class="w-full">
                <div class="flex items-center justify-between p-4 border-2 border-gray-300 rounded-lg">
                  <span class="text-sm text-gray-500">{{ selectedFiles.length }} file(s) selected</span>
                  <button class="text-red-500 hover:text-red-700" @click="removeFiles">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                  </button>
                </div>
              </div>
            </div>
            <template v-if="hasSelectedFiles && !hasNonImageFile">
              <div>
                <label for="file-format" class="block text-sm font-medium text-gray-700">File Output Format</label>
                <select id="file-format" v-model="fileFormat" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                  <option value="jpg">
                    JPG
                  </option>
                  <option value="png">
                    PNG
                  </option>
                </select>
              </div>

              <div>
                <label for="file-quality" class="block text-sm font-medium text-gray-700">File Quality (1-100)</label>
                <input
                  id="file-quality"
                  v-model.number="fileQuality"
                  type="number"
                  min="1"
                  max="100"
                  class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
              </div>

              <div>
                <label for="resize-width" class="block text-sm font-medium text-gray-700">Max width or height (px)</label>
                <input id="resize-width" v-model.number="resizeWidth" type="number" min="0" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
              </div>
            </template>
            <button type="submit" :disabled="!hasSelectedFiles" class="w-full px-4 py-2 text-white bg-blue-500 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed">
              Upload
            </button>
          </form>
        </LoadingWrapper>
        <p v-if="uploadStatus" class="mt-4 text-center font-semibold" :class="{'text-green-500': uploadStatus.includes('success'), 'text-red-500': uploadStatus.includes('failed') || uploadStatus.includes('error'), 'text-orange-500': uploadStatus.includes('uploaded, ')}">
          {{ uploadStatus }}
        </p>
      </div>
    </div>
    <div class="flex-1 p-6">
      <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
        <div v-for="file in files" :key="file.path" class="relative group ">
          <ImageDns v-if="!file?.mimeType || file?.mimeType?.startsWith('image/')" :src="file.url" class="w-full border-[4px] border-white" />
          <div v-else class="w-full h-full flex items-center justify-center bg-white p-2 text-center text-xs break-all">
            {{ file.mimeType }}
          </div>
          <button
            class="absolute bottom-2 right-2 bg-white bg-opacity-75 p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
            title="Copy URL"
            @click="copyUrl(file.url)"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      selectedFiles: [],
      uploadStatus: '',
      files: [],
      isLoading: false,
      fileFormat: 'jpg',
      fileQuality: 90,
      resizeWidth: 0
    }
  },
  computed: {
    hasNonImageFile () {
      return this.selectedFiles.some(file => !file.type.startsWith('image/'))
    },
    hasSelectedFiles () {
      return this.selectedFiles.length > 0
    }
  },
  async mounted () {
    await this.fetchAllFiles()
  },
  methods: {
    async submitForm () {
      if (!this.hasSelectedFiles) {
        return this.$toast.error('No files selected')
      }

      this.isLoading = true
      this.uploadStatus = 'Uploading files...'
      const uploadPromises = []

      for (const file of this.selectedFiles) {
        const mimeType = file.type
        if (mimeType && mimeType.startsWith('image/')) {
          const options = this.hasNonImageFile
            ? {}
            : {
                format: this.fileFormat,
                quality: this.fileQuality,
                resizeWidth: this.resizeWidth
              }
          uploadPromises.push(this.uploadImageSingle(file, options))
        } else {
          uploadPromises.push(this.uploadFileSingle(file))
        }
      }

      try {
        const results = await Promise.allSettled(uploadPromises)

        let successfulUploads = 0
        let failedUploads = 0
        const newFiles = []

        results.forEach((result) => {
          if (result.status === 'fulfilled' && result.value?.data) {
            successfulUploads++
            newFiles.push({
              url: result.value.data.url,
              path: result.value.data.path,
              mimeType: result.value.data.mimeType,
              _id: result.value.data._id
            })
          } else {
            failedUploads++
            console.error('Upload failed for a file:', result.reason || 'Unknown error')
          }
        })

        this.files.unshift(...newFiles)

        if (failedUploads === 0) {
          this.uploadStatus = `${successfulUploads} file(s) uploaded successfully!`
          this.$toast.success(this.uploadStatus)
        } else if (successfulUploads > 0) {
          this.uploadStatus = `${successfulUploads} file(s) uploaded, ${failedUploads} failed.`
          this.$toast.warning(this.uploadStatus)
        } else {
          this.uploadStatus = `All ${failedUploads} file uploads failed.`
          this.$toast.error(this.uploadStatus)
        }

        this.removeFiles()
      } catch (error) {
        console.error('Error processing uploads:', error)
        this.uploadStatus = 'An error occurred during the upload process.'
        this.$toast.error(this.uploadStatus)
      } finally {
        this.isLoading = false
      }
    },
    handleFileChange (event) {
      this.selectedFiles = Array.from(event.target.files)
      this.uploadStatus = ''
    },
    async copyUrl (url) {
      try {
        await navigator.clipboard.writeText(url)
        this.$toast.success('URL copied to clipboard')
      } catch (err) {
        console.error('Failed to copy URL: ', err)
        this.$toast.error('Failed to copy URL')
      }
    },
    removeFiles () {
      this.selectedFiles = []
      if (this.$refs.fileInput) {
        this.$refs.fileInput.value = ''
      }
    },
    uploadFileSingle (file) {
      const formData = new FormData()
      formData.append('file', file)

      return this.$axios.$post('/admin/upload/file', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      })
    },
    uploadImageSingle (file, options = {}) {
      if (!file) { return Promise.reject(new Error('No file provided to uploadImageSingle')) }

      const formData = new FormData()
      formData.append('file', file)
      if (options.format) { formData.append('format', options.format) }
      if (options.quality) { formData.append('quality', options.quality) }
      if (options.resizeWidth && options.resizeWidth > 0) { formData.append('resizeWidth', options.resizeWidth) }

      return this.$axios.$post('/admin/upload/image', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      })
    },
    async fetchAllFiles () {
      try {
        this.isLoading = true
        const response = await this.$axios.$get('/admin/upload/all')
        this.files = response.data?.map(f => ({ ...f, mimeType: f.mimeType || null })) || []
      } catch (error) {
        console.error('Error fetching files:', error)
        this.$toast.error('Failed to fetch uploaded files')
      } finally {
        this.isLoading = false
      }
    }
  }
}
</script>
