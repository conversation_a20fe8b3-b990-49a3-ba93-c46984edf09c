<template>
  <div class="p-8">
    <div class="flex w-full justify-between space-x-2 p-2">
      <Input v-model="search" placeholder="Search" class="w-full" />
      <InputSelect v-model="searchBy" :options="['organizationId']" class="w-1/4" />
      <ButtonPrimary size="sm" @click="fetchData">
        Search
      </ButtonPrimary>
      <ButtonPrimary size="sm" @click="addNew">
        Add new
      </ButtonPrimary>
    </div>
    <div class="mx-auto w-full bg-white p-8 shadow">
      <LoadingWrapper :is-loading="isLoading">
        <Table :head="['Organization id', 'Organization name', 'Credits', 'Available credits', 'Tokens', 'Actions']">
          <template v-for="item in apiClients">
            <!-- <template v-if="inSearch(item)"> -->
            <TableRow :key="item._id">
              <TableItem>
                <a v-if="item?.organization" :href="`/admin/organization?id=${item.organization._id}`" class="hover:underline">
                  {{ item.organization._id }}
                </a>
              </TableItem>
              <TableItem>
                <a v-if="item?.organization" :href="`/admin/organization?id=${item.organization._id}`" class="hover:underline">
                  {{ item.organization.name }}
                </a>
              </TableItem>
              <TableItem>{{ item.totalCredits }}</TableItem>
              <TableItem>{{ item.availableCredits }}</TableItem>
              <TableItem>{{ item.tokens.length }}</TableItem>
              <TableItem>
                <ButtonDropdown v-if="dropdownItems.length > 0" :items="dropdownItems" title="Actions" @select="handleDropdown($event, item._id)" />
              </TableItem>
            </TableRow>
            <!-- </template> -->
          </template>
        </Table>
      </LoadingWrapper>
    </div>
    <Popup v-if="showAddModal" size="xl" @closeModal="closeModals">
      <form @submit.prevent="addNewOrganization">
        <InputSelect v-model="selectedOrganization" label="Organization" :options="organizations.map(o => ({ title: o.name, value: o._id }))" />
        <ButtonPrimary type="submit">
          Add
        </ButtonPrimary>
      </form>
    </Popup>
    <Popup v-if="showTokensModal" size="xl" @closeModal="closeModals">
      <div>
        <h2 class="text-lg font-bold">
          Tokens
        </h2>
        <ul class="mt-4">
          <li v-for="token in activeClient.tokens" :key="token" class="px-4 py-2 even:bg-gray-50 rounded text-sm flex">
            <p>{{ token }}</p>
            <div class="ml-auto pl-4 flex space-x-2">
              <ButtonWhite size="xs" @click="copyToken(token)">
                Copy
              </ButtonWhite>
              <ButtonWhite size="xs" @click="deleteToken(token)">
                Delete
              </ButtonWhite>
            </div>
          </li>
        </ul>
      </div>
    </Popup>
    <Popup v-if="showAddCreditsModal" size="xl" @closeModal="closeModals">
      <form class="space-y-4" @submit.prevent="addCredits">
        <Input v-model="creditsToAdd" type="number" min="0" label="Credits" />
        <Input v-model="selectedDate" type="date" label="Date (optional)" />
        <ButtonPrimary size="sm" type="submit">
          Add credits
        </ButtonPrimary>
      </form>
    </Popup>
  </div>
</template>

<script>
import copy from 'copy-to-clipboard'

export default {
  layout: 'admin',
  data () {
    return {
      apiClients: [],
      organizations: [],
      isLoading: true,
      search: null,
      searchBy: 'organizationId',
      dropdownItems: [
        { title: 'Add credits', value: 'add:credits' },
        { title: 'List tokens', value: 'list:tokens' },
        { title: 'Add token', value: 'add:token' },
        { title: 'Delete', value: 'delete' }
      ],
      showAddModal: false,
      showTokensModal: false,
      showAddCreditsModal: false,
      selectedOrganization: null,
      activeClient: null,
      selectedDate: null,
      creditsToAdd: 0
    }
  },

  async created () {
    if (this.$route.query.search) {
      this.search = this.$route.query.search
    }
    if (this.$route.query.searchBy) {
      this.searchBy = this.$route.query.searchBy
    }
    await this.fetchData()
    this.isLoading = false
  },

  methods: {
    handleDropdown (event, id) {
      this.activeClient = this.apiClients.find(item => item._id === id)
      if (event === 'add:credits') {
        this.showAddCreditsModal = true
      }
      if (event === 'list:tokens') {
        this.showTokensModal = true
      }
      if (event === 'add:token') {
        this.addToken(id)
      }
      if (event === 'delete') {
        this.deleteClient(id)
      }
    },
    addCredits () {
      this.$axios.$post(`/admin/api-clients/${this.activeClient._id}/add-credits`, {
        date: this.selectedDate,
        amount: this.creditsToAdd
      })
        .then(() => {
          this.fetchData()
          this.creditsToAdd = 0
          this.selectedDate = null
          this.closeModals()
          this.$toast.success('Credits added successfully')
        })
        .catch((err) => {
          console.error(err)
          this.$toast.error('Failed to add credits')
        })
    },
    addToken (id) {
      if (!confirm('Are you sure you want to add a new token to this client?')) {
        return
      }

      this.$axios.post('/admin/api-clients/' + id + '/add-token')
        .then(async () => {
          this.$toast.success('Token added successfully')
          await this.fetchData()
          this.handleDropdown('list:tokens', id)
        })
        .catch((err) => {
          console.error(err)
          this.$toast.error('Failed to add token')
        })
    },
    deleteClient (id) {
      if (!confirm('Are you sure you want to delete this API client? This action cannot be reversed')) {
        return
      }

      if (!confirm('Double confirmation just to be sure :-)')) {
        return
      }

      this.$axios.delete('/admin/api-clients/' + id)
        .then(() => {
          this.apiClients = this.apiClients.filter(item => item._id !== id)
          this.activeClient = null
        })
        .catch((err) => {
          console.error(err)
          this.$toast.error('Failed to delete API client')
        })
    },
    addNew () {
      this.showAddModal = true
    },
    async addNewOrganization () {
      if (!this.selectedOrganization) {
        return
      }

      this.isLoading = true

      await this.$axios.$post('/admin/api-clients/create', {
        organizationId: this.selectedOrganization
      })

      await this.fetchData()
      this.selectedOrganization = null
      this.showAddModal = false
    },
    async fetchData () {
      this.isLoading = true
      this.apiClients = await this.$axios.$get('/admin/api-clients', {
        params: {
          ...(this.search && this.search.length > 0) ? { search: this.search.trim() } : {},
          ...(this.search && this.search.length > 0) ? { searchBy: this.searchBy.trim() } : {}
        }
      })

      const apiClientOrganizations = this.apiClients.map(item => item.organizationId)
      const organizations = (await this.$axios.$get('/admin/organization')).data
      this.organizations = organizations.filter(item => !apiClientOrganizations.includes(item._id))
      this.isLoading = false
    },
    closeModals () {
      this.showAddModal = false
      this.showTokensModal = false
      this.showAddCreditsModal = false
      this.activeClient = null
    },
    copyToken (token) {
      copy(token)
      this.$toast.success('Token copied to clipboard')
    },
    deleteToken (token) {
      if (!confirm('Are you sure you want to delete this token?')) {
        return
      }

      if (!confirm('Double confirmation just to be sure :-)')) {
        return
      }

      this.$axios.$delete('/admin/api-clients/' + this.activeClient._id + '/token/' + token)
        .then(async () => {
          this.$toast.success('Token deleted successfully')
          await this.fetchData()
          this.handleDropdown('list:tokens', this.activeClient._id)
        })
        .catch((err) => {
          console.error(err)
          this.$toast.error('Failed to delete token')
        })
    }
  }
}
</script>

  <style></style>
