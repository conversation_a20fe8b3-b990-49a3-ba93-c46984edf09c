<template>
  <div class="p-8">
    <div class="flex w-full justify-end space-x-2 pb-4">
      <InputSelect v-model="filter.status" :options="['all','void', 'complete', 'quoted']" />
      <InputSelect v-model="userRequestedFilterValue" :options="[{title: 'All', value: 'all'}, {title: 'Show user requested', value: true}, {title: 'Hide user requested', value: false}]" />
      <ButtonWhite size="sm" class="whitespace-nowrap" @click="showCreateInvoiceModal = true">
        Create invoice
      </ButtonWhite>
    </div>
    <Card class="mx-auto w-full">
      <LoadingWrapper :is-loading="isLoading">
        <Table :head="['Date', 'Status', 'Organization', 'Amount', 'Note', 'Actions']">
          <template v-for="item in filteredInvoices">
            <TableRow :key="item._id">
              <TableItem>{{ formatDate(item.createdOn) }}</TableItem>
              <TableItem>
                <span :class="{'text-orange-500': item.status === 'void', 'text-gray-500': item.status === 'invoiced', 'text-green-500': item.status === 'complete' }">
                  {{ item.status }} <span v-if="item?.meta?.quoteRequestedByUser" class="text-xs text-gray-500">(by user)</span>
                </span>
              </TableItem>
              <TableItem>
                <nuxt-link class="underline text-sm" target="_blank" :to="`/admin/organization?id=${item.organization._id}`">
                  {{ item.organization.name }}
                </nuxt-link>
              </TableItem>
              <TableItem>{{ formatPrice(item.amountTotal/100, item.currency, 2, false) }}</TableItem>
              <TableItem><span v-if="item.meta.notes" class="text-[10px] text-gray-500">{{ item.meta.notes }}</span></TableItem>
              <TableItem>
                <LoadingWrapper :is-loading="isLoading">
                  <ButtonDropdown
                    title="Actions"
                    size="xs"
                    :items="getDropdownItems(item)"
                    @select="handleDropdownAction($event, item)"
                  />
                </LoadingWrapper>
              </TableItem>
            </TableRow>
          </template>
        </Table>
        <PaginationInfinity v-if="invoices.length > 50" @fetch="fetchData($event)" />
      </LoadingWrapper>
    </Card>
    <Modal v-if="showCreateInvoiceModal" max-width="sm:max-w-5xl" @close="showCreateInvoiceModal = false">
      <AdminCreateInvoiceForm @success="fetchData" />
    </Modal>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      invoices: [],
      isLoading: true,
      showCreateInvoiceModal: false,
      filter: {
        status: 'all',
        userRequested: 'all'
      }
    }
  },
  computed: {
    filteredInvoices () {
      return this.invoices.filter((invoice) => {
        // Filter status
        const statusMatch = this.filter.status === 'all' || invoice.status === this.filter.status

        // Filter user requested
        let userRequestedMatch = true // Default to true if filter is 'all'
        if (this.filter.userRequested === true) {
          userRequestedMatch = invoice.meta?.quoteRequestedByUser === true
        } else if (this.filter.userRequested === false) {
          userRequestedMatch = !invoice.meta?.quoteRequestedByUser
        }

        return statusMatch && userRequestedMatch
      })
    },
    userRequestedFilterValue: {
      get () {
        return this.filter.userRequested
      },
      set (value) {
        if (value === 'all') {
          this.filter.userRequested = 'all'
        } else if (typeof value === 'string') {
          this.filter.userRequested = value === 'true'
        } else {
          this.filter.userRequested = Boolean(value)
        }
      }
    }
  },
  async created () {
    await this.fetchData()
    this.isLoading = false
  },
  methods: {
    async fetchData () {
      this.isLoading = true
      try {
        const response = await this.$axios.$get('/admin/invoices', {
          params: {
            ...(this.search && this.search.length > 0) ? { search: this.search.trim() } : {},
            ...(this.search && this.search.length > 0) ? { searchBy: this.searchBy.trim() } : {}
          }
        })
        this.invoices = response
        this.isLoading = false
        this.showCreateInvoiceModal = false
      } catch (err) {
        this.$toast.error('Error fetching invoices')
        this.isLoading = false
      }
    },
    async voidTransaction (item) {
      this.isLoading = true
      try {
        await this.$axios.$post(`/admin/invoices/${item._id}/void`)
        this.$toast.success('Invoice voided')
        await this.fetchData()
      } catch (err) {
        this.$toast.error('Error voiding invoice')
      } finally {
        this.isLoading = false
      }
    },
    openInvoice (item) {
      const url = `${process.env.BASE_URL}/invoice/${item.organization._id}/${item._id}`
      window.open(url, '_blank')
    },
    async markTransactionAsPaid (item) {
      this.isLoading = true
      try {
        await this.$axios.$post(`/admin/invoices/${item._id}/mark-as-paid`)
        this.$toast.success('Invoice marked as paid')
        await this.fetchData()
      } catch (err) {
        this.$toast.error('Error marking invoice as paid')
      } finally {
        this.isLoading = false
      }
    },
    getDropdownItems (item) {
      const items = []

      // Conditional items based on status
      if (item.status === 'invoiced' || item.status === 'quoted') {
        items.push({
          title: 'Void',
          value: 'void',
          classes: 'text-red-600'
        })
        items.push({
          title: 'Mark as paid',
          value: 'mark-as-paid',
          classes: 'text-green-600'
        })
      }

      // Always available items
      items.push({
        title: 'View user',
        value: 'view-user'
      })
      items.push({
        title: 'See invoice',
        value: 'see-invoice'
      })
      items.push({
        title: 'See quote',
        value: 'see-quote'
      })

      return items
    },
    handleDropdownAction (action, item) {
      if (action === 'void') {
        this.voidTransaction(item)
      } else if (action === 'mark-as-paid') {
        this.markTransactionAsPaid(item)
      } else if (action === 'view-user') {
        window.open(`/admin/user?search=${item.uid}&searchBy=uid`, '_blank')
      } else if (action === 'see-invoice') {
        this.openInvoice(item)
      } else if (action === 'see-quote') {
        const url = `/invoice/preview?org=${item.organization._id}&id=${item._id}`
        window.open(url, '_blank')
      }
    }
  }
}
</script>

  <style></style>
