<template>
  <div class="p-8">
    <div class="flex items-center justify-between mb-6">
      <h1 class="text-2xl font-bold text-gray-900">
        Edit Changelog Entry
      </h1>
      <nuxt-link to="/admin/changelog">
        <ButtonWhite size="sm">
          Back to list
        </ButtonWhite>
      </nuxt-link>
    </div>

    <LoadingWrapper :is-loading="isLoading">
      <Card class="mx-auto max-w-2xl">
        <form @submit.prevent="submitForm">
          <div class="space-y-6">
            <Input
              v-model="formData.title"
              label="Title"
              placeholder="Enter changelog title"
              :error="!!errors.title"
              required
            />
            <span v-if="errors.title" class="text-red-500 text-sm">{{ errors.title }}</span>

            <InputSelect
              v-model="formData.type"
              label="Type"
              :options="typeOptions"
              :error="!!errors.type"
              required
            />
            <span v-if="errors.type" class="text-red-500 text-sm">{{ errors.type }}</span>

            <Input
              v-model="formData.date"
              label="Date"
              type="date"
              :error="!!errors.date"
              required
            />
            <span v-if="errors.date" class="text-red-500 text-sm">{{ errors.date }}</span>

            <InputTextArea
              v-model="formData.description"
              label="Description"
              placeholder="Enter description in markdown format"
              :error="!!errors.description"
              rows="8"
              required
            />
            <span v-if="errors.description" class="text-red-500 text-sm">{{ errors.description }}</span>

            <InputSelect
              v-model="formData.status"
              label="Status"
              :options="statusOptions"
            />

            <div class="flex justify-end space-x-4">
              <nuxt-link to="/admin/changelog">
                <ButtonWhite>
                  Cancel
                </ButtonWhite>
              </nuxt-link>
              <ButtonPrimary type="submit" :disabled="isSubmitting">
                {{ isSubmitting ? 'Updating...' : 'Update Entry' }}
              </ButtonPrimary>
            </div>
          </div>
        </form>
      </Card>
    </LoadingWrapper>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      isLoading: true,
      isSubmitting: false,
      formData: {
        title: '',
        type: 'Improvement',
        date: '',
        description: '',
        status: 'published'
      },
      errors: {},
      typeOptions: [
        { title: 'New Feature', value: 'New Feature' },
        { title: 'Improvement', value: 'Improvement' },
        { title: 'Bugfix', value: 'Bugfix' }
      ],
      statusOptions: [
        { title: 'Published', value: 'published' },
        { title: 'Draft', value: 'draft' }
      ]
    }
  },
  async mounted () {
    await this.fetchChangelog()
  },
  methods: {
    async fetchChangelog () {
      try {
        this.isLoading = true
        const { success, data, errorMessage } = await this.$axios.$get(`/admin/changelog/${this.$route.params.id}`)

        if (!success) {
          throw new Error(errorMessage)
        }

        this.formData = {
          title: data.title,
          type: data.type,
          date: new Date(data.date).toISOString().split('T')[0],
          description: data.description,
          status: data.status
        }
      } catch (err) {
        this.handleError(err)
        this.$router.push('/admin/changelog')
      } finally {
        this.isLoading = false
      }
    },

    validateForm () {
      this.errors = {}

      if (!this.formData.title.trim()) {
        this.errors.title = 'Title is required'
      }

      if (!this.formData.type) {
        this.errors.type = 'Type is required'
      }

      if (!this.formData.date) {
        this.errors.date = 'Date is required'
      }

      if (!this.formData.description.trim()) {
        this.errors.description = 'Description is required'
      }

      return Object.keys(this.errors).length === 0
    },

    async submitForm () {
      if (!this.validateForm()) {
        return
      }

      try {
        this.isSubmitting = true
        const { success, errorMessage } = await this.$axios.$put(`/admin/changelog/${this.$route.params.id}`, this.formData)

        if (!success) {
          throw new Error(errorMessage)
        }

        this.$router.push('/admin/changelog')
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isSubmitting = false
      }
    }
  }
}
</script>

<style></style>
