<template>
  <div class="p-8">
    <div class="flex w-full justify-between space-x-2 pb-4">
      <nuxt-link to="/admin/changelog/new">
        <ButtonPrimary size="sm">
          New changelog entry
        </ButtonPrimary>
      </nuxt-link>
    </div>
    <Card class="mx-auto w-full">
      <LoadingWrapper :is-loading="isLoading">
        <Table v-if="changelogs.length > 0" :head="['Type', 'Title', 'Date', 'Status', 'Actions']">
          <template v-for="changelog in changelogs">
            <TableRow :key="changelog._id">
              <TableItem>
                <span :class="getTypeColor(changelog.type)" class="px-2 py-1 rounded-full text-xs font-medium">
                  {{ changelog.type }}
                </span>
              </TableItem>
              <TableItem>{{ changelog.title }}</TableItem>
              <TableItem>{{ formatDate(changelog.date) }}</TableItem>
              <TableItem>
                <span :style="`color:${getStatusColor(changelog.status)}`">{{ changelog.status }}</span>
              </TableItem>
              <TableItem class="flex items-center space-x-2">
                <ButtonDelete size="sm" @click="deleteItem(changelog._id)">
                  Delete
                </ButtonDelete>
                <nuxt-link :to="`/admin/changelog/edit/${changelog._id}`">
                  <ButtonWhite size="sm">
                    Edit
                  </ButtonWhite>
                </nuxt-link>
              </TableItem>
            </TableRow>
          </template>
        </Table>
        <p v-else>
          No changelog entries found
        </p>
      </LoadingWrapper>
    </Card>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      isLoading: true,
      changelogs: []
    }
  },
  async mounted () {
    await this.fetchData()
    this.isLoading = false
  },
  methods: {
    getTypeColor (type) {
      switch (type) {
        case 'New Feature':
          return 'bg-green-100 text-green-800'
        case 'Improvement':
          return 'bg-blue-100 text-blue-800'
        case 'Bugfix':
          return 'bg-yellow-100 text-yellow-800'
        default:
          return 'bg-gray-100 text-gray-800'
      }
    },
    getStatusColor (status) {
      switch (status) {
        case 'draft':
          return 'gray'
        case 'published':
          return 'green'
        default:
          return 'gray'
      }
    },
    async fetchData () {
      try {
        this.isLoading = true
        const { success, data, errorMessage } = await this.$axios.$get('/admin/changelog')
        if (!success) { throw new Error(errorMessage) }
        this.changelogs = data
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = false
      }
    },
    async deleteItem (id) {
      if (!confirm('Are you sure you want to delete this changelog entry?')) {
        return
      }

      try {
        const { success, errorMessage } = await this.$axios.$delete(`/admin/changelog/${id}`)
        if (!success) { throw new Error(errorMessage) }

        // Remove from local array
        this.changelogs = this.changelogs.filter(changelog => changelog._id !== id)
      } catch (err) {
        this.handleError(err)
      }
    }
  }
}
</script>

<style></style>
