<template>
  <div class="flex items-center justify-center">
    <div class="flex space-x-2">
      <Input v-model="path" />
      <ButtonPrimary size="sm" @click="signUrl">
        Sign
      </ButtonPrimary>
    </div>
    {{ signedUrl }}
  </div>
</template>

<script>
export default {
  data () {
    return {
      signedUrl: null,
      path: null
    }
  },
  methods: {
    signUrl () {
      if (!this.path) { return }
      this.$axios.post('/admin/sign-image', { path: this.path })
        .then((res) => {
          this.copyString(res.data.url)
          this.path = null
        })
        .catch((err) => {
          console.log(err)
        })
    }
  }

}
</script>

<style>

</style>
