<template>
  <div class="p-8">
    <div class="flex w-full justify-between space-x-2 p-2">
      <Input v-model="search" placeholder="Search" class="w-full" />
      <InputSelect v-model="searchBy" :options="['title', 'slug', 'alias']" class="w-1/4" />
      <ButtonPrimary size="sm" @click="fetchData">
        Search
      </ButtonPrimary>
      <ButtonPrimary size="sm" @click="addNew">
        Add new
      </ButtonPrimary>
    </div>
    <div class="mx-auto w-full bg-white p-8 shadow">
      <LoadingWrapper :is-loading="isLoading">
        <Table :head="['Title', 'Slug', 'Alias', 'Cost', 'Version', 'Actions']">
          <template v-for="item in comfyWorkflows">
            <!-- <template v-if="inSearch(item)"> -->
            <TableRow :key="item._id">
              <TableItem>{{ item.title }}</TableItem>
              <TableItem>{{ item.slug }}</TableItem>
              <TableItem>{{ item.alias }}</TableItem>
              <TableItem>{{ item.cost }}</TableItem>
              <TableItem>{{ item.version }}</TableItem>
              <TableItem>
                <ButtonDropdown v-if="dropdownItems.length > 0" :items="dropdownItems" title="Actions" @select="handleDropdown($event, item._id)" />
              </TableItem>
            </TableRow>
            <!-- </template> -->
          </template>
        </Table>
      </LoadingWrapper>
    </div>
    <Popup v-if="showAddModal" size="xl" @closeModal="closeModals">
      <form class="space-y-4" @submit.prevent="createWorkflow">
        <Input v-model="newWorkflow.title" label="Title" :required="true" placeholder="Background removal" />
        <Input v-model="newWorkflow.slug" label="Slug" :required="true" placeholder="background-removal-001" />
        <Input v-model="newWorkflow.version" label="Version" type="number" :required="true" placeholder="1" />
        <Input v-model="newWorkflow.cost" label="Cost in cents" type="number" placeholder="100" />
        <Input v-model="newWorkflow.alias" label="Alias (optional)" placeholder="background-removal" />
        <Input v-model="newWorkflow.replicateModel" label="Custom model (optional)" placeholder="headshotpro/cog-comfyui-workflow" />
        <InputTextArea v-model="newWorkflow.contents" label="ComfyUI Workflow" />
        <div key="variables" class="flex flex-col space-y-4 bg-slate-50 rounded-md p-4">
          <div class="flex items-center justify-between">
            <h3 class="text-base font-bold">
              Variables
            </h3>
            <ButtonPrimary size="sm" @click="addVariable">
              Add variable
            </ButtonPrimary>
          </div>
          <div v-for="(variable, index) in newWorkflow.variables" :key="index" class="flex items-end justify-between gap-2">
            <Input v-model="variable.name" label="Camel case name" placeholder="positivePrompt" />
            <Input v-model="variable.path" label="Path" placeholder="6.inputs.text" />
            <Input v-model="variable.default" label="Default value (optional)" />
            <ButtonWhite size="sm" @click="removeVariable(index)">
              Remove
            </ButtonWhite>
          </div>
        </div>
        <ButtonPrimary type="submit">
          Create workflow
        </ButtonPrimary>
      </form>
    </Popup>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      comfyWorkflows: [],
      isLoading: true,
      search: null,
      searchBy: 'slug',
      dropdownItems: [
        { title: 'Delete', value: 'delete' }
      ],
      showAddModal: false,
      newWorkflow: {
        title: '',
        slug: '',
        version: 1,
        alias: '',
        cost: 0,
        contents: '',
        replicateModel: '',
        variables: []
      }
    }
  },

  async created () {
    if (this.$route.query.search) {
      this.search = this.$route.query.search
    }
    if (this.$route.query.searchBy) {
      this.searchBy = this.$route.query.searchBy
    }
    await this.fetchData()
    this.isLoading = false
  },

  methods: {
    handleDropdown (event, id) {
      if (event === 'delete') {
        this.deleteWorkflow(id)
      }
    },
    deleteWorkflow (id) {
      if (!confirm('Are you sure you want to delete this ComfyUI workflow? This action cannot be reversed')) {
        return
      }

      if (!confirm('Double confirmation just to be sure :-)')) {
        return
      }

      this.$axios.delete('/admin/workflows/' + id)
        .then(() => {
          this.comfyWorkflows = this.comfyWorkflows.filter(item => item._id !== id)
        })
        .catch((err) => {
          console.error(err)
          this.$toast.error('Failed to delete ComfyUI Workflow')
        })
    },
    addNew () {
      this.showAddModal = true
    },
    async createWorkflow () {
      const contentError = this.contentErrorMessage()
      if (contentError) {
        this.$toast.error(contentError)
        return
      }

      this.isLoading = true

      try {
        const response = await this.$axios.$post('/admin/workflows/create', this.newWorkflow)
        if (response.err) {
          this.$toast.error(response.err)
          this.isLoading = false
          return
        }

        await this.fetchData()

        this.closeModals()
      } catch (err) {
        console.error(err)
        this.handleError(err)
      }
    },
    async fetchData () {
      this.isLoading = true
      this.comfyWorkflows = await this.$axios.$get('/admin/workflows', {
        params: {
          ...(this.search && this.search.length > 0) ? { search: this.search.trim() } : {},
          ...(this.search && this.search.length > 0) ? { searchBy: this.searchBy.trim() } : {}
        }
      })

      this.isLoading = false
    },
    closeModals () {
      this.showAddModal = false
      this.resetNewWorkflow()
    },
    resetNewWorkflow () {
      this.newWorkflow = {
        title: '',
        slug: '',
        version: 1,
        alias: '',
        cost: 0,
        contents: '',
        replicateModel: '',
        variables: []
      }
    },
    addVariable () {
      this.newWorkflow.variables.push({
        name: '',
        path: '',
        default: ''
      })
    },
    removeVariable (index) {
      this.newWorkflow.variables.splice(index, 1)
    },
    contentErrorMessage () {
      let content = null
      try {
        content = JSON.parse(this.newWorkflow.contents)
      } catch (err) {
        return 'Invalid JSON provided in the contents field'
      }

      for (const variable of this.newWorkflow.variables) {
        const path = variable.path.split('.')
        let current = content
        for (const key of path) {
          const numericalKey = parseInt(key)
          const stringKey = `${key}`
          if (!Object.keys(current).includes(stringKey) && !Object.keys(current).includes(numericalKey)) {
            return `Path ${variable.path} does not exist in the contents`
          }

          current = current[numericalKey] || current[stringKey]
        }
      }

      return null
    }
  }
}
</script>
