<template>
  <section class="flex min-h-screen items-start justify-center p-2 text-black md:p-4">
    <div class="w-full max-w-7xl p-2 md:p-8 space-y-4">
      <div class="flex flex-col space-y-4 md:flex-row md:space-y-0 md:justify-between md:items-center">
        <div class="flex flex-col space-y-2">
          <h2 class="text-xl font-bold">
            Audit Log
          </h2>
          <p class="mb-4 text-gray-700">
            Logs of all actions taken by the admin.
          </p>
        </div>
      </div>
      <div>
        <Table :head="['Action', 'Type', 'Operator', 'Date', 'IP Address', 'Data']">
          <TableRow v-for="log in auditLogs" :key="log._id">
            <TableItem>{{ log.action }}</TableItem>
            <TableItem>{{ log.type }}</TableItem>
            <TableItem>{{ log?.user?.displayName || log.user.email }}</TableItem>
            <TableItem>{{ formatDate(log.createdAt) }}</TableItem>
            <TableItem>{{ log.ip }}</TableItem>
            <TableItem>
              <button
                v-if="hasData(log.data)"
                class="px-2 py-1 bg-blue-100 hover:bg-blue-200 text-blue-800 rounded text-xs"
                @click="showDataModal(log.data)"
              >
                View Data
              </button>
            </TableItem>
          </TableRow>
        </Table>
      </div>
    </div>

    <!-- Data Modal -->
    <Modal v-if="showModal" max-width="sm:max-w-2xl" @close="showModal = false">
      <div class="p-6 space-y-4">
        <h3 class="text-lg font-bold">
          Data Object
        </h3>
        <pre class="bg-gray-100 p-4 rounded-md overflow-auto max-h-[60vh] text-xs">{{ JSON.stringify(currentData, null, 2) }}</pre>
      </div>
    </Modal>
  </section>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      auditLogs: [],
      showModal: false,
      currentData: null
    }
  },
  async mounted () {
    this.$loading.show()
    const response = await this.$axios.$get('/admin/auditlog')
    this.auditLogs = response
    this.$loading.hide()
  },
  methods: {
    formatDate (dateString) {
      const date = new Date(dateString)
      const now = new Date()
      const diffTime = Math.abs(now - date)
      const diffSeconds = Math.floor(diffTime / 1000)

      // Less than a minute
      if (diffSeconds < 60) {
        return `${diffSeconds} seconds ago`
      }

      // Less than an hour
      const diffMinutes = Math.floor(diffSeconds / 60)
      if (diffMinutes < 60) {
        return `${diffMinutes} minute${diffMinutes === 1 ? '' : 's'} ago`
      }

      // Less than a day
      const diffHours = Math.floor(diffMinutes / 60)
      if (diffHours < 24) {
        return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`
      }

      // Less than 7 days
      const diffDays = Math.floor(diffHours / 24)
      if (diffDays < 7) {
        return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`
      }

      // More than 7 days - show the actual date
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    hasData (data) {
      return data && Object.keys(data).length > 0
    },
    showDataModal (data) {
      this.currentData = data
      this.showModal = true
    }
  }
}
</script>

<style>

</style>
