<template>
  <section class="flex min-h-screen items-start justify-center px-0 py-4 text-black md:items-start">
    <div class="flex flex-row space-x-2 w-full p-4">
      <div class="w-[400px]">
        <Card>
          <div class="flex flex-col space-y-2 p-4">
            <LoadingWrapper :is-loading="isLoading">
              <AdminWardrobeUpload />
              <Input v-model="form.caption" label="Caption" />
              <Input v-model="form.title" label="Title" />
              <InputSelect v-model="form.gender" :options="['male', 'female']" label="Gender" />
              <ButtonPrimary v-if="trainingImagePaths?.length > 0 && form?.caption?.length > 0 && form?.title?.length > 0 && form?.gender?.length > 0" size="sm" @click="addItem">
                Submit
              </ButtonPrimary>
              <ButtonDisabled v-else size="sm">
                Submit
              </ButtonDisabled>
            </LoadingWrapper>
          </div>
        </Card>
      </div>
      <div class="w-full">
        <div class="grid grid-cols-4 gap-4">
          <div v-for="item in items" :key="item._id">
            <Card>
              <div class="p-2">
                <img v-if="item?.thumbnails.length > 0" :src="item.thumbnails[0]">
                {{ item.status }} - {{ item.title }}
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      items: [],
      item: null,
      isLoading: true,
      form: {
        caption: 'wearing TOK clothing',
        title: null,
        gender: null
      }
    }
  },
  computed: {
    trainingImagePaths: {
      get () { return this.$store.state.admin.trainingImagePaths },
      set () { this.$store.commit('admin/SET_TRAINING_IMAGE_PATHS', this.trainingImagePaths) }
    }
  },
  mounted () {
    this.fetch()
  },
  methods: {

    async fetch () {
      try {
        this.isLoading = true
        const { success, data, errorMessage } = await this.$axios.$get('/admin/wardrobe')
        if (!success) {
          throw new Error(errorMessage)
        }
        this.items = data
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = false
      }
    },
    async addItem () {
      try {
        this.isLoading = true
        const { success, data, errorMessage } = await this.$axios.$post('/admin/wardrobe', { ...this.form, trainingImages: this.trainingImagePaths })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.items.push(data)
        this.form = { caption: 'wearing TOK clothing', title: null, gender: null }
        this.trainingImagePaths = []
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isLoading = false
      }
    },
    async deleteItem (id) {
      try {
        const { success, errorMessage } = await this.$axios.$delete(`/admin/clothing/${id}`)
        if (!success) {
          throw new Error(errorMessage)
        }
        this.clothingItems = this.clothingItems.filter(item => item._id !== id)
      } catch (err) {
        this.handleError(err)
      }
    },
    async toggleStatus (id) {
      try {
        const { success, errorMessage } = await this.$axios.$post(`/admin/clothing/${id}/status`)
        if (!success) {
          throw new Error(errorMessage)
        }
        const item = this.clothingItems.find(item => item._id === id)
        item.active = !item.active
      } catch (err) {
        this.handleError(err)
      }
    }
  }
}
</script>

<style></style>
