<template>
  <div class="flex w-full space-y-4 space-x-2 p-2">
    <Card class="w-[400px] p-4">
      <h1 class="text-xl font-medium">
        Catalog contains {{ catalog.length }} images
      </h1>

      <div class="flex flex-col items-start justify-start space-y-4">
        <!-- <InputTag :tags="tags" label="Tags" @update="tags = $event" /> -->
        <input
          id="fileextra"
          ref="fileextra"
          name="fileextra"
          type="file"
          accept="image/png, image/jpeg, image/jpg"
          enctype="multipart/form-data"
          multiple
          @change="onFileSelected"
        >
        <!-- <div class="flex space-x-2">
          <InputCheckbox
            v-for="gender in genderOptions"
            :key="gender"
            :value="gender"
            :label="gender"
            :checked="editItem?.gender?.includes(gender) || false"
            @input="updateSelectedGender"
          />
        </div> -->
        <!-- <InputTextArea v-model="urls" label="Image URL Array" class="w-full" /> -->
        <ButtonPrimary @click="submit">
          Submit
        </ButtonPrimary>
      </div>
      {{ totalActivePerGender }}
      <InputSelect v-model="filterGender" :options="['all', 'male', 'female']" label="Filter gender" />
      <InputSelect v-model="showInactive" :options="[true, false]" label="Show inactive" />
    </Card>
    <div class="grid w-full grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6">
      <template v-for="photo in filteredCatalog">
        <div :key="photo._id" v-masonry-tile class="group item w-full p-2" :style="`${photo?.active === false ? 'opacity:0.5' : ''}`">
          <ImageDns :src="photo.image" class="w-full border-[4px] border-white" @load="refreshMasonry()" />
          <div class="bg-white p-1 text-xs text-gray-700">
            <div>
              <span v-for="gender in photo.gender" :key="gender" :style="`color: ${(gender === 'male' ? 'blue' : 'pink')}`" class="pr-2">
                {{ gender }}
              </span>
              <div v-if="photo.usage?.total" class="flex items-center justify-start">
                {{ photo.usage?.favorite }}/
                {{ photo.usage?.total }} ->
                {{ (photo.usage?.conversionRate).toFixed(2) }}%
              </div>
              <div class=" space-x-2  hidden group-hover:flex">
                <ButtonDelete size="xs" @click="deleteItem(photo._id)">
                  Delete
                </ButtonDelete>
                <ButtonWhite size="sm" @click="edit(photo._id)">
                  Edit
                </ButtonWhite>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
    <PopupSlideIn v-if="editId" :title="editId" @closeModal="editId=null; editItem =null">
      <div class="space-y-4">
        <img :src="editItem?.image" class="w-full">
        <div class="flex space-x-2">
          <InputCheckbox
            v-for="gender in genderOptions"
            :key="gender"
            :value="gender"
            :label="gender"
            :checked="editItem?.gender?.includes(gender) || false"
            @input="updateGender"
          />
        </div>
        <InputSelect v-model="editItem.active" :options="[true, false]" label="Active" />
        <ButtonPrimary @click="saveEdit">
          Edit item
        </ButtonPrimary>
      </div>
    </PopupSlideIn>
  </div>
</template>

<script>
export default {
  layout: 'admin',
  data () {
    return {
      files: [],
      filterGender: 'all',
      selectedGender: ['male', 'female'],
      showInactive: false,
      catalog: [],
      tags: [],
      urls: null,
      editId: null,
      editItem: null,
      active: true,
      genderOptions: ['male', 'female']
    }
  },
  computed: {
    filteredCatalog () {
      const { filterGender, catalog, showInactive } = this
      let array = catalog

      if (filterGender !== 'all') {
        array = catalog.filter((item) => {
          return item.gender.includes(filterGender)
        })
      }

      if (showInactive === false || showInactive === 'false') {
        array = array.filter((item) => {
          return item.active !== false && item.active !== 'false'
        })
      }

      return array
    },
    totalActivePerGender () {
      const activeGenderCount = {
        male: 0,
        female: 0
      }
      this.catalog.forEach((item) => {
        const { gender, active } = item
        if (active !== false) {
          if (gender.length === 1) {
            activeGenderCount[gender] += 1
          } else {
            gender.forEach((g) => {
              activeGenderCount[g] += 1
            })
          }
        }
      })
      return activeGenderCount
    }
  },
  mounted () {
    this.fetch()
  },
  methods: {
    saveEdit () {
      const item = this.editItem
      this.$axios.put('/admin/catalog/' + item._id, item).then((response) => {
        this.$toast.success('Catalog updated successfully')
        this.editId = null
        this.editItem = null
        this.fetch()
      })
    },
    updateSelectedGender (gender) {
      let currentGenders = this.selectedGender || []
      if (currentGenders.includes(gender)) {
        currentGenders = currentGenders.filter(g => g !== gender)
      } else {
        currentGenders.push(gender)
      }
      this.selectedGender = currentGenders
    },
    updateGender (gender) {
      let currentGenders = this.editItem.gender || []
      if (currentGenders.includes(gender)) {
        currentGenders = currentGenders.filter(g => g !== gender)
      } else {
        currentGenders.push(gender)
      }
      this.editItem.gender = currentGenders
    },
    edit (id) {
      this.editId = id
      this.editItem = JSON.parse(JSON.stringify(this.catalog.find(photo => photo._id === id)))
      if (!this.editItem.gender) { this.editItem.gender = [] }
    },
    fetch () {
      this.$axios.$get('/admin/catalog').then((response) => {
        this.catalog = response
      })
    },
    onFileSelected (event) {
      this.files = event.target.files
    },
    async submit (event) {
      const formData = new FormData()
      if (this.files.length > 0) {
        for (let i = 0; i < this.files.length; i++) {
          formData.append('files[]', this.files[i])
        }
      } else {
        this.$toast.error('Please select a file or enter a url')
        return
      }

      const response = await this.$axios.$post('/admin/catalog', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      if (response && response.success) {
        this.$toast.success('Catalog uploaded successfully')
        this.selectedGender = this.genderOptions
      }
    },
    async deleteItem (id) {
      try {
        console.log('called')
        const { success, errorMessage } = await this.$axios.$delete(`/admin/catalog/${id}`)
        if (!success) {
          throw new Error(errorMessage)
        }
        this.catalog = this.catalog.filter(photo => photo._id !== id)
      } catch (err) {
        this.handleError(err)
      }
    }
  }
}
</script>

<style></style>
