<template>
  <div>
    <div class="grid" style="grid-template-columns: repeat(32, minmax(0, 1fr));">
      <ImageDns v-for="avatar in avatars" :key="avatar._id" :src="avatar.image" class="w-full" />
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      avatars: []
    }
  },
  async mounted () {
    const { data } = await this.$axios.$get('/model/avatars')
    this.avatars = data
  }

}
</script>

<style>

</style>
