<template>
  <div class="flex flex-col min-h-screen">
    <!-- <MarketingSalesBar v-if="!isLoggedIn" /> -->
    <Header />
    <main class="flex-1">
      <section class="py-12 bg-[#F8FCFF] sm:py-16 lg:py-20 xl:py-24">
        <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8 2xl:px-0">
          <div class="relative max-w-2xl mx-auto text-left md:text-center">
            <!-- <div class="flex items-center justify-start md:justify-center gap-x-3">
              <img class="w-auto h-6" src="@/assets/img/ratings-4.png" alt="">
              <img class="w-auto h-6" src="@/assets/img/logo-trustpilot.png" alt="">
            </div> -->
            <div class="flex flex-row  items-start md:items-center gap-3 w-full pb-2">
              <TrustpilotRating class="md:mx-auto" />
            </div>
            <h2
              class="mt-3 text-2xl font-bold tracking-[-1.05px] sm:text-3xl lg:text-4xl text-primary-500 max-w-xl mx-auto"
            >
              {{ $t('heroTitle') }}
            </h2>
            <p class="mt-3 text-base font-medium text-[#474368] sm:text-lg max-w-xl mx-auto">
              {{ $t('heroDescription') }}
              <a target="_blank" href="/blog/how-much-does-a-headshot-cost" class="underline">{{ $t('priceReference') }}</a>
            </p>

            <div class="absolute flex-col items-center hidden gap-1 top-12 -right-24 xl:flex">
              <p
                class="text-sm leading-4 rotate-[18deg] py-4 text-right font-cursive text-paragraph tracking-[-0.056px] max-w-[140px]"
              >
                {{ $t('researchNote') }}
              </p>
              <svg
                class="w-auto h-6 text-paragraph"
                viewBox="0 0 50 38"
                fill="currentColor"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M7.73613 31.9344C9.78808 33.6973 11.7607 35.4378 14.0258 37.3792C12.9909 37.9954 11.7571 37.608 10.7461 36.8492C7.71298 34.5728 4.66991 32.2147 1.75552 29.6559C0.085201 28.198 0.123864 26.6299 1.64443 26.2296C4.16052 25.5179 6.7758 24.9921 9.38111 24.3845C9.52971 24.3474 9.71296 24.2807 9.83685 24.3551C10.3225 24.5709 10.7833 24.8983 11.2789 25.1959C11.1701 25.56 11.2399 26.1324 10.9873 26.2585C10.1256 26.7261 9.20938 27.0599 8.33774 27.4458C7.86727 27.6684 7.38684 27.8093 7.05539 28.5449C7.97202 28.8428 8.93327 29.1928 9.8499 29.4907C28.7172 34.8107 43.321 26.8091 46.3967 9.63548C46.7767 7.41352 46.8744 5.07245 47.1306 2.77608C47.2242 1.97347 47.3625 1.22292 47.4561 0.420307C47.6493 0.435309 47.8525 0.532069 48.0457 0.547071C48.3432 1.1047 48.844 1.75908 48.869 2.27935C49.0435 4.02604 49.218 5.77273 49.1546 7.45236C48.5902 24.1893 36.9454 34.8016 20.0177 33.7789C16.7778 33.5759 13.3443 32.7262 10.02 32.1441C9.31644 32.0247 8.63763 31.7938 7.92417 31.5927C7.81024 31.6 7.75087 31.7412 7.73613 31.9344Z"
                />
              </svg>
            </div>
          </div>
          <LandingpageV2PricingTiers :recommended="$t('recommendedText')" class="mt-8" />
          <hr class="max-w-2xl mx-auto mt-6">

          <div class="mt-4 flex flex-col items-start md:items-center justify-start md:justify-center gap-4 sm:mt-8  sm:gap-4">
            <div class="flex flex-col items-start md:items-center gap-2">
              <p class="hidden md:flex -mt-0.5 text-sm font-normal text-[#474368]/80 gap-0.5">
                <span class="hidden md:inline-flex font-bold">18M</span>
                <span class="hidden md:inline-flex"> {{ $t('headshots created for') }}</span>
                <span class="font-bold">{{ $store.state.stats.users }}+</span>
                {{ $t('happy customers') }}
              </p>

              <div class="max-w-[100%] w-full md:w-[600px] mx-auto overflow-hidden relative">
                <div class="absolute inset-y-0 left-0 w-16 bg-gradient-to-r from-[#F8FCFF] md:from-[#F8FCFF] to-transparent z-10" />
                <div class="absolute inset-y-0 right-0 w-16 bg-gradient-to-l from-[#F8FCFF] md:from-[#F8FCFF] to-transparent z-10" />
                <img
                  src="@/assets/img/logo-cloud-horizontal.png"
                  class="w-[240%] max-w-[240%] grayscale opacity-60 animate-scroll"
                >
              </div>
            </div>
          </div>
          <div class="max-w-2xl mx-auto mt-8">
            <MarketingLogoCloud />
          </div>

          <div class="hidden md:grid md:grid-cols-3 gap-4 mt-8">
            <client-only>
              <LandingpageRandomTrustpilotReview v-for="i in 9" :key="i" />
            </client-only>
          </div>
        </div>
      </section>
      <LandingpageV2Faq />
      <!-- <MarketingRichTestimonials /> -->
      <MarketingCTAV2 :title="$t('ctaTitle')" />
      <MarketingFooter />
    </main>
  </div>
</template>

<script>
export default {
  head () {
    const { path } = this.$route
    const base = 'https://www.headshotpro.com'
    return {
      title: this.$t('seoTitle'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('seoDescription')
        }
      ],
      link: [
        { rel: 'canonical', href: base + this.localePath(path, 'en') },
        { rel: 'alternate', hreflang: 'en', href: base + this.localePath(path, 'en') },
        { rel: 'alternate', hreflang: 'de', href: base + this.localePath(path, 'de') },
        { rel: 'alternate', hreflang: 'x-default', href: base + this.localePath(path, 'en') }
      ]
    }
  },
  mounted () {
    this.$posthog.capture('$page:pricing')
  }
}
</script>

<i18n>
{
  "en": {
    "heroTitle": "Professional headshots for 8x less than a physical photo shoot",
    "heroDescription": "The average cost of professional headshots in the United States is",
    "priceReference": "$232.50 per session*.",
    "researchNote": "We've done the research for you",
    "ctaTitle": "Try Us Once, Use Your Headshots Forever",
    "recommendedText": "Perfect choice for you",
    "seoTitle": "Pricing of professional headshots | HeadshotPro",
    "seoDescription": "Professional AI headshots starting at $29. Get 120+ headshots in 2 hours. 8x cheaper than traditional photo shoots. Try HeadshotPro today!"
  },
  "de": {
    "heroTitle": "Bewerbungsfotos 8x günstiger als klassische Fotoshootings",
    "heroDescription": "Die durchschnittlichen Kosten für Bewerbungsfotos in den USA betragen",
    "priceReference": "232,50 $ pro Termin*.",
    "researchNote": "Recherche bereits für dich erledigt",
    "ctaTitle": "Einmal testen, Fotos für immer nutzen",
    "recommendedText": "Perfekte Wahl für dich",
    "seoTitle": "Preise für professionelle Bewerbungsfotos | HeadshotPro",
    "seoDescription": "Professionelle KI-Bewerbungsfotos ab 29 $. Erhalte 120+ Fotos in 2 Stunden. 8x günstiger als klassische Fotoshootings. Teste HeadshotPro jetzt!"
  }
}
</i18n>
