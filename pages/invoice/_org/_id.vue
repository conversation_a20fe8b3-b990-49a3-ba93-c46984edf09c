<template>
  <div class="w-full">
    <header class="print:hidden relative w-full bg-white border-b border-gray-200 px-4 py-3.5">
      <div class="max-w-7xl mx-auto px-4">
        <div class="flex items-center justify-center w-full">
          <img width="181" height="28" class="h-6 w-auto" src="@/assets/img/logo.svg" alt="HeadshotPro logo">
        </div>
      </div>
    </header>
    <div v-if="invoice" class="w-full p-4 md:p-8 lg:p-12 min-h-screen max-w-7xl mx-auto print:hidden">
      <AppTitle :title="((isQuote) ? 'Quote ' : 'Invoice ') + (invoice.id || invoice._id)" with-mobile-actions>
        <template #description>
          <div class="flex items-center gap-4 mt-2">
            <InvoiceStatusBadge :invoice="invoice" />
            <div class="flex items-center gap-2 text-sm text-slate-500 font-medium">
              <p>{{ formatPrice(invoice.amountTotal / 100, invoice.currency, 2, false) }}</p>
            </div>
          </div>
        </template>
        <nuxt-link :to="`/invoice/preview/?org=${$route.params.org}&id=${$route.params.id}`" target="_blank">
          <ButtonPrimary size="sm">
            View quote
          </ButtonPrimary>
        </nuxt-link>
        <ButtonWhite size="sm" @click="print">
          <IconDocumentArrowDown class="w-4 h-4 mr-2.5" />
          Export
        </ButtonWhite>
        <template #mobile>
          <ButtonWhite size="sm" class="w-full" @click="print">
            <IconDocumentArrowDown class="w-4 h-4 mr-2.5" />
            Export
          </ButtonWhite>
        </template>
      </AppTitle>
      <div class="flex flex-col lg:flex-row items-start gap-6 mt-6">
        <div class="w-full lg:order-last lg:sticky lg:top-0 lg:w-auto lg:min-w-[450px] space-y-6">
          <Card>
            <h2 class="text-lg font-bold text-primary-500">
              Billing information
            </h2>
            <p class="text-slate-500 font-medium">
              Add your billing data below to add it to the invoice.
            </p>
            <InputTextArea
              v-model="invoice.billingDetails"
              theme="v2"
              autocomplete="street-address"
              label="Bill to:"
              placeholder="i.e. 20 W 34th St., New York, NY 10001, United States"
              class="mt-4"
              :disabled="invoice.status !== 'invoiced' && invoice.status !== 'quoted'"
            />
          </Card>
          <template v-if="invoice.status === 'invoiced' || invoice.status === 'quoted'">
            <CheckoutPaymentMethodSelection
              :payment-providers="[{ ...paymentProviders[0], isPrimary: true }]"
              theme="button"
              @select="selectPaymentProvider"
            />
            <div class="border-ring-100 rounded-xl p-5 shadow-sm ring-1 bg-white ring-black/5 cursor-pointer hover:bg-gray-50 transition-colors flex justify-between items-center">
              <span class="block text-sm font-bold tracking-tight text-primary-500 sm:text-lg">Pay by bank transfer</span>
              <ButtonWhite size="sm" @click="payByTransfer">
                Pay now
              </ButtonWhite>
            </div>
          </template>
        </div>
        <Card class="lg:col-span-2 lg:aspect-[210/297] w-full">
          <Invoice :charge="invoice" :billing-information="invoice.billingDetails" :is-organization-invoice="true" />
        </Card>
      </div>
    </div>
    <div class="hidden print:block">
      <Invoice :charge="invoice" :billing-information="invoice.billingDetails" :is-organization-invoice="true" :payment-url="`/invoice/${$route.params.org}/${$route.params.id}`" />
    </div>
    <Modal v-if="showPayByTransfer" max-width="sm:max-w-5xl" @close="showPayByTransfer = false">
      <div class="px-4 py-5 sm:p-6">
        <div class="space-y-1">
          <p class="text-lg font-bold tracking-tight text-primary-500">
            Rather pay by bank transfer?
          </p>
          <p class="text-sm text-paragraph">
            You may transfer to any of these bank accounts. After payment, please do inform our team via email at <a href="mailto:<EMAIL>" class="text-primary-500 underline"><EMAIL></a>.
          </p>
        </div>
        <div class="mt-12 grid gap-4 grid-cols-1 md:gap-8 md:grid-cols-2 lg:grid-cols-3 justify-center">
          <div class="p-4 pt-5 border border-gray-200 rounded-lg relative">
            <p class="text-sm font-bold flex items-center justify-center text-primary-500 gap-2 absolute top-0 left-1/2 -translate-y-1/2 -translate-x-1/2 px-2 py-1 bg-teal-300 rounded-full">
              <svg class="w-4 h-4 text-primary-500" fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 70 117">
                <path d="M31.682 116.636V.273h7.454v116.363h-7.454ZM55 36.364c-.485-4.303-2.485-7.637-6-10-3.515-2.394-7.94-3.591-13.273-3.591-3.818 0-7.12.606-9.909 1.818-2.788 1.182-4.954 2.818-6.5 4.909-1.515 2.06-2.273 4.41-2.273 7.045 0 2.213.516 4.122 1.546 5.728 1.06 1.606 2.44 2.954 4.136 4.045A31.254 31.254 0 0 0 28.273 49a62.576 62.576 0 0 0 5.682 1.727l9.09 2.364c2.97.727 6.016 1.712 9.137 2.954a36.623 36.623 0 0 1 8.682 4.91c2.666 2.03 4.818 4.545 6.454 7.545 1.667 3 2.5 6.59 2.5 10.773 0 5.273-1.364 9.954-4.09 14.045-2.698 4.091-6.622 7.318-11.773 9.682-5.122 2.364-11.319 3.545-18.591 3.545-6.97 0-13-1.106-18.091-3.318-5.091-2.212-9.076-5.348-11.955-9.409-2.879-4.09-4.47-8.94-4.773-14.545h14.091c.273 3.363 1.364 6.166 3.273 8.409 1.94 2.212 4.41 3.864 7.41 4.954 3.03 1.061 6.348 1.591 9.954 1.591 3.97 0 7.5-.62 10.59-1.863 3.122-1.273 5.576-3.03 7.364-5.273 1.788-2.273 2.682-4.924 2.682-7.955 0-2.757-.788-5.015-2.364-6.772C52 70.606 49.894 69.15 47.227 68c-2.636-1.151-5.62-2.167-8.954-3.046l-11-3C19.818 59.925 13.909 56.94 9.545 53c-4.333-3.94-6.5-9.151-6.5-15.636C3.045 32 4.5 27.318 7.41 23.318c2.91-4 6.849-7.106 11.818-9.318 4.97-2.242 10.576-3.364 16.818-3.364 6.303 0 11.864 1.106 16.682 3.319 4.849 2.212 8.667 5.257 11.455 9.136 2.788 3.848 4.242 8.273 4.364 13.273H55Z" fill="currentColor" />
              </svg>
              <span>SGD</span>
            </p>
            <ul class="text-sm text-paragraph divide-y divide-gray-100">
              <li class="flex items-start justify-start gap-2 py-3">
                <IconSmallGlobe class="w-4 h-4 text-gray-400 flex-shrink-0 translate-y-0.5" />
                Payment network: FAST
              </li>
              <li class="flex items-start justify-start gap-2 py-3">
                <IconBuildingOffice class="w-4 h-4 text-gray-400 flex-shrink-0 translate-y-0.5" />
                Account holder: Headshot Pro Photography Pte. Ltd.
              </li>
              <li class="flex items-start justify-start gap-2 py-3">
                <IconPencilSquare class="w-4 h-4 text-gray-400 flex-shrink-0 translate-y-0.5" />
                Bank name: Wise Asia-Pacific Pte. Ltd. (Formerly TransferWise)
              </li>
              <li class="flex items-start justify-start gap-2 py-3">
                <IconHashtag class="w-4 h-4 text-gray-400 flex-shrink-0 translate-y-0.5" />
                Bank code: 0516
              </li>
              <li class="flex items-start justify-start gap-2 py-3">
                <IconBanknote class="w-4 h-4 text-gray-400 flex-shrink-0 translate-y-0.5" />
                Account Nº: ********
              </li>
              <li class="flex items-start justify-start gap-2 py-3">
                <IconMapPin class="w-4 h-4 text-gray-400 flex-shrink-0 translate-y-0.5" />
                Address: 1 Paya Lebar Link #13-06 - #13-08 PLQ 2, Paya Lebar Quarter, Singapore, 408533, Singapore
              </li>
            </ul>
          </div>
          <div class="p-4 pt-5 border border-gray-200 rounded-lg relative">
            <p class="text-sm font-bold flex items-center justify-center text-primary-500 gap-2 absolute top-0 left-1/2 -translate-y-1/2 -translate-x-1/2 px-2 py-1 bg-teal-300 rounded-full">
              <svg class="w-4 h-4 text-primary-500" fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 70 117">
                <path d="M31.682 116.636V.273h7.454v116.363h-7.454ZM55 36.364c-.485-4.303-2.485-7.637-6-10-3.515-2.394-7.94-3.591-13.273-3.591-3.818 0-7.12.606-9.909 1.818-2.788 1.182-4.954 2.818-6.5 4.909-1.515 2.06-2.273 4.41-2.273 7.045 0 2.213.516 4.122 1.546 5.728 1.06 1.606 2.44 2.954 4.136 4.045A31.254 31.254 0 0 0 28.273 49a62.576 62.576 0 0 0 5.682 1.727l9.09 2.364c2.97.727 6.016 1.712 9.137 2.954a36.623 36.623 0 0 1 8.682 4.91c2.666 2.03 4.818 4.545 6.454 7.545 1.667 3 2.5 6.59 2.5 10.773 0 5.273-1.364 9.954-4.09 14.045-2.698 4.091-6.622 7.318-11.773 9.682-5.122 2.364-11.319 3.545-18.591 3.545-6.97 0-13-1.106-18.091-3.318-5.091-2.212-9.076-5.348-11.955-9.409-2.879-4.09-4.47-8.94-4.773-14.545h14.091c.273 3.363 1.364 6.166 3.273 8.409 1.94 2.212 4.41 3.864 7.41 4.954 3.03 1.061 6.348 1.591 9.954 1.591 3.97 0 7.5-.62 10.59-1.863 3.122-1.273 5.576-3.03 7.364-5.273 1.788-2.273 2.682-4.924 2.682-7.955 0-2.757-.788-5.015-2.364-6.772C52 70.606 49.894 69.15 47.227 68c-2.636-1.151-5.62-2.167-8.954-3.046l-11-3C19.818 59.925 13.909 56.94 9.545 53c-4.333-3.94-6.5-9.151-6.5-15.636C3.045 32 4.5 27.318 7.41 23.318c2.91-4 6.849-7.106 11.818-9.318 4.97-2.242 10.576-3.364 16.818-3.364 6.303 0 11.864 1.106 16.682 3.319 4.849 2.212 8.667 5.257 11.455 9.136 2.788 3.848 4.242 8.273 4.364 13.273H55Z" fill="currentColor" />
              </svg>
              <span>USD</span>
            </p>
            <ul class="text-sm text-paragraph divide-y divide-gray-100">
              <li class="flex items-start justify-start gap-2 py-3">
                <IconBuildingOffice class="w-4 h-4 text-gray-400 flex-shrink-0 translate-y-0.5" />
                Account holder: Headshot Pro Photography Pte. Ltd.
              </li>
              <li class="flex items-start justify-start gap-2 py-3">
                <IconPencilSquare class="w-4 h-4 text-gray-400 flex-shrink-0 translate-y-0.5" />
                Bank name: Wise US Inc
              </li>
              <li class="flex items-start justify-start gap-2 py-3">
                <IconHashtag class="w-4 h-4 text-gray-400 flex-shrink-0 translate-y-0.5" />
                ACH and Wire RN: *********
              </li>
              <li class="flex items-start justify-start gap-2 py-3">
                <IconBanknote class="w-4 h-4 text-gray-400 flex-shrink-0 translate-y-0.5" />
                Account Nº: ***************
              </li>
              <li class="flex items-start justify-start gap-2 py-3">
                <IconSmallTag class="w-4 h-4 text-gray-400 flex-shrink-0 translate-y-0.5" />
                Account type: Checking
              </li>
              <li class="flex items-start justify-start gap-2 py-3">
                <IconMapPin class="w-4 h-4 text-gray-400 flex-shrink-0 translate-y-0.5" />
                Address: 30 W. 26th Street, Sixth Floor, New York, NY, 10010, United States
              </li>
            </ul>
          </div>
          <div class="p-4 pt-5 border border-gray-200 rounded-lg relative">
            <p class="text-sm font-bold flex items-center justify-center text-primary-500 gap-2 absolute top-0 left-1/2 -translate-y-1/2 -translate-x-1/2 px-2 py-1 bg-teal-300 rounded-full">
              <svg class="w-4 h-4 text-primary-500" viewBox="0 0 495 512" xmlns="http://www.w3.org/2000/svg" fill="currentColor"><path d="M292.984 512c-104.533 0-185.605-73.247-220.45-164.268H0l24.887-61.156h31.29a274.403 274.403 0 01-2.137-34.132H4.97l24.891-61.156h31.29C84.614 90.308 168.528 0 297.243 0c89.605 0 159.294 44.798 191.292 110.225L398.93 172.8c-20.62-53.33-67.556-68.98-104.534-68.98-58.314 0-95.287 39.115-111.65 87.471h161.428l-24.891 61.156h-146.49c0 13.512.713 24.182 2.137 34.132h147.911l-24.886 61.156h-100.98c21.333 36.27 55.465 60.447 98.846 60.447 38.399 0 84.618-19.199 103.109-69.684l96.004 56.176C465.077 469.331 372.631 512 292.984 512z" fillRule="evenodd" /></svg>
              <span>EUR</span>
            </p>
            <ul class="text-sm text-paragraph divide-y divide-gray-100">
              <li class="flex items-start justify-start gap-2 py-3">
                <IconBuildingOffice class="w-4 h-4 text-gray-400 flex-shrink-0 translate-y-0.5" />
                Account holder: POSTCRAFTS PTE. LTD.
              </li>
              <li class="flex items-start justify-start gap-2 py-3">
                <IconPencilSquare class="w-4 h-4 text-gray-400 flex-shrink-0 translate-y-0.5" />
                Bank name: Wise
              </li>
              <li class="flex items-start justify-start gap-2 py-3">
                <IconHashtag class="w-4 h-4 text-gray-400 flex-shrink-0 translate-y-0.5" />
                BIC: TRWIBEB1XXX
              </li>
              <li class="flex items-start justify-start gap-2 py-3">
                <IconBanknote class="w-4 h-4 text-gray-400 flex-shrink-0 translate-y-0.5" />
                IBAN: ****************
              </li>
              <li class="flex items-start justify-start gap-2 py-3">
                <IconMapPin class="w-4 h-4 text-gray-400 flex-shrink-0 translate-y-0.5" />
                Address: Rue du Trône 100, 3rd floor, Brussels, 1050, Belgium
              </li>
            </ul>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
import CheckoutMixin from '@/mixins/CheckoutMixin'

export default {
  mixins: [CheckoutMixin],
  layout: 'empty',
  async asyncData ({ params, $axios }) {
    const invoice = await $axios.$get(`/invoices/${params.org}/${params.id}`)
    return { invoice }
  },
  data () {
    return {
      showPayByTransfer: false
    }
  },
  computed: {
    isQuote () {
      return ['invoiced', 'void', 'quoted'].includes(this?.invoice?.status)
    }
  },
  methods: {
    selectPaymentProvider (paymentProvider) {
      this.toInvoiceCheckout()
    },
    print () {
      window.print()
    },
    async toInvoiceCheckout () {
      if (this.isPaying) {
        return
      }

      try {
        this.$loading.show({
          title: 'Redirecting to checkout'
        })

        this.trackCheckoutStartEvent()
        this.isPaying = true

        const { success, data, errorMessage } = await this.$axios.$post('/checkout/stripe/create-checkout-session-team-invoice', {
          organizationId: this.invoice.organization._id,
          invoiceId: this.invoice._id,
          currency: this.invoice.currency
        })
        if (!success) {
          throw new Error(errorMessage)
        }
        this.$facebook.trackEvent('InitiateCheckout')
        this.$posthog.capture('$funnel:checkout_started', {
          provider: 'stripe',
          product: 'package',
          team: true
        })
        window.location.href = data.url
      } catch (err) {
        this.handleError(err)
      } finally {
        this.isPaying = false
        this.$loading.hide()
      }
    },
    payByTransfer () {
      this.showPayByTransfer = true
    }
  }
}

</script>
