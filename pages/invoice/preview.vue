<!-- QuoteDocument.vue  -->
<template>
  <div class="flex flex-col min-h-screen  text-gray-900 font-sans max-w-4xl mx-auto space-y-8 print:space-y-0  print:bg-white !bg-[#E5E7EB] py-8 print:py-0">
    <!-- Print button-->
    <button class="print:hidden bg-primary-500 flex items-center justify-center w-14 h-14 rounded-full fixed bottom-4 right-4 hover:bg-primary-600 transition-colors duration-200 hover:scale-110 cursor-pointer" @click="print">
      <IconSolidPrint class="w-5 h-5 text-white" />
    </button>

    <!-- Edit button (only show for quoted invoices and quote owners) -->
    <button
      v-if="canEdit && !isEditMode"
      class="print:hidden bg-blue-500 flex items-center justify-center w-14 h-14 rounded-full fixed bottom-4 right-20 hover:bg-blue-600 transition-colors duration-200 hover:scale-110 cursor-pointer shadow-lg"
      title="Edit Quote Information"
      @click="toggleEditMode"
    >
      <IconPencilSquare class="w-5 h-5 text-white" />
    </button>

    <!-- Chat/Sales button -->
    <button
      class="print:hidden bg-green-500 flex items-center justify-center w-14 h-14 rounded-full fixed bottom-4 right-36 hover:bg-green-600 transition-colors duration-200 hover:scale-110 cursor-pointer shadow-lg"
      title="Contact Sales Team"
      @click="showSalesModal = true"
    >
      <IconSolidChat class="w-5 h-5 text-white" />
    </button>

    <!-- Loading indicator while checking authentication -->
    <div
      v-if="isAuthLoading && invoice?.status === 'quoted'"
      class="print:hidden bg-gray-400 flex items-center justify-center w-14 h-14 rounded-full fixed bottom-4 right-20 shadow-lg"
      title="Checking permissions..."
    >
      <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white" />
    </div>

    <!-- Save/Cancel buttons in edit mode -->
    <div v-if="isEditMode" class="print:hidden fixed bottom-4 right-4 flex gap-3">
      <button
        class="bg-green-500 flex items-center justify-center w-14 h-14 rounded-full hover:bg-green-600 transition-colors duration-200 hover:scale-110 cursor-pointer shadow-lg"
        :disabled="isSaving"
        title="Save Changes"
        @click="saveChanges"
      >
        <IconCheck class="w-5 h-5 text-white" />
      </button>
      <button
        class="bg-gray-500 flex items-center justify-center w-14 h-14 rounded-full hover:bg-gray-600 transition-colors duration-200 hover:scale-110 cursor-pointer shadow-lg"
        title="Cancel"
        @click="cancelEdit"
      >
        <IconMiniXMark class="w-5 h-5 text-white" />
      </button>
    </div>

    <!-- Sales Modal -->
    <MarketingSalesModal v-if="showSalesModal" @close="showSalesModal = false" />

    <!-- Edit Form Modal -->
    <Modal
      v-if="isEditMode"
      :show-close-button="false"
      max-width="max-w-md w-full mx-4"
      class="print:hidden"
      @close="cancelEdit"
    >
      <div class="p-6">
        <h3 class="text-lg font-bold mb-4">
          Edit Quote Information
        </h3>

        <div class="space-y-4">
          <!-- Company Name -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Company Name</label>
            <input
              v-model="editData.meta.companyName"
              type="text"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="Company name"
            >
          </div>

          <!-- First Name -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
            <input
              v-model="editData.meta.firstName"
              type="text"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="First name"
            >
          </div>

          <!-- Last Name -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
            <input
              v-model="editData.meta.lastName"
              type="text"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="Last name"
            >
          </div>

          <!-- Email -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
            <input
              v-model="editData.meta.email"
              type="email"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="Email address"
            >
          </div>

          <!-- Website -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Website</label>
            <input
              v-model="editData.organization.website"
              type="url"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="Company website"
            >
          </div>

          <!-- Billing Address -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Billing Address</label>
            <textarea
              v-model="editData.billingDetails"
              rows="3"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="Billing address"
            />
          </div>
        </div>

        <div class="flex gap-2 mt-6">
          <button
            :disabled="isSaving"
            class="flex-1 bg-primary-500 text-white py-2 px-4 rounded-md hover:bg-primary-600 transition-colors duration-200 disabled:opacity-50"
            @click="saveChanges"
          >
            {{ isSaving ? 'Saving...' : 'Save Changes' }}
          </button>
          <button
            class="flex-1 bg-gray-500 text-white py-2 px-4 rounded-md hover:bg-gray-600 transition-colors duration-200"
            @click="cancelEdit"
          >
            Cancel
          </button>
        </div>
      </div>
    </Modal>

    <div class="flex flex-col min-h-screen bg-white">
      <!-- Top Section (White Background) -->
      <div class="flex-grow px-16 py-8">
        <!-- Header Row -->
        <div class="flex justify-between items-start mb-12">
          <p class="text-lg font-medium">
            Get uniform headshots for your entire team
          </p>
          <img src="@/assets/img/logo.svg" alt="HeadshotPro Logo" class="h-6">
        </div>

        <!-- Main Content -->
        <div class="flex items-start flex-col justify-start mt-16 space-y-8">
          <!-- <div class="w-[300px]">
            <div class="w-full bg-gray-200 flex items-center justify-center text-gray-500">
              <img src="@/assets/img/quote/cover.png" alt="HeadshotPro Graphic" class="w-full">
            </div>
          </div> -->
          <div class="w-full">
            <h1 class="text-5xl font-bold mb-2 text-gray-800">
              HeadshotPro Quote
            </h1>
            <h2 class="text-xl text-primary-500 font-medium">
              Virtual Headshot Session
            </h2>
          </div>
        </div>
      </div>

      <!-- Separator -->
      <div class="h-2 bg-primary-500 w-full" />

      <!-- Bottom Section (Dark Background) -->
      <div class="bg-muted-50 text-gray-800 px-16 py-10 text-sm print:bg-muted-50 print:text-gray-800 print:color-adjust-exact">
        <div class="grid grid-cols-2 gap-8">
          <!-- Prepared For -->
          <div>
            <h3 class="text-xs uppercase text-gray-400 mb-2 font-semibold tracking-wide">
              Prepared For
            </h3>
            <p class="font-semibold text-primary-500 mb-1">
              {{ quote.client.company }}
            </p>
            <p v-if="quote.client.firstName" class="mt-4 text-gray-800">
              {{ quote.client.firstName }} {{ quote.client.lastName }}
            </p>
            <div class="mt-4 text-gray-800">
              <p v-for="(line, index) in formattedClientAddressLines" :key="index">
                {{ line }}
              </p>
            </div>
            <a :href="quote.client.website" target="_blank" class="text-gray-800 hover:text-white mt-1 block">
              {{ quote.client.website }}
            </a>
          </div>

          <!-- Prepared By -->
          <div>
            <h3 class="text-xs uppercase text-gray-400 mb-2 font-semibold tracking-wide">
              Prepared By
            </h3>
            <p class="font-semibold text-primary-500 mb-1">
              {{ quote.vendor.name }}
            </p>
            <p class="text-gray-800">
              {{ quote.vendor.title }}
            </p>
            <p class="text-gray-800">
              {{ quote.vendor.company }}
            </p>
            <p class="text-gray-800 leading-snug">
              {{ quote.vendor.address }}
            </p>
            <p class="text-gray-800 leading-snug">
              {{ quote.vendor.companyId }}
            </p>
            <p class="text-gray-800">
              E: {{ quote.vendor.email }}
            </p>
            <!-- Assuming vendor website comes from data if needed -->
            <a href="https://www.headshotpro.com" target="_blank" class="text-gray-800 hover:text-white mt-1 block">
              https://www.headshotpro.com
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Page 2 -->
    <div class="flex min-h-screen h-full bg-white text-gray-900 page-break">
      <div class="w-3/4 mx-auto py-16 text-left space-y-4">
        <H2>Your HeadshotPro Quote</H2>
        <Paragraph>
          At HeadshotPro, we specialize in providing high-quality, consistent headshots for entire teams using cutting-edge AI technology, streamlining a process that traditionally takes days into just a few hours.
        </Paragraph>
        <Paragraph size="md" class="!leading-[24px]">
          <span v-if="quote.client.firstName">
            Dear {{ quote.client.firstName }},
          </span>
          <br><br>
          On behalf of HeadshotPro, I'm pleased to provide you with this quote for our AI-powered virtual headshot solution. We understand the challenge of getting professional, uniform headshots for your entire team, especially across different locations.
          <br><br>
          Our service is designed to solve exactly that. We deliver over 80 unique headshot options per team member in under 2 hours, eliminating the cost and logistical hassle of traditional photoshoots. You get studio-quality, consistent results that elevate your company's brand image, all managed through a simple online process.
          <br><br>
          This quote details the specifics for {{ quote.client.company }}. We are confident that HeadshotPro offers a significantly faster, more affordable, and convenient way to achieve the professional look you need.
          <br><br>
          We're excited about the possibility of working with you and helping {{ quote.client.company }} put its best face forward.
          <br><br>
          With kind regards,
        </Paragraph>
        <img src="@/assets/img/signature-danny.png" alt="Danny Postma" class="w-24">
        <Paragraph size="md" class="!leading-[24px]">
          <strong>{{ quote.vendor.name }}</strong>
          <br>{{ quote.vendor.title }}
        </Paragraph>
      </div>
    </div>

    <!-- Page 3 -->
    <div class="flex min-h-screen h-full bg-white text-gray-900 page-break">
      <div class="w-3/4 mx-auto py-32 text-left space-y-4">
        <H2>Make studio quality headshots the new standard at your company.</H2>
        <Paragraph size="large">
          Outfit your entire team with professional, matching headshots generated completely online. Customize backgrounds, outfits, and branding for perfect consistency without the hassle.
        </Paragraph>
        <div class="flex flex-col gap-4">
          <IconListItem
            icon="IconMiniCheckBadge"
            title="On-Brand, Every Time"
            description="Automatically apply your company branding to profile pictures for 1:1 consistency."
            color="#6AC9E0"
          />
          <IconListItem
            icon="IconMiniPhoto"
            title="Ready-to-Use Results"
            description="Receive 80 high-resolution headshots and 80 branded PFPs per team member."
            color="#64BAE9"
          />

          <div class="mt-8">
            <H4>Secure & ethical AI headshots you can trust</H4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 print:grid-cols-2 gap-4 mt-4">
              <Card v-for="item in security" :key="item.title">
                <div class="flex flex-col space-y-[12px]">
                  <IconSquare :icon="item.icon" :color="item.color" />
                  <H6>{{ item.title }}</H6>
                  <Paragraph size="sm">
                    {{ item.description }}
                  </Paragraph>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Page 4 -->
    <div class="flex min-h-screen h-full bg-white text-gray-900 page-break">
      <div class="w-3/4 mx-auto py-32 text-left space-y-4">
        <H2>Package Quote</H2>
        <Paragraph size="lg">
          This quote includes:
        </Paragraph>

        <Table :head="['Description', 'Qty', 'Unit price', 'Amount']" class="mt-8 w-full overflow-x-auto" :sticky="false">
          <TableRow v-for="item in invoice.items" :key="item.id">
            <TableItem>{{ item.name }}</TableItem>
            <TableItem>{{ item.quantity }}</TableItem>
            <TableItem>{{ formatPrice(item.amount / 100, invoice.currency,2,false) }}</TableItem>
            <TableItem class="text-right">
              {{ formatPrice(item.amount * item.quantity / 100, invoice.currency,2,false) }}
            </TableItem>
          </TableRow>
          <TableRow v-if="invoice.items.length > 1">
            <TableItem />
            <TableItem />
            <TableItem>Subtotal</TableItem>
            <TableItem class="text-right">
              {{ formatPrice(totalCost / 100, invoice.currency,2,false) }}
            </TableItem>
          </TableRow>
          <TableRow v-if="totalDiscount > 0">
            <TableItem />
            <TableItem />
            <TableItem>Discount</TableItem>
            <TableItem class="text-green-600 text-right">
              -{{ formatPrice(totalDiscount / 100, invoice.currency,2,false) }}
            </TableItem>
          </TableRow>
          <TableRow>
            <TableItem />
            <TableItem />
            <TableItem>VAT (0%)</TableItem>
            <TableItem class="text-right">
              {{ formatPrice(0, invoice.currency, 2, false) }}
            </TableItem>
          </TableRow>
          <TableRow v-if="invoice.refunded">
            <TableItem />
            <TableItem />
            <TableItem>Refund</TableItem>
            <TableItem class="text-right">
              <template v-if="invoice.amountRefunded > 0">
                -{{ formatPrice(invoice.amountRefunded / 100, invoice.currency,2,false) }}
              </template>
              <template v-else>
                -{{ formatPrice(amountCaptured / 100, invoice.currency,2,false) }}
              </template>
            </TableItem>
          </TableRow>
          <TableRow>
            <TableItem />
            <TableItem />
            <template v-if="!isQuote">
              <TableItem class="font-bold text-primary-500">
                Amount paid
              </TableItem>
              <TableItem class="font-bold text-primary-500 text-right">
                {{ formatPrice(subtotal / 100, invoice.currency, 2, false) }}
              </TableItem>
            </template>
            <template v-else>
              <TableItem class="font-bold text-primary-500">
                Amount quoted
              </TableItem>
              <TableItem class="font-bold text-primary-500 text-right">
                {{ formatPrice(amountCaptured / 100, invoice.currency, 2, false) }}
              </TableItem>
            </template>
          </TableRow>
        </Table>
        <H5>Additional terms agreed upon</H5>
        <Paragraph v-for="term in quote.terms" :key="term" size="sm">
          <strong>{{ term.title }}</strong>
          <br>
          {{ term.description }}
        </Paragraph>
      </div>
    </div>

    <!-- Page 5 -->
    <div class="flex min-h-screen h-full bg-white text-gray-900 page-break">
      <div class="w-2/3 mx-auto py-32 text-left space-y-4">
        <H2>Acceptance of Quote</H2>
        <Paragraph v-if="quote.client.firstName" size="md">
          Dear {{ quote.client.firstName }},
        </Paragraph>
        <Paragraph size="md">
          We are happy to have made this quotation on your
          request. Based on our contact we have discussed
          the following:
        </Paragraph>
        <Paragraph size="md">
          <ul>
            <li v-for="item in invoice.items" :key="item.id">
              <strong>• {{ item.quantity }}x {{ item.name }}</strong>
            </li>
          </ul>
        </Paragraph>
        <Paragraph size="md">
          The total discounted price for the above services is {{ formatPrice(quote.total, 'USD', 2, false) }}, payable in advance. This is a one-time payment.
        </Paragraph>
        <Paragraph size="md">
          This quote is governed by our Master Services Agreement, Data Management and Retention Policy and Terms and Conditions
        </Paragraph>
        <Paragraph size="md">
          <div class="flex flex-col gap-2">
            <a href="/legal/terms-and-conditions" target="_blank" class="text-sky-500">
              Terms and Conditions
            </a>
            <a href="/legal/data-management-retention" target="_blank" class="text-sky-500">
              Data Management and Retention Policy
            </a>
            <a href="/legal/master-services-agreement" target="_blank" class="text-sky-500">
              Master Services Agreement
            </a>
          </div>
        </Paragraph>
        <Paragraph size="md">
          To accept this quote and proceed with the service, please complete the payment via the invoice link provided separately. Payment constitutes acceptance of this quote and the associated terms. {{ quote.client.company }} allows HeadshotPro to use their logo for promotional purposes.
        </Paragraph>
        <a :href="`https://www.headshotpro.com/invoice/${$route.query.org}/${$route.query.id}`" target="_blank" class="button hover:bg-brand-800 focus:ring-brand-200 flex flex-shrink-0 content-center items-center justify-center rounded-md border border-transparent bg-brand-500 px-6 py-3 text-center text-base font-medium text-white shadow-sm transition duration-100 focus:outline-none focus:ring-2 focus:ring-offset-2">
          Accept quote & pay invoice
        </a>
        <!-- <TrustpilotRatingSmall /> -->
        <Paragraph size="xxs">
          Copy this link if the button above doesn't work: https://www.headshotpro.com/invoice/{{ $route.query.org }}/{{ $route.query.id }}
        </Paragraph>
      </div>
    </div>
  </div>
</template>

<script>
import H6 from '@/components/landingpage/common/H6.vue'
import H5 from '@/components/landingpage/common/H5.vue'
import H4 from '@/components/landingpage/common/H4.vue'
// import H3 from '@/components/landingpage/common/H3.vue'
import H2 from '@/components/landingpage/common/H2.vue'
// import H1 from '@/components/landingpage/common/H1.vue'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'
import IconListItem from '@/components/landingpage/common/IconListItem.vue'
import IconSquare from '@/components/landingpage/common/IconSquare.vue'

export default {
  components: {
    H5,
    H4,
    // H3,
    H2,
    // H1,
    Paragraph,
    IconListItem,
    IconSquare,
    H6
  },
  layout: 'empty',
  async asyncData ({ query, $axios }) {
    const invoice = await $axios.$get(`/invoices/${query.org}/${query.id}`)
    return { invoice }
  },
  data () {
    return {
      invoice: null,
      unsubscribeAuthChange: () => {},
      isAuthLoading: true,
      security: [
        {
          title: 'SOC2 Type II compliant',
          description: 'Our data handling systems and processes are independently audited and certified.',
          icon: 'IconMiniShield',
          color: '#73D9D8'
        },
        {
          title: 'Content moderation',
          description: 'We use a combination of human AI moderation systems to safeguard our output quality from bias and NSFW.',
          icon: 'IconMiniEye',
          color: '#6AC9E0'
        },
        {
          title: 'Data encryption and deletion',
          description: 'All data is encrypted at rest and in transit. Personal data is deleted after 30 days and after generation.',
          icon: 'IconMiniLock',
          color: '#64BAE9'
        },
        {
          title: 'Your data stays yours',
          description: 'Your data is only used to create your headshots and is never used to train any other models.',
          icon: 'IconMiniServer',
          color: '#64BAE9'
        }
      ],
      isEditMode: false,
      editData: {
        meta: {
          companyName: '',
          firstName: '',
          lastName: '',
          email: ''
        },
        organization: {
          website: ''
        },
        billingDetails: ''
      },
      originalData: null,
      isSaving: false,
      showSalesModal: false
    }
  },
  head () {
    const createdAt = new Date(this.invoice.createdAt)
    const year = createdAt.getFullYear()
    const month = (createdAt.getMonth() + 1).toString().padStart(2, '0') // Months are 0-indexed
    const day = createdAt.getDate().toString().padStart(2, '0')
    const formattedDate = `${year}-${month}-${day}`
    const companyName = this.invoice?.organization?.name || 'HeadshotPro' // Fallback if name is missing

    return {
      title: `${formattedDate} HeadshotPro QUOTE for ${companyName}`,
      meta: [
        { name: 'description', content: `Quote for ${companyName}` }
      ]
    }
  },
  computed: {
    formattedClientAddressLines () {
      if (!this.quote?.client?.address) {
        return []
      }
      // Split by comma or newline, trim whitespace, and filter out empty lines
      return this.quote.client.address
        .split(/[,\n]+/)
        .map(line => line.trim())
        .filter(line => line)
    },
    totalDiscount () {
      let discountFromInvoice = 0
      if (this.invoice?.discount) {
        if (this.invoice.discount.type === 'percentage') {
          // NOTE: Original logic calculates percentage based on amountCaptured, which might be unexpected.
          // Keeping it as is for the 'discountFromInvoice' part as per current implementation.
          // A potentially more common calculation would be: this.totalCost * (this.invoice.discount.amount / 100)
          discountFromInvoice = this.amountCaptured * (this.invoice.discount.amount / 100)
        } else if (this.invoice.discount.type === 'fixed' || this.invoice.discount.type === 'amount') {
          discountFromInvoice = this.invoice.discount.amount // Assuming this is in cents
        }
      }

      // Calculate discount based on the difference between total cost and final captured amount
      const discountFromDifference = this.totalCost - this.amountCaptured

      // Return the maximum of the two calculated discounts, ensuring it's not negative
      return Math.max(0, discountFromInvoice, discountFromDifference)
    },
    totalCost () {
      let totalCost = 0
      this.invoice.items.forEach((item) => {
        totalCost += item.amount * item.quantity
      })
      return totalCost
    },
    subtotal () {
      if (this.invoice.refunded) {
        if (this.invoice.amountRefunded > 0) {
          return Math.max(0, this.amountCaptured - this.invoice.amountRefunded)
        } else {
          return 0
        }
      }

      return this.amountCaptured
    },
    amountCaptured () {
      return this.invoice?.amountCaptured || this.invoice.amountTotal
    },
    quote () {
      // Calculate validUntil date
      const createdAtDate = new Date(this.invoice.createdAt)
      const validUntilDate = new Date(createdAtDate)
      validUntilDate.setDate(createdAtDate.getDate() + 30)

      return {
        id: this.invoice._id,
        issuedAt: this.invoice.createdAt,
        validUntil: validUntilDate,
        vendor: {
          name: 'Danny Postma',
          title: 'Founder & CEO',
          company: 'Headshot Pro Photography Pte. Ltd.',
          address: '7 Temasek Boulevard, #12-07, Suntec Tower One, Singapore 038987',
          companyId: '202450176D',
          email: '<EMAIL>',
          website: 'https://www.headshotpro.com'
        },
        client: {
          company: this.invoice?.meta?.companyName || this.invoice.organization.name,
          firstName: this.invoice?.meta?.firstName || null,
          lastName: this.invoice?.meta?.lastName || null,
          address: this.invoice.billingDetails,
          website: this.invoice.organization.website
        },
        terms: [
          { title: 'Renewal Price Guarantee', description: 'You will receive a forever discount based on the amount of seats you purchase on your organisation. This includes repurchases.' },
          { title: 'Payment Terms', description: 'Payment of the invoice in full is required to receive credits applied to your account, regardless of the invoice\'s due date.' }
        ],
        discount: this.invoice.price.discount,
        total: this.invoice.amountTotal / 100
      }
    },
    isAuthenticated () {
      return !this.isAuthLoading && this.$store.state.user && this.$store.state.user.uid
    },
    canEdit () {
      // Only allow editing for quoted invoices and if user is authenticated
      if (this.invoice?.status !== 'quoted') {
        return false
      }

      if (!this.isAuthenticated) {
        return false
      }

      // Check if user belongs to the same organization as the quote
      const userOrg = this.$store.state.organization?.organization?._id
      const quoteOrg = this.$route.query.org

      // Debug logging (only in development)
      if (process.env.NODE_ENV === 'development') {
        console.log('canEdit debug:', {
          userOrg,
          quoteOrg,
          userOrgString: String(userOrg),
          quoteOrgString: String(quoteOrg),
          match: userOrg === quoteOrg,
          isAuthenticated: this.isAuthenticated,
          invoiceStatus: this.invoice?.status,
          user: this.$store.state.user,
          organization: this.$store.state.organization
        })
      }

      return userOrg === quoteOrg
    },
    isQuote () {
      return this.invoice?.status === 'quoted'
    }
  },
  mounted () {
    // Set up Firebase auth state listener
    this.unsubscribeAuthChange = this.$fire.auth.onAuthStateChanged(async (user) => {
      if (user) {
        // User is signed in, set up user details
        await this.setupUserDetails(user)
      }
      // Whether user is signed in or not, we're done loading auth state
      this.isAuthLoading = false
    })
  },
  beforeDestroy () {
    // Clean up the auth state listener
    this.unsubscribeAuthChange()
  },
  methods: {
    print () {
      window.print()
    },
    toggleEditMode () {
      this.isEditMode = true
      // Initialize edit data with current values
      this.editData = {
        meta: {
          companyName: this.invoice?.meta?.companyName || this.invoice?.organization?.name || '',
          firstName: this.invoice?.meta?.firstName || '',
          lastName: this.invoice?.meta?.lastName || '',
          email: this.invoice?.meta?.email || ''
        },
        organization: {
          website: this.invoice?.organization?.website || ''
        },
        billingDetails: this.invoice?.billingDetails || ''
      }
      // Store original data for cancel functionality
      this.originalData = JSON.parse(JSON.stringify(this.editData))
    },
    async saveChanges () {
      // Double-check authentication before making the request
      if (!this.isAuthenticated) {
        this.$toast.error('You must be logged in to edit quotes')
        return
      }

      this.isSaving = true
      try {
        const updateData = {
          meta: this.editData.meta,
          billingDetails: this.editData.billingDetails,
          organizationWebsite: this.editData.organization.website
        }

        const response = await this.$axios.$put(
          `/invoices/${this.$route.query.org}/${this.$route.query.id}`,
          updateData
        )

        if (response.success) {
          // Update the local invoice data
          this.invoice.meta = { ...this.invoice.meta, ...this.editData.meta }
          this.invoice.billingDetails = this.editData.billingDetails
          this.invoice.organization.website = this.editData.organization.website

          this.isEditMode = false
          this.$toast.success('Quote updated successfully')

          // Refresh the page to ensure all computed properties are updated
          setTimeout(() => {
            window.location.reload()
          }, 1000)
        } else {
          throw new Error(response.errorMessage || 'Failed to update quote')
        }
      } catch (error) {
        console.error('Error updating quote:', error)

        // Handle specific error cases
        if (error.response?.status === 401) {
          this.$toast.error('Authentication failed. Please log in and try again.')
        } else if (error.response?.status === 403) {
          this.$toast.error('You do not have permission to edit this quote.')
        } else if (error.response?.status === 404) {
          this.$toast.error('Quote not found or cannot be edited.')
        } else {
          this.$toast.error(error.response?.data?.errorMessage || error.message || 'Failed to update quote')
        }
      } finally {
        this.isSaving = false
      }
    },
    cancelEdit () {
      this.isEditMode = false
      this.editData = JSON.parse(JSON.stringify(this.originalData))
    }
  }
}
</script>

<style scoped>

.page-break {
  page-break-before: always;
}

@media print {
  /* Ensure H2 components keep their color */
  ::v-deep(h2) {
    @apply text-primary-500;
    color-adjust: exact;
    -webkit-print-color-adjust: exact; /* For Safari/Chrome */
  }

  /* Ensure Paragraph components keep their color */
  ::v-deep(p) {
    @apply text-slate-500;
    color-adjust: exact;
    -webkit-print-color-adjust: exact; /* For Safari/Chrome */
  }

  /* Ensure IconListItem title color prints */
  ::v-deep(.flex.items-center h3) {
    @apply text-primary-500;
    color-adjust: exact;
    -webkit-print-color-adjust: exact;
  }

  /* Ensure IconListItem description color prints */
  ::v-deep(.flex.flex-col p) {
     @apply text-slate-500;
     color-adjust: exact;
     -webkit-print-color-adjust: exact;
  }

  /* Ensure IconSquare background color prints */
  /* This targets the div within IconSquare based on its classes */
  ::v-deep(.rounded-\[8px\]) {
    /* We can't easily @apply a dynamic background here */
    /* Instead, we rely on the inline style + color-adjust */
    color-adjust: exact;
    -webkit-print-color-adjust: exact;
  }

  ::v-deep(table) {
    color-adjust: exact;
    -webkit-print-color-adjust: exact;
  }

  ::v-deep(tr) {
    color-adjust: exact;
    -webkit-print-color-adjust: exact;
  }

  ::v-deep(td), ::v-deep(th) {
    color-adjust: exact;
    -webkit-print-color-adjust: exact;
  }

  .button{
    color-adjust: exact;
    -webkit-print-color-adjust: exact;
    background-color: #1B145D;
  }

  /* If the above is too broad, you might need to scope it, e.g.: */
  /* .page-break h2 { ... } */
  /* .page-break p { ... } */
}
</style>
