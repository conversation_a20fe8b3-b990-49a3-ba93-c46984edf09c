<template>
  <div class="flex flex-col min-h-screen">
    <!-- <MarketingTopbar /> -->
    <Header />
    <main class="flex flex-col flex-1">
      <div class="block md:hidden">
        <div id="convert-10-v2" class="hidden">
          <MarketingHeroMobile />
        </div>
        <div id="convert-10-v1">
          <MarketingHero />
          <MarketingStats />
        </div>
      </div>
      <div class="hidden md:block">
        <MarketingHeroDesktop />
        <MarketingStatsDesktop />
      </div>

      <div class="w-full h-8 bg-gradient-to-b from-teal-100 to-white" />
      <MarketingReviews :tweets="tweets" />
      <MarketingHowItWorks id="convert-11-v1" />
      <MarketingHowItWorksV2 id="convert-11-v2" class="hidden" />
      <MarketingStyles />
      <MarketingPricing />
      <MarketingFAQ />
      <MarketingCTA class="pb-16" />
    </main>
    <MarketingFooter />
  </div>
</template>

<script>
export default {
  async asyncData ({ $axios }) {
    // const styles = await $axios.$get('/styles')
    // const tweets = await $axios.$get('/reviews/wall-of-love/twitter', { params: { limit: 8 } })
    // return { styles: styles.data.styles, tweets }
  },
  head () {
    return {
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com' + this.$route.path
        }
      ]
    }
  }
}
</script>

<style></style>
