<template>
  <div class="bg-gray-50">
    <Header />
    <section class="py-12 bg-[#F8FCFF] sm:py-16 lg:py-20 pb-0 lg:pb-4">
      <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8 2xl:px-0">
        <div class="max-w-2xl mx-auto text-left md:text-center">
          <div class="hidden md:items-center md:justify-center md:gap-6 md:flex">
            <!-- <div class="flex items-center gap-3">
              <img class="w-auto h-6" src="@/assets/img/trustpilot-stars-4.5.svg" alt="" loading="lazy">
              <img class="w-auto h-6" src="@/assets/img/logo-trustpilot.png" alt="" loading="lazy">
            </div>

            <div class="flex items-center gap-2">
              <div class="flex -space-x-2 overflow-hidden">
                <img class="inline-block w-6 h-6 rounded-full ring-2 ring-white" src="@/assets/img/avatar-1.jpg" alt="" loading="lazy">
                <img class="inline-block w-6 h-6 rounded-full ring-2 ring-white" src="@/assets/img/avatar-2.jpg" alt="" loading="lazy">
                <img class="inline-block w-6 h-6 rounded-full ring-2 ring-white" src="@/assets/img/avatar-3.jpg" alt="" loading="lazy">
              </div>
              <p class="text-sm font-normal text-[#474368] -mt-0.5">
                <span class="font-bold">{{ $store.state.stats.users }}+</span> happy customers
              </p>
            </div> -->
            <TrustpilotRating />
          </div>

          <h1
            class="text-2xl sm:text-4xl xl:text-[42px] leading-tight mt-4 font-bold tracking-tighter text-primary-500"
          >
            Professional AI Headshot Examples<br> Created with HeadshotPro
          </h1>
          <p class="mt-4 text-base font-medium text-[#474368] sm:text-lg">
            Find hundreds of professional headshot examples created with the best AI headshot generator for professional headshots.
          </p>

          <div class="mt-6 flex flex-col md:flex-row items-center justify-center gap-2 md:gap-4">
            <nuxt-link
              to="/"
              class="text-base w-full sm:w-auto inline-flex font-bold text-white rounded-lg shadow-sm bg-primary-500 pt-3 pb-3.5 px-6 h-12 border border-transparent transition-all duration-150 hover:bg-opacity-90 gap-1.5 items-center justify-center disabled:bg-opacity-20"
              role="button"
            >
              Get your AI headshots now
              <svg class="size-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path
                  fill-rule="evenodd"
                  d="M12.97 3.97a.75.75 0 0 1 1.06 0l7.5 7.5a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 1 1-1.06-1.06l6.22-6.22H3a.75.75 0 0 1 0-1.5h16.19l-6.22-6.22a.75.75 0 0 1 0-1.06Z"
                  clip-rule="evenodd"
                />
              </svg>
            </nuxt-link>
            <a href="https://www.trustpilot.com/review/headshotpro.com" target="_blank" class="w-full md:w-auto">
              <ButtonDark class="w-full md:w-auto">
                View on TrustPilot
              </ButtonDark>
            </a>
          </div>

          <div class="flex flex-col gap-4 mt-4 md:hidden sm:flex-row sm:items-center sm:mt-8">
            <div class="flex items-center gap-3">
              <img class="w-auto h-6" src="@/assets/img/trustpilot-stars-4.5.svg" alt="" loading="lazy">
              <img class="w-auto h-6" src="@/assets/img/logo-trustpilot.png" alt="" loading="lazy">
            </div>

            <div class="flex items-center gap-2">
              <div class="flex -space-x-2 overflow-hidden">
                <img class="inline-block w-6 h-6 rounded-full ring-2 ring-white" src="@/assets/img/avatar-1.jpg" alt="" loading="lazy">
                <img class="inline-block w-6 h-6 rounded-full ring-2 ring-white" src="@/assets/img/avatar-2.jpg" alt="" loading="lazy">
                <img class="inline-block w-6 h-6 rounded-full ring-2 ring-white" src="@/assets/img/avatar-3.jpg" alt="" loading="lazy">
              </div>
              <p class="text-sm font-normal text-[#474368] -mt-0.5">
                <span class="font-bold">{{ $store.state.stats.users }}+</span> happy customers
              </p>
            </div>
          </div>
        </div>
        <MarketingLogoCloud class="w-full max-w-2xl mx-auto mt-8" />
      </div>
    </section>
    <section class="py-4 bg-[#F8FCFF] sm:py-4 lg:py-4">
      <div class="max-w-screen-xl px-4 mx-auto 2xl:px-0 sm:px-6 lg:px-8">
        <!-- <div class="text-left md:text-center">
          <h2 class=" text-2xl font-medium tracking-tight sm:text-2xl lg:text-2xl text-primary-500">
            <span class=" font-bold text-teal-500">
              {{ $store.state.stats.photos }}</span>
            {{ $t('AI headshots already created') }}
            <br class="hidden md:block">
            {{ $t('for') }}
            <span class="font-bold text-yellow-500">{{ $store.state.stats.users }}</span>
            {{ $t('happy customers!') }}
          </h2>
        </div> -->

        <div
          ref="masonryContainer"
          v-masonry
          transition-duration="0.1s"
          stagger="0s"
          item-selector=".item"
          class="w-full mt-4"
        >
          <template v-for="item in visibleReviews">
            <div :key="item.id" v-masonry-tile class="item w-full p-2 sm:w-1/2 lg:w-1/3 xl:w-1/4">
              <MarketingReviewItem
                v-if="item.type === 'review'"
                :key="item._id"
                :item="item"
                :badge="isCreatedAfterAugust21(item.createdAt) ? 'Updated model' : ''"
                @load="refreshMasonry"
              />
              <LandingpageV2ReviewTrustpilot
                v-else
                :key="item.id"
                :item="item"
              />
            </div>
          </template>
        </div>
        <div ref="loadMoreTrigger" class="h-10" />

        <!-- <div class="grid grid-cols-1 gap-4 mt-8 sm:mt-12 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4">
          <div class="space-y-4">
            <MarketingReviewItem
              v-for="item in firstColumn"
              :key="item._id"
              :item="item"
              :badge="isCreatedAfterAugust21(item.createdAt) ? 'Updated model' : ''"
            />
          </div>
          <div class="space-y-4">
            <MarketingReviewItem
              v-for="item in secondColumn"
              :key="item._id"
              :item="item"
              :badge="isCreatedAfterAugust21(item.createdAt) ? 'Updated model' : ''"
            />
          </div>
          <div class="space-y-4">
            <MarketingReviewItem
              v-for="item in thirdColumn"
              :key="item._id"
              :item="item"
              :badge="isCreatedAfterAugust21(item.createdAt) ? 'Updated model' : ''"
            />
          </div>
          <div class="space-y-4">
            <template v-for="item of trustPilotReviews.slice(0, filteredReviews.length / )">
              <LandingpageV2ReviewTrustpilot :key="item.id" :item="item" />
            </template>
          </div>
        </div> -->
      </div>
    </section>
    <!-- <MarketingUpgradeYourHeadshots :testimonials="false" />
    <MarketingCTAV2 /> -->
    <MarketingFooter />
  </div>
</template>

<script>
export default {
  async asyncData ({ $axios, store }) {
    // Use reviews from the store if they exist
    if (store.state.reviews?.length > 0) {
      return {
        reviews: store.state.reviews
      }
    }

    // Otherwise fetch them
    const { data } = await $axios.$get('/reviews/all')
    return {
      reviews: data
    }
  },
  data () {
    return {
      visibleReviews: [],
      currentIndex: 0,
      batchSize: 100,
      observer: null
    }
  },
  head () {
    return {
      title: this.title,
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com/reviews'
        }
      ],
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.description
        },
        {
          hid: 'twitter:title',
          name: 'twitter:title',
          content: this.title
        },
        {
          hid: 'twitter:creator',
          name: 'twitter:creator',
          content: '@dannypostmaa'
        },
        {
          hid: 'twitter:description',
          name: 'twitter:description',
          content: this.description
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.title
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.description
        }
      ]
    }
  },
  computed: {
    mixedReviews () {
      const mixed = []
      const newReviews = [...this.newReviews]
      const trustpilotReviews = [...this.trustPilotReviews]

      while (newReviews.length > 0 || trustpilotReviews.length > 0) {
        if (newReviews.length > 0) {
          mixed.push({ type: 'review', ...newReviews.shift() })
        }
        if (trustpilotReviews.length > 0) {
          mixed.push({ type: 'trustpilot', ...trustpilotReviews.shift() })
        }
      }

      return mixed
    },

    masonryColumns () {
      const columns = [[], [], [], [], []]
      this.mixedReviews.forEach((item, index) => {
        columns[index % 5].push(item)
      })
      return columns
    },
    trustPilotReviews () {
      return this.$store.state.trustpilotReviews
    },
    title () {
      return 'Professional Headshot Examples | HeadshotPro'
    },
    description () {
      return '453,720 AI headshots already created for 3,781 happy customers! You\'re in good company. Here\'s what our beautiful customers have to say about us.'
    },
    firstColumn () {
      return this.reviewsForColumn(1)
    },
    secondColumn () {
      return this.reviewsForColumn(2)
    },
    thirdColumn () {
      return this.reviewsForColumn(3)
    },
    fourthColumn () {
      return this.reviewsForColumn(4)
    },
    filteredReviews () {
      return [...this.newReviews, ...this.oldReviews].filter(review => this.isAtLeastFromJune(review.createdAt) && review?.review?.image !== null)
    },
    oldReviews () {
      // Any reviews before augst 19th
      return this.reviews.filter(review => !this.isCreatedAfterAugust21(review.createdAt))
    },
    newReviews () {
      // Any reviews after augst 19th
      return this.reviews.filter(review =>
        this.isCreatedAfterAugust21(review.createdAt) &&
        review?.review?.image != null
      )
    }
  },
  mounted () {
    this.loadMoreReviews()
    this.setupIntersectionObserver()
  },

  beforeDestroy () {
    if (this.observer) {
      this.observer.disconnect()
    }
  },
  methods: {
    // shuffleArray (array) {
    //   for (let i = array.length - 1; i > 0; i--) {
    //     const j = Math.floor(Math.random() * (i + 1));
    //     [array[i], array[j]] = [array[j], array[i]]
    //   }
    //   return array
    // },
    loadMoreReviews () {
      const newBatch = this.mixedReviews.slice(this.currentIndex, this.currentIndex + this.batchSize)
      this.visibleReviews.push(...newBatch)
      this.currentIndex += this.batchSize

      this.$nextTick(() => {
        this.refreshMasonry()
      })
    },

    setupIntersectionObserver () {
      this.observer = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && this.currentIndex < this.mixedReviews.length) {
          this.loadMoreReviews()
        }
      }, { rootMargin: '100px' })

      this.observer.observe(this.$refs.loadMoreTrigger)
    },

    reviewsForColumn (col) {
      const reviews = []
      for (let i = 0; i < this.filteredReviews.length; i++) {
        if (i % 4 === col - 1) {
          reviews.push(this.filteredReviews[i])
        }
      }

      return reviews
    },
    isCreatedAfterAugust21 (createdAt) {
      const date = new Date(createdAt)
      const august21 = new Date('2024-08-19')
      return date > august21
    },
    isAtLeastFromJune (createdAt) {
      const date = new Date(createdAt)
      const june = new Date('2024-06-01')
      return date > june
    }
  }
}
</script>

<style></style>
