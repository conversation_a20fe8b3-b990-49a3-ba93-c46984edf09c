<template>
  <div>
    <Header />
    <main class="flex flex-col py-8 flex-1 min-h-screen bg-muted-50">
      <div class="max-w-4xl w-full mx-auto px-4 py-8">
        <Card v-if="job">
          <div class="p-8 gap-8 flex flex-col">
            <H2 class="mb-6 text-[32px]">
              {{ job.title }}
            </H2>
            <nuxt-content :document="job" class="prose max-w-none" />
          </div>
        </Card>
        <div v-else class="text-center py-12">
          <H2>Job not found</H2>
          <Paragraph>Sorry, we couldn't find the job you were looking for.</Paragraph>
          <nuxt-link to="/jobs" class="text-blue-600 hover:underline mt-4 inline-block">
            Back to Open Positions
          </nuxt-link>
        </div>
      </div>
    </main>
    <MarketingFooter />
  </div>
</template>

<script>
import H2 from '@/components/landingpage/common/H2.vue'
import Paragraph from '@/components/landingpage/common/Paragraph.vue'

export default {
  components: {
    H2,
    Paragraph
  },
  layout: 'default',
  async asyncData ({ $content, params, error }) {
    try {
      const job = await $content('jobs', params.slug).fetch()

      if (!job) {
        error({ statusCode: 404, message: 'Job not found' })
        return { job: null }
      }

      return {
        job
      }
    } catch (e) {
      error({ statusCode: 500, message: 'Error fetching job content' })
      return { job: null }
    }
  },
  head () {
    if (!this.job) {
      return {
        title: 'Job Not Found - HeadshotPro'
      }
    }
    const title = `${this.job.title} - HeadshotPro Careers`
    // Use the first paragraph of the description as meta description, capped at 160 chars
    const description = this.job.description ? this.job.description.split('\n')[0].substring(0, 160) : 'View job details for the ' + this.job.title + ' position at HeadshotPro.'

    return {
      title,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: description
        }
      ]
    }
  }
}
</script>

<style scoped>
/* Add styles for the prose class if needed, or rely on Tailwind Typography plugin if installed */
.prose {
  /* Example basic prose styling if Tailwind Typography is not used */
  line-height: 1.6;
}
.prose :deep(h2) {
  font-size: 1.5em;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
}
.prose :deep(ul) {
  list-style-type: disc;
  padding-left: 1.5em;
  margin-top: 1em;
  margin-bottom: 1em;
}
.prose :deep(p) {
  margin-bottom: 1em;
}
</style>
