<template>
  <div>
    <Header />
    <main class="flex flex-col py-8 flex-1 min-h-screen bg-muted-50">
      <div class="max-w-5xl w-full mx-auto px-4 py-8">
        <Card>
          <div class="p-8 gap-8 flex flex-col">
            <div class="flex flex-col gap-0.5">
              <H2 class="mb-6 text-[32px]">
                Open Positions
              </H2>
              <Paragraph size="md">
                Explore career opportunities at HeadshotPro, a leading AI headshot service. We're looking for proactive individuals to join our remote, async team. Check out our open positions.
              </Paragraph>
            </div>
            <ul v-if="jobs && jobs.length">
              <li v-for="job in jobs" :key="job.slug" class="mb-4">
                <nuxt-link
                  :to="`/jobs/${job.slug}`"
                  class="text-xl text-blue-600 hover:underline"
                >
                  {{ job.title }}
                </nuxt-link>
              </li>
            </ul>
            <p v-else>
              No open positions at the moment.
            </p>
          </div>
        </Card>
      </div>
    </main>
    <MarketingFooter />
  </div>
</template>

<script>
import H2 from '@/components/landingpage/common/H2.vue'

export default {
  components: {
    H2
  },
  layout: 'default',
  async asyncData ({ $content, params }) {
    const jobs = await $content('jobs')
      .only(['title', 'description', 'slug'])
      .sortBy('createdAt', 'asc')
      .fetch()

    return {
      jobs
    }
  },
  head () {
    return {
      title: 'Open Positions - HeadshotPro',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'Explore career opportunities at HeadshotPro, a leading AI headshot service. We\'re looking for proactive individuals to join our remote, async team. Check out our open positions.'
        }
      ]
    }
  }
}
</script>

<style scoped>
/* Add any specific styles if needed */
</style>
