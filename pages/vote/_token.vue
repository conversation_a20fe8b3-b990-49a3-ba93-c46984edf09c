<template>
  <div class="relative bg-gray-100 min-h-screen">
    <div class="w-full bg-white border-b border-gray-200 p-3 px-4 z-50 md:sticky md:top-0">
      <div class="max-w-8xl mx-auto px-4 flex justify-between gap-4">
        <div class="flex items-center justify-start">
          <NuxtLink to="/">
            <Logo class="w-[140px]" />
          </NuxtLink>
        </div>
      </div>
    </div>
    <LoadingWrapper :is-loading="isLoading" :show-spinner="false">
      <template #loading>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 h-screen w-full p-2 md:p-8">
          <template v-for="(_, index) in 24">
            <SkeletonBlock :key="`skeleton-${index}`" height="300px" />
          </template>
        </div>
      </template>
      <template v-if="linkIsInvalid">
        <div class="max-w-xl mx-auto px-4 mt-4 md:mt-16">
          <div class="flex items-center justify-center">
            <Card>
              <div class="space-y-1">
                <h1 class="text-[25px] font-bold leading-tight text-primary-500 text-center">
                  Voting not available
                </h1>
                <p class="text-slate-500 text-center">
                  The person who shared this link has either deleted it, it never existed or you've voted too many times.
                </p>
              </div>
              <div class="mt-4 w-full flex items-center justify-center">
                <NuxtLink to="/">
                  <ButtonDark size="sm">
                    Back to home
                  </ButtonDark>
                </NuxtLink>
              </div>
            </Card>
          </div>
        </div>
      </template>
      <template v-else>
        <div v-if="model" class="pb-32 pt-6 lg:pt-16 lg:pb-0 max-w-8xl mx-auto">
          <div class="max-w-[766px] px-4 mx-auto pb-6 lg:pb-16">
            <h1 class="text-lg lg:text-[25px] font-bold leading-tight text-primary-500 text-center">
              📸 Vote for your favorite headshots
            </h1>
            <p class="text-slate-500 text-center mt-1 text-sm lg:text-base">
              You have {{ maxVotes }} votes to cast for "{{ model.title }}". Choose wisely!
            </p>
          </div>
          <div class="px-4 w-full min-h-screen flex flex-col lg:flex-row gap-8">
            <Card class="w-full">
              <header class="flex flex-col xs:flex-row gap-8 items-center justify-between">
                <h2 class="text-lg font-medium text-primary-500 items-center hidden xs:flex">
                  <IconCamera class="h-6 w-6 text-paragraph mr-[10px]" />
                  All photos
                </h2>
              </header>
              <div class="grid grid-cols-2 xs:grid-cols-2 md:grid-cols-3 lg:grid-cols-2 xl:grid-cols-3 gap-4 lg:mt-5">
                <template v-for="photo in allPhotos">
                  <GalleryItem
                    v-if="!photo?.nsfw && photo?._id"
                    :key="photo?._id"
                    :photo="photo"
                    :selected="selectedIds"
                    :selection-screen="true"
                    :watermark="!model?.removeWatermarks"
                    :still-generating="false"
                    @like="toggleSelected(photo?._id)"
                    @select="toggleSelected(photo?._id)"
                    @preview="openPreview(photo)"
                  />
                </template>
              </div>
            </Card>
            <div class="w-full lg:w-[380px] lg:flex-shrink-0">
              <div class="fixed bottom-0 left-0 z-40 w-full lg:sticky lg:top-20 lg:bottom-auto lg:left-auto space-y-4">
                <Card>
                  <h3 class="text-[15px] font-medium text-primary-500 pb-4 border-b border-[#E4E4E7CC] hidden lg:block">
                    Your votes ({{ selectedIds.length }}/{{ maxVotes }})
                  </h3>
                  <div class="flex space-x-2.5 items-center justify-center lg:justify-start  lg:mt-5 ">
                    <div v-if="selectedPhotos.length > 0" class="flex items-center justify-start -space-x-3 ">
                      <div v-for="photo in selectedPhotos" :key="photo._id" class="group w-10 h-10 rounded-full shadow-xl relative overflow-hidden border-black/10 border">
                        <ImageDns :src="photo.thumbnail || photo.image" class=" w-10 h-10 rounded-full  object-cover" />
                        <button class="group-hover:opacity-100 opacity-0 transition-opacity duration-300 absolute top-2 left-2 z-20" @click="toggleSelected(photo._id)">
                          <IconCross class="w-6 h-6 text-red-500" />
                        </button>
                        <div class="hidden group-hover:block bg-black/70 absolute top-0 left-0 z-10 w-10 h-10 rounded-full" />
                      </div>
                    </div>
                    <ButtonWhite v-if="selectedIds.length > 0" class="inline-flex" size="xs" @click="selectedIds = []">
                      Deselect all
                    </ButtonWhite>
                  </div>
                  <ButtonPrimary v-if="selectedIds.length === 0" :is-disabled="true" class="not-first:mt-5 w-full">
                    <span>Please select at least one photo</span>
                  </ButtonPrimary>
                  <ButtonGradient v-else class="mt-5 w-full" @click="sendMyVotes">
                    <span>Send my votes</span>
                    <IconSmallArrow class="w-5 h-5 ml-1.5 text-white" />
                  </ButtonGradient>
                  <p class="hidden lg:block text-xs text-slate-500 mt-5 text-center">
                    You can change your votes later.
                  </p>
                </Card>
                <Card class="!hidden lg:!flex">
                  <h3 class="font-bold text-primary-500">
                    ✅ Vote wisely
                  </h3>
                  <p class="text-slate-500 mt-2">
                    You have been given {{ maxVotes }} votes to cast for "{{ model.title }}". Cast your votes wisely!
                  </p>
                </Card>
              </div>
            </div>
          </div>
          <Modal v-if="showIntroModal" :show-close-button="false">
            <div class="w-full md:w-[500px] p-4 md:p-8 flex flex-col text-gray-700 space-y-3 prose prose-h5:font-medium prose-h5:text-black md:prose-h5:text-lg prose-h5:text-base prose-p:text-sm md:prose-p:text-base prose-p:text-gray-700 prose-bold:text-gray-700">
              <h3 class="font-bold text-xl md:text-2xl text-black m-0 p-0">
                Ready to vote?
              </h3>
              <p class="font-medium text-gray-900 ">
                Wait! Here's a small guide to help you cast your votes wisely:
              </p>
              <h5>❌ Ignore the duds</h5>
              <p><strong>In a 📸 real photoshoot</strong>, the duds are the photos where your eyes are closed, crossed, or just plain awkward. </p>
              <p><strong>In an 🤖 AI photoshoot</strong>, the duds are the photos where you have extra limbs, strange scenery, or a strong lack of resemblance.</p>
              <h5>✅ Vote wisely</h5>
              <p>You have been given {{ maxVotes }} votes to cast for "{{ model.title }}". Cast your votes wisely!</p>
              <div class="pt-4 w-full">
                <ButtonPrimary v-if="canContinue" class="w-full" @click="showIntroModal = false">
                  <span>Start voting</span>
                </ButtonPrimary>
                <ButtonDisabled v-else class="w-full">
                  <LoadingSpinnerGray class="mr-1.5" />
                  <span>Start voting</span>
                </ButtonDisabled>
              </div>
            </div>
          </Modal>
          <Modal v-if="showThankYouModal" :show-close-button="false" max-width="sm:max-w-5xl">
            <div class="w-full p-4 md:p-8 flex flex-col text-gray-700">
              <div class="relative flex flex-col items-center justify-center md:flex-row md:justify-between">
                <Logo class="w-[150px] text-black" />
                <MarketingTrustpilot size="h-5 w-5" />
              </div>
              <h3 class="font-bold text-xl text-center md:text-2xl text-black mt-12">
                Thank you for voting! 💙
              </h3>
              <p class="font-medium text-center text-gray-900 mt-2 max-w-md mx-auto">
                If you liked the headshots and you want to create your own, you can do it with a<strong>$10 discount for the next 24 hours!</strong>
              </p>

              <div class="mt-8 space-y-4 max-w-md w-full mx-auto">
                <ButtonPrimary class="w-full" @click="createYourOwnHeadshots">
                  <span>Create your headshots ($10 off)</span>
                </ButtonPrimary>
                <ButtonWhite class="w-full" @click="showThankYouModal = false">
                  <span>Back</span>
                </ButtonWhite>
              </div>

              <div class="mx-auto mt-8 grid max-w-5xl grid-cols-1 gap-6 sm:mt-12 md:grid md:grid-cols-3">
                <client-only>
                  <LandingpageRandomTrustpilotReview v-for="i in 3" :key="i" />
                </client-only>
              </div>
            </div>
          </Modal>
          <Popup v-if="showPreviewModal" size="5xl" @closeModal="showPreviewModal = false; previewImage = null;">
            <GalleryPreviewModal
              :preview-image="previewImage"
              :preview-image-large="highQualityImage"
              :all-photos="photos"
              :selected="selectedIds.includes(openedPhoto?._id)"
              @select="toggleSelected(openedPhoto?._id)"
              @navigate="navigate"
            />
          </Popup>
        </div>
      </template>
    </LoadingWrapper>
  </div>
</template>

<script>

export default {
  layout: 'default',
  data () {
    return {
      canContinue: false,
      isLoading: true,
      voterToken: null,
      model: null,
      selectedIds: [],
      previewImage: null,
      highQualityImage: null,
      showPreviewModal: false,
      showIntroModal: true,
      votesSent: false,
      showThankYouModal: false,
      linkIsInvalid: false,
      isSubmitting: false,
      maxVotes: 5
    }
  },
  head () {
    return {
      title: 'Vote headshots | HeadshotPro'
    }
  },
  computed: {
    voterTokenKey () {
      return `voterToken-${this.$route.params.token}`
    },
    photos () {
      const photos = this.model?.images || []
      return photos.filter(photo => photo?.status === 'active') || []
    },
    allPhotos () {
      if (this.selectedPhotos.length >= this.maxVotes) {
        return this.selectedPhotos
      }

      return this.photos
    },
    selectedPhotos () {
      return this.photos.filter(photo => this.selectedIds.includes(photo?._id)) || []
    },
    openedPhoto () {
      return this.photos.find(photo => photo.preview === this.previewImage || photo.thumbnail === this.previewImage)
    }
  },
  async mounted () {
    if (!localStorage.getItem(this.voterTokenKey)) {
      await this.registerVoter()
    } else {
      this.voterToken = localStorage.getItem(this.voterTokenKey)
    }

    if (!this.$store.state.packages || this.$store.state.packages.length === 0) {
      this.$store.dispatch('getPackages')
    }

    this.fetchMyVotes()
  },
  methods: {
    startContinueTimer () {
      this.canContinue = false
      setTimeout(() => {
        this.canContinue = true
      }, (this.isDevelopment) ? 1 : 2000)
    },
    fetchMyVotes () {
      this.isLoading = true
      this.$loading.show({ title: 'Loading all photos...' })
      this.$axios.$get(`/model/voting/my-votes?shareToken=${this.$route.params.token}&token=${this.voterToken}`)
        .then((response) => {
          this.model = response.data.model
          this.selectedIds = response.data.votes
          this.votesSent = response.data.votes?.length > 0
          this.startContinueTimer()
        }).catch((error) => {
          this.linkIsInvalid = true
          this.handleError(error)
        }).finally(() => {
          this.$loading.hide()
          this.isLoading = false
        })
    },
    async registerVoter () {
      try {
        const response = await this.$axios.$post('/model/voting/register-voter', {
          shareToken: this.$route.params.token
        })

        if (response?.data?.token) {
          this.voterToken = response.data.token
          localStorage.setItem(this.voterTokenKey, response.data.token)
        }
      } catch (error) {
        this.isLoading = false
        this.linkIsInvalid = true
        this.handleError(error)
      }
    },
    openPreview (photo) {
      this.previewImage = photo.preview || photo.thumbnail
      this.highQualityImage = photo.image || photo.thumbnail
      this.showPreviewModal = true
    },
    toggleSelected (photoId) {
      if (this.selectedIds.includes(photoId)) {
        this.selectedIds = this.selectedIds.filter(id => id !== photoId)
      } else {
        if (this.selectedIds.length >= this.maxVotes) {
          this.$toast.error(`You can only select up to ${this.maxVotes} photos`)
          return
        }

        this.selectedIds = [...this.selectedIds, photoId]
      }
    },
    sendMyVotes () {
      if (this.selectedIds.length === 0) {
        this.$toast.error('Please select at least one photo')
        return
      }

      // Show popup
      this.$loading.show({ title: 'Submitting votes...' })

      this.$axios.$post('/model/voting/vote', {
        shareToken: this.$route.params.token,
        token: this.voterToken,
        modelPhotoIds: this.selectedIds
      }).then(() => {
        this.$toast.success('Your votes have been sent!')
        this.showThankYouModal = true
      }).catch((error) => {
        this.handleError(error)
      }).finally(() => {
        this.$loading.hide()
      })
    },
    navigate (index) {
      const photo = this.photos[index]
      if (photo) {
        this.openPreview(photo)
      }
    },
    createYourOwnHeadshots () {
      this.$axios.$post('/model/voting/vote-discount', {
        shareToken: this.$route.params.token,
        token: this.voterToken
      }).then((res) => {
        if (res.data?.discountCode) {
          this.$router.push(`/checkout?coupon=${res.data.discountCode}&priceId=${this.$store.state.packages.medium.id}&productId=package`)
        } else {
          this.$toast.error('Something went wrong. Please try again later.')
        }
      }).catch((error) => {
        this.handleError(error)
      })
    }
  }
}
</script>

<style scoped>
.card{
  padding: 0.8rem;
}

@media (min-width: 1024px) {
  .card {
    padding: 1.5rem;
  }
}
</style>
