<template>
  <div>
    Purchase tracked
  </div>
</template>

<script>
export default {
  async mounted () {
    const { type } = this.$route.params
    console.log(type)

    if (type === 'ecommerce') { await this.trackPurchase() }
  },
  methods: {
    async trackPurchase () {
      const { unit_amount: unitAmount, id, quantity, name, transactionId, paymentIntentId } = this.$route.query
      const totalAmount = (parseInt(unitAmount) * parseInt(quantity || 1) / 100)

      const paymentIntent = await this.$axios.$get(`/paymentintent/single/${paymentIntentId}`)
      if (!paymentIntent && paymentIntent.data) { return false }

      if (paymentIntent.data.isTracked) { return false }
      // this.$gtm.push({
      //   event: 'purchase',
      //   ecommerce: {
      //     purchase: {
      //       actionField: {
      //         id: transactionId, // Transaction ID. Required for purchases and refunds.
      //         revenue: totalAmount // Total transaction value (incl. tax and shipping)
      //       },
      //       products: [{ // List of productFieldObjects.
      //         name, // Name or ID is required.
      //         id,
      //         price: (parseInt(unitAmount) / 100),
      //         quantity: parseInt(quantity) || 1
      //       }]
      //     }
      //   }
      // })
      console.log({
        event: 'purchase',
        ecommerce: {
          purchase: {
            actionField: {
              id: transactionId, // Transaction ID. Required for purchases and refunds.
              revenue: totalAmount // Total transaction value (incl. tax and shipping)
            },
            products: [{ // List of productFieldObjects.
              name, // Name or ID is required.
              id,
              price: (parseInt(unitAmount) / 100),
              quantity: parseInt(quantity) || 1
            }]
          }
        }
      })
      await this.$axios.$post('/paymentintent/track/' + paymentIntentId)
    }
  }

}
</script>

<style>

</style>
