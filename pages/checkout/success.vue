<template>
  <PrecheckoutWrapper>
    <section class="py-8 sm:py-12">
      <div class="max-w-screen-xl px-5 mx-auto sm:px-6 lg:px-8">
        <div class="max-w-sm mx-auto md:text-center">
          <svg
            aria-hidden="true"
            class="mx-auto size-12 text-[#65C466]"
            viewBox="0 0 52 52"
            fill="none"
            stroke="currentColor"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M20.5834 19.4997C20.5834 20.098 20.0983 20.583 19.5 20.583C18.9017 20.583 18.4167 20.098 18.4167 19.4997M20.5834 19.4997C20.5834 18.9014 20.0983 18.4163 19.5 18.4163C18.9017 18.4163 18.4167 18.9014 18.4167 19.4997M20.5834 19.4997H18.4167M33.5834 19.4997C33.5834 20.098 33.0984 20.583 32.5 20.583C31.9017 20.583 31.4167 20.098 31.4167 19.4997M33.5834 19.4997C33.5834 18.9014 33.0984 18.4163 32.5 18.4163C31.9017 18.4163 31.4167 18.9014 31.4167 19.4997M33.5834 19.4997H31.4167M26 47.6663C14.0339 47.6663 4.33337 37.9658 4.33337 25.9997C4.33337 14.0335 14.0339 4.33301 26 4.33301C37.9662 4.33301 47.6667 14.0335 47.6667 25.9997C47.6667 37.9658 37.9662 47.6663 26 47.6663ZM36.8334 28.1663C36.8334 28.1663 34.1246 36.833 26 36.833C17.8755 36.833 15.1667 28.1663 15.1667 28.1663H36.8334Z"
              stroke-width="4.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>

          <h1 class="mt-4 text-xl font-bold tracking-tight text-primary-500">
            {{ $t('Thank you for your purchase!') }}
          </h1>

          <div class="mt-2 text-base font-medium text-gray-600 md:max-w-sm md:mx-auto">
            <p v-if="isTeam">
              It's time to invite your team! You will now be redirected to your dashboard.
            </p>
            <p v-else-if="isTeamInvoice">
              Thank you for your purchase! Credits have been added to your account.
            </p>
            <p v-else-if="!basketId">
              {{ $t('It\'s time to create your headshots! An invoice has been sent to your email.') }}
            </p>
            <p v-else>
              Thanks for your purchase! If you purchased new styles, they should be processed within 2 hours. If you purchased new remix credits, they should be already available in your account.
            </p>
          </div>

          <div class="mt-5">
            <button
              type="button"
              class="text-base font-bold text-white rounded-lg shadow-sm bg-primary-500 py-2.5 px-4 transition-all duration-150 hover:bg-opacity-90 flex items-center justify-center w-full"
            >
              <template v-if="isTeam">
                Redirecting to dashboard... ({{ timer }}s)
              </template>
              <template v-else-if="isTeamInvoice">
                Redirecting to homepage... ({{ timer }}s)
              </template>
              <template v-else-if="!basketId">
                {{ $t('redirecting', { seconds: timer }) }}
              </template>
              <template v-else>
                {{ $t('simpleRedirecting', { seconds: timer }) }}
              </template>
            </button>
            <nuxt-link v-if="showRedirect && canContinue" :to="redirectPath" class="text-xs underline text-gray-600 mt-2 block">
              Click here if it takes too long.
            </nuxt-link>
          </div>
        </div>
      </div>
    </section>
  </PrecheckoutWrapper>
</template>

<script>
/* eslint-disable */
export default {
  layout: 'empty',
  head () {
    return {
      title: this.$t('title') + ' | HeadshotPro',
    }
  },
  data () {
    return {
      showRedirect: false,
      timer: 5,
      interval: null,
      canContinue: false
    }
  },
  computed: {
    sessionId () {
      return this.$route.query.session_id
    },
    basketId () {
      return this.$route.query.paymentIntentId
    },
    modelId () {
      return this.$route.query.modelId
    },
    isTeam () {
      return this.$route.query.type === 'team'
    },
    isTeamInvoice () {
      return this.$route.query.type === 'team-invoice'
    },
    redirectPath () {
      if (this.isTeamInvoice) {
        return this.localePath('/')
      }

      if (this.isTeam) {
        return this.localePath('/app/admin/team/new/invite-team')
      }

      if (this.basketId && this.$route.query.productType === 'upsell') {
        return this.localePath(`/app/results/${this.modelId}`)
      }

      return this.localePath('/app/add')
    }
  },
  async mounted () {
    try {
      if (this.$confetti) {
        this.$confetti.start({
          particles: [
            { type: 'circle' },
            { type: 'heart' },
            { type: 'rect' },
          ],
          defaultDropRate: 20,
          defaultColors: [
            '#77B7E5',
            '#9F9EB8',
            '#9ADADD',
          ]
        })
      }
      
      await this.trackEcommerce()
      this.startRedirectTimer()
    } catch (error) {
      console.error('Error in mounted:', error)
      this.canContinue = true
      this.startRedirectTimer()
    }
  },
  beforeDestroy () {
    if (this.interval) {
      clearInterval(this.interval)
    }
    if (this.$confetti) {
      this.$confetti.stop()
    }
  },
  methods: {
    startRedirectTimer() {
      this.interval = setInterval(() => {
        this.timer -= 1
        if (this.timer === 2) {
          if (this.$confetti) {
            this.$confetti.stop()
          }
        }

        if (this.timer <= 0) {
          if (this.canContinue) {
            if (this.interval) {
              clearInterval(this.interval)
            }

            const { productType } = this.$route.query

            // Handle package redirect
            if (productType === 'package') {
              this.$router.push(this.redirectPath)
              return
            }
            // Handle upsell redirect
            if (productType === 'upsell') {
              this.$router.push(this.localePath(this.modelId ? `/app/results/${this.modelId}?payment=success` : '/app'))
              return
            }

            if (productType === 'studiocredits') {
              this.$router.push('/studio')
              return
            }
          }

          this.showRedirect = true
        }
      }, 1000)
    },
    async trackEcommerce() {
      try {
        const { unit_amount: unitAmount, id, quantity, name, currency, productType = 'package', localPriceConvertedToUsd, transactionId } = this.$route.query

        if (!transactionId) {
          this.canContinue = true
          return
        }

        const totalAmount = (parseInt(unitAmount) * parseInt(quantity || 1) / 100)

        const paymentIntent = await this.$axios.$get(`/transaction/single/${transactionId}`)

        if (!paymentIntent || paymentIntent?.data?.isTracked === true) {
          this.canContinue = true
          return
        }

        

        if (this.$posthog) {
          this.$posthog.capture('$funnel:purchase', {
            id: transactionId,
            value: localPriceConvertedToUsd ? (parseInt(localPriceConvertedToUsd) / 100) : totalAmount, // Use USD value converted from local currency to USD
            localValue: totalAmount,
            quantity: parseInt(quantity) || 1,
            products: [{
              name,
              id,
              price: (parseInt(unitAmount) / 100),
              quantity: parseInt(quantity) || 1
            }],
            currency: (currency && currency.length > 0) ? currency.toUpperCase() : 'USD',
            team: (this.$route.query?.type === 'team') ? true : false,
            product: (productType === 'package') ? 'package' : 'upsell',
            name: name,
            hasVoted: Object.keys(localStorage).find(key => key.includes('voterToken')) ? true : false
          })
        }

        if (this.$gtm) {
          this.$gtm.push({
            event: 'purchase',
            email: paymentIntent?.data?.email || null,
            ecommerce: {
              purchase: {
                actionField: {
                  id: transactionId,
                  revenue: totalAmount,
                  currency: (currency && currency.length > 0) ? currency.toUpperCase() : 'USD'
                },
                products: [{
                  name,
                  id,
                  price: (parseInt(unitAmount) / 100),
                  quantity: parseInt(quantity) || 1
                }]
              }
            }
          })
        }

        this.canContinue = true
      } catch (error) {
        console.error('Error tracking ecommerce:', error)
        this.canContinue = true
      }
    },
  }
}
</script>

<i18n>
  {
    "en": {
      "Thank you for your purchase!": "Thank you for your purchase!",
      "It's time to create your headshots! An invoice has been sent to your email.": "It's time to create your headshots! An invoice has been sent to your email.",
      "redirecting": "Redirecting to upload photos ({seconds}s)",
      "Click here if it takes too long.": "Click here if it takes too long.",
      "title": "Checkout success",
      "simpleRedirecting": "Redirecting ({seconds}s)"
    },
    "es": {
      "Thank you for your purchase!": "¡Gracias por tu compra!",
      "It's time to create your headshots! An invoice has been sent to your email.": "¡Es hora de crear tus fotos! Hemos enviado una factura a tu correo electrónico.",
      "redirecting": "Redirigiendo a subir fotos ({seconds}s)",
      "Click here if it takes too long.": "Haz clic aquí si tarda demasiado.",
      "title": "Éxito en la compra",
      "simpleRedirecting": "Redirigiendo ({seconds}s)"
    },
    "de": {
      "Thank you for your purchase!": "Vielen Dank für deinen Kauf!",
      "It's time to create your headshots! An invoice has been sent to your email.": "Zeit für deine Bewerbungsfotos! Eine Rechnung wurde an deine E-Mail gesendet.",
      "redirecting": "Weiterleitung zum Foto-Upload ({seconds}s)",
      "Click here if it takes too long.": "Hier klicken, falls es zu lange dauert.",
      "title": "Kauf erfolgreich",
      "simpleRedirecting": "Weiterleitung ({seconds}s)"
    }
  }
</i18n>
