<template>
  <PrecheckoutWrapper>
    <section class="py-8 sm:py-12">
      <div class="max-w-screen-xl px-5 mx-auto sm:px-6 lg:px-8">
        <div class="max-w-sm mx-auto md:text-center">
          <svg
            class="mx-auto size-12 text-red-500"
            width="48"
            height="48"
            viewBox="0 0 48 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M18.5834 17.4997C18.5834 18.098 18.0983 18.583 17.5 18.583C16.9017 18.583 16.4167 18.098 16.4167 17.4997M18.5834 17.4997C18.5834 16.9014 18.0983 16.4163 17.5 16.4163C16.9017 16.4163 16.4167 16.9014 16.4167 17.4997M18.5834 17.4997H16.4167M31.5834 17.4997C31.5834 18.098 31.0984 18.583 30.5 18.583C29.9017 18.583 29.4167 18.098 29.4167 17.4997M31.5834 17.4997C31.5834 16.9014 31.0984 16.4163 30.5 16.4163C29.9017 16.4163 29.4167 16.9014 29.4167 17.4997M31.5834 17.4997H29.4167M34.5 35.5C34.5 35.5 31.6246 26.5 23.5 26.5C15.3755 26.5 13 35.5 13 35.5M24 45.6663C12.0339 45.6663 2.33337 35.9658 2.33337 23.9997C2.33337 12.0335 12.0339 2.33301 24 2.33301C35.9662 2.33301 45.6667 12.0335 45.6667 23.9997C45.6667 35.9658 35.9662 45.6663 24 45.6663Z" stroke="currentColor" stroke-width="4.5" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
          <h1 class="mt-4 text-xl font-bold tracking-tight text-red-500">
            {{ $t('Payment unsuccessful') }}
          </h1>
          <p class="mt-2 text-base font-medium text-gray-600 md:max-w-sm md:mx-auto">
            {{ $t('Your payment was unsuccessful. Please, try again or reach out to support if there was an error.') }}
          </p>

          <div class="mt-5">
            <button
              type="button"
              class="text-base font-bold text-white rounded-lg shadow-sm bg-primary-500 py-2.5 px-4 transition-all duration-150 hover:bg-opacity-90 flex items-center justify-center w-full"
            >
              {{ $t('Going back in', { seconds: timer }) }}
            </button>
            <nuxt-link v-if="showRedirect" :to="localePath('/checkout')" class="text-xs underline text-gray-600 mt-2 block">
              {{ $t('Click here if it takes too long.') }}
            </nuxt-link>
          </div>
        </div>
      </div>
    </section>
    <MetaMain
      :title="$t('title')"
    />
  </PrecheckoutWrapper>
</template>

<script>
import PosthogMixin from '@/mixins/PosthogMixin'
export default {
  mixins: [PosthogMixin],
  layout: 'empty',
  data () {
    return {
      showRedirect: false,
      timer: 3,
      interval: null
    }
  },
  head () {
    return {
      title: this.$t('title') + ' | HeadshotPro'
    }
  },
  mounted () {
    this.interval = setInterval(() => {
      this.timer -= 1

      if (this.timer === 0) {
        this.interval && clearInterval(this.interval)

        const { priceId, modelId, paymentIntentId, productId } = this.$route.query
        if (productId === 'package') {
          this.$router.push(this.localePath('/checkout?priceId=' + priceId + '&productId=' + productId))
        } else if (productId === 'upsell') {
          if (modelId) {
            this.$router.push(this.localePath('/app/results/' + modelId + '/unlock-more?paymentIntentId=' + paymentIntentId + '&payment=failed'))
          } else {
            this.$router.push(this.localePath('/app'))
          }
        } else {
          this.$router.push(this.localePath('/app'))
        }
        this.showRedirect = true
      }
    }, 1000)
  },
  beforeDestroy () {
    this.interval && clearInterval(this.interval)
  }
}
</script>

<i18n>
  {
    "en": {
      "Payment unsuccessful": "Payment unsuccessful",
      "Your payment was unsuccessful. Please, try again or reach out to support if there was an error.": "Your payment was unsuccessful. Please, try again or reach out to support if there was an error.",
      "Going back in": "Going back in {seconds}s",
      "title": "Checkout Failed",
      "Click here if it takes too long.": "Click here if it takes too long."
    },
    "es": {
      "Payment unsuccessful": "Pago no exitoso",
      "Your payment was unsuccessful. Please, try again or reach out to support if there was an error.": "Tu pago no fue exitoso. Por favor, inténtalo de nuevo o ponte en contacto con el soporte si hubo un error.",
      "Going back in": "Volviendo en {seconds}s",
      "title": "Pago fallido",
      "Click here if it takes too long.": "Haz clic aquí si tarda demasiado."
    },
    "de": {
      "Payment unsuccessful": "Zahlung fehlgeschlagen",
      "Your payment was unsuccessful. Please, try again or reach out to support if there was an error.": "Deine Zahlung war nicht erfolgreich. Bitte versuche es erneut oder kontaktiere den Support, falls ein Fehler aufgetreten ist.",
      "Going back in": "Zurück in {seconds}s",
      "title": "Zahlung fehlgeschlagen",
      "Click here if it takes too long.": "Hier klicken, falls es zu lange dauert."
    }
  }
</i18n>
