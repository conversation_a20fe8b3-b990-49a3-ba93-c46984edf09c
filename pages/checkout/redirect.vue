<template>
  <PrecheckoutWrapper>
    <section class="py-8 sm:py-12">
      <div class="max-w-screen-xl px-5 mx-auto sm:px-6 lg:px-8">
        <div class="max-w-sm mx-auto md:text-center">
          <LoadingSpinner class="mx-auto size-12 text-primary-500" />

          <h1 class="mt-4 text-xl font-bold tracking-tight text-primary-500">
            {{ $t('Processing your payment...') }}
          </h1>

          <div class="mt-2 text-base font-medium text-gray-600 md:max-w-sm md:mx-auto">
            <p>{{ $t('Please wait while we verify your payment status.') }}</p>
          </div>

          <div class="mt-5">
            <div class="text-base font-bold text-white rounded-lg shadow-sm bg-primary-500 py-2.5 px-4 w-full">
              {{ $t('Checking payment status...') }} ({{ timer }}s)
            </div>
          </div>

          <!-- Show error if payment check fails -->
          <div v-if="hasError" class="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p class="text-sm text-red-600">
              {{ $t('Unable to verify payment status. Redirecting to checkout...') }}
            </p>
          </div>
        </div>
      </div>
    </section>
  </PrecheckoutWrapper>
</template>

<script>
export default {
  layout: 'empty',
  data () {
    return {
      timer: 5,
      interval: null,
      hasError: false,
      checkInterval: null
    }
  },
  head () {
    return {
      title: this.$t('Processing Payment') + ' | HeadshotPro'
    }
  },
  computed: {
    // Get query parameters from HitPay redirect
    referenceId () {
      return this.$route.query.reference || this.$route.query.payment_request_id
    },
    transactionId () {
      return this.$route.query.transactionId
    },
    hitpayStatus () {
      return this.$route.query.status
    }
  },
  mounted () {
    try {
      // Start the timer countdown
      this.startTimer()

      // Wait 5 seconds before checking payment status
      setTimeout(async () => {
        await this.checkPaymentStatus()
      }, 5000)
    } catch (error) {
      console.error('Error in mounted:', error)
      this.handleError()
    }
  },
  beforeDestroy () {
    if (this.interval) {
      clearInterval(this.interval)
    }
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
    }
  },
  methods: {
    startTimer () {
      this.interval = setInterval(() => {
        this.timer -= 1
        if (this.timer <= 0) {
          if (this.interval) {
            clearInterval(this.interval)
          }
        }
      }, 1000)
    },

    async checkPaymentStatus () {
      try {
        let paymentIntent = null

        // First try to get payment intent by transaction ID if available
        if (this.transactionId) {
          try {
            const response = await this.$axios.$get(`/transaction/single/${this.transactionId}`)
            if (response.success && response.data) {
              paymentIntent = response.data
            }
          } catch (error) {
            console.log('Error fetching by transaction ID:', error)
          }
        }

        // If we have a payment intent, check its status
        if (paymentIntent) {
          if (paymentIntent.paymentStatus === 'paid') {
            this.redirectToSuccess()
            return
          } else if (paymentIntent.paymentStatus === 'failed' || paymentIntent.paymentStatus === 'cancelled') {
            this.redirectToFailed()
            return
          }
        }

        // If HitPay already provided a status, use that
        if (this.hitpayStatus) {
          if (this.hitpayStatus === 'completed' || this.hitpayStatus === 'paid') {
            this.redirectToSuccess()
            return
          } else if (this.hitpayStatus === 'failed' || this.hitpayStatus === 'cancelled') {
            this.redirectToFailed()
            return
          }
        }

        // If we still don't have a clear status, try checking HitPay directly
        if (this.referenceId) {
          try {
            const response = await this.$axios.$get(`/checkout/hitpay/payment-status/${this.referenceId}`)
            if (response.success && response.data) {
              const hitpayPayment = response.data
              if (hitpayPayment.status === 'completed' || hitpayPayment.status === 'paid') {
                this.redirectToSuccess()
                return
              } else if (hitpayPayment.status === 'failed' || hitpayPayment.status === 'cancelled') {
                this.redirectToFailed()
                return
              }
            }
          } catch (error) {
            console.log('Error checking HitPay status:', error)
          }
        }

        // If no clear status found, default to failed
        this.redirectToFailed()
      } catch (error) {
        console.error('Error checking payment status:', error)
        this.handleError()
      }
    },

    redirectToSuccess () {
      const queryParams = { ...this.$route.query }
      delete queryParams.reference
      delete queryParams.status
      delete queryParams.payment_request_id

      const queryString = Object.keys(queryParams).length > 0
        ? '?' + new URLSearchParams(queryParams).toString()
        : ''

      this.$router.replace(`/checkout/success${queryString}`)
    },

    redirectToFailed () {
      const queryParams = { ...this.$route.query }
      delete queryParams.reference
      delete queryParams.status
      delete queryParams.payment_request_id

      const queryString = Object.keys(queryParams).length > 0
        ? '?' + new URLSearchParams(queryParams).toString()
        : ''

      this.$router.replace(`/checkout/failed${queryString}`)
    },

    handleError () {
      this.hasError = true
      setTimeout(() => {
        // Redirect to checkout page as fallback
        this.$router.replace('/checkout')
      }, 3000)
    }
  }
}
</script>

<i18n>
{
  "en": {
    "Processing your payment...": "Processing your payment...",
    "Please wait while we verify your payment status.": "Please wait while we verify your payment status.",
    "Checking payment status...": "Checking payment status...",
    "Unable to verify payment status. Redirecting to checkout...": "Unable to verify payment status. Redirecting to checkout...",
    "Processing Payment": "Processing Payment"
  },
  "es": {
    "Processing your payment...": "Procesando tu pago...",
    "Please wait while we verify your payment status.": "Por favor espera mientras verificamos el estado de tu pago.",
    "Checking payment status...": "Verificando estado del pago...",
    "Unable to verify payment status. Redirecting to checkout...": "No se pudo verificar el estado del pago. Redirigiendo al checkout...",
    "Processing Payment": "Procesando Pago"
  }
}
</i18n>
