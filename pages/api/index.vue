<template>
  <div>
    <Header />
    <section class="bg-[#F8FCFF] py-8 sm:py-12">
      <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8 2xl:px-0">
        <div
          class="max-w-4xl p-6 sm:p-8 md:px-16 md:py-12 xl:px-24 xl:py-16 mx-auto shadow-[0px_0px_75px_0px_rgba(0,0,0,0.07)] bg-white border rounded-xl border-primary-500/15"
        >
          <h1 class="text-2xl sm:text-4xl xl:text-[42px] leading-tight font-bold tracking-tighter text-primary-500">
            API Documentation (WIP)
          </h1>
          <NuxtContent
            class="prose text-[#474368] mx-auto mt-4 prose-headings:text-primary-500"
            :document="article"
          />
        </div>
      </div>
      <MetaMain
        :title="title"
        :description="description"
      />
    </section>
    <MarketingReviewCTA class="py-12" />
    <MarketingFooter />
  </div>
</template>

<script>
export default {
  layout: 'default',
  async asyncData ({ $content, params, error }) {
    try {
      const article = await $content('api', 'index').fetch()

      return {
        article
      }
    } catch (err) {
      error({ statusCode: 404, message: 'Post not found' })
    }
  },
  head () {
    return {
      title: this.title,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.description
        },
        {
          hid: 'twitter:title',
          name: 'twitter:title',
          content: this.title
        },
        {
          hid: 'twitter:creator',
          name: 'twitter:creator',
          content: '@dannypostmaa'
        },
        {
          hid: 'twitter:description',
          name: 'twitter:description',
          content: this.description
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.title
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.description
        }
      ],
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com/api'
        }
      ]
    }
  },
  computed: {
    title () {
      return 'API Documentation | HeadshotPro'
    },
    description () {
      return 'API Documentation for HeadshotPro. Easily integrate our API into your application.'
    }
  }
}
</script>
