<template>
  <div class="flex flex-col min-h-screen">
    <Header />
    <main class="flex flex-col flex-1">
      <!-- Test Hero-->
      <LandingpageV3Hero class="hidden md:block" />
      <!-- <LandingpageV2Hero v-else class="hidden md:block" /> -->
      <!-- End Test Hero-->
      <LandingpageV2MobileHero class="block md:hidden" />
      <LandingpageV2Stats />
      <MarketingReviews />
      <LandingpageV2Bottom />
      <MarketingFooter />
    </main>
  </div>
</template>

<script>
import PosthogMixin from '@/mixins/PosthogMixin'
import adHeadlines from '@/assets/ad-title.json'
export default {
  mixins: [PosthogMixin],
  data () {
    return {
      adHeadlines,
      isInTest: false
    }
  },
  head () {
    return {
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com' + this.$route.path
        },
        {
          rel: 'alternate',
          hreflang: 'es',
          href: 'https://www.headshotpro.com/es'
        },
        {
          rel: 'alternate',
          hreflang: 'en',
          href: 'https://www.headshotpro.com'
        },
        {
          rel: 'alternate',
          hreflang: 'de',
          href: 'https://www.headshotpro.com/de'
        },
        {
          rel: 'alternate',
          hreflang: 'x-default',
          href: 'https://www.headshotpro.com'
        }
      ],
      title: this.seoTitle,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.seoDescription
        }
      ]
    }
  },
  computed: {
    countryCode () {
      return this.$store.state.user.countryCode
    },
    isMobile () {
      return this.screenWidth < 768
    },
    reviews () {
      return this.$store.state.reviews
    },
    headline () {
      const { title } = this.$route.query
      if (!title) { return this.$t('Professional business headshots, without a physical photo shoot') }
      const headline = adHeadlines.filter((headline) => {
        return headline.title === title
      })[0].headline

      if (headline && headline.length > 0) {
        return headline
      } else {
        return this.$t('Professional business headshots, without a physical photo shoot')
      }
    },
    seoTitle () {
      return this.$t('The #1 AI Headshot Generator for Professional Headshots')
    },
    seoDescription () {
      return this.$t('The AI headshot generator used by Fortune 500 companies. Create professional grade AI headshots with HeadshotPro. Best for professional business headshots.')
    }
  },
  watch: {
    countryCode (newValue, oldValue) {
      if (newValue === 'DE') {
        this.$posthog.startSessionRecording(true)
      }
    }
  },

  mounted () {
    this.$posthog.capture('$page:homepage')
  }
}
</script>
<style></style>

<i18n>
  {
    "es": {
      "Professional business headshots, without a physical photo shoot": "Fotos profesionales para empresas, sin una sesión física",
      "The #1 AI Headshot Generator for Professional Headshots": "El generador de fotos de perfil profesionales con IA #1",
      "The AI headshot generator used by Fortune 500 companies. Create professional grade AI headshots with HeadshotPro. Best for professional business headshots.": "El generador de fotos de perfil con IA utilizado por empresas Fortune 500. Crea fotos de perfil profesionales con IA usando HeadshotPro. Ideal para fotos corporativas profesionales."
    },
    "de": {
      "Professional business headshots, without a physical photo shoot": "Bewerbungsfotos vom Profi, ohne Studio-Termin",
      "The #1 AI Headshot Generator for Professional Headshots": "Der #1 KI-Generator für professionelle Bewerbungsfotos",
      "The AI headshot generator used by Fortune 500 companies. Create professional grade AI headshots with HeadshotPro. Best for professional business headshots.": "Der KI-Bewerbungsfoto-Generator, der von Fortune-500-Unternehmen verwendet wird. Erstelle professionelle KI-Bewerbungsfotos mit HeadshotPro. Ideal für professionelle Business-Fotos."
    },
    "en": {
      "The #1 AI Headshot Generator for Professional Headshots": "The #1 AI Headshot Generator for Professional Headshots",
      "The AI headshot generator used by Fortune 500 companies. Create professional grade AI headshots with HeadshotPro. Best for professional business headshots.": "The AI headshot generator used by Fortune 500 companies. Create professional grade AI headshots with HeadshotPro. Best for professional business headshots."
    }
  }
</i18n>
