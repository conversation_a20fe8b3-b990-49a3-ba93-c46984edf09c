<template>
  <div>
    <Header />
    <section class="bg-[#F8FCFF] py-8 sm:py-12">
      <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8 2xl:px-0">
        <div
          class="max-w-4xl p-6 sm:p-8 md:px-16 md:py-12 xl:px-24 xl:py-16 mx-auto shadow-[0px_0px_75px_0px_rgba(0,0,0,0.07)] bg-white border rounded-xl border-primary-500/15"
        >
          <h1 class="text-2xl sm:text-4xl xl:text-[42px] leading-tight font-bold tracking-tighter text-primary-500">
            {{ article.title }}
          </h1>
          <p class="mt-4 text-base font-medium sm:text-lg text-[#474368]">
            {{ article.description }}
          </p>
          <NuxtContent
            class="prose text-[#474368] mx-auto mt-4 prose-headings:text-primary-500"
            :document="article"
          />
        </div>
      </div>
      <MetaMain
        :title="article.title"
        :description="article.description"
      />
    </section>
    <MarketingFooter />
  </div>
</template>

<script>
export default {
  async asyncData ({ $content, params, error }) {
    try {
      const article = await $content('legal', params.file).fetch()

      return {
        article
      }
    } catch (err) {
      error({ statusCode: 404, message: 'Post not found' })
    }
  },

  head () {
    return {
      title: this?.article?.title || 'Legal | HeadshotPro',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this?.article?.description || null
        }
      ],
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com/legal/' + this.$route.params.file
        }
      ]
    }
  }
}
</script>
