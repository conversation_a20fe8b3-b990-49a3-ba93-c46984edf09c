<template>
  <div>
    <Header />
    <div class="bg-gray-100 p-2 md:p-0">
      <section class="min-h-screen md:py-16">
        <div class="mx-auto max-w-3xl p-4 md:p-12  bg-white">
          <div class="max-w-3xl mx-auto xl:max-w-4xl  ">
            <div class="flex items-center justify-between space-x-2">
              <h1 class="text-3xl font-bold text-gray-900 sm:text-4xl xl:text-3xl">
                {{ article.title }}
              </h1>
              <img :src="article.avatar" class="w-12 h-12 rounded-full">
            </div>
            <div class="prose mx-auto mt-8">
              <h3>Latest articles</h3>
              <ul>
                <li v-for="post in latestArticles" :key="post.slug">
                  <nuxt-link :to="`/headshot-types/${post.slug}-headshots`">
                    {{ post.title }}
                  </nuxt-link>
                </li>
              </ul>
            </div>
            <NuxtContent
              class="prose mx-auto mt-8"
              :document="article"
            />
          </div>
        </div>
      </section>
    </div>
    <MarketingReviewCTA class="pb-24 pt-12" />
    <MarketingFooter />
  </div>
</template>

<script>
export default {
  async asyncData ({ $content, params, error, $axios }) {
    try {
      const article = await $content('author/' + params.name).fetch()
      const response = await $axios.$get('/blog/all')

      return {
        article,
        latestArticles: response?.data?.filter(post => post.category === 'headshot-types').slice(0, 3) || []
      }
    } catch (err) {
      console.log(err)
      error({ statusCode: 404, message: 'Post not found' })
    }
  },
  head () {
    return {
      title: this.title + ' | HeadshotPro',
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com/author/' + this.$route.params.name
        }
      ],
      script: [
        {
          type: 'application/ld+json',
          json: {
            '@context': 'https://schema.org',
            '@type': 'Person',
            name: 'Danny Postma',
            url: 'https://twitter.com/dannypostmaa',
            jobTitle: 'Founder of HeadshotPro',
            image: require('@/assets/img/avatar-danny.jpeg')
          }
        }
      ],
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.description
        },
        {
          hid: 'twitter:title',
          name: 'twitter:title',
          content: this.title
        },
        {
          hid: 'twitter:description',
          name: 'twitter:description',
          content: this.description
        },
        {
          hid: 'twitter:image:alt',
          name: 'twitter:image:alt',
          content: this.title
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.title
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.description
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        },
        {
          hid: 'thumbnail',
          name: 'thumbnail',
          content: this.article?.avatar || null
        },
        {
          hid: 'twitter:image:src',
          name: 'twitter:image:src',
          content: this.article?.avatar || null
        },
        {
          hid: 'twitter:image',
          name: 'twitter:image',
          content: this.article?.avatar || null
        },
        {
          hid: 'og:image',
          property: 'og:image',
          content: this.article?.avatar || null
        },
        {
          hid: 'og:image:secure_url',
          property: 'og:image:secure_url',
          content: this.article?.avatar || null
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        }
      ]
    }
  },
  computed: {
    title () {
      return 'Danny Postma: Founder of HeadshotPro'
    },
    description () {
      return this.article.description
    }
  }
}
</script>
