<template>
  <div class="w-full min-h-screen bg-gradient-to-t from-[#F1FCFB] to-white">
    <Header class="z-10 relative" />
    <div class="max-w-6xl mx-auto py-12 px-4 md:px-12">
      <template v-if="isLoggedIn">
        <div class="flex flex-col items-center justify-center">
          <h1 class="text-3xl font-bold text-primary-500">
            Oops! You're already logged in.
          </h1>
          <p class="text-lg text-[#0F172A] font-medium mt-4">
            Referral program is only available for new users who haven't made a purchase yet.
          </p>
        </div>
      </template>
      <template v-else-if="referralData === false">
        <div class="flex flex-col items-center justify-center">
          <h1 class="text-3xl font-bold text-primary-500">
            Invalid referral code
          </h1>
          <p class="text-lg text-[#0F172A] font-medium mt-4">
            Please check the referral code and try again.
          </p>
        </div>
      </template>
      <template v-else-if="referralData">
        <div class="flex flex-col items-center justify-center space-y-10">
          <div class="max-w-xl text-center space-y-4">
            <h1 class="max-w-md mx-auto text-3xl font-bold text-primary-500">
              {{ title }}
            </h1>

            <p class="text-base text-primary-500 font-medium">
              Because you were invited by {{ referralData?.name }}, you'll get <strong>$9 off</strong> your purchase if you make a purchase in the next 7 days.
            </p>
            <div class="w-full flex items-center justify-center">
              <nuxt-link :to="localePath('/app/add')" @click.native="showMobileMenu = false">
                <ButtonPrimary class="bg-orange-500">
                  Create your headshots
                  <IconSmallArrow class="flex-shrink-0 w-5 h-5 text-white ml-1.5" />
                </ButtonPrimary>
              </nuxt-link>
            </div>
          </div>
          <div class="space-y-4">
            <p class="text-base font-bold text-primary-500 text-center">
              Here are a few of their favorite photos:
            </p>
            <div class="grid grid-cols-1 2xs:grid-cols-3 gap-4 w-full">
              <div v-for="photo in referralData.photos" :key="photo._id" class="group overflow-hidden rounded-xl">
                <ImageDns :src="photo.thumbnail" alt="Referral photo" class="w-full object-cover aspect-result transition-all duration-300 group-hover:scale-105" />
              </div>
            </div>
          </div>
          <div class="space-y-4 text-center max-w-lg mx-auto">
            <!-- <p class="text-base text-primary-500 font-medium">
              Because you were invited by {{ referralData?.name }}, you'll get $9 off your purchase if you make a purchase in the next 7 days.
            </p> -->
            <p class="text-lg text-[#0F172A] font-medium mt-4">
              {{ description }}
            </p>
          </div>
          <div class="flex flex-row items-center justify-center space-x-4">
            <div class="hidden lg:flex -space-x-3 overflow-hidden">
              <img class="inline-block h-8 w-8 rounded-full ring-[3px] ring-white" :src="require('@/assets/img/avatar-01.png')" alt="">
              <img class="inline-block h-8 w-8 rounded-full ring-[3px] ring-white" :src="require('@/assets/img/avatar-02.png')" alt="">
              <img class="inline-block h-8 w-8 rounded-full ring-[3px] ring-white" :src="require('@/assets/img/avatar-05.png')" alt="">
            </div>
            <div class="flex flex-row lg:flex-col items-start justify-start space-x-4 md:space-x-0 md:space-y-0.5">
              <span class="text-primary-500 font-bold text-base">Used by more than {{ $store.state.stats.users }} happy customers</span>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      referralData: null
    }
  },
  head () {
    return {
      title: this.title + ' | HeadshotPro',
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com/invite/' + this.$route.params.id
        }
      ],
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.description
        },
        {
          hid: 'twitter:title',
          name: 'twitter:title',
          content: this.title
        },
        {
          hid: 'twitter:description',
          name: 'twitter:description',
          content: this.description
        },
        {
          hid: 'twitter:image:alt',
          name: 'twitter:image:alt',
          content: this.title
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.title
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.description
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        }
      ]
    }
  },
  computed: {
    title () {
      return `${this.referralData?.name || 'Your friend'} recommends you to try HeadshotPro`
    },
    description () {
      if (!this.referralData) {
        return ''
      }

      const packageId = this.referralData?.package || 'medium'
      const packageName = this.packages[packageId]?.title || 'default'

      return `${this.referralData?.name} chose the ${packageName} package for ${this.getLocalizedPrice(packageId, true, 0, false)}.`
    },
    happyCustomers () {
      return Math.round(this.$store.state.stats.users.replace(/,/g, '') / 1000) + ',000+'
    }
  },
  mounted () {
    if (!this.$store.state.packages || this.$store.state.packages.length === 0) {
      this.$store.dispatch('getPackages')
    }

    this.loadReferralData()
  },
  methods: {
    async loadReferralData () {
      this.$loading.show({ title: 'Loading referral data...' })
      try {
        const { id } = this.$route.params
        const referralData = await this.$axios.$get(`/referral/details?referralCode=${id}`)
        if (referralData.success) {
          this.referralData = referralData.data
          this.trackReferralProgram(id)
        }
      } catch (error) {
        console.error('Error loading referral data:', error)
        this.handleError(error)
      } finally {
        this.$loading.hide()
      }
    }
  }
}
</script>
