<template>
  <div class="flex flex-col min-h-screen isolate">
    <Header />

    <!-- START MAIN-->
    <main class="flex-1">
      <LandingpageTypesHero
        v-if="content?.hero"
        :title="content.hero.title"
        :subtitle="content.hero.subtitle"
        :usps="content.hero?.usps"
      />
      <LandingpageTypesQA
        v-if="content?.qa"
        :blocks="content.qa.blocks"
      />
      <LandingpageTypesReviews />
      <!-- <LandingpageTypesComparison
        :title="content?.comparison?.title"
        :subtitle="content?.comparison?.subtitle"
      /> -->
      <LandingpageTypesPricing
        :title="content?.pricing?.title"
        :content="content?.pricing?.content"
      />
      <LandingpageTypesAlsoUsing
        :blocks="content?.also?.blocks"
      />
    </main>
    <MarketingFooter />
  </div>
</template>

<script>
export default {
  async asyncData ({ $content, route, error }) {
    const headshotTypesJSONContent = await import('@/assets/headshot-types.json')
    const slug = route.params.slug.replace('-headshots', '')
    const content = headshotTypesJSONContent[slug]
    if (!content) {
      // Trigger Nuxt's error handling with a 404 status
      error({ statusCode: 404, message: 'Page not found' })
    } else {
      return { content }
    }
  },
  head () {
    return {
      title: this.title,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.description
        },
        {
          hid: 'twitter:title',
          name: 'twitter:title',
          content: this.title
        },
        {
          hid: 'twitter:creator',
          name: 'twitter:creator',
          content: '@dannypostmaa'
        },
        {
          hid: 'twitter:description',
          name: 'twitter:description',
          content: this.description
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.title
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.description
        }
      ],
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com/headshot-types/' + this.$route.params.slug
        }
      ]
    }
  },
  computed: {
    descrption () {
      return this.content?.hero?.subtitle || 'Headshot Types'
    },
    title () {
      return `${this.capitalizeWords(this.$route.params.slug).replace('Headshots', '')} Headshots 101: Examples, Cost, & Tips`
    }
  },
  methods: {
    capitalizeWords (str) {
      return str.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')
    }
  }
}
</script>

<style>

</style>
