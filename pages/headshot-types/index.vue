<template>
  <div class="flex flex-col min-h-screen">
    <Header />
    <main class="flex-1">
      <!-- START HERO -->
      <section class="bg-[#F8FCFF] py-12 sm:py-16">
        <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8 2xl:px-0">
          <div class="text-left md:text-center">
            <p class="text-sm sm:text-base font-normal text-paragraph tracking-[-0.3px]">
              Headshot Types
            </p>
            <h2
              class="mt-3 text-2xl font-bold tracking-[-1.05px] sm:text-3xl lg:text-[42px] text-primary-500 lg:leading-[48px]"
            >
              AI Headshots for Professionals
            </h2>
            <p class="mt-3 text-base font-medium text-[#474368] sm:text-lg lg:text-xl md:mx-auto md:max-w-2xl">
              Hundreds of business professionals of all kinds use HeadshotPro every day to generate a professional
              headshot they can
              use at work.
            </p>

            <div class="mt-4 lg:hidden">
              <a
                href="#"
                title=""
                class="text-base w-full sm:w-auto inline-flex font-bold text-white rounded-lg shadow-sm bg-primary-500 pt-3 pb-3.5 px-12 h-12 border border-transparent transition-all duration-150 hover:bg-opacity-90 gap-1.5 items-center justify-center disabled:bg-opacity-20"
                role="button"
              >
                Get your headshots now
                <svg class="size-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                  <path
                    fill-rule="evenodd"
                    d="M12.97 3.97a.75.75 0 0 1 1.06 0l7.5 7.5a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 1 1-1.06-1.06l6.22-6.22H3a.75.75 0 0 1 0-1.5h16.19l-6.22-6.22a.75.75 0 0 1 0-1.06Z"
                    clip-rule="evenodd"
                  />
                </svg>
              </a>
            </div>

            <div class="flex items-center gap-3 mt-4 lg:hidden">
              <img class="w-auto h-6" src="images/ratings-4.png" alt="">
              <img class="w-auto h-6" src="images/logo-trustpilot.png" alt="">
            </div>
          </div>

          <div class="grid grid-cols-1 gap-4 mt-12 sm:grid-cols-2 xl:grid-cols-3 sm:mt-16">
            <div
              v-for="(block, index) in blocks"
              :key="block.slug"
              class="p-6 bg-white border shadow-[0_0px_75px_0px_rgba(0,0,0,0.07)] rounded-lg border-primary-500/15 space-y-6"
            >
              <div class="">
                <nuxt-link
                  :to="`/headshot-types/${block.slug}`"
                >
                  <img class="object-cover w-full rounded-lg" :src="examples[index]" alt="">
                </nuxt-link>
              </div>

              <div class="space-y-2">
                <div class="flex gap-2 items-cener">
                  <p class="tracking-[-0.3px] font-bold text-primary-500 text-lg leading-none capitalize">
                    {{ block.slug }} Headshots
                  </p>
                  <!-- <span
                    class="px-2 py-1.5 text-[10px] leading-none font-extrabold  text-white uppercase bg-[#21B8BA] rounded-full"
                  >
                    <span class="hidden lg:inline-block">USED by</span> 32K customers
                  </span> -->
                </div>
                <p class="text-base font-normal text-paragraph tracking-[-0.3px]">
                  {{ block?.hero?.subtitle }}
                </p>
              </div>

              <nuxt-link
                :to="`/headshot-types/${block.slug}`"
                :title="`Learn more about ${block.slug} headshots`"
                class="text-base flex items-center justify-center w-full font-medium text-[#240D0D] border border-[#E6E6E6] hover:opacity-80 transition-all duration-150 bg-[#EEEEEE] rounded-lg px-4 pt-2 pb-2.5 h-10"
                role="button"
              >
                Learn more
              </nuxt-link>
            </div>
          </div>
        </div>
      </section>
      <!-- END HERO -->
    </main>
    <MarketingFooter />
  </div>
</template>

<script>
export default {
  async asyncData ({ $content, route, error }) {
    const headshotTypesJSONContent = await import('@/assets/headshot-types.json')
    const content = headshotTypesJSONContent
    if (!content) {
      // Trigger Nuxt's error handling with a 404 status
      error({ statusCode: 404, message: 'Page not found' })
    } else {
      return { content }
    }
  },
  data () {
    return {
      blocks: [],
      examples: [],
      usedReviews: []
    }
  },
  head () {
    return {
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com/headshot-types'
        }
      ]
    }
  },
  created () {
    const exampleReviews = this.$store.state.reviews.filter(review => review?.image && review.createdAt && this.isCreatedAfterAugust21(review.createdAt))
    for (const key in this.content) {
      if (Object.hasOwnProperty.call(this.content, key)) {
        if (key === 'default') { continue }
        const element = this.content[key]
        this.blocks.push({
          slug: key,
          ...element
        })
        // Get images from reviews
        const notSelectedReviews = exampleReviews.filter(review => !this.usedReviews.includes(review._id))
        const randomReview = notSelectedReviews[Math.floor(Math.random() * notSelectedReviews.length)]
        this.examples.push(randomReview.thumbnail)
        this.usedReviews.push(randomReview._id)
      }
    }
    console.log(this.blocks)
  },
  methods: {

    isCreatedAfterAugust21 (createdAt) {
      const date = new Date(createdAt)
      const august21 = new Date('2024-08-19')
      return date > august21
    }
  }
}
</script>

<style>

</style>
