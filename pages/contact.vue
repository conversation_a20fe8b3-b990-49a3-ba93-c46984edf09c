<template>
  <div class="bg-gray-50">
    <div class="flex flex-col min-h-screen">
      <!-- <template v-if="isInTest">
      <LandingpageV2All />
    </template>
    <template v-else> -->
      <Header />
      <section class="bg-[#F8FCFF] py-8 sm:py-12">
        <div class="max-w-screen-xl px-4 mx-auto sm:px-6 lg:px-8 2xl:px-0">
          <div
            class="max-w-2xl p-6 sm:p-8 md:px-16 md:py-12 xl:px-24 xl:py-16 mx-auto shadow-[0px_0px_75px_0px_rgba(0,0,0,0.07)] bg-white border rounded-xl border-primary-500/15"
          >
            <div class="">
              <h1 class="font-bold text-2xl">
                Contact and support
              </h1>
              <p v-if="$store.state?.user?.uid" class="text-sm text-gray-700">
                When contacting us, please include the following information: <strong class="text-blue-700">{{ $store.state.user.uid }}</strong>.
              </p>
              <p v-else class="text-sm text-gray-700" />
            </div>
            <div class="border-b border-gray-100 py-3.5 mb-2">
              <div class="flex items-start justify-between space-x-2">
                <img src="@/assets/img/support-avatar.png" class="w-12 h-12 rounded-full" alt="Support avatar">
                <div class="flex flex-col space-y-1 text-xs text-gray-700">
                  <span v-if="isSupportOnline" class="text-xs font-medium">
                    Camilla is currently <span class="text-green-500">answering emails.</span>
                  </span>
                  <span v-else class="text-xs font-medium">
                    Camilla will be back again on <span class="text-orange-500">{{ nextOnlineTime }}.</span>
                  </span>
                  <span>You can expect a response from Camilla within the next <span v-if="isSupportOnline" class="font-medium">1-3 business hours</span><span v-else class="font-medium">24 business hours</span>, but usually she's a little quicker than that. Please be nice to Camilla—she's here to help 🙂</span>
                </div>
              </div>
            </div>
            <div class="space-y-2 mt-4">
              <div v-for="(item, index) in faq" :key="`faq-${index}`" role="region" class="bg-gray-50 border border-gray-100 p-4 rounded">
                <h4>
                  <button class="flex w-full items-center justify-between text-left text-sm font-bold text-gray-900" @click="active = index">
                    <span>{{ item.question }}</span>
                    <span v-if="active === index" aria-hidden="true" class="ml-4">
                      <svg class="h-5 w-5 text-gray-900" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                      </svg>
                    </span>
                    <span v-else aria-hidden="true" class="ml-4">
                      <svg class="h-5 w-5 text-gray-900" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                      </svg>
                    </span>
                  </button>
                </h4>
                <collapse-transition>
                  <div v-if="active === index">
                    <div class="space-y-5 pt-4">
                      <p class="text-sm font-normal text-gray-700" v-html="item.answer" />
                    </div>
                  </div>
                </collapse-transition>
              </div>
            </div>
          </div>
        </div>
      </section>
      <MarketingFooter />
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      active: null,
      faq: [
        {
          question: 'Contact us',
          answer: "Send an email to <a href='mailto:<EMAIL>' class='underline text-blue-500'><EMAIL></a> including your customer ID. Our support is available 5 days a week and we respond within 1-24 hours. Contacting us in the weekend? We'll get back to you on Monday."
        },
        {
          question: 'Team enquiries',
          answer: "For enterprise or team purchases of HeadshotPro, or if you need assistance with invoice payments or have general questions, please contact us. For general inquiries, email <a class='underline text-blue-500' href='mailto:<EMAIL>'><EMAIL></a>. For sales and payment-related matters, reach out to <a class='underline text-blue-500' href='mailto:<EMAIL>'><EMAIL></a>. We're committed to providing prompt assistance to meet your needs."
        },
        // {
        //   question: 'Refund',
        //   answer: "To request a refund, please send an email to <a href='mailto:<EMAIL>'><EMAIL></a> with the reason for your refund. We will get back to you within 24 hours."
        // },
        {
          question: 'Refund',
          answer: '<a href="/profile/refund?utm_source=support-modal" class="underline text-blue-500">Request refund</a> by submitting the form. We will process your refund with 48 hours. Cannot access the page? Send an <NAME_EMAIL>.'
        },
        {
          question: 'Invoice and payment',
          answer: "We'll send you an invoice after you've completed the payment. You can also find them under the \"Invoices\" link in the footer."
        },
        {
          question: 'Submit feedback or suggestions',
          answer: "We'd love to hear from you! Please send us an <NAME_EMAIL> and we'll get back to you as soon as possible."
        },
        {
          question: 'Report a bug or technical issue',
          answer: "We're sorry to hear that! Please send us an <NAME_EMAIL> including a detailed explaination of the issue and screenshots/screen recording if possible. We'll get back to you as soon as possible."
        }
      ]
    }
  },
  head () {
    return {
      title: 'Contact us | HeadshotPro',
      link: [
        {
          rel: 'canonical',
          href: 'https://www.headshotpro.com/contact'
        }
      ]
    }
  },
  computed: {
    isSupportOnline () {
      const now = new Date()
      // Convert local time to GMT+8
      const gmt8Time = new Date(now.getTime() + (now.getTimezoneOffset() * 60000) + (8 * 3600000))

      const hours = gmt8Time.getHours()
      const day = gmt8Time.getDay()

      // Support hours are from 13:00 to 17:00 GMT+8
      const isWeekday = day >= 1 && day <= 5 // Monday (1) to Friday (5)
      const isWorkingHours = hours >= 13 && hours < 17

      return isWeekday && isWorkingHours
    },
    nextOnlineTime () {
      const now = new Date()
      const localTime = now.getTime()
      const localOffset = now.getTimezoneOffset() * 60000
      const utc = localTime + localOffset
      const offset = 8 // GMT+8
      const supportTime = utc + (3600000 * offset)
      const supportDate = new Date(supportTime)

      const day = supportDate.getUTCDay()
      let nextOnline

      if (day >= 5) { // If it's Friday or the weekend, set to Monday
        nextOnline = new Date(supportDate)
        nextOnline.setUTCDate(supportDate.getUTCDate() + ((8 - day) % 7))
        nextOnline.setUTCHours(13, 0, 0, 0) // Set to 13:00 GMT+8
      } else {
        nextOnline = new Date(supportDate)
        nextOnline.setUTCHours(13, 0, 0, 0) // Set to 13:00 GMT+8
        if (supportDate.getUTCHours() >= 16) {
          // If after working hours, set to next day
          nextOnline.setUTCDate(supportDate.getUTCDate() + 1)
        }
      }

      // Format the date to a readable format, e.g., "Monday at 1:00 PM"
      return nextOnline.toLocaleString('en-US', { weekday: 'long', hour: 'numeric', minute: 'numeric', hour12: true, timeZone: 'UTC' })
    }
  }
}
</script>

<style>

</style>
