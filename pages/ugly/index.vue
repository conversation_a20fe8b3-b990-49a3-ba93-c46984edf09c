<template>
  <div class="flex flex-col min-h-screen bg-gray-100">
    <Header />
    <main class="flex flex-col flex-1 items-center justify-center  relative  max-w-full overflow-hidden p-2 md:px-0 py-4 md:py-12 2xl:py-24">
      <div class="hero relative z-20 opacity-10" />
      <div class="bg-white/10 backdrop-blur-sm w-full h-full absolute top-0 left-0 z-20" />
      <div class="w-full max-w-xl mx-auto bg-white shadow rounded-md relative z-30 min-h-[300px] flex items-center justify-center">
        <div class="p-8 md:p-12 text-center w-full space-y-3">
          <template v-if="!giveVerdict">
            <template v-if="!isLoading">
              <h1 class="text-3xl mt-4 font-bold  tracking-tight text-brand-500 sm:text-3xl xl:text-3xl sm:tracking-tight">
                Free Headshots for <br class="block md:hidden">Ugly People
              </h1>
              <h3 class="mt-4 text-md font-medium text-gray-800 sm:text-xl">
                Is your profile picture ugly enough?
              </h3>
              <p class="max-w-xl mx-auto mt-6 text-base text-gray-700">
                Upload your current LinkedIn picture to see if you're ugly enough to qualify for free AI headshots
              </p>
              <label for="fileextra">
                <div class="w-[300px] my-4 mx-auto cursor-pointer flex content-center items-center justify-center text-center px-6 py-3 border border-transparent text-base font-bold rounded-md shadow-sm text-white gradient-bg hover:bg-gray-900 hover:border-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-200">
                  <IconPlus class="w-4 h-4 text-white mr-1.5" />
                  <span>Upload your photo</span>
                </div>
              </label>
              <input
                id="fileextra"
                ref="fileextra"
                name="fileextra"
                type="file"
                class="hidden"
                accept="image/png, image/jpeg, image/heic, image/heif, image/jpg"
                @change="addFile"
              >
              <p class="text-sm italic text-gray-700">
                "Finally, a company fighting against beauty privilege" - <strong>Sarah</strong>
              </p>
              <img src="@/static/og-tool-ugly.jpg" alt="Ugly Tool" class="w-full mt-4">
            </template>
            <template v-if="isLoading">
              <LoadingSpinner title="Oh, wow... That's you in this photo? Ok hold on..." />
            </template>
          </template>
          <template v-else>
            <div v-if="photo" class="w-24 h-24 mx-auto rounded-full overflow-hidden">
              <img :src="photo" class="object-cover">
            </div>
            <h3 class="text-sm font-medium">
              Why did you take this test?
            </h3>
            <h1 class="text-2xl mt-4 font-bold  tracking-tight text-brand-500 sm:text-3xl xl:text-3xl sm:tracking-tight">
              You are gorgeous! 😍
            </h1>
            <template v-if="compliment">
              <h3 class="italic font-medium font-serif text-lg pt-2 pb-4">
                "{{ compliment }}"
              </h3>
              <hr>
            </template>
            <p class="max-w-2xl mx-auto mt-6 text-base leading-7 text-gray-700 lg:leading-8">
              The best we can do for someone this good looking is 20% off. <br class="hidden md:block">
              Use code <strong>"NOTUGLY"</strong> at checkout.
            </p>
            <div class="w-full flex items-center justify-center">
              <nuxt-link to="/#pricing">
                <ButtonPrimary class="bg-teal-500">
                  Get your headshots
                </ButtonPrimary>
              </nuxt-link>
            </div>
            <p class="text-sm text-gray-500 italic">
              Coupon expires at midnight April 2nd. <CountdownText date="April 2, 2024 10:00:00" />
            </p>
          </template>
        </div>
      </div>
    </main>
    <MarketingReviews />
    <MarketingFooter />
  </div>
</template>

<script>
export default {
  layout: 'default',
  data () {
    return {
      isLoading: false,
      giveVerdict: false,
      photo: null,
      compliment: null

    }
  },
  head () {
    return {
      title: this.title,
      description: this.description,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.description
        },
        {
          hid: 'twitter:title',
          name: 'twitter:title',
          content: this.title
        },
        {
          hid: 'twitter:description',
          name: 'twitter:description',
          content: this.description
        },
        {
          hid: 'twitter:image:alt',
          name: 'twitter:image:alt',
          content: this.title
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.title
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.description
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: this.title
        },
        {
          hid: 'thumbnail',
          name: 'thumbnail',
          content: 'https://www.headshotpro.com/og-tool-ugly.jpg'
        },
        {
          hid: 'twitter:image:src',
          name: 'twitter:image:src',
          content: 'https://www.headshotpro.com/og-tool-ugly.jpg'
        },
        {
          hid: 'twitter:image',
          name: 'twitter:image',
          content: 'https://www.headshotpro.com/og-tool-ugly.jpg'
        },
        {
          hid: 'og:image',
          property: 'og:image',
          content: 'https://www.headshotpro.com/og-tool-ugly.jpg'
        },
        {
          hid: 'og:image:secure_url',
          property: 'og:image:secure_url',
          content: 'https://www.headshotpro.com/og-tool-ugly.jpg'
        },
        {
          hid: 'og:image:alt',
          property: 'og:image:alt',
          content: 'https://www.headshotpro.com/og-tool-ugly.jpg'
        }
      ]
    }
  },
  computed: {
    title () {
      return 'Free Headshots for Ugly People'
    },
    description () {
      return "Is your profile picture ugly enough? Upload your current LinkedIn picture to see if you're ugly enough to qualify for free AI headshots"
    }
  },
  async mounted () {
    await this.$recaptcha.init()
  },
  methods: {
    async addFile (event) {
      this.isLoading = true

      try {
        this.isUploading = true
        const files = event?.target?.files
        // Add time out. If function takes longer than 1 second, it will be rejected
        let data = files[0]
        if (data.type === 'image/heic' || data.type === 'image/heif') {
          const heic2any = (await import('heic2any')).default
          data = await heic2any({ blob: data, toType: 'image/jpeg' })
        }
        data = await this.resizeImage(data)
        this.convertFileToBlob(data).then(async (blob) => {
          const base64 = await this.convertBlobToBase64(blob)
          this.photo = `data:image/png;base64,${base64}`
        })
        await this.getCompliment()
      } catch (err) {
        console.log(err)
      } finally {
        this.isLoading = false
        this.giveVerdict = true
      }
    },
    async getCompliment () {
      try {
        const token = await this.$recaptcha.execute('login')
        if (!token) { return this.$toast.open({ type: 'error', message: 'Please accept the reCAPTCHA.' }) }

        const { data, success, errorMessage } = await this.$axios.$post(
          '/tools/ugly-analyser/',
          {
            captcha: token,
            imageUrl: this.photo
          }
        )

        if (success) {
          const placeholder = 'Wow. Ok. This was supposed to be a funny compliment but you literally broke the AI. Error code: H07713 '
          if (!data.compliment) { this.compliment = null; return }
          if (data.compliment === "I'm sorry, I can't assist with that request.") { this.compliment = placeholder; return }
          if (data.compliment.toLowerCase().includes('sorry')) { this.compliment = placeholder; return }
          this.compliment = data.compliment
        }

        if (errorMessage) {
          if (errorMessage.includes('429')) {
            return this.$toast.error(
              'You have reached the limit of 3 compliments per day. Please try again tomorrow.'
            )
          }
          throw new Error(data.errorMessage)
        }
      } catch (error) {
        if (error?.response?.status === 429) {
          return this.$toast.error(
            // error?.response?.data?.errorMessage ??
            'You have reached the limit of 3 compliments per day. Please try again tomorrow.'
          )
        }
        return this.$toast.error('Something went wrong. Please try again.')
      }
    },
    convertFileToBlob (file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()

        reader.onload = () => {
          const arrayBuffer = reader.result
          const blob = new Blob([arrayBuffer], { type: file.type })
          resolve(blob)
        }

        reader.onerror = () => {
          reject(new Error('Failed to convert file to Blob.'))
        }

        reader.readAsArrayBuffer(file)
      })
    },
    convertBlobToBase64 (blob) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()

        reader.onload = () => {
          const base64String = reader.result.split(',')[1]
          resolve(base64String)
        }

        reader.onerror = () => {
          reject(new Error('Failed to convert Blob to base64.'))
        }

        reader.readAsDataURL(blob)
      })
    },
    resizeImage (blob) {
      try {
        // Resize image from blob and return resized blob with canvas
        return new Promise((resolve, reject) => {
          const reader = new FileReader()
          reader.readAsDataURL(blob)
          reader.onload = (event) => {
            const img = new Image()
            img.src = event.target.result
            img.onload = () => {
              const elem = document.createElement('canvas')
              const scaleFactor = img.width / 256
              elem.width = 256
              elem.height = img.height / scaleFactor
              const ctx = elem.getContext('2d')
              ctx.drawImage(img, 0, 0, elem.width, elem.height)
              ctx.canvas.toBlob(
                (blob) => {
                  resolve(blob)
                },
                'image/png',
                1
              )
            }
            img.onerror = (error) => {
              reject(error)
            }
          }
        })
      } catch (err) {
        console.log(err)
      }
    }
  }

}
</script>

<style>
.hero{
    background-image:url('@/assets/img/hero-examples-moving.jpg');
    background-size:100%;
    background-position: 0 10%;
    /* background-size:cover; */
    position: absolute;
    top: 0;
    left: 0;
    width: 200%; /* Double the width to cover the full container */
    height: 100%;
    animation: moveBackground 100s linear infinite; /* Adjust the animation duration as needed */

  }
    @keyframes moveBackground {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-50%); /* Move the image to the left by half of its width */
    }
  }
</style>
